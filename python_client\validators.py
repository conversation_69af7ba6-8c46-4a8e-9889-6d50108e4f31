"""
Additional validation utilities for the REST API
"""

import base64
import re
from typing import Any, Dict, List


def validate_base64(value: str) -> bool:
    """Validate if string is valid base64"""
    try:
        if isinstance(value, str):
            # Check if it's valid base64
            base64.b64decode(value, validate=True)
            return True
    except Exception:
        pass
    return False


def validate_user_id(user_id: str) -> bool:
    """Validate user ID format"""
    if not user_id or not isinstance(user_id, str):
        return False
    
    # User ID should be alphanumeric with optional hyphens/underscores
    # Length between 1 and 50 characters
    pattern = r'^[a-zA-Z0-9_-]{1,50}$'
    return bool(re.match(pattern, user_id))


def validate_finger_position(position: int) -> bool:
    """Validate finger position is in valid range"""
    return isinstance(position, int) and 1 <= position <= 10


def validate_operation_type(op_type: str) -> bool:
    """Validate operation type"""
    valid_types = ["flat", "rolled", "slaps"]
    return op_type in valid_types


def validate_threshold(threshold: int) -> bool:
    """Validate matching threshold"""
    return isinstance(threshold, int) and 0 <= threshold <= 100


def validate_quality(quality: int) -> bool:
    """Validate image quality score"""
    return isinstance(quality, int) and 0 <= quality <= 100


def validate_timeout(timeout: int) -> bool:
    """Validate timeout value"""
    return isinstance(timeout, int) and 1 <= timeout <= 300


class ValidationResult:
    """Result of validation with error details"""
    
    def __init__(self, is_valid: bool = True, errors: List[str] = None):
        self.is_valid = is_valid
        self.errors = errors or []
    
    def add_error(self, error: str):
        """Add an error message"""
        self.is_valid = False
        self.errors.append(error)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON response"""
        return {
            'valid': self.is_valid,
            'errors': self.errors
        }


def validate_capture_request(data: Dict[str, Any]) -> ValidationResult:
    """Comprehensive validation for capture requests"""
    result = ValidationResult()
    
    # Required fields
    if 'finger_position' not in data:
        result.add_error("finger_position is required")
    elif not validate_finger_position(data['finger_position']):
        result.add_error("finger_position must be an integer between 1 and 10")
    
    # Optional fields with validation
    if 'operation_type' in data and not validate_operation_type(data['operation_type']):
        result.add_error("operation_type must be one of: flat, rolled, slaps")
    
    if 'timeout' in data and not validate_timeout(data['timeout']):
        result.add_error("timeout must be an integer between 1 and 300 seconds")
    
    if 'save_image' in data and not isinstance(data['save_image'], bool):
        result.add_error("save_image must be a boolean")
    
    return result


def validate_identify_request(data: Dict[str, Any]) -> ValidationResult:
    """Comprehensive validation for identify requests"""
    result = ValidationResult()
    
    # Required fields
    if 'template_data' not in data:
        result.add_error("template_data is required")
    elif not isinstance(data['template_data'], str):
        result.add_error("template_data must be a string")
    elif not validate_base64(data['template_data']):
        result.add_error("template_data must be valid base64 encoded data")
    
    # Optional fields
    if 'threshold' in data and not validate_threshold(data['threshold']):
        result.add_error("threshold must be an integer between 0 and 100")
    
    return result


def validate_enroll_request(data: Dict[str, Any]) -> ValidationResult:
    """Comprehensive validation for enroll requests"""
    result = ValidationResult()
    
    # Required fields
    if 'user_id' not in data:
        result.add_error("user_id is required")
    elif not validate_user_id(data['user_id']):
        result.add_error("user_id must be alphanumeric (1-50 characters, hyphens/underscores allowed)")
    
    if 'finger_position' not in data:
        result.add_error("finger_position is required")
    elif not validate_finger_position(data['finger_position']):
        result.add_error("finger_position must be an integer between 1 and 10")
    
    if 'template_data' not in data:
        result.add_error("template_data is required")
    elif not isinstance(data['template_data'], str):
        result.add_error("template_data must be a string")
    elif not validate_base64(data['template_data']):
        result.add_error("template_data must be valid base64 encoded data")
    
    # Optional fields
    if 'image_data' in data and data['image_data']:
        if not isinstance(data['image_data'], str):
            result.add_error("image_data must be a string")
        elif not validate_base64(data['image_data']):
            result.add_error("image_data must be valid base64 encoded data")
    
    if 'image_quality' in data and not validate_quality(data['image_quality']):
        result.add_error("image_quality must be an integer between 0 and 100")
    
    return result


def get_finger_position_name(position: int) -> str:
    """Get human-readable name for finger position"""
    positions = {
        1: "Right Thumb",
        2: "Right Index",
        3: "Right Middle",
        4: "Right Ring", 
        5: "Right Little",
        6: "Left Thumb",
        7: "Left Index",
        8: "Left Middle",
        9: "Left Ring",
        10: "Left Little"
    }
    return positions.get(position, f"Unknown Position {position}")


def sanitize_user_input(data: Dict[str, Any]) -> Dict[str, Any]:
    """Sanitize user input data"""
    sanitized = {}
    
    for key, value in data.items():
        if isinstance(value, str):
            # Strip whitespace and limit length
            sanitized[key] = value.strip()[:1000]  # Reasonable limit
        else:
            sanitized[key] = value
    
    return sanitized
