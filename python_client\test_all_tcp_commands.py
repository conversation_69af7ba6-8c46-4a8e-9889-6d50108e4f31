#!/usr/bin/env python3
"""
Complete TCP Commands Test
Tests all TCP socket commands including device management commands
"""

import sys
import time
from tcp_client import Tcp<PERSON>lient, TcpClientError

def test_connection():
    """Test basic TCP connection"""
    print("\n🔌 Testing TCP Connection...")
    try:
        client = TcpClient()
        if client.test_connection():
            print("   ✅ TCP connection successful")
            return True
        else:
            print("   ❌ TCP connection failed")
            return False
    except Exception as e:
        print(f"   ❌ Connection error: {e}")
        return False

def test_device_management():
    """Test all device management commands"""
    print("\n📱 Testing Device Management Commands...")
    
    try:
        client = TcpClient()
        
        # Test device info (initial state)
        print("   📊 Getting initial device status...")
        info = client.get_device_info()
        print(f"      Status: {info.get('status', 'Unknown')}")
        
        # Test open device
        print("   🔓 Opening device...")
        result = client.open_device()
        print(f"      Result: {result.get('status', 'Unknown')}")
        if 'device_name' in result:
            print(f"      Device: {result['device_name']} (ID: {result.get('device_id', 'Unknown')})")
        
        # Test device info (after open)
        print("   📊 Getting device status after open...")
        info = client.get_device_info()
        print(f"      Status: {info.get('status', 'Unknown')}")
        
        # Test close device
        print("   🔒 Closing device...")
        result = client.close_device()
        print(f"      Result: {result.get('status', 'Unknown')}")
        
        # Test device info (after close)
        print("   📊 Getting device status after close...")
        info = client.get_device_info()
        print(f"      Status: {info.get('status', 'Unknown')}")
        
        return True
        
    except TcpClientError as e:
        print(f"   ❌ Device management error: {e}")
        return False

def main():
    """Run comprehensive TCP commands test"""
    print("🧪 Complete TCP Commands Test")
    print("=" * 60)
    print("Testing TCP socket commands for device management")
    print("=" * 60)
    
    # Test sequence
    tests = [
        ("TCP Connection", test_connection),
        ("Device Management", test_device_management),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
            if success:
                print(f"   ✅ {test_name} - PASSED")
            else:
                print(f"   ❌ {test_name} - FAILED")
        except Exception as e:
            print(f"   💥 {test_name} - EXCEPTION: {e}")
            results.append((test_name, False))
        
        # Small delay between tests
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} - {test_name}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All TCP commands working!")
        print("\n💡 Available TCP Commands:")
        print("   • OPEN / OPEN_DEVICE - Open fingerprint device")
        print("   • CLOSE / CLOSE_DEVICE - Close fingerprint device") 
        print("   • STATUS / DEVICE_INFO - Get device information")
    else:
        print("⚠️ Some tests failed. Check the C# bridge application.")
        print("\n🔧 Troubleshooting:")
        print("   1. Make sure C# bridge application is running")
        print("   2. Check that TCP server is listening on port 8123")
        print("   3. Verify A900 device is connected via USB")

if __name__ == "__main__":
    main()
