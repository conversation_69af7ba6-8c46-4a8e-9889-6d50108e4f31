using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Owin.Hosting;

namespace MultiFingerDemo
{
    public class SimpleTrayApp : ApplicationContext
    {
        private NotifyIcon trayIcon;
        private IDisposable webApp;

        public SimpleTrayApp()
        {
            // Start Web API
            StartWebApi();
            
            // Create tray icon
            CreateTrayIcon();
            
            // Show startup notification
            ShowStartupNotification();
        }

        private void StartWebApi()
        {
            try
        {
                string[] ports = { "9000", "9001", "9002", "8090", "8091" };
                
                foreach (string port in ports)
                {
                    try
                    {
                        string apiUrl = "http://localhost:" + port + "/";
                        webApp = WebApp.Start<Startup>(url: apiUrl);
                        return; // Success
                    }
                    catch
                    {
                        // Try next port
                    }
                }
            }
            catch
            {
                // Web API failed to start
            }
        }

        private void CreateTrayIcon()
        {
            trayIcon = new NotifyIcon()
            {
                Icon = SystemIcons.Application,
                ContextMenuStrip = new ContextMenuStrip(),
                Visible = true,
                Text = "Fingerprint Bridge Service"
            };

            // Add context menu items
            trayIcon.ContextMenuStrip.Items.Add("API Status", null, ShowApiStatus);
            trayIcon.ContextMenuStrip.Items.Add("-");
            trayIcon.ContextMenuStrip.Items.Add("Exit", null, Exit);
        }

        private void ShowStartupNotification()
        {
            trayIcon.ShowBalloonTip(3000, 
                "Fingerprint Bridge Service", 
                "Service started successfully!\nAPI: http://localhost:9000/", 
                ToolTipIcon.Info);
        }

        private void ShowApiStatus(object sender, EventArgs e)
        {
            string message = webApp != null ? 
                "Web API is running on http://localhost:9000/" : 
                "Web API failed to start";
                
            MessageBox.Show(message, "API Status", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Exit(object sender, EventArgs e)
        {
            trayIcon.Visible = false;
            
            if (webApp != null)
            {
                webApp.Dispose();
            }
            
            Application.Exit();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                trayIcon?.Dispose();
                webApp?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
