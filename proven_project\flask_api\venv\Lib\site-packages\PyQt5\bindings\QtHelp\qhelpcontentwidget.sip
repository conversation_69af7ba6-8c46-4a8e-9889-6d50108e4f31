// qhelpcontentwidget.sip generated by MetaSIP
//
// This file is part of the QtHelp Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHelpContentItem /NoDefaultCtors/
{
%TypeHeaderCode
#include <qhelpcontentwidget.h>
%End

public:
    ~QHelpContentItem();
    QHelpContentItem *child(int row) const;
    int childCount() const;
    QString title() const;
    QUrl url() const;
    int row() const;
    QHelpContentItem *parent() const;
    int childPosition(QHelpContentItem *child) const;
};

class QHelpContentModel : public QAbstractItemModel /NoDefaultCtors/
{
%TypeHeaderCode
#include <qhelpcontentwidget.h>
%End

public:
    virtual ~QHelpContentModel();
    void createContents(const QString &customFilterName);
    QHelpContentItem *contentItemAt(const QModelIndex &index) const;
    virtual QVariant data(const QModelIndex &index, int role) const;
    virtual QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const;
    virtual QModelIndex parent(const QModelIndex &index) const;
    virtual int rowCount(const QModelIndex &parent = QModelIndex()) const;
    virtual int columnCount(const QModelIndex &parent = QModelIndex()) const;
    bool isCreatingContents() const;

signals:
    void contentsCreationStarted();
    void contentsCreated();
};

class QHelpContentWidget : public QTreeView
{
%TypeHeaderCode
#include <qhelpcontentwidget.h>
%End

public:
    QModelIndex indexOf(const QUrl &link);

signals:
    void linkActivated(const QUrl &link);

private:
    QHelpContentWidget();
};
