// qprocess.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (WS_WIN)
%If (PyQt_Process)
typedef void *Q_PID;
%End
%End
%If (WS_X11 || WS_MACX)
%If (PyQt_Process)
typedef qint64 Q_PID;
%End
%End
%If (PyQt_Process)

class QProcess : public QIODevice
{
%TypeHeaderCode
#include <qprocess.h>
%End

public:
    enum ExitStatus
    {
        NormalExit,
        CrashExit,
    };

    enum ProcessError
    {
        FailedToStart,
        Crashed,
        Timedout,
        ReadError,
        WriteError,
        UnknownError,
    };

    enum ProcessState
    {
        NotRunning,
        Starting,
        Running,
    };

    enum ProcessChannel
    {
        StandardOutput,
        StandardError,
    };

    enum ProcessChannelMode
    {
        SeparateChannels,
        MergedChannels,
        ForwardedChannels,
%If (Qt_5_2_0 -)
        ForwardedOutputChannel,
%End
%If (Qt_5_2_0 -)
        ForwardedErrorChannel,
%End
    };

    explicit QProcess(QObject *parent /TransferThis/ = 0);
    virtual ~QProcess();
    void start(const QString &program, const QStringList &arguments, QIODevice::OpenMode mode = QIODevice::ReadWrite) /HoldGIL/;
    void start(const QString &command, QIODevice::OpenMode mode = QIODevice::ReadWrite) /HoldGIL/;
%If (Qt_5_1_0 -)
    void start(QIODevice::OpenMode mode = QIODevice::ReadWrite) /HoldGIL/;
%End
    QProcess::ProcessChannel readChannel() const;
    void setReadChannel(QProcess::ProcessChannel channel);
    void closeReadChannel(QProcess::ProcessChannel channel);
    void closeWriteChannel();
    QString workingDirectory() const;
    void setWorkingDirectory(const QString &dir);
    QProcess::ProcessError error() const;
    QProcess::ProcessState state() const;
    Q_PID pid() const;
    bool waitForStarted(int msecs = 30000) /ReleaseGIL/;
    virtual bool waitForReadyRead(int msecs = 30000) /ReleaseGIL/;
    virtual bool waitForBytesWritten(int msecs = 30000) /ReleaseGIL/;
    bool waitForFinished(int msecs = 30000) /ReleaseGIL/;
    QByteArray readAllStandardOutput() /ReleaseGIL/;
    QByteArray readAllStandardError() /ReleaseGIL/;
    int exitCode() const;
    QProcess::ExitStatus exitStatus() const;
    virtual qint64 bytesAvailable() const;
    virtual qint64 bytesToWrite() const;
    virtual bool isSequential() const;
    virtual bool canReadLine() const;
    virtual void close();
    virtual bool atEnd() const;
    static int execute(const QString &program, const QStringList &arguments) /ReleaseGIL/;
    static int execute(const QString &program) /ReleaseGIL/;
    static bool startDetached(const QString &program, const QStringList &arguments, const QString &workingDirectory, qint64 *pid = 0);
    static bool startDetached(const QString &program, const QStringList &arguments);
    static bool startDetached(const QString &program);
%If (Qt_5_10_0 -)
    bool startDetached(qint64 *pid = 0);
%End
    static QStringList systemEnvironment();
    QProcess::ProcessChannelMode processChannelMode() const;
    void setProcessChannelMode(QProcess::ProcessChannelMode mode);
    void setStandardInputFile(const QString &fileName);
    void setStandardOutputFile(const QString &fileName, QIODevice::OpenMode mode = QIODevice::Truncate);
    void setStandardErrorFile(const QString &fileName, QIODevice::OpenMode mode = QIODevice::Truncate);
    void setStandardOutputProcess(QProcess *destination);

public slots:
    void terminate();
    void kill();

signals:
    void started();
    void finished(int exitCode, QProcess::ExitStatus exitStatus);
    void error(QProcess::ProcessError error);
    void stateChanged(QProcess::ProcessState state);
    void readyReadStandardOutput();
    void readyReadStandardError();
%If (Qt_5_6_0 -)
    void errorOccurred(QProcess::ProcessError error);
%End

protected:
    void setProcessState(QProcess::ProcessState state);
    virtual void setupChildProcess();
    virtual SIP_PYOBJECT readData(qint64 maxlen) /TypeHint="Py_v3:bytes;str",ReleaseGIL/ [qint64 (char *data, qint64 maxlen)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QProcess::readData(s, a0) : sipCpp->readData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual qint64 writeData(const char *data /Array/, qint64 len /ArraySize/) /ReleaseGIL/;

public:
    void setProcessEnvironment(const QProcessEnvironment &environment);
    QProcessEnvironment processEnvironment() const;
    QString program() const;
%If (Qt_5_1_0 -)
    void setProgram(const QString &program);
%End
    QStringList arguments() const;
%If (Qt_5_1_0 -)
    void setArguments(const QStringList &arguments);
%End
%If (Qt_5_1_0 -)
    virtual bool open(QIODevice::OpenMode mode = QIODevice::ReadWrite) /ReleaseGIL/;
%End
%If (Qt_5_2_0 -)

    enum InputChannelMode
    {
        ManagedInputChannel,
        ForwardedInputChannel,
    };

%End
%If (Qt_5_2_0 -)
    QProcess::InputChannelMode inputChannelMode() const;
%End
%If (Qt_5_2_0 -)
    void setInputChannelMode(QProcess::InputChannelMode mode);
%End
%If (Qt_5_2_0 -)
    static QString nullDevice();
%End
%If (Qt_5_3_0 -)
    qint64 processId() const;
%End
};

%End
%If (PyQt_Process)

class QProcessEnvironment
{
%TypeHeaderCode
#include <qprocess.h>
%End

public:
    QProcessEnvironment();
    QProcessEnvironment(const QProcessEnvironment &other);
    ~QProcessEnvironment();
    bool operator==(const QProcessEnvironment &other) const;
    bool operator!=(const QProcessEnvironment &other) const;
    bool isEmpty() const;
    void clear();
    bool contains(const QString &name) const;
    void insert(const QString &name, const QString &value);
    void insert(const QProcessEnvironment &e);
    void remove(const QString &name);
    QString value(const QString &name, const QString &defaultValue = QString()) const;
    QStringList toStringList() const;
    static QProcessEnvironment systemEnvironment();
    QStringList keys() const;
    void swap(QProcessEnvironment &other /Constrained/);
};

%End
