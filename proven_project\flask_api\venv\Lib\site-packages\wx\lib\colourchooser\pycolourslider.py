"""
PyColourChooser
Copyright (C) 2002 <PERSON>

This file is part of PyColourChooser.

You should have received a file COPYING containing license terms
along with this program; if not, write to <PERSON>
(<EMAIL>) for a copy.

This version of PyColourChooser is open source; you can redistribute it and/or
modify it under the terms listed in the file COPYING.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
"""

# 12/14/2003 - <PERSON> (<EMAIL>)
#
# o 2.5 compatibility update.
#
# 12/21/2003 - <PERSON> (<EMAIL>)
#
# o wxPyColorChooser -> PyColorChooser
# o wxPyColourChooser -> PyColourChooser
#

import  wx

from . import  canvas
import  colorsys

class PyColourSlider(canvas.Canvas):
    """A Pure-Python Colour Slider

    The colour slider displays transitions from value 0 to value 1 in
    HSV, allowing the user to select a colour within the transition
    spectrum.

    This class is best accompanying by a wxSlider that allows the user
    to select a particular colour shade.
    """

    HEIGHT = 172
    WIDTH = 12

    def __init__(self, parent, id, colour=None):
        """Creates a blank slider instance. A colour must be set before the
        slider will be filled in."""
        # Set the base colour first since our base class calls the buffer
        # drawing function
        self.SetBaseColour(colour)

        canvas.Canvas.__init__(self, parent, id, forceClientSize=(self.WIDTH, self.HEIGHT))

    def SetBaseColour(self, colour):
        """Sets the base, or target colour, to use as the central colour
        when calculating colour transitions."""
        self.base_colour = colour

    def GetBaseColour(self):
        """Return the current colour used as a colour base for filling out
        the slider."""
        return self.base_colour

    def GetVFromClick(self, pos):
        """
        Returns the HSV value "V" based on the location of a mouse click at y offset "pos"
        """
        _, height = self.GetClientSize()
        if pos < 0:
            return 1             # Snap to max
        if pos >= height - 1:
            return 0             # Snap to 0

        return 1 - (pos / self.HEIGHT)

    def DrawBuffer(self):
        """Actual implementation of the widget's drawing. We simply draw
        from value 0.0 to value 1.0 in HSV."""
        if self.base_colour is None:
            return

        target_red = self.base_colour.Red()
        target_green = self.base_colour.Green()
        target_blue = self.base_colour.Blue()

        h,s,v = colorsys.rgb_to_hsv(target_red / 255.0, target_green / 255.0,
                                    target_blue / 255.0)
        v = 1.0
        vstep = 1.0 / self.HEIGHT
        for y_pos in range(0, self.HEIGHT):
            r,g,b = [c * 255.0 for c in colorsys.hsv_to_rgb(h,s,v)]
            colour = wx.Colour(int(r), int(g), int(b))
            self.buffer.SetPen(wx.Pen(colour, 1, wx.PENSTYLE_SOLID))
            self.buffer.DrawRectangle(0, y_pos, 15, 1)
            v = v - vstep
