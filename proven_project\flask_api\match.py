"""Fingerprint Matching Module

This module handles the matching of fingerprints against the entire database
of stored templates. It communicates with the fingerprint bridge service
via TCP socket to perform the 1:N matching process.
"""

import socket
import time

def match_fingerprint():
    """Match a captured fingerprint against all stored templates.
    
    Connects to the fingerprint bridge service and requests a 1:N matching
    operation between a newly captured fingerprint and all stored templates
    in the database.
    
    Returns:
        dict: Contains matching result
            - status: 'match', 'no_match', or 'error'
            - message: Matching result description
            - bmp_base64: Base64 encoded fingerprint image (if available)
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(60)
            s.connect(('127.0.0.1', 8123))
            s.sendall(b"MATCH\n")

            start_time = time.time()
            data = b""
            while True:
                chunk = s.recv(4096)
                if not chunk:
                    break
                data += chunk

            duration = time.time() - start_time
            decoded = data.decode("utf-8", errors="ignore")
            lines = decoded.splitlines()
            print(f"[Bridge] Response received in {duration:.2f} sec")

            result_message = "❌ No valid response"
            base64_bmp = ""

            for line in lines:
                clean_line = line.strip().lstrip("\ufeff")
                print(f"[Bridge] {clean_line}")
                if clean_line.startswith("BMP:"):
                    base64_bmp = clean_line[4:]
                elif "✅" in clean_line or "❌" in clean_line:
                    result_message = clean_line

            status = "match" if "✅" in result_message else "no_match"

            return {
                "status": status,
                "message": result_message,
                "bmp_base64": base64_bmp
            }

    except socket.timeout:
        return {"status": "error", "message": "Socket error: timed out"}
    except socket.error as e:
        return {"status": "error", "message": f"Socket error: {str(e)}"}
    except Exception as e:
        return {"status": "error", "message": f"Unexpected error: {str(e)}"}
