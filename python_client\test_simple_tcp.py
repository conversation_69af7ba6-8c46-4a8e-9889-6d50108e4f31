#!/usr/bin/env python3
"""
Simple TCP test to check if the bridge server is running
"""

import socket
import time

def test_tcp_connection():
    """Test basic TCP connection to port 8123."""
    print("🔌 Testing TCP connection to localhost:8123...")
    
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(5)
            result = s.connect_ex(('127.0.0.1', 8123))
            
            if result == 0:
                print("   ✅ TCP connection successful!")
                
                # Try sending a simple command
                s.sendall(b"CAPTURE USER001 1\n")
                
                # Try to receive response
                data = s.recv(1024)
                response = data.decode('utf-8', errors='ignore')
                print(f"   📡 Response: {response.strip()}")
                
                return True
            else:
                print(f"   ❌ TCP connection failed (error code: {result})")
                return False
                
    except Exception as e:
        print(f"   ❌ TCP connection error: {e}")
        return False

def check_port_in_use():
    """Check if port 8123 is in use by any process."""
    print("\n🔍 Checking if port 8123 is in use...")
    
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('127.0.0.1', 8123))
            
            if result == 0:
                print("   ✅ Port 8123 is in use (something is listening)")
                return True
            else:
                print("   ❌ Port 8123 is not in use (nothing listening)")
                return False
                
    except Exception as e:
        print(f"   ❌ Error checking port: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Simple TCP Bridge Test")
    print("=" * 40)
    
    # Check if port is in use
    port_in_use = check_port_in_use()
    
    if port_in_use:
        # Try to connect and send command
        test_tcp_connection()
    else:
        print("\n💡 Troubleshooting:")
        print("1. Make sure the C# application is running")
        print("2. Check if the TCP bridge server started successfully")
        print("3. Look for any error messages in the C# application")
        print("4. Verify the device is connected (TCP server might require device)")
    
    print("\n🏁 Test completed")
