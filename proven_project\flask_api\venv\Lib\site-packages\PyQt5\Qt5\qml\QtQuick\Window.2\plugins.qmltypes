import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        file: "plugin.h"
        name: "QQuickRootItem"
        defaultProperty: "data"
        prototype: "QQuickItem"
        Method {
            name: "setWidth"
            Parameter { name: "w"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "h"; type: "int" }
        }
    }
    Component {
        file: "plugin.h"
        name: "QQuickScreen"
        prototype: "QObject"
        exports: [
            "QtQuick.Window/Screen 2.0",
            "QtQuick.Window/Screen 2.10",
            "QtQuick.Window/Screen 2.3"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 10, 3]
        attachedType: "QQuickScreenAttached"
    }
    Component {
        file: "plugin.h"
        name: "QQuickScreenAttached"
        prototype: "QQuickScreenInfo"
        Property { name: "orientationUpdateMask"; type: "Qt::ScreenOrientations" }
        Method {
            name: "screenChanged"
            Parameter { type: "QScreen"; isPointer: true }
        }
        Method {
            name: "angleBetween"
            type: "int"
            Parameter { name: "a"; type: "int" }
            Parameter { name: "b"; type: "int" }
        }
    }
    Component {
        file: "plugin.h"
        name: "QQuickScreenInfo"
        prototype: "QObject"
        exports: [
            "QtQuick.Window/ScreenInfo 2.10",
            "QtQuick.Window/ScreenInfo 2.3"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [10, 3]
        Property { name: "name"; type: "string"; isReadonly: true }
        Property { name: "manufacturer"; revision: 10; type: "string"; isReadonly: true }
        Property { name: "model"; revision: 10; type: "string"; isReadonly: true }
        Property { name: "serialNumber"; revision: 10; type: "string"; isReadonly: true }
        Property { name: "width"; type: "int"; isReadonly: true }
        Property { name: "height"; type: "int"; isReadonly: true }
        Property { name: "desktopAvailableWidth"; type: "int"; isReadonly: true }
        Property { name: "desktopAvailableHeight"; type: "int"; isReadonly: true }
        Property { name: "logicalPixelDensity"; type: "double"; isReadonly: true }
        Property { name: "pixelDensity"; type: "double"; isReadonly: true }
        Property { name: "devicePixelRatio"; type: "double"; isReadonly: true }
        Property { name: "primaryOrientation"; type: "Qt::ScreenOrientation"; isReadonly: true }
        Property { name: "orientation"; type: "Qt::ScreenOrientation"; isReadonly: true }
        Property { name: "virtualX"; revision: 3; type: "int"; isReadonly: true }
        Property { name: "virtualY"; revision: 3; type: "int"; isReadonly: true }
        Signal { name: "manufacturerChanged"; revision: 10 }
        Signal { name: "modelChanged"; revision: 10 }
        Signal { name: "serialNumberChanged"; revision: 10 }
        Signal { name: "desktopGeometryChanged" }
        Signal { name: "virtualXChanged"; revision: 3 }
        Signal { name: "virtualYChanged"; revision: 3 }
    }
    Component {
        file: "plugin.h"
        name: "QQuickWindow"
        defaultProperty: "data"
        prototype: "QWindow"
        exports: ["QtQuick.Window/Window 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "CreateTextureOptions"
            alias: "CreateTextureOption"
            isFlag: true
            values: [
                "TextureHasAlphaChannel",
                "TextureHasMipmaps",
                "TextureOwnsGLTexture",
                "TextureCanUseAtlas",
                "TextureIsOpaque"
            ]
        }
        Enum {
            name: "SceneGraphError"
            values: ["ContextNotAvailable"]
        }
        Enum {
            name: "TextRenderType"
            values: ["QtTextRendering", "NativeTextRendering"]
        }
        Enum {
            name: "NativeObjectType"
            values: ["NativeObjectTexture"]
        }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "color"; type: "QColor" }
        Property { name: "contentItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property {
            name: "activeFocusItem"
            revision: 1
            type: "QQuickItem"
            isReadonly: true
            isPointer: true
        }
        Signal { name: "frameSwapped" }
        Signal {
            name: "openglContextCreated"
            revision: 2
            Parameter { name: "context"; type: "QOpenGLContext"; isPointer: true }
        }
        Signal { name: "sceneGraphInitialized" }
        Signal { name: "sceneGraphInvalidated" }
        Signal { name: "beforeSynchronizing" }
        Signal { name: "afterSynchronizing"; revision: 2 }
        Signal { name: "beforeRendering" }
        Signal { name: "afterRendering" }
        Signal { name: "afterAnimating"; revision: 2 }
        Signal { name: "sceneGraphAboutToStop"; revision: 2 }
        Signal {
            name: "closing"
            revision: 1
            Parameter { name: "close"; type: "QQuickCloseEvent"; isPointer: true }
        }
        Signal {
            name: "colorChanged"
            Parameter { type: "QColor" }
        }
        Signal { name: "activeFocusItemChanged"; revision: 1 }
        Signal {
            name: "sceneGraphError"
            revision: 2
            Parameter { name: "error"; type: "QQuickWindow::SceneGraphError" }
            Parameter { name: "message"; type: "string" }
        }
        Signal { name: "beforeRenderPassRecording"; revision: 14 }
        Signal { name: "afterRenderPassRecording"; revision: 14 }
        Method { name: "update" }
        Method { name: "releaseResources" }
        Method { name: "maybeUpdate" }
        Method { name: "cleanupSceneGraph" }
        Method { name: "physicalDpiChanged" }
        Method {
            name: "handleScreenChanged"
            Parameter { name: "screen"; type: "QScreen"; isPointer: true }
        }
        Method {
            name: "setTransientParent_helper"
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Method { name: "runJobsAfterSwap" }
        Method {
            name: "handleApplicationStateChanged"
            Parameter { name: "state"; type: "Qt::ApplicationState" }
        }
    }
    Component {
        file: "plugin.h"
        name: "QQuickWindowAttached"
        prototype: "QObject"
        Property { name: "visibility"; type: "QWindow::Visibility"; isReadonly: true }
        Property { name: "active"; type: "bool"; isReadonly: true }
        Property { name: "activeFocusItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "contentItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "width"; type: "int"; isReadonly: true }
        Property { name: "height"; type: "int"; isReadonly: true }
        Property { name: "window"; type: "QQuickWindow"; isReadonly: true; isPointer: true }
        Method {
            name: "windowChange"
            Parameter { type: "QQuickWindow"; isPointer: true }
        }
    }
    Component {
        file: "plugin.h"
        name: "QQuickWindowQmlImpl"
        defaultProperty: "data"
        prototype: "QQuickWindow"
        exports: [
            "QtQuick.Window/Window 2.1",
            "QtQuick.Window/Window 2.13",
            "QtQuick.Window/Window 2.14",
            "QtQuick.Window/Window 2.2",
            "QtQuick.Window/Window 2.3"
        ]
        exportMetaObjectRevisions: [1, 13, 14, 2, 3]
        attachedType: "QQuickWindowAttached"
        Property { name: "visible"; type: "bool" }
        Property { name: "visibility"; type: "Visibility" }
        Property { name: "screen"; revision: 3; type: "QObject"; isPointer: true }
        Signal {
            name: "visibleChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "visibilityChanged"
            Parameter { name: "visibility"; type: "QWindow::Visibility" }
        }
        Signal { name: "screenChanged"; revision: 3 }
        Method { name: "setWindowVisibility" }
    }
    Component {
        file: "plugin.h"
        name: "QWindow"
        prototype: "QObject"
        Enum {
            name: "Visibility"
            values: [
                "Hidden",
                "AutomaticVisibility",
                "Windowed",
                "Minimized",
                "Maximized",
                "FullScreen"
            ]
        }
        Enum {
            name: "AncestorMode"
            values: ["ExcludeTransients", "IncludeTransients"]
        }
        Property { name: "title"; type: "string" }
        Property { name: "modality"; type: "Qt::WindowModality" }
        Property { name: "flags"; type: "Qt::WindowFlags" }
        Property { name: "x"; type: "int" }
        Property { name: "y"; type: "int" }
        Property { name: "width"; type: "int" }
        Property { name: "height"; type: "int" }
        Property { name: "minimumWidth"; type: "int" }
        Property { name: "minimumHeight"; type: "int" }
        Property { name: "maximumWidth"; type: "int" }
        Property { name: "maximumHeight"; type: "int" }
        Property { name: "visible"; type: "bool" }
        Property { name: "active"; revision: 1; type: "bool"; isReadonly: true }
        Property { name: "visibility"; revision: 1; type: "Visibility" }
        Property { name: "contentOrientation"; type: "Qt::ScreenOrientation" }
        Property { name: "opacity"; revision: 1; type: "double" }
        Property { name: "transientParent"; revision: 13; type: "QWindow"; isPointer: true }
        Signal {
            name: "screenChanged"
            Parameter { name: "screen"; type: "QScreen"; isPointer: true }
        }
        Signal {
            name: "modalityChanged"
            Parameter { name: "modality"; type: "Qt::WindowModality" }
        }
        Signal {
            name: "windowStateChanged"
            Parameter { name: "windowState"; type: "Qt::WindowState" }
        }
        Signal {
            name: "windowTitleChanged"
            revision: 2
            Parameter { name: "title"; type: "string" }
        }
        Signal {
            name: "xChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "yChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "minimumWidthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "minimumHeightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "maximumWidthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "maximumHeightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "visibleChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "visibilityChanged"
            revision: 1
            Parameter { name: "visibility"; type: "QWindow::Visibility" }
        }
        Signal { name: "activeChanged"; revision: 1 }
        Signal {
            name: "contentOrientationChanged"
            Parameter { name: "orientation"; type: "Qt::ScreenOrientation" }
        }
        Signal {
            name: "focusObjectChanged"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "opacityChanged"
            revision: 1
            Parameter { name: "opacity"; type: "double" }
        }
        Signal {
            name: "transientParentChanged"
            revision: 13
            Parameter { name: "transientParent"; type: "QWindow"; isPointer: true }
        }
        Method { name: "requestActivate"; revision: 1 }
        Method {
            name: "setVisible"
            Parameter { name: "visible"; type: "bool" }
        }
        Method { name: "show" }
        Method { name: "hide" }
        Method { name: "showMinimized" }
        Method { name: "showMaximized" }
        Method { name: "showFullScreen" }
        Method { name: "showNormal" }
        Method { name: "close"; type: "bool" }
        Method { name: "raise" }
        Method { name: "lower" }
        Method {
            name: "startSystemResize"
            type: "bool"
            Parameter { name: "edges"; type: "Qt::Edges" }
        }
        Method { name: "startSystemMove"; type: "bool" }
        Method {
            name: "setTitle"
            Parameter { type: "string" }
        }
        Method {
            name: "setX"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setY"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "posx"; type: "int" }
            Parameter { name: "posy"; type: "int" }
            Parameter { name: "w"; type: "int" }
            Parameter { name: "h"; type: "int" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "rect"; type: "QRect" }
        }
        Method {
            name: "setMinimumWidth"
            Parameter { name: "w"; type: "int" }
        }
        Method {
            name: "setMinimumHeight"
            Parameter { name: "h"; type: "int" }
        }
        Method {
            name: "setMaximumWidth"
            Parameter { name: "w"; type: "int" }
        }
        Method {
            name: "setMaximumHeight"
            Parameter { name: "h"; type: "int" }
        }
        Method {
            name: "alert"
            revision: 1
            Parameter { name: "msec"; type: "int" }
        }
        Method { name: "requestUpdate"; revision: 3 }
        Method { name: "_q_clearAlert" }
    }
}
