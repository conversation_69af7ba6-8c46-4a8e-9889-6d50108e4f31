@echo off
echo ========================================
echo   Testing Fingerprint Bridge Service
echo ========================================
echo.

echo Starting application...
start "" "bin\Debug\MultipleFinger.exe"

echo.
echo Waiting 3 seconds for startup...
timeout /t 3 /nobreak >nul

echo.
echo Testing API endpoint...
curl -X POST http://localhost:9000/api/fingerprint/capture -H "Content-Type: application/json" -d "{}"

echo.
echo.
echo ========================================
echo Instructions:
echo 1. Look for system tray icon (bottom-right)
echo 2. Right-click tray icon
echo 3. Click "Open Console" to test main window
echo 4. Use "Exit" to close application
echo ========================================
pause
