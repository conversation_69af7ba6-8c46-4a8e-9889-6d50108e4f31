# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#
#-- begin-_propgrid --#

import wx
#-- end-_propgrid --#
#-- begin-propgriddefs --#

PG_LABEL = "@!"
PG_LABEL_STRING = PG_LABEL
PG_NULL_BITMAP = wx.NullBitmap
PG_COLOUR_BLACK = wx.BLACK
PG_DEFAULT_IMAGE_SIZE = wx.Size(-1, -1)
PG_INVALID_VALUE: int
PG_BASE_OCT: int
PG_BASE_DEC: int
PG_BASE_HEX: int
PG_BASE_HEXL: int
PG_PREFIX_NONE: int
PG_PREFIX_0x: int
PG_PREFIX_DOLLAR_SIGN: int

class _PG_GETPROPERTYVALUES_FLAGS(IntEnum):
    PG_DONT_RECURSE = auto()
    PG_KEEP_STRUCTURE = auto()
    PG_RECURSE = auto()
    PG_INC_ATTRIBUTES = auto()
    PG_RECURSE_STARTS = auto()
    PG_FORCE = auto()
    PG_SORT_TOP_LEVEL_ONLY = auto()
PG_GETPROPERTYVALUES_FLAGS: TypeAlias = Union[_PG_GETPROPERTYVALUES_FLAGS, int]
PG_DONT_RECURSE = _PG_GETPROPERTYVALUES_FLAGS.PG_DONT_RECURSE
PG_KEEP_STRUCTURE = _PG_GETPROPERTYVALUES_FLAGS.PG_KEEP_STRUCTURE
PG_RECURSE = _PG_GETPROPERTYVALUES_FLAGS.PG_RECURSE
PG_INC_ATTRIBUTES = _PG_GETPROPERTYVALUES_FLAGS.PG_INC_ATTRIBUTES
PG_RECURSE_STARTS = _PG_GETPROPERTYVALUES_FLAGS.PG_RECURSE_STARTS
PG_FORCE = _PG_GETPROPERTYVALUES_FLAGS.PG_FORCE
PG_SORT_TOP_LEVEL_ONLY = _PG_GETPROPERTYVALUES_FLAGS.PG_SORT_TOP_LEVEL_ONLY

class _PG_MISC_ARG_FLAGS(IntEnum):
    PG_FULL_VALUE = auto()
    PG_REPORT_ERROR = auto()
    PG_PROPERTY_SPECIFIC = auto()
    PG_EDITABLE_VALUE = auto()
    PG_COMPOSITE_FRAGMENT = auto()
    PG_UNEDITABLE_COMPOSITE_FRAGMENT = auto()
    PG_VALUE_IS_CURRENT = auto()
    PG_PROGRAMMATIC_VALUE = auto()
PG_MISC_ARG_FLAGS: TypeAlias = Union[_PG_MISC_ARG_FLAGS, int]
PG_FULL_VALUE = _PG_MISC_ARG_FLAGS.PG_FULL_VALUE
PG_REPORT_ERROR = _PG_MISC_ARG_FLAGS.PG_REPORT_ERROR
PG_PROPERTY_SPECIFIC = _PG_MISC_ARG_FLAGS.PG_PROPERTY_SPECIFIC
PG_EDITABLE_VALUE = _PG_MISC_ARG_FLAGS.PG_EDITABLE_VALUE
PG_COMPOSITE_FRAGMENT = _PG_MISC_ARG_FLAGS.PG_COMPOSITE_FRAGMENT
PG_UNEDITABLE_COMPOSITE_FRAGMENT = _PG_MISC_ARG_FLAGS.PG_UNEDITABLE_COMPOSITE_FRAGMENT
PG_VALUE_IS_CURRENT = _PG_MISC_ARG_FLAGS.PG_VALUE_IS_CURRENT
PG_PROGRAMMATIC_VALUE = _PG_MISC_ARG_FLAGS.PG_PROGRAMMATIC_VALUE

class _PG_SETVALUE_FLAGS(IntEnum):
    PG_SETVAL_REFRESH_EDITOR = auto()
    PG_SETVAL_AGGREGATED = auto()
    PG_SETVAL_FROM_PARENT = auto()
    PG_SETVAL_BY_USER = auto()
PG_SETVALUE_FLAGS: TypeAlias = Union[_PG_SETVALUE_FLAGS, int]
PG_SETVAL_REFRESH_EDITOR = _PG_SETVALUE_FLAGS.PG_SETVAL_REFRESH_EDITOR
PG_SETVAL_AGGREGATED = _PG_SETVALUE_FLAGS.PG_SETVAL_AGGREGATED
PG_SETVAL_FROM_PARENT = _PG_SETVALUE_FLAGS.PG_SETVAL_FROM_PARENT
PG_SETVAL_BY_USER = _PG_SETVALUE_FLAGS.PG_SETVAL_BY_USER
#-- end-propgriddefs --#
#-- begin-propgridproperty --#
PG_PROP_MAX: int
PG_PROP_PARENTAL_FLAGS: int
PG_STRING_STORED_FLAGS: int

class _PGPropertyFlags(IntFlag):
    PG_PROP_MODIFIED = auto()
    PG_PROP_DISABLED = auto()
    PG_PROP_HIDDEN = auto()
    PG_PROP_CUSTOMIMAGE = auto()
    PG_PROP_NOEDITOR = auto()
    PG_PROP_COLLAPSED = auto()
    PG_PROP_INVALID_VALUE = auto()
    PG_PROP_WAS_MODIFIED = auto()
    PG_PROP_AGGREGATE = auto()
    PG_PROP_CHILDREN_ARE_COPIES = auto()
    PG_PROP_PROPERTY = auto()
    PG_PROP_CATEGORY = auto()
    PG_PROP_MISC_PARENT = auto()
    PG_PROP_READONLY = auto()
    PG_PROP_COMPOSED_VALUE = auto()
    PG_PROP_USES_COMMON_VALUE = auto()
    PG_PROP_AUTO_UNSPECIFIED = auto()
    PG_PROP_CLASS_SPECIFIC_1 = auto()
    PG_PROP_CLASS_SPECIFIC_2 = auto()
    PG_PROP_BEING_DELETED = auto()
PGPropertyFlags: TypeAlias = Union[_PGPropertyFlags, int]
PG_PROP_MODIFIED = _PGPropertyFlags.PG_PROP_MODIFIED
PG_PROP_DISABLED = _PGPropertyFlags.PG_PROP_DISABLED
PG_PROP_HIDDEN = _PGPropertyFlags.PG_PROP_HIDDEN
PG_PROP_CUSTOMIMAGE = _PGPropertyFlags.PG_PROP_CUSTOMIMAGE
PG_PROP_NOEDITOR = _PGPropertyFlags.PG_PROP_NOEDITOR
PG_PROP_COLLAPSED = _PGPropertyFlags.PG_PROP_COLLAPSED
PG_PROP_INVALID_VALUE = _PGPropertyFlags.PG_PROP_INVALID_VALUE
PG_PROP_WAS_MODIFIED = _PGPropertyFlags.PG_PROP_WAS_MODIFIED
PG_PROP_AGGREGATE = _PGPropertyFlags.PG_PROP_AGGREGATE
PG_PROP_CHILDREN_ARE_COPIES = _PGPropertyFlags.PG_PROP_CHILDREN_ARE_COPIES
PG_PROP_PROPERTY = _PGPropertyFlags.PG_PROP_PROPERTY
PG_PROP_CATEGORY = _PGPropertyFlags.PG_PROP_CATEGORY
PG_PROP_MISC_PARENT = _PGPropertyFlags.PG_PROP_MISC_PARENT
PG_PROP_READONLY = _PGPropertyFlags.PG_PROP_READONLY
PG_PROP_COMPOSED_VALUE = _PGPropertyFlags.PG_PROP_COMPOSED_VALUE
PG_PROP_USES_COMMON_VALUE = _PGPropertyFlags.PG_PROP_USES_COMMON_VALUE
PG_PROP_AUTO_UNSPECIFIED = _PGPropertyFlags.PG_PROP_AUTO_UNSPECIFIED
PG_PROP_CLASS_SPECIFIC_1 = _PGPropertyFlags.PG_PROP_CLASS_SPECIFIC_1
PG_PROP_CLASS_SPECIFIC_2 = _PGPropertyFlags.PG_PROP_CLASS_SPECIFIC_2
PG_PROP_BEING_DELETED = _PGPropertyFlags.PG_PROP_BEING_DELETED

class PGPaintData:
    """
    Contains information related to property's OnCustomPaint.
    """
    m_parent: PropertyGrid
    m_choiceItem: int
    m_drawnWidth: int
    m_drawnHeight: int
# end of class PGPaintData


class PGCellRenderer(wx.RefCounter):
    """
    PGCellRenderer() -> None
    
    Base class for wxPropertyGrid cell renderers.
    """

    class _enum_40(IntEnum):
        Selected = auto()
        ChoicePopup = auto()
        Control = auto()
        Disabled = auto()
        DontUseCellFgCol = auto()
        DontUseCellBgCol = auto()
        DontUseCellColours = auto()
    Selected = _enum_40.Selected
    ChoicePopup = _enum_40.ChoicePopup
    Control = _enum_40.Control
    Disabled = _enum_40.Disabled
    DontUseCellFgCol = _enum_40.DontUseCellFgCol
    DontUseCellBgCol = _enum_40.DontUseCellBgCol
    DontUseCellColours = _enum_40.DontUseCellColours

    def __init__(self) -> None:
        """
        PGCellRenderer() -> None
        
        Base class for wxPropertyGrid cell renderers.
        """

    def Render(self, dc: wx.DC, rect: wx.Rect, propertyGrid: PropertyGrid, property: PGProperty, column: int, item: int, flags: int) -> bool:
        """
        Render(dc, rect, propertyGrid, property, column, item, flags) -> bool
        
        Returns true if rendered something in the foreground (text or bitmap).
        """

    def GetImageSize(self, property: PGProperty, column: int, item: int) -> wx.Size:
        """
        GetImageSize(property, column, item) -> wx.Size
        
        Returns size of the image in front of the editable area.
        """

    def DrawCaptionSelectionRect(self, dc: wx.DC, x: int, y: int, w: int, h: int) -> None:
        """
        DrawCaptionSelectionRect(dc, x, y, w, h) -> None
        
        Paints property category selection rectangle.
        """

    def DrawText(self, dc: wx.DC, rect: wx.Rect, imageWidth: int, text: str) -> None:
        """
        DrawText(dc, rect, imageWidth, text) -> None
        
        Utility to draw vertically centered text.
        """

    def DrawEditorValue(self, dc: wx.DC, rect: wx.Rect, xOffset: int, text: str, property: PGProperty, editor: PGEditor) -> None:
        """
        DrawEditorValue(dc, rect, xOffset, text, property, editor) -> None
        
        Utility to draw editor's value, or vertically aligned text if editor
        is NULL.
        """

    def PreDrawCell(self, dc: wx.DC, rect: wx.Rect, propGrid: PropertyGrid, cell: PGCell, flags: int) -> int:
        """
        PreDrawCell(dc, rect, propGrid, cell, flags) -> int
        
        Utility to render cell bitmap and set text colour plus bg brush
        colour.
        """

    def PostDrawCell(self, dc: wx.DC, propGrid: PropertyGrid, cell: PGCell, flags: int) -> None:
        """
        PostDrawCell(dc, propGrid, cell, flags) -> None
        
        Utility to be called after drawing is done, to revert whatever changes
        PreDrawCell() did.
        """
# end of class PGCellRenderer


class PGDefaultRenderer(PGCellRenderer):
    """
    Default cell renderer, that can handles the common scenarios.
    """

    def Render(self, dc: wx.DC, rect: wx.Rect, propertyGrid: PropertyGrid, property: PGProperty, column: int, item: int, flags: int) -> bool:
        """
        Render(dc, rect, propertyGrid, property, column, item, flags) -> bool
        
        Returns true if rendered something in the foreground (text or bitmap.
        """

    def GetImageSize(self, property: PGProperty, column: int, item: int) -> wx.Size:
        """
        GetImageSize(property, column, item) -> wx.Size
        
        Returns size of the image in front of the editable area.
        """
# end of class PGDefaultRenderer


class PGCellData(wx.RefCounter):
    """
    PGCellData() -> None
    """

    def __init__(self) -> None:
        """
        PGCellData() -> None
        """

    def SetText(self, text: str) -> None:
        """
        SetText(text) -> None
        """

    def SetBitmap(self, bitmap: wx.BitmapBundle) -> None:
        """
        SetBitmap(bitmap) -> None
        """

    def SetFgCol(self, col: wx.Colour) -> None:
        """
        SetFgCol(col) -> None
        """

    def SetBgCol(self, col: wx.Colour) -> None:
        """
        SetBgCol(col) -> None
        """

    def SetFont(self, font: wx.Font) -> None:
        """
        SetFont(font) -> None
        """
# end of class PGCellData


class PGCell(wx.Object):
    """
    PGCell() -> None
    PGCell(other) -> None
    PGCell(text, bitmap=wx.BitmapBundle(), fgCol=wx.NullColour, bgCol=wx.NullColour) -> None
    
    Base class for wxPropertyGrid cell information.
    """

    @overload
    def __init__(self, other: PGCell) -> None:
        ...

    @overload
    def __init__(self, text: str, bitmap: wx.BitmapBundle=wx.BitmapBundle(), fgCol: wx.Colour=wx.NullColour, bgCol: wx.Colour=wx.NullColour) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        PGCell() -> None
        PGCell(other) -> None
        PGCell(text, bitmap=wx.BitmapBundle(), fgCol=wx.NullColour, bgCol=wx.NullColour) -> None
        
        Base class for wxPropertyGrid cell information.
        """

    def GetData(self) -> PGCellData:
        """
        GetData() -> PGCellData
        """

    def HasText(self) -> bool:
        """
        HasText() -> bool
        
        Returns true if this cell has custom text stored within.
        """

    def SetEmptyData(self) -> None:
        """
        SetEmptyData() -> None
        
        Sets empty but valid data to this cell object.
        """

    def MergeFrom(self, srcCell: PGCell) -> None:
        """
        MergeFrom(srcCell) -> None
        
        Merges valid data from srcCell into this.
        """

    def SetText(self, text: str) -> None:
        """
        SetText(text) -> None
        """

    def SetBitmap(self, bitmap: wx.BitmapBundle) -> None:
        """
        SetBitmap(bitmap) -> None
        """

    def SetFgCol(self, col: wx.Colour) -> None:
        """
        SetFgCol(col) -> None
        """

    def SetFont(self, font: wx.Font) -> None:
        """
        SetFont(font) -> None
        
        Sets font of the cell.
        """

    def SetBgCol(self, col: wx.Colour) -> None:
        """
        SetBgCol(col) -> None
        """

    def GetText(self) -> str:
        """
        GetText() -> str
        """

    def GetBitmap(self) -> wx.BitmapBundle:
        """
        GetBitmap() -> wx.BitmapBundle
        """

    def GetFgCol(self) -> wx.Colour:
        """
        GetFgCol() -> wx.Colour
        """

    def GetFont(self) -> wx.Font:
        """
        GetFont() -> wx.Font
        
        Returns font of the cell.
        """

    def GetBgCol(self) -> wx.Colour:
        """
        GetBgCol() -> wx.Colour
        """
    @property
    def BgCol(self) -> wx.Colour: ...
    @BgCol.setter
    def BgCol(self, value: wx.Colour, /) -> None: ...
    @property
    def Bitmap(self) -> wx.BitmapBundle: ...
    @Bitmap.setter
    def Bitmap(self, value: wx.BitmapBundle, /) -> None: ...
    @property
    def Data(self) -> PGCellData: ...
    @property
    def FgCol(self) -> wx.Colour: ...
    @FgCol.setter
    def FgCol(self, value: wx.Colour, /) -> None: ...
    @property
    def Font(self) -> wx.Font: ...
    @Font.setter
    def Font(self, value: wx.Font, /) -> None: ...
    @property
    def Text(self) -> str: ...
    @Text.setter
    def Text(self, value: str, /) -> None: ...
# end of class PGCell


class PGAttributeStorage:
    """
    PGAttributeStorage() -> None
    
    wxPGAttributeStorage is somewhat optimized storage for key=variant
    pairs (i.e.
    """

    def __init__(self) -> None:
        """
        PGAttributeStorage() -> None
        
        wxPGAttributeStorage is somewhat optimized storage for key=variant
        pairs (i.e.
        """

    def Set(self, name: str, value: PGVariant) -> None:
        """
        Set(name, value) -> None
        """

    def GetCount(self) -> int:
        """
        GetCount() -> int
        """

    def FindValue(self, name: str) -> PGVariant:
        """
        FindValue(name) -> PGVariant
        """
    @property
    def Count(self) -> int: ...
# end of class PGAttributeStorage


class PGProperty(wx.Object):
    """
    PGProperty() -> None
    PGProperty(label, name) -> None
    
    wxPGProperty is base class for all wxPropertyGrid properties and as
    such it is not intended to be instantiated directly.
    """
    m_clientData: Any

    def OnSetValue(self) -> None:
        """
        OnSetValue() -> None
        
        This virtual function is called after m_value has been set.
        """

    def DoGetValue(self) -> PGVariant:
        """
        DoGetValue() -> PGVariant
        
        Override this to return something else than m_value as the value.
        """

    def ValidateValue(self, value: PGVariant, validationInfo: PGValidationInfo) -> bool:
        """
        ValidateValue(value, validationInfo) -> bool
        
        Implement this function in derived class to check the value.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def IntToValue(self, number: int, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        IntToValue(number, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts integer (possibly a choice selection) into wxVariant value
        appropriate for this property.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def SetValueFromString(self, text: str, flags: int=PG_PROGRAMMATIC_VALUE) -> bool:
        """
        SetValueFromString(text, flags=PG_PROGRAMMATIC_VALUE) -> bool
        
        Converts string to a value, and if successful, calls SetValue() on it.
        """

    def SetValueFromInt(self, value: int, flags: int=0) -> bool:
        """
        SetValueFromInt(value, flags=0) -> bool
        
        Converts integer to a value, and if successful, calls SetValue() on
        it.
        """

    def OnMeasureImage(self, item: int=-1) -> wx.Size:
        """
        OnMeasureImage(item=-1) -> wx.Size
        
        Returns size of the custom painted image in front of property.
        """

    def OnEvent(self, propgrid: PropertyGrid, wnd_primary: wx.Window, event: wx.Event) -> bool:
        """
        OnEvent(propgrid, wnd_primary, event) -> bool
        
        Events received by editor widgets are processed here.
        """

    def ChildChanged(self, thisValue: PGVariant, childIndex: int, childValue: PGVariant) -> PGVariant:
        """
        ChildChanged(thisValue, childIndex, childValue) -> PGVariant
        
        Called after value of a child property has been altered.
        """

    def DoGetEditorClass(self) -> PGEditor:
        """
        DoGetEditorClass() -> PGEditor
        
        Returns pointer to an instance of used editor.
        """

    def DoGetValidator(self) -> wx.Validator:
        """
        DoGetValidator() -> wx.Validator
        
        Returns pointer to the wxValidator that should be used with the editor
        of this property (NULL for no validator).
        """

    def OnCustomPaint(self, dc: wx.DC, rect: wx.Rect, paintdata: PGPaintData) -> None:
        """
        OnCustomPaint(dc, rect, paintdata) -> None
        
        Override to paint an image in front of the property value text or
        drop-down list item (but only if wxPGProperty::OnMeasureImage is
        overridden as well).
        """

    def GetCellRenderer(self, column: int) -> PGCellRenderer:
        """
        GetCellRenderer(column) -> PGCellRenderer
        
        Returns used wxPGCellRenderer instance for given property column
        (label=0, value=1).
        """

    def GetChoiceSelection(self) -> int:
        """
        GetChoiceSelection() -> int
        
        Returns which choice is currently selected.
        """

    def RefreshChildren(self) -> None:
        """
        RefreshChildren() -> None
        
        Refresh values of child properties.
        """

    def DoSetAttribute(self, name: str, value: PGVariant) -> bool:
        """
        DoSetAttribute(name, value) -> bool
        
        Reimplement this member function to add special handling for
        attributes of this property.
        """

    def DoGetAttribute(self, name: str) -> PGVariant:
        """
        DoGetAttribute(name) -> PGVariant
        
        Returns value of an attribute.
        """

    def GetEditorDialog(self) -> PGEditorDialogAdapter:
        """
        GetEditorDialog() -> PGEditorDialogAdapter
        
        Returns instance of a new wxPGEditorDialogAdapter instance, which is
        used when user presses the (optional) button next to the editor
        control;.
        """

    def OnValidationFailure(self, pendingValue: PGVariant) -> None:
        """
        OnValidationFailure(pendingValue) -> None
        
        Called whenever validation has failed with given pending value.
        """

    def AddChoice(self, label: str, value: int=PG_INVALID_VALUE) -> int:
        """
        AddChoice(label, value=PG_INVALID_VALUE) -> int
        
        Append a new choice to property's list of choices.
        """

    def AddPrivateChild(self, prop: PGProperty) -> None:
        """
        AddPrivateChild(prop) -> None
        
        Adds a private child property.
        """

    def AdaptListToValue(self, list: PGVariant, value: PGVariant) -> None:
        """
        AdaptListToValue(list, value) -> None
        
        Adapts list variant into proper value using consecutive ChildChanged()
        calls.
        """

    def AppendChild(self, childProperty: PGProperty) -> PGProperty:
        """
        AppendChild(childProperty) -> PGProperty
        
        Use this member function to add independent (i.e.
        """

    def AreAllChildrenSpecified(self, pendingList: Optional[PGVariant]=None) -> bool:
        """
        AreAllChildrenSpecified(pendingList=None) -> bool
        
        Determines, recursively, if all children are not unspecified.
        """

    def AreChildrenComponents(self) -> bool:
        """
        AreChildrenComponents() -> bool
        
        Returns true if children of this property are component values (for
        instance, points size, face name, and is_underlined are component
        values of a font).
        """

    def ChangeFlag(self, flag: PGPropertyFlags, set: bool) -> None:
        """
        ChangeFlag(flag, set) -> None
        
        Sets or clears given property flag.
        """

    def DeleteChildren(self) -> None:
        """
        DeleteChildren() -> None
        
        Deletes children of the property.
        """

    def DeleteChoice(self, index: int) -> None:
        """
        DeleteChoice(index) -> None
        
        Removes entry from property's wxPGChoices and editor control (if it is
        active).
        """

    def Enable(self, enable: bool=True) -> None:
        """
        Enable(enable=True) -> None
        
        Enables or disables the property.
        """

    def EnableCommonValue(self, enable: bool=True) -> None:
        """
        EnableCommonValue(enable=True) -> None
        
        Call to enable or disable usage of common value (integer value that
        can be selected for properties instead of their normal values) for
        this property.
        """

    def GenerateComposedValue(self) -> str:
        """
        GenerateComposedValue() -> str
        
        Composes text from values of child properties.
        """

    def GetLabel(self) -> str:
        """
        GetLabel() -> str
        
        Returns property's label.
        """

    @overload
    def GetAttribute(self, name: str, defVal: str) -> str:
        ...

    @overload
    def GetAttribute(self, name: str) -> PGVariant:
        """
        GetAttribute(name) -> PGVariant
        GetAttribute(name, defVal) -> str
        
        Returns property attribute value, null variant if not found.
        """

    def GetAttributeAsLong(self, name: str, defVal: int) -> int:
        """
        GetAttributeAsLong(name, defVal) -> int
        
        Returns named attribute, as long, if found.
        """

    def GetAttributeAsDouble(self, name: str, defVal: float) -> float:
        """
        GetAttributeAsDouble(name, defVal) -> float
        
        Returns named attribute, as double, if found.
        """

    def GetAttributes(self) -> Any:
        """
        GetAttributes() -> Any
        
        Returns map-like storage of property's attributes.
        """

    def GetAttributesAsList(self) -> PGVariant:
        """
        GetAttributesAsList() -> PGVariant
        
        Returns attributes as list wxVariant.
        """

    def GetColumnEditor(self, column: int) -> PGEditor:
        """
        GetColumnEditor(column) -> PGEditor
        
        Returns editor used for given column.
        """

    def GetBaseName(self) -> str:
        """
        GetBaseName() -> str
        
        Returns property's base name (i.e.
        """

    def GetCell(self, column: int) -> PGCell:
        """
        GetCell(column) -> PGCell
        
        Returns wxPGCell of given column, creating one if necessary.
        """

    def GetOrCreateCell(self, column: int) -> PGCell:
        """
        GetOrCreateCell(column) -> PGCell
        
        Returns wxPGCell of given column, creating one if necessary.
        """

    def GetChildCount(self) -> int:
        """
        GetChildCount() -> int
        
        Returns number of child properties.
        """

    def GetChildrenHeight(self, lh: int, iMax: int=-1) -> int:
        """
        GetChildrenHeight(lh, iMax=-1) -> int
        
        Returns height of children, recursively, and by taking
        expanded/collapsed status into account.
        """

    def GetChoices(self) -> PGChoices:
        """
        GetChoices() -> PGChoices
        
        Returns read-only reference to property's list of choices.
        """

    def GetClientData(self) -> ClientData:
        """
        GetClientData() -> ClientData
        
        Gets managed client object of a property.
        """

    def GetDefaultValue(self) -> PGVariant:
        """
        GetDefaultValue() -> PGVariant
        
        Returns property's default value.
        """

    def GetCommonValue(self) -> int:
        """
        GetCommonValue() -> int
        
        Returns common value selected for this property.
        """

    def GetDepth(self) -> int:
        """
        GetDepth() -> int
        """

    def GetDisplayedCommonValueCount(self) -> int:
        """
        GetDisplayedCommonValueCount() -> int
        
        Return number of displayed common values for this property.
        """

    def GetDisplayedString(self) -> str:
        """
        GetDisplayedString() -> str
        
        Returns property's displayed text.
        """

    def GetEditorClass(self) -> PGEditor:
        """
        GetEditorClass() -> PGEditor
        
        Returns wxPGEditor that will be used and created when property becomes
        selected.
        """

    def GetHintText(self) -> str:
        """
        GetHintText() -> str
        
        Returns property's hint text (shown in empty value cell).
        """

    def GetGrid(self) -> PropertyGrid:
        """
        GetGrid() -> PropertyGrid
        
        Returns property grid where property lies.
        """

    def GetGridIfDisplayed(self) -> PropertyGrid:
        """
        GetGridIfDisplayed() -> PropertyGrid
        
        Returns owner wxPropertyGrid, but only if one is currently on a page
        displaying this property.
        """

    def GetHelpString(self) -> str:
        """
        GetHelpString() -> str
        
        Returns property's help or description text.
        """

    def GetFlagsAsString(self, flagsMask: FlagType) -> str:
        """
        GetFlagsAsString(flagsMask) -> str
        
        Gets flags as a'|' delimited string.
        """

    def GetIndexInParent(self) -> int:
        """
        GetIndexInParent() -> int
        
        Returns position in parent's array.
        """

    def GetLastVisibleSubItem(self) -> PGProperty:
        """
        GetLastVisibleSubItem() -> PGProperty
        
        Returns last visible child property, recursively.
        """

    def GetMainParent(self) -> PGProperty:
        """
        GetMainParent() -> PGProperty
        
        Returns highest level non-category, non-root parent.
        """

    def GetMaxLength(self) -> int:
        """
        GetMaxLength() -> int
        
        Returns maximum allowed length of the text the user can enter in the
        property text editor.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns property's name with all (non-category, non-root) parents.
        """

    def GetParent(self) -> PGProperty:
        """
        GetParent() -> PGProperty
        
        Return parent of property.
        """

    def GetPropertyByName(self, name: str) -> PGProperty:
        """
        GetPropertyByName(name) -> PGProperty
        
        Returns (direct) child property with given name (or NULL if not
        found).
        """

    def GetValidator(self) -> wx.Validator:
        """
        GetValidator() -> wx.Validator
        
        Gets assignable version of property's validator.
        """

    def GetValue(self) -> PGVariant:
        """
        GetValue() -> PGVariant
        
        Returns property's value.
        """

    def GetValueImage(self) -> wx.Bitmap:
        """
        GetValueImage() -> wx.Bitmap
        
        Returns bitmap that appears next to value text.
        """

    def GetValueAsString(self, argFlags: int=0) -> str:
        """
        GetValueAsString(argFlags=0) -> str
        
        Returns text representation of property's value.
        """

    def GetValueType(self) -> str:
        """
        GetValueType() -> str
        
        Returns value type used by this property.
        """

    def GetY(self) -> int:
        """
        GetY() -> int
        
        Returns coordinate to the top y of the property.
        """

    def GetImageOffset(self, imageWidth: int) -> int:
        """
        GetImageOffset(imageWidth) -> int
        
        Converts image width into full image offset, with margins.
        """

    def GetItemAtY(self, y: int) -> PGProperty:
        """
        GetItemAtY(y) -> PGProperty
        
        Returns property at given virtual y coordinate.
        """

    def HasFlag(self, flag: PGPropertyFlags) -> bool:
        """
        HasFlag(flag) -> bool
        
        Returns true if property has given flag set.
        """

    def HasFlagsExact(self, flags: FlagType) -> bool:
        """
        HasFlagsExact(flags) -> bool
        
        Returns true if property has all given flags set.
        """

    def HasVisibleChildren(self) -> bool:
        """
        HasVisibleChildren() -> bool
        
        Returns true if property has even one visible child.
        """

    def Hide(self, hide: bool, flags: int=PG_RECURSE) -> bool:
        """
        Hide(hide, flags=PG_RECURSE) -> bool
        
        Hides or reveals the property.
        """

    def Index(self, p: PGProperty) -> int:
        """
        Index(p) -> int
        
        Returns index of given child property.
        """

    def InsertChild(self, index: int, childProperty: PGProperty) -> PGProperty:
        """
        InsertChild(index, childProperty) -> PGProperty
        
        Use this member function to add independent (i.e.
        """

    def InsertChoice(self, label: str, index: int, value: int=PG_INVALID_VALUE) -> int:
        """
        InsertChoice(label, index, value=PG_INVALID_VALUE) -> int
        
        Inserts a new choice to property's list of choices.
        """

    def IsCategory(self) -> bool:
        """
        IsCategory() -> bool
        
        Returns true if this property is actually a wxPropertyCategory.
        """

    def IsEnabled(self) -> bool:
        """
        IsEnabled() -> bool
        
        Returns true if property is enabled.
        """

    def IsExpanded(self) -> bool:
        """
        IsExpanded() -> bool
        
        Returns true if property has visible children.
        """

    def IsRoot(self) -> bool:
        """
        IsRoot() -> bool
        
        Returns true if this property is actually a wxRootProperty.
        """

    def IsSubProperty(self) -> bool:
        """
        IsSubProperty() -> bool
        
        Returns true if this is a sub-property.
        """

    def IsSomeParent(self, candidateParent: PGProperty) -> bool:
        """
        IsSomeParent(candidateParent) -> bool
        
        Returns true if candidateParent is some parent of this property.
        """

    def IsTextEditable(self) -> bool:
        """
        IsTextEditable() -> bool
        
        Returns true if property has editable wxTextCtrl when selected.
        """

    def IsValueUnspecified(self) -> bool:
        """
        IsValueUnspecified() -> bool
        
        Returns true if property's value is considered unspecified.
        """

    def IsVisible(self) -> bool:
        """
        IsVisible() -> bool
        
        Returns true if all parents expanded.
        """

    def Item(self, i: int) -> PGProperty:
        """
        Item(i) -> PGProperty
        
        Returns child property at index i.
        """

    def Last(self) -> PGProperty:
        """
        Last() -> PGProperty
        
        Returns last sub-property.
        """

    def RecreateEditor(self) -> bool:
        """
        RecreateEditor() -> bool
        
        If property's editor is created this forces its recreation.
        """

    def RefreshEditor(self) -> None:
        """
        RefreshEditor() -> None
        
        If property's editor is active, then update it's value.
        """

    def SetAttribute(self, name: str, value: PGVariant) -> None:
        """
        SetAttribute(name, value) -> None
        
        Sets an attribute for this property.
        """

    def SetAutoUnspecified(self, enable: bool=True) -> None:
        """
        SetAutoUnspecified(enable=True) -> None
        
        Set if user can change the property's value to unspecified by
        modifying the value of the editor control (usually by clearing it).
        """

    def SetBackgroundColour(self, colour: wx.Colour, flags: int=PG_RECURSE) -> None:
        """
        SetBackgroundColour(colour, flags=PG_RECURSE) -> None
        
        Sets property's background colour.
        """

    @overload
    def SetEditor(self, editorName: str) -> None:
        ...

    @overload
    def SetEditor(self, editor: PGEditor) -> None:
        """
        SetEditor(editor) -> None
        SetEditor(editorName) -> None
        
        Sets editor for a property.
        """

    def SetCell(self, column: int, cell: PGCell) -> None:
        """
        SetCell(column, cell) -> None
        
        Sets cell information for given column.
        """

    def SetCommonValue(self, commonValue: int) -> None:
        """
        SetCommonValue(commonValue) -> None
        
        Sets common value selected for this property.
        """

    def SetChoices(self, choices: PGChoices) -> bool:
        """
        SetChoices(choices) -> bool
        
        Sets new set of choices for the property.
        """

    def SetClientData(self, data: ClientData) -> None:
        """
        SetClientData(data) -> None
        
        Sets client object of a property.
        """

    def SetChoiceSelection(self, newValue: int) -> None:
        """
        SetChoiceSelection(newValue) -> None
        
        Sets selected choice and changes property value.
        """

    def SetDefaultValue(self, value: PGVariant) -> None:
        """
        SetDefaultValue(value) -> None
        
        Set default value of a property.
        """

    def SetExpanded(self, expanded: bool) -> None:
        """
        SetExpanded(expanded) -> None
        """

    def SetFlagsFromString(self, str: str) -> None:
        """
        SetFlagsFromString(str) -> None
        
        Sets flags from a '|' delimited string.
        """

    def SetFlagRecursively(self, flag: PGPropertyFlags, set: bool) -> None:
        """
        SetFlagRecursively(flag, set) -> None
        
        Sets or clears given property flag, recursively.
        """

    def SetHelpString(self, helpString: str) -> None:
        """
        SetHelpString(helpString) -> None
        
        Sets property's help string, which is shown, for example, in
        wxPropertyGridManager's description text box.
        """

    def SetLabel(self, label: str) -> None:
        """
        SetLabel(label) -> None
        
        Sets property's label.
        """

    def SetMaxLength(self, maxLen: int) -> bool:
        """
        SetMaxLength(maxLen) -> bool
        
        Set maximum length of the text the user can enter in the text editor.
        """

    def SetModifiedStatus(self, modified: bool) -> None:
        """
        SetModifiedStatus(modified) -> None
        
        Sets property's "is it modified?" flag.
        """

    def SetName(self, newName: str) -> None:
        """
        SetName(newName) -> None
        
        Sets new (base) name for property.
        """

    def SetParentalType(self, flag: int) -> None:
        """
        SetParentalType(flag) -> None
        
        Changes what sort of parent this property is for its children.
        """

    def SetTextColour(self, colour: wx.Colour, flags: int=PG_RECURSE) -> None:
        """
        SetTextColour(colour, flags=PG_RECURSE) -> None
        
        Sets property's text colour.
        """

    def SetDefaultColours(self, flags: int=PG_RECURSE) -> None:
        """
        SetDefaultColours(flags=PG_RECURSE) -> None
        
        Sets property's default text and background colours.
        """

    def SetValidator(self, validator: wx.Validator) -> None:
        """
        SetValidator(validator) -> None
        
        Sets wxValidator for a property.
        """

    def SetValue(self, value: PGVariant, pList: Optional[PGVariant]=None, flags: int=PG_SETVAL_REFRESH_EDITOR) -> None:
        """
        SetValue(value, pList=None, flags=PG_SETVAL_REFRESH_EDITOR) -> None
        
        Call this to set value of the property.
        """

    def SetValueImage(self, bmp: wx.BitmapBundle) -> None:
        """
        SetValueImage(bmp) -> None
        
        Set wxBitmap taken from wxBitmapBundle in front of the value.
        """

    def SetValueInEvent(self, value: PGVariant) -> None:
        """
        SetValueInEvent(value) -> None
        
        Call this function in OnEvent(), OnButtonClick() etc.
        """

    def SetValueToUnspecified(self) -> None:
        """
        SetValueToUnspecified() -> None
        
        Sets property's value to unspecified (i.e.
        """

    def SetWasModified(self, set: bool=True) -> None:
        """
        SetWasModified(set=True) -> None
        
        Call with false in OnSetValue() to cancel value changes after all
        (i.e.
        """

    def UpdateParentValues(self) -> PGProperty:
        """
        UpdateParentValues() -> PGProperty
        
        Updates composed values of parent non-category properties,
        recursively.
        """

    def UsesAutoUnspecified(self) -> bool:
        """
        UsesAutoUnspecified() -> bool
        
        Returns true if containing grid uses wxPG_EX_AUTO_UNSPECIFIED_VALUES.
        """
    @property
    def m_value(self) -> PGVariant: ...
    @m_value.setter
    def m_value(self, value: PGVariant, /) -> None: ...

    def SetAttributes(self, attributes):
        """
        Set the property's attributes from a Python dictionary.
        """

    def GetClientObject(self, n):
        """
        Alias for :meth:`GetClientData`
        """

    def SetClientObject(self, n, data):
        """
        Alias for :meth:`SetClientData`
        """

    @overload
    def __init__(self, label: str, name: str) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        """
# end of class PGProperty


class PropertyCategory(PGProperty):
    """
    PropertyCategory() -> None
    PropertyCategory(label, name=PG_LABEL) -> None
    
    Category (caption) property.
    """

    @overload
    def __init__(self, label: str, name: str=PG_LABEL) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        PropertyCategory() -> None
        PropertyCategory(label, name=PG_LABEL) -> None
        
        Category (caption) property.
        """

    def GetTextExtent(self, wnd: wx.Window, font: wx.Font) -> int:
        """
        GetTextExtent(wnd, font) -> int
        """

    def ValueToString(self, value: PGVariant, argFlags: int) -> str:
        """
        ValueToString(value, argFlags) -> str
        
        Converts property value into a text representation.
        """

    def GetValueAsString(self, argFlags: int=0) -> str:
        """
        GetValueAsString(argFlags=0) -> str
        
        Returns text representation of property's value.
        """
    @property
    def ValueAsString(self) -> str: ...
# end of class PropertyCategory


class PGChoiceEntry(PGCell):
    """
    PGChoiceEntry() -> None
    PGChoiceEntry(other) -> None
    PGChoiceEntry(label, value=PG_INVALID_VALUE) -> None
    
    Data of a single wxPGChoices choice.
    """

    @overload
    def __init__(self, other: PGChoiceEntry) -> None:
        ...

    @overload
    def __init__(self, label: str, value: int=PG_INVALID_VALUE) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        PGChoiceEntry() -> None
        PGChoiceEntry(other) -> None
        PGChoiceEntry(label, value=PG_INVALID_VALUE) -> None
        
        Data of a single wxPGChoices choice.
        """

    def SetValue(self, value: int) -> None:
        """
        SetValue(value) -> None
        """

    def GetValue(self) -> int:
        """
        GetValue() -> int
        """
    @property
    def Value(self) -> int: ...
    @Value.setter
    def Value(self, value: int, /) -> None: ...
# end of class PGChoiceEntry


class PGChoicesData(wx.RefCounter):
    """
    PGChoicesData() -> None
    """

    def __init__(self) -> None:
        """
        PGChoicesData() -> None
        """

    def CopyDataFrom(self, data: PGChoicesData) -> None:
        """
        CopyDataFrom(data) -> None
        """

    def Insert(self, index: int, item: PGChoiceEntry) -> PGChoiceEntry:
        """
        Insert(index, item) -> PGChoiceEntry
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        """

    def GetCount(self) -> int:
        """
        GetCount() -> int
        """

    def Item(self, i: int) -> PGChoiceEntry:
        """
        Item(i) -> PGChoiceEntry
        """
    @property
    def Count(self) -> int: ...
# end of class PGChoicesData


class PGChoices:
    """
    PGChoices() -> None
    PGChoices(a) -> None
    PGChoices(labels, values=[]) -> None
    PGChoices(data) -> None
    
    Helper class for managing choices of wxPropertyGrid properties.
    """

    @overload
    def __init__(self, a: PGChoices) -> None:
        ...

    @overload
    def __init__(self, labels: List[str], values: List[int]=[]) -> None:
        ...

    @overload
    def __init__(self, data: PGChoicesData) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        PGChoices() -> None
        PGChoices(a) -> None
        PGChoices(labels, values=[]) -> None
        PGChoices(data) -> None
        
        Helper class for managing choices of wxPropertyGrid properties.
        """

    @overload
    def Add(self, label: str, value: int=PG_INVALID_VALUE) -> PGChoiceEntry:
        ...

    @overload
    def Add(self, label: str, bitmap: wx.Bitmap, value: int=PG_INVALID_VALUE) -> PGChoiceEntry:
        ...

    @overload
    def Add(self, entry: PGChoiceEntry) -> PGChoiceEntry:
        ...

    @overload
    def Add(self, arr: List[str], arrint: List[int]) -> None:
        """
        Add(arr, arrint) -> None
        Add(label, value=PG_INVALID_VALUE) -> PGChoiceEntry
        Add(label, bitmap, value=PG_INVALID_VALUE) -> PGChoiceEntry
        Add(entry) -> PGChoiceEntry
        
        This is an overloaded member function, provided for convenience. It
        differs from the above function only in what argument(s) it accepts.
        """

    def AddAsSorted(self, label: str, value: int=PG_INVALID_VALUE) -> PGChoiceEntry:
        """
        AddAsSorted(label, value=PG_INVALID_VALUE) -> PGChoiceEntry
        
        Adds a single item, sorted.
        """

    def Assign(self, a: PGChoices) -> None:
        """
        Assign(a) -> None
        
        Assigns choices data, using reference counting.
        """

    def AssignData(self, data: PGChoicesData) -> None:
        """
        AssignData(data) -> None
        
        Assigns data from another set of choices.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Deletes all items.
        """

    def Copy(self) -> PGChoices:
        """
        Copy() -> PGChoices
        
        Returns a real copy of the choices.
        """

    def EnsureData(self) -> None:
        """
        EnsureData() -> None
        """

    def GetId(self) -> IntPtr:
        """
        GetId() -> IntPtr
        
        Gets an unsigned number identifying this list.
        """

    def GetLabel(self, ind: int) -> str:
        """
        GetLabel(ind) -> str
        
        Returns label of item.
        """

    def GetCount(self) -> int:
        """
        GetCount() -> int
        
        Returns number of items.
        """

    def GetValue(self, ind: int) -> int:
        """
        GetValue(ind) -> int
        
        Returns value of item.
        """

    def GetValuesForStrings(self, strings: List[str]) -> List[int]:
        """
        GetValuesForStrings(strings) -> List[int]
        
        Returns array of values matching the given strings.
        """

    def GetIndicesForStrings(self, strings: List[str], unmatched: Optional[List[str]]=None) -> List[int]:
        """
        GetIndicesForStrings(strings, unmatched=None) -> List[int]
        
        Returns array of indices matching given strings.
        """

    @overload
    def Index(self, val: int) -> int:
        ...

    @overload
    def Index(self, label: str) -> int:
        """
        Index(label) -> int
        Index(val) -> int
        
        Returns index of item with given label.
        """

    @overload
    def Insert(self, entry: PGChoiceEntry, index: int) -> PGChoiceEntry:
        ...

    @overload
    def Insert(self, label: str, index: int, value: int=PG_INVALID_VALUE) -> PGChoiceEntry:
        """
        Insert(label, index, value=PG_INVALID_VALUE) -> PGChoiceEntry
        Insert(entry, index) -> PGChoiceEntry
        
        Inserts a single item.
        """

    def IsOk(self) -> bool:
        """
        IsOk() -> bool
        
        Returns false if this is a constant empty set of choices, which should
        not be modified.
        """

    def Item(self, i: int) -> PGChoiceEntry:
        """
        Item(i) -> PGChoiceEntry
        
        Returns item at given index.
        """

    def RemoveAt(self, nIndex: int, count: int=1) -> None:
        """
        RemoveAt(nIndex, count=1) -> None
        
        Removes count items starting at position nIndex.
        """

    def Set(self, labels: List[str], values: List[int]=[]) -> None:
        """
        Set(labels, values=[]) -> None
        
        This is an overloaded member function, provided for convenience. It
        differs from the above function only in what argument(s) it accepts.
        """

    def AllocExclusive(self) -> None:
        """
        AllocExclusive() -> None
        
        Creates exclusive copy of current choices.
        """

    def GetData(self) -> PGChoicesData:
        """
        GetData() -> PGChoicesData
        
        Returns data, increases refcount.
        """

    def GetDataPtr(self) -> PGChoicesData:
        """
        GetDataPtr() -> PGChoicesData
        
        Returns plain data ptr - no refcounting stuff is done.
        """

    def ExtractData(self) -> PGChoicesData:
        """
        ExtractData() -> PGChoicesData
        
        Changes ownership of data to you.
        """

    def GetLabels(self) -> List[str]:
        """
        GetLabels() -> List[str]
        
        Returns array of choice labels.
        """

    def __getitem__(self, index):
        """
        Returns a reference to a :class:PGChoiceEntry using Python list syntax.
        """

    def __len__(self):
        """
        
        """
    @property
    def Count(self) -> int: ...
    @property
    def Data(self) -> PGChoicesData: ...
    @property
    def DataPtr(self) -> PGChoicesData: ...
    @property
    def Id(self) -> IntPtr: ...
    @property
    def Labels(self) -> List[str]: ...
# end of class PGChoices


PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None
#-- end-propgridproperty --#
#-- begin-propgrideditors --#

class PGWindowList:
    """
    PGWindowList(primary, secondary=None) -> None
    
    Contains a list of editor windows returned by CreateControls.
    """

    def __init__(self, primary: wx.Window, secondary: Optional[wx.Window]=None) -> None:
        """
        PGWindowList(primary, secondary=None) -> None
        
        Contains a list of editor windows returned by CreateControls.
        """

    def SetSecondary(self, secondary: wx.Window) -> None:
        """
        SetSecondary(secondary) -> None
        """

    def GetPrimary(self) -> wx.Window:
        """
        GetPrimary() -> wx.Window
        
        Gets window of primary editor.
        """

    def GetSecondary(self) -> wx.Window:
        """
        GetSecondary() -> wx.Window
        
        Gets window of secondary editor.
        """
    @property
    def Primary(self) -> wx.Window: ...
    @property
    def Secondary(self) -> wx.Window: ...
    @Secondary.setter
    def Secondary(self, value: wx.Window, /) -> None: ...
# end of class PGWindowList


class PGEditor(wx.Object):
    """
    PGEditor() -> None
    
    Base class for custom wxPropertyGrid editors.
    """

    def __init__(self) -> None:
        """
        PGEditor() -> None
        
        Base class for custom wxPropertyGrid editors.
        """
    m_clientData: Any

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns pointer to the name of the editor.
        """

    def CreateControls(self, propgrid: PropertyGrid, property: PGProperty, pos: wx.Point, size: wx.Size) -> PGWindowList:
        """
        CreateControls(propgrid, property, pos, size) -> PGWindowList
        
        Instantiates editor controls.
        """

    def UpdateControl(self, property: PGProperty, ctrl: wx.Window) -> None:
        """
        UpdateControl(property, ctrl) -> None
        
        Loads value from property to the control.
        """

    def DrawValue(self, dc: wx.DC, rect: wx.Rect, property: PGProperty, text: str) -> None:
        """
        DrawValue(dc, rect, property, text) -> None
        
        Draws value for given property.
        """

    def OnEvent(self, propgrid: PropertyGrid, property: PGProperty, wnd_primary: wx.Window, event: wx.Event) -> bool:
        """
        OnEvent(propgrid, property, wnd_primary, event) -> bool
        
        Handles events.
        """

    def GetValueFromControl(self, property: PGProperty, ctrl: wx.Window) -> Tuple[bool, PGVariant]:
        """
        GetValueFromControl(property, ctrl) -> Tuple[bool, PGVariant]
        
        Returns value from control, via parameter variant.
        """

    def SetValueToUnspecified(self, property: PGProperty, ctrl: wx.Window) -> None:
        """
        SetValueToUnspecified(property, ctrl) -> None
        
        Sets value in control to unspecified.
        """

    def SetControlAppearance(self, pg: PropertyGrid, property: PGProperty, ctrl: wx.Window, appearance: PGCell, oldAppearance: PGCell, unspecified: bool) -> None:
        """
        SetControlAppearance(pg, property, ctrl, appearance, oldAppearance, unspecified) -> None
        
        Called by property grid to set new appearance for the control.
        """

    def SetControlStringValue(self, property: PGProperty, ctrl: wx.Window, txt: str) -> None:
        """
        SetControlStringValue(property, ctrl, txt) -> None
        
        Sets control's value specifically from string.
        """

    def SetControlIntValue(self, property: PGProperty, ctrl: wx.Window, value: int) -> None:
        """
        SetControlIntValue(property, ctrl, value) -> None
        
        Sets control's value specifically from int (applies to choice etc.).
        """

    def InsertItem(self, ctrl: wx.Window, label: str, index: int) -> int:
        """
        InsertItem(ctrl, label, index) -> int
        
        Inserts item to existing control.
        """

    def DeleteItem(self, ctrl: wx.Window, index: int) -> None:
        """
        DeleteItem(ctrl, index) -> None
        
        Deletes item from existing control.
        """

    def OnFocus(self, property: PGProperty, wnd: wx.Window) -> None:
        """
        OnFocus(property, wnd) -> None
        
        Extra processing when control gains focus.
        """

    def CanContainCustomImage(self) -> bool:
        """
        CanContainCustomImage() -> bool
        
        Returns true if control itself can contain the custom image.
        """
    @property
    def Name(self) -> str: ...
# end of class PGEditor


class PGTextCtrlEditor(PGEditor):
    """
    PGTextCtrlEditor() -> None
    """

    def __init__(self) -> None:
        """
        PGTextCtrlEditor() -> None
        """

    def CreateControls(self, propgrid: PropertyGrid, property: PGProperty, pos: wx.Point, size: wx.Size) -> PGWindowList:
        """
        CreateControls(propgrid, property, pos, size) -> PGWindowList
        
        Instantiates editor controls.
        """

    def UpdateControl(self, property: PGProperty, ctrl: wx.Window) -> None:
        """
        UpdateControl(property, ctrl) -> None
        
        Loads value from property to the control.
        """

    def OnEvent(self, propgrid: PropertyGrid, property: PGProperty, wnd_primary: wx.Window, event: wx.Event) -> bool:
        """
        OnEvent(propgrid, property, wnd_primary, event) -> bool
        
        Handles events.
        """

    def GetValueFromControl(self, variant: PGVariant, property: PGProperty, ctrl: wx.Window) -> bool:
        """
        GetValueFromControl(variant, property, ctrl) -> bool
        
        Returns value from control, via parameter variant.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns pointer to the name of the editor.
        """

    def SetControlStringValue(self, property: PGProperty, ctrl: wx.Window, txt: str) -> None:
        """
        SetControlStringValue(property, ctrl, txt) -> None
        
        Sets control's value specifically from string.
        """

    def OnFocus(self, property: PGProperty, wnd: wx.Window) -> None:
        """
        OnFocus(property, wnd) -> None
        
        Extra processing when control gains focus.
        """

    @staticmethod
    def OnTextCtrlEvent(propgrid: PropertyGrid, property: PGProperty, ctrl: wx.Window, event: wx.Event) -> bool:
        """
        OnTextCtrlEvent(propgrid, property, ctrl, event) -> bool
        """

    @staticmethod
    def GetTextCtrlValueFromControl(variant: PGVariant, property: PGProperty, ctrl: wx.Window) -> bool:
        """
        GetTextCtrlValueFromControl(variant, property, ctrl) -> bool
        """
    @property
    def Name(self) -> str: ...
# end of class PGTextCtrlEditor


class PGChoiceEditor(PGEditor):
    """
    PGChoiceEditor() -> None
    """

    def __init__(self) -> None:
        """
        PGChoiceEditor() -> None
        """

    def CreateControls(self, propgrid: PropertyGrid, property: PGProperty, pos: wx.Point, size: wx.Size) -> PGWindowList:
        """
        CreateControls(propgrid, property, pos, size) -> PGWindowList
        
        Instantiates editor controls.
        """

    def UpdateControl(self, property: PGProperty, ctrl: wx.Window) -> None:
        """
        UpdateControl(property, ctrl) -> None
        
        Loads value from property to the control.
        """

    def OnEvent(self, propgrid: PropertyGrid, property: PGProperty, wnd_primary: wx.Window, event: wx.Event) -> bool:
        """
        OnEvent(propgrid, property, wnd_primary, event) -> bool
        
        Handles events.
        """

    def GetValueFromControl(self, variant: PGVariant, property: PGProperty, ctrl: wx.Window) -> bool:
        """
        GetValueFromControl(variant, property, ctrl) -> bool
        
        Returns value from control, via parameter variant.
        """

    def SetValueToUnspecified(self, property: PGProperty, ctrl: wx.Window) -> None:
        """
        SetValueToUnspecified(property, ctrl) -> None
        
        Sets value in control to unspecified.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns pointer to the name of the editor.
        """

    def SetControlIntValue(self, property: PGProperty, ctrl: wx.Window, value: int) -> None:
        """
        SetControlIntValue(property, ctrl, value) -> None
        
        Sets control's value specifically from int (applies to choice etc.).
        """

    def SetControlStringValue(self, property: PGProperty, ctrl: wx.Window, txt: str) -> None:
        """
        SetControlStringValue(property, ctrl, txt) -> None
        
        Sets control's value specifically from string.
        """

    def InsertItem(self, ctrl: wx.Window, label: str, index: int) -> int:
        """
        InsertItem(ctrl, label, index) -> int
        
        Inserts item to existing control.
        """

    def DeleteItem(self, ctrl: wx.Window, index: int) -> None:
        """
        DeleteItem(ctrl, index) -> None
        
        Deletes item from existing control.
        """

    def CanContainCustomImage(self) -> bool:
        """
        CanContainCustomImage() -> bool
        
        Returns true if control itself can contain the custom image.
        """

    def CreateControlsBase(self, propgrid: PropertyGrid, property: PGProperty, pos: wx.Point, sz: wx.Size, extraStyle: int) -> wx.Window:
        """
        CreateControlsBase(propgrid, property, pos, sz, extraStyle) -> wx.Window
        """
    @property
    def Name(self) -> str: ...
# end of class PGChoiceEditor


class PGComboBoxEditor(PGChoiceEditor):
    """
    PGComboBoxEditor() -> None
    """

    def __init__(self) -> None:
        """
        PGComboBoxEditor() -> None
        """

    def CreateControls(self, propgrid: PropertyGrid, property: PGProperty, pos: wx.Point, size: wx.Size) -> PGWindowList:
        """
        CreateControls(propgrid, property, pos, size) -> PGWindowList
        
        Instantiates editor controls.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns pointer to the name of the editor.
        """

    def UpdateControl(self, property: PGProperty, ctrl: wx.Window) -> None:
        """
        UpdateControl(property, ctrl) -> None
        
        Loads value from property to the control.
        """

    def OnEvent(self, propgrid: PropertyGrid, property: PGProperty, wnd_primary: wx.Window, event: wx.Event) -> bool:
        """
        OnEvent(propgrid, property, wnd_primary, event) -> bool
        
        Handles events.
        """

    def GetValueFromControl(self, variant: PGVariant, property: PGProperty, ctrl: wx.Window) -> bool:
        """
        GetValueFromControl(variant, property, ctrl) -> bool
        
        Returns value from control, via parameter variant.
        """

    def OnFocus(self, property: PGProperty, wnd: wx.Window) -> None:
        """
        OnFocus(property, wnd) -> None
        
        Extra processing when control gains focus.
        """
    @property
    def Name(self) -> str: ...
# end of class PGComboBoxEditor


class PGChoiceAndButtonEditor(PGChoiceEditor):
    """
    PGChoiceAndButtonEditor() -> None
    """

    def __init__(self) -> None:
        """
        PGChoiceAndButtonEditor() -> None
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns pointer to the name of the editor.
        """

    def CreateControls(self, propgrid: PropertyGrid, property: PGProperty, pos: wx.Point, size: wx.Size) -> PGWindowList:
        """
        CreateControls(propgrid, property, pos, size) -> PGWindowList
        
        Instantiates editor controls.
        """
    @property
    def Name(self) -> str: ...
# end of class PGChoiceAndButtonEditor


class PGTextCtrlAndButtonEditor(PGTextCtrlEditor):
    """
    PGTextCtrlAndButtonEditor() -> None
    """

    def __init__(self) -> None:
        """
        PGTextCtrlAndButtonEditor() -> None
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns pointer to the name of the editor.
        """

    def CreateControls(self, propgrid: PropertyGrid, property: PGProperty, pos: wx.Point, size: wx.Size) -> PGWindowList:
        """
        CreateControls(propgrid, property, pos, size) -> PGWindowList
        
        Instantiates editor controls.
        """
    @property
    def Name(self) -> str: ...
# end of class PGTextCtrlAndButtonEditor


class PGCheckBoxEditor(PGEditor):
    """
    PGCheckBoxEditor() -> None
    """

    def __init__(self) -> None:
        """
        PGCheckBoxEditor() -> None
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns pointer to the name of the editor.
        """

    def CreateControls(self, propgrid: PropertyGrid, property: PGProperty, pos: wx.Point, size: wx.Size) -> PGWindowList:
        """
        CreateControls(propgrid, property, pos, size) -> PGWindowList
        
        Instantiates editor controls.
        """

    def UpdateControl(self, property: PGProperty, ctrl: wx.Window) -> None:
        """
        UpdateControl(property, ctrl) -> None
        
        Loads value from property to the control.
        """

    def OnEvent(self, propgrid: PropertyGrid, property: PGProperty, wnd_primary: wx.Window, event: wx.Event) -> bool:
        """
        OnEvent(propgrid, property, wnd_primary, event) -> bool
        
        Handles events.
        """

    def GetValueFromControl(self, variant: PGVariant, property: PGProperty, ctrl: wx.Window) -> bool:
        """
        GetValueFromControl(variant, property, ctrl) -> bool
        
        Returns value from control, via parameter variant.
        """

    def SetValueToUnspecified(self, property: PGProperty, ctrl: wx.Window) -> None:
        """
        SetValueToUnspecified(property, ctrl) -> None
        
        Sets value in control to unspecified.
        """

    def DrawValue(self, dc: wx.DC, rect: wx.Rect, property: PGProperty, text: str) -> None:
        """
        DrawValue(dc, rect, property, text) -> None
        
        Draws value for given property.
        """

    def SetControlIntValue(self, property: PGProperty, ctrl: wx.Window, value: int) -> None:
        """
        SetControlIntValue(property, ctrl, value) -> None
        
        Sets control's value specifically from int (applies to choice etc.).
        """
    @property
    def Name(self) -> str: ...
# end of class PGCheckBoxEditor


class PGEditorDialogAdapter(wx.Object):
    """
    PGEditorDialogAdapter() -> None
    
    Derive a class from this to adapt an existing editor dialog or
    function to be used when editor button of a property is pushed.
    """

    def __init__(self) -> None:
        """
        PGEditorDialogAdapter() -> None
        
        Derive a class from this to adapt an existing editor dialog or
        function to be used when editor button of a property is pushed.
        """
    m_clientData: Any

    def ShowDialog(self, propGrid: PropertyGrid, property: PGProperty) -> bool:
        """
        ShowDialog(propGrid, property) -> bool
        """

    def DoShowDialog(self, propGrid: PropertyGrid, property: PGProperty) -> bool:
        """
        DoShowDialog(propGrid, property) -> bool
        """

    def SetValue(self, value: PGVariant) -> None:
        """
        SetValue(value) -> None
        """

    def GetValue(self) -> PGVariant:
        """
        GetValue() -> PGVariant
        
        This method is typically only used if deriving class from existing
        adapter with value conversion purposes.
        """
    @property
    def Value(self) -> PGVariant: ...
    @Value.setter
    def Value(self, value: PGVariant, /) -> None: ...
# end of class PGEditorDialogAdapter


class PGMultiButton(wx.Window):
    """
    PGMultiButton(pg, sz) -> None
    
    This class can be used to have multiple buttons in a property editor.
    """

    def __init__(self, pg: PropertyGrid, sz: wx.Size) -> None:
        """
        PGMultiButton(pg, sz) -> None
        
        This class can be used to have multiple buttons in a property editor.
        """

    @overload
    def Add(self, bitmap: wx.BitmapBundle, id: int=-2) -> None:
        ...

    @overload
    def Add(self, label: str, id: int=-2) -> None:
        """
        Add(label, id=-2) -> None
        Add(bitmap, id=-2) -> None
        
        Adds new button, with given label.
        """

    def Finalize(self, propGrid: PropertyGrid, pos: wx.Point) -> None:
        """
        Finalize(propGrid, pos) -> None
        
        Call this in CreateControls() of your custom editor class after all
        buttons have been added.
        """

    def GetButton(self, i: int) -> wx.Window:
        """
        GetButton(i) -> wx.Window
        
        Returns pointer to one of the buttons.
        """

    def GetButtonId(self, i: int) -> int:
        """
        GetButtonId(i) -> int
        
        Returns Id of one of the buttons.
        """

    def GetCount(self) -> int:
        """
        GetCount() -> int
        
        Returns number of buttons.
        """

    def GetPrimarySize(self) -> wx.Size:
        """
        GetPrimarySize() -> wx.Size
        
        Returns size of primary editor control, as appropriately reduced by
        number of buttons present.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    def AddButton(self, label, id=-2):
        """
        A simple wrapper around the PGMultiButton.Add method, for backwards compatibility.
        """

    def AddBitmapButton(self, bitmap, id=-2):
        """
        A simple wrapper around the PGMultiButton.Add method, for backwards compatibility.
        """
    @property
    def Count(self) -> int: ...
    @property
    def PrimarySize(self) -> wx.Size: ...
# end of class PGMultiButton

PGEditor_TextCtrl: PGEditor
PGEditor_Choice: PGEditor
PGEditor_ComboBox: PGEditor
PGEditor_TextCtrlAndButton: PGEditor
PGEditor_CheckBox: PGEditor
PGEditor_ChoiceAndButton: PGEditor
#-- end-propgrideditors --#
#-- begin-propgridpagestate --#

class _PG_ITERATOR_FLAGS(IntEnum):
    PG_ITERATE_PROPERTIES = auto()
    PG_ITERATE_HIDDEN = auto()
    PG_ITERATE_FIXED_CHILDREN = auto()
    PG_ITERATE_CATEGORIES = auto()
    PG_ITERATE_ALL_PARENTS = auto()
    PG_ITERATE_ALL_PARENTS_RECURSIVELY = auto()
    PG_ITERATOR_FLAGS_ALL = auto()
    PG_ITERATOR_MASK_OP_ITEM = auto()
    PG_ITERATOR_MASK_OP_PARENT = auto()
    PG_ITERATE_VISIBLE = auto()
    PG_ITERATE_ALL = auto()
    PG_ITERATE_NORMAL = auto()
    PG_ITERATE_DEFAULT = auto()
PG_ITERATOR_FLAGS: TypeAlias = Union[_PG_ITERATOR_FLAGS, int]
PG_ITERATE_PROPERTIES = _PG_ITERATOR_FLAGS.PG_ITERATE_PROPERTIES
PG_ITERATE_HIDDEN = _PG_ITERATOR_FLAGS.PG_ITERATE_HIDDEN
PG_ITERATE_FIXED_CHILDREN = _PG_ITERATOR_FLAGS.PG_ITERATE_FIXED_CHILDREN
PG_ITERATE_CATEGORIES = _PG_ITERATOR_FLAGS.PG_ITERATE_CATEGORIES
PG_ITERATE_ALL_PARENTS = _PG_ITERATOR_FLAGS.PG_ITERATE_ALL_PARENTS
PG_ITERATE_ALL_PARENTS_RECURSIVELY = _PG_ITERATOR_FLAGS.PG_ITERATE_ALL_PARENTS_RECURSIVELY
PG_ITERATOR_FLAGS_ALL = _PG_ITERATOR_FLAGS.PG_ITERATOR_FLAGS_ALL
PG_ITERATOR_MASK_OP_ITEM = _PG_ITERATOR_FLAGS.PG_ITERATOR_MASK_OP_ITEM
PG_ITERATOR_MASK_OP_PARENT = _PG_ITERATOR_FLAGS.PG_ITERATOR_MASK_OP_PARENT
PG_ITERATE_VISIBLE = _PG_ITERATOR_FLAGS.PG_ITERATE_VISIBLE
PG_ITERATE_ALL = _PG_ITERATOR_FLAGS.PG_ITERATE_ALL
PG_ITERATE_NORMAL = _PG_ITERATOR_FLAGS.PG_ITERATE_NORMAL
PG_ITERATE_DEFAULT = _PG_ITERATOR_FLAGS.PG_ITERATE_DEFAULT

class PropertyGridHitTestResult:
    """
    PropertyGridHitTestResult() -> None
    """

    def __init__(self) -> None:
        """
        PropertyGridHitTestResult() -> None
        """

    def GetColumn(self) -> int:
        """
        GetColumn() -> int
        
        Returns column hit.
        """

    def GetProperty(self) -> PGProperty:
        """
        GetProperty() -> PGProperty
        
        Returns property hit.
        """

    def GetSplitter(self) -> int:
        """
        GetSplitter() -> int
        
        Returns index of splitter hit, -1 for none.
        """

    def GetSplitterHitOffset(self) -> int:
        """
        GetSplitterHitOffset() -> int
        
        If splitter hit, then this member function returns offset to the exact
        splitter position.
        """
    @property
    def Column(self) -> int: ...
    @property
    def Property(self) -> PGProperty: ...
    @property
    def Splitter(self) -> int: ...
    @property
    def SplitterHitOffset(self) -> int: ...
# end of class PropertyGridHitTestResult


class PropertyGridIteratorBase:
    """
    PropertyGridIteratorBase() -> None
    
    Base for wxPropertyGridIterator classes.
    """

    def __init__(self) -> None:
        """
        PropertyGridIteratorBase() -> None
        
        Base for wxPropertyGridIterator classes.
        """

    def Assign(self, it: PropertyGridIteratorBase) -> None:
        """
        Assign(it) -> None
        """

    def AtEnd(self) -> bool:
        """
        AtEnd() -> bool
        """

    def GetProperty(self) -> PGProperty:
        """
        GetProperty() -> PGProperty
        
        Get current property.
        """

    @overload
    def Init(self, state: PropertyGridPageState, flags: int, startPos: int=wx.TOP, dir: int=0) -> None:
        ...

    @overload
    def Init(self, state: PropertyGridPageState, flags: int, property: PGProperty, dir: int=1) -> None:
        """
        Init(state, flags, property, dir=1) -> None
        Init(state, flags, startPos=wx.TOP, dir=0) -> None
        """

    def Next(self, iterateChildren: bool=True) -> None:
        """
        Next(iterateChildren=True) -> None
        
        Iterate to the next property.
        """

    def Prev(self) -> None:
        """
        Prev() -> None
        
        Iterate to the previous property.
        """

    def SetBaseParent(self, baseParent: PGProperty) -> None:
        """
        SetBaseParent(baseParent) -> None
        
        Set base parent, i.e.
        """
    @property
    def Property(self) -> PGProperty: ...
# end of class PropertyGridIteratorBase


class PropertyGridIterator(PropertyGridIteratorBase):
    """
    PropertyGridIterator() -> None
    PropertyGridIterator(state, flags=PG_ITERATE_DEFAULT, property=None, dir=1) -> None
    PropertyGridIterator(state, flags, startPos, dir=0) -> None
    PropertyGridIterator(it) -> None
    """

    @overload
    def __init__(self, state: PropertyGridPageState, flags: int=PG_ITERATE_DEFAULT, property: Optional[PGProperty]=None, dir: int=1) -> None:
        ...

    @overload
    def __init__(self, state: PropertyGridPageState, flags: int, startPos: int, dir: int=0) -> None:
        ...

    @overload
    def __init__(self, it: PropertyGridIterator) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        PropertyGridIterator() -> None
        PropertyGridIterator(state, flags=PG_ITERATE_DEFAULT, property=None, dir=1) -> None
        PropertyGridIterator(state, flags, startPos, dir=0) -> None
        PropertyGridIterator(it) -> None
        """
# end of class PropertyGridIterator


class PGVIterator:
    """
    PGVIterator() -> None
    PGVIterator(it) -> None
    """

    @overload
    def __init__(self, it: PGVIterator) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        PGVIterator() -> None
        PGVIterator(it) -> None
        """

    def UnRef(self) -> None:
        """
        UnRef() -> None
        """

    def Next(self) -> None:
        """
        Next() -> None
        """

    def AtEnd(self) -> bool:
        """
        AtEnd() -> bool
        """

    def GetProperty(self) -> PGProperty:
        """
        GetProperty() -> PGProperty
        """
    @property
    def Property(self) -> PGProperty: ...
# end of class PGVIterator


class PropertyGridPageState:
    """
    PropertyGridPageState() -> None
    
    Contains low-level property page information (properties, column
    widths, etc.) of a single wxPropertyGrid or single wxPropertyGridPage.
    """

    def __init__(self) -> None:
        """
        PropertyGridPageState() -> None
        
        Contains low-level property page information (properties, column
        widths, etc.) of a single wxPropertyGrid or single wxPropertyGridPage.
        """

    def CheckColumnWidths(self, widthChange: int=0) -> None:
        """
        CheckColumnWidths(widthChange=0) -> None
        
        Makes sure all columns have minimum width.
        """

    def DoDelete(self, item: PGProperty, doDelete: bool=True) -> None:
        """
        DoDelete(item, doDelete=True) -> None
        
        Override this member function to add custom behaviour on property
        deletion.
        """

    def DoInsert(self, parent: PGProperty, index: int, property: PGProperty) -> PGProperty:
        """
        DoInsert(parent, index, property) -> PGProperty
        
        Override this member function to add custom behaviour on property
        insertion.
        """

    def DoSetSplitterPosition(self, pos: int, splitterColumn: int=0, flags: int=0) -> None:
        """
        DoSetSplitterPosition(pos, splitterColumn=0, flags=0) -> None
        
        This needs to be overridden in grid used the manager so that splitter
        changes can be propagated to other pages.
        """

    def EnableCategories(self, enable: bool) -> bool:
        """
        EnableCategories(enable) -> bool
        """

    def EnsureVirtualHeight(self) -> None:
        """
        EnsureVirtualHeight() -> None
        
        Make sure virtual height is up-to-date.
        """

    def GetVirtualHeight(self) -> int:
        """
        GetVirtualHeight() -> int
        
        Returns (precalculated) height of contained visible properties.
        """

    def GetActualVirtualHeight(self) -> int:
        """
        GetActualVirtualHeight() -> int
        
        Returns actual height of contained visible properties.
        """

    def GetColumnCount(self) -> int:
        """
        GetColumnCount() -> int
        """

    def GetColumnMinWidth(self, column: int) -> int:
        """
        GetColumnMinWidth(column) -> int
        """

    def GetColumnWidth(self, column: int) -> int:
        """
        GetColumnWidth(column) -> int
        """

    def GetGrid(self) -> PropertyGrid:
        """
        GetGrid() -> PropertyGrid
        """

    def GetLastItem(self, flags: int=PG_ITERATE_DEFAULT) -> PGProperty:
        """
        GetLastItem(flags=PG_ITERATE_DEFAULT) -> PGProperty
        
        Returns last item which could be iterated using given flags.
        """

    def GetSelection(self) -> PGProperty:
        """
        GetSelection() -> PGProperty
        
        Returns currently selected property.
        """

    def GetPropertyCategory(self, p: PGProperty) -> PropertyCategory:
        """
        GetPropertyCategory(p) -> PropertyCategory
        """

    def GetVirtualWidth(self) -> int:
        """
        GetVirtualWidth() -> int
        
        Returns combined width of margin and all the columns.
        """

    def GetColumnFullWidth(self, p: PGProperty, col: int) -> int:
        """
        GetColumnFullWidth(p, col) -> int
        """

    def HitTest(self, pt: wx.Point) -> PropertyGridHitTestResult:
        """
        HitTest(pt) -> PropertyGridHitTestResult
        
        Returns information about arbitrary position in the grid.
        """

    def IsDisplayed(self) -> bool:
        """
        IsDisplayed() -> bool
        
        Returns true if page is visibly displayed.
        """

    def IsInNonCatMode(self) -> bool:
        """
        IsInNonCatMode() -> bool
        """

    def VirtualHeightChanged(self) -> None:
        """
        VirtualHeightChanged() -> None
        
        Called after virtual height needs to be recalculated.
        """
    @property
    def ActualVirtualHeight(self) -> int: ...
    @property
    def ColumnCount(self) -> int: ...
    @property
    def Grid(self) -> PropertyGrid: ...
    @property
    def LastItem(self) -> PGProperty: ...
    @property
    def Selection(self) -> PGProperty: ...
    @property
    def VirtualHeight(self) -> int: ...
    @property
    def VirtualWidth(self) -> int: ...
# end of class PropertyGridPageState

#-- end-propgridpagestate --#
#-- begin-propgridiface --#

class PGPropArgCls:
    """
    PGPropArgCls(property) -> None
    PGPropArgCls(str) -> None
    PGPropArgCls(id) -> None
    """

    @overload
    def __init__(self, str: str) -> None:
        ...

    @overload
    def __init__(self, id: Union[PGPropArgCls, str, _None]) -> None:
        ...

    @overload
    def __init__(self, property: PGProperty) -> None:
        """
        PGPropArgCls(property) -> None
        PGPropArgCls(str) -> None
        PGPropArgCls(id) -> None
        """

    @overload
    def GetPtr(self, iface: PropertyGridInterface) -> PGProperty:
        ...

    @overload
    def GetPtr(self) -> PGProperty:
        """
        GetPtr() -> PGProperty
        GetPtr(iface) -> PGProperty
        """

    def GetPtr0(self) -> PGProperty:
        """
        GetPtr0() -> PGProperty
        """

    def HasName(self) -> bool:
        """
        HasName() -> bool
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        """
    @property
    def Name(self) -> str: ...
    @property
    def Ptr(self) -> PGProperty: ...
    @property
    def Ptr0(self) -> PGProperty: ...
# end of class PGPropArgCls


class PropertyGridInterface:
    """
    Most of the shared property manipulation interface shared by
    wxPropertyGrid, wxPropertyGridPage, and wxPropertyGridManager is
    defined in this class.
    """

    class _EditableStateFlags(IntFlag):
        SelectionState = auto()
        ExpandedState = auto()
        ScrollPosState = auto()
        PageState = auto()
        SplitterPosState = auto()
        DescBoxState = auto()
        AllStates = auto()
    EditableStateFlags: TypeAlias = Union[_EditableStateFlags, int]
    SelectionState = _EditableStateFlags.SelectionState
    ExpandedState = _EditableStateFlags.ExpandedState
    ScrollPosState = _EditableStateFlags.ScrollPosState
    PageState = _EditableStateFlags.PageState
    SplitterPosState = _EditableStateFlags.SplitterPosState
    DescBoxState = _EditableStateFlags.DescBoxState
    AllStates = _EditableStateFlags.AllStates

    def Append(self, property: PGProperty) -> PGProperty:
        """
        Append(property) -> PGProperty
        
        Appends property to the list.
        """

    def AppendIn(self, id: Union[PGPropArgCls, str, _None], newProperty: PGProperty) -> PGProperty:
        """
        AppendIn(id, newProperty) -> PGProperty
        
        Same as Append(), but appends under given parent property.
        """

    def BeginAddChildren(self, id: Union[PGPropArgCls, str, _None]) -> None:
        """
        BeginAddChildren(id) -> None
        
        In order to add new items into a property with private children (for
        instance, wxFlagsProperty), you need to call this method.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Deletes all properties.
        """

    def ClearSelection(self, validation: bool=False) -> bool:
        """
        ClearSelection(validation=False) -> bool
        
        Clears current selection, if any.
        """

    def ClearModifiedStatus(self) -> None:
        """
        ClearModifiedStatus() -> None
        
        Resets modified status of all properties.
        """

    def Collapse(self, id: Union[PGPropArgCls, str, _None]) -> bool:
        """
        Collapse(id) -> bool
        
        Collapses given category or property with children.
        """

    def CollapseAll(self) -> bool:
        """
        CollapseAll() -> bool
        
        Collapses all items that can be collapsed.
        """

    def ChangePropertyValue(self, id: Union[PGPropArgCls, str, _None], newValue: PGVariant) -> bool:
        """
        ChangePropertyValue(id, newValue) -> bool
        
        Changes value of a property, as if by user.
        """

    def DeleteProperty(self, id: Union[PGPropArgCls, str, _None]) -> None:
        """
        DeleteProperty(id) -> None
        
        Removes and deletes a property and any children.
        """

    def DisableProperty(self, id: Union[PGPropArgCls, str, _None]) -> bool:
        """
        DisableProperty(id) -> bool
        
        Disables a property.
        """

    def EditorValidate(self) -> bool:
        """
        EditorValidate() -> bool
        
        Returns true if all property grid data changes have been committed.
        """

    def EnableProperty(self, id: Union[PGPropArgCls, str, _None], enable: bool=True) -> bool:
        """
        EnableProperty(id, enable=True) -> bool
        
        Enables or disables property.
        """

    def EndAddChildren(self, id: Union[PGPropArgCls, str, _None]) -> None:
        """
        EndAddChildren(id) -> None
        
        Called after population of property with fixed children has finished.
        """

    def Expand(self, id: Union[PGPropArgCls, str, _None]) -> bool:
        """
        Expand(id) -> bool
        
        Expands given category or property with children.
        """

    def ExpandAll(self, expand: bool=True) -> bool:
        """
        ExpandAll(expand=True) -> bool
        
        Expands all items that can be expanded.
        """

    def GetColumnProportion(self, column: int) -> int:
        """
        GetColumnProportion(column) -> int
        
        Returns auto-resize proportion of the given column.
        """

    def GetFirstChild(self, id: Union[PGPropArgCls, str, _None]) -> PGProperty:
        """
        GetFirstChild(id) -> PGProperty
        
        Returns id of first child of given property.
        """

    @overload
    def GetIterator(self, flags: int, startPos: int) -> PropertyGridIterator:
        ...

    @overload
    def GetIterator(self, flags: int=PG_ITERATE_DEFAULT, firstProp: Optional[PGProperty]=None) -> PropertyGridIterator:
        """
        GetIterator(flags=PG_ITERATE_DEFAULT, firstProp=None) -> PropertyGridIterator
        GetIterator(flags, startPos) -> PropertyGridIterator
        
        Returns iterator class instance.
        """

    def GetFirst(self, flags: int=PG_ITERATE_ALL) -> PGProperty:
        """
        GetFirst(flags=PG_ITERATE_ALL) -> PGProperty
        
        Returns id of first item that matches given criteria.
        """

    def GetProperty(self, name: str) -> PGProperty:
        """
        GetProperty(name) -> PGProperty
        
        Returns pointer to a property with given name (case-sensitive).
        """

    def GetPropertiesWithFlag(self, targetArr: List[PGProperty], flags: PGProperty.FlagType, inverse: bool=False, iterFlags: int=PG_ITERATE_PROPERTIES|PG_ITERATE_HIDDEN|PG_ITERATE_CATEGORIES) -> None:
        """
        GetPropertiesWithFlag(targetArr, flags, inverse=False, iterFlags=PG_ITERATE_PROPERTIES|PG_ITERATE_HIDDEN|PG_ITERATE_CATEGORIES) -> None
        
        Adds to targetArr pointers to properties that have given flags set.
        """

    def GetPropertyAttribute(self, id: Union[PGPropArgCls, str, _None], attrName: str) -> PGVariant:
        """
        GetPropertyAttribute(id, attrName) -> PGVariant
        
        Returns value of given attribute.
        """

    def GetPropertyAttributes(self, id: Union[PGPropArgCls, str, _None]) -> PGAttributeStorage:
        """
        GetPropertyAttributes(id) -> PGAttributeStorage
        
        Returns map-like storage of property's attributes.
        """

    def GetPropertyBackgroundColour(self, id: Union[PGPropArgCls, str, _None]) -> wx.Colour:
        """
        GetPropertyBackgroundColour(id) -> wx.Colour
        
        Returns background colour of first cell of a property.
        """

    def GetPropertyCategory(self, id: Union[PGPropArgCls, str, _None]) -> PropertyCategory:
        """
        GetPropertyCategory(id) -> PropertyCategory
        
        Returns pointer of property's nearest parent category.
        """

    def GetPropertyByLabel(self, label: str) -> PGProperty:
        """
        GetPropertyByLabel(label) -> PGProperty
        
        Returns first property which label matches given string.
        """

    @overload
    def GetPropertyByName(self, name: str, subname: str) -> PGProperty:
        ...

    @overload
    def GetPropertyByName(self, name: str) -> PGProperty:
        """
        GetPropertyByName(name) -> PGProperty
        GetPropertyByName(name, subname) -> PGProperty
        
        Returns pointer to a property with given name (case-sensitive).
        """

    def GetPropertyEditor(self, id: Union[PGPropArgCls, str, _None]) -> PGEditor:
        """
        GetPropertyEditor(id) -> PGEditor
        
        Returns property's editor.
        """

    def GetPropertyHelpString(self, id: Union[PGPropArgCls, str, _None]) -> str:
        """
        GetPropertyHelpString(id) -> str
        
        Returns help string associated with a property.
        """

    def GetPropertyImage(self, id: Union[PGPropArgCls, str, _None]) -> wx.Bitmap:
        """
        GetPropertyImage(id) -> wx.Bitmap
        
        Returns property's custom value image (NULL of none).
        """

    def GetPropertyLabel(self, id: Union[PGPropArgCls, str, _None]) -> str:
        """
        GetPropertyLabel(id) -> str
        
        Returns label of a property.
        """

    def GetPropertyName(self, property: PGProperty) -> str:
        """
        GetPropertyName(property) -> str
        
        Returns property's name, by which it is globally accessible.
        """

    def GetPropertyParent(self, id: Union[PGPropArgCls, str, _None]) -> PGProperty:
        """
        GetPropertyParent(id) -> PGProperty
        
        Returns parent item of a property.
        """

    def GetPropertyTextColour(self, id: Union[PGPropArgCls, str, _None]) -> wx.Colour:
        """
        GetPropertyTextColour(id) -> wx.Colour
        
        Returns text colour of first cell of a property.
        """

    def GetPropertyValidator(self, id: Union[PGPropArgCls, str, _None]) -> wx.Validator:
        """
        GetPropertyValidator(id) -> wx.Validator
        
        Returns validator of a property as a reference, which you can pass to
        any number of SetPropertyValidator.
        """

    def GetPropertyValue(self, id: Union[PGPropArgCls, str, _None]) -> PGVariant:
        """
        GetPropertyValue(id) -> PGVariant
        
        Returns property's value as wxVariant.
        """

    def GetPropertyValueAsArrayInt(self, id: Union[PGPropArgCls, str, _None]) -> List[int]:
        """
        GetPropertyValueAsArrayInt(id) -> List[int]
        
        Return's property's value as wxArrayInt.
        """

    def GetPropertyValueAsArrayString(self, id: Union[PGPropArgCls, str, _None]) -> List[str]:
        """
        GetPropertyValueAsArrayString(id) -> List[str]
        
        Returns property's value as wxArrayString.
        """

    def GetPropertyValueAsBool(self, id: Union[PGPropArgCls, str, _None]) -> bool:
        """
        GetPropertyValueAsBool(id) -> bool
        
        Returns property's value as bool.
        """

    def GetPropertyValueAsDateTime(self, id: Union[PGPropArgCls, str, _None]) -> wx.DateTime:
        """
        GetPropertyValueAsDateTime(id) -> wx.DateTime
        
        Return's property's value as wxDateTime.
        """

    def GetPropertyValueAsDouble(self, id: Union[PGPropArgCls, str, _None]) -> float:
        """
        GetPropertyValueAsDouble(id) -> float
        
        Returns property's value as double-precision floating point number.
        """

    def GetPropertyValueAsInt(self, id: Union[PGPropArgCls, str, _None]) -> int:
        """
        GetPropertyValueAsInt(id) -> int
        
        Returns property's value as integer.
        """

    def GetPropertyValueAsLong(self, id: Union[PGPropArgCls, str, _None]) -> int:
        """
        GetPropertyValueAsLong(id) -> int
        
        Returns property's value as integer.
        """

    def GetPropertyValueAsLongLong(self, id: Union[PGPropArgCls, str, _None]) -> LongLong_t:
        """
        GetPropertyValueAsLongLong(id) -> LongLong_t
        
        Returns property's value as native signed 64-bit integer.
        """

    def GetPropertyValueAsString(self, id: Union[PGPropArgCls, str, _None]) -> str:
        """
        GetPropertyValueAsString(id) -> str
        
        Returns property's value as wxString.
        """

    def GetPropertyValueAsULong(self, id: Union[PGPropArgCls, str, _None]) -> int:
        """
        GetPropertyValueAsULong(id) -> int
        
        Returns property's value as unsigned integer.
        """

    def GetPropertyValueAsULongLong(self, id: Union[PGPropArgCls, str, _None]) -> ULongLong_t:
        """
        GetPropertyValueAsULongLong(id) -> ULongLong_t
        
        Returns property's value as native unsigned 64-bit integer.
        """

    def GetSelectedProperties(self) -> List[PGProperty]:
        """
        GetSelectedProperties() -> List[PGProperty]
        
        Returns list of currently selected properties.
        """

    def GetSelection(self) -> PGProperty:
        """
        GetSelection() -> PGProperty
        
        Returns currently selected property.
        """

    def GetVIterator(self, flags: int) -> PGVIterator:
        """
        GetVIterator(flags) -> PGVIterator
        
        Similar to GetIterator(), but instead returns wxPGVIterator instance,
        which can be useful for forward-iterating through arbitrary property
        containers.
        """

    def HideProperty(self, id: Union[PGPropArgCls, str, _None], hide: bool=True, flags: int=PG_RECURSE) -> bool:
        """
        HideProperty(id, hide=True, flags=PG_RECURSE) -> bool
        
        Hides or reveals a property.
        """

    @overload
    def Insert(self, parent: Union[PGPropArgCls, str, _None], index: int, newProperty: PGProperty) -> PGProperty:
        ...

    @overload
    def Insert(self, priorThis: Union[PGPropArgCls, str, _None], newProperty: PGProperty) -> PGProperty:
        """
        Insert(priorThis, newProperty) -> PGProperty
        Insert(parent, index, newProperty) -> PGProperty
        
        Inserts property to the property container.
        """

    def IsPropertyCategory(self, id: Union[PGPropArgCls, str, _None]) -> bool:
        """
        IsPropertyCategory(id) -> bool
        
        Returns true if property is a category.
        """

    def IsPropertyEnabled(self, id: Union[PGPropArgCls, str, _None]) -> bool:
        """
        IsPropertyEnabled(id) -> bool
        
        Returns true if property is enabled.
        """

    def IsPropertyExpanded(self, id: Union[PGPropArgCls, str, _None]) -> bool:
        """
        IsPropertyExpanded(id) -> bool
        
        Returns true if given property is expanded.
        """

    def IsPropertyModified(self, id: Union[PGPropArgCls, str, _None]) -> bool:
        """
        IsPropertyModified(id) -> bool
        
        Returns true if property has been modified after value set or modify
        flag clear by software.
        """

    def IsPropertySelected(self, id: Union[PGPropArgCls, str, _None]) -> bool:
        """
        IsPropertySelected(id) -> bool
        
        Returns true if property is selected.
        """

    def IsPropertyShown(self, id: Union[PGPropArgCls, str, _None]) -> bool:
        """
        IsPropertyShown(id) -> bool
        
        Returns true if property is shown (i.e.
        """

    def IsPropertyValueUnspecified(self, id: Union[PGPropArgCls, str, _None]) -> bool:
        """
        IsPropertyValueUnspecified(id) -> bool
        
        Returns true if property value is set to unspecified.
        """

    def LimitPropertyEditing(self, id: Union[PGPropArgCls, str, _None], limit: bool=True) -> None:
        """
        LimitPropertyEditing(id, limit=True) -> None
        
        Disables (limit = true) or enables (limit = false) wxTextCtrl editor
        of a property, if it is not the sole mean to edit the value.
        """

    def RefreshGrid(self, state: Optional[PropertyGridPageState]=None) -> None:
        """
        RefreshGrid(state=None) -> None
        
        If state is shown in its grid, refresh it now.
        """

    def RemoveProperty(self, id: Union[PGPropArgCls, str, _None]) -> PGProperty:
        """
        RemoveProperty(id) -> PGProperty
        
        Removes a property.
        """

    def ReplaceProperty(self, id: Union[PGPropArgCls, str, _None], property: PGProperty) -> PGProperty:
        """
        ReplaceProperty(id, property) -> PGProperty
        
        Replaces property with id with newly created one.
        """

    def RestoreEditableState(self, src: str, restoreStates: int=AllStates) -> bool:
        """
        RestoreEditableState(src, restoreStates=AllStates) -> bool
        
        Restores user-editable state.
        """

    def SaveEditableState(self, includedStates: int=AllStates) -> str:
        """
        SaveEditableState(includedStates=AllStates) -> str
        
        Used to acquire user-editable state (selected property, expanded
        properties, scrolled position, splitter positions).
        """

    def SetColumnProportion(self, column: int, proportion: int) -> bool:
        """
        SetColumnProportion(column, proportion) -> bool
        
        Set proportion of an auto-stretchable column.
        """

    def SetPropertyAttribute(self, id: Union[PGPropArgCls, str, _None], attrName: str, value: PGVariant, argFlags: int=0) -> None:
        """
        SetPropertyAttribute(id, attrName, value, argFlags=0) -> None
        
        Sets an attribute for this property.
        """

    def SetPropertyAttributeAll(self, attrName: str, value: PGVariant) -> None:
        """
        SetPropertyAttributeAll(attrName, value) -> None
        
        Sets property attribute for all applicable properties.
        """

    def SetPropertyBackgroundColour(self, id: Union[PGPropArgCls, str, _None], colour: wx.Colour, flags: int=PG_RECURSE) -> None:
        """
        SetPropertyBackgroundColour(id, colour, flags=PG_RECURSE) -> None
        
        Sets background colour of given property.
        """

    def SetPropertyCell(self, id: Union[PGPropArgCls, str, _None], column: int, text: str='', bitmap: wx.BitmapBundle=wx.BitmapBundle(), fgCol: wx.Colour=wx.NullColour, bgCol: wx.Colour=wx.NullColour) -> None:
        """
        SetPropertyCell(id, column, text='', bitmap=wx.BitmapBundle(), fgCol=wx.NullColour, bgCol=wx.NullColour) -> None
        
        Sets text, bitmap, and colours for given column's cell.
        """

    def SetPropertyColoursToDefault(self, id: Union[PGPropArgCls, str, _None], flags: int=PG_DONT_RECURSE) -> None:
        """
        SetPropertyColoursToDefault(id, flags=PG_DONT_RECURSE) -> None
        
        Resets text and background colours of given property.
        """

    @overload
    def SetPropertyEditor(self, id: Union[PGPropArgCls, str, _None], editorName: str) -> None:
        ...

    @overload
    def SetPropertyEditor(self, id: Union[PGPropArgCls, str, _None], editor: PGEditor) -> None:
        """
        SetPropertyEditor(id, editor) -> None
        SetPropertyEditor(id, editorName) -> None
        
        Sets editor for a property.
        """

    def SetPropertyLabel(self, id: Union[PGPropArgCls, str, _None], newproplabel: str) -> None:
        """
        SetPropertyLabel(id, newproplabel) -> None
        
        Sets label of a property.
        """

    def SetPropertyName(self, id: Union[PGPropArgCls, str, _None], newName: str) -> None:
        """
        SetPropertyName(id, newName) -> None
        
        Sets name of a property.
        """

    def SetPropertyReadOnly(self, id: Union[PGPropArgCls, str, _None], set: bool=True, flags: int=PG_RECURSE) -> None:
        """
        SetPropertyReadOnly(id, set=True, flags=PG_RECURSE) -> None
        
        Sets property (and, recursively, its children) to have read-only
        value.
        """

    def SetPropertyValueUnspecified(self, id: Union[PGPropArgCls, str, _None]) -> None:
        """
        SetPropertyValueUnspecified(id) -> None
        
        Sets property's value to unspecified.
        """

    def SetPropertyHelpString(self, id: Union[PGPropArgCls, str, _None], helpString: str) -> None:
        """
        SetPropertyHelpString(id, helpString) -> None
        
        Associates the help string with property.
        """

    def SetPropertyImage(self, id: Union[PGPropArgCls, str, _None], bmp: wx.BitmapBundle) -> None:
        """
        SetPropertyImage(id, bmp) -> None
        
        Set wxBitmap taken from wxBitmapBundle in front of the value.
        """

    def SetPropertyMaxLength(self, id: Union[PGPropArgCls, str, _None], maxLen: int) -> bool:
        """
        SetPropertyMaxLength(id, maxLen) -> bool
        
        Sets maximum length of text in property text editor.
        """

    def SetPropertyTextColour(self, id: Union[PGPropArgCls, str, _None], colour: wx.Colour, flags: int=PG_RECURSE) -> None:
        """
        SetPropertyTextColour(id, colour, flags=PG_RECURSE) -> None
        
        Sets text colour of given property.
        """

    def SetPropertyValidator(self, id: Union[PGPropArgCls, str, _None], validator: wx.Validator) -> None:
        """
        SetPropertyValidator(id, validator) -> None
        
        Sets validator of a property.
        """

    @overload
    def SetPropertyValue(self, id: Union[PGPropArgCls, str, _None], value: bool) -> None:
        ...

    @overload
    def SetPropertyValue(self, id: Union[PGPropArgCls, str, _None], value: str) -> None:
        ...

    @overload
    def SetPropertyValue(self, id: Union[PGPropArgCls, str, _None], value: List[str]) -> None:
        ...

    @overload
    def SetPropertyValue(self, id: Union[PGPropArgCls, str, _None], value: wx.DateTime) -> None:
        ...

    @overload
    def SetPropertyValue(self, id: Union[PGPropArgCls, str, _None], value: wx.Object) -> None:
        ...

    @overload
    def SetPropertyValue(self, id: Union[PGPropArgCls, str, _None], value: List[int]) -> None:
        ...

    @overload
    def SetPropertyValue(self, id: Union[PGPropArgCls, str, _None], value: PGVariant) -> None:
        ...

    @overload
    def SetPropertyValue(self, id: Union[PGPropArgCls, str, _None], value: int) -> None:
        ...

    @overload
    def SetPropertyValue(self, id: Union[PGPropArgCls, str, _None], value: float) -> None:
        """
        SetPropertyValue(id, value) -> None
        SetPropertyValue(id, value) -> None
        SetPropertyValue(id, value) -> None
        SetPropertyValue(id, value) -> None
        SetPropertyValue(id, value) -> None
        SetPropertyValue(id, value) -> None
        SetPropertyValue(id, value) -> None
        SetPropertyValue(id, value) -> None
        SetPropertyValue(id, value) -> None
        
        Sets value (floating point) of a property.
        """

    def SetPropertyValueString(self, id: Union[PGPropArgCls, str, _None], value: str) -> None:
        """
        SetPropertyValueString(id, value) -> None
        
        Sets value (wxString) of a property.
        """

    def SetPropVal(self, id: Union[PGPropArgCls, str, _None], value: PGVariant) -> None:
        """
        SetPropVal(id, value) -> None
        
        Sets value (wxVariant&) of a property.
        """

    def SetValidationFailureBehavior(self, vfbFlags: int) -> None:
        """
        SetValidationFailureBehavior(vfbFlags) -> None
        
        Adjusts how wxPropertyGrid behaves when invalid value is entered in a
        property.
        """

    def Sort(self, flags: int=0) -> None:
        """
        Sort(flags=0) -> None
        
        Sorts all properties recursively.
        """

    def SortChildren(self, id: Union[PGPropArgCls, str, _None], flags: int=0) -> None:
        """
        SortChildren(id, flags=0) -> None
        
        Sorts children of a property.
        """

    def GetPropertyByNameA(self, name: str) -> PGProperty:
        """
        GetPropertyByNameA(name) -> PGProperty
        
        GetPropertyByName() with assertion error message.
        """

    def RefreshProperty(self, p: PGProperty) -> None:
        """
        RefreshProperty(p) -> None
        """

    @staticmethod
    def InitAllTypeHandlers() -> None:
        """
        InitAllTypeHandlers() -> None
        
        Initializes all property types.
        """

    @staticmethod
    def RegisterAdditionalEditors() -> None:
        """
        RegisterAdditionalEditors() -> None
        
        Initializes additional property editors (SpinCtrl etc.).
        """

    @staticmethod
    def SetBoolChoices(trueChoice: str, falseChoice: str) -> None:
        """
        SetBoolChoices(trueChoice, falseChoice) -> None
        
        Sets strings listed in the choice dropdown of a wxBoolProperty.
        """

    @staticmethod
    def GetEditorByName(editorName: str) -> PGEditor:
        """
        GetEditorByName(editorName) -> PGEditor
        
        Returns editor pointer of editor with given name.
        """

    def MapType(self, class_, factory):
        """
        Registers Python type/class to property mapping.
        
        :param `factory`: Property builder function/class.
        """

    def DoDefaultTypeMappings(self):
        """
        Add built-in properties to the map.
        """

    def DoDefaultValueTypeMappings(self):
        """
        Map pg value type ids to getter methods.
        """

    def GetPropertyValues(self, dict_=None, as_strings=False, inc_attributes=False, flags=PG_ITERATE_PROPERTIES):
        """
        Returns all property values in the grid.
        
        :param `dict_`: A diftionary to fill with the property values.
            If not given, then a new one is created. The dict_ can be an
            object as well, in which case it's __dict__ is used.
        :param `as_strings`: if True, then string representations of values
            are fetched instead of native types. Useful for config and such.
        :param `inc_attributes`: if True, then property attributes are added
            in the form of ``"@<propname>@<attr>"``.
        :param `flags`: Flags to pass to the iterator. See :ref:`wx.propgrid.PG_ITERATOR_FLAGS`.
        :returns: A dictionary with values. It is always a dictionary,
            so if dict_ was an object with __dict__ attribute, then that
            attribute is returned.
        """

    def SetPropertyValues(self, dict_, autofill=False):
        """
        Sets property values from a dictionary.
        
        :param `dict_`: the source of the property values to set, which can be
            either a dictionary or an object with a __dict__ attribute.
        :param `autofill`: If true, keys with not relevant properties are
            auto-created. For more info, see :method:`AutoFill`.
        
        :note:
          * Keys starting with underscore are ignored.
          * Attributes can be set with entries named like "@<propname>@<attr>".
        """

    def _AutoFillMany(self,cat,dict_):
        """
        
        """

    def _AutoFillOne(self,cat,k,v):
        """
        
        """

    def AutoFill(self, obj, parent=None):
        """
        "Clears properties and re-fills to match members and values of
        the given object or dictionary obj.
        """

    def RegisterEditor(self, editor, editorName=None):
        """
        Register a new editor, either an instance or a class.
        """

    def GetPropertyClientData(self, p):
        """
        
        """

    def SetPropertyClientData(self, p, data):
        """
        
        """

    def GetPyIterator(self, flags=PG_ITERATE_DEFAULT, firstProperty=None):
        """
        Returns a pythonic property iterator for a single :ref:`PropertyGrid`
        or page in :ref:`PropertyGridManager`. Arguments are same as for
        :ref:`GetIterator`.
        
        The following example demonstrates iterating absolutely all items in
        a single grid::
        
            iterator = propGrid.GetPyIterator(wx.propgrid.PG_ITERATE_ALL)
            for prop in iterator:
                print(prop)
        
        :see: `wx.propgrid.PropertyGridInterface.Properties`
              `wx.propgrid.PropertyGridInterface.Items`
        """

    def GetPyVIterator(self, flags=PG_ITERATE_DEFAULT):
        """
        Similar to :ref:`GetVIterator` but returns a pythonic iterator.
        """

    def _Properties(self):
        """
        This attribute is a pythonic iterator over all properties in
        this `PropertyGrid` property container. It will only skip
        categories and private child properties. Usage is simple::
        
            for prop in propGrid.Properties:
                print(prop)
        
        :see: `wx.propgrid.PropertyGridInterface.Items`
              `wx.propgrid.PropertyGridInterface.GetPyIterator`
        """
    Properties = property(_Properties)

    def _Items(self):
        """
        This attribute is a pythonic iterator over all items in this
        `PropertyGrid` property container, excluding only private child
        properties. Usage is simple::
        
            for prop in propGrid.Items:
                print(prop)
        
        :see: `wx.propgrid.PropertyGridInterface.Properties`
              `wx.propgrid.PropertyGridInterface.GetPyVIterator`
        """
    Items = property(_Items)
# end of class PropertyGridInterface


_type2property = None
_vt2getter = None

PropertyGridInterface.GetValues = PropertyGridInterface.GetPropertyValues
PropertyGridInterface.SetValues = PropertyGridInterface.SetPropertyValues
#-- end-propgridiface --#
#-- begin-propgrid --#
PG_DEFAULT_STYLE: int
PGMAN_DEFAULT_STYLE: int

class _PG_WINDOW_STYLES(IntEnum):
    PG_AUTO_SORT = auto()
    PG_HIDE_CATEGORIES = auto()
    PG_ALPHABETIC_MODE = auto()
    PG_BOLD_MODIFIED = auto()
    PG_SPLITTER_AUTO_CENTER = auto()
    PG_TOOLTIPS = auto()
    PG_HIDE_MARGIN = auto()
    PG_STATIC_SPLITTER = auto()
    PG_STATIC_LAYOUT = auto()
    PG_LIMITED_EDITING = auto()
    PG_TOOLBAR = auto()
    PG_DESCRIPTION = auto()
    PG_NO_INTERNAL_BORDER = auto()
    PG_WINDOW_STYLE_MASK = auto()
PG_WINDOW_STYLES: TypeAlias = Union[_PG_WINDOW_STYLES, int]
PG_AUTO_SORT = _PG_WINDOW_STYLES.PG_AUTO_SORT
PG_HIDE_CATEGORIES = _PG_WINDOW_STYLES.PG_HIDE_CATEGORIES
PG_ALPHABETIC_MODE = _PG_WINDOW_STYLES.PG_ALPHABETIC_MODE
PG_BOLD_MODIFIED = _PG_WINDOW_STYLES.PG_BOLD_MODIFIED
PG_SPLITTER_AUTO_CENTER = _PG_WINDOW_STYLES.PG_SPLITTER_AUTO_CENTER
PG_TOOLTIPS = _PG_WINDOW_STYLES.PG_TOOLTIPS
PG_HIDE_MARGIN = _PG_WINDOW_STYLES.PG_HIDE_MARGIN
PG_STATIC_SPLITTER = _PG_WINDOW_STYLES.PG_STATIC_SPLITTER
PG_STATIC_LAYOUT = _PG_WINDOW_STYLES.PG_STATIC_LAYOUT
PG_LIMITED_EDITING = _PG_WINDOW_STYLES.PG_LIMITED_EDITING
PG_TOOLBAR = _PG_WINDOW_STYLES.PG_TOOLBAR
PG_DESCRIPTION = _PG_WINDOW_STYLES.PG_DESCRIPTION
PG_NO_INTERNAL_BORDER = _PG_WINDOW_STYLES.PG_NO_INTERNAL_BORDER
PG_WINDOW_STYLE_MASK = _PG_WINDOW_STYLES.PG_WINDOW_STYLE_MASK

class _PG_EX_WINDOW_STYLES(IntEnum):
    PG_EX_INIT_NOCAT = auto()
    PG_EX_NO_FLAT_TOOLBAR = auto()
    PG_EX_MODE_BUTTONS = auto()
    PG_EX_HELP_AS_TOOLTIPS = auto()
    PG_EX_NATIVE_DOUBLE_BUFFERING = auto()
    PG_EX_AUTO_UNSPECIFIED_VALUES = auto()
    PG_EX_WRITEONLY_BUILTIN_ATTRIBUTES = auto()
    PG_EX_HIDE_PAGE_BUTTONS = auto()
    PG_EX_MULTIPLE_SELECTION = auto()
    PG_EX_ENABLE_TLP_TRACKING = auto()
    PG_EX_NO_TOOLBAR_DIVIDER = auto()
    PG_EX_TOOLBAR_SEPARATOR = auto()
    PG_EX_ALWAYS_ALLOW_FOCUS = auto()
    PG_EX_WINDOW_PG_STYLE_MASK = auto()
    PG_EX_WINDOW_PGMAN_STYLE_MASK = auto()
    PG_EX_WINDOW_STYLE_MASK = auto()
PG_EX_WINDOW_STYLES: TypeAlias = Union[_PG_EX_WINDOW_STYLES, int]
PG_EX_INIT_NOCAT = _PG_EX_WINDOW_STYLES.PG_EX_INIT_NOCAT
PG_EX_NO_FLAT_TOOLBAR = _PG_EX_WINDOW_STYLES.PG_EX_NO_FLAT_TOOLBAR
PG_EX_MODE_BUTTONS = _PG_EX_WINDOW_STYLES.PG_EX_MODE_BUTTONS
PG_EX_HELP_AS_TOOLTIPS = _PG_EX_WINDOW_STYLES.PG_EX_HELP_AS_TOOLTIPS
PG_EX_NATIVE_DOUBLE_BUFFERING = _PG_EX_WINDOW_STYLES.PG_EX_NATIVE_DOUBLE_BUFFERING
PG_EX_AUTO_UNSPECIFIED_VALUES = _PG_EX_WINDOW_STYLES.PG_EX_AUTO_UNSPECIFIED_VALUES
PG_EX_WRITEONLY_BUILTIN_ATTRIBUTES = _PG_EX_WINDOW_STYLES.PG_EX_WRITEONLY_BUILTIN_ATTRIBUTES
PG_EX_HIDE_PAGE_BUTTONS = _PG_EX_WINDOW_STYLES.PG_EX_HIDE_PAGE_BUTTONS
PG_EX_MULTIPLE_SELECTION = _PG_EX_WINDOW_STYLES.PG_EX_MULTIPLE_SELECTION
PG_EX_ENABLE_TLP_TRACKING = _PG_EX_WINDOW_STYLES.PG_EX_ENABLE_TLP_TRACKING
PG_EX_NO_TOOLBAR_DIVIDER = _PG_EX_WINDOW_STYLES.PG_EX_NO_TOOLBAR_DIVIDER
PG_EX_TOOLBAR_SEPARATOR = _PG_EX_WINDOW_STYLES.PG_EX_TOOLBAR_SEPARATOR
PG_EX_ALWAYS_ALLOW_FOCUS = _PG_EX_WINDOW_STYLES.PG_EX_ALWAYS_ALLOW_FOCUS
PG_EX_WINDOW_PG_STYLE_MASK = _PG_EX_WINDOW_STYLES.PG_EX_WINDOW_PG_STYLE_MASK
PG_EX_WINDOW_PGMAN_STYLE_MASK = _PG_EX_WINDOW_STYLES.PG_EX_WINDOW_PGMAN_STYLE_MASK
PG_EX_WINDOW_STYLE_MASK = _PG_EX_WINDOW_STYLES.PG_EX_WINDOW_STYLE_MASK

class _PG_VALIDATION_FAILURE_BEHAVIOR_FLAGS(IntEnum):
    PG_VFB_STAY_IN_PROPERTY = auto()
    PG_VFB_BEEP = auto()
    PG_VFB_MARK_CELL = auto()
    PG_VFB_SHOW_MESSAGE = auto()
    PG_VFB_SHOW_MESSAGEBOX = auto()
    PG_VFB_SHOW_MESSAGE_ON_STATUSBAR = auto()
    PG_VFB_DEFAULT = auto()
PG_VALIDATION_FAILURE_BEHAVIOR_FLAGS: TypeAlias = Union[_PG_VALIDATION_FAILURE_BEHAVIOR_FLAGS, int]
PG_VFB_STAY_IN_PROPERTY = _PG_VALIDATION_FAILURE_BEHAVIOR_FLAGS.PG_VFB_STAY_IN_PROPERTY
PG_VFB_BEEP = _PG_VALIDATION_FAILURE_BEHAVIOR_FLAGS.PG_VFB_BEEP
PG_VFB_MARK_CELL = _PG_VALIDATION_FAILURE_BEHAVIOR_FLAGS.PG_VFB_MARK_CELL
PG_VFB_SHOW_MESSAGE = _PG_VALIDATION_FAILURE_BEHAVIOR_FLAGS.PG_VFB_SHOW_MESSAGE
PG_VFB_SHOW_MESSAGEBOX = _PG_VALIDATION_FAILURE_BEHAVIOR_FLAGS.PG_VFB_SHOW_MESSAGEBOX
PG_VFB_SHOW_MESSAGE_ON_STATUSBAR = _PG_VALIDATION_FAILURE_BEHAVIOR_FLAGS.PG_VFB_SHOW_MESSAGE_ON_STATUSBAR
PG_VFB_DEFAULT = _PG_VALIDATION_FAILURE_BEHAVIOR_FLAGS.PG_VFB_DEFAULT

class _PG_KEYBOARD_ACTIONS(IntEnum):
    PG_ACTION_INVALID = auto()
    PG_ACTION_NEXT_PROPERTY = auto()
    PG_ACTION_PREV_PROPERTY = auto()
    PG_ACTION_EXPAND_PROPERTY = auto()
    PG_ACTION_COLLAPSE_PROPERTY = auto()
    PG_ACTION_CANCEL_EDIT = auto()
    PG_ACTION_EDIT = auto()
    PG_ACTION_PRESS_BUTTON = auto()
    PG_ACTION_MAX = auto()
PG_KEYBOARD_ACTIONS: TypeAlias = Union[_PG_KEYBOARD_ACTIONS, int]
PG_ACTION_INVALID = _PG_KEYBOARD_ACTIONS.PG_ACTION_INVALID
PG_ACTION_NEXT_PROPERTY = _PG_KEYBOARD_ACTIONS.PG_ACTION_NEXT_PROPERTY
PG_ACTION_PREV_PROPERTY = _PG_KEYBOARD_ACTIONS.PG_ACTION_PREV_PROPERTY
PG_ACTION_EXPAND_PROPERTY = _PG_KEYBOARD_ACTIONS.PG_ACTION_EXPAND_PROPERTY
PG_ACTION_COLLAPSE_PROPERTY = _PG_KEYBOARD_ACTIONS.PG_ACTION_COLLAPSE_PROPERTY
PG_ACTION_CANCEL_EDIT = _PG_KEYBOARD_ACTIONS.PG_ACTION_CANCEL_EDIT
PG_ACTION_EDIT = _PG_KEYBOARD_ACTIONS.PG_ACTION_EDIT
PG_ACTION_PRESS_BUTTON = _PG_KEYBOARD_ACTIONS.PG_ACTION_PRESS_BUTTON
PG_ACTION_MAX = _PG_KEYBOARD_ACTIONS.PG_ACTION_MAX
wxEVT_PG_SELECTED: int
wxEVT_PG_CHANGING: int
wxEVT_PG_CHANGED: int
wxEVT_PG_HIGHLIGHTED: int
wxEVT_PG_RIGHT_CLICK: int
wxEVT_PG_PAGE_CHANGED: int
wxEVT_PG_ITEM_COLLAPSED: int
wxEVT_PG_ITEM_EXPANDED: int
wxEVT_PG_DOUBLE_CLICK: int
wxEVT_PG_LABEL_EDIT_BEGIN: int
wxEVT_PG_LABEL_EDIT_ENDING: int
wxEVT_PG_COL_BEGIN_DRAG: int
wxEVT_PG_COL_DRAGGING: int
wxEVT_PG_COL_END_DRAG: int

class PGValidationInfo:
    """
    Used to convey validation information to and from functions that
    actually perform validation.
    """

    def GetFailureBehavior(self) -> int:
        """
        GetFailureBehavior() -> int
        """

    def GetFailureMessage(self) -> str:
        """
        GetFailureMessage() -> str
        
        Returns current failure message.
        """

    def GetValue(self) -> PGVariant:
        """
        GetValue() -> PGVariant
        
        Returns reference to pending value.
        """

    def SetFailureBehavior(self, failureBehavior: int) -> None:
        """
        SetFailureBehavior(failureBehavior) -> None
        
        Set validation failure behaviour.
        """

    def SetFailureMessage(self, message: str) -> None:
        """
        SetFailureMessage(message) -> None
        
        Set current failure message.
        """
    @property
    def FailureBehavior(self) -> int: ...
    @FailureBehavior.setter
    def FailureBehavior(self, value: int, /) -> None: ...
    @property
    def FailureMessage(self) -> str: ...
    @FailureMessage.setter
    def FailureMessage(self, value: str, /) -> None: ...
    @property
    def Value(self) -> PGVariant: ...
# end of class PGValidationInfo

PropertyGridNameStr: str

class PropertyGrid(wx.Control, PropertyGridInterface):
    """
    PropertyGrid() -> None
    PropertyGrid(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=PG_DEFAULT_STYLE, name=PropertyGridNameStr) -> None
    
    wxPropertyGrid is a specialized grid for editing properties - in other
    words name = value pairs.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=PG_DEFAULT_STYLE, name: str=PropertyGridNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        PropertyGrid() -> None
        PropertyGrid(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=PG_DEFAULT_STYLE, name=PropertyGridNameStr) -> None
        
        wxPropertyGrid is a specialized grid for editing properties - in other
        words name = value pairs.
        """

    def DoShowPropertyError(self, property: PGProperty, msg: str) -> None:
        """
        DoShowPropertyError(property, msg) -> None
        
        Override in derived class to display error messages in custom manner
        (these message usually only result from validation failure).
        """

    def DoHidePropertyError(self, property: PGProperty) -> None:
        """
        DoHidePropertyError(property) -> None
        
        Override in derived class to hide an error displayed by
        DoShowPropertyError().
        """

    def GetStatusBar(self) -> wx.StatusBar:
        """
        GetStatusBar() -> wx.StatusBar
        
        Return wxStatusBar that is used by this wxPropertyGrid.
        """

    def DoOnValidationFailure(self, property: PGProperty, invalidValue: PGVariant) -> bool:
        """
        DoOnValidationFailure(property, invalidValue) -> bool
        
        Override to customize property validation failure behaviour.
        """

    def DoOnValidationFailureReset(self, property: PGProperty) -> None:
        """
        DoOnValidationFailureReset(property) -> None
        
        Override to customize resetting of property validation failure status.
        """

    def EditorsValueWasModified(self) -> None:
        """
        EditorsValueWasModified() -> None
        
        Call when editor widget's contents is modified.
        """

    def EditorsValueWasNotModified(self) -> None:
        """
        EditorsValueWasNotModified() -> None
        
        Reverse of EditorsValueWasModified().
        """

    def GetUncommittedPropertyValue(self) -> PGVariant:
        """
        GetUncommittedPropertyValue() -> PGVariant
        
        Returns most up-to-date value of selected property.
        """

    def IsEditorsValueModified(self) -> bool:
        """
        IsEditorsValueModified() -> bool
        
        Returns true if editor's value was marked modified.
        """

    def ShowPropertyError(self, id: PGPropArgCls, msg: str) -> None:
        """
        ShowPropertyError(id, msg) -> None
        
        Shows a brief error message that is related to a property.
        """

    def ValueChangeInEvent(self, variant: PGVariant) -> None:
        """
        ValueChangeInEvent(variant) -> None
        
        Call this from wxPGProperty::OnEvent() to cause property value to be
        changed after the function returns (with true as return value).
        """

    def WasValueChangedInEvent(self) -> bool:
        """
        WasValueChangedInEvent() -> bool
        
        You can use this member function, for instance, to detect in
        wxPGProperty::OnEvent() if wxPGProperty::SetValueInEvent() was already
        called in wxPGEditor::OnEvent().
        """

    def AddActionTrigger(self, action: int, keycode: int, modifiers: int=0) -> None:
        """
        AddActionTrigger(action, keycode, modifiers=0) -> None
        
        Adds given key combination to trigger given action.
        """

    def AddToSelection(self, id: PGPropArgCls) -> bool:
        """
        AddToSelection(id) -> bool
        
        Adds given property into selection.
        """

    def BeginLabelEdit(self, colIndex: int=0) -> None:
        """
        BeginLabelEdit(colIndex=0) -> None
        
        Creates label editor wxTextCtrl for given column, for property that is
        currently selected.
        """

    def ChangePropertyValue(self, id: PGPropArgCls, newValue: PGVariant) -> bool:
        """
        ChangePropertyValue(id, newValue) -> bool
        
        Changes value of a property, as if from an editor.
        """

    def CenterSplitter(self, enableAutoResizing: bool=False) -> None:
        """
        CenterSplitter(enableAutoResizing=False) -> None
        
        Centers the splitter.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Deletes all properties.
        """

    def ClearActionTriggers(self, action: int) -> None:
        """
        ClearActionTriggers(action) -> None
        
        Clears action triggers for given action.
        """

    def CommitChangesFromEditor(self, flags: Uint32=0) -> bool:
        """
        CommitChangesFromEditor(flags=0) -> bool
        
        Forces updating the value of property from the editor control.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=PG_DEFAULT_STYLE, name: str=PropertyGridNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=PG_DEFAULT_STYLE, name=PropertyGridNameStr) -> bool
        
        Two step creation.
        """

    def DedicateKey(self, keycode: int) -> None:
        """
        DedicateKey(keycode) -> None
        
        Dedicates a specific keycode to wxPropertyGrid.
        """

    def EnableCategories(self, enable: bool) -> bool:
        """
        EnableCategories(enable) -> bool
        
        Enables or disables (shows/hides) categories according to parameter
        enable.
        """

    def EndLabelEdit(self, commit: bool=True) -> None:
        """
        EndLabelEdit(commit=True) -> None
        
        Destroys label editor wxTextCtrl, if any.
        """

    def EnsureVisible(self, id: PGPropArgCls) -> bool:
        """
        EnsureVisible(id) -> bool
        
        Scrolls and/or expands items to ensure that the given item is visible.
        """

    def FitColumns(self) -> wx.Size:
        """
        FitColumns() -> wx.Size
        
        Reduces column sizes to minimum possible, while still retaining fully
        visible grid contents (labels, images).
        """

    def GetLabelEditor(self) -> wx.TextCtrl:
        """
        GetLabelEditor() -> wx.TextCtrl
        
        Returns currently active label editor, NULL if none.
        """

    def GetPanel(self) -> wx.Window:
        """
        GetPanel() -> wx.Window
        
        Returns wxWindow that the properties are painted on, and which should
        be used as the parent for editor controls.
        """

    def GetCaptionBackgroundColour(self) -> wx.Colour:
        """
        GetCaptionBackgroundColour() -> wx.Colour
        
        Returns current category caption background colour.
        """

    def GetCaptionFont(self) -> wx.Font:
        """
        GetCaptionFont() -> wx.Font
        
        Returns current category caption font.
        """

    def GetCaptionForegroundColour(self) -> wx.Colour:
        """
        GetCaptionForegroundColour() -> wx.Colour
        
        Returns current category caption text colour.
        """

    def GetCellBackgroundColour(self) -> wx.Colour:
        """
        GetCellBackgroundColour() -> wx.Colour
        
        Returns current cell background colour.
        """

    def GetCellDisabledTextColour(self) -> wx.Colour:
        """
        GetCellDisabledTextColour() -> wx.Colour
        
        Returns current cell text colour when disabled.
        """

    def GetCellTextColour(self) -> wx.Colour:
        """
        GetCellTextColour() -> wx.Colour
        
        Returns current cell text colour.
        """

    def GetColumnCount(self) -> int:
        """
        GetColumnCount() -> int
        
        Returns number of columns currently on grid.
        """

    def GetEmptySpaceColour(self) -> wx.Colour:
        """
        GetEmptySpaceColour() -> wx.Colour
        
        Returns colour of empty space below properties.
        """

    def GetFontHeight(self) -> int:
        """
        GetFontHeight() -> int
        
        Returns height of highest characters of used font.
        """

    def GetGrid(self) -> PropertyGrid:
        """
        GetGrid() -> PropertyGrid
        
        Returns pointer to itself.
        """

    def GetImageRect(self, property: PGProperty, item: int) -> wx.Rect:
        """
        GetImageRect(property, item) -> wx.Rect
        
        Returns rectangle of custom paint image.
        """

    def GetImageSize(self, property: Optional[PGProperty]=None, item: int=-1) -> wx.Size:
        """
        GetImageSize(property=None, item=-1) -> wx.Size
        
        Returns size of the custom paint image in front of property.
        """

    def GetLastItem(self, flags: int=PG_ITERATE_DEFAULT) -> PGProperty:
        """
        GetLastItem(flags=PG_ITERATE_DEFAULT) -> PGProperty
        
        Returns last item which could be iterated using given flags.
        """

    def GetLineColour(self) -> wx.Colour:
        """
        GetLineColour() -> wx.Colour
        
        Returns colour of lines between cells.
        """

    def GetMarginColour(self) -> wx.Colour:
        """
        GetMarginColour() -> wx.Colour
        
        Returns background colour of margin.
        """

    def GetMarginWidth(self) -> int:
        """
        GetMarginWidth() -> int
        
        Returns margin width.
        """

    def GetRoot(self) -> PGProperty:
        """
        GetRoot() -> PGProperty
        
        Returns "root property".
        """

    def GetRowHeight(self) -> int:
        """
        GetRowHeight() -> int
        
        Returns height of a single grid row (in pixels).
        """

    def GetSelectedProperty(self) -> PGProperty:
        """
        GetSelectedProperty() -> PGProperty
        
        Returns currently selected property.
        """

    def GetSelection(self) -> PGProperty:
        """
        GetSelection() -> PGProperty
        
        Returns currently selected property.
        """

    def GetSelectionBackgroundColour(self) -> wx.Colour:
        """
        GetSelectionBackgroundColour() -> wx.Colour
        
        Returns current selection background colour.
        """

    def GetSelectionForegroundColour(self) -> wx.Colour:
        """
        GetSelectionForegroundColour() -> wx.Colour
        
        Returns current selection text colour.
        """

    def GetSplitterPosition(self, splitterIndex: int=0) -> int:
        """
        GetSplitterPosition(splitterIndex=0) -> int
        
        Returns current splitter x position.
        """

    def GetEditorTextCtrl(self) -> wx.TextCtrl:
        """
        GetEditorTextCtrl() -> wx.TextCtrl
        
        Returns wxTextCtrl active in currently selected property, if any.
        """

    def GetUnspecifiedValueAppearance(self) -> PGCell:
        """
        GetUnspecifiedValueAppearance() -> PGCell
        
        Returns current appearance of unspecified value cells.
        """

    def GetUnspecifiedValueText(self, argFlags: int=0) -> str:
        """
        GetUnspecifiedValueText(argFlags=0) -> str
        
        Returns (visual) text representation of the unspecified property
        value.
        """

    def GetVerticalSpacing(self) -> int:
        """
        GetVerticalSpacing() -> int
        
        Returns current vertical spacing.
        """

    def HitTest(self, pt: wx.Point) -> PropertyGridHitTestResult:
        """
        HitTest(pt) -> PropertyGridHitTestResult
        
        Returns information about arbitrary position in the grid.
        """

    def IsAnyModified(self) -> bool:
        """
        IsAnyModified() -> bool
        
        Returns true if any property has been modified by the user.
        """

    def IsEditorFocused(self) -> bool:
        """
        IsEditorFocused() -> bool
        
        Returns true if a property editor control has focus.
        """

    def IsFrozen(self) -> bool:
        """
        IsFrozen() -> bool
        
        Returns true if updating is frozen (i.e.
        """

    def MakeColumnEditable(self, column: int, editable: bool=True) -> None:
        """
        MakeColumnEditable(column, editable=True) -> None
        
        Makes given column editable by user.
        """

    def OnTLPChanging(self, newTLP: wx.Window) -> None:
        """
        OnTLPChanging(newTLP) -> None
        
        It is recommended that you call this function any time your code
        causes wxPropertyGrid's top-level parent to change.
        """

    def RefreshEditor(self) -> None:
        """
        RefreshEditor() -> None
        
        Refreshes any active editor control.
        """

    def RefreshProperty(self, p: PGProperty) -> None:
        """
        RefreshProperty(p) -> None
        
        Redraws given property.
        """

    def ResetColours(self) -> None:
        """
        ResetColours() -> None
        
        Resets all colours to the original system values.
        """

    def ResetColumnSizes(self, enableAutoResizing: bool=False) -> None:
        """
        ResetColumnSizes(enableAutoResizing=False) -> None
        
        Resets column sizes and splitter positions, based on proportions.
        """

    def RemoveFromSelection(self, id: PGPropArgCls) -> bool:
        """
        RemoveFromSelection(id) -> bool
        
        Removes given property from selection.
        """

    def SelectProperty(self, id: PGPropArgCls, focus: bool=False) -> bool:
        """
        SelectProperty(id, focus=False) -> bool
        
        Selects a property.
        """

    def SetCaptionBackgroundColour(self, col: wx.Colour) -> None:
        """
        SetCaptionBackgroundColour(col) -> None
        
        Sets category caption background colour.
        """

    def SetCaptionTextColour(self, col: wx.Colour) -> None:
        """
        SetCaptionTextColour(col) -> None
        
        Sets category caption text colour.
        """

    def SetCellBackgroundColour(self, col: wx.Colour) -> None:
        """
        SetCellBackgroundColour(col) -> None
        
        Sets default cell background colour - applies to property cells.
        """

    def SetCellDisabledTextColour(self, col: wx.Colour) -> None:
        """
        SetCellDisabledTextColour(col) -> None
        
        Sets cell text colour for disabled properties.
        """

    def SetCellTextColour(self, col: wx.Colour) -> None:
        """
        SetCellTextColour(col) -> None
        
        Sets default cell text colour - applies to property name and value
        text.
        """

    def SetColumnCount(self, colCount: int) -> None:
        """
        SetColumnCount(colCount) -> None
        
        Set number of columns (2 or more).
        """

    def SetCurrentCategory(self, id: PGPropArgCls) -> None:
        """
        SetCurrentCategory(id) -> None
        
        Sets the 'current' category - Append will add non-category properties
        under it.
        """

    def SetEmptySpaceColour(self, col: wx.Colour) -> None:
        """
        SetEmptySpaceColour(col) -> None
        
        Sets colour of empty space below properties.
        """

    def SetLineColour(self, col: wx.Colour) -> None:
        """
        SetLineColour(col) -> None
        
        Sets colour of lines between cells.
        """

    def SetMarginColour(self, col: wx.Colour) -> None:
        """
        SetMarginColour(col) -> None
        
        Sets background colour of margin.
        """

    def SetSelection(self, newSelection: List[PGProperty]) -> None:
        """
        SetSelection(newSelection) -> None
        
        Set entire new selection from given list of properties.
        """

    def SetSelectionBackgroundColour(self, col: wx.Colour) -> None:
        """
        SetSelectionBackgroundColour(col) -> None
        
        Sets selection background colour - applies to selected property name
        background.
        """

    def SetSelectionTextColour(self, col: wx.Colour) -> None:
        """
        SetSelectionTextColour(col) -> None
        
        Sets selection foreground colour - applies to selected property name
        text.
        """

    def SetSplitterPosition(self, newxpos: int, col: int=0) -> None:
        """
        SetSplitterPosition(newxpos, col=0) -> None
        
        Sets x coordinate of the splitter.
        """

    def SetSplitterLeft(self, privateChildrenToo: bool=False) -> None:
        """
        SetSplitterLeft(privateChildrenToo=False) -> None
        
        Moves splitter as left as possible, while still allowing all labels to
        be shown in full.
        """

    def SetUnspecifiedValueAppearance(self, cell: PGCell) -> None:
        """
        SetUnspecifiedValueAppearance(cell) -> None
        
        Sets appearance of value cells representing an unspecified property
        value.
        """

    def SetVerticalSpacing(self, vspacing: int) -> None:
        """
        SetVerticalSpacing(vspacing) -> None
        
        Sets vertical spacing.
        """

    def SetVirtualWidth(self, width: int) -> None:
        """
        SetVirtualWidth(width) -> None
        
        Set virtual width for this particular page.
        """

    def SetupTextCtrlValue(self, text: str) -> None:
        """
        SetupTextCtrlValue(text) -> None
        
        Must be called in wxPGEditor::CreateControls() if primary editor
        window is wxTextCtrl, just before textctrl is created.
        """

    def UnfocusEditor(self) -> bool:
        """
        UnfocusEditor() -> bool
        
        Unfocuses or closes editor if one was open, but does not deselect
        property.
        """

    def DrawItemAndValueRelated(self, p: PGProperty) -> None:
        """
        DrawItemAndValueRelated(p) -> None
        
        Draws item, children, and consecutive parents as long as category is
        not met.
        """

    @staticmethod
    def AutoGetTranslation(enable: bool) -> None:
        """
        AutoGetTranslation(enable) -> None
        
        This static function enables or disables automatic use of
        wxGetTranslation() for following strings: wxEnumProperty list labels,
        wxFlagsProperty child property labels.
        """

    @staticmethod
    def RegisterEditorClass(editor: PGEditor, noDefCheck: bool=False) -> PGEditor:
        """
        RegisterEditorClass(editor, noDefCheck=False) -> PGEditor
        
        Forwards to DoRegisterEditorClass with empty name.
        """

    @staticmethod
    def DoRegisterEditorClass(editor: PGEditor, name: str, noDefCheck: bool=False) -> PGEditor:
        """
        DoRegisterEditorClass(editor, name, noDefCheck=False) -> PGEditor
        
        Registers a new editor class.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    _PropertyGrid__init__orig = __init__
    def _PropertyGrid__init__(self, *args, **kw):
        _PropertyGrid__init__orig(self, *args, **kw)
        self.DoDefaultTypeMappings()
        self.edited_objects = {}
        self.DoDefaultValueTypeMappings()
        if not hasattr(self.__class__, '_vt2setter'):
            self.__class__._vt2setter = {}
    __init__ = _PropertyGrid__init__

    @overload
    def CalcScrolledPosition(self, pt: Point) -> Point:
        ...

    @overload
    def CalcScrolledPosition(self, x: int, y: int) -> Tuple[int, int]:
        """
        CalcScrolledPosition(x, y) -> Tuple[int, int]
        CalcScrolledPosition(pt) -> Point
        
        Translates the logical coordinates to the device ones.
        """

    @overload
    def CalcUnscrolledPosition(self, pt: Point) -> Point:
        ...

    @overload
    def CalcUnscrolledPosition(self, x: int, y: int) -> Tuple[int, int]:
        """
        CalcUnscrolledPosition(x, y) -> Tuple[int, int]
        CalcUnscrolledPosition(pt) -> Point
        
        Translates the device coordinates to the logical ones.
        """

    def DisableKeyboardScrolling(self) -> None:
        """
        DisableKeyboardScrolling() -> None
        
        Disable use of keyboard keys for scrolling.
        """

    def DoPrepareDC(self, dc: DC) -> None:
        """
        DoPrepareDC(dc) -> None
        
        Call this function to prepare the device context for drawing a
        scrolled image.
        """

    def EnableScrolling(self, xScrolling: bool, yScrolling: bool) -> None:
        """
        EnableScrolling(xScrolling, yScrolling) -> None
        
        Enable or disable use of wxWindow::ScrollWindow() for scrolling.
        """

    def ShowScrollbars(self, horz: ScrollbarVisibility, vert: ScrollbarVisibility) -> None:
        """
        ShowScrollbars(horz, vert) -> None
        
        Set the scrollbar visibility.
        """

    def GetScrollPixelsPerUnit(self) -> Tuple[int, int]:
        """
        GetScrollPixelsPerUnit() -> Tuple[int, int]
        
        Get the number of pixels per scroll unit (line), in each direction, as
        set by SetScrollbars().
        """

    def GetViewStart(self) -> Tuple[int, int]:
        """
        GetViewStart() -> Tuple[int, int]
        
        Get the position at which the visible portion of the window starts.
        """

    def IsRetained(self) -> bool:
        """
        IsRetained() -> bool
        
        Motif only: true if the window has a backing bitmap.
        """

    def OnDraw(self, dc: DC) -> None:
        """
        OnDraw(dc) -> None
        
        Called by the default paint event handler to allow the application to
        define painting behaviour without having to worry about calling
        DoPrepareDC().
        """

    def PrepareDC(self, dc: DC) -> None:
        """
        PrepareDC(dc) -> None
        
        This function is for backwards compatibility only and simply calls
        DoPrepareDC() now.
        """

    @overload
    def Scroll(self, pt: Point) -> None:
        ...

    @overload
    def Scroll(self, x: int, y: int) -> None:
        """
        Scroll(x, y) -> None
        Scroll(pt) -> None
        
        Scrolls a window so the view start is at the given point.
        """

    def SetScrollRate(self, xstep: int, ystep: int) -> None:
        """
        SetScrollRate(xstep, ystep) -> None
        
        Set the horizontal and vertical scrolling increment only.
        """

    def SetScrollbars(self, pixelsPerUnitX: int, pixelsPerUnitY: int, noUnitsX: int, noUnitsY: int, xPos: int=0, yPos: int=0, noRefresh: bool=False) -> None:
        """
        SetScrollbars(pixelsPerUnitX, pixelsPerUnitY, noUnitsX, noUnitsY, xPos=0, yPos=0, noRefresh=False) -> None
        
        Sets up vertical and/or horizontal scrollbars.
        """

    def SetTargetWindow(self, window: Window) -> None:
        """
        SetTargetWindow(window) -> None
        
        Call this function to tell wxScrolled to perform the actual scrolling
        on a different window (and not on itself).
        """

    def GetTargetWindow(self) -> Window:
        """
        GetTargetWindow() -> Window
        """

    def SetTargetRect(self, rect: Rect) -> None:
        """
        SetTargetRect(rect) -> None
        """

    def GetTargetRect(self) -> Rect:
        """
        GetTargetRect() -> Rect
        """

    def GetScrollPageSize(self, orient: int) -> int:
        """
        GetScrollPageSize(orient) -> int
        """

    def SetScrollPageSize(self, orient: int, pageSize: int) -> None:
        """
        SetScrollPageSize(orient, pageSize) -> None
        """

    def GetScrollLines(self, orient: int) -> int:
        """
        GetScrollLines(orient) -> int
        """

    def SetScale(self, xs: float, ys: float) -> None:
        """
        SetScale(xs, ys) -> None
        """

    def GetScaleX(self) -> float:
        """
        GetScaleX() -> float
        """

    def GetScaleY(self) -> float:
        """
        GetScaleY() -> float
        """

    def AdjustScrollbars(self) -> None:
        """
        AdjustScrollbars() -> None
        """

    def IsAutoScrolling(self) -> bool:
        """
        IsAutoScrolling() -> bool
        
        Are we generating the autoscroll events?
        """

    def StopAutoScrolling(self) -> None:
        """
        StopAutoScrolling() -> None
        
        Stop generating the scroll events when mouse is held outside the
        window.
        """

    def SendAutoScrollEvents(self, event: ScrollWinEvent) -> bool:
        """
        SendAutoScrollEvents(event) -> bool
        
        This method can be overridden in a derived class to forbid sending the
        auto scroll events - note that unlike StopAutoScrolling() it doesn't
        stop the timer, so it will be called repeatedly and will typically
        return different values depending on the current mouse position.
        """
    @property
    def CaptionBackgroundColour(self) -> wx.Colour: ...
    @CaptionBackgroundColour.setter
    def CaptionBackgroundColour(self, value: wx.Colour, /) -> None: ...
    @property
    def CaptionFont(self) -> wx.Font: ...
    @property
    def CaptionForegroundColour(self) -> wx.Colour: ...
    @property
    def CellBackgroundColour(self) -> wx.Colour: ...
    @CellBackgroundColour.setter
    def CellBackgroundColour(self, value: wx.Colour, /) -> None: ...
    @property
    def CellDisabledTextColour(self) -> wx.Colour: ...
    @CellDisabledTextColour.setter
    def CellDisabledTextColour(self, value: wx.Colour, /) -> None: ...
    @property
    def CellTextColour(self) -> wx.Colour: ...
    @CellTextColour.setter
    def CellTextColour(self, value: wx.Colour, /) -> None: ...
    @property
    def ColumnCount(self) -> int: ...
    @ColumnCount.setter
    def ColumnCount(self, value: int, /) -> None: ...
    @property
    def EditorTextCtrl(self) -> wx.TextCtrl: ...
    @property
    def EmptySpaceColour(self) -> wx.Colour: ...
    @EmptySpaceColour.setter
    def EmptySpaceColour(self, value: wx.Colour, /) -> None: ...
    @property
    def FontHeight(self) -> int: ...
    @property
    def Grid(self) -> PropertyGrid: ...
    @property
    def ImageSize(self) -> wx.Size: ...
    @property
    def LabelEditor(self) -> wx.TextCtrl: ...
    @property
    def LastItem(self) -> PGProperty: ...
    @property
    def LineColour(self) -> wx.Colour: ...
    @LineColour.setter
    def LineColour(self, value: wx.Colour, /) -> None: ...
    @property
    def MarginColour(self) -> wx.Colour: ...
    @MarginColour.setter
    def MarginColour(self, value: wx.Colour, /) -> None: ...
    @property
    def MarginWidth(self) -> int: ...
    @property
    def Panel(self) -> wx.Window: ...
    @property
    def Root(self) -> PGProperty: ...
    @property
    def RowHeight(self) -> int: ...
    @property
    def ScaleX(self) -> float: ...
    @property
    def ScaleY(self) -> float: ...
    @property
    def SelectedProperty(self) -> PGProperty: ...
    @property
    def Selection(self) -> List[PGProperty]: ...
    @Selection.setter
    def Selection(self, value: List[PGProperty], /) -> None: ...
    @property
    def SelectionBackgroundColour(self) -> wx.Colour: ...
    @SelectionBackgroundColour.setter
    def SelectionBackgroundColour(self, value: wx.Colour, /) -> None: ...
    @property
    def SelectionForegroundColour(self) -> wx.Colour: ...
    @property
    def SplitterPosition(self) -> int: ...
    @SplitterPosition.setter
    def SplitterPosition(self, value: int, /) -> None: ...
    @property
    def StatusBar(self) -> wx.StatusBar: ...
    @property
    def TargetRect(self) -> Rect: ...
    @TargetRect.setter
    def TargetRect(self, value: Rect, /) -> None: ...
    @property
    def TargetWindow(self) -> Window: ...
    @TargetWindow.setter
    def TargetWindow(self, value: Window, /) -> None: ...
    @property
    def UncommittedPropertyValue(self) -> PGVariant: ...
    @property
    def UnspecifiedValueAppearance(self) -> PGCell: ...
    @UnspecifiedValueAppearance.setter
    def UnspecifiedValueAppearance(self, value: PGCell, /) -> None: ...
    @property
    def UnspecifiedValueText(self) -> str: ...
    @property
    def VerticalSpacing(self) -> int: ...
    @VerticalSpacing.setter
    def VerticalSpacing(self, value: int, /) -> None: ...

    def ShouldScrollToChildOnFocus(self, child: Window) -> bool:
        """
        ShouldScrollToChildOnFocus(child) -> bool
        
        This method can be overridden in a derived class to prevent scrolling
        the child window into view automatically when it gets focus.
        """

    def GetSizeAvailableForScrollTarget(self, size: Size) -> Size:
        """
        GetSizeAvailableForScrollTarget(size) -> Size
        
        Function which must be overridden to implement the size available for
        the scroll target for the given size of the main window.
        """
# end of class PropertyGrid


class PropertyGridEvent(wx.CommandEvent):
    """
    PropertyGridEvent(commandType=0, id=0) -> None
    PropertyGridEvent(event) -> None
    
    A property grid event holds information about events associated with
    wxPropertyGrid objects.
    """

    @overload
    def __init__(self, event: PropertyGridEvent) -> None:
        ...

    @overload
    def __init__(self, commandType: EventType=0, id: int=0) -> None:
        """
        PropertyGridEvent(commandType=0, id=0) -> None
        PropertyGridEvent(event) -> None
        
        A property grid event holds information about events associated with
        wxPropertyGrid objects.
        """

    def CanVeto(self) -> bool:
        """
        CanVeto() -> bool
        
        Returns true if you can veto the action that the event is signaling.
        """

    def GetColumn(self) -> int:
        """
        GetColumn() -> int
        
        Returns the column index associated with this event.
        """

    def GetMainParent(self) -> PGProperty:
        """
        GetMainParent() -> PGProperty
        
        Returns highest level non-category, non-root parent of property for
        which event occurred.
        """

    def GetProperty(self) -> PGProperty:
        """
        GetProperty() -> PGProperty
        
        Returns property associated with this event.
        """

    def GetValidationFailureBehavior(self) -> int:
        """
        GetValidationFailureBehavior() -> int
        
        Returns current validation failure flags.
        """

    def GetPropertyName(self) -> str:
        """
        GetPropertyName() -> str
        
        Returns name of the associated property.
        """

    def GetPropertyValue(self) -> PGVariant:
        """
        GetPropertyValue() -> PGVariant
        
        Returns value of the associated property.
        """

    def GetValue(self) -> PGVariant:
        """
        GetValue() -> PGVariant
        
        Returns value of the associated property.
        """

    def SetCanVeto(self, canVeto: bool) -> None:
        """
        SetCanVeto(canVeto) -> None
        
        Set if event can be vetoed.
        """

    def SetProperty(self, p: PGProperty) -> None:
        """
        SetProperty(p) -> None
        
        Changes the property associated with this event.
        """

    def SetValidationFailureBehavior(self, flags: int) -> None:
        """
        SetValidationFailureBehavior(flags) -> None
        
        Set override validation failure behaviour.
        """

    def SetValidationFailureMessage(self, message: str) -> None:
        """
        SetValidationFailureMessage(message) -> None
        
        Sets custom failure message for this time only.
        """

    def Veto(self, veto: bool=True) -> None:
        """
        Veto(veto=True) -> None
        
        Call this from your event handler to veto action that the event is
        signaling.
        """

    def WasVetoed(self) -> bool:
        """
        WasVetoed() -> bool
        
        Returns true if event was vetoed.
        """
    @property
    def Column(self) -> int: ...
    @property
    def MainParent(self) -> PGProperty: ...
    @property
    def Property(self) -> PGProperty: ...
    @Property.setter
    def Property(self, value: PGProperty, /) -> None: ...
    @property
    def PropertyName(self) -> str: ...
    @property
    def PropertyValue(self) -> PGVariant: ...
    @property
    def ValidationFailureBehavior(self) -> int: ...
    @ValidationFailureBehavior.setter
    def ValidationFailureBehavior(self, value: int, /) -> None: ...
    @property
    def Value(self) -> PGVariant: ...
# end of class PropertyGridEvent


class PropertyGridPopulator:
    """
    PropertyGridPopulator() -> None
    
    Allows populating wxPropertyGrid from arbitrary text source.
    """

    def __init__(self) -> None:
        """
        PropertyGridPopulator() -> None
        
        Allows populating wxPropertyGrid from arbitrary text source.
        """

    def SetState(self, state: PropertyGridPageState) -> None:
        """
        SetState(state) -> None
        """

    def SetGrid(self, pg: PropertyGrid) -> None:
        """
        SetGrid(pg) -> None
        """

    def Add(self, propClass: str, propLabel: str, propName: str, propValue: str, pChoices: Optional[PGChoices]=None) -> PGProperty:
        """
        Add(propClass, propLabel, propName, propValue, pChoices=None) -> PGProperty
        
        Appends a new property under bottommost parent.
        """

    def AddChildren(self, property: PGProperty) -> None:
        """
        AddChildren(property) -> None
        
        Pushes property to the back of parent array (ie it becomes bottommost
        parent), and starts scanning/adding children for it.
        """

    def AddAttribute(self, name: str, type: str, value: str) -> bool:
        """
        AddAttribute(name, type, value) -> bool
        
        Adds attribute to the bottommost property.
        """

    def DoScanForChildren(self) -> None:
        """
        DoScanForChildren() -> None
        
        Called once in AddChildren.
        """

    def GetCurParent(self) -> PGProperty:
        """
        GetCurParent() -> PGProperty
        
        Returns id of parent property for which children can currently be
        added.
        """

    def GetState(self) -> PropertyGridPageState:
        """
        GetState() -> PropertyGridPageState
        """

    def ParseChoices(self, choicesString: str, idString: str) -> PGChoices:
        """
        ParseChoices(choicesString, idString) -> PGChoices
        
        Parses strings of format "choice1"[=value1] ...
        """

    def ProcessError(self, msg: str) -> None:
        """
        ProcessError(msg) -> None
        
        Implement in derived class to do custom process when an error occurs.
        """

    @staticmethod
    def ToLongPCT(s: str, pval: int, max: int) -> bool:
        """
        ToLongPCT(s, pval, max) -> bool
        
        Like wxString::ToLong, except allows N% in addition of N.
        """
    @property
    def CurParent(self) -> PGProperty: ...
    @property
    def State(self) -> PropertyGridPageState: ...
    @State.setter
    def State(self, value: PropertyGridPageState, /) -> None: ...
# end of class PropertyGridPopulator


EVT_PG_CHANGED = wx.PyEventBinder( wxEVT_PG_CHANGED, 1 )
EVT_PG_CHANGING = wx.PyEventBinder( wxEVT_PG_CHANGING, 1 )
EVT_PG_SELECTED = wx.PyEventBinder( wxEVT_PG_SELECTED, 1 )
EVT_PG_HIGHLIGHTED = wx.PyEventBinder( wxEVT_PG_HIGHLIGHTED, 1 )
EVT_PG_RIGHT_CLICK = wx.PyEventBinder( wxEVT_PG_RIGHT_CLICK, 1 )
EVT_PG_PAGE_CHANGED = wx.PyEventBinder( wxEVT_PG_PAGE_CHANGED, 1 )
EVT_PG_ITEM_COLLAPSED = wx.PyEventBinder( wxEVT_PG_ITEM_COLLAPSED, 1 )
EVT_PG_ITEM_EXPANDED = wx.PyEventBinder( wxEVT_PG_ITEM_EXPANDED, 1 )
EVT_PG_DOUBLE_CLICK = wx.PyEventBinder( wxEVT_PG_DOUBLE_CLICK, 1 )
EVT_PG_LABEL_EDIT_BEGIN = wx.PyEventBinder( wxEVT_PG_LABEL_EDIT_BEGIN, 1 )
EVT_PG_LABEL_EDIT_ENDING = wx.PyEventBinder( wxEVT_PG_LABEL_EDIT_ENDING, 1 )
EVT_PG_COL_BEGIN_DRAG = wx.PyEventBinder( wxEVT_PG_COL_BEGIN_DRAG, 1 )
EVT_PG_COL_DRAGGING = wx.PyEventBinder( wxEVT_PG_COL_DRAGGING, 1 )
EVT_PG_COL_END_DRAG = wx.PyEventBinder( wxEVT_PG_COL_END_DRAG, 1 )
#-- end-propgrid --#
#-- begin-propgridprops --#
PG_PROP_PASSWORD: int
PG_PROP_STATIC_CHOICES: int
PG_PROP_SHOW_FULL_FILENAME: int
PG_PROP_ACTIVE_BTN: int
PG_PROP_USE_CHECKBOX: int
PG_PROP_USE_DCC: int
AEDIALOG_STYLE: int

class _PGNumericValidationConstants(IntEnum):
    PG_PROPERTY_VALIDATION_ERROR_MESSAGE = auto()
    PG_PROPERTY_VALIDATION_SATURATE = auto()
    PG_PROPERTY_VALIDATION_WRAP = auto()
PGNumericValidationConstants: TypeAlias = Union[_PGNumericValidationConstants, int]
PG_PROPERTY_VALIDATION_ERROR_MESSAGE = _PGNumericValidationConstants.PG_PROPERTY_VALIDATION_ERROR_MESSAGE
PG_PROPERTY_VALIDATION_SATURATE = _PGNumericValidationConstants.PG_PROPERTY_VALIDATION_SATURATE
PG_PROPERTY_VALIDATION_WRAP = _PGNumericValidationConstants.PG_PROPERTY_VALIDATION_WRAP

class PGInDialogValidator:
    """
    PGInDialogValidator() -> None
    
    Creates and manages a temporary wxTextCtrl for validation purposes.
    """

    def __init__(self) -> None:
        """
        PGInDialogValidator() -> None
        
        Creates and manages a temporary wxTextCtrl for validation purposes.
        """

    def DoValidate(self, propGrid: PropertyGrid, validator: wx.Validator, value: str) -> bool:
        """
        DoValidate(propGrid, validator, value) -> bool
        """
# end of class PGInDialogValidator


class StringProperty(PGProperty):
    """
    StringProperty(label=PG_LABEL, name=PG_LABEL, value='') -> None
    
    Basic property with string value.
    """

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: str='') -> None:
        """
        StringProperty(label=PG_LABEL, name=PG_LABEL, value='') -> None
        
        Basic property with string value.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def DoSetAttribute(self, name: str, value: PGVariant) -> bool:
        """
        DoSetAttribute(name, value) -> bool
        
        Reimplement this member function to add special handling for
        attributes of this property.
        """

    def OnSetValue(self) -> None:
        """
        OnSetValue() -> None
        
        This is updated so "<composed>" special value can be handled.
        """
# end of class StringProperty


class NumericProperty(PGProperty):
    """
    NumericProperty(label, name) -> None
    
    This is an abstract class which serves as a base class for numeric
    properties, like wxIntProperty, wxUIntProperty, wxFloatProperty.
    """

    def DoSetAttribute(self, name: str, value: PGVariant) -> bool:
        """
        DoSetAttribute(name, value) -> bool
        
        Reimplement this member function to add special handling for
        attributes of this property.
        """

    def AddSpinStepValue(self, stepScale: int) -> PGVariant:
        """
        AddSpinStepValue(stepScale) -> PGVariant
        
        Returns what would be the new value of the property after adding
        SpinCtrl editor step to the current value.
        """

    def UseSpinMotion(self) -> bool:
        """
        UseSpinMotion() -> bool
        
        Return true if value can be changed with SpinCtrl editor by moving the
        mouse.
        """

    def __init__(self, label: str, name: str) -> None:
        """
        """
# end of class NumericProperty


class NumericPropertyValidator(wx.Validator):
    """
    NumericPropertyValidator(numericType, base=10) -> None
    
    A more comprehensive numeric validator class.
    """

    class _NumericType(IntEnum):
        Signed = auto()
        Unsigned = auto()
        Float = auto()
    NumericType: TypeAlias = Union[_NumericType, int]
    Signed = _NumericType.Signed
    Unsigned = _NumericType.Unsigned
    Float = _NumericType.Float

    def __init__(self, numericType: NumericType, base: int=10) -> None:
        """
        NumericPropertyValidator(numericType, base=10) -> None
        
        A more comprehensive numeric validator class.
        """

    def Validate(self, parent: wx.Window) -> bool:
        """
        Validate(parent) -> bool
        
        Validates the window contents against the include or exclude lists, depending on the validator style.
        """
# end of class NumericPropertyValidator


class IntProperty(NumericProperty):
    """
    IntProperty(label=PG_LABEL, name=PG_LABEL, value=0) -> None
    IntProperty(label, name, value) -> None
    
    Basic property with integer value.
    """

    @overload
    def __init__(self, label: str, name: str, value: int) -> None:
        ...

    @overload
    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: int=0) -> None:
        """
        IntProperty(label=PG_LABEL, name=PG_LABEL, value=0) -> None
        IntProperty(label, name, value) -> None
        
        Basic property with integer value.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def ValidateValue(self, value: PGVariant, validationInfo: PGValidationInfo) -> bool:
        """
        ValidateValue(value, validationInfo) -> bool
        
        Implement this function in derived class to check the value.
        """

    def IntToValue(self, number: int, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        IntToValue(number, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts integer (possibly a choice selection) into wxVariant value
        appropriate for this property.
        """

    def DoGetValidator(self) -> wx.Validator:
        """
        DoGetValidator() -> wx.Validator
        
        Returns pointer to the wxValidator that should be used with the editor
        of this property (NULL for no validator).
        """

    def AddSpinStepValue(self, stepScale: int) -> PGVariant:
        """
        AddSpinStepValue(stepScale) -> PGVariant
        
        Returns what would be the new value of the property after adding
        SpinCtrl editor step to the current value.
        """

    @staticmethod
    def GetClassValidator() -> wx.Validator:
        """
        GetClassValidator() -> wx.Validator
        """
# end of class IntProperty


class UIntProperty(NumericProperty):
    """
    UIntProperty(label=PG_LABEL, name=PG_LABEL, value=0) -> None
    UIntProperty(label, name, value) -> None
    
    Basic property with unsigned integer value.
    """

    @overload
    def __init__(self, label: str, name: str, value: ULongLong) -> None:
        ...

    @overload
    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: int=0) -> None:
        """
        UIntProperty(label=PG_LABEL, name=PG_LABEL, value=0) -> None
        UIntProperty(label, name, value) -> None
        
        Basic property with unsigned integer value.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def DoSetAttribute(self, name: str, value: PGVariant) -> bool:
        """
        DoSetAttribute(name, value) -> bool
        
        Reimplement this member function to add special handling for
        attributes of this property.
        """

    def ValidateValue(self, value: PGVariant, validationInfo: PGValidationInfo) -> bool:
        """
        ValidateValue(value, validationInfo) -> bool
        
        Implement this function in derived class to check the value.
        """

    def DoGetValidator(self) -> wx.Validator:
        """
        DoGetValidator() -> wx.Validator
        
        Returns pointer to the wxValidator that should be used with the editor
        of this property (NULL for no validator).
        """

    def IntToValue(self, number: int, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        IntToValue(number, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts integer (possibly a choice selection) into wxVariant value
        appropriate for this property.
        """

    def AddSpinStepValue(self, stepScale: int) -> PGVariant:
        """
        AddSpinStepValue(stepScale) -> PGVariant
        
        Returns what would be the new value of the property after adding
        SpinCtrl editor step to the current value.
        """
# end of class UIntProperty


class FloatProperty(NumericProperty):
    """
    FloatProperty(label=PG_LABEL, name=PG_LABEL, value=0.0) -> None
    
    Basic property with double-precision floating point value.
    """

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: float=0.0) -> None:
        """
        FloatProperty(label=PG_LABEL, name=PG_LABEL, value=0.0) -> None
        
        Basic property with double-precision floating point value.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def DoSetAttribute(self, name: str, value: PGVariant) -> bool:
        """
        DoSetAttribute(name, value) -> bool
        
        Reimplement this member function to add special handling for
        attributes of this property.
        """

    def ValidateValue(self, value: PGVariant, validationInfo: PGValidationInfo) -> bool:
        """
        ValidateValue(value, validationInfo) -> bool
        
        Implement this function in derived class to check the value.
        """

    def DoGetValidator(self) -> wx.Validator:
        """
        DoGetValidator() -> wx.Validator
        
        Returns pointer to the wxValidator that should be used with the editor
        of this property (NULL for no validator).
        """

    def AddSpinStepValue(self, stepScale: int) -> PGVariant:
        """
        AddSpinStepValue(stepScale) -> PGVariant
        
        Returns what would be the new value of the property after adding
        SpinCtrl editor step to the current value.
        """

    @staticmethod
    def GetClassValidator() -> wx.Validator:
        """
        GetClassValidator() -> wx.Validator
        """
# end of class FloatProperty


class BoolProperty(PGProperty):
    """
    BoolProperty(label=PG_LABEL, name=PG_LABEL, value=False) -> None
    
    Basic property with boolean value.
    """

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: bool=False) -> None:
        """
        BoolProperty(label=PG_LABEL, name=PG_LABEL, value=False) -> None
        
        Basic property with boolean value.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def IntToValue(self, number: int, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        IntToValue(number, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts integer (possibly a choice selection) into wxVariant value
        appropriate for this property.
        """

    def DoSetAttribute(self, name: str, value: PGVariant) -> bool:
        """
        DoSetAttribute(name, value) -> bool
        
        Reimplement this member function to add special handling for
        attributes of this property.
        """
# end of class BoolProperty


class EnumProperty(PGProperty):
    """
    EnumProperty(label, name, choices, value=0) -> None
    EnumProperty(label=PG_LABEL, name=PG_LABEL, labels=[], values=[], value=0) -> None
    
    You can derive custom properties with choices from this class.
    """

    @overload
    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, labels: List[str]=[], values: List[int]=[], value: int=0) -> None:
        ...

    @overload
    def __init__(self, label: str, name: str, choices: PGChoices, value: int=0) -> None:
        """
        EnumProperty(label, name, choices, value=0) -> None
        EnumProperty(label=PG_LABEL, name=PG_LABEL, labels=[], values=[], value=0) -> None
        
        You can derive custom properties with choices from this class.
        """

    def GetItemCount(self) -> int:
        """
        GetItemCount() -> int
        """

    def OnSetValue(self) -> None:
        """
        OnSetValue() -> None
        
        This virtual function is called after m_value has been set.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def ValidateValue(self, value: PGVariant, validationInfo: PGValidationInfo) -> bool:
        """
        ValidateValue(value, validationInfo) -> bool
        
        Implement this function in derived class to check the value.
        """

    def IntToValue(self, number: int, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        IntToValue(number, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts integer (possibly a choice selection) into wxVariant value
        appropriate for this property.
        """

    def GetIndexForValue(self, value: int) -> int:
        """
        GetIndexForValue(value) -> int
        """

    def GetChoiceSelection(self) -> int:
        """
        GetChoiceSelection() -> int
        
        Returns which choice is currently selected.
        """
    @property
    def ChoiceSelection(self) -> int: ...
    @property
    def ItemCount(self) -> int: ...
# end of class EnumProperty


class EditEnumProperty(EnumProperty):
    """
    EditEnumProperty(label=PG_LABEL, name=PG_LABEL, labels=[], values=[], value='') -> None
    EditEnumProperty(label, name, choices, value='') -> None
    
    wxEnumProperty with wxString value and writable combo box editor.
    """

    @overload
    def __init__(self, label: str, name: str, choices: PGChoices, value: str='') -> None:
        ...

    @overload
    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, labels: List[str]=[], values: List[int]=[], value: str='') -> None:
        """
        EditEnumProperty(label=PG_LABEL, name=PG_LABEL, labels=[], values=[], value='') -> None
        EditEnumProperty(label, name, choices, value='') -> None
        
        wxEnumProperty with wxString value and writable combo box editor.
        """
# end of class EditEnumProperty


class FlagsProperty(PGProperty):
    """
    FlagsProperty(label, name, choices, value=0) -> None
    FlagsProperty(label=PG_LABEL, name=PG_LABEL, labels=[], values=[], value=0) -> None
    
    Represents a bit set that fits in a long integer.
    """

    @overload
    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, labels: List[str]=[], values: List[int]=[], value: int=0) -> None:
        ...

    @overload
    def __init__(self, label: str, name: str, choices: PGChoices, value: int=0) -> None:
        """
        FlagsProperty(label, name, choices, value=0) -> None
        FlagsProperty(label=PG_LABEL, name=PG_LABEL, labels=[], values=[], value=0) -> None
        
        Represents a bit set that fits in a long integer.
        """

    def OnSetValue(self) -> None:
        """
        OnSetValue() -> None
        
        This virtual function is called after m_value has been set.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def ChildChanged(self, thisValue: PGVariant, childIndex: int, childValue: PGVariant) -> PGVariant:
        """
        ChildChanged(thisValue, childIndex, childValue) -> PGVariant
        
        Called after value of a child property has been altered.
        """

    def RefreshChildren(self) -> None:
        """
        RefreshChildren() -> None
        
        Refresh values of child properties.
        """

    def DoSetAttribute(self, name: str, value: PGVariant) -> bool:
        """
        DoSetAttribute(name, value) -> bool
        
        Reimplement this member function to add special handling for
        attributes of this property.
        """

    def GetChoiceSelection(self) -> int:
        """
        GetChoiceSelection() -> int
        
        Returns which choice is currently selected.
        """

    def GetItemCount(self) -> int:
        """
        GetItemCount() -> int
        """

    def GetLabel(self, ind: int) -> str:
        """
        GetLabel(ind) -> str
        """
    @property
    def ChoiceSelection(self) -> int: ...
    @property
    def ItemCount(self) -> int: ...
# end of class FlagsProperty


class EditorDialogProperty(PGProperty):
    """
    EditorDialogProperty(label, name) -> None
    
    This is an abstract class which serves as a base class for the
    properties having a button triggering an editor dialog, like e.g.
    """

    def GetEditorDialog(self) -> PGEditorDialogAdapter:
        """
        GetEditorDialog() -> PGEditorDialogAdapter
        
        Returns instance of a new wxPGEditorDialogAdapter instance, which is
        used when user presses the (optional) button next to the editor
        control;.
        """

    def DoSetAttribute(self, name: str, value: PGVariant) -> bool:
        """
        DoSetAttribute(name, value) -> bool
        
        Reimplement this member function to add special handling for
        attributes of this property.
        """
    @property
    def EditorDialog(self) -> PGEditorDialogAdapter: ...

    def __init__(self, label: str, name: str) -> None:
        """
        """

    def DisplayEditorDialog(self, pg: PropertyGrid, value: PGVariant) -> bool:
        """
        DisplayEditorDialog(pg, value) -> bool
        
        Shows editor dialog.
        """
# end of class EditorDialogProperty


class FileProperty(EditorDialogProperty):
    """
    FileProperty(label=PG_LABEL, name=PG_LABEL, value='') -> None
    
    Like wxLongStringProperty, but the button triggers file selector
    instead.
    """

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: str='') -> None:
        """
        FileProperty(label=PG_LABEL, name=PG_LABEL, value='') -> None
        
        Like wxLongStringProperty, but the button triggers file selector
        instead.
        """

    def OnSetValue(self) -> None:
        """
        OnSetValue() -> None
        
        This virtual function is called after m_value has been set.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def DoSetAttribute(self, name: str, value: PGVariant) -> bool:
        """
        DoSetAttribute(name, value) -> bool
        
        Reimplement this member function to add special handling for
        attributes of this property.
        """

    def DoGetValidator(self) -> wx.Validator:
        """
        DoGetValidator() -> wx.Validator
        
        Returns pointer to the wxValidator that should be used with the editor
        of this property (NULL for no validator).
        """

    def GetFileName(self) -> str:
        """
        GetFileName() -> str
        
        Returns filename to file represented by current value.
        """

    @staticmethod
    def GetClassValidator() -> wx.Validator:
        """
        GetClassValidator() -> wx.Validator
        """
    @property
    def FileName(self) -> str: ...

    def DisplayEditorDialog(self, pg: PropertyGrid, value: PGVariant) -> Tuple[bool, PGVariant]:
        """
        DisplayEditorDialog(pg, value) -> Tuple[bool, PGVariant]
        
        Shows editor dialog.
        """
# end of class FileProperty


class LongStringProperty(EditorDialogProperty):
    """
    LongStringProperty(label=PG_LABEL, name=PG_LABEL, value='') -> None
    
    Like wxStringProperty, but has a button that triggers a small text
    editor dialog.
    """

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: str='') -> None:
        """
        LongStringProperty(label=PG_LABEL, name=PG_LABEL, value='') -> None
        
        Like wxStringProperty, but has a button that triggers a small text
        editor dialog.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def DisplayEditorDialog(self, pg: PropertyGrid, value: PGVariant) -> Tuple[bool, PGVariant]:
        """
        DisplayEditorDialog(pg, value) -> Tuple[bool, PGVariant]
        
        Shows editor dialog.
        """
# end of class LongStringProperty


class DirProperty(EditorDialogProperty):
    """
    DirProperty(label=PG_LABEL, name=PG_LABEL, value='') -> None
    
    Like wxLongStringProperty, but the button triggers directory selector
    instead.
    """

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: str='') -> None:
        """
        DirProperty(label=PG_LABEL, name=PG_LABEL, value='') -> None
        
        Like wxLongStringProperty, but the button triggers directory selector
        instead.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def DoGetValidator(self) -> wx.Validator:
        """
        DoGetValidator() -> wx.Validator
        
        Returns pointer to the wxValidator that should be used with the editor
        of this property (NULL for no validator).
        """

    def DisplayEditorDialog(self, pg: PropertyGrid, value: PGVariant) -> Tuple[bool, PGVariant]:
        """
        DisplayEditorDialog(pg, value) -> Tuple[bool, PGVariant]
        
        Shows editor dialog.
        """
# end of class DirProperty


class ArrayStringProperty(EditorDialogProperty):
    """
    ArrayStringProperty(label=PG_LABEL, name=PG_LABEL, value=[]) -> None
    
    Property that manages a list of strings.
    """

    class _ConversionFlags(IntFlag):
        Escape = auto()
        QuoteStrings = auto()
    ConversionFlags: TypeAlias = Union[_ConversionFlags, int]
    Escape = _ConversionFlags.Escape
    QuoteStrings = _ConversionFlags.QuoteStrings

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: List[str]=[]) -> None:
        """
        ArrayStringProperty(label=PG_LABEL, name=PG_LABEL, value=[]) -> None
        
        Property that manages a list of strings.
        """

    def OnSetValue(self) -> None:
        """
        OnSetValue() -> None
        
        This virtual function is called after m_value has been set.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def DoSetAttribute(self, name: str, value: PGVariant) -> bool:
        """
        DoSetAttribute(name, value) -> bool
        
        Reimplement this member function to add special handling for
        attributes of this property.
        """

    def ConvertArrayToString(self, arr: List[str], delimiter: wx.UniChar) -> str:
        """
        ConvertArrayToString(arr, delimiter) -> str
        
        Implement in derived class for custom array-to-string conversion.
        """

    def OnCustomStringEdit(self, parent: wx.Window, value: str) -> bool:
        """
        OnCustomStringEdit(parent, value) -> bool
        
        Shows string editor dialog to edit the individual item.
        """

    def CreateEditorDialog(self) -> PGArrayEditorDialog:
        """
        CreateEditorDialog() -> PGArrayEditorDialog
        
        Creates wxPGArrayEditorDialog for string editing.
        """

    @staticmethod
    def ArrayStringToString(src: List[str], delimiter: wx.UniChar, flags: int) -> str:
        """
        ArrayStringToString(src, delimiter, flags) -> str
        
        Generates string based on the contents of wxArrayString src.
        """

    def DisplayEditorDialog(self, pg: PropertyGrid, value: PGVariant) -> Tuple[bool, PGVariant]:
        """
        DisplayEditorDialog(pg, value) -> Tuple[bool, PGVariant]
        
        Shows editor dialog.
        """

    def GenerateValueAsString(self) -> None:
        """
        GenerateValueAsString() -> None
        
        Previously this was to be implemented in derived class for array-to-
        string conversion.
        """
# end of class ArrayStringProperty


class PGArrayEditorDialog(wx.Dialog):
    """
    PGArrayEditorDialog() -> None
    """

    def __init__(self) -> None:
        """
        PGArrayEditorDialog() -> None
        """

    def Init(self) -> None:
        """
        Init() -> None
        """

    def Create(self, parent: wx.Window, message: str, caption: str, style: int=AEDIALOG_STYLE, pos: wx.Point=wx.DefaultPosition, sz: wx.Size=wx.DefaultSize) -> bool:
        """
        Create(parent, message, caption, style=AEDIALOG_STYLE, pos=wx.DefaultPosition, sz=wx.DefaultSize) -> bool
        """

    def EnableCustomNewAction(self) -> None:
        """
        EnableCustomNewAction() -> None
        """

    def SetNewButtonText(self, text: str) -> None:
        """
        SetNewButtonText(text) -> None
        
        Sets tooltip text for button allowing the user to enter new string.
        """

    def SetDialogValue(self, value: PGVariant) -> None:
        """
        SetDialogValue(value) -> None
        
        Set value modified by dialog.
        """

    def GetDialogValue(self) -> PGVariant:
        """
        GetDialogValue() -> PGVariant
        
        Return value modified by dialog.
        """

    def GetTextCtrlValidator(self) -> wx.Validator:
        """
        GetTextCtrlValidator() -> wx.Validator
        
        Override to return wxValidator to be used with the wxTextCtrl in
        dialog.
        """

    def IsModified(self) -> bool:
        """
        IsModified() -> bool
        
        Returns true if array was actually modified.
        """

    def GetSelection(self) -> int:
        """
        GetSelection() -> int
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def DialogValue(self) -> PGVariant: ...
    @DialogValue.setter
    def DialogValue(self, value: PGVariant, /) -> None: ...
    @property
    def Selection(self) -> int: ...
    @property
    def TextCtrlValidator(self) -> wx.Validator: ...

    def ArrayGet(self, index: int) -> str:
        """
        ArrayGet(index) -> str
        """

    def ArrayGetCount(self) -> int:
        """
        ArrayGetCount() -> int
        """

    def ArrayInsert(self, str: str, index: int) -> bool:
        """
        ArrayInsert(str, index) -> bool
        """

    def ArraySet(self, index: int, str: str) -> bool:
        """
        ArraySet(index, str) -> bool
        """

    def ArrayRemoveAt(self, index: int) -> None:
        """
        ArrayRemoveAt(index) -> None
        """

    def ArraySwap(self, first: int, second: int) -> None:
        """
        ArraySwap(first, second) -> None
        """

    def OnCustomNewAction(self, resString: str) -> bool:
        """
        OnCustomNewAction(resString) -> bool
        """
# end of class PGArrayEditorDialog


class PGArrayStringEditorDialog(PGArrayEditorDialog):
    """
    PGArrayStringEditorDialog() -> None
    """

    def __init__(self) -> None:
        """
        PGArrayStringEditorDialog() -> None
        """

    def Init(self) -> None:
        """
        Init() -> None
        """

    def SetDialogValue(self, value: PGVariant) -> None:
        """
        SetDialogValue(value) -> None
        
        Set value modified by dialog.
        """

    def GetDialogValue(self) -> PGVariant:
        """
        GetDialogValue() -> PGVariant
        
        Return value modified by dialog.
        """

    def SetCustomButton(self, custBtText: str, pcc: List[StringProperty]) -> None:
        """
        SetCustomButton(custBtText, pcc) -> None
        """

    def OnCustomNewAction(self, resString: str) -> bool:
        """
        OnCustomNewAction(resString) -> bool
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def DialogValue(self) -> PGVariant: ...
    @DialogValue.setter
    def DialogValue(self, value: PGVariant, /) -> None: ...

    def ArrayGet(self, index: int) -> str:
        """
        ArrayGet(index) -> str
        """

    def ArrayGetCount(self) -> int:
        """
        ArrayGetCount() -> int
        """

    def ArrayInsert(self, str: str, index: int) -> bool:
        """
        ArrayInsert(str, index) -> bool
        """

    def ArraySet(self, index: int, str: str) -> bool:
        """
        ArraySet(index, str) -> bool
        """

    def ArrayRemoveAt(self, index: int) -> None:
        """
        ArrayRemoveAt(index) -> None
        """

    def ArraySwap(self, first: int, second: int) -> None:
        """
        ArraySwap(first, second) -> None
        """
# end of class PGArrayStringEditorDialog

#-- end-propgridprops --#
#-- begin-propgridadvprops --#
PG_COLOUR_WEB_BASE: int
PG_COLOUR_CUSTOM: int
PG_COLOUR_UNSPECIFIED: int
PG_PROP_TRANSLATE_CUSTOM: int

class ColourPropertyValue(wx.Object):
    """
    ColourPropertyValue() -> None
    ColourPropertyValue(v) -> None
    ColourPropertyValue(colour) -> None
    ColourPropertyValue(type) -> None
    ColourPropertyValue(type, colour) -> None
    
    Because text, background and other colours tend to differ between
    platforms, wxSystemColourProperty must be able to select between
    system colour and, when necessary, to pick a custom one.
    """

    @overload
    def __init__(self, v: ColourPropertyValue) -> None:
        ...

    @overload
    def __init__(self, colour: wx.Colour) -> None:
        ...

    @overload
    def __init__(self, type: Uint32) -> None:
        ...

    @overload
    def __init__(self, type: Uint32, colour: wx.Colour) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        ColourPropertyValue() -> None
        ColourPropertyValue(v) -> None
        ColourPropertyValue(colour) -> None
        ColourPropertyValue(type) -> None
        ColourPropertyValue(type, colour) -> None
        
        Because text, background and other colours tend to differ between
        platforms, wxSystemColourProperty must be able to select between
        system colour and, when necessary, to pick a custom one.
        """
    m_type: Uint32
    m_colour: wx.Colour

    def Init(self, type: Uint32, colour: wx.Colour) -> None:
        """
        Init(type, colour) -> None
        """
# end of class ColourPropertyValue


class FontProperty(EditorDialogProperty):
    """
    FontProperty(label=PG_LABEL, name=PG_LABEL, value=wx.Font()) -> None
    
    Property representing wxFont.
    """

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: wx.Font=wx.Font()) -> None:
        """
        FontProperty(label=PG_LABEL, name=PG_LABEL, value=wx.Font()) -> None
        
        Property representing wxFont.
        """

    def OnSetValue(self) -> None:
        """
        OnSetValue() -> None
        
        This virtual function is called after m_value has been set.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def ChildChanged(self, thisValue: PGVariant, childIndex: int, childValue: PGVariant) -> PGVariant:
        """
        ChildChanged(thisValue, childIndex, childValue) -> PGVariant
        
        Called after value of a child property has been altered.
        """

    def RefreshChildren(self) -> None:
        """
        RefreshChildren() -> None
        
        Refresh values of child properties.
        """

    def DisplayEditorDialog(self, pg: PropertyGrid, value: PGVariant) -> Tuple[bool, PGVariant]:
        """
        DisplayEditorDialog(pg, value) -> Tuple[bool, PGVariant]
        
        Shows editor dialog.
        """
# end of class FontProperty


class SystemColourProperty(EnumProperty):
    """
    SystemColourProperty(label=PG_LABEL, name=PG_LABEL, value=ColourPropertyValue()) -> None
    
    Has dropdown list of wxWidgets system colours.
    """

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: ColourPropertyValue=ColourPropertyValue()) -> None:
        """
        SystemColourProperty(label=PG_LABEL, name=PG_LABEL, value=ColourPropertyValue()) -> None
        
        Has dropdown list of wxWidgets system colours.
        """

    def OnSetValue(self) -> None:
        """
        OnSetValue() -> None
        
        This virtual function is called after m_value has been set.
        """

    def IntToValue(self, number: int, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        IntToValue(number, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts integer (possibly a choice selection) into wxVariant value
        appropriate for this property.
        """

    def ColourToString(self, col: wx.Colour, index: int, argFlags: int=0) -> str:
        """
        ColourToString(col, index, argFlags=0) -> str
        
        Override in derived class to customize how colours are printed as
        strings.
        """

    def GetCustomColourIndex(self) -> int:
        """
        GetCustomColourIndex() -> int
        
        Returns index of entry that triggers colour picker dialog (default is
        last).
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def OnEvent(self, propgrid: PropertyGrid, wnd_primary: wx.Window, event: wx.Event) -> bool:
        """
        OnEvent(propgrid, wnd_primary, event) -> bool
        
        Events received by editor widgets are processed here.
        """

    def DoSetAttribute(self, name: str, value: PGVariant) -> bool:
        """
        DoSetAttribute(name, value) -> bool
        
        Reimplement this member function to add special handling for
        attributes of this property.
        """

    def OnMeasureImage(self, item: int) -> wx.Size:
        """
        OnMeasureImage(item) -> wx.Size
        
        Returns size of the custom painted image in front of property.
        """

    def OnCustomPaint(self, dc: wx.DC, rect: wx.Rect, paintdata: PGPaintData) -> None:
        """
        OnCustomPaint(dc, rect, paintdata) -> None
        
        Override to paint an image in front of the property value text or
        drop-down list item (but only if wxPGProperty::OnMeasureImage is
        overridden as well).
        """

    def QueryColourFromUser(self, variant: PGVariant) -> bool:
        """
        QueryColourFromUser(variant) -> bool
        """

    def GetColour(self, index: int) -> wx.Colour:
        """
        GetColour(index) -> wx.Colour
        
        Default is to use wxSystemSettings::GetColour(index).
        """

    def GetVal(self, pVariant: Optional[PGVariant]=None) -> ColourPropertyValue:
        """
        GetVal(pVariant=None) -> ColourPropertyValue
        """
    @property
    def CustomColourIndex(self) -> int: ...
    @property
    def Val(self) -> ColourPropertyValue: ...
# end of class SystemColourProperty


class ColourProperty(SystemColourProperty):
    """
    ColourProperty(label=PG_LABEL, name=PG_LABEL, value=wx.WHITE) -> None
    
    Allows to select a colour from the list or with colour dialog.
    """

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: wx.Colour=wx.WHITE) -> None:
        """
        ColourProperty(label=PG_LABEL, name=PG_LABEL, value=wx.WHITE) -> None
        
        Allows to select a colour from the list or with colour dialog.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def GetColour(self, index: int) -> wx.Colour:
        """
        GetColour(index) -> wx.Colour
        
        Default is to use wxSystemSettings::GetColour(index).
        """
# end of class ColourProperty


class CursorProperty(EnumProperty):
    """
    CursorProperty(label=PG_LABEL, name=PG_LABEL, value=0) -> None
    
    Property representing wxCursor.
    """

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: int=0) -> None:
        """
        CursorProperty(label=PG_LABEL, name=PG_LABEL, value=0) -> None
        
        Property representing wxCursor.
        """

    def OnMeasureImage(self, item: int) -> wx.Size:
        """
        OnMeasureImage(item) -> wx.Size
        
        Returns size of the custom painted image in front of property.
        """

    def OnCustomPaint(self, dc: wx.DC, rect: wx.Rect, paintdata: PGPaintData) -> None:
        """
        OnCustomPaint(dc, rect, paintdata) -> None
        
        Override to paint an image in front of the property value text or
        drop-down list item (but only if wxPGProperty::OnMeasureImage is
        overridden as well).
        """
# end of class CursorProperty


class ImageFileProperty(FileProperty):
    """
    ImageFileProperty(label=PG_LABEL, name=PG_LABEL, value='') -> None
    
    Property representing image file(name).
    """

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: str='') -> None:
        """
        ImageFileProperty(label=PG_LABEL, name=PG_LABEL, value='') -> None
        
        Property representing image file(name).
        """

    def OnSetValue(self) -> None:
        """
        OnSetValue() -> None
        
        This virtual function is called after m_value has been set.
        """

    def OnMeasureImage(self, item: int) -> wx.Size:
        """
        OnMeasureImage(item) -> wx.Size
        
        Returns size of the custom painted image in front of property.
        """

    def OnCustomPaint(self, dc: wx.DC, rect: wx.Rect, paintdata: PGPaintData) -> None:
        """
        OnCustomPaint(dc, rect, paintdata) -> None
        
        Override to paint an image in front of the property value text or
        drop-down list item (but only if wxPGProperty::OnMeasureImage is
        overridden as well).
        """
# end of class ImageFileProperty


class MultiChoiceProperty(EditorDialogProperty):
    """
    MultiChoiceProperty(label, name=PG_LABEL, choices=[], value=[]) -> None
    MultiChoiceProperty(label, name, choices, value=[]) -> None
    MultiChoiceProperty(label=PG_LABEL, name=PG_LABEL, value=[]) -> None
    
    Property that manages a value resulting from wxMultiChoiceDialog.
    """

    @overload
    def __init__(self, label: str, name: str, choices: PGChoices, value: List[str]=[]) -> None:
        ...

    @overload
    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: List[str]=[]) -> None:
        ...

    @overload
    def __init__(self, label: str, name: str=PG_LABEL, choices: List[str]=[], value: List[str]=[]) -> None:
        """
        MultiChoiceProperty(label, name=PG_LABEL, choices=[], value=[]) -> None
        MultiChoiceProperty(label, name, choices, value=[]) -> None
        MultiChoiceProperty(label=PG_LABEL, name=PG_LABEL, value=[]) -> None
        
        Property that manages a value resulting from wxMultiChoiceDialog.
        """

    def OnSetValue(self) -> None:
        """
        OnSetValue() -> None
        
        This virtual function is called after m_value has been set.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def GetValueAsArrayInt(self) -> List[int]:
        """
        GetValueAsArrayInt() -> List[int]
        """
    @property
    def ValueAsArrayInt(self) -> List[int]: ...

    def DisplayEditorDialog(self, pg: PropertyGrid, value: PGVariant) -> Tuple[bool, PGVariant]:
        """
        DisplayEditorDialog(pg, value) -> Tuple[bool, PGVariant]
        
        Shows editor dialog.
        """
# end of class MultiChoiceProperty


class DateProperty(PGProperty):
    """
    DateProperty(label=PG_LABEL, name=PG_LABEL, value=wx.DateTime()) -> None
    
    Property representing wxDateTime.
    """

    def __init__(self, label: str=PG_LABEL, name: str=PG_LABEL, value: wx.DateTime=wx.DateTime()) -> None:
        """
        DateProperty(label=PG_LABEL, name=PG_LABEL, value=wx.DateTime()) -> None
        
        Property representing wxDateTime.
        """

    def OnSetValue(self) -> None:
        """
        OnSetValue() -> None
        
        This virtual function is called after m_value has been set.
        """

    def ValueToString(self, value: PGVariant, argFlags: int=0) -> str:
        """
        ValueToString(value, argFlags=0) -> str
        
        Converts property value into a text representation.
        """

    def StringToValue(self, text: str, argFlags: int=0) -> Tuple[bool, PGVariant]:
        """
        StringToValue(text, argFlags=0) -> Tuple[bool, PGVariant]
        
        Converts text into wxVariant value appropriate for this property.
        """

    def DoSetAttribute(self, name: str, value: PGVariant) -> bool:
        """
        DoSetAttribute(name, value) -> bool
        
        Reimplement this member function to add special handling for
        attributes of this property.
        """

    def SetFormat(self, format: str) -> None:
        """
        SetFormat(format) -> None
        """

    def GetFormat(self) -> str:
        """
        GetFormat() -> str
        """

    def SetDateValue(self, dt: wx.DateTime) -> None:
        """
        SetDateValue(dt) -> None
        """

    def GetDateValue(self) -> wx.DateTime:
        """
        GetDateValue() -> wx.DateTime
        """

    def GetDatePickerStyle(self) -> int:
        """
        GetDatePickerStyle() -> int
        """
    @property
    def DatePickerStyle(self) -> int: ...
    @property
    def DateValue(self) -> wx.DateTime: ...
    @DateValue.setter
    def DateValue(self, value: wx.DateTime, /) -> None: ...
    @property
    def Format(self) -> str: ...
    @Format.setter
    def Format(self, value: str, /) -> None: ...
# end of class DateProperty


class PGSpinCtrlEditor(PGTextCtrlEditor):
    """
    
    """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns pointer to the name of the editor.
        """

    def CreateControls(self, propgrid: PropertyGrid, property: PGProperty, pos: wx.Point, size: wx.Size) -> PGWindowList:
        """
        CreateControls(propgrid, property, pos, size) -> PGWindowList
        
        Instantiates editor controls.
        """

    def OnEvent(self, propgrid: PropertyGrid, property: PGProperty, wnd_primary: wx.Window, event: wx.Event) -> bool:
        """
        OnEvent(propgrid, property, wnd_primary, event) -> bool
        
        Handles events.
        """
    @property
    def Name(self) -> str: ...
# end of class PGSpinCtrlEditor


def PGGetDefaultImageWildcard() -> str:    """
    PGGetDefaultImageWildcard() -> str
    """
PGEditor_SpinCtrl: PGEditor
PGEditor_DatePickerCtrl: PGEditor

PyArrayStringProperty = wx.deprecated(ArrayStringProperty, "Use ArrayStringProperty instead.")
PyChoiceEditor = wx.deprecated(PGChoiceEditor, "Use PGChoiceEditor instead.")
PyColourProperty = wx.deprecated(ColourProperty, "Use ColourProperty instead.")
PyComboBoxEditor = wx.deprecated(PGComboBoxEditor, "Use PGComboBoxEditor instead.")
PyEditEnumProperty = wx.deprecated(EditEnumProperty, "Use PGEditEnumProperty instead.")
PyEditor = wx.deprecated(PGEditor, "Use PGEditor instead.")
PyEditorDialogAdapter = wx.deprecated(PGEditorDialogAdapter, "Use PGEditorDialogAdapter instead.")
PyEnumProperty = wx.deprecated(EnumProperty, "Use EnumProperty instead.")
PyFileProperty = wx.deprecated(FileProperty, "Use FileProperty instead.")
PyFlagsProperty = wx.deprecated(FlagsProperty, "Use FlagsProperty instead.")
PyFloatProperty = wx.deprecated(FloatProperty, "Use FloatProperty instead.")
PyFontProperty = wx.deprecated(FontProperty, "Use FontProperty instead.")
PyIntProperty = wx.deprecated(IntProperty, "Use IntProperty instead.")
PyLongStringProperty = wx.deprecated(LongStringProperty, "Use LongStringProperty instead.")
PyProperty = wx.deprecated(PGProperty, "Use PGProperty instead.")
PyStringProperty = wx.deprecated(StringProperty, "Use StringProperty instead.")
PySystemColourProperty = wx.deprecated(SystemColourProperty, "Use SystemColourProperty instead.")
PyTextCtrlEditor = wx.deprecated(PGTextCtrlEditor, "Use PGTextCtrlEditor instead.")
PyUIntProperty = wx.deprecated(UIntProperty, "Use UIntProperty instead.")

@wx.deprecated
def RegisterEditor(editor, editorName):
    pass
#-- end-propgridadvprops --#
#-- begin-propgridmanager --#
PropertyGridManagerNameStr: str

class PropertyGridPage(wx.EvtHandler, PropertyGridInterface, PropertyGridPageState):
    """
    PropertyGridPage() -> None
    
    Holder of property grid page information.
    """

    def __init__(self) -> None:
        """
        PropertyGridPage() -> None
        
        Holder of property grid page information.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Deletes all properties on page.
        """

    def FitColumns(self) -> wx.Size:
        """
        FitColumns() -> wx.Size
        
        Reduces column sizes to minimum possible that contents are still
        visibly (naturally some margin space will be applied as well).
        """

    def GetIndex(self) -> int:
        """
        GetIndex() -> int
        
        Returns page index in manager;.
        """

    def GetRoot(self) -> PGProperty:
        """
        GetRoot() -> PGProperty
        
        Returns "root property".
        """

    def GetSplitterPosition(self, col: int=0) -> int:
        """
        GetSplitterPosition(col=0) -> int
        
        Returns x-coordinate position of splitter on a page.
        """

    def GetStatePtr(self) -> PropertyGridPageState:
        """
        GetStatePtr() -> PropertyGridPageState
        
        Returns pointer to contained property grid state.
        """

    def GetToolId(self) -> int:
        """
        GetToolId() -> int
        
        Returns id of the tool bar item that represents this page on
        wxPropertyGridManager's wxToolBar.
        """

    def Init(self) -> None:
        """
        Init() -> None
        
        Do any member initialization in this method.
        """

    def IsHandlingAllEvents(self) -> bool:
        """
        IsHandlingAllEvents() -> bool
        
        Return false here to indicate unhandled events should be propagated to
        manager's parent, as normal.
        """

    def OnShow(self) -> None:
        """
        OnShow() -> None
        
        Called every time page is about to be shown.
        """

    def RefreshProperty(self, p: PGProperty) -> None:
        """
        RefreshProperty(p) -> None
        
        Refreshes given property on page.
        """

    def SetSplitterPosition(self, splitterPos: int, col: int=0) -> None:
        """
        SetSplitterPosition(splitterPos, col=0) -> None
        
        Sets splitter position on page.
        """
    @property
    def Index(self) -> int: ...
    @property
    def Root(self) -> PGProperty: ...
    @property
    def SplitterPosition(self) -> int: ...
    @SplitterPosition.setter
    def SplitterPosition(self, value: int, /) -> None: ...
    @property
    def StatePtr(self) -> PropertyGridPageState: ...
    @property
    def ToolId(self) -> int: ...
# end of class PropertyGridPage


class PropertyGridManager(wx.Panel, PropertyGridInterface):
    """
    PropertyGridManager() -> None
    PropertyGridManager(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=PGMAN_DEFAULT_STYLE, name=PropertyGridManagerNameStr) -> None
    
    wxPropertyGridManager is an efficient multi-page version of
    wxPropertyGrid, which can optionally have toolbar for mode and page
    selection, a help text box, and a header.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=PGMAN_DEFAULT_STYLE, name: str=PropertyGridManagerNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        PropertyGridManager() -> None
        PropertyGridManager(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=PGMAN_DEFAULT_STYLE, name=PropertyGridManagerNameStr) -> None
        
        wxPropertyGridManager is an efficient multi-page version of
        wxPropertyGrid, which can optionally have toolbar for mode and page
        selection, a help text box, and a header.
        """

    def AddPage(self, label: str='', bmp: wx.BitmapBundle=wx.BitmapBundle(), pageObj: Optional[PropertyGridPage]=None) -> PropertyGridPage:
        """
        AddPage(label='', bmp=wx.BitmapBundle(), pageObj=None) -> PropertyGridPage
        
        Creates new property page.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Deletes all properties and all pages.
        """

    def ClearPage(self, page: int) -> None:
        """
        ClearPage(page) -> None
        
        Deletes all properties on given page.
        """

    def CommitChangesFromEditor(self, flags: Uint32=0) -> bool:
        """
        CommitChangesFromEditor(flags=0) -> bool
        
        Forces updating the value of property from the editor control.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=PGMAN_DEFAULT_STYLE, name: str=PropertyGridManagerNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=PGMAN_DEFAULT_STYLE, name=PropertyGridManagerNameStr) -> bool
        
        Two step creation.
        """

    def EnableCategories(self, enable: bool) -> bool:
        """
        EnableCategories(enable) -> bool
        
        Enables or disables (shows/hides) categories according to parameter
        enable.
        """

    def EnsureVisible(self, id: PGPropArgCls) -> bool:
        """
        EnsureVisible(id) -> bool
        
        Selects page, scrolls and/or expands items to ensure that the given
        item is visible.
        """

    def GetColumnCount(self, page: int=-1) -> int:
        """
        GetColumnCount(page=-1) -> int
        
        Returns number of columns on given page.
        """

    def GetDescBoxHeight(self) -> int:
        """
        GetDescBoxHeight() -> int
        
        Returns height of the description text box.
        """

    def GetGrid(self) -> PropertyGrid:
        """
        GetGrid() -> PropertyGrid
        
        Returns pointer to the contained wxPropertyGrid.
        """

    def GetVIterator(self, flags: int) -> PGVIterator:
        """
        GetVIterator(flags) -> PGVIterator
        
        Similar to GetIterator(), but instead returns wxPGVIterator instance,
        which can be useful for forward-iterating through arbitrary property
        containers.
        """

    def GetCurrentPage(self) -> PropertyGridPage:
        """
        GetCurrentPage() -> PropertyGridPage
        
        Returns currently selected page.
        """

    @overload
    def GetPage(self, name: str) -> PropertyGridPage:
        ...

    @overload
    def GetPage(self, ind: int) -> PropertyGridPage:
        """
        GetPage(ind) -> PropertyGridPage
        GetPage(name) -> PropertyGridPage
        
        Returns page object for given page index.
        """

    def GetPageByName(self, name: str) -> int:
        """
        GetPageByName(name) -> int
        
        Returns index for a page name.
        """

    def GetPageByState(self, pstate: PropertyGridPageState) -> int:
        """
        GetPageByState(pstate) -> int
        
        Returns index for a relevant propertygrid state.
        """

    def GetPageCount(self) -> int:
        """
        GetPageCount() -> int
        
        Returns number of managed pages.
        """

    def GetPageName(self, index: int) -> str:
        """
        GetPageName(index) -> str
        
        Returns name of given page.
        """

    def GetPageRoot(self, index: int) -> PGProperty:
        """
        GetPageRoot(index) -> PGProperty
        
        Returns "root property" of the given page.
        """

    def GetSelectedPage(self) -> int:
        """
        GetSelectedPage() -> int
        
        Returns index to currently selected page.
        """

    def GetSelectedProperty(self) -> PGProperty:
        """
        GetSelectedProperty() -> PGProperty
        
        Alias for GetSelection().
        """

    def GetSelection(self) -> PGProperty:
        """
        GetSelection() -> PGProperty
        
        Shortcut for GetGrid()->GetSelection().
        """

    def GetToolBar(self) -> wx.ToolBar:
        """
        GetToolBar() -> wx.ToolBar
        
        Returns a pointer to the toolbar currently associated with the
        wxPropertyGridManager (if any).
        """

    def InsertPage(self, index: int, label: str, bmp: wx.BitmapBundle=wx.BitmapBundle(), pageObj: Optional[PropertyGridPage]=None) -> PropertyGridPage:
        """
        InsertPage(index, label, bmp=wx.BitmapBundle(), pageObj=None) -> PropertyGridPage
        
        Creates new property page.
        """

    def IsAnyModified(self) -> bool:
        """
        IsAnyModified() -> bool
        
        Returns true if any property on any page has been modified by the
        user.
        """

    def IsPageModified(self, index: int) -> bool:
        """
        IsPageModified(index) -> bool
        
        Returns true if any property on given page has been modified by the
        user.
        """

    def IsPropertySelected(self, id: PGPropArgCls) -> bool:
        """
        IsPropertySelected(id) -> bool
        
        Returns true if property is selected.
        """

    def RemovePage(self, page: int) -> bool:
        """
        RemovePage(page) -> bool
        
        Removes a page.
        """

    @overload
    def SelectPage(self, label: str) -> None:
        ...

    @overload
    def SelectPage(self, page: PropertyGridPage) -> None:
        ...

    @overload
    def SelectPage(self, index: int) -> None:
        """
        SelectPage(index) -> None
        SelectPage(label) -> None
        SelectPage(page) -> None
        
        Select and displays a given page.
        """

    def SelectProperty(self, id: PGPropArgCls, focus: bool=False) -> bool:
        """
        SelectProperty(id, focus=False) -> bool
        
        Select a property.
        """

    def SetColumnCount(self, colCount: int, page: int=-1) -> None:
        """
        SetColumnCount(colCount, page=-1) -> None
        
        Sets number of columns on given page (default is current page).
        """

    def SetColumnTitle(self, idx: int, title: str) -> None:
        """
        SetColumnTitle(idx, title) -> None
        
        Sets a column title.
        """

    def SetDescription(self, label: str, content: str) -> None:
        """
        SetDescription(label, content) -> None
        
        Sets label and text in description box.
        """

    def SetDescBoxHeight(self, ht: int, refresh: bool=True) -> None:
        """
        SetDescBoxHeight(ht, refresh=True) -> None
        
        Sets y coordinate of the description box splitter.
        """

    def SetSplitterLeft(self, subProps: bool=False, allPages: bool=True) -> None:
        """
        SetSplitterLeft(subProps=False, allPages=True) -> None
        
        Moves splitter as left as possible, while still allowing all labels to
        be shown in full.
        """

    def SetPageSplitterLeft(self, page: int, subProps: bool=False) -> None:
        """
        SetPageSplitterLeft(page, subProps=False) -> None
        
        Moves splitter as left as possible on an individual page, while still
        allowing all labels to be shown in full.
        """

    def SetPageSplitterPosition(self, page: int, pos: int, column: int=0) -> None:
        """
        SetPageSplitterPosition(page, pos, column=0) -> None
        
        Sets splitter position on individual page.
        """

    def SetSplitterPosition(self, pos: int, column: int=0) -> None:
        """
        SetSplitterPosition(pos, column=0) -> None
        
        Sets splitter position for all pages.
        """

    def ShowHeader(self, show: bool=True) -> None:
        """
        ShowHeader(show=True) -> None
        
        Show or hide the property grid header control.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    _PropertyGridManager__init__orig = __init__
    def _PropertyGridManager__init__(self, *args, **kw):
        _PropertyGridManager__init__orig(self, *args, **kw)
        self.DoDefaultTypeMappings()
        self.edited_objects = {}
        self.DoDefaultValueTypeMappings()
        if not hasattr(self.__class__, '_vt2setter'):
            self.__class__._vt2setter = {}
    __init__ = _PropertyGridManager__init__
    @property
    def ColumnCount(self) -> int: ...
    @ColumnCount.setter
    def ColumnCount(self, value: int, /) -> None: ...
    @property
    def CurrentPage(self) -> PropertyGridPage: ...
    @property
    def DescBoxHeight(self) -> int: ...
    @DescBoxHeight.setter
    def DescBoxHeight(self, value: int, /) -> None: ...
    @property
    def Grid(self) -> PropertyGrid: ...
    @property
    def PageCount(self) -> int: ...
    @property
    def SelectedPage(self) -> int: ...
    @property
    def SelectedProperty(self) -> PGProperty: ...
    @property
    def Selection(self) -> PGProperty: ...
    @property
    def ToolBar(self) -> wx.ToolBar: ...

    def CreatePropertyGrid(self) -> PropertyGrid:
        """
        CreatePropertyGrid() -> PropertyGrid
        
        Creates property grid for the manager.
        """
# end of class PropertyGridManager

#-- end-propgridmanager --#
