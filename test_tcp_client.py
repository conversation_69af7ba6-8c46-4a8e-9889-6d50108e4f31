#!/usr/bin/env python3
"""
Simple TCP client to test CaptureAndSave functionality
"""
import socket
import json
import time

def send_tcp_command(host='localhost', port=8888, command=''):
    """Send a command to the TCP server and return the response"""
    try:
        # Create socket connection
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            print(f"🔌 Connecting to {host}:{port}...")
            sock.connect((host, port))
            print("✅ Connected!")
            
            # Send command
            print(f"📤 Sending: {command}")
            sock.sendall((command + '\n').encode('utf-8'))
            
            # Receive response
            response = ""
            while True:
                data = sock.recv(4096).decode('utf-8')
                if not data:
                    break
                response += data
                # Check if we have a complete response (simple check)
                if '\n' in response:
                    break
            
            print(f"📥 Response: {response.strip()}")
            return response.strip()
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_basic_commands():
    """Test basic TCP commands"""
    print("🧪 Testing Basic Commands...")
    
    # Test device status
    print("\n1️⃣ Testing DEVICE_INFO:")
    send_tcp_command(command="DEVICE_INFO")
    
    # Test open device
    print("\n2️⃣ Testing OPEN_DEVICE:")
    send_tcp_command(command="OPEN_DEVICE")
    
    time.sleep(1)

def test_capture_and_save():
    """Test the new CAPTURE_AND_SAVE command"""
    print("\n🎯 Testing CAPTURE_AND_SAVE Command...")
    
    # Test with person ID and finger index
    person_id = "TEST123"
    finger_index = 2  # Left Index finger
    
    print(f"\n3️⃣ Testing CAPTURE_AND_SAVE {person_id} {finger_index}:")
    response = send_tcp_command(command=f"CAPTURE_AND_SAVE {person_id} {finger_index}")
    
    # Try to parse JSON response
    if response:
        try:
            # Look for JSON in the response
            lines = response.split('\n')
            for line in lines:
                if line.strip().startswith('{'):
                    json_data = json.loads(line.strip())
                    print("📊 Parsed JSON Response:")
                    print(json.dumps(json_data, indent=2))
                    break
        except json.JSONDecodeError:
            print("⚠️ Response is not JSON format")

def test_slaps_capture():
    """Test slaps capture commands"""
    print("\n🖐️ Testing Slaps Capture...")
    
    person_id = "TEST456"
    
    # Test Left 4 fingers
    print(f"\n4️⃣ Testing CAPTURE_AND_SAVE {person_id} 12 (Left 4 fingers):")
    send_tcp_command(command=f"CAPTURE_AND_SAVE {person_id} 12")
    
    time.sleep(2)
    
    # Test Right 4 fingers  
    print(f"\n5️⃣ Testing CAPTURE_AND_SAVE {person_id} 13 (Right 4 fingers):")
    send_tcp_command(command=f"CAPTURE_AND_SAVE {person_id} 13")

def test_error_cases():
    """Test error handling"""
    print("\n❌ Testing Error Cases...")
    
    # Test missing person ID
    print("\n6️⃣ Testing missing person ID:")
    send_tcp_command(command="CAPTURE_AND_SAVE")
    
    # Test empty person ID
    print("\n7️⃣ Testing empty person ID:")
    send_tcp_command(command="CAPTURE_AND_SAVE '' 2")
    
    # Test invalid finger index
    print("\n8️⃣ Testing invalid finger index:")
    send_tcp_command(command="CAPTURE_AND_SAVE TEST999 99")

def main():
    """Main test function"""
    print("🚀 TCP CaptureAndSave Test Client")
    print("=" * 50)
    
    # Test basic commands first
    test_basic_commands()
    
    # Test the new CAPTURE_AND_SAVE command
    test_capture_and_save()
    
    # Test slaps capture
    # test_slaps_capture()  # Uncomment to test slaps
    
    # Test error cases
    test_error_cases()
    
    print("\n✅ Testing completed!")
    print("\n💡 Instructions for manual testing:")
    print("1. Make sure C# app is running and device is open")
    print("2. When CAPTURE_AND_SAVE command is sent, place finger on scanner")
    print("3. Check C# console for detailed logs")
    print("4. Check database for saved records")

if __name__ == "__main__":
    main()
