// qbluetooth.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

namespace QBluetooth
{
%TypeHeaderCode
#include <qbluetooth.h>
%End

    enum Security
    {
        NoSecurity,
        Authorization,
        Authentication,
        Encryption,
        Secure,
    };

    typedef QFlags<QBluetooth::Security> SecurityFlags;
    QFlags<QBluetooth::Security> operator|(QBluetooth::Security f1, QFlags<QBluetooth::Security> f2);
%If (Qt_5_7_0 -)

    enum AttAccessConstraint
    {
        AttAuthorizationRequired,
        AttAuthenticationRequired,
        AttEncryptionRequired,
    };

%End
%If (Qt_5_7_0 -)
    typedef QFlags<QBluetooth::AttAccessConstraint> AttAccessConstraints;
%End
%If (Qt_5_7_0 -)
    QFlags<QBluetooth::AttAccessConstraint> operator|(QBluetooth::AttAccessConstraint f1, QFlags<QBluetooth::AttAccessConstraint> f2);
%End
};

%End
%If (Qt_5_4_0 -)
typedef quint16 QLowEnergyHandle;
%End
