// qbuffer.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QBuffer : public QIODevice
{
%TypeHeaderCode
#include <qbuffer.h>
%End

public:
    explicit QBuffer(QObject *parent /TransferThis/ = 0);
    QBuffer(QByteArray *byteArray /Constrained/, QObject *parent /TransferThis/ = 0);
    virtual ~QBuffer();
    QByteArray &buffer();
    const QByteArray &data() const;
    void setBuffer(QByteArray *a /Constrained/);
    void setData(const QByteArray &data);
    void setData(const char *adata /Array/, int alen /ArraySize/);
    virtual bool open(QIODevice::OpenMode openMode);
    virtual void close();
    virtual qint64 size() const;
    virtual qint64 pos() const;
    virtual bool seek(qint64 off);
    virtual bool atEnd() const;
    virtual bool canReadLine() const;

protected:
    virtual SIP_PYOBJECT readData(qint64 maxlen) /TypeHint="Py_v3:bytes;str",ReleaseGIL/ [qint64 (char *data, qint64 maxlen)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QBuffer::readData(s, a0) : sipCpp->readData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual qint64 writeData(const char *data /Array/, qint64 len /ArraySize/) /ReleaseGIL/;
    virtual void connectNotify(const QMetaMethod &);
    virtual void disconnectNotify(const QMetaMethod &);
};
