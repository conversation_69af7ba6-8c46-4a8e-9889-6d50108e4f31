// qstorageinfo.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_4_0 -)

class QStorageInfo
{
%TypeHeaderCode
#include <qstorageinfo.h>
%End

public:
    QStorageInfo();
    explicit QStorageInfo(const QString &path);
    explicit QStorageInfo(const QDir &dir);
    QStorageInfo(const QStorageInfo &other);
    ~QStorageInfo();
    void swap(QStorageInfo &other /Constrained/);
    void setPath(const QString &path);
    QString rootPath() const;
    QByteArray device() const;
    QByteArray fileSystemType() const;
    QString name() const;
    QString displayName() const;
    qint64 bytesTotal() const;
    qint64 bytesFree() const;
    qint64 bytesAvailable() const;
    bool isReadOnly() const;
    bool isReady() const;
    bool isValid() const;
    void refresh();
    static QList<QStorageInfo> mountedVolumes();
    static QStorageInfo root();
    bool isRoot() const;
%If (Qt_5_6_0 -)
    int blockSize() const;
%End
%If (Qt_5_9_0 -)
    QByteArray subvolume() const;
%End
};

%End
%If (Qt_5_4_0 -)
bool operator==(const QStorageInfo &first, const QStorageInfo &second);
%End
%If (Qt_5_4_0 -)
bool operator!=(const QStorageInfo &first, const QStorageInfo &second);
%End
