<?php
// Test script to verify capture API with correct enumeration values

echo "<h2>Testing Capture API with Correct Enumeration Values</h2>\n";

// Test data using string operation types as expected by current API
$testCases = [
    [
        'name' => 'Left 4 Fingers (Slaps)',
        'FingerPosition' => 12,  // CapFingerPosition.LeftFour
        'OperationType' => 'slaps'
    ],
    [
        'name' => 'Right 4 Fingers (Slaps)',
        'FingerPosition' => 13,  // CapFingerPosition.RightFour
        'OperationType' => 'slaps'
    ],
    [
        'name' => 'Two Thumbs (Slaps)',
        'FingerPosition' => 11,  // CapFingerPosition.TwoThumb
        'OperationType' => 'slaps'
    ],
    [
        'name' => 'Right Thumb (Flat)',
        'FingerPosition' => 1,   // CapFingerPosition.RightThumb
        'OperationType' => 'flat'
    ],
    [
        'name' => 'Right Thumb (Rolled)',
        'FingerPosition' => 1,   // CapFingerPosition.RightThumb
        'OperationType' => 'rolled'
    ]
];

foreach ($testCases as $test) {
    echo "<h3>Testing: {$test['name']}</h3>\n";
    echo "FingerPosition: {$test['FingerPosition']}, OperationType: {$test['OperationType']}<br>\n";
    
    $captureData = [
        'FingerPosition' => $test['FingerPosition'],
        'OperationType' => $test['OperationType'],
        'Timeout' => 30,
        'SaveImage' => true
    ];
    
    echo "Request JSON: " . json_encode($captureData, JSON_PRETTY_PRINT) . "<br>\n";
    
    // Make API call
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:9000/api/fingerprint/capture');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($captureData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 35);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode<br>\n";
    
    if ($curlError) {
        echo "CURL Error: $curlError<br>\n";
    } elseif ($response === false) {
        echo "No response received<br>\n";
    } else {
        $result = json_decode($response, true);
        if ($result) {
            echo "Response: " . json_encode($result, JSON_PRETTY_PRINT) . "<br>\n";
            
            if (isset($result['Success']) && $result['Success']) {
                echo "<strong style='color: green;'>✓ SUCCESS</strong><br>\n";
                if (isset($result['TemplateData'])) {
                    echo "Template Length: " . strlen($result['TemplateData']) . " characters<br>\n";
                }
                if (isset($result['ImageData'])) {
                    echo "Image Length: " . strlen($result['ImageData']) . " characters<br>\n";
                }
            } else {
                echo "<strong style='color: red;'>✗ FAILED</strong><br>\n";
                if (isset($result['Message'])) {
                    echo "Error: " . $result['Message'] . "<br>\n";
                }
            }
        } else {
            echo "Invalid JSON response: $response<br>\n";
        }
    }
    
    echo "<hr>\n";
    
    // Don't overwhelm the device - wait between tests
    sleep(2);
}

echo "<p><strong>Note:</strong> This test requires the device to be opened first and a finger to be placed on the scanner during each test.</p>\n";
?>
