@echo off
echo ========================================
echo   Port Availability Check
echo ========================================
echo.

echo Checking if ports are in use...
echo.

echo Port 9000 (Primary API):
netstat -an | find ":9000 " >nul
if %ERRORLEVEL% == 0 (
    echo ✗ Port 9000 is in use
    netstat -ano | find ":9000 "
) else (
    echo ✓ Port 9000 is available
)

echo.
echo Port 9001 (Backup API):
netstat -an | find ":9001 " >nul
if %ERRORLEVEL% == 0 (
    echo ✗ Port 9001 is in use
    netstat -ano | find ":9001 "
) else (
    echo ✓ Port 9001 is available
)

echo.
echo Port 8080 (Web Demo):
netstat -an | find ":8080 " >nul
if %ERRORLEVEL% == 0 (
    echo ✗ Port 8080 is in use
    netstat -ano | find ":8080 "
) else (
    echo ✓ Port 8080 is available
)

echo.
echo ========================================
echo If ports are in use, you can:
echo 1. Stop the processes using those ports
echo 2. The application will try alternative ports
echo ========================================
pause
