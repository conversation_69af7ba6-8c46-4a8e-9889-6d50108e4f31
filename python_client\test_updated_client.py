#!/usr/bin/env python3
"""
Quick test script to verify the updated Python client works with MainForm.cs TCP bridge
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tcp_client import TcpClient, TcpClientError

def test_connection():
    """Test basic connection to MainForm.cs TCP bridge"""
    print("🔌 Testing connection to MainForm.cs TCP bridge (port 8123)...")
    
    try:
        client = TcpClient()
        connected = client.test_connection()
        
        if connected:
            print("   ✅ Connection successful!")
            return True
        else:
            print("   ❌ Connection failed!")
            return False
            
    except Exception as e:
        print(f"   ❌ Connection error: {e}")
        return False

def test_capture():
    """Test capture command"""
    print("\n📸 Testing CAPTURE command...")
    
    try:
        client = TcpClient()
        result = client.capture_fingerprint(
            finger_position=1,
            user_id="TEST_USER"
        )
        
        print(f"   📡 Response: {result}")
        return True
        
    except TcpClientError as e:
        print(f"   ❌ Capture error: {e}")
        return False

def test_identify():
    """Test identify command"""
    print("\n🔍 Testing IDENTIFY command...")
    
    try:
        client = TcpClient()
        result = client.identify_fingerprint()
        
        print(f"   📡 Response: {result}")
        return True
        
    except TcpClientError as e:
        print(f"   ❌ Identify error: {e}")
        return False

def test_verify():
    """Test verify command"""
    print("\n✅ Testing VERIFY command...")
    
    try:
        client = TcpClient()
        result = client.verify_fingerprint(
            user_id="TEST_USER",
            finger_position=1
        )
        
        print(f"   📡 Response: {result}")
        return True
        
    except TcpClientError as e:
        print(f"   ❌ Verify error: {e}")
        return False

def test_enroll():
    """Test enroll command"""
    print("\n💾 Testing ENROLL command...")
    
    try:
        client = TcpClient()
        result = client.enroll_fingerprint(user_id="TEST_USER")
        
        print(f"   📡 Response: {result}")
        return True
        
    except TcpClientError as e:
        print(f"   ❌ Enroll error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Updated Python Client Test Suite")
    print("Testing connection to MainForm.cs TCP bridge on port 8123")
    print("=" * 60)
    
    tests = [
        ("Connection", test_connection),
        ("Capture", test_capture),
        ("Identify", test_identify),
        ("Verify", test_verify),
        ("Enroll", test_enroll)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
        except Exception as e:
            print(f"❌ ERROR {test_name}: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed >= 1:  # At least connection should work
        print("🎉 Python client is properly configured for MainForm.cs!")
        if passed < total:
            print("⚠️  Some commands failed - this is expected if C# app is not running or device not connected")
        return 0
    else:
        print("❌ Connection failed - make sure C# application is running")
        return 1

if __name__ == "__main__":
    sys.exit(main())
