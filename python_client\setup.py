#!/usr/bin/env python3
"""
Setup script for MultipleFinger Bridge REST API
"""

import os
import sys
import subprocess
import venv
from pathlib import Path


def create_virtual_environment(venv_path: Path):
    """Create a virtual environment"""
    print(f"Creating virtual environment at {venv_path}")
    venv.create(venv_path, with_pip=True)
    print("✅ Virtual environment created")


def get_pip_executable(venv_path: Path):
    """Get the pip executable path for the virtual environment"""
    if sys.platform == "win32":
        return venv_path / "Scripts" / "pip.exe"
    else:
        return venv_path / "bin" / "pip"


def install_requirements(venv_path: Path):
    """Install requirements in the virtual environment"""
    pip_exe = get_pip_executable(venv_path)
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    print("Installing Python packages...")
    try:
        subprocess.run([
            str(pip_exe), "install", "-r", str(requirements_file)
        ], check=True)
        print("✅ Requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False
    
    return True


def create_activation_script(venv_path: Path):
    """Create activation scripts for convenience"""
    project_dir = Path(__file__).parent
    
    if sys.platform == "win32":
        # Windows batch file
        activate_script = project_dir / "activate.bat"
        with open(activate_script, 'w') as f:
            f.write(f"@echo off\n")
            f.write(f"call {venv_path}\\Scripts\\activate.bat\n")
            f.write(f"echo Virtual environment activated\n")
            f.write(f"echo Run 'python start_api.py' to start the REST API\n")
        
        # Windows PowerShell script
        activate_ps1 = project_dir / "activate.ps1"
        with open(activate_ps1, 'w') as f:
            f.write(f"& {venv_path}\\Scripts\\Activate.ps1\n")
            f.write(f"Write-Host 'Virtual environment activated'\n")
            f.write(f"Write-Host 'Run python start_api.py to start the REST API'\n")
    else:
        # Unix shell script
        activate_script = project_dir / "activate.sh"
        with open(activate_script, 'w') as f:
            f.write(f"#!/bin/bash\n")
            f.write(f"source {venv_path}/bin/activate\n")
            f.write(f"echo 'Virtual environment activated'\n")
            f.write(f"echo 'Run python start_api.py to start the REST API'\n")
        
        # Make executable
        os.chmod(activate_script, 0o755)
    
    print("✅ Activation scripts created")


def main():
    """Main setup function"""
    print("MultipleFinger Bridge REST API Setup")
    print("=" * 40)
    
    project_dir = Path(__file__).parent
    venv_path = project_dir / "venv"
    
    # Check if virtual environment already exists
    if venv_path.exists():
        print("Virtual environment already exists")
        response = input("Do you want to recreate it? (y/N): ").lower()
        if response == 'y':
            import shutil
            shutil.rmtree(venv_path)
        else:
            print("Using existing virtual environment")
    
    # Create virtual environment if it doesn't exist
    if not venv_path.exists():
        create_virtual_environment(venv_path)
    
    # Install requirements
    if not install_requirements(venv_path):
        return 1
    
    # Create activation scripts
    create_activation_script(venv_path)
    
    print("\n" + "=" * 40)
    print("✅ Setup completed successfully!")
    print("\nNext steps:")
    print("1. Make sure MultipleFinger.exe is running")
    
    if sys.platform == "win32":
        print("2. Run: activate.bat")
    else:
        print("2. Run: source activate.sh")
    
    print("3. Run: python start_api.py")
    print("4. Test: python test_rest_api.py")
    print("\nThe REST API will be available at http://localhost:5000")
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
