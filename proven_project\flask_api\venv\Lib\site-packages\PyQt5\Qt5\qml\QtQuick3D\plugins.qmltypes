import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtQuick3D 1.15'

Module {
    dependencies: [
        "QtQuick 2.15",
        "QtQuick.Window 2.1",
        "QtQuick3D.Effects 1.15",
        "QtQuick3D.Materials 1.15"
    ]
    Component {
        name: "QQuick3DAbstractLight"
        defaultProperty: "data"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D/Light 1.14"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "QSSGShadowMapQuality"
            values: {
                "ShadowMapQualityLow": 0,
                "ShadowMapQualityMedium": 1,
                "ShadowMapQualityHigh": 2,
                "ShadowMapQualityVeryHigh": 3
            }
        }
        Property { name: "color"; type: "QColor" }
        Property { name: "ambientColor"; type: "QColor" }
        Property { name: "brightness"; type: "float" }
        Property { name: "scope"; type: "QQuick3DNode"; isPointer: true }
        Property { name: "castsShadow"; type: "bool" }
        Property { name: "shadowBias"; type: "float" }
        Property { name: "shadowFactor"; type: "float" }
        Property { name: "shadowMapQuality"; type: "QSSGShadowMapQuality" }
        Property { name: "shadowMapFar"; type: "float" }
        Property { name: "shadowFilter"; type: "float" }
        Method {
            name: "setColor"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "setAmbientColor"
            Parameter { name: "ambientColor"; type: "QColor" }
        }
        Method {
            name: "setBrightness"
            Parameter { name: "brightness"; type: "float" }
        }
        Method {
            name: "setScope"
            Parameter { name: "scope"; type: "QQuick3DNode"; isPointer: true }
        }
        Method {
            name: "setCastsShadow"
            Parameter { name: "castsShadow"; type: "bool" }
        }
        Method {
            name: "setShadowBias"
            Parameter { name: "shadowBias"; type: "float" }
        }
        Method {
            name: "setShadowFactor"
            Parameter { name: "shadowFactor"; type: "float" }
        }
        Method {
            name: "setShadowMapQuality"
            Parameter { name: "shadowMapQuality"; type: "QSSGShadowMapQuality" }
        }
        Method {
            name: "setShadowMapFar"
            Parameter { name: "shadowMapFar"; type: "float" }
        }
        Method {
            name: "setShadowFilter"
            Parameter { name: "shadowFilter"; type: "float" }
        }
    }
    Component {
        name: "QQuick3DAreaLight"
        defaultProperty: "data"
        prototype: "QQuick3DAbstractLight"
        exports: ["QtQuick3D/AreaLight 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "width"; type: "float" }
        Property { name: "height"; type: "float" }
        Method {
            name: "setWidth"
            Parameter { name: "width"; type: "float" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "height"; type: "float" }
        }
    }
    Component {
        name: "QQuick3DCamera"
        defaultProperty: "data"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D/Camera 1.14", "QtQuick3D/Camera 1.15"]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "FieldOfViewOrientation"
            values: {
                "Vertical": 0,
                "Horizontal": 1
            }
        }
        Property { name: "frustumCullingEnabled"; type: "bool" }
        Method {
            name: "setFrustumCullingEnabled"
            Parameter { name: "frustumCullingEnabled"; type: "bool" }
        }
        Method {
            name: "mapToViewport"
            type: "QVector3D"
            Parameter { name: "scenePos"; type: "QVector3D" }
        }
        Method {
            name: "mapFromViewport"
            type: "QVector3D"
            Parameter { name: "viewportPos"; type: "QVector3D" }
        }
        Method {
            name: "lookAt"
            revision: 1
            Parameter { name: "scenePos"; type: "QVector3D" }
        }
        Method {
            name: "lookAt"
            revision: 1
            Parameter { name: "node"; type: "const QQuick3DNode"; isPointer: true }
        }
    }
    Component {
        name: "QQuick3DCustomCamera"
        defaultProperty: "data"
        prototype: "QQuick3DCamera"
        exports: ["QtQuick3D/CustomCamera 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "projection"; type: "QMatrix4x4" }
        Method {
            name: "setProjection"
            Parameter { name: "projection"; type: "QMatrix4x4" }
        }
    }
    Component {
        name: "QQuick3DCustomMaterial"
        defaultProperty: "data"
        prototype: "QQuick3DMaterial"
        exports: ["QtQuick3D.Materials/CustomMaterial 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "hasTransparency"; type: "bool" }
        Property { name: "hasRefraction"; type: "bool" }
        Property { name: "alwaysDirty"; type: "bool" }
        Property { name: "shaderInfo"; type: "QQuick3DShaderUtilsShaderInfo"; isPointer: true }
        Property { name: "passes"; type: "QQuick3DShaderUtilsRenderPass"; isList: true; isReadonly: true }
        Signal {
            name: "hasTransparencyChanged"
            Parameter { name: "hasTransparency"; type: "bool" }
        }
        Signal {
            name: "hasRefractionChanged"
            Parameter { name: "hasRefraction"; type: "bool" }
        }
        Signal {
            name: "alwaysDirtyChanged"
            Parameter { name: "alwaysDirty"; type: "bool" }
        }
        Method {
            name: "setHasTransparency"
            Parameter { name: "hasTransparency"; type: "bool" }
        }
        Method {
            name: "setHasRefraction"
            Parameter { name: "hasRefraction"; type: "bool" }
        }
        Method {
            name: "setShaderInfo"
            Parameter { name: "shaderInfo"; type: "QQuick3DShaderUtilsShaderInfo"; isPointer: true }
        }
        Method {
            name: "setAlwaysDirty"
            Parameter { name: "alwaysDirty"; type: "bool" }
        }
    }
    Component {
        name: "QQuick3DDefaultMaterial"
        defaultProperty: "data"
        prototype: "QQuick3DMaterial"
        exports: [
            "QtQuick3D/DefaultMaterial 1.14",
            "QtQuick3D/DefaultMaterial 1.15"
        ]
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "Lighting"
            values: {
                "NoLighting": 0,
                "FragmentLighting": 1
            }
        }
        Enum {
            name: "BlendMode"
            values: {
                "SourceOver": 0,
                "Screen": 1,
                "Multiply": 2,
                "Overlay": 3,
                "ColorBurn": 4,
                "ColorDodge": 5
            }
        }
        Enum {
            name: "SpecularModel"
            values: {
                "Default": 0,
                "KGGX": 1,
                "KWard": 2
            }
        }
        Property { name: "lighting"; type: "Lighting" }
        Property { name: "blendMode"; type: "BlendMode" }
        Property { name: "diffuseColor"; type: "QColor" }
        Property { name: "diffuseMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "emissiveFactor"; type: "float" }
        Property { name: "emissiveMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "emissiveColor"; type: "QColor" }
        Property { name: "specularReflectionMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "specularMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "specularModel"; type: "SpecularModel" }
        Property { name: "specularTint"; type: "QColor" }
        Property { name: "indexOfRefraction"; type: "float" }
        Property { name: "fresnelPower"; type: "float" }
        Property { name: "specularAmount"; type: "float" }
        Property { name: "specularRoughness"; type: "float" }
        Property { name: "roughnessMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "roughnessChannel"; revision: 1; type: "TextureChannelMapping" }
        Property { name: "opacity"; type: "float" }
        Property { name: "opacityMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "opacityChannel"; revision: 1; type: "TextureChannelMapping" }
        Property { name: "bumpMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "bumpAmount"; type: "float" }
        Property { name: "normalMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "translucencyMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "translucencyChannel"; revision: 1; type: "TextureChannelMapping" }
        Property { name: "translucentFalloff"; type: "float" }
        Property { name: "diffuseLightWrap"; type: "float" }
        Property { name: "vertexColorsEnabled"; type: "bool" }
        Signal {
            name: "lightingChanged"
            Parameter { name: "lighting"; type: "Lighting" }
        }
        Signal {
            name: "blendModeChanged"
            Parameter { name: "blendMode"; type: "BlendMode" }
        }
        Signal {
            name: "diffuseColorChanged"
            Parameter { name: "diffuseColor"; type: "QColor" }
        }
        Signal {
            name: "diffuseMapChanged"
            Parameter { name: "diffuseMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "emissiveFactorChanged"
            Parameter { name: "emissiveFactor"; type: "float" }
        }
        Signal {
            name: "emissiveMapChanged"
            Parameter { name: "emissiveMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "emissiveColorChanged"
            Parameter { name: "emissiveColor"; type: "QColor" }
        }
        Signal {
            name: "specularReflectionMapChanged"
            Parameter { name: "specularReflectionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "specularMapChanged"
            Parameter { name: "specularMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "specularModelChanged"
            Parameter { name: "specularModel"; type: "SpecularModel" }
        }
        Signal {
            name: "specularTintChanged"
            Parameter { name: "specularTint"; type: "QColor" }
        }
        Signal {
            name: "indexOfRefractionChanged"
            Parameter { name: "indexOfRefraction"; type: "float" }
        }
        Signal {
            name: "fresnelPowerChanged"
            Parameter { name: "fresnelPower"; type: "float" }
        }
        Signal {
            name: "specularAmountChanged"
            Parameter { name: "specularAmount"; type: "float" }
        }
        Signal {
            name: "specularRoughnessChanged"
            Parameter { name: "specularRoughness"; type: "float" }
        }
        Signal {
            name: "roughnessMapChanged"
            Parameter { name: "roughnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "opacityChanged"
            Parameter { name: "opacity"; type: "float" }
        }
        Signal {
            name: "opacityMapChanged"
            Parameter { name: "opacityMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "bumpMapChanged"
            Parameter { name: "bumpMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "bumpAmountChanged"
            Parameter { name: "bumpAmount"; type: "float" }
        }
        Signal {
            name: "normalMapChanged"
            Parameter { name: "normalMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "translucencyMapChanged"
            Parameter { name: "translucencyMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "translucentFalloffChanged"
            Parameter { name: "translucentFalloff"; type: "float" }
        }
        Signal {
            name: "diffuseLightWrapChanged"
            Parameter { name: "diffuseLightWrap"; type: "float" }
        }
        Signal {
            name: "vertexColorsEnabledChanged"
            Parameter { name: "vertexColorsEnabled"; type: "bool" }
        }
        Signal {
            name: "roughnessChannelChanged"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
        Signal {
            name: "opacityChannelChanged"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
        Signal {
            name: "translucencyChannelChanged"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
        Method {
            name: "setLighting"
            Parameter { name: "lighting"; type: "Lighting" }
        }
        Method {
            name: "setBlendMode"
            Parameter { name: "blendMode"; type: "BlendMode" }
        }
        Method {
            name: "setDiffuseColor"
            Parameter { name: "diffuseColor"; type: "QColor" }
        }
        Method {
            name: "setDiffuseMap"
            Parameter { name: "diffuseMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setEmissiveFactor"
            Parameter { name: "emissiveFactor"; type: "float" }
        }
        Method {
            name: "setEmissiveMap"
            Parameter { name: "emissiveMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setEmissiveColor"
            Parameter { name: "emissiveColor"; type: "QColor" }
        }
        Method {
            name: "setSpecularReflectionMap"
            Parameter { name: "specularReflectionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setSpecularMap"
            Parameter { name: "specularMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setSpecularModel"
            Parameter { name: "specularModel"; type: "SpecularModel" }
        }
        Method {
            name: "setSpecularTint"
            Parameter { name: "specularTint"; type: "QColor" }
        }
        Method {
            name: "setIndexOfRefraction"
            Parameter { name: "indexOfRefraction"; type: "float" }
        }
        Method {
            name: "setFresnelPower"
            Parameter { name: "fresnelPower"; type: "float" }
        }
        Method {
            name: "setSpecularAmount"
            Parameter { name: "specularAmount"; type: "float" }
        }
        Method {
            name: "setSpecularRoughness"
            Parameter { name: "specularRoughness"; type: "float" }
        }
        Method {
            name: "setRoughnessMap"
            Parameter { name: "roughnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setOpacity"
            Parameter { name: "opacity"; type: "float" }
        }
        Method {
            name: "setOpacityMap"
            Parameter { name: "opacityMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setBumpMap"
            Parameter { name: "bumpMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setBumpAmount"
            Parameter { name: "bumpAmount"; type: "float" }
        }
        Method {
            name: "setNormalMap"
            Parameter { name: "normalMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setTranslucencyMap"
            Parameter { name: "translucencyMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setTranslucentFalloff"
            Parameter { name: "translucentFalloff"; type: "float" }
        }
        Method {
            name: "setDiffuseLightWrap"
            Parameter { name: "diffuseLightWrap"; type: "float" }
        }
        Method {
            name: "setVertexColorsEnabled"
            Parameter { name: "vertexColorsEnabled"; type: "bool" }
        }
        Method {
            name: "setRoughnessChannel"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
        Method {
            name: "setOpacityChannel"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
        Method {
            name: "setTranslucencyChannel"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
    }
    Component {
        name: "QQuick3DDirectionalLight"
        defaultProperty: "data"
        prototype: "QQuick3DAbstractLight"
        exports: ["QtQuick3D/DirectionalLight 1.14"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuick3DEffect"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D.Effects/Effect 1.15"]
        exportMetaObjectRevisions: [0]
        Property { name: "passes"; type: "QQuick3DShaderUtilsRenderPass"; isList: true; isReadonly: true }
    }
    Component {
        name: "QQuick3DFrustumCamera"
        defaultProperty: "data"
        prototype: "QQuick3DPerspectiveCamera"
        exports: ["QtQuick3D/FrustumCamera 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "top"; type: "float" }
        Property { name: "bottom"; type: "float" }
        Property { name: "right"; type: "float" }
        Property { name: "left"; type: "float" }
        Method {
            name: "setTop"
            Parameter { name: "top"; type: "float" }
        }
        Method {
            name: "setBottom"
            Parameter { name: "bottom"; type: "float" }
        }
        Method {
            name: "setRight"
            Parameter { name: "right"; type: "float" }
        }
        Method {
            name: "setLeft"
            Parameter { name: "left"; type: "float" }
        }
    }
    Component {
        name: "QQuick3DGeometry"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/Geometry 1.14"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "name"; type: "string" }
        Signal { name: "geometryNodeDirty" }
        Method {
            name: "setName"
            Parameter { name: "name"; type: "string" }
        }
    }
    Component {
        name: "QQuick3DLoader"
        defaultProperty: "data"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D/Loader3D 1.14"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Status"
            values: {
                "Null": 0,
                "Ready": 1,
                "Loading": 2,
                "Error": 3
            }
        }
        Property { name: "active"; type: "bool" }
        Property { name: "source"; type: "QUrl" }
        Property { name: "sourceComponent"; type: "QQmlComponent"; isPointer: true }
        Property { name: "item"; type: "QObject"; isReadonly: true; isPointer: true }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "progress"; type: "double"; isReadonly: true }
        Property { name: "asynchronous"; type: "bool" }
        Signal { name: "loaded" }
        Method {
            name: "setSource"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
    }
    Component {
        name: "QQuick3DMaterial"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/Material 1.14"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "CullMode"
            values: {
                "BackFaceCulling": 1,
                "FrontFaceCulling": 2,
                "NoCulling": 3
            }
        }
        Enum {
            name: "TextureChannelMapping"
            values: {
                "R": 0,
                "G": 1,
                "B": 2,
                "A": 3
            }
        }
        Property { name: "lightmapIndirect"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "lightmapRadiosity"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "lightmapShadow"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "lightProbe"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "displacementMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "displacementAmount"; type: "float" }
        Property { name: "cullMode"; type: "CullMode" }
        Signal {
            name: "lightmapIndirectChanged"
            Parameter { name: "lightmapIndirect"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "lightmapRadiosityChanged"
            Parameter { name: "lightmapRadiosity"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "lightmapShadowChanged"
            Parameter { name: "lightmapShadow"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "lightProbeChanged"
            Parameter { name: "lightProbe"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "displacementMapChanged"
            Parameter { name: "displacementMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "displacementAmountChanged"
            Parameter { name: "displacementAmount"; type: "float" }
        }
        Signal {
            name: "cullModeChanged"
            Parameter { name: "cullMode"; type: "CullMode" }
        }
        Method {
            name: "setLightmapIndirect"
            Parameter { name: "lightmapIndirect"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setLightmapRadiosity"
            Parameter { name: "lightmapRadiosity"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setLightmapShadow"
            Parameter { name: "lightmapShadow"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setLightProbe"
            Parameter { name: "lightProbe"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setDisplacementMap"
            Parameter { name: "displacementMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setDisplacementAmount"
            Parameter { name: "displacementAmount"; type: "float" }
        }
        Method {
            name: "setCullMode"
            Parameter { name: "cullMode"; type: "CullMode" }
        }
    }
    Component {
        name: "QQuick3DModel"
        defaultProperty: "data"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D/Model 1.14", "QtQuick3D/Model 1.15"]
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "QSSGTessellationModeValues"
            values: {
                "NoTessellation": 0,
                "Linear": 1,
                "Phong": 2,
                "NPatch": 3
            }
        }
        Property { name: "source"; type: "QUrl" }
        Property { name: "tessellationMode"; type: "QSSGTessellationModeValues" }
        Property { name: "edgeTessellation"; type: "float" }
        Property { name: "innerTessellation"; type: "float" }
        Property { name: "isWireframeMode"; type: "bool" }
        Property { name: "castsShadows"; type: "bool" }
        Property { name: "receivesShadows"; type: "bool" }
        Property { name: "materials"; type: "QQuick3DMaterial"; isList: true; isReadonly: true }
        Property { name: "pickable"; type: "bool" }
        Property { name: "geometry"; type: "QQuick3DGeometry"; isPointer: true }
        Property { name: "bounds"; revision: 1; type: "QQuick3DBounds3"; isReadonly: true }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setTessellationMode"
            Parameter { name: "tessellationMode"; type: "QSSGTessellationModeValues" }
        }
        Method {
            name: "setEdgeTessellation"
            Parameter { name: "edgeTessellation"; type: "float" }
        }
        Method {
            name: "setInnerTessellation"
            Parameter { name: "innerTessellation"; type: "float" }
        }
        Method {
            name: "setIsWireframeMode"
            Parameter { name: "isWireframeMode"; type: "bool" }
        }
        Method {
            name: "setCastsShadows"
            Parameter { name: "castsShadows"; type: "bool" }
        }
        Method {
            name: "setReceivesShadows"
            Parameter { name: "receivesShadows"; type: "bool" }
        }
        Method {
            name: "setPickable"
            Parameter { name: "pickable"; type: "bool" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "geometry"; type: "QQuick3DGeometry"; isPointer: true }
        }
        Method {
            name: "setBounds"
            Parameter { name: "min"; type: "QVector3D" }
            Parameter { name: "max"; type: "QVector3D" }
        }
    }
    Component {
        name: "QQuick3DNode"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/Node 1.14", "QtQuick3D/Node 1.15"]
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "TransformSpace"
            values: {
                "LocalSpace": 0,
                "ParentSpace": 1,
                "SceneSpace": 2
            }
        }
        Enum {
            name: "StaticFlags"
            values: {
                "None": 0
            }
        }
        Property { name: "x"; type: "float" }
        Property { name: "y"; type: "float" }
        Property { name: "z"; type: "float" }
        Property { name: "rotation"; revision: 1; type: "QQuaternion" }
        Property { name: "eulerRotation"; revision: 1; type: "QVector3D" }
        Property { name: "position"; type: "QVector3D" }
        Property { name: "scale"; type: "QVector3D" }
        Property { name: "pivot"; type: "QVector3D" }
        Property { name: "opacity"; type: "float" }
        Property { name: "visible"; type: "bool" }
        Property { name: "forward"; type: "QVector3D"; isReadonly: true }
        Property { name: "up"; type: "QVector3D"; isReadonly: true }
        Property { name: "right"; type: "QVector3D"; isReadonly: true }
        Property { name: "scenePosition"; type: "QVector3D"; isReadonly: true }
        Property { name: "sceneRotation"; revision: 1; type: "QQuaternion"; isReadonly: true }
        Property { name: "sceneScale"; type: "QVector3D"; isReadonly: true }
        Property { name: "sceneTransform"; type: "QMatrix4x4"; isReadonly: true }
        Property { name: "staticFlags"; revision: 1; type: "int" }
        Signal { name: "rotationChanged"; revision: 1 }
        Signal { name: "eulerRotationChanged"; revision: 1 }
        Signal { name: "localOpacityChanged" }
        Method {
            name: "setX"
            Parameter { name: "x"; type: "float" }
        }
        Method {
            name: "setY"
            Parameter { name: "y"; type: "float" }
        }
        Method {
            name: "setZ"
            Parameter { name: "z"; type: "float" }
        }
        Method {
            name: "setRotation"
            revision: 1
            Parameter { name: "rotation"; type: "QQuaternion" }
        }
        Method {
            name: "setEulerRotation"
            revision: 1
            Parameter { name: "eulerRotation"; type: "QVector3D" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "QVector3D" }
        }
        Method {
            name: "setScale"
            Parameter { name: "scale"; type: "QVector3D" }
        }
        Method {
            name: "setPivot"
            Parameter { name: "pivot"; type: "QVector3D" }
        }
        Method {
            name: "setLocalOpacity"
            Parameter { name: "opacity"; type: "float" }
        }
        Method {
            name: "setVisible"
            Parameter { name: "visible"; type: "bool" }
        }
        Method {
            name: "setStaticFlags"
            Parameter { name: "staticFlags"; type: "int" }
        }
        Method {
            name: "rotate"
            Parameter { name: "degrees"; type: "double" }
            Parameter { name: "axis"; type: "QVector3D" }
            Parameter { name: "space"; type: "TransformSpace" }
        }
        Method {
            name: "mapPositionToScene"
            type: "QVector3D"
            Parameter { name: "localPosition"; type: "QVector3D" }
        }
        Method {
            name: "mapPositionFromScene"
            type: "QVector3D"
            Parameter { name: "scenePosition"; type: "QVector3D" }
        }
        Method {
            name: "mapPositionToNode"
            type: "QVector3D"
            Parameter { name: "node"; type: "QQuick3DNode"; isPointer: true }
            Parameter { name: "localPosition"; type: "QVector3D" }
        }
        Method {
            name: "mapPositionFromNode"
            type: "QVector3D"
            Parameter { name: "node"; type: "QQuick3DNode"; isPointer: true }
            Parameter { name: "localPosition"; type: "QVector3D" }
        }
        Method {
            name: "mapDirectionToScene"
            type: "QVector3D"
            Parameter { name: "localDirection"; type: "QVector3D" }
        }
        Method {
            name: "mapDirectionFromScene"
            type: "QVector3D"
            Parameter { name: "sceneDirection"; type: "QVector3D" }
        }
        Method {
            name: "mapDirectionToNode"
            type: "QVector3D"
            Parameter { name: "node"; type: "QQuick3DNode"; isPointer: true }
            Parameter { name: "localDirection"; type: "QVector3D" }
        }
        Method {
            name: "mapDirectionFromNode"
            type: "QVector3D"
            Parameter { name: "node"; type: "QQuick3DNode"; isPointer: true }
            Parameter { name: "localDirection"; type: "QVector3D" }
        }
    }
    Component {
        name: "QQuick3DObject"
        defaultProperty: "data"
        prototype: "QObject"
        exports: ["QtQuick3D/Object3D 1.14"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "parent"; type: "QQuick3DObject"; isPointer: true }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "resources"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "children"; type: "QQuick3DObject"; isList: true; isReadonly: true }
        Property { name: "states"; type: "QQuickState"; isList: true; isReadonly: true }
        Property { name: "transitions"; type: "QQuickTransition"; isList: true; isReadonly: true }
        Property { name: "state"; type: "string" }
        Method { name: "update" }
        Method {
            name: "setParentItem"
            Parameter { name: "parentItem"; type: "QQuick3DObject"; isPointer: true }
        }
    }
    Component {
        name: "QQuick3DOrthographicCamera"
        defaultProperty: "data"
        prototype: "QQuick3DCamera"
        exports: ["QtQuick3D/OrthographicCamera 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "clipNear"; type: "float" }
        Property { name: "clipFar"; type: "float" }
        Method {
            name: "setClipNear"
            Parameter { name: "clipNear"; type: "float" }
        }
        Method {
            name: "setClipFar"
            Parameter { name: "clipFar"; type: "float" }
        }
    }
    Component {
        name: "QQuick3DPerspectiveCamera"
        defaultProperty: "data"
        prototype: "QQuick3DCamera"
        exports: ["QtQuick3D/PerspectiveCamera 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "clipNear"; type: "float" }
        Property { name: "clipFar"; type: "float" }
        Property { name: "fieldOfView"; type: "float" }
        Property { name: "fieldOfViewOrientation"; type: "FieldOfViewOrientation" }
        Method {
            name: "setClipNear"
            Parameter { name: "clipNear"; type: "float" }
        }
        Method {
            name: "setClipFar"
            Parameter { name: "clipFar"; type: "float" }
        }
        Method {
            name: "setFieldOfView"
            Parameter { name: "fieldOfView"; type: "float" }
        }
        Method {
            name: "setFieldOfViewOrientation"
            Parameter { name: "fieldOfViewOrientation"; type: "FieldOfViewOrientation" }
        }
    }
    Component {
        name: "QQuick3DPointLight"
        defaultProperty: "data"
        prototype: "QQuick3DAbstractLight"
        exports: ["QtQuick3D/PointLight 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "constantFade"; type: "float" }
        Property { name: "linearFade"; type: "float" }
        Property { name: "quadraticFade"; type: "float" }
        Method {
            name: "setConstantFade"
            Parameter { name: "constantFade"; type: "float" }
        }
        Method {
            name: "setLinearFade"
            Parameter { name: "linearFade"; type: "float" }
        }
        Method {
            name: "setQuadraticFade"
            Parameter { name: "quadraticFade"; type: "float" }
        }
    }
    Component {
        name: "QQuick3DPrincipledMaterial"
        defaultProperty: "data"
        prototype: "QQuick3DMaterial"
        exports: [
            "QtQuick3D/PrincipledMaterial 1.14",
            "QtQuick3D/PrincipledMaterial 1.15"
        ]
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "Lighting"
            values: {
                "NoLighting": 0,
                "FragmentLighting": 1
            }
        }
        Enum {
            name: "BlendMode"
            values: {
                "SourceOver": 0,
                "Screen": 1,
                "Multiply": 2,
                "Overlay": 3,
                "ColorBurn": 4,
                "ColorDodge": 5
            }
        }
        Enum {
            name: "AlphaMode"
            values: {
                "Opaque": 0,
                "Mask": 1,
                "Blend": 2
            }
        }
        Property { name: "lighting"; type: "Lighting" }
        Property { name: "blendMode"; type: "BlendMode" }
        Property { name: "baseColor"; type: "QColor" }
        Property { name: "baseColorMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "metalness"; type: "float" }
        Property { name: "metalnessMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "metalnessChannel"; revision: 1; type: "TextureChannelMapping" }
        Property { name: "specularAmount"; type: "float" }
        Property { name: "specularMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "specularTint"; type: "float" }
        Property { name: "roughness"; type: "float" }
        Property { name: "roughnessMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "roughnessChannel"; revision: 1; type: "TextureChannelMapping" }
        Property { name: "indexOfRefraction"; type: "float" }
        Property { name: "emissiveColor"; type: "QColor" }
        Property { name: "emissiveMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "opacity"; type: "float" }
        Property { name: "opacityMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "opacityChannel"; revision: 1; type: "TextureChannelMapping" }
        Property { name: "normalMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "normalStrength"; type: "float" }
        Property { name: "specularReflectionMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "occlusionMap"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "occlusionChannel"; revision: 1; type: "TextureChannelMapping" }
        Property { name: "occlusionAmount"; type: "float" }
        Property { name: "alphaMode"; type: "AlphaMode" }
        Property { name: "alphaCutoff"; type: "float" }
        Signal {
            name: "lightingChanged"
            Parameter { name: "lighting"; type: "Lighting" }
        }
        Signal {
            name: "blendModeChanged"
            Parameter { name: "blendMode"; type: "BlendMode" }
        }
        Signal {
            name: "baseColorChanged"
            Parameter { name: "baseColor"; type: "QColor" }
        }
        Signal {
            name: "baseColorMapChanged"
            Parameter { name: "baseColorMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "emissiveMapChanged"
            Parameter { name: "emissiveMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "emissiveColorChanged"
            Parameter { name: "emissiveColor"; type: "QColor" }
        }
        Signal {
            name: "specularReflectionMapChanged"
            Parameter { name: "specularReflectionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "specularMapChanged"
            Parameter { name: "specularMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "specularTintChanged"
            Parameter { name: "specularTint"; type: "float" }
        }
        Signal {
            name: "indexOfRefractionChanged"
            Parameter { name: "indexOfRefraction"; type: "float" }
        }
        Signal {
            name: "specularAmountChanged"
            Parameter { name: "specularAmount"; type: "float" }
        }
        Signal {
            name: "roughnessChanged"
            Parameter { name: "roughness"; type: "float" }
        }
        Signal {
            name: "roughnessMapChanged"
            Parameter { name: "roughnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "opacityChanged"
            Parameter { name: "opacity"; type: "float" }
        }
        Signal {
            name: "opacityMapChanged"
            Parameter { name: "opacityMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "normalMapChanged"
            Parameter { name: "normalMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "metalnessChanged"
            Parameter { name: "metalness"; type: "float" }
        }
        Signal {
            name: "metalnessMapChanged"
            Parameter { name: "metalnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "normalStrengthChanged"
            Parameter { name: "normalStrength"; type: "float" }
        }
        Signal {
            name: "occlusionMapChanged"
            Parameter { name: "occlusionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "occlusionAmountChanged"
            Parameter { name: "occlusionAmount"; type: "float" }
        }
        Signal {
            name: "alphaModeChanged"
            Parameter { name: "alphaMode"; type: "AlphaMode" }
        }
        Signal {
            name: "alphaCutoffChanged"
            Parameter { name: "alphaCutoff"; type: "float" }
        }
        Signal {
            name: "metalnessChannelChanged"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
        Signal {
            name: "roughnessChannelChanged"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
        Signal {
            name: "opacityChannelChanged"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
        Signal {
            name: "occlusionChannelChanged"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
        Method {
            name: "setLighting"
            Parameter { name: "lighting"; type: "Lighting" }
        }
        Method {
            name: "setBlendMode"
            Parameter { name: "blendMode"; type: "BlendMode" }
        }
        Method {
            name: "setBaseColor"
            Parameter { name: "baseColor"; type: "QColor" }
        }
        Method {
            name: "setBaseColorMap"
            Parameter { name: "baseColorMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setEmissiveMap"
            Parameter { name: "emissiveMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setEmissiveColor"
            Parameter { name: "emissiveColor"; type: "QColor" }
        }
        Method {
            name: "setSpecularReflectionMap"
            Parameter { name: "specularReflectionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setSpecularMap"
            Parameter { name: "specularMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setSpecularTint"
            Parameter { name: "specularTint"; type: "float" }
        }
        Method {
            name: "setIndexOfRefraction"
            Parameter { name: "indexOfRefraction"; type: "float" }
        }
        Method {
            name: "setSpecularAmount"
            Parameter { name: "specularAmount"; type: "float" }
        }
        Method {
            name: "setRoughness"
            Parameter { name: "roughness"; type: "float" }
        }
        Method {
            name: "setRoughnessMap"
            Parameter { name: "roughnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setOpacity"
            Parameter { name: "opacity"; type: "float" }
        }
        Method {
            name: "setOpacityMap"
            Parameter { name: "opacityMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setNormalMap"
            Parameter { name: "normalMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setMetalness"
            Parameter { name: "metalnessAmount"; type: "float" }
        }
        Method {
            name: "setMetalnessMap"
            Parameter { name: "metalnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setNormalStrength"
            Parameter { name: "normalStrength"; type: "float" }
        }
        Method {
            name: "setOcclusionMap"
            Parameter { name: "occlusionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setOcclusionAmount"
            Parameter { name: "occlusionAmount"; type: "float" }
        }
        Method {
            name: "setAlphaMode"
            Parameter { name: "alphaMode"; type: "AlphaMode" }
        }
        Method {
            name: "setAlphaCutoff"
            Parameter { name: "alphaCutoff"; type: "float" }
        }
        Method {
            name: "setMetalnessChannel"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
        Method {
            name: "setRoughnessChannel"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
        Method {
            name: "setOpacityChannel"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
        Method {
            name: "setOcclusionChannel"
            revision: 1
            Parameter { name: "channel"; type: "TextureChannelMapping" }
        }
    }
    Component {
        name: "QQuick3DQuaternionAnimation"
        prototype: "QQuickPropertyAnimation"
        exports: ["QtQuick3D/QuaternionAnimation 1.15"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Type"
            values: {
                "Slerp": 0,
                "Nlerp": 1
            }
        }
        Property { name: "from"; type: "QQuaternion" }
        Property { name: "to"; type: "QQuaternion" }
        Property { name: "type"; type: "Type" }
        Property { name: "fromXRotation"; type: "float" }
        Property { name: "fromYRotation"; type: "float" }
        Property { name: "fromZRotation"; type: "float" }
        Property { name: "toXRotation"; type: "float" }
        Property { name: "toYRotation"; type: "float" }
        Property { name: "toZRotation"; type: "float" }
        Signal {
            name: "typeChanged"
            Parameter { name: "type"; type: "Type" }
        }
        Signal {
            name: "fromXRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "fromYRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "fromZRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "toXRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "toYRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "toZRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
    }
    Component {
        name: "QQuick3DQuaternionUtils"
        prototype: "QObject"
        exports: ["QtQuick3D/Quaternion 1.15"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [0]
        Method {
            name: "fromAxesAndAngles"
            type: "QQuaternion"
            Parameter { name: "axis1"; type: "QVector3D" }
            Parameter { name: "angle1"; type: "float" }
            Parameter { name: "axis2"; type: "QVector3D" }
            Parameter { name: "angle2"; type: "float" }
            Parameter { name: "axis3"; type: "QVector3D" }
            Parameter { name: "angle3"; type: "float" }
        }
        Method {
            name: "fromAxesAndAngles"
            type: "QQuaternion"
            Parameter { name: "axis1"; type: "QVector3D" }
            Parameter { name: "angle1"; type: "float" }
            Parameter { name: "axis2"; type: "QVector3D" }
            Parameter { name: "angle2"; type: "float" }
        }
        Method {
            name: "fromAxisAndAngle"
            type: "QQuaternion"
            Parameter { name: "x"; type: "float" }
            Parameter { name: "y"; type: "float" }
            Parameter { name: "z"; type: "float" }
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "fromAxisAndAngle"
            type: "QQuaternion"
            Parameter { name: "axis"; type: "QVector3D" }
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "fromEulerAngles"
            type: "QQuaternion"
            Parameter { name: "x"; type: "float" }
            Parameter { name: "y"; type: "float" }
            Parameter { name: "z"; type: "float" }
        }
        Method {
            name: "fromEulerAngles"
            type: "QQuaternion"
            Parameter { name: "eulerAngles"; type: "QVector3D" }
        }
        Method {
            name: "lookAt"
            revision: 1
            type: "QQuaternion"
            Parameter { name: "sourcePosition"; type: "QVector3D" }
            Parameter { name: "sourceDirection"; type: "QVector3D" }
            Parameter { name: "targetPosition"; type: "QVector3D" }
            Parameter { name: "upDirection"; type: "QVector3D" }
        }
        Method {
            name: "lookAt"
            revision: 1
            type: "QQuaternion"
            Parameter { name: "sourcePosition"; type: "QVector3D" }
            Parameter { name: "sourceDirection"; type: "QVector3D" }
            Parameter { name: "targetPosition"; type: "QVector3D" }
        }
    }
    Component {
        name: "QQuick3DRepeater"
        defaultProperty: "delegate"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D/Repeater3D 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "model"; type: "QVariant" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "count"; type: "int"; isReadonly: true }
        Signal {
            name: "objectAdded"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QQuick3DObject"; isPointer: true }
        }
        Signal {
            name: "objectRemoved"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QQuick3DObject"; isPointer: true }
        }
        Method {
            name: "objectAt"
            type: "QQuick3DObject*"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        name: "QQuick3DSceneEnvironment"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: [
            "QtQuick3D/SceneEnvironment 1.14",
            "QtQuick3D/SceneEnvironment 1.15"
        ]
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "QQuick3DEnvironmentAAModeValues"
            values: {
                "NoAA": 0,
                "SSAA": 1,
                "MSAA": 2,
                "ProgressiveAA": 3
            }
        }
        Enum {
            name: "QQuick3DEnvironmentAAQualityValues"
            values: {
                "Medium": 2,
                "High": 4,
                "VeryHigh": 8
            }
        }
        Enum {
            name: "QQuick3DEnvironmentBackgroundTypes"
            values: {
                "Transparent": 0,
                "Unspecified": 1,
                "Color": 2,
                "SkyBox": 3
            }
        }
        Property { name: "antialiasingMode"; type: "QQuick3DEnvironmentAAModeValues" }
        Property { name: "antialiasingQuality"; type: "QQuick3DEnvironmentAAQualityValues" }
        Property { name: "temporalAAEnabled"; type: "bool" }
        Property { name: "temporalAAStrength"; type: "float" }
        Property { name: "backgroundMode"; type: "QQuick3DEnvironmentBackgroundTypes" }
        Property { name: "clearColor"; type: "QColor" }
        Property { name: "depthTestEnabled"; type: "bool" }
        Property { name: "depthPrePassEnabled"; type: "bool" }
        Property { name: "aoStrength"; type: "float" }
        Property { name: "aoDistance"; type: "float" }
        Property { name: "aoSoftness"; type: "float" }
        Property { name: "aoDither"; type: "bool" }
        Property { name: "aoSampleRate"; type: "int" }
        Property { name: "aoBias"; type: "float" }
        Property { name: "lightProbe"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "probeBrightness"; type: "float" }
        Property { name: "fastImageBasedLightingEnabled"; type: "bool" }
        Property { name: "probeHorizon"; type: "float" }
        Property { name: "probeFieldOfView"; type: "float" }
        Property { name: "effects"; revision: 1; type: "QQuick3DEffect"; isList: true; isReadonly: true }
        Method {
            name: "setAntialiasingMode"
            Parameter { name: "antialiasingMode"; type: "QQuick3DEnvironmentAAModeValues" }
        }
        Method {
            name: "setAntialiasingQuality"
            Parameter { name: "antialiasingQuality"; type: "QQuick3DEnvironmentAAQualityValues" }
        }
        Method {
            name: "setTemporalAAEnabled"
            Parameter { name: "temporalAAEnabled"; type: "bool" }
        }
        Method {
            name: "setTemporalAAStrength"
            Parameter { name: "strength"; type: "float" }
        }
        Method {
            name: "setBackgroundMode"
            Parameter { name: "backgroundMode"; type: "QQuick3DEnvironmentBackgroundTypes" }
        }
        Method {
            name: "setClearColor"
            Parameter { name: "clearColor"; type: "QColor" }
        }
        Method {
            name: "setAoStrength"
            Parameter { name: "aoStrength"; type: "float" }
        }
        Method {
            name: "setAoDistance"
            Parameter { name: "aoDistance"; type: "float" }
        }
        Method {
            name: "setAoSoftness"
            Parameter { name: "aoSoftness"; type: "float" }
        }
        Method {
            name: "setAoDither"
            Parameter { name: "aoDither"; type: "bool" }
        }
        Method {
            name: "setAoSampleRate"
            Parameter { name: "aoSampleRate"; type: "int" }
        }
        Method {
            name: "setAoBias"
            Parameter { name: "aoBias"; type: "float" }
        }
        Method {
            name: "setLightProbe"
            Parameter { name: "lightProbe"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setProbeBrightness"
            Parameter { name: "probeBrightness"; type: "float" }
        }
        Method {
            name: "setFastImageBasedLightingEnabled"
            Parameter { name: "fastImageBasedLightingEnabled"; type: "bool" }
        }
        Method {
            name: "setProbeHorizon"
            Parameter { name: "probeHorizon"; type: "float" }
        }
        Method {
            name: "setProbeFieldOfView"
            Parameter { name: "probeFieldOfView"; type: "float" }
        }
        Method {
            name: "setDepthTestEnabled"
            Parameter { name: "depthTestEnabled"; type: "bool" }
        }
        Method {
            name: "setDepthPrePassEnabled"
            Parameter { name: "depthPrePassEnabled"; type: "bool" }
        }
    }
    Component {
        name: "QQuick3DShaderApplyDepthValue"
        prototype: "QQuick3DShaderUtilsRenderCommand"
        exports: ["QtQuick3D/DepthInput 1.15"]
        exportMetaObjectRevisions: [0]
        Property { name: "param"; type: "QByteArray" }
    }
    Component {
        name: "QQuick3DShaderUtilsApplyValue"
        prototype: "QQuick3DShaderUtilsRenderCommand"
        exports: ["QtQuick3D/SetUniformValue 1.15"]
        exportMetaObjectRevisions: [0]
        Property { name: "target"; type: "QByteArray" }
        Property { name: "value"; type: "QVariant" }
    }
    Component {
        name: "QQuick3DShaderUtilsBlending"
        prototype: "QQuick3DShaderUtilsRenderCommand"
        exports: ["QtQuick3D/Blending 1.14"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "SrcBlending"
            values: {
                "Unknown": 0,
                "Zero": 1,
                "One": 2,
                "SrcColor": 3,
                "OneMinusSrcColor": 4,
                "DstColor": 5,
                "OneMinusDstColor": 6,
                "SrcAlpha": 7,
                "OneMinusSrcAlpha": 8,
                "DstAlpha": 9,
                "OneMinusDstAlpha": 10,
                "ConstantColor": 11,
                "OneMinusConstantColor": 12,
                "ConstantAlpha": 13,
                "OneMinusConstantAlpha": 14,
                "SrcAlphaSaturate": 15
            }
        }
        Enum {
            name: "DestBlending"
            values: {
                "Unknown": 0,
                "Zero": 1,
                "One": 2,
                "SrcColor": 3,
                "OneMinusSrcColor": 4,
                "DstColor": 5,
                "OneMinusDstColor": 6,
                "SrcAlpha": 7,
                "OneMinusSrcAlpha": 8,
                "DstAlpha": 9,
                "OneMinusDstAlpha": 10,
                "ConstantColor": 11,
                "OneMinusConstantColor": 12,
                "ConstantAlpha": 13,
                "OneMinusConstantAlpha": 14
            }
        }
        Property { name: "srcBlending"; type: "SrcBlending" }
        Property { name: "destBlending"; type: "DestBlending" }
        Method {
            name: "setDestBlending"
            Parameter { name: "destBlending"; type: "DestBlending" }
        }
        Method {
            name: "setSrcBlending"
            Parameter { name: "srcBlending"; type: "SrcBlending" }
        }
    }
    Component {
        name: "QQuick3DShaderUtilsBuffer"
        prototype: "QObject"
        exports: ["QtQuick3D/Buffer 1.14"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "TextureFilterOperation"
            values: {
                "Unknown": 0,
                "Nearest": 1,
                "Linear": 2
            }
        }
        Enum {
            name: "TextureCoordOperation"
            values: {
                "Unknown": 0,
                "ClampToEdge": 1,
                "MirroredRepeat": 2,
                "Repeat": 3
            }
        }
        Enum {
            name: "AllocateBufferFlagValues"
            values: {
                "None": 0,
                "SceneLifetime": 1
            }
        }
        Enum {
            name: "TextureFormat"
            values: {
                "Unknown": 0,
                "R8": 1,
                "R16": 2,
                "R16F": 3,
                "R32I": 4,
                "R32UI": 5,
                "R32F": 6,
                "RG8": 7,
                "RGBA8": 8,
                "RGB8": 9,
                "SRGB8": 10,
                "SRGB8A8": 11,
                "RGB565": 12,
                "RGBA16F": 13,
                "RG16F": 14,
                "RG32F": 15,
                "RGB32F": 16,
                "RGBA32F": 17,
                "R11G11B10": 18,
                "RGB9E5": 19,
                "Depth16": 20,
                "Depth24": 21,
                "Depth32": 22,
                "Depth24Stencil8": 23
            }
        }
        Property { name: "format"; type: "TextureFormat" }
        Property { name: "textureFilterOperation"; type: "TextureFilterOperation" }
        Property { name: "textureCoordOperation"; type: "TextureCoordOperation" }
        Property { name: "sizeMultiplier"; type: "float" }
        Property { name: "bufferFlags"; type: "AllocateBufferFlagValues" }
        Property { name: "name"; type: "QByteArray" }
    }
    Component {
        name: "QQuick3DShaderUtilsBufferBlit"
        prototype: "QQuick3DShaderUtilsRenderCommand"
        exports: ["QtQuick3D/BufferBlit 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "source"; type: "QQuick3DShaderUtilsBuffer"; isPointer: true }
        Property { name: "destination"; type: "QQuick3DShaderUtilsBuffer"; isPointer: true }
    }
    Component {
        name: "QQuick3DShaderUtilsBufferInput"
        prototype: "QQuick3DShaderUtilsRenderCommand"
        exports: ["QtQuick3D/BufferInput 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "buffer"; type: "QQuick3DShaderUtilsBuffer"; isPointer: true }
        Property { name: "param"; type: "QByteArray" }
    }
    Component {
        name: "QQuick3DShaderUtilsCullMode"
        prototype: "QQuick3DShaderUtilsRenderCommand"
        exports: ["QtQuick3D/CullMode 1.15"]
        exportMetaObjectRevisions: [0]
        Property { name: "cullMode"; type: "QQuick3DMaterial::CullMode" }
        Method {
            name: "setCullMode"
            Parameter { name: "cullMode"; type: "QQuick3DMaterial::CullMode" }
        }
    }
    Component {
        name: "QQuick3DShaderUtilsRenderCommand"
        prototype: "QObject"
        exports: ["QtQuick3D/Command 1.14"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QQuick3DShaderUtilsRenderPass"
        prototype: "QObject"
        exports: ["QtQuick3D/Pass 1.14"]
        exportMetaObjectRevisions: [0]
        Property {
            name: "commands"
            type: "QQuick3DShaderUtilsRenderCommand"
            isList: true
            isReadonly: true
        }
        Property { name: "output"; type: "QQuick3DShaderUtilsBuffer"; isPointer: true }
        Property { name: "shaders"; type: "QQuick3DShaderUtilsShader"; isList: true; isReadonly: true }
    }
    Component {
        name: "QQuick3DShaderUtilsRenderState"
        prototype: "QQuick3DShaderUtilsRenderCommand"
        exports: ["QtQuick3D/RenderState 1.14"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "RenderState"
            values: {
                "Unknown": 0,
                "Blend": 1,
                "CullFace": 2,
                "DepthTest": 3,
                "StencilTest": 4,
                "ScissorTest": 5,
                "DepthWrite": 6,
                "Multisample": 7
            }
        }
        Property { name: "renderState"; type: "RenderState" }
        Property { name: "enabled"; type: "bool" }
        Method {
            name: "setRenderState"
            Parameter { name: "renderState"; type: "RenderState" }
        }
    }
    Component {
        name: "QQuick3DShaderUtilsShader"
        prototype: "QObject"
        exports: ["QtQuick3D/Shader 1.14"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Stage"
            values: {
                "Shared": 0,
                "Vertex": 1,
                "Fragment": 2,
                "Geometry": 3,
                "Compute": 4
            }
        }
        Property { name: "shader"; type: "QByteArray" }
        Property { name: "stage"; type: "Stage" }
    }
    Component {
        name: "QQuick3DShaderUtilsShaderInfo"
        prototype: "QObject"
        exports: ["QtQuick3D/ShaderInfo 1.14"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "MaterialShaderKeyValues"
            values: {
                "Diffuse": 1,
                "Specular": 2,
                "Cutout": 4,
                "Refraction": 8,
                "Transparent": 16,
                "Displace": 32,
                "Transmissive": 64,
                "Glossy": 3
            }
        }
        Property { name: "version"; type: "QByteArray" }
        Property { name: "type"; type: "QByteArray" }
        Property { name: "shaderKey"; type: "int" }
    }
    Component {
        name: "QQuick3DShaderUtilsTextureInput"
        prototype: "QObject"
        exports: ["QtQuick3D/TextureInput 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "texture"; type: "QQuick3DTexture"; isPointer: true }
        Property { name: "enabled"; type: "bool" }
        Signal {
            name: "textureDirty"
            Parameter { name: "texture"; type: "QQuick3DShaderUtilsTextureInput"; isPointer: true }
        }
        Method {
            name: "setTexture"
            Parameter { name: "texture"; type: "QQuick3DTexture"; isPointer: true }
        }
    }
    Component {
        name: "QQuick3DSpotLight"
        defaultProperty: "data"
        prototype: "QQuick3DAbstractLight"
        exports: ["QtQuick3D/SpotLight 1.15"]
        exportMetaObjectRevisions: [0]
        Property { name: "constantFade"; type: "float" }
        Property { name: "linearFade"; type: "float" }
        Property { name: "quadraticFade"; type: "float" }
        Property { name: "coneAngle"; type: "float" }
        Property { name: "innerConeAngle"; type: "float" }
        Method {
            name: "setConstantFade"
            Parameter { name: "constantFade"; type: "float" }
        }
        Method {
            name: "setLinearFade"
            Parameter { name: "linearFade"; type: "float" }
        }
        Method {
            name: "setQuadraticFade"
            Parameter { name: "quadraticFade"; type: "float" }
        }
        Method {
            name: "setConeAngle"
            Parameter { name: "coneAngle"; type: "float" }
        }
        Method {
            name: "setInnerConeAngle"
            Parameter { name: "innerConeAngle"; type: "float" }
        }
    }
    Component {
        name: "QQuick3DTexture"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/Texture 1.14"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "MappingMode"
            values: {
                "UV": 0,
                "Environment": 1,
                "LightProbe": 2
            }
        }
        Enum {
            name: "TilingMode"
            values: {
                "ClampToEdge": 1,
                "MirroredRepeat": 2,
                "Repeat": 3
            }
        }
        Enum {
            name: "Format"
            values: {
                "Automatic": 0,
                "R8": 1,
                "R16": 2,
                "R16F": 3,
                "R32I": 4,
                "R32UI": 5,
                "R32F": 6,
                "RG8": 7,
                "RGBA8": 8,
                "RGB8": 9,
                "SRGB8": 10,
                "SRGB8A8": 11,
                "RGB565": 12,
                "RGBA5551": 13,
                "Alpha8": 14,
                "Luminance8": 15,
                "Luminance16": 16,
                "LuminanceAlpha8": 17,
                "RGBA16F": 18,
                "RG16F": 19,
                "RG32F": 20,
                "RGB32F": 21,
                "RGBA32F": 22,
                "R11G11B10": 23,
                "RGB9E5": 24,
                "RGBA_DXT1": 25,
                "RGB_DXT1": 26,
                "RGBA_DXT3": 27,
                "RGBA_DXT5": 28,
                "Depth16": 29,
                "Depth24": 30,
                "Depth32": 31,
                "Depth24Stencil8": 32
            }
        }
        Property { name: "source"; type: "QUrl" }
        Property { name: "sourceItem"; type: "QQuickItem"; isPointer: true }
        Property { name: "scaleU"; type: "float" }
        Property { name: "scaleV"; type: "float" }
        Property { name: "mappingMode"; type: "MappingMode" }
        Property { name: "tilingModeHorizontal"; type: "TilingMode" }
        Property { name: "tilingModeVertical"; type: "TilingMode" }
        Property { name: "rotationUV"; type: "float" }
        Property { name: "positionU"; type: "float" }
        Property { name: "positionV"; type: "float" }
        Property { name: "pivotU"; type: "float" }
        Property { name: "pivotV"; type: "float" }
        Property { name: "flipV"; type: "bool" }
        Property { name: "format"; type: "Format" }
        Signal { name: "horizontalTilingChanged" }
        Signal { name: "verticalTilingChanged" }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setSourceItem"
            Parameter { name: "sourceItem"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "setScaleU"
            Parameter { name: "scaleU"; type: "float" }
        }
        Method {
            name: "setScaleV"
            Parameter { name: "scaleV"; type: "float" }
        }
        Method {
            name: "setMappingMode"
            Parameter { name: "mappingMode"; type: "MappingMode" }
        }
        Method {
            name: "setHorizontalTiling"
            Parameter { name: "tilingModeHorizontal"; type: "TilingMode" }
        }
        Method {
            name: "setVerticalTiling"
            Parameter { name: "tilingModeVertical"; type: "TilingMode" }
        }
        Method {
            name: "setRotationUV"
            Parameter { name: "rotationUV"; type: "float" }
        }
        Method {
            name: "setPositionU"
            Parameter { name: "positionU"; type: "float" }
        }
        Method {
            name: "setPositionV"
            Parameter { name: "positionV"; type: "float" }
        }
        Method {
            name: "setPivotU"
            Parameter { name: "pivotU"; type: "float" }
        }
        Method {
            name: "setPivotV"
            Parameter { name: "pivotV"; type: "float" }
        }
        Method {
            name: "setFlipV"
            Parameter { name: "flipV"; type: "bool" }
        }
        Method {
            name: "setFormat"
            Parameter { name: "format"; type: "Format" }
        }
    }
    Component {
        name: "QQuick3DViewport"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtQuick3D/View3D 1.14"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "RenderMode"
            values: {
                "Offscreen": 0,
                "Underlay": 1,
                "Overlay": 2,
                "Inline": 3
            }
        }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "camera"; type: "QQuick3DCamera"; isPointer: true }
        Property { name: "environment"; type: "QQuick3DSceneEnvironment"; isPointer: true }
        Property { name: "scene"; type: "QQuick3DNode"; isReadonly: true; isPointer: true }
        Property { name: "importScene"; type: "QQuick3DNode"; isPointer: true }
        Property { name: "renderMode"; type: "RenderMode" }
        Property { name: "renderStats"; type: "QQuick3DRenderStats"; isReadonly: true; isPointer: true }
        Method {
            name: "setCamera"
            Parameter { name: "camera"; type: "QQuick3DCamera"; isPointer: true }
        }
        Method {
            name: "setEnvironment"
            Parameter { name: "environment"; type: "QQuick3DSceneEnvironment"; isPointer: true }
        }
        Method {
            name: "setImportScene"
            Parameter { name: "inScene"; type: "QQuick3DNode"; isPointer: true }
        }
        Method {
            name: "setRenderMode"
            Parameter { name: "renderMode"; type: "RenderMode" }
        }
        Method {
            name: "mapFrom3DScene"
            type: "QVector3D"
            Parameter { name: "scenePos"; type: "QVector3D" }
        }
        Method {
            name: "mapTo3DScene"
            type: "QVector3D"
            Parameter { name: "viewPos"; type: "QVector3D" }
        }
        Method {
            name: "pick"
            type: "QQuick3DPickResult"
            Parameter { name: "x"; type: "float" }
            Parameter { name: "y"; type: "float" }
        }
    }
}
