# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
`wx.aui` provides a set of classes for implementing an "Advanced User Interface".
More specifically, these classes enable to you present some of your application in
floating or dockable panels, notebooks with floatable tabs, etc.

There is also a pure-python implementation of these classes available in the
`wx.lib.agw.aui` package.
"""
#-- begin-_aui --#

import wx
#-- end-_aui --#
#-- begin-auiframemanager --#

class _AuiManagerDock(IntEnum):
    AUI_DOCK_NONE = auto()
    AUI_DOCK_TOP = auto()
    AUI_DOCK_RIGHT = auto()
    AUI_DOCK_BOTTOM = auto()
    AUI_DOCK_LEFT = auto()
    AUI_DOCK_CENTER = auto()
    AUI_DOCK_CENTRE = auto()
AuiManagerDock: TypeAlias = Union[_AuiManagerDock, int]
AUI_DOCK_NONE = _AuiManagerDock.AUI_DOCK_NONE
AUI_DOCK_TOP = _AuiManagerDock.AUI_DOCK_TOP
AUI_DOCK_RIGHT = _AuiManagerDock.AUI_DOCK_RIGHT
AUI_DOCK_BOTTOM = _AuiManagerDock.AUI_DOCK_BOTTOM
AUI_DOCK_LEFT = _AuiManagerDock.AUI_DOCK_LEFT
AUI_DOCK_CENTER = _AuiManagerDock.AUI_DOCK_CENTER
AUI_DOCK_CENTRE = _AuiManagerDock.AUI_DOCK_CENTRE

class _AuiManagerOption(IntEnum):
    AUI_MGR_ALLOW_FLOATING = auto()
    AUI_MGR_ALLOW_ACTIVE_PANE = auto()
    AUI_MGR_TRANSPARENT_DRAG = auto()
    AUI_MGR_TRANSPARENT_HINT = auto()
    AUI_MGR_VENETIAN_BLINDS_HINT = auto()
    AUI_MGR_RECTANGLE_HINT = auto()
    AUI_MGR_HINT_FADE = auto()
    AUI_MGR_NO_VENETIAN_BLINDS_FADE = auto()
    AUI_MGR_LIVE_RESIZE = auto()
    AUI_MGR_DEFAULT = auto()
AuiManagerOption: TypeAlias = Union[_AuiManagerOption, int]
AUI_MGR_ALLOW_FLOATING = _AuiManagerOption.AUI_MGR_ALLOW_FLOATING
AUI_MGR_ALLOW_ACTIVE_PANE = _AuiManagerOption.AUI_MGR_ALLOW_ACTIVE_PANE
AUI_MGR_TRANSPARENT_DRAG = _AuiManagerOption.AUI_MGR_TRANSPARENT_DRAG
AUI_MGR_TRANSPARENT_HINT = _AuiManagerOption.AUI_MGR_TRANSPARENT_HINT
AUI_MGR_VENETIAN_BLINDS_HINT = _AuiManagerOption.AUI_MGR_VENETIAN_BLINDS_HINT
AUI_MGR_RECTANGLE_HINT = _AuiManagerOption.AUI_MGR_RECTANGLE_HINT
AUI_MGR_HINT_FADE = _AuiManagerOption.AUI_MGR_HINT_FADE
AUI_MGR_NO_VENETIAN_BLINDS_FADE = _AuiManagerOption.AUI_MGR_NO_VENETIAN_BLINDS_FADE
AUI_MGR_LIVE_RESIZE = _AuiManagerOption.AUI_MGR_LIVE_RESIZE
AUI_MGR_DEFAULT = _AuiManagerOption.AUI_MGR_DEFAULT
wxEVT_AUI_PANE_BUTTON: int
wxEVT_AUI_PANE_CLOSE: int
wxEVT_AUI_PANE_MAXIMIZE: int
wxEVT_AUI_PANE_RESTORE: int
wxEVT_AUI_PANE_ACTIVATED: int
wxEVT_AUI_RENDER: int
wxEVT_AUI_FIND_MANAGER: int

class AuiManager(wx.EvtHandler):
    """
    AuiManager(managed_wnd=None, flags=AUI_MGR_DEFAULT) -> None
    
    wxAuiManager is the central class of the wxAUI class framework.
    """

    def __init__(self, managed_wnd: Optional[wx.Window]=None, flags: int=AUI_MGR_DEFAULT) -> None:
        """
        AuiManager(managed_wnd=None, flags=AUI_MGR_DEFAULT) -> None
        
        wxAuiManager is the central class of the wxAUI class framework.
        """

    @overload
    def AddPane(self, window: wx.Window, direction: int=wx.LEFT, caption: str='') -> bool:
        ...

    @overload
    def AddPane(self, window: wx.Window, pane_info: AuiPaneInfo, drop_pos: wx.Point) -> bool:
        ...

    @overload
    def AddPane(self, window: wx.Window, pane_info: AuiPaneInfo) -> bool:
        """
        AddPane(window, pane_info) -> bool
        AddPane(window, direction=wx.LEFT, caption='') -> bool
        AddPane(window, pane_info, drop_pos) -> bool
        
        AddPane() tells the frame manager to start managing a child window.
        """

    def CalculateHintRect(self, paneWindow: wx.Window, pt: wx.Point, offset: wx.Point) -> wx.Rect:
        """
        CalculateHintRect(paneWindow, pt, offset) -> wx.Rect
        
        This function is used by controls to calculate the drop hint
        rectangle.
        """

    def CanDockPanel(self, p: AuiPaneInfo) -> bool:
        """
        CanDockPanel(p) -> bool
        
        Check if a key modifier is pressed (actually WXK_CONTROL or WXK_ALT)
        while dragging the frame to not dock the window.
        """

    def ClosePane(self, paneInfo: AuiPaneInfo) -> None:
        """
        ClosePane(paneInfo) -> None
        
        Destroys or hides the given pane depending on its flags.
        """

    def CreateFloatingFrame(self, parent: wx.Window, p: AuiPaneInfo) -> AuiFloatingFrame:
        """
        CreateFloatingFrame(parent, p) -> AuiFloatingFrame
        
        Creates a floating frame in this wxAuiManager with the given parent
        and wxAuiPaneInfo.
        """

    def DetachPane(self, window: wx.Window) -> bool:
        """
        DetachPane(window) -> bool
        
        Tells the wxAuiManager to stop managing the pane specified by window.
        """

    def DrawHintRect(self, paneWindow: wx.Window, pt: wx.Point, offset: wx.Point) -> None:
        """
        DrawHintRect(paneWindow, pt, offset) -> None
        
        This function is used by controls to draw the hint window.
        """

    def GetAllPanes(self) -> AuiPaneInfoArray:
        """
        GetAllPanes() -> AuiPaneInfoArray
        
        Returns an array of all panes managed by the frame manager.
        """

    def GetArtProvider(self) -> AuiDockArt:
        """
        GetArtProvider() -> AuiDockArt
        
        Returns the current art provider being used.
        """

    def GetDockSizeConstraint(self, widthpct: float, heightpct: float) -> None:
        """
        GetDockSizeConstraint(widthpct, heightpct) -> None
        
        Returns the current dock constraint values.
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        
        Returns the current wxAuiManagerOption's flags.
        """

    def GetManagedWindow(self) -> wx.Window:
        """
        GetManagedWindow() -> wx.Window
        
        Returns the frame currently being managed by wxAuiManager.
        """

    @overload
    def GetPane(self, name: str) -> AuiPaneInfo:
        ...

    @overload
    def GetPane(self, window: wx.Window) -> AuiPaneInfo:
        """
        GetPane(window) -> AuiPaneInfo
        GetPane(name) -> AuiPaneInfo
        
        GetPane() is used to lookup a wxAuiPaneInfo object either by window pointer or by pane name, which acts as a unique id for a window pane.
        """

    def HasLiveResize(self) -> bool:
        """
        HasLiveResize() -> bool
        
        Returns true if windows are resized live.
        """

    def HideHint(self) -> None:
        """
        HideHint() -> None
        
        HideHint() hides any docking hint that may be visible.
        """

    def InsertPane(self, window: wx.Window, insert_location: AuiPaneInfo, insert_level: int=AUI_INSERT_PANE) -> bool:
        """
        InsertPane(window, insert_location, insert_level=AUI_INSERT_PANE) -> bool
        
        This method is used to insert either a previously unmanaged pane
        window into the frame manager, or to insert a currently managed pane
        somewhere else.
        """

    def LoadPaneInfo(self, pane_part: str, pane: AuiPaneInfo) -> None:
        """
        LoadPaneInfo(pane_part, pane) -> None
        
        LoadPaneInfo() is similar to LoadPerspective, with the exception that it only loads information about a single pane.
        """

    def LoadPerspective(self, perspective: str, update: bool=True) -> bool:
        """
        LoadPerspective(perspective, update=True) -> bool
        
        Loads a saved perspective.
        """

    def MaximizePane(self, paneInfo: AuiPaneInfo) -> None:
        """
        MaximizePane(paneInfo) -> None
        
        Maximize the given pane.
        """

    def RestorePane(self, paneInfo: AuiPaneInfo) -> None:
        """
        RestorePane(paneInfo) -> None
        
        Restore the last state of the given pane.
        """

    def RestoreMaximizedPane(self) -> None:
        """
        RestoreMaximizedPane() -> None
        
        Restore the previously maximized pane.
        """

    def SavePaneInfo(self, pane: AuiPaneInfo) -> str:
        """
        SavePaneInfo(pane) -> str
        
        SavePaneInfo() is similar to SavePerspective, with the exception that it only saves information about a single pane.
        """

    def SavePerspective(self) -> str:
        """
        SavePerspective() -> str
        
        Saves the entire user interface layout into an encoded wxString, which
        can then be stored by the application (probably using wxConfig).
        """

    def SetArtProvider(self, art_provider: AuiDockArt) -> None:
        """
        SetArtProvider(art_provider) -> None
        
        Instructs wxAuiManager to use art provider specified by parameter
        art_provider for all drawing calls.
        """

    def SetDockSizeConstraint(self, widthpct: float, heightpct: float) -> None:
        """
        SetDockSizeConstraint(widthpct, heightpct) -> None
        
        When a user creates a new dock by dragging a window into a docked
        position, often times the large size of the window will create a dock
        that is unwieldy large.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        This method is used to specify wxAuiManagerOption's flags.
        """

    def SetManagedWindow(self, managed_wnd: wx.Window) -> None:
        """
        SetManagedWindow(managed_wnd) -> None
        
        Called to specify the frame or window which is to be managed by
        wxAuiManager.
        """

    def ShowHint(self, rect: wx.Rect) -> None:
        """
        ShowHint(rect) -> None
        
        This function is used by controls to explicitly show a hint window at
        the specified rectangle.
        """

    def StartPaneDrag(self, paneWindow: wx.Window, offset: wx.Point) -> None:
        """
        StartPaneDrag(paneWindow, offset) -> None
        
        Mostly used internally to define the drag action parameters.
        """

    def UnInit(self) -> None:
        """
        UnInit() -> None
        
        Dissociate the managed window from the manager.
        """

    def Update(self) -> None:
        """
        Update() -> None
        
        This method is called after any number of changes are made to any of
        the managed panes.
        """

    @staticmethod
    def AlwaysUsesLiveResize() -> bool:
        """
        AlwaysUsesLiveResize() -> bool
        
        Returns true if live resize is always used on the current platform.
        """

    @staticmethod
    def GetManager(window: wx.Window) -> AuiManager:
        """
        GetManager(window) -> AuiManager
        
        Calling this method will return the wxAuiManager for a given window.
        """
    @property
    def AllPanes(self) -> AuiPaneInfoArray: ...
    @property
    def ArtProvider(self) -> AuiDockArt: ...
    @ArtProvider.setter
    def ArtProvider(self, value: AuiDockArt, /) -> None: ...
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def ManagedWindow(self) -> wx.Window: ...
    @ManagedWindow.setter
    def ManagedWindow(self, value: wx.Window, /) -> None: ...

    def ProcessDockResult(self, target: AuiPaneInfo, new_pos: AuiPaneInfo) -> bool:
        """
        ProcessDockResult(target, new_pos) -> bool
        
        ProcessDockResult() is a protected member of the wxAUI layout manager.
        """
# end of class AuiManager


class AuiPaneInfo:
    """
    AuiPaneInfo() -> None
    AuiPaneInfo(c) -> None
    
    wxAuiPaneInfo is part of the wxAUI class framework.
    """

    @overload
    def __init__(self, c: AuiPaneInfo) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        AuiPaneInfo() -> None
        AuiPaneInfo(c) -> None
        
        wxAuiPaneInfo is part of the wxAUI class framework.
        """
    name: str
    caption: str
    icon: wx.BitmapBundle
    window: wx.Window
    frame: wx.Frame
    state: int
    dock_direction: int
    dock_layer: int
    dock_row: int
    dock_pos: int
    best_size: wx.Size
    min_size: wx.Size
    max_size: wx.Size
    floating_pos: wx.Point
    floating_size: wx.Size
    dock_proportion: int
    rect: wx.Rect

    @overload
    def BestSize(self, x: int, y: int) -> AuiPaneInfo:
        ...

    @overload
    def BestSize(self, size: wx.Size) -> AuiPaneInfo:
        """
        BestSize(size) -> AuiPaneInfo
        BestSize(x, y) -> AuiPaneInfo
        
        BestSize() sets the ideal size for the pane.
        """

    def Bottom(self) -> AuiPaneInfo:
        """
        Bottom() -> AuiPaneInfo
        
        Bottom() sets the pane dock position to the bottom side of the frame.
        """

    def BottomDockable(self, b: bool=True) -> AuiPaneInfo:
        """
        BottomDockable(b=True) -> AuiPaneInfo
        
        BottomDockable() indicates whether a pane can be docked at the bottom of the frame.
        """

    def Caption(self, c: str) -> AuiPaneInfo:
        """
        Caption(c) -> AuiPaneInfo
        
        Caption() sets the caption of the pane.
        """

    def CaptionVisible(self, visible: bool=True) -> AuiPaneInfo:
        """
        CaptionVisible(visible=True) -> AuiPaneInfo
        
        CaptionVisible indicates that a pane caption should be visible.
        """

    def Centre(self) -> AuiPaneInfo:
        """
        Centre() -> AuiPaneInfo
        
        Center() sets the pane dock position to the left side of the frame.
        """

    def Center(self) -> AuiPaneInfo:
        """
        Center() -> AuiPaneInfo
        """

    def CentrePane(self) -> AuiPaneInfo:
        """
        CentrePane() -> AuiPaneInfo
        
        CentrePane() specifies that the pane should adopt the default center pane settings.
        """

    def CenterPane(self) -> AuiPaneInfo:
        """
        CenterPane() -> AuiPaneInfo
        """

    def CloseButton(self, visible: bool=True) -> AuiPaneInfo:
        """
        CloseButton(visible=True) -> AuiPaneInfo
        
        CloseButton() indicates that a close button should be drawn for the pane.
        """

    def DefaultPane(self) -> AuiPaneInfo:
        """
        DefaultPane() -> AuiPaneInfo
        
        DefaultPane() specifies that the pane should adopt the default pane settings.
        """

    def DestroyOnClose(self, b: bool=True) -> AuiPaneInfo:
        """
        DestroyOnClose(b=True) -> AuiPaneInfo
        
        DestroyOnClose() indicates whether a pane should be destroyed when it is closed.
        """

    def Direction(self, direction: int) -> AuiPaneInfo:
        """
        Direction(direction) -> AuiPaneInfo
        
        Direction() determines the direction of the docked pane.
        """

    def Dock(self) -> AuiPaneInfo:
        """
        Dock() -> AuiPaneInfo
        
        Dock() indicates that a pane should be docked.
        """

    def DockFixed(self, b: bool=True) -> AuiPaneInfo:
        """
        DockFixed(b=True) -> AuiPaneInfo
        
        DockFixed() causes the containing dock to have no resize sash.
        """

    def Dockable(self, b: bool=True) -> AuiPaneInfo:
        """
        Dockable(b=True) -> AuiPaneInfo
        
        Dockable() specifies whether a frame can be docked or not.
        """

    def Fixed(self) -> AuiPaneInfo:
        """
        Fixed() -> AuiPaneInfo
        
        Fixed() forces a pane to be fixed size so that it cannot be resized.
        """

    def Float(self) -> AuiPaneInfo:
        """
        Float() -> AuiPaneInfo
        
        Float() indicates that a pane should be floated.
        """

    def Floatable(self, b: bool=True) -> AuiPaneInfo:
        """
        Floatable(b=True) -> AuiPaneInfo
        
        Floatable() sets whether the user will be able to undock a pane and turn it into a floating window.
        """

    @overload
    def FloatingPosition(self, x: int, y: int) -> AuiPaneInfo:
        ...

    @overload
    def FloatingPosition(self, pos: wx.Point) -> AuiPaneInfo:
        """
        FloatingPosition(pos) -> AuiPaneInfo
        FloatingPosition(x, y) -> AuiPaneInfo
        
        FloatingPosition() sets the position of the floating pane.
        """

    @overload
    def FloatingSize(self, x: int, y: int) -> AuiPaneInfo:
        ...

    @overload
    def FloatingSize(self, size: wx.Size) -> AuiPaneInfo:
        """
        FloatingSize(size) -> AuiPaneInfo
        FloatingSize(x, y) -> AuiPaneInfo
        
        FloatingSize() sets the size of the floating pane.
        """

    def Gripper(self, visible: bool=True) -> AuiPaneInfo:
        """
        Gripper(visible=True) -> AuiPaneInfo
        
        Gripper() indicates that a gripper should be drawn for the pane.
        """

    def GripperTop(self, attop: bool=True) -> AuiPaneInfo:
        """
        GripperTop(attop=True) -> AuiPaneInfo
        
        GripperTop() indicates that a gripper should be drawn at the top of the pane.
        """

    def HasBorder(self) -> bool:
        """
        HasBorder() -> bool
        
        HasBorder() returns true if the pane displays a border.
        """

    def HasCaption(self) -> bool:
        """
        HasCaption() -> bool
        
        HasCaption() returns true if the pane displays a caption.
        """

    def HasCloseButton(self) -> bool:
        """
        HasCloseButton() -> bool
        
        HasCloseButton() returns true if the pane displays a button to close the pane.
        """

    def HasFlag(self, flag: int) -> bool:
        """
        HasFlag(flag) -> bool
        
        HasFlag() returns true if the property specified by flag is active for the pane.
        """

    def HasGripper(self) -> bool:
        """
        HasGripper() -> bool
        
        HasGripper() returns true if the pane displays a gripper.
        """

    def HasGripperTop(self) -> bool:
        """
        HasGripperTop() -> bool
        
        HasGripper() returns true if the pane displays a gripper at the top.
        """

    def HasMaximizeButton(self) -> bool:
        """
        HasMaximizeButton() -> bool
        
        HasMaximizeButton() returns true if the pane displays a button to maximize the pane.
        """

    def HasMinimizeButton(self) -> bool:
        """
        HasMinimizeButton() -> bool
        
        HasMinimizeButton() returns true if the pane displays a button to minimize the pane.
        """

    def HasPinButton(self) -> bool:
        """
        HasPinButton() -> bool
        
        HasPinButton() returns true if the pane displays a button to float the pane.
        """

    def Hide(self) -> AuiPaneInfo:
        """
        Hide() -> AuiPaneInfo
        
        Hide() indicates that a pane should be hidden.
        """

    def Icon(self, b: wx.BitmapBundle) -> AuiPaneInfo:
        """
        Icon(b) -> AuiPaneInfo
        
        Icon() sets the icon of the pane.
        """

    def IsBottomDockable(self) -> bool:
        """
        IsBottomDockable() -> bool
        
        IsBottomDockable() returns true if the pane can be docked at the bottom of the managed frame.
        """

    def IsDockable(self) -> bool:
        """
        IsDockable() -> bool
        
        Returns true if the pane can be docked at any side.
        """

    def IsDocked(self) -> bool:
        """
        IsDocked() -> bool
        
        IsDocked() returns true if the pane is currently docked.
        """

    def IsFixed(self) -> bool:
        """
        IsFixed() -> bool
        
        IsFixed() returns true if the pane cannot be resized.
        """

    def IsFloatable(self) -> bool:
        """
        IsFloatable() -> bool
        
        IsFloatable() returns true if the pane can be undocked and displayed as a floating window.
        """

    def IsFloating(self) -> bool:
        """
        IsFloating() -> bool
        
        IsFloating() returns true if the pane is floating.
        """

    def IsLeftDockable(self) -> bool:
        """
        IsLeftDockable() -> bool
        
        IsLeftDockable() returns true if the pane can be docked on the left of the managed frame.
        """

    def IsMovable(self) -> bool:
        """
        IsMovable() -> bool
        
        IsMoveable() returns true if the docked frame can be undocked or moved
        to another dock position.
        """

    def IsOk(self) -> bool:
        """
        IsOk() -> bool
        
        IsOk() returns true if the wxAuiPaneInfo structure is valid.
        """

    def IsResizable(self) -> bool:
        """
        IsResizable() -> bool
        
        IsResizable() returns true if the pane can be resized.
        """

    def IsRightDockable(self) -> bool:
        """
        IsRightDockable() -> bool
        
        IsRightDockable() returns true if the pane can be docked on the right of the managed frame.
        """

    def IsShown(self) -> bool:
        """
        IsShown() -> bool
        
        IsShown() returns true if the pane is currently shown.
        """

    def IsToolbar(self) -> bool:
        """
        IsToolbar() -> bool
        
        IsToolbar() returns true if the pane contains a toolbar.
        """

    def IsTopDockable(self) -> bool:
        """
        IsTopDockable() -> bool
        
        IsTopDockable() returns true if the pane can be docked at the top of the managed frame.
        """

    def Layer(self, layer: int) -> AuiPaneInfo:
        """
        Layer(layer) -> AuiPaneInfo
        
        Layer() determines the layer of the docked pane.
        """

    def Left(self) -> AuiPaneInfo:
        """
        Left() -> AuiPaneInfo
        
        Left() sets the pane dock position to the left side of the frame.
        """

    def LeftDockable(self, b: bool=True) -> AuiPaneInfo:
        """
        LeftDockable(b=True) -> AuiPaneInfo
        
        LeftDockable() indicates whether a pane can be docked on the left of the frame.
        """

    @overload
    def MaxSize(self, x: int, y: int) -> AuiPaneInfo:
        ...

    @overload
    def MaxSize(self, size: wx.Size) -> AuiPaneInfo:
        """
        MaxSize(size) -> AuiPaneInfo
        MaxSize(x, y) -> AuiPaneInfo
        
        MaxSize() sets the maximum size of the pane.
        """

    def MaximizeButton(self, visible: bool=True) -> AuiPaneInfo:
        """
        MaximizeButton(visible=True) -> AuiPaneInfo
        
        MaximizeButton() indicates that a maximize button should be drawn for the pane.
        """

    @overload
    def MinSize(self, x: int, y: int) -> AuiPaneInfo:
        ...

    @overload
    def MinSize(self, size: wx.Size) -> AuiPaneInfo:
        """
        MinSize(size) -> AuiPaneInfo
        MinSize(x, y) -> AuiPaneInfo
        
        MinSize() sets the minimum size of the pane.
        """

    def MinimizeButton(self, visible: bool=True) -> AuiPaneInfo:
        """
        MinimizeButton(visible=True) -> AuiPaneInfo
        
        MinimizeButton() indicates that a minimize button should be drawn for the pane.
        """

    def Movable(self, b: bool=True) -> AuiPaneInfo:
        """
        Movable(b=True) -> AuiPaneInfo
        
        Movable indicates whether a frame can be moved.
        """

    def Name(self, n: str) -> AuiPaneInfo:
        """
        Name(n) -> AuiPaneInfo
        
        Name() sets the name of the pane so it can be referenced in lookup functions.
        """

    def PaneBorder(self, visible: bool=True) -> AuiPaneInfo:
        """
        PaneBorder(visible=True) -> AuiPaneInfo
        
        PaneBorder indicates that a border should be drawn for the pane.
        """

    def PinButton(self, visible: bool=True) -> AuiPaneInfo:
        """
        PinButton(visible=True) -> AuiPaneInfo
        
        PinButton() indicates that a pin button should be drawn for the pane.
        """

    def Position(self, pos: int) -> AuiPaneInfo:
        """
        Position(pos) -> AuiPaneInfo
        
        Position() determines the position of the docked pane.
        """

    def Resizable(self, resizable: bool=True) -> AuiPaneInfo:
        """
        Resizable(resizable=True) -> AuiPaneInfo
        
        Resizable() allows a pane to be resized if the parameter is true, and forces it to be a fixed size if the parameter is false.
        """

    def Right(self) -> AuiPaneInfo:
        """
        Right() -> AuiPaneInfo
        
        Right() sets the pane dock position to the right side of the frame.
        """

    def RightDockable(self, b: bool=True) -> AuiPaneInfo:
        """
        RightDockable(b=True) -> AuiPaneInfo
        
        RightDockable() indicates whether a pane can be docked on the right of the frame.
        """

    def Row(self, row: int) -> AuiPaneInfo:
        """
        Row(row) -> AuiPaneInfo
        
        Row() determines the row of the docked pane.
        """

    def SafeSet(self, source: AuiPaneInfo) -> None:
        """
        SafeSet(source) -> None
        
        Write the safe parts of a PaneInfo object "source" into "this".
        """

    def SetFlag(self, flag: int, option_state: bool) -> AuiPaneInfo:
        """
        SetFlag(flag, option_state) -> AuiPaneInfo
        
        SetFlag() turns the property given by flag on or off with the option_state parameter.
        """

    def Show(self, show: bool=True) -> AuiPaneInfo:
        """
        Show(show=True) -> AuiPaneInfo
        
        Show() indicates that a pane should be shown.
        """

    def ToolbarPane(self) -> AuiPaneInfo:
        """
        ToolbarPane() -> AuiPaneInfo
        
        ToolbarPane() specifies that the pane should adopt the default toolbar pane settings.
        """

    def Top(self) -> AuiPaneInfo:
        """
        Top() -> AuiPaneInfo
        
        Top() sets the pane dock position to the top of the frame.
        """

    def TopDockable(self, b: bool=True) -> AuiPaneInfo:
        """
        TopDockable(b=True) -> AuiPaneInfo
        
        TopDockable() indicates whether a pane can be docked at the top of the frame.
        """

    def Window(self, w: wx.Window) -> AuiPaneInfo:
        """
        Window(w) -> AuiPaneInfo
        
        Window() assigns the window pointer that the wxAuiPaneInfo should use.
        """

    def IsValid(self) -> bool:
        """
        IsValid() -> bool
        """
# end of class AuiPaneInfo


class AuiManagerEvent(wx.Event):
    """
    AuiManagerEvent(type=wx.wxEVT_NULL) -> None
    
    Event used to indicate various actions taken with wxAuiManager.
    """

    def __init__(self, type: EventType=wx.wxEVT_NULL) -> None:
        """
        AuiManagerEvent(type=wx.wxEVT_NULL) -> None
        
        Event used to indicate various actions taken with wxAuiManager.
        """

    def CanVeto(self) -> bool:
        """
        CanVeto() -> bool
        """

    def GetButton(self) -> int:
        """
        GetButton() -> int
        """

    def GetDC(self) -> wx.DC:
        """
        GetDC() -> wx.DC
        """

    def GetVeto(self) -> bool:
        """
        GetVeto() -> bool
        """

    def GetManager(self) -> AuiManager:
        """
        GetManager() -> AuiManager
        """

    def GetPane(self) -> AuiPaneInfo:
        """
        GetPane() -> AuiPaneInfo
        """

    def SetButton(self, button: int) -> None:
        """
        SetButton(button) -> None
        
        Sets the ID of the button clicked that triggered this event.
        """

    def SetCanVeto(self, can_veto: bool) -> None:
        """
        SetCanVeto(can_veto) -> None
        
        Sets whether or not this event can be vetoed.
        """

    def SetDC(self, pdc: wx.DC) -> None:
        """
        SetDC(pdc) -> None
        """

    def SetManager(self, manager: AuiManager) -> None:
        """
        SetManager(manager) -> None
        
        Sets the wxAuiManager this event is associated with.
        """

    def SetPane(self, pane: AuiPaneInfo) -> None:
        """
        SetPane(pane) -> None
        
        Sets the pane this event is associated with.
        """

    def Veto(self, veto: bool=True) -> None:
        """
        Veto(veto=True) -> None
        
        Cancels the action indicated by this event if CanVeto() is true.
        """
    @property
    def Button(self) -> int: ...
    @Button.setter
    def Button(self, value: int, /) -> None: ...
    @property
    def DC(self) -> wx.DC: ...
    @DC.setter
    def DC(self, value: wx.DC, /) -> None: ...
    @property
    def Manager(self) -> AuiManager: ...
    @Manager.setter
    def Manager(self, value: AuiManager, /) -> None: ...
    @property
    def Pane(self) -> AuiPaneInfo: ...
    @Pane.setter
    def Pane(self, value: AuiPaneInfo, /) -> None: ...
# end of class AuiManagerEvent


class AuiDockInfo:
    """
    AuiDockInfo() -> None
    AuiDockInfo(c) -> None
    """

    @overload
    def __init__(self, c: AuiDockInfo) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        AuiDockInfo() -> None
        AuiDockInfo(c) -> None
        """
    panes: AuiPaneInfoPtrArray
    rect: wx.Rect
    dock_direction: int
    dock_layer: int
    dock_row: int
    size: int
    min_size: int
    resizable: bool
    toolbar: bool
    fixed: bool
    reserved1: bool

    def IsOk(self) -> bool:
        """
        IsOk() -> bool
        """

    def IsHorizontal(self) -> bool:
        """
        IsHorizontal() -> bool
        """

    def IsVertical(self) -> bool:
        """
        IsVertical() -> bool
        """
# end of class AuiDockInfo


class AuiDockUIPart:
    """
    
    """

    class _enum_1(IntEnum):
        typeCaption = auto()
        typeGripper = auto()
        typeDock = auto()
        typeDockSizer = auto()
        typePane = auto()
        typePaneSizer = auto()
        typeBackground = auto()
        typePaneBorder = auto()
        typePaneButton = auto()
    typeCaption = _enum_1.typeCaption
    typeGripper = _enum_1.typeGripper
    typeDock = _enum_1.typeDock
    typeDockSizer = _enum_1.typeDockSizer
    typePane = _enum_1.typePane
    typePaneSizer = _enum_1.typePaneSizer
    typeBackground = _enum_1.typeBackground
    typePaneBorder = _enum_1.typePaneBorder
    typePaneButton = _enum_1.typePaneButton
    type: int
    orientation: int
    dock: AuiDockInfo
    pane: AuiPaneInfo
    button: int
    cont_sizer: wx.Sizer
    sizer_item: wx.SizerItem
    rect: wx.Rect
# end of class AuiDockUIPart


class AuiFloatingFrame(wx.Frame):
    """
    AuiFloatingFrame(parent, ownerMgr, pane, id=wx.ID_ANY, style=wx.RESIZE_BORDER|wx.SYSTEM_MENU|wx.CAPTION|wx.FRAME_NO_TASKBAR|wx.FRAME_FLOAT_ON_PARENT|wx.CLIP_CHILDREN) -> None
    """

    def __init__(self, parent: Optional[wx.Window], ownerMgr: AuiManager, pane: AuiPaneInfo, id: int=wx.ID_ANY, style: int=wx.RESIZE_BORDER|wx.SYSTEM_MENU|wx.CAPTION|wx.FRAME_NO_TASKBAR|wx.FRAME_FLOAT_ON_PARENT|wx.CLIP_CHILDREN) -> None:
        """
        AuiFloatingFrame(parent, ownerMgr, pane, id=wx.ID_ANY, style=wx.RESIZE_BORDER|wx.SYSTEM_MENU|wx.CAPTION|wx.FRAME_NO_TASKBAR|wx.FRAME_FLOAT_ON_PARENT|wx.CLIP_CHILDREN) -> None
        """

    def SetPaneWindow(self, pane: AuiPaneInfo) -> None:
        """
        SetPaneWindow(pane) -> None
        """

    def GetOwnerManager(self) -> AuiManager:
        """
        GetOwnerManager() -> AuiManager
        """

    def GetAuiManager(self) -> AuiManager:
        """
        GetAuiManager() -> AuiManager
        
        Returns the embedded wxAuiManager managing this floating pane's
        contents.
        """
    @property
    def AuiManager(self) -> AuiManager: ...
    @property
    def OwnerManager(self) -> AuiManager: ...
# end of class AuiFloatingFrame


EVT_AUI_PANE_BUTTON = wx.PyEventBinder( wxEVT_AUI_PANE_BUTTON )
EVT_AUI_PANE_CLOSE = wx.PyEventBinder( wxEVT_AUI_PANE_CLOSE )
EVT_AUI_PANE_MAXIMIZE = wx.PyEventBinder( wxEVT_AUI_PANE_MAXIMIZE )
EVT_AUI_PANE_RESTORE = wx.PyEventBinder( wxEVT_AUI_PANE_RESTORE )
EVT_AUI_PANE_ACTIVATED = wx.PyEventBinder( wxEVT_AUI_PANE_ACTIVATED )
EVT_AUI_RENDER = wx.PyEventBinder( wxEVT_AUI_RENDER )
EVT_AUI_FIND_MANAGER = wx.PyEventBinder( wxEVT_AUI_FIND_MANAGER )
#-- end-auiframemanager --#
#-- begin-auidockart --#

class _AuiPaneDockArtSetting(IntEnum):
    AUI_DOCKART_SASH_SIZE = auto()
    AUI_DOCKART_CAPTION_SIZE = auto()
    AUI_DOCKART_GRIPPER_SIZE = auto()
    AUI_DOCKART_PANE_BORDER_SIZE = auto()
    AUI_DOCKART_PANE_BUTTON_SIZE = auto()
    AUI_DOCKART_BACKGROUND_COLOUR = auto()
    AUI_DOCKART_SASH_COLOUR = auto()
    AUI_DOCKART_ACTIVE_CAPTION_COLOUR = auto()
    AUI_DOCKART_ACTIVE_CAPTION_GRADIENT_COLOUR = auto()
    AUI_DOCKART_INACTIVE_CAPTION_COLOUR = auto()
    AUI_DOCKART_INACTIVE_CAPTION_GRADIENT_COLOUR = auto()
    AUI_DOCKART_ACTIVE_CAPTION_TEXT_COLOUR = auto()
    AUI_DOCKART_INACTIVE_CAPTION_TEXT_COLOUR = auto()
    AUI_DOCKART_BORDER_COLOUR = auto()
    AUI_DOCKART_GRIPPER_COLOUR = auto()
    AUI_DOCKART_CAPTION_FONT = auto()
    AUI_DOCKART_GRADIENT_TYPE = auto()
AuiPaneDockArtSetting: TypeAlias = Union[_AuiPaneDockArtSetting, int]
AUI_DOCKART_SASH_SIZE = _AuiPaneDockArtSetting.AUI_DOCKART_SASH_SIZE
AUI_DOCKART_CAPTION_SIZE = _AuiPaneDockArtSetting.AUI_DOCKART_CAPTION_SIZE
AUI_DOCKART_GRIPPER_SIZE = _AuiPaneDockArtSetting.AUI_DOCKART_GRIPPER_SIZE
AUI_DOCKART_PANE_BORDER_SIZE = _AuiPaneDockArtSetting.AUI_DOCKART_PANE_BORDER_SIZE
AUI_DOCKART_PANE_BUTTON_SIZE = _AuiPaneDockArtSetting.AUI_DOCKART_PANE_BUTTON_SIZE
AUI_DOCKART_BACKGROUND_COLOUR = _AuiPaneDockArtSetting.AUI_DOCKART_BACKGROUND_COLOUR
AUI_DOCKART_SASH_COLOUR = _AuiPaneDockArtSetting.AUI_DOCKART_SASH_COLOUR
AUI_DOCKART_ACTIVE_CAPTION_COLOUR = _AuiPaneDockArtSetting.AUI_DOCKART_ACTIVE_CAPTION_COLOUR
AUI_DOCKART_ACTIVE_CAPTION_GRADIENT_COLOUR = _AuiPaneDockArtSetting.AUI_DOCKART_ACTIVE_CAPTION_GRADIENT_COLOUR
AUI_DOCKART_INACTIVE_CAPTION_COLOUR = _AuiPaneDockArtSetting.AUI_DOCKART_INACTIVE_CAPTION_COLOUR
AUI_DOCKART_INACTIVE_CAPTION_GRADIENT_COLOUR = _AuiPaneDockArtSetting.AUI_DOCKART_INACTIVE_CAPTION_GRADIENT_COLOUR
AUI_DOCKART_ACTIVE_CAPTION_TEXT_COLOUR = _AuiPaneDockArtSetting.AUI_DOCKART_ACTIVE_CAPTION_TEXT_COLOUR
AUI_DOCKART_INACTIVE_CAPTION_TEXT_COLOUR = _AuiPaneDockArtSetting.AUI_DOCKART_INACTIVE_CAPTION_TEXT_COLOUR
AUI_DOCKART_BORDER_COLOUR = _AuiPaneDockArtSetting.AUI_DOCKART_BORDER_COLOUR
AUI_DOCKART_GRIPPER_COLOUR = _AuiPaneDockArtSetting.AUI_DOCKART_GRIPPER_COLOUR
AUI_DOCKART_CAPTION_FONT = _AuiPaneDockArtSetting.AUI_DOCKART_CAPTION_FONT
AUI_DOCKART_GRADIENT_TYPE = _AuiPaneDockArtSetting.AUI_DOCKART_GRADIENT_TYPE

class _AuiPaneDockArtGradients(IntEnum):
    AUI_GRADIENT_NONE = auto()
    AUI_GRADIENT_VERTICAL = auto()
    AUI_GRADIENT_HORIZONTAL = auto()
AuiPaneDockArtGradients: TypeAlias = Union[_AuiPaneDockArtGradients, int]
AUI_GRADIENT_NONE = _AuiPaneDockArtGradients.AUI_GRADIENT_NONE
AUI_GRADIENT_VERTICAL = _AuiPaneDockArtGradients.AUI_GRADIENT_VERTICAL
AUI_GRADIENT_HORIZONTAL = _AuiPaneDockArtGradients.AUI_GRADIENT_HORIZONTAL

class _AuiPaneButtonState(IntEnum):
    AUI_BUTTON_STATE_NORMAL = auto()
    AUI_BUTTON_STATE_HOVER = auto()
    AUI_BUTTON_STATE_PRESSED = auto()
    AUI_BUTTON_STATE_DISABLED = auto()
    AUI_BUTTON_STATE_HIDDEN = auto()
    AUI_BUTTON_STATE_CHECKED = auto()
AuiPaneButtonState: TypeAlias = Union[_AuiPaneButtonState, int]
AUI_BUTTON_STATE_NORMAL = _AuiPaneButtonState.AUI_BUTTON_STATE_NORMAL
AUI_BUTTON_STATE_HOVER = _AuiPaneButtonState.AUI_BUTTON_STATE_HOVER
AUI_BUTTON_STATE_PRESSED = _AuiPaneButtonState.AUI_BUTTON_STATE_PRESSED
AUI_BUTTON_STATE_DISABLED = _AuiPaneButtonState.AUI_BUTTON_STATE_DISABLED
AUI_BUTTON_STATE_HIDDEN = _AuiPaneButtonState.AUI_BUTTON_STATE_HIDDEN
AUI_BUTTON_STATE_CHECKED = _AuiPaneButtonState.AUI_BUTTON_STATE_CHECKED

class _AuiButtonId(IntEnum):
    AUI_BUTTON_CLOSE = auto()
    AUI_BUTTON_MAXIMIZE_RESTORE = auto()
    AUI_BUTTON_MINIMIZE = auto()
    AUI_BUTTON_PIN = auto()
    AUI_BUTTON_OPTIONS = auto()
    AUI_BUTTON_WINDOWLIST = auto()
    AUI_BUTTON_LEFT = auto()
    AUI_BUTTON_RIGHT = auto()
    AUI_BUTTON_UP = auto()
    AUI_BUTTON_DOWN = auto()
    AUI_BUTTON_CUSTOM1 = auto()
    AUI_BUTTON_CUSTOM2 = auto()
    AUI_BUTTON_CUSTOM3 = auto()
AuiButtonId: TypeAlias = Union[_AuiButtonId, int]
AUI_BUTTON_CLOSE = _AuiButtonId.AUI_BUTTON_CLOSE
AUI_BUTTON_MAXIMIZE_RESTORE = _AuiButtonId.AUI_BUTTON_MAXIMIZE_RESTORE
AUI_BUTTON_MINIMIZE = _AuiButtonId.AUI_BUTTON_MINIMIZE
AUI_BUTTON_PIN = _AuiButtonId.AUI_BUTTON_PIN
AUI_BUTTON_OPTIONS = _AuiButtonId.AUI_BUTTON_OPTIONS
AUI_BUTTON_WINDOWLIST = _AuiButtonId.AUI_BUTTON_WINDOWLIST
AUI_BUTTON_LEFT = _AuiButtonId.AUI_BUTTON_LEFT
AUI_BUTTON_RIGHT = _AuiButtonId.AUI_BUTTON_RIGHT
AUI_BUTTON_UP = _AuiButtonId.AUI_BUTTON_UP
AUI_BUTTON_DOWN = _AuiButtonId.AUI_BUTTON_DOWN
AUI_BUTTON_CUSTOM1 = _AuiButtonId.AUI_BUTTON_CUSTOM1
AUI_BUTTON_CUSTOM2 = _AuiButtonId.AUI_BUTTON_CUSTOM2
AUI_BUTTON_CUSTOM3 = _AuiButtonId.AUI_BUTTON_CUSTOM3

class AuiDockArt:
    """
    AuiDockArt() -> None
    
    wxAuiDockArt is part of the wxAUI class framework.
    """

    def __init__(self) -> None:
        """
        AuiDockArt() -> None
        
        wxAuiDockArt is part of the wxAUI class framework.
        """

    def Clone(self) -> AuiDockArt:
        """
        Clone() -> AuiDockArt
        
        Create a copy of this wxAuiDockArt instance.
        """

    def DrawBackground(self, dc: wx.DC, window: wx.Window, orientation: int, rect: wx.Rect) -> None:
        """
        DrawBackground(dc, window, orientation, rect) -> None
        
        Draws a background.
        """

    def DrawBorder(self, dc: wx.DC, window: wx.Window, rect: wx.Rect, pane: AuiPaneInfo) -> None:
        """
        DrawBorder(dc, window, rect, pane) -> None
        
        Draws a border.
        """

    def DrawCaption(self, dc: wx.DC, window: wx.Window, text: str, rect: wx.Rect, pane: AuiPaneInfo) -> None:
        """
        DrawCaption(dc, window, text, rect, pane) -> None
        
        Draws a caption.
        """

    def DrawGripper(self, dc: wx.DC, window: wx.Window, rect: wx.Rect, pane: AuiPaneInfo) -> None:
        """
        DrawGripper(dc, window, rect, pane) -> None
        
        Draws a gripper.
        """

    def DrawPaneButton(self, dc: wx.DC, window: wx.Window, button: int, button_state: int, rect: wx.Rect, pane: AuiPaneInfo) -> None:
        """
        DrawPaneButton(dc, window, button, button_state, rect, pane) -> None
        
        Draws a button in the pane's title bar.
        """

    def DrawSash(self, dc: wx.DC, window: wx.Window, orientation: int, rect: wx.Rect) -> None:
        """
        DrawSash(dc, window, orientation, rect) -> None
        
        Draws a sash between two windows.
        """

    def GetColour(self, id: int) -> wx.Colour:
        """
        GetColour(id) -> wx.Colour
        
        Get the colour of a certain setting.
        """

    def GetFont(self, id: int) -> wx.Font:
        """
        GetFont(id) -> wx.Font
        
        Get a font setting.
        """

    def GetMetric(self, id: int) -> int:
        """
        GetMetric(id) -> int
        
        Get the value of a certain setting.
        """

    def SetColour(self, id: int, colour: wx.Colour) -> None:
        """
        SetColour(id, colour) -> None
        
        Set a certain setting with the value colour.
        """

    def SetFont(self, id: int, font: wx.Font) -> None:
        """
        SetFont(id, font) -> None
        
        Set a font setting.
        """

    def SetMetric(self, id: int, new_val: int) -> None:
        """
        SetMetric(id, new_val) -> None
        
        Set a certain setting with the value new_val.
        """
# end of class AuiDockArt


class AuiDefaultDockArt(AuiDockArt):
    """
    AuiDefaultDockArt() -> None
    
    This is the default art provider for wxAuiManager.
    """

    def __init__(self) -> None:
        """
        AuiDefaultDockArt() -> None
        
        This is the default art provider for wxAuiManager.
        """

    def Clone(self) -> AuiDockArt:
        """
        Clone() -> AuiDockArt
        
        Create a copy of this wxAuiDockArt instance.
        """

    def GetMetric(self, id: int) -> int:
        """
        GetMetric(id) -> int
        
        Get the value of a certain setting.
        """

    def SetMetric(self, id: int, new_val: int) -> None:
        """
        SetMetric(id, new_val) -> None
        
        Set a certain setting with the value new_val.
        """

    def GetColour(self, id: int) -> wx.Colour:
        """
        GetColour(id) -> wx.Colour
        
        Get the colour of a certain setting.
        """

    def SetColour(self, id: int, colour: wx.Colour) -> None:
        """
        SetColour(id, colour) -> None
        
        Set a certain setting with the value colour.
        """

    def SetFont(self, id: int, font: wx.Font) -> None:
        """
        SetFont(id, font) -> None
        
        Set a font setting.
        """

    def GetFont(self, id: int) -> wx.Font:
        """
        GetFont(id) -> wx.Font
        
        Get a font setting.
        """

    def DrawSash(self, dc: wx.DC, window: wx.Window, orientation: int, rect: wx.Rect) -> None:
        """
        DrawSash(dc, window, orientation, rect) -> None
        
        Draws a sash between two windows.
        """

    def DrawBackground(self, dc: wx.DC, window: wx.Window, orientation: int, rect: wx.Rect) -> None:
        """
        DrawBackground(dc, window, orientation, rect) -> None
        
        Draws a background.
        """

    def DrawCaption(self, dc: wx.DC, window: wx.Window, text: str, rect: wx.Rect, pane: AuiPaneInfo) -> None:
        """
        DrawCaption(dc, window, text, rect, pane) -> None
        
        Draws a caption.
        """

    def DrawGripper(self, dc: wx.DC, window: wx.Window, rect: wx.Rect, pane: AuiPaneInfo) -> None:
        """
        DrawGripper(dc, window, rect, pane) -> None
        
        Draws a gripper.
        """

    def DrawBorder(self, dc: wx.DC, window: wx.Window, rect: wx.Rect, pane: AuiPaneInfo) -> None:
        """
        DrawBorder(dc, window, rect, pane) -> None
        
        Draws a border.
        """

    def DrawPaneButton(self, dc: wx.DC, window: wx.Window, button: int, button_state: int, rect: wx.Rect, pane: AuiPaneInfo) -> None:
        """
        DrawPaneButton(dc, window, button, button_state, rect, pane) -> None
        
        Draws a button in the pane's title bar.
        """

    def DrawIcon(self, dc: wx.DC, rect: wx.Rect, pane: AuiPaneInfo) -> None:
        """
        DrawIcon(dc, rect, pane) -> None
        """
# end of class AuiDefaultDockArt

#-- end-auidockart --#
#-- begin-auibar --#

class _AuiToolBarStyle(IntEnum):
    AUI_TB_TEXT = auto()
    AUI_TB_NO_TOOLTIPS = auto()
    AUI_TB_NO_AUTORESIZE = auto()
    AUI_TB_GRIPPER = auto()
    AUI_TB_OVERFLOW = auto()
    AUI_TB_VERTICAL = auto()
    AUI_TB_HORZ_LAYOUT = auto()
    AUI_TB_HORIZONTAL = auto()
    AUI_TB_PLAIN_BACKGROUND = auto()
    AUI_TB_HORZ_TEXT = auto()
    AUI_ORIENTATION_MASK = auto()
    AUI_TB_DEFAULT_STYLE = auto()
AuiToolBarStyle: TypeAlias = Union[_AuiToolBarStyle, int]
AUI_TB_TEXT = _AuiToolBarStyle.AUI_TB_TEXT
AUI_TB_NO_TOOLTIPS = _AuiToolBarStyle.AUI_TB_NO_TOOLTIPS
AUI_TB_NO_AUTORESIZE = _AuiToolBarStyle.AUI_TB_NO_AUTORESIZE
AUI_TB_GRIPPER = _AuiToolBarStyle.AUI_TB_GRIPPER
AUI_TB_OVERFLOW = _AuiToolBarStyle.AUI_TB_OVERFLOW
AUI_TB_VERTICAL = _AuiToolBarStyle.AUI_TB_VERTICAL
AUI_TB_HORZ_LAYOUT = _AuiToolBarStyle.AUI_TB_HORZ_LAYOUT
AUI_TB_HORIZONTAL = _AuiToolBarStyle.AUI_TB_HORIZONTAL
AUI_TB_PLAIN_BACKGROUND = _AuiToolBarStyle.AUI_TB_PLAIN_BACKGROUND
AUI_TB_HORZ_TEXT = _AuiToolBarStyle.AUI_TB_HORZ_TEXT
AUI_ORIENTATION_MASK = _AuiToolBarStyle.AUI_ORIENTATION_MASK
AUI_TB_DEFAULT_STYLE = _AuiToolBarStyle.AUI_TB_DEFAULT_STYLE

class _AuiToolBarArtSetting(IntEnum):
    AUI_TBART_SEPARATOR_SIZE = auto()
    AUI_TBART_GRIPPER_SIZE = auto()
    AUI_TBART_OVERFLOW_SIZE = auto()
AuiToolBarArtSetting: TypeAlias = Union[_AuiToolBarArtSetting, int]
AUI_TBART_SEPARATOR_SIZE = _AuiToolBarArtSetting.AUI_TBART_SEPARATOR_SIZE
AUI_TBART_GRIPPER_SIZE = _AuiToolBarArtSetting.AUI_TBART_GRIPPER_SIZE
AUI_TBART_OVERFLOW_SIZE = _AuiToolBarArtSetting.AUI_TBART_OVERFLOW_SIZE

class _AuiToolBarToolTextOrientation(IntEnum):
    AUI_TBTOOL_TEXT_LEFT = auto()
    AUI_TBTOOL_TEXT_RIGHT = auto()
    AUI_TBTOOL_TEXT_TOP = auto()
    AUI_TBTOOL_TEXT_BOTTOM = auto()
AuiToolBarToolTextOrientation: TypeAlias = Union[_AuiToolBarToolTextOrientation, int]
AUI_TBTOOL_TEXT_LEFT = _AuiToolBarToolTextOrientation.AUI_TBTOOL_TEXT_LEFT
AUI_TBTOOL_TEXT_RIGHT = _AuiToolBarToolTextOrientation.AUI_TBTOOL_TEXT_RIGHT
AUI_TBTOOL_TEXT_TOP = _AuiToolBarToolTextOrientation.AUI_TBTOOL_TEXT_TOP
AUI_TBTOOL_TEXT_BOTTOM = _AuiToolBarToolTextOrientation.AUI_TBTOOL_TEXT_BOTTOM
wxEVT_AUITOOLBAR_TOOL_DROPDOWN: int
wxEVT_AUITOOLBAR_OVERFLOW_CLICK: int
wxEVT_AUITOOLBAR_RIGHT_CLICK: int
wxEVT_AUITOOLBAR_MIDDLE_CLICK: int
wxEVT_AUITOOLBAR_BEGIN_DRAG: int

class AuiToolBarItem:
    """
    AuiToolBarItem() -> None
    AuiToolBarItem(c) -> None
    
    wxAuiToolBarItem is part of the wxAUI class framework, representing a
    toolbar element.
    """

    @overload
    def __init__(self, c: AuiToolBarItem) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        AuiToolBarItem() -> None
        AuiToolBarItem(c) -> None
        
        wxAuiToolBarItem is part of the wxAUI class framework, representing a
        toolbar element.
        """

    def Assign(self, c: AuiToolBarItem) -> None:
        """
        Assign(c) -> None
        
        Assigns the properties of the wxAuiToolBarItem "c" to this.
        """

    def SetWindow(self, w: wx.Window) -> None:
        """
        SetWindow(w) -> None
        
        Assigns a window to the toolbar item.
        """

    def GetWindow(self) -> wx.Window:
        """
        GetWindow() -> wx.Window
        
        Returns the wxWindow* associated to the toolbar item.
        """

    def SetId(self, new_id: int) -> None:
        """
        SetId(new_id) -> None
        
        Sets the toolbar item identifier.
        """

    def GetId(self) -> int:
        """
        GetId() -> int
        
        Returns the toolbar item identifier.
        """

    def SetKind(self, new_kind: int) -> None:
        """
        SetKind(new_kind) -> None
        
        Sets the wxAuiToolBarItem kind.
        """

    def GetKind(self) -> int:
        """
        GetKind() -> int
        
        Returns the toolbar item kind.
        """

    def SetState(self, new_state: int) -> None:
        """
        SetState(new_state) -> None
        
        Set the current state of the toolbar item.
        """

    def GetState(self) -> int:
        """
        GetState() -> int
        
        Gets the current state of the toolbar item.
        """

    def SetSizerItem(self, s: wx.SizerItem) -> None:
        """
        SetSizerItem(s) -> None
        """

    def GetSizerItem(self) -> wx.SizerItem:
        """
        GetSizerItem() -> wx.SizerItem
        """

    def SetLabel(self, s: str) -> None:
        """
        SetLabel(s) -> None
        """

    def GetLabel(self) -> str:
        """
        GetLabel() -> str
        """

    def SetBitmap(self, bmp: wx.BitmapBundle) -> None:
        """
        SetBitmap(bmp) -> None
        """

    def GetBitmap(self) -> wx.Bitmap:
        """
        GetBitmap() -> wx.Bitmap
        """

    def SetDisabledBitmap(self, bmp: wx.BitmapBundle) -> None:
        """
        SetDisabledBitmap(bmp) -> None
        """

    def GetDisabledBitmap(self) -> wx.Bitmap:
        """
        GetDisabledBitmap() -> wx.Bitmap
        """

    def SetHoverBitmap(self, bmp: wx.BitmapBundle) -> None:
        """
        SetHoverBitmap(bmp) -> None
        """

    def GetHoverBitmap(self) -> wx.Bitmap:
        """
        GetHoverBitmap() -> wx.Bitmap
        """

    def SetShortHelp(self, s: str) -> None:
        """
        SetShortHelp(s) -> None
        """

    def GetShortHelp(self) -> str:
        """
        GetShortHelp() -> str
        """

    def SetLongHelp(self, s: str) -> None:
        """
        SetLongHelp(s) -> None
        """

    def GetLongHelp(self) -> str:
        """
        GetLongHelp() -> str
        """

    def SetMinSize(self, s: wx.Size) -> None:
        """
        SetMinSize(s) -> None
        """

    def GetMinSize(self) -> wx.Size:
        """
        GetMinSize() -> wx.Size
        """

    def SetSpacerPixels(self, s: int) -> None:
        """
        SetSpacerPixels(s) -> None
        """

    def GetSpacerPixels(self) -> int:
        """
        GetSpacerPixels() -> int
        """

    def SetProportion(self, p: int) -> None:
        """
        SetProportion(p) -> None
        """

    def GetProportion(self) -> int:
        """
        GetProportion() -> int
        """

    def SetActive(self, b: bool) -> None:
        """
        SetActive(b) -> None
        """

    def IsActive(self) -> bool:
        """
        IsActive() -> bool
        """

    def SetHasDropDown(self, b: bool) -> None:
        """
        SetHasDropDown(b) -> None
        
        Set whether this tool has a drop down button.
        """

    def HasDropDown(self) -> bool:
        """
        HasDropDown() -> bool
        
        Returns whether the toolbar item has an associated drop down button.
        """

    def SetSticky(self, b: bool) -> None:
        """
        SetSticky(b) -> None
        """

    def IsSticky(self) -> bool:
        """
        IsSticky() -> bool
        """

    def SetUserData(self, l: int) -> None:
        """
        SetUserData(l) -> None
        """

    def GetUserData(self) -> int:
        """
        GetUserData() -> int
        """

    def SetAlignment(self, l: int) -> None:
        """
        SetAlignment(l) -> None
        """

    def GetAlignment(self) -> int:
        """
        GetAlignment() -> int
        """

    def CanBeToggled(self) -> bool:
        """
        CanBeToggled() -> bool
        
        Returns whether the toolbar item can be toggled.
        """
    @property
    def Alignment(self) -> int: ...
    @Alignment.setter
    def Alignment(self, value: int, /) -> None: ...
    @property
    def Bitmap(self) -> wx.BitmapBundle: ...
    @Bitmap.setter
    def Bitmap(self, value: wx.BitmapBundle, /) -> None: ...
    @property
    def DisabledBitmap(self) -> wx.BitmapBundle: ...
    @DisabledBitmap.setter
    def DisabledBitmap(self, value: wx.BitmapBundle, /) -> None: ...
    @property
    def HoverBitmap(self) -> wx.BitmapBundle: ...
    @HoverBitmap.setter
    def HoverBitmap(self, value: wx.BitmapBundle, /) -> None: ...
    @property
    def Id(self) -> int: ...
    @Id.setter
    def Id(self, value: int, /) -> None: ...
    @property
    def Kind(self) -> int: ...
    @Kind.setter
    def Kind(self, value: int, /) -> None: ...
    @property
    def Label(self) -> str: ...
    @Label.setter
    def Label(self, value: str, /) -> None: ...
    @property
    def LongHelp(self) -> str: ...
    @LongHelp.setter
    def LongHelp(self, value: str, /) -> None: ...
    @property
    def MinSize(self) -> wx.Size: ...
    @MinSize.setter
    def MinSize(self, value: wx.Size, /) -> None: ...
    @property
    def Proportion(self) -> int: ...
    @Proportion.setter
    def Proportion(self, value: int, /) -> None: ...
    @property
    def ShortHelp(self) -> str: ...
    @ShortHelp.setter
    def ShortHelp(self, value: str, /) -> None: ...
    @property
    def SizerItem(self) -> wx.SizerItem: ...
    @SizerItem.setter
    def SizerItem(self, value: wx.SizerItem, /) -> None: ...
    @property
    def SpacerPixels(self) -> int: ...
    @SpacerPixels.setter
    def SpacerPixels(self, value: int, /) -> None: ...
    @property
    def State(self) -> int: ...
    @State.setter
    def State(self, value: int, /) -> None: ...
    @property
    def UserData(self) -> int: ...
    @UserData.setter
    def UserData(self, value: int, /) -> None: ...
    @property
    def Window(self) -> wx.Window: ...
    @Window.setter
    def Window(self, value: wx.Window, /) -> None: ...
# end of class AuiToolBarItem


class AuiToolBarArt:
    """
    AuiToolBarArt() -> None
    
    wxAuiToolBarArt is part of the wxAUI class framework.
    """

    def __init__(self) -> None:
        """
        AuiToolBarArt() -> None
        
        wxAuiToolBarArt is part of the wxAUI class framework.
        """

    def Clone(self) -> AuiToolBarArt:
        """
        Clone() -> AuiToolBarArt
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        """

    def SetFont(self, font: wx.Font) -> None:
        """
        SetFont(font) -> None
        """

    def GetFont(self) -> wx.Font:
        """
        GetFont() -> wx.Font
        """

    def SetTextOrientation(self, orientation: int) -> None:
        """
        SetTextOrientation(orientation) -> None
        """

    def GetTextOrientation(self) -> int:
        """
        GetTextOrientation() -> int
        """

    def DrawBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawBackground(dc, wnd, rect) -> None
        """

    def DrawPlainBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawPlainBackground(dc, wnd, rect) -> None
        """

    def DrawLabel(self, dc: wx.DC, wnd: wx.Window, item: AuiToolBarItem, rect: wx.Rect) -> None:
        """
        DrawLabel(dc, wnd, item, rect) -> None
        """

    def DrawButton(self, dc: wx.DC, wnd: wx.Window, item: AuiToolBarItem, rect: wx.Rect) -> None:
        """
        DrawButton(dc, wnd, item, rect) -> None
        """

    def DrawDropDownButton(self, dc: wx.DC, wnd: wx.Window, item: AuiToolBarItem, rect: wx.Rect) -> None:
        """
        DrawDropDownButton(dc, wnd, item, rect) -> None
        """

    def DrawControlLabel(self, dc: wx.DC, wnd: wx.Window, item: AuiToolBarItem, rect: wx.Rect) -> None:
        """
        DrawControlLabel(dc, wnd, item, rect) -> None
        """

    def DrawSeparator(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawSeparator(dc, wnd, rect) -> None
        """

    def DrawGripper(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawGripper(dc, wnd, rect) -> None
        """

    def DrawOverflowButton(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, state: int) -> None:
        """
        DrawOverflowButton(dc, wnd, rect, state) -> None
        """

    def GetLabelSize(self, dc: wx.DC, wnd: wx.Window, item: AuiToolBarItem) -> wx.Size:
        """
        GetLabelSize(dc, wnd, item) -> wx.Size
        """

    def GetToolSize(self, dc: wx.DC, wnd: wx.Window, item: AuiToolBarItem) -> wx.Size:
        """
        GetToolSize(dc, wnd, item) -> wx.Size
        """

    def GetElementSize(self, element_id: int) -> int:
        """
        GetElementSize(element_id) -> int
        """

    def SetElementSize(self, element_id: int, size: int) -> None:
        """
        SetElementSize(element_id, size) -> None
        """

    def ShowDropDown(self, wnd: wx.Window, items: AuiToolBarItemArray) -> int:
        """
        ShowDropDown(wnd, items) -> int
        """
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def Font(self) -> wx.Font: ...
    @Font.setter
    def Font(self, value: wx.Font, /) -> None: ...
    @property
    def TextOrientation(self) -> int: ...
    @TextOrientation.setter
    def TextOrientation(self, value: int, /) -> None: ...
# end of class AuiToolBarArt


class AuiDefaultToolBarArt(AuiToolBarArt):
    """
    AuiDefaultToolBarArt() -> None
    
    wxAuiDefaultToolBarArt is part of the wxAUI class framework.
    """

    def __init__(self) -> None:
        """
        AuiDefaultToolBarArt() -> None
        
        wxAuiDefaultToolBarArt is part of the wxAUI class framework.
        """

    def Clone(self) -> AuiToolBarArt:
        """
        Clone() -> AuiToolBarArt
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        """

    def SetFont(self, font: wx.Font) -> None:
        """
        SetFont(font) -> None
        """

    def GetFont(self) -> wx.Font:
        """
        GetFont() -> wx.Font
        """

    def SetTextOrientation(self, orientation: int) -> None:
        """
        SetTextOrientation(orientation) -> None
        """

    def GetTextOrientation(self) -> int:
        """
        GetTextOrientation() -> int
        """

    def DrawBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawBackground(dc, wnd, rect) -> None
        """

    def DrawPlainBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawPlainBackground(dc, wnd, rect) -> None
        """

    def DrawLabel(self, dc: wx.DC, wnd: wx.Window, item: AuiToolBarItem, rect: wx.Rect) -> None:
        """
        DrawLabel(dc, wnd, item, rect) -> None
        """

    def DrawButton(self, dc: wx.DC, wnd: wx.Window, item: AuiToolBarItem, rect: wx.Rect) -> None:
        """
        DrawButton(dc, wnd, item, rect) -> None
        """

    def DrawDropDownButton(self, dc: wx.DC, wnd: wx.Window, item: AuiToolBarItem, rect: wx.Rect) -> None:
        """
        DrawDropDownButton(dc, wnd, item, rect) -> None
        """

    def DrawControlLabel(self, dc: wx.DC, wnd: wx.Window, item: AuiToolBarItem, rect: wx.Rect) -> None:
        """
        DrawControlLabel(dc, wnd, item, rect) -> None
        """

    def DrawSeparator(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawSeparator(dc, wnd, rect) -> None
        """

    def DrawGripper(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawGripper(dc, wnd, rect) -> None
        """

    def DrawOverflowButton(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, state: int) -> None:
        """
        DrawOverflowButton(dc, wnd, rect, state) -> None
        """

    def GetLabelSize(self, dc: wx.DC, wnd: wx.Window, item: AuiToolBarItem) -> wx.Size:
        """
        GetLabelSize(dc, wnd, item) -> wx.Size
        """

    def GetToolSize(self, dc: wx.DC, wnd: wx.Window, item: AuiToolBarItem) -> wx.Size:
        """
        GetToolSize(dc, wnd, item) -> wx.Size
        """

    def GetElementSize(self, element: int) -> int:
        """
        GetElementSize(element) -> int
        """

    def SetElementSize(self, element_id: int, size: int) -> None:
        """
        SetElementSize(element_id, size) -> None
        """

    def ShowDropDown(self, wnd: wx.Window, items: AuiToolBarItemArray) -> int:
        """
        ShowDropDown(wnd, items) -> int
        """
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def Font(self) -> wx.Font: ...
    @Font.setter
    def Font(self, value: wx.Font, /) -> None: ...
    @property
    def TextOrientation(self) -> int: ...
    @TextOrientation.setter
    def TextOrientation(self, value: int, /) -> None: ...
# end of class AuiDefaultToolBarArt


class AuiToolBar(wx.Control):
    """
    AuiToolBar() -> None
    AuiToolBar(parent, id=wx.ID_ANY, position=wx.DefaultPosition, size=wx.DefaultSize, style=AUI_TB_DEFAULT_STYLE) -> None
    
    wxAuiToolBar is a dockable toolbar, part of the wxAUI class framework.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, position: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=AUI_TB_DEFAULT_STYLE) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        AuiToolBar() -> None
        AuiToolBar(parent, id=wx.ID_ANY, position=wx.DefaultPosition, size=wx.DefaultSize, style=AUI_TB_DEFAULT_STYLE) -> None
        
        wxAuiToolBar is a dockable toolbar, part of the wxAUI class framework.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=AUI_TB_DEFAULT_STYLE) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=AUI_TB_DEFAULT_STYLE) -> bool
        
        Really create wxAuiToolBar created using default constructor.
        """

    def SetWindowStyleFlag(self, style: int) -> None:
        """
        SetWindowStyleFlag(style) -> None
        
        Sets the style of the window.
        """

    def GetWindowStyleFlag(self) -> int:
        """
        GetWindowStyleFlag() -> int
        
        Gets the window style that was passed to the constructor or Create()
        method.
        """

    def SetArtProvider(self, art: AuiToolBarArt) -> None:
        """
        SetArtProvider(art) -> None
        """

    def GetArtProvider(self) -> AuiToolBarArt:
        """
        GetArtProvider() -> AuiToolBarArt
        """

    def SetFont(self, font: wx.Font) -> bool:
        """
        SetFont(font) -> bool
        
        Sets the font for this window.
        """

    @overload
    def AddTool(self, toolId: int, label: str, bitmap: wx.BitmapBundle, disabled_bitmap: wx.BitmapBundle, kind: wx.ItemKind, short_help_string: str, long_help_string: str, client_data: wx.Object) -> AuiToolBarItem:
        ...

    @overload
    def AddTool(self, toolId: int, bitmap: wx.BitmapBundle, disabled_bitmap: wx.BitmapBundle, toggle: bool=False, client_data: Optional[wx.Object]=None, short_help_string: str='', long_help_string: str='') -> AuiToolBarItem:
        ...

    @overload
    def AddTool(self, toolId: int, label: str, bitmap: wx.BitmapBundle, short_help_string: str='', kind: wx.ItemKind=wx.ITEM_NORMAL) -> AuiToolBarItem:
        """
        AddTool(toolId, label, bitmap, short_help_string='', kind=wx.ITEM_NORMAL) -> AuiToolBarItem
        AddTool(toolId, label, bitmap, disabled_bitmap, kind, short_help_string, long_help_string, client_data) -> AuiToolBarItem
        AddTool(toolId, bitmap, disabled_bitmap, toggle=False, client_data=None, short_help_string='', long_help_string='') -> AuiToolBarItem
        """

    def AddLabel(self, toolId: int, label: str='', width: int=-1) -> AuiToolBarItem:
        """
        AddLabel(toolId, label='', width=-1) -> AuiToolBarItem
        """

    def AddControl(self, control: wx.Control, label: str='') -> AuiToolBarItem:
        """
        AddControl(control, label='') -> AuiToolBarItem
        """

    def AddSeparator(self) -> AuiToolBarItem:
        """
        AddSeparator() -> AuiToolBarItem
        """

    def AddSpacer(self, pixels: int) -> AuiToolBarItem:
        """
        AddSpacer(pixels) -> AuiToolBarItem
        """

    def AddStretchSpacer(self, proportion: int=1) -> AuiToolBarItem:
        """
        AddStretchSpacer(proportion=1) -> AuiToolBarItem
        """

    def Realize(self) -> bool:
        """
        Realize() -> bool
        """

    def FindControl(self, window_id: int) -> wx.Control:
        """
        FindControl(window_id) -> wx.Control
        """

    def FindToolByPosition(self, x: int, y: int) -> AuiToolBarItem:
        """
        FindToolByPosition(x, y) -> AuiToolBarItem
        """

    def FindToolByIndex(self, idx: int) -> AuiToolBarItem:
        """
        FindToolByIndex(idx) -> AuiToolBarItem
        """

    def FindTool(self, toolId: int) -> AuiToolBarItem:
        """
        FindTool(toolId) -> AuiToolBarItem
        """

    def ClearTools(self) -> None:
        """
        ClearTools() -> None
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        """

    def DestroyTool(self, toolId: int) -> bool:
        """
        DestroyTool(toolId) -> bool
        
        Destroys the tool with the given ID and its associated window, if any.
        """

    def DestroyToolByIndex(self, idx: int) -> bool:
        """
        DestroyToolByIndex(idx) -> bool
        
        Destroys the tool at the given position and its associated window, if
        any.
        """

    def DeleteTool(self, toolId: int) -> bool:
        """
        DeleteTool(toolId) -> bool
        
        Removes the tool with the given ID from the toolbar.
        """

    def DeleteByIndex(self, idx: int) -> bool:
        """
        DeleteByIndex(idx) -> bool
        
        Removes the tool at the given position from the toolbar.
        """

    def GetToolCount(self) -> int:
        """
        GetToolCount() -> int
        """

    def GetToolPos(self, toolId: int) -> int:
        """
        GetToolPos(toolId) -> int
        """

    def GetToolIndex(self, toolId: int) -> int:
        """
        GetToolIndex(toolId) -> int
        """

    def GetToolFits(self, toolId: int) -> bool:
        """
        GetToolFits(toolId) -> bool
        """

    def GetToolRect(self, toolId: int) -> wx.Rect:
        """
        GetToolRect(toolId) -> wx.Rect
        """

    def GetToolFitsByIndex(self, toolId: int) -> bool:
        """
        GetToolFitsByIndex(toolId) -> bool
        """

    def GetToolBarFits(self) -> bool:
        """
        GetToolBarFits() -> bool
        """

    @overload
    def SetMargins(self, x: int, y: int) -> None:
        ...

    @overload
    def SetMargins(self, left: int, right: int, top: int, bottom: int) -> None:
        ...

    @overload
    def SetMargins(self, size: wx.Size) -> None:
        """
        SetMargins(size) -> None
        SetMargins(x, y) -> None
        SetMargins(left, right, top, bottom) -> None
        """

    def SetToolBitmapSize(self, size: wx.Size) -> None:
        """
        SetToolBitmapSize(size) -> None
        """

    def GetToolBitmapSize(self) -> wx.Size:
        """
        GetToolBitmapSize() -> wx.Size
        """

    def GetOverflowVisible(self) -> bool:
        """
        GetOverflowVisible() -> bool
        """

    def SetOverflowVisible(self, visible: bool) -> None:
        """
        SetOverflowVisible(visible) -> None
        """

    def GetGripperVisible(self) -> bool:
        """
        GetGripperVisible() -> bool
        """

    def SetGripperVisible(self, visible: bool) -> None:
        """
        SetGripperVisible(visible) -> None
        """

    def ToggleTool(self, toolId: int, state: bool) -> None:
        """
        ToggleTool(toolId, state) -> None
        """

    def GetToolToggled(self, toolId: int) -> bool:
        """
        GetToolToggled(toolId) -> bool
        """

    def EnableTool(self, toolId: int, state: bool) -> None:
        """
        EnableTool(toolId, state) -> None
        """

    def GetToolEnabled(self, toolId: int) -> bool:
        """
        GetToolEnabled(toolId) -> bool
        """

    def SetToolDropDown(self, toolId: int, dropdown: bool) -> None:
        """
        SetToolDropDown(toolId, dropdown) -> None
        
        Set whether the specified toolbar item has a drop down button.
        """

    def GetToolDropDown(self, toolId: int) -> bool:
        """
        GetToolDropDown(toolId) -> bool
        
        Returns whether the specified toolbar item has an associated drop down
        button.
        """

    def SetToolBorderPadding(self, padding: int) -> None:
        """
        SetToolBorderPadding(padding) -> None
        """

    def GetToolBorderPadding(self) -> int:
        """
        GetToolBorderPadding() -> int
        """

    def SetToolTextOrientation(self, orientation: int) -> None:
        """
        SetToolTextOrientation(orientation) -> None
        """

    def GetToolTextOrientation(self) -> int:
        """
        GetToolTextOrientation() -> int
        """

    def SetToolPacking(self, packing: int) -> None:
        """
        SetToolPacking(packing) -> None
        """

    def GetToolPacking(self) -> int:
        """
        GetToolPacking() -> int
        """

    def SetToolProportion(self, toolId: int, proportion: int) -> None:
        """
        SetToolProportion(toolId, proportion) -> None
        """

    def GetToolProportion(self, toolId: int) -> int:
        """
        GetToolProportion(toolId) -> int
        """

    def SetToolSeparation(self, separation: int) -> None:
        """
        SetToolSeparation(separation) -> None
        """

    def GetToolSeparation(self) -> int:
        """
        GetToolSeparation() -> int
        """

    def SetToolSticky(self, toolId: int, sticky: bool) -> None:
        """
        SetToolSticky(toolId, sticky) -> None
        """

    def GetToolSticky(self, toolId: int) -> bool:
        """
        GetToolSticky(toolId) -> bool
        """

    def GetToolLabel(self, toolId: int) -> str:
        """
        GetToolLabel(toolId) -> str
        """

    def SetToolLabel(self, toolId: int, label: str) -> None:
        """
        SetToolLabel(toolId, label) -> None
        """

    def GetToolBitmap(self, toolId: int) -> wx.Bitmap:
        """
        GetToolBitmap(toolId) -> wx.Bitmap
        """

    def SetToolBitmap(self, toolId: int, bitmap: wx.BitmapBundle) -> None:
        """
        SetToolBitmap(toolId, bitmap) -> None
        """

    def GetToolShortHelp(self, toolId: int) -> str:
        """
        GetToolShortHelp(toolId) -> str
        """

    def SetToolShortHelp(self, toolId: int, help_string: str) -> None:
        """
        SetToolShortHelp(toolId, help_string) -> None
        """

    def GetToolLongHelp(self, toolId: int) -> str:
        """
        GetToolLongHelp(toolId) -> str
        """

    def SetToolLongHelp(self, toolId: int, help_string: str) -> None:
        """
        SetToolLongHelp(toolId, help_string) -> None
        """

    def SetCustomOverflowItems(self, prepend: AuiToolBarItemArray, append: AuiToolBarItemArray) -> None:
        """
        SetCustomOverflowItems(prepend, append) -> None
        
        Add toolbar items that are always displayed in the overflow menu.
        """

    def GetHintSize(self, dock_direction: int) -> wx.Size:
        """
        GetHintSize(dock_direction) -> wx.Size
        
        get size of hint rectangle for a particular dock location
        """

    def IsPaneValid(self, pane: AuiPaneInfo) -> bool:
        """
        IsPaneValid(pane) -> bool
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ArtProvider(self) -> AuiToolBarArt: ...
    @ArtProvider.setter
    def ArtProvider(self, value: AuiToolBarArt, /) -> None: ...
    @property
    def GripperVisible(self) -> bool: ...
    @GripperVisible.setter
    def GripperVisible(self, value: bool, /) -> None: ...
    @property
    def OverflowVisible(self) -> bool: ...
    @OverflowVisible.setter
    def OverflowVisible(self, value: bool, /) -> None: ...
    @property
    def ToolBarFits(self) -> bool: ...
    @property
    def ToolBitmapSize(self) -> wx.Size: ...
    @ToolBitmapSize.setter
    def ToolBitmapSize(self, value: wx.Size, /) -> None: ...
    @property
    def ToolBorderPadding(self) -> int: ...
    @ToolBorderPadding.setter
    def ToolBorderPadding(self, value: int, /) -> None: ...
    @property
    def ToolCount(self) -> int: ...
    @property
    def ToolPacking(self) -> int: ...
    @ToolPacking.setter
    def ToolPacking(self, value: int, /) -> None: ...
    @property
    def ToolSeparation(self) -> int: ...
    @ToolSeparation.setter
    def ToolSeparation(self, value: int, /) -> None: ...
    @property
    def ToolTextOrientation(self) -> int: ...
    @ToolTextOrientation.setter
    def ToolTextOrientation(self, value: int, /) -> None: ...
    @property
    def WindowStyleFlag(self) -> int: ...
    @WindowStyleFlag.setter
    def WindowStyleFlag(self, value: int, /) -> None: ...
# end of class AuiToolBar


class AuiToolBarEvent(wx.NotifyEvent):
    """
    AuiToolBarEvent(commandType=wx.wxEVT_NULL, winId=0) -> None
    AuiToolBarEvent(c) -> None
    
    wxAuiToolBarEvent is used for the events generated by wxAuiToolBar.
    """

    @overload
    def __init__(self, c: AuiToolBarEvent) -> None:
        ...

    @overload
    def __init__(self, commandType: EventType=wx.wxEVT_NULL, winId: int=0) -> None:
        """
        AuiToolBarEvent(commandType=wx.wxEVT_NULL, winId=0) -> None
        AuiToolBarEvent(c) -> None
        
        wxAuiToolBarEvent is used for the events generated by wxAuiToolBar.
        """

    def IsDropDownClicked(self) -> bool:
        """
        IsDropDownClicked() -> bool
        
        Returns whether the drop down menu has been clicked.
        """

    def GetClickPoint(self) -> wx.Point:
        """
        GetClickPoint() -> wx.Point
        
        Returns the point where the user clicked with the mouse.
        """

    def GetItemRect(self) -> wx.Rect:
        """
        GetItemRect() -> wx.Rect
        
        Returns the wxAuiToolBarItem rectangle bounding the mouse click point.
        """

    def GetToolId(self) -> int:
        """
        GetToolId() -> int
        
        Returns the wxAuiToolBarItem identifier.
        """

    def SetDropDownClicked(self, c: bool) -> None:
        """
        SetDropDownClicked(c) -> None
        """

    def SetClickPoint(self, p: wx.Point) -> None:
        """
        SetClickPoint(p) -> None
        """

    def SetItemRect(self, r: wx.Rect) -> None:
        """
        SetItemRect(r) -> None
        """

    def SetToolId(self, toolId: int) -> None:
        """
        SetToolId(toolId) -> None
        """
    @property
    def ClickPoint(self) -> wx.Point: ...
    @ClickPoint.setter
    def ClickPoint(self, value: wx.Point, /) -> None: ...
    @property
    def ItemRect(self) -> wx.Rect: ...
    @ItemRect.setter
    def ItemRect(self, value: wx.Rect, /) -> None: ...
    @property
    def ToolId(self) -> int: ...
    @ToolId.setter
    def ToolId(self, value: int, /) -> None: ...
# end of class AuiToolBarEvent


EVT_AUITOOLBAR_TOOL_DROPDOWN = wx.PyEventBinder( wxEVT_AUITOOLBAR_TOOL_DROPDOWN, 1 )
EVT_AUITOOLBAR_OVERFLOW_CLICK = wx.PyEventBinder( wxEVT_AUITOOLBAR_OVERFLOW_CLICK, 1 )
EVT_AUITOOLBAR_RIGHT_CLICK = wx.PyEventBinder( wxEVT_AUITOOLBAR_RIGHT_CLICK, 1 )
EVT_AUITOOLBAR_MIDDLE_CLICK = wx.PyEventBinder( wxEVT_AUITOOLBAR_MIDDLE_CLICK, 1 )
EVT_AUITOOLBAR_BEGIN_DRAG = wx.PyEventBinder( wxEVT_AUITOOLBAR_BEGIN_DRAG, 1 )
#-- end-auibar --#
#-- begin-auibook --#

class _AuiNotebookOption(IntEnum):
    AUI_NB_TOP = auto()
    AUI_NB_LEFT = auto()
    AUI_NB_RIGHT = auto()
    AUI_NB_BOTTOM = auto()
    AUI_NB_TAB_SPLIT = auto()
    AUI_NB_TAB_MOVE = auto()
    AUI_NB_TAB_EXTERNAL_MOVE = auto()
    AUI_NB_TAB_FIXED_WIDTH = auto()
    AUI_NB_SCROLL_BUTTONS = auto()
    AUI_NB_WINDOWLIST_BUTTON = auto()
    AUI_NB_CLOSE_BUTTON = auto()
    AUI_NB_CLOSE_ON_ACTIVE_TAB = auto()
    AUI_NB_CLOSE_ON_ALL_TABS = auto()
    AUI_NB_MIDDLE_CLICK_CLOSE = auto()
    AUI_NB_DEFAULT_STYLE = auto()
AuiNotebookOption: TypeAlias = Union[_AuiNotebookOption, int]
AUI_NB_TOP = _AuiNotebookOption.AUI_NB_TOP
AUI_NB_LEFT = _AuiNotebookOption.AUI_NB_LEFT
AUI_NB_RIGHT = _AuiNotebookOption.AUI_NB_RIGHT
AUI_NB_BOTTOM = _AuiNotebookOption.AUI_NB_BOTTOM
AUI_NB_TAB_SPLIT = _AuiNotebookOption.AUI_NB_TAB_SPLIT
AUI_NB_TAB_MOVE = _AuiNotebookOption.AUI_NB_TAB_MOVE
AUI_NB_TAB_EXTERNAL_MOVE = _AuiNotebookOption.AUI_NB_TAB_EXTERNAL_MOVE
AUI_NB_TAB_FIXED_WIDTH = _AuiNotebookOption.AUI_NB_TAB_FIXED_WIDTH
AUI_NB_SCROLL_BUTTONS = _AuiNotebookOption.AUI_NB_SCROLL_BUTTONS
AUI_NB_WINDOWLIST_BUTTON = _AuiNotebookOption.AUI_NB_WINDOWLIST_BUTTON
AUI_NB_CLOSE_BUTTON = _AuiNotebookOption.AUI_NB_CLOSE_BUTTON
AUI_NB_CLOSE_ON_ACTIVE_TAB = _AuiNotebookOption.AUI_NB_CLOSE_ON_ACTIVE_TAB
AUI_NB_CLOSE_ON_ALL_TABS = _AuiNotebookOption.AUI_NB_CLOSE_ON_ALL_TABS
AUI_NB_MIDDLE_CLICK_CLOSE = _AuiNotebookOption.AUI_NB_MIDDLE_CLICK_CLOSE
AUI_NB_DEFAULT_STYLE = _AuiNotebookOption.AUI_NB_DEFAULT_STYLE
wxEVT_AUINOTEBOOK_PAGE_CLOSE: int
wxEVT_AUINOTEBOOK_PAGE_CHANGED: int
wxEVT_AUINOTEBOOK_PAGE_CHANGING: int
wxEVT_AUINOTEBOOK_PAGE_CLOSED: int
wxEVT_AUINOTEBOOK_BUTTON: int
wxEVT_AUINOTEBOOK_BEGIN_DRAG: int
wxEVT_AUINOTEBOOK_END_DRAG: int
wxEVT_AUINOTEBOOK_DRAG_MOTION: int
wxEVT_AUINOTEBOOK_ALLOW_DND: int
wxEVT_AUINOTEBOOK_TAB_MIDDLE_DOWN: int
wxEVT_AUINOTEBOOK_TAB_MIDDLE_UP: int
wxEVT_AUINOTEBOOK_TAB_RIGHT_DOWN: int
wxEVT_AUINOTEBOOK_TAB_RIGHT_UP: int
wxEVT_AUINOTEBOOK_DRAG_DONE: int
wxEVT_AUINOTEBOOK_BG_DCLICK: int

class AuiNotebook(wx.BookCtrlBase):
    """
    AuiNotebook() -> None
    AuiNotebook(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=AUI_NB_DEFAULT_STYLE) -> None
    
    wxAuiNotebook is part of the wxAUI class framework, which represents a
    notebook control, managing multiple windows with associated tabs.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=AUI_NB_DEFAULT_STYLE) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        AuiNotebook() -> None
        AuiNotebook(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=AUI_NB_DEFAULT_STYLE) -> None
        
        wxAuiNotebook is part of the wxAUI class framework, which represents a
        notebook control, managing multiple windows with associated tabs.
        """

    @overload
    def AddPage(self, page: wx.Window, text: str, select: bool, imageId: int) -> bool:
        ...

    @overload
    def AddPage(self, page: wx.Window, caption: str, select: bool=False, bitmap: wx.BitmapBundle=wx.BitmapBundle()) -> bool:
        """
        AddPage(page, caption, select=False, bitmap=wx.BitmapBundle()) -> bool
        AddPage(page, text, select, imageId) -> bool
        
        Adds a page.
        """

    def AdvanceSelection(self, forward: bool=True) -> None:
        """
        AdvanceSelection(forward=True) -> None
        
        Sets the selection to the next or previous page.
        """

    def ChangeSelection(self, n: int) -> int:
        """
        ChangeSelection(n) -> int
        
        Changes the selection for the given page, returning the previous
        selection.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> bool
        
        Creates the notebook window.
        """

    def DeleteAllPages(self) -> bool:
        """
        DeleteAllPages() -> bool
        
        Deletes all pages.
        """

    def DeletePage(self, page: int) -> bool:
        """
        DeletePage(page) -> bool
        
        Deletes a page at the given index.
        """

    def GetArtProvider(self) -> AuiTabArt:
        """
        GetArtProvider() -> AuiTabArt
        
        Returns the associated art provider.
        """

    def GetCurrentPage(self) -> wx.Window:
        """
        GetCurrentPage() -> wx.Window
        
        Returns the currently selected page or NULL.
        """

    def GetHeightForPageHeight(self, pageHeight: int) -> int:
        """
        GetHeightForPageHeight(pageHeight) -> int
        
        Returns the desired height of the notebook for the given page height.
        """

    def GetPage(self, page_idx: int) -> wx.Window:
        """
        GetPage(page_idx) -> wx.Window
        
        Returns the page specified by the given index.
        """

    def GetPageBitmap(self, page: int) -> wx.Bitmap:
        """
        GetPageBitmap(page) -> wx.Bitmap
        
        Returns the tab bitmap for the page.
        """

    def GetPageCount(self) -> int:
        """
        GetPageCount() -> int
        
        Returns the number of pages in the notebook.
        """

    def GetPageIndex(self, page_wnd: wx.Window) -> int:
        """
        GetPageIndex(page_wnd) -> int
        
        Returns the page index for the specified window.
        """

    def GetPageText(self, page: int) -> str:
        """
        GetPageText(page) -> str
        
        Returns the tab label for the page.
        """

    def GetPageToolTip(self, pageIdx: int) -> str:
        """
        GetPageToolTip(pageIdx) -> str
        
        Returns the tooltip for the tab label of the page.
        """

    def GetSelection(self) -> int:
        """
        GetSelection() -> int
        
        Returns the currently selected page.
        """

    def GetTabCtrlHeight(self) -> int:
        """
        GetTabCtrlHeight() -> int
        
        Returns the height of the tab control.
        """

    @overload
    def InsertPage(self, index: int, page: wx.Window, text: str, select: bool, imageId: int) -> bool:
        ...

    @overload
    def InsertPage(self, page_idx: int, page: wx.Window, caption: str, select: bool=False, bitmap: wx.BitmapBundle=wx.BitmapBundle()) -> bool:
        """
        InsertPage(page_idx, page, caption, select=False, bitmap=wx.BitmapBundle()) -> bool
        InsertPage(index, page, text, select, imageId) -> bool
        
        InsertPage() is similar to AddPage, but allows the ability to specify the insert location.
        """

    def RemovePage(self, page: int) -> bool:
        """
        RemovePage(page) -> bool
        
        Removes a page, without deleting the window pointer.
        """

    def SetArtProvider(self, art: AuiTabArt) -> None:
        """
        SetArtProvider(art) -> None
        
        Sets the art provider to be used by the notebook.
        """

    def SetFont(self, font: wx.Font) -> bool:
        """
        SetFont(font) -> bool
        
        Sets the font for drawing the tab labels, using a bold version of the
        font for selected tab labels.
        """

    def SetMeasuringFont(self, font: wx.Font) -> None:
        """
        SetMeasuringFont(font) -> None
        
        Sets the font for measuring tab labels.
        """

    def SetNormalFont(self, font: wx.Font) -> None:
        """
        SetNormalFont(font) -> None
        
        Sets the font for drawing unselected tab labels.
        """

    def SetPageBitmap(self, page: int, bitmap: wx.BitmapBundle) -> bool:
        """
        SetPageBitmap(page, bitmap) -> bool
        
        Sets the bitmap for the page.
        """

    def SetPageImage(self, n: int, imageId: int) -> bool:
        """
        SetPageImage(n, imageId) -> bool
        
        Sets the image index for the given page.
        """

    def SetPageText(self, page: int, text: str) -> bool:
        """
        SetPageText(page, text) -> bool
        
        Sets the tab label for the page.
        """

    def SetPageToolTip(self, page: int, text: str) -> bool:
        """
        SetPageToolTip(page, text) -> bool
        
        Sets the tooltip displayed when hovering over the tab label of the
        page.
        """

    def SetSelectedFont(self, font: wx.Font) -> None:
        """
        SetSelectedFont(font) -> None
        
        Sets the font for drawing selected tab labels.
        """

    def SetSelection(self, new_page: int) -> int:
        """
        SetSelection(new_page) -> int
        
        Sets the page selection.
        """

    def SetTabCtrlHeight(self, height: int) -> None:
        """
        SetTabCtrlHeight(height) -> None
        
        Sets the tab height.
        """

    def SetUniformBitmapSize(self, size: wx.Size) -> None:
        """
        SetUniformBitmapSize(size) -> None
        
        Ensure that all tabs have the same height, even if some of them don't
        have bitmaps.
        """

    def Split(self, page: int, direction: int) -> None:
        """
        Split(page, direction) -> None
        
        Split performs a split operation programmatically.
        """

    def ShowWindowMenu(self) -> bool:
        """
        ShowWindowMenu() -> bool
        
        Shows the window menu for the active tab control associated with this
        notebook, and returns true if a selection was made.
        """

    def GetPageImage(self, nPage: int) -> int:
        """
        GetPageImage(nPage) -> int
        
        Returns the image index for the given page.
        """

    def GetTabCtrlFromPoint(self, pt: wx.Point) -> AuiTabCtrl:
        """
        GetTabCtrlFromPoint(pt) -> AuiTabCtrl
        
        Returns tab control based on point coordinates inside the tab frame.
        """

    def GetActiveTabCtrl(self) -> AuiTabCtrl:
        """
        GetActiveTabCtrl() -> AuiTabCtrl
        
        Returns active tab control for this notebook.
        """

    def FindTab(self, page: wx.Window, ctrl: AuiTabCtrl, idx: int) -> bool:
        """
        FindTab(page, ctrl, idx) -> bool
        
        Finds tab control associated with a given window and its tab index.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ActiveTabCtrl(self) -> AuiTabCtrl: ...
    @property
    def ArtProvider(self) -> AuiTabArt: ...
    @ArtProvider.setter
    def ArtProvider(self, value: AuiTabArt, /) -> None: ...
    @property
    def CurrentPage(self) -> wx.Window: ...
    @property
    def PageCount(self) -> int: ...
    @property
    def Selection(self) -> int: ...
    @Selection.setter
    def Selection(self, value: int, /) -> None: ...
    @property
    def TabCtrlHeight(self) -> int: ...
    @TabCtrlHeight.setter
    def TabCtrlHeight(self, value: int, /) -> None: ...
# end of class AuiNotebook


class AuiNotebookPage:
    """
    A simple class which holds information about the notebook's pages and
    their state.
    """
    window: wx.Window
    caption: str
    tooltip: str
    bitmap: wx.BitmapBundle
    rect: wx.Rect
    active: bool
# end of class AuiNotebookPage


class AuiTabContainerButton:
    """
    A simple class which holds information about wxAuiNotebook tab buttons
    and their state.
    """
    id: int
    curState: int
    location: int
    bitmap: wx.BitmapBundle
    disBitmap: wx.BitmapBundle
    rect: wx.Rect
# end of class AuiTabContainerButton


class AuiTabContainer:
    """
    AuiTabContainer() -> None
    
    wxAuiTabContainer is a class which contains information about each
    tab.
    """

    def __init__(self) -> None:
        """
        AuiTabContainer() -> None
        
        wxAuiTabContainer is a class which contains information about each
        tab.
        """

    def SetArtProvider(self, art: AuiTabArt) -> None:
        """
        SetArtProvider(art) -> None
        """

    def GetArtProvider(self) -> AuiTabArt:
        """
        GetArtProvider() -> AuiTabArt
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        """

    def AddPage(self, page: wx.Window, info: AuiNotebookPage) -> bool:
        """
        AddPage(page, info) -> bool
        """

    def InsertPage(self, page: wx.Window, info: AuiNotebookPage, idx: int) -> bool:
        """
        InsertPage(page, info, idx) -> bool
        """

    def MovePage(self, page: wx.Window, newIdx: int) -> bool:
        """
        MovePage(page, newIdx) -> bool
        """

    def RemovePage(self, page: wx.Window) -> bool:
        """
        RemovePage(page) -> bool
        """

    @overload
    def SetActivePage(self, page: int) -> bool:
        ...

    @overload
    def SetActivePage(self, page: wx.Window) -> bool:
        """
        SetActivePage(page) -> bool
        SetActivePage(page) -> bool
        """

    def SetNoneActive(self) -> None:
        """
        SetNoneActive() -> None
        """

    def GetActivePage(self) -> int:
        """
        GetActivePage() -> int
        """

    def TabHitTest(self, x: int, y: int, hit: wx.Window) -> bool:
        """
        TabHitTest(x, y, hit) -> bool
        """

    def ButtonHitTest(self, x: int, y: int, hit: AuiTabContainerButton) -> bool:
        """
        ButtonHitTest(x, y, hit) -> bool
        """

    def GetWindowFromIdx(self, idx: int) -> wx.Window:
        """
        GetWindowFromIdx(idx) -> wx.Window
        """

    def GetIdxFromWindow(self, page: wx.Window) -> int:
        """
        GetIdxFromWindow(page) -> int
        """

    def GetPageCount(self) -> int:
        """
        GetPageCount() -> int
        """

    def GetPage(self, idx: int) -> AuiNotebookPage:
        """
        GetPage(idx) -> AuiNotebookPage
        """

    def GetPages(self) -> AuiNotebookPageArray:
        """
        GetPages() -> AuiNotebookPageArray
        """

    def SetNormalFont(self, normalFont: wx.Font) -> None:
        """
        SetNormalFont(normalFont) -> None
        """

    def SetSelectedFont(self, selectedFont: wx.Font) -> None:
        """
        SetSelectedFont(selectedFont) -> None
        """

    def SetMeasuringFont(self, measuringFont: wx.Font) -> None:
        """
        SetMeasuringFont(measuringFont) -> None
        """

    def SetColour(self, colour: wx.Colour) -> None:
        """
        SetColour(colour) -> None
        """

    def SetActiveColour(self, colour: wx.Colour) -> None:
        """
        SetActiveColour(colour) -> None
        """

    def DoShowHide(self) -> None:
        """
        DoShowHide() -> None
        """

    def SetRect(self, rect: wx.Rect, wnd: Optional[wx.Window]=None) -> None:
        """
        SetRect(rect, wnd=None) -> None
        """

    def RemoveButton(self, id: int) -> None:
        """
        RemoveButton(id) -> None
        """

    def AddButton(self, id: int, location: int, normalBitmap: wx.BitmapBundle=wx.BitmapBundle(), disabledBitmap: wx.BitmapBundle=wx.BitmapBundle()) -> None:
        """
        AddButton(id, location, normalBitmap=wx.BitmapBundle(), disabledBitmap=wx.BitmapBundle()) -> None
        """

    def GetTabOffset(self) -> int:
        """
        GetTabOffset() -> int
        """

    def SetTabOffset(self, offset: int) -> None:
        """
        SetTabOffset(offset) -> None
        """

    def IsTabVisible(self, tabPage: int, tabOffset: int, dc: wx.DC, wnd: wx.Window) -> bool:
        """
        IsTabVisible(tabPage, tabOffset, dc, wnd) -> bool
        """

    def MakeTabVisible(self, tabPage: int, win: wx.Window) -> None:
        """
        MakeTabVisible(tabPage, win) -> None
        """
    @property
    def ActivePage(self) -> wx.Window: ...
    @ActivePage.setter
    def ActivePage(self, value: wx.Window, /) -> None: ...
    @property
    def ArtProvider(self) -> AuiTabArt: ...
    @ArtProvider.setter
    def ArtProvider(self, value: AuiTabArt, /) -> None: ...
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def PageCount(self) -> int: ...
    @property
    def Pages(self) -> AuiNotebookPageArray: ...
    @property
    def TabOffset(self) -> int: ...
    @TabOffset.setter
    def TabOffset(self, value: int, /) -> None: ...
# end of class AuiTabContainer


class AuiTabArt:
    """
    AuiTabArt() -> None
    
    Tab art provider defines all the drawing functions used by
    wxAuiNotebook.
    """

    def __init__(self) -> None:
        """
        AuiTabArt() -> None
        
        Tab art provider defines all the drawing functions used by
        wxAuiNotebook.
        """

    def Clone(self) -> AuiTabArt:
        """
        Clone() -> AuiTabArt
        
        Clones the art object.
        """

    def DrawBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawBackground(dc, wnd, rect) -> None
        
        Draws a background on the given area.
        """

    def DrawButton(self, dc: wx.DC, wnd: wx.Window, in_rect: wx.Rect, bitmap_id: int, button_state: int, orientation: int, out_rect: wx.Rect) -> None:
        """
        DrawButton(dc, wnd, in_rect, bitmap_id, button_state, orientation, out_rect) -> None
        
        Draws a button.
        """

    def DrawTab(self, dc: wx.DC, wnd: wx.Window, page: AuiNotebookPage, rect: wx.Rect, close_button_state: int, out_tab_rect: wx.Rect, out_button_rect: wx.Rect, x_extent: int) -> None:
        """
        DrawTab(dc, wnd, page, rect, close_button_state, out_tab_rect, out_button_rect, x_extent) -> None
        
        Draws a tab.
        """

    def GetBestTabCtrlSize(self, : wx.Size) -> int:
        """
        GetBestTabCtrlSize() -> int
        
        Returns the tab control size.
        """

    def GetIndentSize(self) -> int:
        """
        GetIndentSize() -> int
        
        Returns the indent size.
        """

    def GetTabSize(self, dc: wx.DC, wnd: wx.Window, caption: str, bitmap: wx.BitmapBundle, active: bool, close_button_state: int, x_extent: int) -> wx.Size:
        """
        GetTabSize(dc, wnd, caption, bitmap, active, close_button_state, x_extent) -> wx.Size
        
        Returns the tab size for the given caption, bitmap and state.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Sets flags.
        """

    def SetMeasuringFont(self, font: wx.Font) -> None:
        """
        SetMeasuringFont(font) -> None
        
        Sets the font used for calculating measurements.
        """

    def SetNormalFont(self, font: wx.Font) -> None:
        """
        SetNormalFont(font) -> None
        
        Sets the normal font for drawing labels.
        """

    def SetSelectedFont(self, font: wx.Font) -> None:
        """
        SetSelectedFont(font) -> None
        
        Sets the font for drawing text for selected UI elements.
        """

    def SetColour(self, colour: wx.Colour) -> None:
        """
        SetColour(colour) -> None
        
        Sets the colour of the inactive tabs.
        """

    def SetActiveColour(self, colour: wx.Colour) -> None:
        """
        SetActiveColour(colour) -> None
        
        Sets the colour of the selected tab.
        """

    def SetSizingInfo(self, tab_ctrl_size: wx.Size, tab_count: int, wnd: Optional[wx.Window]=None) -> None:
        """
        SetSizingInfo(tab_ctrl_size, tab_count, wnd=None) -> None
        
        Sets sizing information.
        """
    @property
    def IndentSize(self) -> int: ...
# end of class AuiTabArt


class AuiDefaultTabArt(AuiTabArt):
    """
    AuiDefaultTabArt() -> None
    
    Default art provider for wxAuiNotebook.
    """

    def __init__(self) -> None:
        """
        AuiDefaultTabArt() -> None
        
        Default art provider for wxAuiNotebook.
        """

    def Clone(self) -> AuiTabArt:
        """
        Clone() -> AuiTabArt
        
        Clones the art object.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Sets flags.
        """

    def SetSizingInfo(self, tab_ctrl_size: wx.Size, tab_count: int, wnd: Optional[wx.Window]=None) -> None:
        """
        SetSizingInfo(tab_ctrl_size, tab_count, wnd=None) -> None
        
        Sets sizing information.
        """

    def SetNormalFont(self, font: wx.Font) -> None:
        """
        SetNormalFont(font) -> None
        
        Sets the normal font for drawing labels.
        """

    def SetSelectedFont(self, font: wx.Font) -> None:
        """
        SetSelectedFont(font) -> None
        
        Sets the font for drawing text for selected UI elements.
        """

    def SetMeasuringFont(self, font: wx.Font) -> None:
        """
        SetMeasuringFont(font) -> None
        
        Sets the font used for calculating measurements.
        """

    def SetColour(self, colour: wx.Colour) -> None:
        """
        SetColour(colour) -> None
        
        Sets the colour of the inactive tabs.
        """

    def SetActiveColour(self, colour: wx.Colour) -> None:
        """
        SetActiveColour(colour) -> None
        
        Sets the colour of the selected tab.
        """

    def DrawBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawBackground(dc, wnd, rect) -> None
        
        Draws a background on the given area.
        """

    def DrawTab(self, dc: wx.DC, wnd: wx.Window, page: AuiNotebookPage, rect: wx.Rect, close_button_state: int, out_tab_rect: wx.Rect, out_button_rect: wx.Rect, x_extent: int) -> None:
        """
        DrawTab(dc, wnd, page, rect, close_button_state, out_tab_rect, out_button_rect, x_extent) -> None
        
        Draws a tab.
        """

    def DrawButton(self, dc: wx.DC, wnd: wx.Window, in_rect: wx.Rect, bitmap_id: int, button_state: int, orientation: int, out_rect: wx.Rect) -> None:
        """
        DrawButton(dc, wnd, in_rect, bitmap_id, button_state, orientation, out_rect) -> None
        
        Draws a button.
        """

    def GetIndentSize(self) -> int:
        """
        GetIndentSize() -> int
        
        Returns the indent size.
        """

    def GetTabSize(self, dc: wx.DC, wnd: wx.Window, caption: str, bitmap: wx.BitmapBundle, active: bool, close_button_state: int, x_extent: int) -> wx.Size:
        """
        GetTabSize(dc, wnd, caption, bitmap, active, close_button_state, x_extent) -> wx.Size
        
        Returns the tab size for the given caption, bitmap and state.
        """

    def ShowDropDown(self, wnd: wx.Window, items: AuiNotebookPageArray, activeIdx: int) -> int:
        """
        ShowDropDown(wnd, items, activeIdx) -> int
        """

    def GetBestTabCtrlSize(self, : wx.Size) -> int:
        """
        GetBestTabCtrlSize() -> int
        
        Returns the tab control size.
        """
    @property
    def IndentSize(self) -> int: ...
# end of class AuiDefaultTabArt


class AuiSimpleTabArt(AuiTabArt):
    """
    AuiSimpleTabArt() -> None
    
    Another standard tab art provider for wxAuiNotebook.
    """

    def __init__(self) -> None:
        """
        AuiSimpleTabArt() -> None
        
        Another standard tab art provider for wxAuiNotebook.
        """

    def Clone(self) -> AuiTabArt:
        """
        Clone() -> AuiTabArt
        
        Clones the art object.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Sets flags.
        """

    def SetSizingInfo(self, tab_ctrl_size: wx.Size, tab_count: int, wnd: Optional[wx.Window]=None) -> None:
        """
        SetSizingInfo(tab_ctrl_size, tab_count, wnd=None) -> None
        
        Sets sizing information.
        """

    def SetNormalFont(self, font: wx.Font) -> None:
        """
        SetNormalFont(font) -> None
        
        Sets the normal font for drawing labels.
        """

    def SetSelectedFont(self, font: wx.Font) -> None:
        """
        SetSelectedFont(font) -> None
        
        Sets the font for drawing text for selected UI elements.
        """

    def SetMeasuringFont(self, font: wx.Font) -> None:
        """
        SetMeasuringFont(font) -> None
        
        Sets the font used for calculating measurements.
        """

    def SetColour(self, colour: wx.Colour) -> None:
        """
        SetColour(colour) -> None
        
        Sets the colour of the inactive tabs.
        """

    def SetActiveColour(self, colour: wx.Colour) -> None:
        """
        SetActiveColour(colour) -> None
        
        Sets the colour of the selected tab.
        """

    def DrawBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawBackground(dc, wnd, rect) -> None
        
        Draws a background on the given area.
        """

    def DrawTab(self, dc: wx.DC, wnd: wx.Window, page: AuiNotebookPage, rect: wx.Rect, close_button_state: int, out_tab_rect: wx.Rect, out_button_rect: wx.Rect, x_extent: int) -> None:
        """
        DrawTab(dc, wnd, page, rect, close_button_state, out_tab_rect, out_button_rect, x_extent) -> None
        
        Draws a tab.
        """

    def DrawButton(self, dc: wx.DC, wnd: wx.Window, in_rect: wx.Rect, bitmap_id: int, button_state: int, orientation: int, out_rect: wx.Rect) -> None:
        """
        DrawButton(dc, wnd, in_rect, bitmap_id, button_state, orientation, out_rect) -> None
        
        Draws a button.
        """

    def GetIndentSize(self) -> int:
        """
        GetIndentSize() -> int
        
        Returns the indent size.
        """

    def GetTabSize(self, dc: wx.DC, wnd: wx.Window, caption: str, bitmap: wx.Bitmap, active: bool, closeButtonState: int, xExtent: int) -> wx.Size:
        """
        GetTabSize(dc, wnd, caption, bitmap, active, closeButtonState, xExtent) -> wx.Size
        """

    def ShowDropDown(self, wnd: wx.Window, items: AuiNotebookPageArray, activeIdx: int) -> int:
        """
        ShowDropDown(wnd, items, activeIdx) -> int
        """

    def GetBestTabCtrlSize(self, : wx.Size) -> int:
        """
        GetBestTabCtrlSize() -> int
        
        Returns the tab control size.
        """
    @property
    def IndentSize(self) -> int: ...
# end of class AuiSimpleTabArt


class AuiNotebookEvent(wx.BookCtrlEvent):
    """
    AuiNotebookEvent(command_type=wx.wxEVT_NULL, win_id=0) -> None
    
    This class is used by the events generated by wxAuiNotebook.
    """

    def __init__(self, command_type: EventType=wx.wxEVT_NULL, win_id: int=0) -> None:
        """
        AuiNotebookEvent(command_type=wx.wxEVT_NULL, win_id=0) -> None
        
        This class is used by the events generated by wxAuiNotebook.
        """

    def Clone(self) -> wx.Event:
        """
        Clone() -> wx.Event
        """
# end of class AuiNotebookEvent


EVT_AUINOTEBOOK_PAGE_CLOSE = wx.PyEventBinder( wxEVT_AUINOTEBOOK_PAGE_CLOSE, 1 )
EVT_AUINOTEBOOK_PAGE_CLOSED = wx.PyEventBinder( wxEVT_AUINOTEBOOK_PAGE_CLOSED, 1 )
EVT_AUINOTEBOOK_PAGE_CHANGED = wx.PyEventBinder( wxEVT_AUINOTEBOOK_PAGE_CHANGED, 1 )
EVT_AUINOTEBOOK_PAGE_CHANGING = wx.PyEventBinder( wxEVT_AUINOTEBOOK_PAGE_CHANGING, 1 )
EVT_AUINOTEBOOK_BUTTON = wx.PyEventBinder( wxEVT_AUINOTEBOOK_BUTTON, 1 )
EVT_AUINOTEBOOK_BEGIN_DRAG = wx.PyEventBinder( wxEVT_AUINOTEBOOK_BEGIN_DRAG, 1 )
EVT_AUINOTEBOOK_END_DRAG = wx.PyEventBinder( wxEVT_AUINOTEBOOK_END_DRAG, 1 )
EVT_AUINOTEBOOK_DRAG_MOTION = wx.PyEventBinder( wxEVT_AUINOTEBOOK_DRAG_MOTION, 1 )
EVT_AUINOTEBOOK_ALLOW_DND = wx.PyEventBinder( wxEVT_AUINOTEBOOK_ALLOW_DND, 1 )
EVT_AUINOTEBOOK_DRAG_DONE = wx.PyEventBinder( wxEVT_AUINOTEBOOK_DRAG_DONE, 1 )
EVT_AUINOTEBOOK_TAB_MIDDLE_DOWN = wx.PyEventBinder( wxEVT_AUINOTEBOOK_TAB_MIDDLE_DOWN, 1 )
EVT_AUINOTEBOOK_TAB_MIDDLE_UP = wx.PyEventBinder( wxEVT_AUINOTEBOOK_TAB_MIDDLE_UP, 1 )
EVT_AUINOTEBOOK_TAB_RIGHT_DOWN = wx.PyEventBinder( wxEVT_AUINOTEBOOK_TAB_RIGHT_DOWN, 1 )
EVT_AUINOTEBOOK_TAB_RIGHT_UP = wx.PyEventBinder( wxEVT_AUINOTEBOOK_TAB_RIGHT_UP, 1 )
EVT_AUINOTEBOOK_BG_DCLICK = wx.PyEventBinder( wxEVT_AUINOTEBOOK_BG_DCLICK, 1 )

class AuiTabCtrl(wx.Control, AuiTabContainer):
    """
    AuiTabCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
    """

    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> None:
        """
        AuiTabCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
        """

    def IsDragging(self) -> bool:
        """
        IsDragging() -> bool
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
# end of class AuiTabCtrl

#-- end-auibook --#
#-- begin-auitabmdi --#

class AuiMDIParentFrame(wx.Frame):
    """
    AuiMDIParentFrame() -> None
    AuiMDIParentFrame(parent, winid=wx.ID_ANY, title='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_FRAME_STYLE|wx.VSCROLL|wx.HSCROLL, name=wx.FrameNameStr) -> None
    """

    @overload
    def __init__(self, parent: Optional[wx.Window], winid: int=wx.ID_ANY, title: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.DEFAULT_FRAME_STYLE|wx.VSCROLL|wx.HSCROLL, name: str=wx.FrameNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        AuiMDIParentFrame() -> None
        AuiMDIParentFrame(parent, winid=wx.ID_ANY, title='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_FRAME_STYLE|wx.VSCROLL|wx.HSCROLL, name=wx.FrameNameStr) -> None
        """

    def Create(self, parent: wx.Window, winid: int=wx.ID_ANY, title: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.DEFAULT_FRAME_STYLE|wx.VSCROLL|wx.HSCROLL, name: str=wx.FrameNameStr) -> bool:
        """
        Create(parent, winid=wx.ID_ANY, title='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_FRAME_STYLE|wx.VSCROLL|wx.HSCROLL, name=wx.FrameNameStr) -> bool
        """

    def SetArtProvider(self, provider: AuiTabArt) -> None:
        """
        SetArtProvider(provider) -> None
        """

    def GetArtProvider(self) -> AuiTabArt:
        """
        GetArtProvider() -> AuiTabArt
        """

    def GetNotebook(self) -> AuiNotebook:
        """
        GetNotebook() -> AuiNotebook
        """

    def GetWindowMenu(self) -> wx.Menu:
        """
        GetWindowMenu() -> wx.Menu
        """

    def SetWindowMenu(self, pMenu: wx.Menu) -> None:
        """
        SetWindowMenu(pMenu) -> None
        """

    def SetMenuBar(self, menuBar: wx.MenuBar) -> None:
        """
        SetMenuBar(menuBar) -> None
        
        Tells the frame to show the given menu bar.
        """

    def SetChildMenuBar(self, pChild: AuiMDIChildFrame) -> None:
        """
        SetChildMenuBar(pChild) -> None
        """

    def GetActiveChild(self) -> AuiMDIChildFrame:
        """
        GetActiveChild() -> AuiMDIChildFrame
        """

    def SetActiveChild(self, pChildFrame: AuiMDIChildFrame) -> None:
        """
        SetActiveChild(pChildFrame) -> None
        """

    def GetClientWindow(self) -> AuiMDIClientWindow:
        """
        GetClientWindow() -> AuiMDIClientWindow
        """

    def OnCreateClient(self) -> AuiMDIClientWindow:
        """
        OnCreateClient() -> AuiMDIClientWindow
        """

    def Cascade(self) -> None:
        """
        Cascade() -> None
        """

    def Tile(self, orient: wx.Orientation=wx.HORIZONTAL) -> None:
        """
        Tile(orient=wx.HORIZONTAL) -> None
        """

    def ArrangeIcons(self) -> None:
        """
        ArrangeIcons() -> None
        """

    def ActivateNext(self) -> None:
        """
        ActivateNext() -> None
        """

    def ActivatePrevious(self) -> None:
        """
        ActivatePrevious() -> None
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ActiveChild(self) -> AuiMDIChildFrame: ...
    @ActiveChild.setter
    def ActiveChild(self, value: AuiMDIChildFrame, /) -> None: ...
    @property
    def ArtProvider(self) -> AuiTabArt: ...
    @ArtProvider.setter
    def ArtProvider(self, value: AuiTabArt, /) -> None: ...
    @property
    def ClientWindow(self) -> AuiMDIClientWindow: ...
    @property
    def Notebook(self) -> AuiNotebook: ...
    @property
    def WindowMenu(self) -> wx.Menu: ...
    @WindowMenu.setter
    def WindowMenu(self, value: wx.Menu, /) -> None: ...
# end of class AuiMDIParentFrame


class AuiMDIChildFrame(TDIChildFrame):
    """
    AuiMDIChildFrame() -> None
    AuiMDIChildFrame(parent, winid=wx.ID_ANY, title='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_FRAME_STYLE, name=wx.FrameNameStr) -> None
    """

    @overload
    def __init__(self, parent: AuiMDIParentFrame, winid: int=wx.ID_ANY, title: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.DEFAULT_FRAME_STYLE, name: str=wx.FrameNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        AuiMDIChildFrame() -> None
        AuiMDIChildFrame(parent, winid=wx.ID_ANY, title='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_FRAME_STYLE, name=wx.FrameNameStr) -> None
        """

    def Create(self, parent: AuiMDIParentFrame, winid: int=wx.ID_ANY, title: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.DEFAULT_FRAME_STYLE, name: str=wx.FrameNameStr) -> bool:
        """
        Create(parent, winid=wx.ID_ANY, title='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_FRAME_STYLE, name=wx.FrameNameStr) -> bool
        """

    def SetMenuBar(self, menuBar: wx.MenuBar) -> None:
        """
        SetMenuBar(menuBar) -> None
        """

    def GetMenuBar(self) -> wx.MenuBar:
        """
        GetMenuBar() -> wx.MenuBar
        """

    def SetTitle(self, title: str) -> None:
        """
        SetTitle(title) -> None
        """

    def GetTitle(self) -> str:
        """
        GetTitle() -> str
        """

    def SetIcons(self, icons: wx.IconBundle) -> None:
        """
        SetIcons(icons) -> None
        """

    def GetIcons(self) -> wx.IconBundle:
        """
        GetIcons() -> wx.IconBundle
        """

    def SetIcon(self, icon: wx.Icon) -> None:
        """
        SetIcon(icon) -> None
        """

    def GetIcon(self) -> wx.Icon:
        """
        GetIcon() -> wx.Icon
        """

    def Activate(self) -> None:
        """
        Activate() -> None
        """

    def Destroy(self) -> bool:
        """
        Destroy() -> bool
        
        Destroys the window safely.
        """

    def Show(self, show: bool=True) -> bool:
        """
        Show(show=True) -> bool
        
        Shows or hides the window.
        """

    def CreateStatusBar(self, number: int=1, style: int=1, winid: int=1, name: str='') -> wx.StatusBar:
        """
        CreateStatusBar(number=1, style=1, winid=1, name='') -> wx.StatusBar
        """

    def GetStatusBar(self) -> wx.StatusBar:
        """
        GetStatusBar() -> wx.StatusBar
        """

    def SetStatusText(self, text: str, number: int=0) -> None:
        """
        SetStatusText(text, number=0) -> None
        """

    def SetStatusWidths(self, widths: List[int]) -> None:
        """
        SetStatusWidths(widths) -> None
        """

    def CreateToolBar(self, style: int, winid: int, name: str) -> wx.ToolBar:
        """
        CreateToolBar(style, winid, name) -> wx.ToolBar
        """

    def GetToolBar(self) -> wx.ToolBar:
        """
        GetToolBar() -> wx.ToolBar
        """

    def Maximize(self, maximize: bool=True) -> None:
        """
        Maximize(maximize=True) -> None
        """

    def Restore(self) -> None:
        """
        Restore() -> None
        """

    def Iconize(self, iconize: bool=True) -> None:
        """
        Iconize(iconize=True) -> None
        """

    def IsMaximized(self) -> bool:
        """
        IsMaximized() -> bool
        """

    def IsIconized(self) -> bool:
        """
        IsIconized() -> bool
        """

    def ShowFullScreen(self, show: bool, style: int) -> bool:
        """
        ShowFullScreen(show, style) -> bool
        """

    def IsFullScreen(self) -> bool:
        """
        IsFullScreen() -> bool
        """

    def IsTopLevel(self) -> bool:
        """
        IsTopLevel() -> bool
        
        Returns true if the given window is a top-level one.
        """

    def SetMDIParentFrame(self, parent: AuiMDIParentFrame) -> None:
        """
        SetMDIParentFrame(parent) -> None
        """

    def GetMDIParentFrame(self) -> AuiMDIParentFrame:
        """
        GetMDIParentFrame() -> AuiMDIParentFrame
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Icon(self) -> wx.Icon: ...
    @Icon.setter
    def Icon(self, value: wx.Icon, /) -> None: ...
    @property
    def Icons(self) -> wx.IconBundle: ...
    @Icons.setter
    def Icons(self, value: wx.IconBundle, /) -> None: ...
    @property
    def MDIParentFrame(self) -> AuiMDIParentFrame: ...
    @MDIParentFrame.setter
    def MDIParentFrame(self, value: AuiMDIParentFrame, /) -> None: ...
    @property
    def MenuBar(self) -> wx.MenuBar: ...
    @MenuBar.setter
    def MenuBar(self, value: wx.MenuBar, /) -> None: ...
    @property
    def StatusBar(self) -> wx.StatusBar: ...
    @property
    def Title(self) -> str: ...
    @Title.setter
    def Title(self, value: str, /) -> None: ...
    @property
    def ToolBar(self) -> wx.ToolBar: ...
# end of class AuiMDIChildFrame


class AuiMDIClientWindow(AuiNotebook):
    """
    AuiMDIClientWindow() -> None
    AuiMDIClientWindow(parent, style=0) -> None
    """

    @overload
    def __init__(self, parent: AuiMDIParentFrame, style: int=0) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        AuiMDIClientWindow() -> None
        AuiMDIClientWindow(parent, style=0) -> None
        """

    def CreateClient(self, parent: AuiMDIParentFrame, style: int=wx.VSCROLL|wx.HSCROLL) -> bool:
        """
        CreateClient(parent, style=wx.VSCROLL|wx.HSCROLL) -> bool
        """

    def SetSelection(self, new_page: int) -> int:
        """
        SetSelection(new_page) -> int
        
        Sets the page selection.
        """

    def GetActiveChild(self) -> AuiMDIChildFrame:
        """
        GetActiveChild() -> AuiMDIChildFrame
        """

    def SetActiveChild(self, pChildFrame: AuiMDIChildFrame) -> None:
        """
        SetActiveChild(pChildFrame) -> None
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ActiveChild(self) -> AuiMDIChildFrame: ...
    @ActiveChild.setter
    def ActiveChild(self, value: AuiMDIChildFrame, /) -> None: ...
# end of class AuiMDIClientWindow

#-- end-auitabmdi --#
