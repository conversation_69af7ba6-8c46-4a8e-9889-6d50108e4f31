// qvariant.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QVariant /AllowNone,TypeHint="Any",TypeHintValue="None"/
{
%TypeHeaderCode
#include <qvariant.h>
%End

%ConvertToTypeCode
if (sipIsErr == NULL)
    // We can convert everything to a QVariant.
    return 1;

// If it is already a QVariant then just return it.
if (Py_TYPE(sipPy) == sipTypeAsPyTypeObject(sipType_QVariant))
{
    *sipCppPtr = reinterpret_cast<QVariant *>(sipConvertToType(sipPy,
            sipType_QVariant, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

    return 0;
}

// Convert it to a QVariant.
QVariant var = qpycore_PyObject_AsQVariant(sipPy, sipIsErr);

if (*sipIsErr)
    return 0;

*sipCppPtr = new QVariant(var);

return sipGetState(sipTransferObj);
%End

%ConvertFromTypeCode
return qpycore_PyObject_FromQVariant(*sipCpp);
%End

public:
    enum Type
    {
        Invalid,
        Bool,
        Int,
        UInt,
        LongLong,
        ULongLong,
        Double,
        Char,
        Map,
        List,
        String,
        StringList,
        ByteArray,
        BitArray,
        Date,
        Time,
        DateTime,
        Url,
        Locale,
        Rect,
        RectF,
        Size,
        SizeF,
        Line,
        LineF,
        Point,
        PointF,
        RegExp,
        Font,
        Pixmap,
        Brush,
        Color,
        Palette,
        Icon,
        Image,
        Polygon,
        Region,
        Bitmap,
        Cursor,
        SizePolicy,
        KeySequence,
        Pen,
        TextLength,
        TextFormat,
        Matrix,
        Transform,
        Hash,
        Matrix4x4,
        Vector2D,
        Vector3D,
        Vector4D,
        Quaternion,
        EasingCurve,
        Uuid,
        ModelIndex,
        PolygonF,
        RegularExpression,
%If (Qt_5_5_0 -)
        PersistentModelIndex,
%End
        UserType,
    };

    QVariant();
    QVariant(QVariant::Type type /Constrained/);
    QVariant(SIP_PYOBJECT obj);
%MethodCode
        int is_err = 0;
        QVariant var = qpycore_PyObject_AsQVariant(a0, &is_err);
        
        if (is_err)
            sipCpp = 0;
        else
            sipCpp = new QVariant(var);
%End

    ~QVariant();
    SIP_PYOBJECT value() const;
%MethodCode
        sipRes = qpycore_PyObject_FromQVariant(*sipCpp);
%End

    QVariant::Type type() const;
    int userType() const;
    const char *typeName() const;
    bool canConvert(int targetTypeId) const;
    bool convert(int targetTypeId);
    bool isValid() const;
    bool isNull() const;
    void clear();
    void load(QDataStream &ds) /ReleaseGIL/;
    void save(QDataStream &ds) const /ReleaseGIL/;
    static const char *typeToName(int typeId);
    static QVariant::Type nameToType(const char *name);
    bool operator==(const QVariant &v) const;
    bool operator!=(const QVariant &v) const;
    void swap(QVariant &other /Constrained/);
%If (Qt_5_2_0 -)
    bool operator<(const QVariant &v) const;
%End
%If (Qt_5_2_0 -)
    bool operator<=(const QVariant &v) const;
%End
%If (Qt_5_2_0 -)
    bool operator>(const QVariant &v) const;
%End
%If (Qt_5_2_0 -)
    bool operator>=(const QVariant &v) const;
%End
};

typedef QList<QVariant> QVariantList /TypeHint="List[QVariant]"/;
typedef QHash<QString, QVariant> QVariantHash /TypeHint="Dict[QString, QVariant]"/;
QDataStream &operator>>(QDataStream &s, QVariant &p /Constrained/) /ReleaseGIL/;
QDataStream &operator<<(QDataStream &s, const QVariant &p /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &s, QVariant::Type &p /Constrained,In/) /ReleaseGIL/;
QDataStream &operator<<(QDataStream &s, const QVariant::Type p /Constrained/) /ReleaseGIL/;
