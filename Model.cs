﻿using Aratek.TrustFinger;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.Common;
using System.Data.Entity;
using System.ComponentModel.DataAnnotations;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.ComponentModel.DataAnnotations.Schema;
using MySql.Data.MySqlClient;
using System.Data.Entity.ModelConfiguration;

namespace MultiFingerDemo
{
    public class DeviceItem
    {
        public string Name { get; set; }
        public int DeviceIndex { get; set; }
    }


    public class CaptureFingerprintItem
    {
        public FingerPosition FingerPosition { get; set; }
        public byte[] Bitmap { get; set; }
        public byte[] Fearture { get; set; }
        public int Qulity { get; set; }
       
    }

    public class IdentifyUser
    {
        public string UserID { get; set; }

        public string UserName { get; set; }
        
        public int Score { get; set; }
        
        public int fingerposition { get; set; }

        public bool IsMatch { get; set; }

        public string FingerPositionName { get; set; }
    }

    [Table("enrollusers")]
    public class EnrollUser :EventArgs
    {
        public int Id { get; set; }
        public string UserId { get; set; }            // Links to prison management system
        public DateTime CreatedTime { get; set; }
        public int OperationType { get; set; }
        public int FingerPosition { get; set; }
        public string FingerPositionName { get; set; }
        public byte[] FingerData { get; set; }        // Template data (for matching)
        public byte[] FingerImage { get; set; }       // BMP image data (for display)
        public int? ImageQuality { get; set; }        // Image quality score (nullable for backward compatibility)
        // UserName removed - will be in prison management database
    }

    public class TrustFingerDbContext : DbContext
    {
        public TrustFingerDbContext()
            : base(new MySqlConnection(GetConnectionString()), true)  // Explicitly use MySQL connection
        {
            // MySQL database initializer
            Database.SetInitializer(new CreateDatabaseIfNotExists<TrustFingerDbContext>());
        }

        private static string GetConnectionString()
        {
            try
            {
                // Read from config.sys using ConfigManager
                string connectionString = ConfigManager.GetDatabaseConnectionString();
                Console.WriteLine($"DEBUG: Connection string from config.sys: {connectionString}");
                return connectionString;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading config.sys, falling back to App.config: {ex.Message}");
                // Fallback to App.config if config.sys fails
                string fallbackConnection = ConfigurationManager.ConnectionStrings["trustfinger"].ConnectionString;
                Console.WriteLine($"DEBUG: Fallback connection string: {fallbackConnection}");
                return fallbackConnection;
            }
        }

        public virtual DbSet<EnrollUser> EnrollUser { get; set; }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure EnrollUser table
            modelBuilder.Entity<EnrollUser>()
                .ToTable("enrollusers");

            // Id configuration is now handled by attributes on the property

            modelBuilder.Entity<EnrollUser>()
                .Property(e => e.UserId)
                .HasMaxLength(50);

            // Configure FingerData for MySQL
            modelBuilder.Entity<EnrollUser>()
                .Property(e => e.FingerData)
                .HasColumnType("longblob")
                .IsRequired();

            // Configure FingerImage for MySQL
            modelBuilder.Entity<EnrollUser>()
                .Property(e => e.FingerImage)
                .HasColumnType("longblob")
                .IsOptional();

            modelBuilder.Entity<EnrollUser>()
                .Property(e => e.FingerPositionName)
                .HasMaxLength(50);

            // Configure ImageQuality to allow NULL values
            modelBuilder.Entity<EnrollUser>()
                .Property(e => e.ImageQuality)
                .IsOptional();
        }
    }



}
