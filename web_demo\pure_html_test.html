<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Pure HTML Test - No JavaScript</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .test-section {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .test-section h3 {
      margin-top: 0;
      color: #333;
    }
    button {
      padding: 10px 20px;
      margin: 10px;
      border: 1px solid #ccc;
      background: #f8f9fa;
      cursor: pointer;
      border-radius: 3px;
    }
    button:hover {
      background: #e9ecef;
    }
    .success {
      color: green;
      font-weight: bold;
    }
    .info {
      color: blue;
      font-style: italic;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Pure HTML Test - No JavaScript</h1>
    <p class="info">This page contains NO JavaScript whatsoever. If CSP errors still appear, the issue is with WAMP configuration.</p>

    <div class="test-section">
      <h3>Test 1: Basic HTML</h3>
      <p class="success">✅ If you can see this, basic HTML works fine.</p>
    </div>

    <div class="test-section">
      <h3>Test 2: CSS Styling</h3>
      <p class="success">✅ If this text is green and bold, CSS works fine.</p>
    </div>

    <div class="test-section">
      <h3>Test 3: Form Elements</h3>
      <form>
        <label for="testInput">Test Input:</label>
        <input type="text" id="testInput" placeholder="Type something here">
        <br><br>
        <label for="testSelect">Test Select:</label>
        <select id="testSelect">
          <option value="1">Option 1</option>
          <option value="2">Option 2</option>
        </select>
        <br><br>
        <button type="button">Test Button (No JavaScript)</button>
      </form>
    </div>

    <div class="test-section">
      <h3>Test 4: Images and Media</h3>
      <p>Testing if media elements work:</p>
      <!-- Using a simple data URI image to avoid external dependencies -->
      <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzAwN2JmZiIvPgogIDx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VEVTVDwvdGV4dD4KICA8L3N2Zz4K" alt="Test Image">
      <p class="success">✅ If you see a blue square with "TEST" text above, images work fine.</p>
    </div>

    <div class="test-section">
      <h3>Test 5: Check Browser Console</h3>
      <p><strong>Instructions:</strong></p>
      <ol>
        <li>Open browser developer tools (F12)</li>
        <li>Go to the Console tab</li>
        <li>Look for any CSP (Content Security Policy) errors</li>
        <li>If you see CSP errors on this page, the issue is WAMP configuration</li>
        <li>If you see NO CSP errors on this page, the issue is with JavaScript in other pages</li>
      </ol>
    </div>

    <div class="test-section">
      <h3>Test Results</h3>
      <p><strong>Expected Result:</strong> NO CSP errors in console</p>
      <p><strong>If CSP errors appear:</strong> The issue is WAMP Apache configuration</p>
      <p><strong>If NO CSP errors:</strong> The issue is specific JavaScript features in other pages</p>
    </div>

    <div class="test-section">
      <h3>Next Steps</h3>
      <p><strong>If this page shows CSP errors:</strong></p>
      <ul>
        <li>Create .htaccess file to disable CSP</li>
        <li>Modify Apache httpd.conf</li>
        <li>Check WAMP security modules</li>
      </ul>
      
      <p><strong>If this page shows NO CSP errors:</strong></p>
      <ul>
        <li>The issue is with specific JavaScript features</li>
        <li>Use the inline event handler approach</li>
        <li>Avoid modern JavaScript features</li>
      </ul>
    </div>
  </div>
</body>
</html>
