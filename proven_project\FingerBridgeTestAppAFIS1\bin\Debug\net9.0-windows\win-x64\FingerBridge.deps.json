{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {}, ".NETCoreApp,Version=v9.0/win-x64": {"FingerBridge/1.0.0": {"dependencies": {"MySqlConnector": "2.4.0", "SourceAFIS": "3.14.0", "System.Drawing.Common": "9.0.5", "Bio.TrustFinger": "*******"}, "runtime": {"FingerBridge.dll": {}}}, "Dahomey.Cbor/1.16.1": {"dependencies": {"System.IO.Pipelines": "6.0.1"}, "runtime": {"lib/net6.0/Dahomey.Cbor.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.Win32.SystemEvents/9.0.5": {"runtime": {"runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "MySqlConnector/2.4.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "runtime": {"lib/net9.0/MySqlConnector.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.4.0.0"}}}, "SixLabors.ImageSharp/2.1.3": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/SixLabors.ImageSharp.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.1.3.0"}}}, "SourceAFIS/3.14.0": {"dependencies": {"Dahomey.Cbor": "1.16.1", "SixLabors.ImageSharp": "2.1.3"}, "runtime": {"lib/net5.0/SourceAFIS.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "System.Drawing.Common/9.0.5": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.5"}, "runtime": {"lib/net9.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21601"}, "lib/net9.0/System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21601"}}}, "System.IO.Pipelines/6.0.1": {}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {}, "System.Text.Encoding.CodePages/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}}, "Bio.TrustFinger/*******": {"runtime": {"Bio.TrustFinger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"FingerBridge/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dahomey.Cbor/1.16.1": {"type": "package", "serviceable": true, "sha512": "sha512-NeFvt9CXi5Ew6ZnpjQsO4RoKfZiSZH3Ap7PCdqHryr/RL5WrrPl1+JbXny8ynNEmb6/1wDfT1F5I9fbPN6H84w==", "path": "dahomey.cbor/1.16.1", "hashPath": "dahomey.cbor.1.16.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "path": "microsoft.extensions.logging.abstractions/8.0.2", "hashPath": "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-D4OYNpmvAsF9MkaY2W8Jue2XuNHDhygvwzo019hs+lP85KaVnOlXmqsjDKr1dHb1DPxDnOKpe6mAgJN7S6ttwg==", "path": "microsoft.win32.systemevents/9.0.5", "hashPath": "microsoft.win32.systemevents.9.0.5.nupkg.sha512"}, "MySqlConnector/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-78M+gVOjbdZEDIyXQqcA7EYlCGS3tpbUELHvn6638A2w0pkPI625ixnzsa5staAd3N9/xFmPJtkKDYwsXpFi/w==", "path": "mysqlconnector/2.4.0", "hashPath": "mysqlconnector.2.4.0.nupkg.sha512"}, "SixLabors.ImageSharp/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-8yonNRWX3vUE9k29ta0Hbfa0AEc0hbDjSH/nZ3vOTJEXmY6hLnGsjDKoz96Z+AgOsrdkAu6PdL/Ebaf70aitzw==", "path": "sixlabors.imagesharp/2.1.3", "hashPath": "sixlabors.imagesharp.2.1.3.nupkg.sha512"}, "SourceAFIS/3.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-TLTICwNMqBROeEAa4JNeY5eW3PyBawg6hrSqqEZ/45RtM0+e6oVHIzL4AYipF1SC2BlKh+ofhgxnuaDmPqMzuA==", "path": "sourceafis/3.14.0", "hashPath": "sourceafis.3.14.0.nupkg.sha512"}, "System.Drawing.Common/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-T/6nqx0B7/uTe5JjBwrKZilLuwfhHLOVmNKlT/wr4A9Dna94mgTdz3lTfrdJ72QRx7IHCv/LzoJPmFSfK/N6WA==", "path": "system.drawing.common/9.0.5", "hashPath": "system.drawing.common.9.0.5.nupkg.sha512"}, "System.IO.Pipelines/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-1j1pZX1ICzEyjafyssU3BzWVxUsaN3zq0wEmGKj3bxINC6NkmS30eEVuZ7k/QX2UKYNCaUch6g46taYKxcpFtw==", "path": "system.io.pipelines/6.0.1", "hashPath": "system.io.pipelines.6.0.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "path": "system.text.encoding.codepages/5.0.0", "hashPath": "system.text.encoding.codepages.5.0.0.nupkg.sha512"}, "Bio.TrustFinger/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}