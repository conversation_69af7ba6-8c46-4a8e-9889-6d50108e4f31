# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['fingerprintapi.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('fingerprintAPI.ico', '.'),
        ('app.py', '.'),
        ('capture.py', '.'),
        ('match.py', '.'),
        ('verify.py', '.')
    ],
    hiddenimports=[
        'pymysql', 
        'flask_cors', 
        'capture', 
        'verify', 
        'match',
        'pystray',
        'PIL',
        'pystray._win32',
        'pystray._darwin',
        'pystray._xorg',
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'PyQt5.sip'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='FingerprintAPI',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='fingerprintAPI.ico'
)
