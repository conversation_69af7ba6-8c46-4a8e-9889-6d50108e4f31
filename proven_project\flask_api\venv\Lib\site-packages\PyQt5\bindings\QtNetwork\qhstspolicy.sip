// qhstspolicy.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_9_0 -)

class QHstsPolicy
{
%TypeHeaderCode
#include <qhstspolicy.h>
%End

public:
    enum PolicyFlag
    {
        IncludeSubDomains,
    };

    typedef QFlags<QHstsPolicy::PolicyFlag> PolicyFlags;
    QHstsPolicy();
    QHstsPolicy(const QDateTime &expiry, QHstsPolicy::PolicyFlags flags, const QString &host, QUrl::ParsingMode mode = QUrl::DecodedMode);
    QHstsPolicy(const QHstsPolicy &rhs);
    ~QHstsPolicy();
    void swap(QHstsPolicy &other);
    void setHost(const QString &host, QUrl::ParsingMode mode = QUrl::DecodedMode);
    QString host(QUrl::ComponentFormattingOptions options = QUrl::ComponentFormattingOption::FullyDecoded) const;
    void setExpiry(const QDateTime &expiry);
    QDateTime expiry() const;
    void setIncludesSubDomains(bool include);
    bool includesSubDomains() const;
    bool isExpired() const;
};

%End
%If (Qt_5_9_0 -)
bool operator==(const QHstsPolicy &lhs, const QHstsPolicy &rhs);
%End
%If (Qt_5_9_0 -)
bool operator!=(const QHstsPolicy &lhs, const QHstsPolicy &rhs);
%End
%If (Qt_5_9_0 -)
QFlags<QHstsPolicy::PolicyFlag> operator|(QHstsPolicy::PolicyFlag f1, QFlags<QHstsPolicy::PolicyFlag> f2);
%End
