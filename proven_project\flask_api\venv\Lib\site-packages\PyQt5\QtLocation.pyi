# The PEP 484 type hints stub file for the QtLocation module.
#
# Generated by SIP 6.8.6
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt5.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import typing

import PyQt5.sip

from PyQt5 import QtCore
from PyQt5 import QtPositioning

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., Any], QtCore.pyqtBoundSignal]


class QGeoCodeReply(QtCore.QObject):

    class Error(int):
        NoError = ... # type: QGeoCodeReply.Error
        EngineNotSetError = ... # type: QGeoCodeReply.Error
        CommunicationError = ... # type: QGeoCodeReply.Error
        ParseError = ... # type: QGeoCodeReply.Error
        UnsupportedOptionError = ... # type: QGeoCodeReply.Error
        CombinationError = ... # type: QGeoCodeReply.Error
        UnknownError = ... # type: QGeoCodeReply.Error

    @typing.overload
    def __init__(self, error: 'QGeoCodeReply.Error', errorString: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setOffset(self, offset: int) -> None: ...
    def setLimit(self, limit: int) -> None: ...
    def setLocations(self, locations: typing.Iterable[QtPositioning.QGeoLocation]) -> None: ...
    def addLocation(self, location: QtPositioning.QGeoLocation) -> None: ...
    def setViewport(self, viewport: QtPositioning.QGeoShape) -> None: ...
    def setFinished(self, finished: bool) -> None: ...
    def setError(self, error: 'QGeoCodeReply.Error', errorString: typing.Optional[str]) -> None: ...
    finished: typing.ClassVar[QtCore.pyqtSignal]
    aborted: typing.ClassVar[QtCore.pyqtSignal]
    def abort(self) -> None: ...
    def offset(self) -> int: ...
    def limit(self) -> int: ...
    def locations(self) -> typing.List[QtPositioning.QGeoLocation]: ...
    def viewport(self) -> QtPositioning.QGeoShape: ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def isFinished(self) -> bool: ...


class QGeoCodingManager(QtCore.QObject):

    error: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    def locale(self) -> QtCore.QLocale: ...
    def setLocale(self, locale: QtCore.QLocale) -> None: ...
    def reverseGeocode(self, coordinate: QtPositioning.QGeoCoordinate, bounds: QtPositioning.QGeoShape = ...) -> typing.Optional[QGeoCodeReply]: ...
    @typing.overload
    def geocode(self, address: QtPositioning.QGeoAddress, bounds: QtPositioning.QGeoShape = ...) -> typing.Optional[QGeoCodeReply]: ...
    @typing.overload
    def geocode(self, searchString: typing.Optional[str], limit: int = ..., offset: int = ..., bounds: QtPositioning.QGeoShape = ...) -> typing.Optional[QGeoCodeReply]: ...
    def managerVersion(self) -> int: ...
    def managerName(self) -> str: ...


class QGeoCodingManagerEngine(QtCore.QObject):

    def __init__(self, parameters: typing.Dict[str, typing.Any], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    error: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    def locale(self) -> QtCore.QLocale: ...
    def setLocale(self, locale: QtCore.QLocale) -> None: ...
    def reverseGeocode(self, coordinate: QtPositioning.QGeoCoordinate, bounds: QtPositioning.QGeoShape) -> typing.Optional[QGeoCodeReply]: ...
    @typing.overload
    def geocode(self, address: QtPositioning.QGeoAddress, bounds: QtPositioning.QGeoShape) -> typing.Optional[QGeoCodeReply]: ...
    @typing.overload
    def geocode(self, address: typing.Optional[str], limit: int, offset: int, bounds: QtPositioning.QGeoShape) -> typing.Optional[QGeoCodeReply]: ...
    def managerVersion(self) -> int: ...
    def managerName(self) -> str: ...


class QGeoManeuver(PyQt5.sipsimplewrapper):

    class InstructionDirection(int):
        NoDirection = ... # type: QGeoManeuver.InstructionDirection
        DirectionForward = ... # type: QGeoManeuver.InstructionDirection
        DirectionBearRight = ... # type: QGeoManeuver.InstructionDirection
        DirectionLightRight = ... # type: QGeoManeuver.InstructionDirection
        DirectionRight = ... # type: QGeoManeuver.InstructionDirection
        DirectionHardRight = ... # type: QGeoManeuver.InstructionDirection
        DirectionUTurnRight = ... # type: QGeoManeuver.InstructionDirection
        DirectionUTurnLeft = ... # type: QGeoManeuver.InstructionDirection
        DirectionHardLeft = ... # type: QGeoManeuver.InstructionDirection
        DirectionLeft = ... # type: QGeoManeuver.InstructionDirection
        DirectionLightLeft = ... # type: QGeoManeuver.InstructionDirection
        DirectionBearLeft = ... # type: QGeoManeuver.InstructionDirection

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QGeoManeuver') -> None: ...

    def extendedAttributes(self) -> typing.Dict[str, typing.Any]: ...
    def setExtendedAttributes(self, extendedAttributes: typing.Dict[str, typing.Any]) -> None: ...
    def waypoint(self) -> QtPositioning.QGeoCoordinate: ...
    def setWaypoint(self, coordinate: QtPositioning.QGeoCoordinate) -> None: ...
    def distanceToNextInstruction(self) -> float: ...
    def setDistanceToNextInstruction(self, distance: float) -> None: ...
    def timeToNextInstruction(self) -> int: ...
    def setTimeToNextInstruction(self, secs: int) -> None: ...
    def direction(self) -> 'QGeoManeuver.InstructionDirection': ...
    def setDirection(self, direction: 'QGeoManeuver.InstructionDirection') -> None: ...
    def instructionText(self) -> str: ...
    def setInstructionText(self, instructionText: typing.Optional[str]) -> None: ...
    def position(self) -> QtPositioning.QGeoCoordinate: ...
    def setPosition(self, position: QtPositioning.QGeoCoordinate) -> None: ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QGeoRoute(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QGeoRoute') -> None: ...

    def extendedAttributes(self) -> typing.Dict[str, typing.Any]: ...
    def setExtendedAttributes(self, extendedAttributes: typing.Dict[str, typing.Any]) -> None: ...
    def routeLegs(self) -> typing.List['QGeoRouteLeg']: ...
    def setRouteLegs(self, legs: typing.Iterable['QGeoRouteLeg']) -> None: ...
    def path(self) -> typing.List[QtPositioning.QGeoCoordinate]: ...
    def setPath(self, path: typing.Iterable[QtPositioning.QGeoCoordinate]) -> None: ...
    def travelMode(self) -> 'QGeoRouteRequest.TravelMode': ...
    def setTravelMode(self, mode: 'QGeoRouteRequest.TravelMode') -> None: ...
    def distance(self) -> float: ...
    def setDistance(self, distance: float) -> None: ...
    def travelTime(self) -> int: ...
    def setTravelTime(self, secs: int) -> None: ...
    def firstRouteSegment(self) -> 'QGeoRouteSegment': ...
    def setFirstRouteSegment(self, routeSegment: 'QGeoRouteSegment') -> None: ...
    def bounds(self) -> QtPositioning.QGeoRectangle: ...
    def setBounds(self, bounds: QtPositioning.QGeoRectangle) -> None: ...
    def request(self) -> 'QGeoRouteRequest': ...
    def setRequest(self, request: 'QGeoRouteRequest') -> None: ...
    def routeId(self) -> str: ...
    def setRouteId(self, id: typing.Optional[str]) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QGeoRouteLeg(QGeoRoute):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QGeoRouteLeg') -> None: ...

    def overallRoute(self) -> QGeoRoute: ...
    def setOverallRoute(self, route: QGeoRoute) -> None: ...
    def legIndex(self) -> int: ...
    def setLegIndex(self, idx: int) -> None: ...


class QGeoRouteReply(QtCore.QObject):

    class Error(int):
        NoError = ... # type: QGeoRouteReply.Error
        EngineNotSetError = ... # type: QGeoRouteReply.Error
        CommunicationError = ... # type: QGeoRouteReply.Error
        ParseError = ... # type: QGeoRouteReply.Error
        UnsupportedOptionError = ... # type: QGeoRouteReply.Error
        UnknownError = ... # type: QGeoRouteReply.Error

    @typing.overload
    def __init__(self, error: 'QGeoRouteReply.Error', errorString: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, request: 'QGeoRouteRequest', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def addRoutes(self, routes: typing.Iterable[QGeoRoute]) -> None: ...
    def setRoutes(self, routes: typing.Iterable[QGeoRoute]) -> None: ...
    def setFinished(self, finished: bool) -> None: ...
    def setError(self, error: 'QGeoRouteReply.Error', errorString: typing.Optional[str]) -> None: ...
    finished: typing.ClassVar[QtCore.pyqtSignal]
    aborted: typing.ClassVar[QtCore.pyqtSignal]
    def abort(self) -> None: ...
    def routes(self) -> typing.List[QGeoRoute]: ...
    def request(self) -> 'QGeoRouteRequest': ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def isFinished(self) -> bool: ...


class QGeoRouteRequest(PyQt5.sipsimplewrapper):

    class ManeuverDetail(int):
        NoManeuvers = ... # type: QGeoRouteRequest.ManeuverDetail
        BasicManeuvers = ... # type: QGeoRouteRequest.ManeuverDetail

    class SegmentDetail(int):
        NoSegmentData = ... # type: QGeoRouteRequest.SegmentDetail
        BasicSegmentData = ... # type: QGeoRouteRequest.SegmentDetail

    class RouteOptimization(int):
        ShortestRoute = ... # type: QGeoRouteRequest.RouteOptimization
        FastestRoute = ... # type: QGeoRouteRequest.RouteOptimization
        MostEconomicRoute = ... # type: QGeoRouteRequest.RouteOptimization
        MostScenicRoute = ... # type: QGeoRouteRequest.RouteOptimization

    class FeatureWeight(int):
        NeutralFeatureWeight = ... # type: QGeoRouteRequest.FeatureWeight
        PreferFeatureWeight = ... # type: QGeoRouteRequest.FeatureWeight
        RequireFeatureWeight = ... # type: QGeoRouteRequest.FeatureWeight
        AvoidFeatureWeight = ... # type: QGeoRouteRequest.FeatureWeight
        DisallowFeatureWeight = ... # type: QGeoRouteRequest.FeatureWeight

    class FeatureType(int):
        NoFeature = ... # type: QGeoRouteRequest.FeatureType
        TollFeature = ... # type: QGeoRouteRequest.FeatureType
        HighwayFeature = ... # type: QGeoRouteRequest.FeatureType
        PublicTransitFeature = ... # type: QGeoRouteRequest.FeatureType
        FerryFeature = ... # type: QGeoRouteRequest.FeatureType
        TunnelFeature = ... # type: QGeoRouteRequest.FeatureType
        DirtRoadFeature = ... # type: QGeoRouteRequest.FeatureType
        ParksFeature = ... # type: QGeoRouteRequest.FeatureType
        MotorPoolLaneFeature = ... # type: QGeoRouteRequest.FeatureType
        TrafficFeature = ... # type: QGeoRouteRequest.FeatureType

    class TravelMode(int):
        CarTravel = ... # type: QGeoRouteRequest.TravelMode
        PedestrianTravel = ... # type: QGeoRouteRequest.TravelMode
        BicycleTravel = ... # type: QGeoRouteRequest.TravelMode
        PublicTransitTravel = ... # type: QGeoRouteRequest.TravelMode
        TruckTravel = ... # type: QGeoRouteRequest.TravelMode

    class TravelModes(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QGeoRouteRequest.TravelModes', 'QGeoRouteRequest.TravelMode']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QGeoRouteRequest.TravelModes', 'QGeoRouteRequest.TravelMode']) -> 'QGeoRouteRequest.TravelModes': ...
        def __xor__(self, f: typing.Union['QGeoRouteRequest.TravelModes', 'QGeoRouteRequest.TravelMode']) -> 'QGeoRouteRequest.TravelModes': ...
        def __ior__(self, f: typing.Union['QGeoRouteRequest.TravelModes', 'QGeoRouteRequest.TravelMode']) -> 'QGeoRouteRequest.TravelModes': ...
        def __or__(self, f: typing.Union['QGeoRouteRequest.TravelModes', 'QGeoRouteRequest.TravelMode']) -> 'QGeoRouteRequest.TravelModes': ...
        def __iand__(self, f: typing.Union['QGeoRouteRequest.TravelModes', 'QGeoRouteRequest.TravelMode']) -> 'QGeoRouteRequest.TravelModes': ...
        def __and__(self, f: typing.Union['QGeoRouteRequest.TravelModes', 'QGeoRouteRequest.TravelMode']) -> 'QGeoRouteRequest.TravelModes': ...
        def __invert__(self) -> 'QGeoRouteRequest.TravelModes': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class FeatureTypes(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QGeoRouteRequest.FeatureTypes', 'QGeoRouteRequest.FeatureType']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QGeoRouteRequest.FeatureTypes', 'QGeoRouteRequest.FeatureType']) -> 'QGeoRouteRequest.FeatureTypes': ...
        def __xor__(self, f: typing.Union['QGeoRouteRequest.FeatureTypes', 'QGeoRouteRequest.FeatureType']) -> 'QGeoRouteRequest.FeatureTypes': ...
        def __ior__(self, f: typing.Union['QGeoRouteRequest.FeatureTypes', 'QGeoRouteRequest.FeatureType']) -> 'QGeoRouteRequest.FeatureTypes': ...
        def __or__(self, f: typing.Union['QGeoRouteRequest.FeatureTypes', 'QGeoRouteRequest.FeatureType']) -> 'QGeoRouteRequest.FeatureTypes': ...
        def __iand__(self, f: typing.Union['QGeoRouteRequest.FeatureTypes', 'QGeoRouteRequest.FeatureType']) -> 'QGeoRouteRequest.FeatureTypes': ...
        def __and__(self, f: typing.Union['QGeoRouteRequest.FeatureTypes', 'QGeoRouteRequest.FeatureType']) -> 'QGeoRouteRequest.FeatureTypes': ...
        def __invert__(self) -> 'QGeoRouteRequest.FeatureTypes': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class FeatureWeights(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QGeoRouteRequest.FeatureWeights', 'QGeoRouteRequest.FeatureWeight']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QGeoRouteRequest.FeatureWeights', 'QGeoRouteRequest.FeatureWeight']) -> 'QGeoRouteRequest.FeatureWeights': ...
        def __xor__(self, f: typing.Union['QGeoRouteRequest.FeatureWeights', 'QGeoRouteRequest.FeatureWeight']) -> 'QGeoRouteRequest.FeatureWeights': ...
        def __ior__(self, f: typing.Union['QGeoRouteRequest.FeatureWeights', 'QGeoRouteRequest.FeatureWeight']) -> 'QGeoRouteRequest.FeatureWeights': ...
        def __or__(self, f: typing.Union['QGeoRouteRequest.FeatureWeights', 'QGeoRouteRequest.FeatureWeight']) -> 'QGeoRouteRequest.FeatureWeights': ...
        def __iand__(self, f: typing.Union['QGeoRouteRequest.FeatureWeights', 'QGeoRouteRequest.FeatureWeight']) -> 'QGeoRouteRequest.FeatureWeights': ...
        def __and__(self, f: typing.Union['QGeoRouteRequest.FeatureWeights', 'QGeoRouteRequest.FeatureWeight']) -> 'QGeoRouteRequest.FeatureWeights': ...
        def __invert__(self) -> 'QGeoRouteRequest.FeatureWeights': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class RouteOptimizations(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QGeoRouteRequest.RouteOptimizations', 'QGeoRouteRequest.RouteOptimization']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QGeoRouteRequest.RouteOptimizations', 'QGeoRouteRequest.RouteOptimization']) -> 'QGeoRouteRequest.RouteOptimizations': ...
        def __xor__(self, f: typing.Union['QGeoRouteRequest.RouteOptimizations', 'QGeoRouteRequest.RouteOptimization']) -> 'QGeoRouteRequest.RouteOptimizations': ...
        def __ior__(self, f: typing.Union['QGeoRouteRequest.RouteOptimizations', 'QGeoRouteRequest.RouteOptimization']) -> 'QGeoRouteRequest.RouteOptimizations': ...
        def __or__(self, f: typing.Union['QGeoRouteRequest.RouteOptimizations', 'QGeoRouteRequest.RouteOptimization']) -> 'QGeoRouteRequest.RouteOptimizations': ...
        def __iand__(self, f: typing.Union['QGeoRouteRequest.RouteOptimizations', 'QGeoRouteRequest.RouteOptimization']) -> 'QGeoRouteRequest.RouteOptimizations': ...
        def __and__(self, f: typing.Union['QGeoRouteRequest.RouteOptimizations', 'QGeoRouteRequest.RouteOptimization']) -> 'QGeoRouteRequest.RouteOptimizations': ...
        def __invert__(self) -> 'QGeoRouteRequest.RouteOptimizations': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class SegmentDetails(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QGeoRouteRequest.SegmentDetails', 'QGeoRouteRequest.SegmentDetail']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QGeoRouteRequest.SegmentDetails', 'QGeoRouteRequest.SegmentDetail']) -> 'QGeoRouteRequest.SegmentDetails': ...
        def __xor__(self, f: typing.Union['QGeoRouteRequest.SegmentDetails', 'QGeoRouteRequest.SegmentDetail']) -> 'QGeoRouteRequest.SegmentDetails': ...
        def __ior__(self, f: typing.Union['QGeoRouteRequest.SegmentDetails', 'QGeoRouteRequest.SegmentDetail']) -> 'QGeoRouteRequest.SegmentDetails': ...
        def __or__(self, f: typing.Union['QGeoRouteRequest.SegmentDetails', 'QGeoRouteRequest.SegmentDetail']) -> 'QGeoRouteRequest.SegmentDetails': ...
        def __iand__(self, f: typing.Union['QGeoRouteRequest.SegmentDetails', 'QGeoRouteRequest.SegmentDetail']) -> 'QGeoRouteRequest.SegmentDetails': ...
        def __and__(self, f: typing.Union['QGeoRouteRequest.SegmentDetails', 'QGeoRouteRequest.SegmentDetail']) -> 'QGeoRouteRequest.SegmentDetails': ...
        def __invert__(self) -> 'QGeoRouteRequest.SegmentDetails': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class ManeuverDetails(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QGeoRouteRequest.ManeuverDetails', 'QGeoRouteRequest.ManeuverDetail']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QGeoRouteRequest.ManeuverDetails', 'QGeoRouteRequest.ManeuverDetail']) -> 'QGeoRouteRequest.ManeuverDetails': ...
        def __xor__(self, f: typing.Union['QGeoRouteRequest.ManeuverDetails', 'QGeoRouteRequest.ManeuverDetail']) -> 'QGeoRouteRequest.ManeuverDetails': ...
        def __ior__(self, f: typing.Union['QGeoRouteRequest.ManeuverDetails', 'QGeoRouteRequest.ManeuverDetail']) -> 'QGeoRouteRequest.ManeuverDetails': ...
        def __or__(self, f: typing.Union['QGeoRouteRequest.ManeuverDetails', 'QGeoRouteRequest.ManeuverDetail']) -> 'QGeoRouteRequest.ManeuverDetails': ...
        def __iand__(self, f: typing.Union['QGeoRouteRequest.ManeuverDetails', 'QGeoRouteRequest.ManeuverDetail']) -> 'QGeoRouteRequest.ManeuverDetails': ...
        def __and__(self, f: typing.Union['QGeoRouteRequest.ManeuverDetails', 'QGeoRouteRequest.ManeuverDetail']) -> 'QGeoRouteRequest.ManeuverDetails': ...
        def __invert__(self) -> 'QGeoRouteRequest.ManeuverDetails': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self, waypoints: typing.Iterable[QtPositioning.QGeoCoordinate] = ...) -> None: ...
    @typing.overload
    def __init__(self, origin: QtPositioning.QGeoCoordinate, destination: QtPositioning.QGeoCoordinate) -> None: ...
    @typing.overload
    def __init__(self, other: 'QGeoRouteRequest') -> None: ...

    def departureTime(self) -> QtCore.QDateTime: ...
    def setDepartureTime(self, departureTime: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def extraParameters(self) -> typing.Dict[str, typing.Any]: ...
    def setExtraParameters(self, extraParameters: typing.Dict[str, typing.Any]) -> None: ...
    def waypointsMetadata(self) -> typing.List[typing.Dict[str, typing.Any]]: ...
    def setWaypointsMetadata(self, waypointMetadata: typing.Iterable[typing.Dict[str, typing.Any]]) -> None: ...
    def maneuverDetail(self) -> 'QGeoRouteRequest.ManeuverDetail': ...
    def setManeuverDetail(self, maneuverDetail: 'QGeoRouteRequest.ManeuverDetail') -> None: ...
    def segmentDetail(self) -> 'QGeoRouteRequest.SegmentDetail': ...
    def setSegmentDetail(self, segmentDetail: 'QGeoRouteRequest.SegmentDetail') -> None: ...
    def routeOptimization(self) -> 'QGeoRouteRequest.RouteOptimizations': ...
    def setRouteOptimization(self, optimization: typing.Union['QGeoRouteRequest.RouteOptimizations', 'QGeoRouteRequest.RouteOptimization']) -> None: ...
    def featureTypes(self) -> typing.List['QGeoRouteRequest.FeatureType']: ...
    def featureWeight(self, featureType: 'QGeoRouteRequest.FeatureType') -> 'QGeoRouteRequest.FeatureWeight': ...
    def setFeatureWeight(self, featureType: 'QGeoRouteRequest.FeatureType', featureWeight: 'QGeoRouteRequest.FeatureWeight') -> None: ...
    def travelModes(self) -> 'QGeoRouteRequest.TravelModes': ...
    def setTravelModes(self, travelModes: typing.Union['QGeoRouteRequest.TravelModes', 'QGeoRouteRequest.TravelMode']) -> None: ...
    def numberAlternativeRoutes(self) -> int: ...
    def setNumberAlternativeRoutes(self, alternatives: int) -> None: ...
    def excludeAreas(self) -> typing.List[QtPositioning.QGeoRectangle]: ...
    def setExcludeAreas(self, areas: typing.Iterable[QtPositioning.QGeoRectangle]) -> None: ...
    def waypoints(self) -> typing.List[QtPositioning.QGeoCoordinate]: ...
    def setWaypoints(self, waypoints: typing.Iterable[QtPositioning.QGeoCoordinate]) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QGeoRouteSegment(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QGeoRouteSegment') -> None: ...

    def isLegLastSegment(self) -> bool: ...
    def maneuver(self) -> QGeoManeuver: ...
    def setManeuver(self, maneuver: QGeoManeuver) -> None: ...
    def path(self) -> typing.List[QtPositioning.QGeoCoordinate]: ...
    def setPath(self, path: typing.Iterable[QtPositioning.QGeoCoordinate]) -> None: ...
    def distance(self) -> float: ...
    def setDistance(self, distance: float) -> None: ...
    def travelTime(self) -> int: ...
    def setTravelTime(self, secs: int) -> None: ...
    def nextRouteSegment(self) -> 'QGeoRouteSegment': ...
    def setNextRouteSegment(self, routeSegment: 'QGeoRouteSegment') -> None: ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QGeoRoutingManager(QtCore.QObject):

    error: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    def measurementSystem(self) -> QtCore.QLocale.MeasurementSystem: ...
    def setMeasurementSystem(self, system: QtCore.QLocale.MeasurementSystem) -> None: ...
    def locale(self) -> QtCore.QLocale: ...
    def setLocale(self, locale: QtCore.QLocale) -> None: ...
    def supportedManeuverDetails(self) -> QGeoRouteRequest.ManeuverDetails: ...
    def supportedSegmentDetails(self) -> QGeoRouteRequest.SegmentDetails: ...
    def supportedRouteOptimizations(self) -> QGeoRouteRequest.RouteOptimizations: ...
    def supportedFeatureWeights(self) -> QGeoRouteRequest.FeatureWeights: ...
    def supportedFeatureTypes(self) -> QGeoRouteRequest.FeatureTypes: ...
    def supportedTravelModes(self) -> QGeoRouteRequest.TravelModes: ...
    def updateRoute(self, route: QGeoRoute, position: QtPositioning.QGeoCoordinate) -> typing.Optional[QGeoRouteReply]: ...
    def calculateRoute(self, request: QGeoRouteRequest) -> typing.Optional[QGeoRouteReply]: ...
    def managerVersion(self) -> int: ...
    def managerName(self) -> str: ...


class QGeoRoutingManagerEngine(QtCore.QObject):

    def __init__(self, parameters: typing.Dict[str, typing.Any], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setSupportedManeuverDetails(self, maneuverDetails: typing.Union[QGeoRouteRequest.ManeuverDetails, QGeoRouteRequest.ManeuverDetail]) -> None: ...
    def setSupportedSegmentDetails(self, segmentDetails: typing.Union[QGeoRouteRequest.SegmentDetails, QGeoRouteRequest.SegmentDetail]) -> None: ...
    def setSupportedRouteOptimizations(self, optimizations: typing.Union[QGeoRouteRequest.RouteOptimizations, QGeoRouteRequest.RouteOptimization]) -> None: ...
    def setSupportedFeatureWeights(self, featureWeights: typing.Union[QGeoRouteRequest.FeatureWeights, QGeoRouteRequest.FeatureWeight]) -> None: ...
    def setSupportedFeatureTypes(self, featureTypes: typing.Union[QGeoRouteRequest.FeatureTypes, QGeoRouteRequest.FeatureType]) -> None: ...
    def setSupportedTravelModes(self, travelModes: typing.Union[QGeoRouteRequest.TravelModes, QGeoRouteRequest.TravelMode]) -> None: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    def measurementSystem(self) -> QtCore.QLocale.MeasurementSystem: ...
    def setMeasurementSystem(self, system: QtCore.QLocale.MeasurementSystem) -> None: ...
    def locale(self) -> QtCore.QLocale: ...
    def setLocale(self, locale: QtCore.QLocale) -> None: ...
    def supportedManeuverDetails(self) -> QGeoRouteRequest.ManeuverDetails: ...
    def supportedSegmentDetails(self) -> QGeoRouteRequest.SegmentDetails: ...
    def supportedRouteOptimizations(self) -> QGeoRouteRequest.RouteOptimizations: ...
    def supportedFeatureWeights(self) -> QGeoRouteRequest.FeatureWeights: ...
    def supportedFeatureTypes(self) -> QGeoRouteRequest.FeatureTypes: ...
    def supportedTravelModes(self) -> QGeoRouteRequest.TravelModes: ...
    def updateRoute(self, route: QGeoRoute, position: QtPositioning.QGeoCoordinate) -> typing.Optional[QGeoRouteReply]: ...
    def calculateRoute(self, request: QGeoRouteRequest) -> typing.Optional[QGeoRouteReply]: ...
    def managerVersion(self) -> int: ...
    def managerName(self) -> str: ...


class QNavigationManager(PyQt5.sipsimplewrapper): ...


class QGeoServiceProvider(QtCore.QObject):

    class NavigationFeature(int):
        NoNavigationFeatures = ... # type: QGeoServiceProvider.NavigationFeature
        OnlineNavigationFeature = ... # type: QGeoServiceProvider.NavigationFeature
        OfflineNavigationFeature = ... # type: QGeoServiceProvider.NavigationFeature
        AnyNavigationFeatures = ... # type: QGeoServiceProvider.NavigationFeature

    class PlacesFeature(int):
        NoPlacesFeatures = ... # type: QGeoServiceProvider.PlacesFeature
        OnlinePlacesFeature = ... # type: QGeoServiceProvider.PlacesFeature
        OfflinePlacesFeature = ... # type: QGeoServiceProvider.PlacesFeature
        SavePlaceFeature = ... # type: QGeoServiceProvider.PlacesFeature
        RemovePlaceFeature = ... # type: QGeoServiceProvider.PlacesFeature
        SaveCategoryFeature = ... # type: QGeoServiceProvider.PlacesFeature
        RemoveCategoryFeature = ... # type: QGeoServiceProvider.PlacesFeature
        PlaceRecommendationsFeature = ... # type: QGeoServiceProvider.PlacesFeature
        SearchSuggestionsFeature = ... # type: QGeoServiceProvider.PlacesFeature
        LocalizedPlacesFeature = ... # type: QGeoServiceProvider.PlacesFeature
        NotificationsFeature = ... # type: QGeoServiceProvider.PlacesFeature
        PlaceMatchingFeature = ... # type: QGeoServiceProvider.PlacesFeature
        AnyPlacesFeatures = ... # type: QGeoServiceProvider.PlacesFeature

    class MappingFeature(int):
        NoMappingFeatures = ... # type: QGeoServiceProvider.MappingFeature
        OnlineMappingFeature = ... # type: QGeoServiceProvider.MappingFeature
        OfflineMappingFeature = ... # type: QGeoServiceProvider.MappingFeature
        LocalizedMappingFeature = ... # type: QGeoServiceProvider.MappingFeature
        AnyMappingFeatures = ... # type: QGeoServiceProvider.MappingFeature

    class GeocodingFeature(int):
        NoGeocodingFeatures = ... # type: QGeoServiceProvider.GeocodingFeature
        OnlineGeocodingFeature = ... # type: QGeoServiceProvider.GeocodingFeature
        OfflineGeocodingFeature = ... # type: QGeoServiceProvider.GeocodingFeature
        ReverseGeocodingFeature = ... # type: QGeoServiceProvider.GeocodingFeature
        LocalizedGeocodingFeature = ... # type: QGeoServiceProvider.GeocodingFeature
        AnyGeocodingFeatures = ... # type: QGeoServiceProvider.GeocodingFeature

    class RoutingFeature(int):
        NoRoutingFeatures = ... # type: QGeoServiceProvider.RoutingFeature
        OnlineRoutingFeature = ... # type: QGeoServiceProvider.RoutingFeature
        OfflineRoutingFeature = ... # type: QGeoServiceProvider.RoutingFeature
        LocalizedRoutingFeature = ... # type: QGeoServiceProvider.RoutingFeature
        RouteUpdatesFeature = ... # type: QGeoServiceProvider.RoutingFeature
        AlternativeRoutesFeature = ... # type: QGeoServiceProvider.RoutingFeature
        ExcludeAreasRoutingFeature = ... # type: QGeoServiceProvider.RoutingFeature
        AnyRoutingFeatures = ... # type: QGeoServiceProvider.RoutingFeature

    class Error(int):
        NoError = ... # type: QGeoServiceProvider.Error
        NotSupportedError = ... # type: QGeoServiceProvider.Error
        UnknownParameterError = ... # type: QGeoServiceProvider.Error
        MissingRequiredParameterError = ... # type: QGeoServiceProvider.Error
        ConnectionError = ... # type: QGeoServiceProvider.Error
        LoaderError = ... # type: QGeoServiceProvider.Error

    class RoutingFeatures(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QGeoServiceProvider.RoutingFeatures', 'QGeoServiceProvider.RoutingFeature']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QGeoServiceProvider.RoutingFeatures', 'QGeoServiceProvider.RoutingFeature']) -> 'QGeoServiceProvider.RoutingFeatures': ...
        def __xor__(self, f: typing.Union['QGeoServiceProvider.RoutingFeatures', 'QGeoServiceProvider.RoutingFeature']) -> 'QGeoServiceProvider.RoutingFeatures': ...
        def __ior__(self, f: typing.Union['QGeoServiceProvider.RoutingFeatures', 'QGeoServiceProvider.RoutingFeature']) -> 'QGeoServiceProvider.RoutingFeatures': ...
        def __or__(self, f: typing.Union['QGeoServiceProvider.RoutingFeatures', 'QGeoServiceProvider.RoutingFeature']) -> 'QGeoServiceProvider.RoutingFeatures': ...
        def __iand__(self, f: typing.Union['QGeoServiceProvider.RoutingFeatures', 'QGeoServiceProvider.RoutingFeature']) -> 'QGeoServiceProvider.RoutingFeatures': ...
        def __and__(self, f: typing.Union['QGeoServiceProvider.RoutingFeatures', 'QGeoServiceProvider.RoutingFeature']) -> 'QGeoServiceProvider.RoutingFeatures': ...
        def __invert__(self) -> 'QGeoServiceProvider.RoutingFeatures': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class GeocodingFeatures(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QGeoServiceProvider.GeocodingFeatures', 'QGeoServiceProvider.GeocodingFeature']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QGeoServiceProvider.GeocodingFeatures', 'QGeoServiceProvider.GeocodingFeature']) -> 'QGeoServiceProvider.GeocodingFeatures': ...
        def __xor__(self, f: typing.Union['QGeoServiceProvider.GeocodingFeatures', 'QGeoServiceProvider.GeocodingFeature']) -> 'QGeoServiceProvider.GeocodingFeatures': ...
        def __ior__(self, f: typing.Union['QGeoServiceProvider.GeocodingFeatures', 'QGeoServiceProvider.GeocodingFeature']) -> 'QGeoServiceProvider.GeocodingFeatures': ...
        def __or__(self, f: typing.Union['QGeoServiceProvider.GeocodingFeatures', 'QGeoServiceProvider.GeocodingFeature']) -> 'QGeoServiceProvider.GeocodingFeatures': ...
        def __iand__(self, f: typing.Union['QGeoServiceProvider.GeocodingFeatures', 'QGeoServiceProvider.GeocodingFeature']) -> 'QGeoServiceProvider.GeocodingFeatures': ...
        def __and__(self, f: typing.Union['QGeoServiceProvider.GeocodingFeatures', 'QGeoServiceProvider.GeocodingFeature']) -> 'QGeoServiceProvider.GeocodingFeatures': ...
        def __invert__(self) -> 'QGeoServiceProvider.GeocodingFeatures': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class MappingFeatures(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QGeoServiceProvider.MappingFeatures', 'QGeoServiceProvider.MappingFeature']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QGeoServiceProvider.MappingFeatures', 'QGeoServiceProvider.MappingFeature']) -> 'QGeoServiceProvider.MappingFeatures': ...
        def __xor__(self, f: typing.Union['QGeoServiceProvider.MappingFeatures', 'QGeoServiceProvider.MappingFeature']) -> 'QGeoServiceProvider.MappingFeatures': ...
        def __ior__(self, f: typing.Union['QGeoServiceProvider.MappingFeatures', 'QGeoServiceProvider.MappingFeature']) -> 'QGeoServiceProvider.MappingFeatures': ...
        def __or__(self, f: typing.Union['QGeoServiceProvider.MappingFeatures', 'QGeoServiceProvider.MappingFeature']) -> 'QGeoServiceProvider.MappingFeatures': ...
        def __iand__(self, f: typing.Union['QGeoServiceProvider.MappingFeatures', 'QGeoServiceProvider.MappingFeature']) -> 'QGeoServiceProvider.MappingFeatures': ...
        def __and__(self, f: typing.Union['QGeoServiceProvider.MappingFeatures', 'QGeoServiceProvider.MappingFeature']) -> 'QGeoServiceProvider.MappingFeatures': ...
        def __invert__(self) -> 'QGeoServiceProvider.MappingFeatures': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class PlacesFeatures(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QGeoServiceProvider.PlacesFeatures', 'QGeoServiceProvider.PlacesFeature']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QGeoServiceProvider.PlacesFeatures', 'QGeoServiceProvider.PlacesFeature']) -> 'QGeoServiceProvider.PlacesFeatures': ...
        def __xor__(self, f: typing.Union['QGeoServiceProvider.PlacesFeatures', 'QGeoServiceProvider.PlacesFeature']) -> 'QGeoServiceProvider.PlacesFeatures': ...
        def __ior__(self, f: typing.Union['QGeoServiceProvider.PlacesFeatures', 'QGeoServiceProvider.PlacesFeature']) -> 'QGeoServiceProvider.PlacesFeatures': ...
        def __or__(self, f: typing.Union['QGeoServiceProvider.PlacesFeatures', 'QGeoServiceProvider.PlacesFeature']) -> 'QGeoServiceProvider.PlacesFeatures': ...
        def __iand__(self, f: typing.Union['QGeoServiceProvider.PlacesFeatures', 'QGeoServiceProvider.PlacesFeature']) -> 'QGeoServiceProvider.PlacesFeatures': ...
        def __and__(self, f: typing.Union['QGeoServiceProvider.PlacesFeatures', 'QGeoServiceProvider.PlacesFeature']) -> 'QGeoServiceProvider.PlacesFeatures': ...
        def __invert__(self) -> 'QGeoServiceProvider.PlacesFeatures': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class NavigationFeatures(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QGeoServiceProvider.NavigationFeatures', 'QGeoServiceProvider.NavigationFeature']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QGeoServiceProvider.NavigationFeatures', 'QGeoServiceProvider.NavigationFeature']) -> 'QGeoServiceProvider.NavigationFeatures': ...
        def __xor__(self, f: typing.Union['QGeoServiceProvider.NavigationFeatures', 'QGeoServiceProvider.NavigationFeature']) -> 'QGeoServiceProvider.NavigationFeatures': ...
        def __ior__(self, f: typing.Union['QGeoServiceProvider.NavigationFeatures', 'QGeoServiceProvider.NavigationFeature']) -> 'QGeoServiceProvider.NavigationFeatures': ...
        def __or__(self, f: typing.Union['QGeoServiceProvider.NavigationFeatures', 'QGeoServiceProvider.NavigationFeature']) -> 'QGeoServiceProvider.NavigationFeatures': ...
        def __iand__(self, f: typing.Union['QGeoServiceProvider.NavigationFeatures', 'QGeoServiceProvider.NavigationFeature']) -> 'QGeoServiceProvider.NavigationFeatures': ...
        def __and__(self, f: typing.Union['QGeoServiceProvider.NavigationFeatures', 'QGeoServiceProvider.NavigationFeature']) -> 'QGeoServiceProvider.NavigationFeatures': ...
        def __invert__(self) -> 'QGeoServiceProvider.NavigationFeatures': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, providerName: typing.Optional[str], parameters: typing.Dict[str, typing.Any] = ..., allowExperimental: bool = ...) -> None: ...

    def navigationErrorString(self) -> str: ...
    def navigationError(self) -> 'QGeoServiceProvider.Error': ...
    def placesErrorString(self) -> str: ...
    def placesError(self) -> 'QGeoServiceProvider.Error': ...
    def routingErrorString(self) -> str: ...
    def routingError(self) -> 'QGeoServiceProvider.Error': ...
    def geocodingErrorString(self) -> str: ...
    def geocodingError(self) -> 'QGeoServiceProvider.Error': ...
    def mappingErrorString(self) -> str: ...
    def mappingError(self) -> 'QGeoServiceProvider.Error': ...
    def navigationManager(self) -> typing.Optional[QNavigationManager]: ...
    def navigationFeatures(self) -> 'QGeoServiceProvider.NavigationFeatures': ...
    def setAllowExperimental(self, allow: bool) -> None: ...
    def setLocale(self, locale: QtCore.QLocale) -> None: ...
    def setParameters(self, parameters: typing.Dict[str, typing.Any]) -> None: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QGeoServiceProvider.Error': ...
    def placeManager(self) -> typing.Optional['QPlaceManager']: ...
    def routingManager(self) -> typing.Optional[QGeoRoutingManager]: ...
    def geocodingManager(self) -> typing.Optional[QGeoCodingManager]: ...
    def placesFeatures(self) -> 'QGeoServiceProvider.PlacesFeatures': ...
    def mappingFeatures(self) -> 'QGeoServiceProvider.MappingFeatures': ...
    def geocodingFeatures(self) -> 'QGeoServiceProvider.GeocodingFeatures': ...
    def routingFeatures(self) -> 'QGeoServiceProvider.RoutingFeatures': ...
    @staticmethod
    def availableServiceProviders() -> typing.List[str]: ...


class QLocation(PyQt5.sip.simplewrapper):

    class Visibility(int):
        UnspecifiedVisibility = ... # type: QLocation.Visibility
        DeviceVisibility = ... # type: QLocation.Visibility
        PrivateVisibility = ... # type: QLocation.Visibility
        PublicVisibility = ... # type: QLocation.Visibility

    class VisibilityScope(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QLocation.VisibilityScope', 'QLocation.Visibility']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QLocation.VisibilityScope', 'QLocation.Visibility']) -> 'QLocation.VisibilityScope': ...
        def __xor__(self, f: typing.Union['QLocation.VisibilityScope', 'QLocation.Visibility']) -> 'QLocation.VisibilityScope': ...
        def __ior__(self, f: typing.Union['QLocation.VisibilityScope', 'QLocation.Visibility']) -> 'QLocation.VisibilityScope': ...
        def __or__(self, f: typing.Union['QLocation.VisibilityScope', 'QLocation.Visibility']) -> 'QLocation.VisibilityScope': ...
        def __iand__(self, f: typing.Union['QLocation.VisibilityScope', 'QLocation.Visibility']) -> 'QLocation.VisibilityScope': ...
        def __and__(self, f: typing.Union['QLocation.VisibilityScope', 'QLocation.Visibility']) -> 'QLocation.VisibilityScope': ...
        def __invert__(self) -> 'QLocation.VisibilityScope': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...


class QPlace(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlace') -> None: ...

    def isEmpty(self) -> bool: ...
    def setVisibility(self, visibility: QLocation.Visibility) -> None: ...
    def visibility(self) -> QLocation.Visibility: ...
    def removeContactDetails(self, contactType: typing.Optional[str]) -> None: ...
    def appendContactDetail(self, contactType: typing.Optional[str], detail: 'QPlaceContactDetail') -> None: ...
    def setContactDetails(self, contactType: typing.Optional[str], details: typing.Iterable['QPlaceContactDetail']) -> None: ...
    def contactDetails(self, contactType: typing.Optional[str]) -> typing.List['QPlaceContactDetail']: ...
    def contactTypes(self) -> typing.List[str]: ...
    def removeExtendedAttribute(self, attributeType: typing.Optional[str]) -> None: ...
    def setExtendedAttribute(self, attributeType: typing.Optional[str], attribute: 'QPlaceAttribute') -> None: ...
    def extendedAttribute(self, attributeType: typing.Optional[str]) -> 'QPlaceAttribute': ...
    def extendedAttributeTypes(self) -> typing.List[str]: ...
    def setDetailsFetched(self, fetched: bool) -> None: ...
    def detailsFetched(self) -> bool: ...
    def primaryWebsite(self) -> QtCore.QUrl: ...
    def primaryEmail(self) -> str: ...
    def primaryFax(self) -> str: ...
    def primaryPhone(self) -> str: ...
    def setPlaceId(self, identifier: typing.Optional[str]) -> None: ...
    def placeId(self) -> str: ...
    def setName(self, name: typing.Optional[str]) -> None: ...
    def name(self) -> str: ...
    def setTotalContentCount(self, type: 'QPlaceContent.Type', total: int) -> None: ...
    def totalContentCount(self, type: 'QPlaceContent.Type') -> int: ...
    def insertContent(self, type: 'QPlaceContent.Type', content: typing.Dict[int, 'QPlaceContent']) -> None: ...
    def setContent(self, type: 'QPlaceContent.Type', content: typing.Dict[int, 'QPlaceContent']) -> None: ...
    def content(self, type: 'QPlaceContent.Type') -> typing.Dict[int, 'QPlaceContent']: ...
    def setIcon(self, icon: 'QPlaceIcon') -> None: ...
    def icon(self) -> 'QPlaceIcon': ...
    def setAttribution(self, attribution: typing.Optional[str]) -> None: ...
    def attribution(self) -> str: ...
    def setSupplier(self, supplier: 'QPlaceSupplier') -> None: ...
    def supplier(self) -> 'QPlaceSupplier': ...
    def setRatings(self, ratings: 'QPlaceRatings') -> None: ...
    def ratings(self) -> 'QPlaceRatings': ...
    def setLocation(self, location: QtPositioning.QGeoLocation) -> None: ...
    def location(self) -> QtPositioning.QGeoLocation: ...
    def setCategories(self, categories: typing.Iterable['QPlaceCategory']) -> None: ...
    def setCategory(self, category: 'QPlaceCategory') -> None: ...
    def categories(self) -> typing.List['QPlaceCategory']: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QPlaceAttribute(PyQt5.sipsimplewrapper):

    OpeningHours = ... # type: typing.Optional[str]
    Payment = ... # type: typing.Optional[str]
    Provider = ... # type: typing.Optional[str]

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlaceAttribute') -> None: ...

    def isEmpty(self) -> bool: ...
    def setText(self, text: typing.Optional[str]) -> None: ...
    def text(self) -> str: ...
    def setLabel(self, label: typing.Optional[str]) -> None: ...
    def label(self) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QPlaceCategory(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlaceCategory') -> None: ...

    def isEmpty(self) -> bool: ...
    def setIcon(self, icon: 'QPlaceIcon') -> None: ...
    def icon(self) -> 'QPlaceIcon': ...
    def setVisibility(self, visibility: QLocation.Visibility) -> None: ...
    def visibility(self) -> QLocation.Visibility: ...
    def setName(self, name: typing.Optional[str]) -> None: ...
    def name(self) -> str: ...
    def setCategoryId(self, identifier: typing.Optional[str]) -> None: ...
    def categoryId(self) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QPlaceContactDetail(PyQt5.sipsimplewrapper):

    Email = ... # type: typing.Optional[str]
    Fax = ... # type: typing.Optional[str]
    Phone = ... # type: typing.Optional[str]
    Website = ... # type: typing.Optional[str]

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlaceContactDetail') -> None: ...

    def clear(self) -> None: ...
    def setValue(self, value: typing.Optional[str]) -> None: ...
    def value(self) -> str: ...
    def setLabel(self, label: typing.Optional[str]) -> None: ...
    def label(self) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QPlaceContent(PyQt5.sipsimplewrapper):

    class Type(int):
        NoType = ... # type: QPlaceContent.Type
        ImageType = ... # type: QPlaceContent.Type
        ReviewType = ... # type: QPlaceContent.Type
        EditorialType = ... # type: QPlaceContent.Type
        CustomType = ... # type: QPlaceContent.Type

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlaceContent') -> None: ...

    def setAttribution(self, attribution: typing.Optional[str]) -> None: ...
    def attribution(self) -> str: ...
    def setUser(self, user: 'QPlaceUser') -> None: ...
    def user(self) -> 'QPlaceUser': ...
    def setSupplier(self, supplier: 'QPlaceSupplier') -> None: ...
    def supplier(self) -> 'QPlaceSupplier': ...
    def type(self) -> 'QPlaceContent.Type': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QPlaceReply(QtCore.QObject):

    class Type(int):
        Reply = ... # type: QPlaceReply.Type
        DetailsReply = ... # type: QPlaceReply.Type
        SearchReply = ... # type: QPlaceReply.Type
        SearchSuggestionReply = ... # type: QPlaceReply.Type
        ContentReply = ... # type: QPlaceReply.Type
        IdReply = ... # type: QPlaceReply.Type
        MatchReply = ... # type: QPlaceReply.Type

    class Error(int):
        NoError = ... # type: QPlaceReply.Error
        PlaceDoesNotExistError = ... # type: QPlaceReply.Error
        CategoryDoesNotExistError = ... # type: QPlaceReply.Error
        CommunicationError = ... # type: QPlaceReply.Error
        ParseError = ... # type: QPlaceReply.Error
        PermissionsError = ... # type: QPlaceReply.Error
        UnsupportedError = ... # type: QPlaceReply.Error
        BadArgumentError = ... # type: QPlaceReply.Error
        CancelError = ... # type: QPlaceReply.Error
        UnknownError = ... # type: QPlaceReply.Error

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setError(self, error: 'QPlaceReply.Error', errorString: typing.Optional[str]) -> None: ...
    def setFinished(self, finished: bool) -> None: ...
    contentUpdated: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    aborted: typing.ClassVar[QtCore.pyqtSignal]
    def abort(self) -> None: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def errorString(self) -> str: ...
    def type(self) -> 'QPlaceReply.Type': ...
    def isFinished(self) -> bool: ...


class QPlaceContentReply(QPlaceReply):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setNextPageRequest(self, next: 'QPlaceContentRequest') -> None: ...
    def setPreviousPageRequest(self, previous: 'QPlaceContentRequest') -> None: ...
    def setRequest(self, request: 'QPlaceContentRequest') -> None: ...
    def setTotalCount(self, total: int) -> None: ...
    def setContent(self, content: typing.Dict[int, QPlaceContent]) -> None: ...
    def nextPageRequest(self) -> 'QPlaceContentRequest': ...
    def previousPageRequest(self) -> 'QPlaceContentRequest': ...
    def request(self) -> 'QPlaceContentRequest': ...
    def totalCount(self) -> int: ...
    def content(self) -> typing.Dict[int, QPlaceContent]: ...
    def type(self) -> QPlaceReply.Type: ...


class QPlaceContentRequest(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlaceContentRequest') -> None: ...

    def clear(self) -> None: ...
    def setLimit(self, limit: int) -> None: ...
    def limit(self) -> int: ...
    def setContentContext(self, context: typing.Any) -> None: ...
    def contentContext(self) -> typing.Any: ...
    def setPlaceId(self, identifier: typing.Optional[str]) -> None: ...
    def placeId(self) -> str: ...
    def setContentType(self, type: QPlaceContent.Type) -> None: ...
    def contentType(self) -> QPlaceContent.Type: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QPlaceDetailsReply(QPlaceReply):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setPlace(self, place: QPlace) -> None: ...
    def place(self) -> QPlace: ...
    def type(self) -> QPlaceReply.Type: ...


class QPlaceEditorial(QPlaceContent):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: QPlaceContent) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPlaceEditorial') -> None: ...

    def setLanguage(self, data: typing.Optional[str]) -> None: ...
    def language(self) -> str: ...
    def setTitle(self, data: typing.Optional[str]) -> None: ...
    def title(self) -> str: ...
    def setText(self, text: typing.Optional[str]) -> None: ...
    def text(self) -> str: ...


class QPlaceIcon(PyQt5.sipsimplewrapper):

    SingleUrl = ... # type: typing.Optional[str]

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlaceIcon') -> None: ...

    def isEmpty(self) -> bool: ...
    def setParameters(self, parameters: typing.Dict[str, typing.Any]) -> None: ...
    def parameters(self) -> typing.Dict[str, typing.Any]: ...
    def setManager(self, manager: typing.Optional['QPlaceManager']) -> None: ...
    def manager(self) -> typing.Optional['QPlaceManager']: ...
    def url(self, size: QtCore.QSize = ...) -> QtCore.QUrl: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QPlaceIdReply(QPlaceReply):

    class OperationType(int):
        SavePlace = ... # type: QPlaceIdReply.OperationType
        SaveCategory = ... # type: QPlaceIdReply.OperationType
        RemovePlace = ... # type: QPlaceIdReply.OperationType
        RemoveCategory = ... # type: QPlaceIdReply.OperationType

    def __init__(self, operationType: 'QPlaceIdReply.OperationType', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setId(self, identifier: typing.Optional[str]) -> None: ...
    def id(self) -> str: ...
    def operationType(self) -> 'QPlaceIdReply.OperationType': ...
    def type(self) -> QPlaceReply.Type: ...


class QPlaceImage(QPlaceContent):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: QPlaceContent) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPlaceImage') -> None: ...

    def setMimeType(self, data: typing.Optional[str]) -> None: ...
    def mimeType(self) -> str: ...
    def setImageId(self, identifier: typing.Optional[str]) -> None: ...
    def imageId(self) -> str: ...
    def setUrl(self, url: QtCore.QUrl) -> None: ...
    def url(self) -> QtCore.QUrl: ...


class QPlaceManager(QtCore.QObject):

    dataChanged: typing.ClassVar[QtCore.pyqtSignal]
    categoryRemoved: typing.ClassVar[QtCore.pyqtSignal]
    categoryUpdated: typing.ClassVar[QtCore.pyqtSignal]
    categoryAdded: typing.ClassVar[QtCore.pyqtSignal]
    placeRemoved: typing.ClassVar[QtCore.pyqtSignal]
    placeUpdated: typing.ClassVar[QtCore.pyqtSignal]
    placeAdded: typing.ClassVar[QtCore.pyqtSignal]
    error: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    def matchingPlaces(self, request: 'QPlaceMatchRequest') -> typing.Optional['QPlaceMatchReply']: ...
    def compatiblePlace(self, place: QPlace) -> QPlace: ...
    def setLocales(self, locale: typing.Iterable[QtCore.QLocale]) -> None: ...
    def setLocale(self, locale: QtCore.QLocale) -> None: ...
    def locales(self) -> typing.List[QtCore.QLocale]: ...
    def childCategories(self, parentId: typing.Optional[str] = ...) -> typing.List[QPlaceCategory]: ...
    def category(self, categoryId: typing.Optional[str]) -> QPlaceCategory: ...
    def childCategoryIds(self, parentId: typing.Optional[str] = ...) -> typing.List[str]: ...
    def parentCategoryId(self, categoryId: typing.Optional[str]) -> str: ...
    def initializeCategories(self) -> typing.Optional[QPlaceReply]: ...
    def removeCategory(self, categoryId: typing.Optional[str]) -> typing.Optional[QPlaceIdReply]: ...
    def saveCategory(self, category: QPlaceCategory, parentId: typing.Optional[str] = ...) -> typing.Optional[QPlaceIdReply]: ...
    def removePlace(self, placeId: typing.Optional[str]) -> typing.Optional[QPlaceIdReply]: ...
    def savePlace(self, place: QPlace) -> typing.Optional[QPlaceIdReply]: ...
    def searchSuggestions(self, request: 'QPlaceSearchRequest') -> typing.Optional['QPlaceSearchSuggestionReply']: ...
    def search(self, query: 'QPlaceSearchRequest') -> typing.Optional['QPlaceSearchReply']: ...
    def getPlaceContent(self, request: QPlaceContentRequest) -> typing.Optional[QPlaceContentReply]: ...
    def getPlaceDetails(self, placeId: typing.Optional[str]) -> typing.Optional[QPlaceDetailsReply]: ...
    def managerVersion(self) -> int: ...
    def managerName(self) -> str: ...


class QPlaceManagerEngine(QtCore.QObject):

    def __init__(self, parameters: typing.Dict[str, typing.Any], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def manager(self) -> typing.Optional[QPlaceManager]: ...
    dataChanged: typing.ClassVar[QtCore.pyqtSignal]
    categoryRemoved: typing.ClassVar[QtCore.pyqtSignal]
    categoryUpdated: typing.ClassVar[QtCore.pyqtSignal]
    categoryAdded: typing.ClassVar[QtCore.pyqtSignal]
    placeRemoved: typing.ClassVar[QtCore.pyqtSignal]
    placeUpdated: typing.ClassVar[QtCore.pyqtSignal]
    placeAdded: typing.ClassVar[QtCore.pyqtSignal]
    error: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    def matchingPlaces(self, request: 'QPlaceMatchRequest') -> typing.Optional['QPlaceMatchReply']: ...
    def compatiblePlace(self, original: QPlace) -> QPlace: ...
    def constructIconUrl(self, icon: QPlaceIcon, size: QtCore.QSize) -> QtCore.QUrl: ...
    def setLocales(self, locales: typing.Iterable[QtCore.QLocale]) -> None: ...
    def locales(self) -> typing.List[QtCore.QLocale]: ...
    def childCategories(self, parentId: typing.Optional[str]) -> typing.List[QPlaceCategory]: ...
    def category(self, categoryId: typing.Optional[str]) -> QPlaceCategory: ...
    def childCategoryIds(self, categoryId: typing.Optional[str]) -> typing.List[str]: ...
    def parentCategoryId(self, categoryId: typing.Optional[str]) -> str: ...
    def initializeCategories(self) -> typing.Optional[QPlaceReply]: ...
    def removeCategory(self, categoryId: typing.Optional[str]) -> typing.Optional[QPlaceIdReply]: ...
    def saveCategory(self, category: QPlaceCategory, parentId: typing.Optional[str]) -> typing.Optional[QPlaceIdReply]: ...
    def removePlace(self, placeId: typing.Optional[str]) -> typing.Optional[QPlaceIdReply]: ...
    def savePlace(self, place: QPlace) -> typing.Optional[QPlaceIdReply]: ...
    def searchSuggestions(self, request: 'QPlaceSearchRequest') -> typing.Optional['QPlaceSearchSuggestionReply']: ...
    def search(self, request: 'QPlaceSearchRequest') -> typing.Optional['QPlaceSearchReply']: ...
    def getPlaceContent(self, request: QPlaceContentRequest) -> typing.Optional[QPlaceContentReply]: ...
    def getPlaceDetails(self, placeId: typing.Optional[str]) -> typing.Optional[QPlaceDetailsReply]: ...
    def managerVersion(self) -> int: ...
    def managerName(self) -> str: ...


class QPlaceMatchReply(QPlaceReply):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setRequest(self, request: 'QPlaceMatchRequest') -> None: ...
    def setPlaces(self, results: typing.Iterable[QPlace]) -> None: ...
    def request(self) -> 'QPlaceMatchRequest': ...
    def places(self) -> typing.List[QPlace]: ...
    def type(self) -> QPlaceReply.Type: ...


class QPlaceMatchRequest(PyQt5.sipsimplewrapper):

    AlternativeId = ... # type: typing.Optional[str]

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlaceMatchRequest') -> None: ...

    def clear(self) -> None: ...
    def setParameters(self, parameters: typing.Dict[str, typing.Any]) -> None: ...
    def parameters(self) -> typing.Dict[str, typing.Any]: ...
    def setResults(self, results: typing.Iterable['QPlaceSearchResult']) -> None: ...
    def setPlaces(self, places: typing.Iterable[QPlace]) -> None: ...
    def places(self) -> typing.List[QPlace]: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QPlaceSearchResult(PyQt5.sipsimplewrapper):

    class SearchResultType(int):
        UnknownSearchResult = ... # type: QPlaceSearchResult.SearchResultType
        PlaceResult = ... # type: QPlaceSearchResult.SearchResultType
        ProposedSearchResult = ... # type: QPlaceSearchResult.SearchResultType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlaceSearchResult') -> None: ...

    def setIcon(self, icon: QPlaceIcon) -> None: ...
    def icon(self) -> QPlaceIcon: ...
    def setTitle(self, title: typing.Optional[str]) -> None: ...
    def title(self) -> str: ...
    def type(self) -> 'QPlaceSearchResult.SearchResultType': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QPlaceProposedSearchResult(QPlaceSearchResult):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: QPlaceSearchResult) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPlaceProposedSearchResult') -> None: ...

    def setSearchRequest(self, request: 'QPlaceSearchRequest') -> None: ...
    def searchRequest(self) -> 'QPlaceSearchRequest': ...


class QPlaceRatings(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlaceRatings') -> None: ...

    def isEmpty(self) -> bool: ...
    def setMaximum(self, max: float) -> None: ...
    def maximum(self) -> float: ...
    def setCount(self, count: int) -> None: ...
    def count(self) -> int: ...
    def setAverage(self, average: float) -> None: ...
    def average(self) -> float: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QPlaceResult(QPlaceSearchResult):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: QPlaceSearchResult) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPlaceResult') -> None: ...

    def setSponsored(self, sponsored: bool) -> None: ...
    def isSponsored(self) -> bool: ...
    def setPlace(self, place: QPlace) -> None: ...
    def place(self) -> QPlace: ...
    def setDistance(self, distance: float) -> None: ...
    def distance(self) -> float: ...


class QPlaceReview(QPlaceContent):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: QPlaceContent) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPlaceReview') -> None: ...

    def setTitle(self, data: typing.Optional[str]) -> None: ...
    def title(self) -> str: ...
    def setReviewId(self, identifier: typing.Optional[str]) -> None: ...
    def reviewId(self) -> str: ...
    def setRating(self, data: float) -> None: ...
    def rating(self) -> float: ...
    def setLanguage(self, data: typing.Optional[str]) -> None: ...
    def language(self) -> str: ...
    def setText(self, text: typing.Optional[str]) -> None: ...
    def text(self) -> str: ...
    def setDateTime(self, dt: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def dateTime(self) -> QtCore.QDateTime: ...


class QPlaceSearchReply(QPlaceReply):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setNextPageRequest(self, next: 'QPlaceSearchRequest') -> None: ...
    def setPreviousPageRequest(self, previous: 'QPlaceSearchRequest') -> None: ...
    def setRequest(self, request: 'QPlaceSearchRequest') -> None: ...
    def setResults(self, results: typing.Iterable[QPlaceSearchResult]) -> None: ...
    def nextPageRequest(self) -> 'QPlaceSearchRequest': ...
    def previousPageRequest(self) -> 'QPlaceSearchRequest': ...
    def request(self) -> 'QPlaceSearchRequest': ...
    def results(self) -> typing.List[QPlaceSearchResult]: ...
    def type(self) -> QPlaceReply.Type: ...


class QPlaceSearchRequest(PyQt5.sipsimplewrapper):

    class RelevanceHint(int):
        UnspecifiedHint = ... # type: QPlaceSearchRequest.RelevanceHint
        DistanceHint = ... # type: QPlaceSearchRequest.RelevanceHint
        LexicalPlaceNameHint = ... # type: QPlaceSearchRequest.RelevanceHint

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlaceSearchRequest') -> None: ...

    def clear(self) -> None: ...
    def setLimit(self, limit: int) -> None: ...
    def limit(self) -> int: ...
    def setRelevanceHint(self, hint: 'QPlaceSearchRequest.RelevanceHint') -> None: ...
    def relevanceHint(self) -> 'QPlaceSearchRequest.RelevanceHint': ...
    def setVisibilityScope(self, visibilityScopes: typing.Union[QLocation.VisibilityScope, QLocation.Visibility]) -> None: ...
    def visibilityScope(self) -> QLocation.VisibilityScope: ...
    def setSearchContext(self, context: typing.Any) -> None: ...
    def searchContext(self) -> typing.Any: ...
    def setRecommendationId(self, recommendationId: typing.Optional[str]) -> None: ...
    def recommendationId(self) -> str: ...
    def setSearchArea(self, area: QtPositioning.QGeoShape) -> None: ...
    def searchArea(self) -> QtPositioning.QGeoShape: ...
    def setCategories(self, categories: typing.Iterable[QPlaceCategory]) -> None: ...
    def setCategory(self, category: QPlaceCategory) -> None: ...
    def categories(self) -> typing.List[QPlaceCategory]: ...
    def setSearchTerm(self, term: typing.Optional[str]) -> None: ...
    def searchTerm(self) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QPlaceSearchSuggestionReply(QPlaceReply):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setSuggestions(self, suggestions: typing.Iterable[typing.Optional[str]]) -> None: ...
    def type(self) -> QPlaceReply.Type: ...
    def suggestions(self) -> typing.List[str]: ...


class QPlaceSupplier(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlaceSupplier') -> None: ...

    def isEmpty(self) -> bool: ...
    def setIcon(self, icon: QPlaceIcon) -> None: ...
    def icon(self) -> QPlaceIcon: ...
    def setUrl(self, data: QtCore.QUrl) -> None: ...
    def url(self) -> QtCore.QUrl: ...
    def setSupplierId(self, identifier: typing.Optional[str]) -> None: ...
    def supplierId(self) -> str: ...
    def setName(self, data: typing.Optional[str]) -> None: ...
    def name(self) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QPlaceUser(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPlaceUser') -> None: ...

    def setName(self, name: typing.Optional[str]) -> None: ...
    def name(self) -> str: ...
    def setUserId(self, identifier: typing.Optional[str]) -> None: ...
    def userId(self) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
