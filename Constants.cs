namespace MultipleFinger
{
    /// <summary>
    /// Defines the event codes for the fingerprint acquisition process.
    /// </summary>
    public enum AcquisitionEventCode
    {
        /// <summary>
        /// Indicates the start of the acquisition process.
        /// </summary>
        Begin = 0,

        /// <summary>
        /// Indicates that an image frame is being processed (e.g., for preview).
        /// </summary>
        Processing = 1,

        /// <summary>
        /// Indicates the successful completion of the acquisition process.
        /// </summary>
        End = 2,

        /// <summary>
        /// Indicates that an error occurred during the acquisition process.
        /// </summary>
        Error = -1
    }
}