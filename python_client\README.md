# MultipleFinger Bridge REST API

A Python Flask-based REST API that provides HTTP endpoints for the MultipleFinger C# application via TCP socket communication.

## Architecture

```
HTTP Client → Flask REST API → TCP Socket → C# MultipleFinger App → Fingerprint Device
```

The system consists of:
- **C# Application**: Handles fingerprint device operations and TCP server
- **Python REST API**: Provides HTTP endpoints and communicates via TCP
- **TCP Communication**: Clean separation between API and core functionality

## Setup

### 1. Prerequisites
- Python 3.8 or higher
- MultipleFinger.exe application running (provides TCP server on port 9999)

### 2. Installation

```bash
# Navigate to python_client directory
cd python_client

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Configuration

Copy `.env` file and modify if needed:
```bash
# TCP Server settings
TCP_HOST=localhost
TCP_PORT=9999

# REST API settings
API_PORT=5000
DEBUG=False
```

### 4. Start the API Server

```bash
python api.py
```

The REST API will be available at `http://localhost:5001`

## API Endpoints

### Base URL: `http://localhost:5001`

### 1. Health Check
```http
GET /api/health
```

**Response:**
```json
{
  "success": true,
  "data": {
    "api_status": "running",
    "tcp_connection": "connected",
    "tcp_host": "localhost",
    "tcp_port": 9999
  }
}
```

### 2. Server Status
```http
GET /api/status
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "TCP API Running",
    "device_connected": true,
    "api_version": "1.0.0",
    "tcp_port": 9999,
    "uptime": "00:15:30.123"
  }
}
```

### 3. Fingerprint Capture
```http
POST /api/fingerprint/capture
Content-Type: application/json

{
  "finger_position": 1,
  "operation_type": "flat",
  "timeout": 30,
  "save_image": true
}
```

**Parameters:**
- `finger_position` (required): Integer 1-10 (see finger positions below)
- `operation_type` (optional): "flat", "rolled", or "slaps" (default: "flat")
- `timeout` (optional): Capture timeout in seconds (default: 30)
- `save_image` (optional): Whether to save image data (default: true)

**Response:**
```json
{
  "success": true,
  "data": {
    "finger_position": 1,
    "finger_position_name": "Right Thumb",
    "template_data": "base64_encoded_template",
    "image_data": "base64_encoded_image",
    "quality": 85,
    "capture_time": "2025-07-10T08:22:18Z"
  }
}
```

### 4. Fingerprint Identification
```http
POST /api/fingerprint/identify
Content-Type: application/json

{
  "template_data": "base64_encoded_template_data",
  "threshold": 70
}
```

**Parameters:**
- `template_data` (required): Base64-encoded fingerprint template
- `threshold` (optional): Matching threshold 0-100 (default: 70)

**Response:**
```json
{
  "success": true,
  "data": {
    "matches": [
      {
        "user_id": "USER001",
        "finger_position": 1,
        "finger_position_name": "Right Thumb",
        "score": 85,
        "match_time": "2025-07-10T08:22:19Z"
      }
    ],
    "total_matches": 1
  }
}
```

### 5. Fingerprint Enrollment
```http
POST /api/fingerprint/enroll
Content-Type: application/json

{
  "user_id": "USER001",
  "finger_position": 1,
  "template_data": "base64_encoded_template_data",
  "image_data": "base64_encoded_image_data",
  "image_quality": 85
}
```

**Parameters:**
- `user_id` (required): User identifier (alphanumeric, 1-50 chars)
- `finger_position` (required): Integer 1-10
- `template_data` (required): Base64-encoded fingerprint template
- `image_data` (optional): Base64-encoded image data
- `image_quality` (optional): Image quality score 0-100

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Enrollment successful"
  }
}
```

### 6. Get Captured Data
```http
GET /api/fingerprint/captured-data
```

**Response:**
```json
{
  "success": true,
  "data": {
    "captured_data": [
      {
        "finger_position": 1,
        "finger_position_name": "Right Thumb",
        "template_data": "base64_template",
        "image_data": "base64_image",
        "quality": 85,
        "capture_time": "2025-07-10T08:22:18Z"
      }
    ],
    "message": "Retrieved captured fingerprints"
  }
}
```

### 7. Finger Positions Reference
```http
GET /api/fingerprint/positions
```

**Response:**
```json
{
  "success": true,
  "data": {
    "positions": {
      "1": "Right Thumb",
      "2": "Right Index",
      "3": "Right Middle",
      "4": "Right Ring",
      "5": "Right Little",
      "6": "Left Thumb",
      "7": "Left Index",
      "8": "Left Middle",
      "9": "Left Ring",
      "10": "Left Little"
    },
    "operation_types": ["flat", "rolled", "slaps"]
  }
}
```

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error category",
  "details": "Detailed error message"
}
```

**HTTP Status Codes:**
- `200`: Success
- `400`: Bad Request (validation error)
- `503`: Service Unavailable (TCP communication error)
- `500`: Internal Server Error

## Testing

### 1. Test TCP Connection
```bash
python test_tcp_client.py
```

### 2. Test REST API
```bash
# Start the API server
python api.py

# In another terminal, test endpoints:
curl http://localhost:5001/api/health
curl http://localhost:5001/api/status
```

### 3. Example Usage with curl

```bash
# Health check
curl -X GET http://localhost:5001/api/health

# Capture fingerprint
curl -X POST http://localhost:5001/api/fingerprint/capture \
  -H "Content-Type: application/json" \
  -d '{"finger_position": 1, "operation_type": "flat"}'

# Identify fingerprint
curl -X POST http://localhost:5001/api/fingerprint/identify \
  -H "Content-Type: application/json" \
  -d '{"template_data": "VGVzdCB0ZW1wbGF0ZSBkYXRh", "threshold": 70}'
```

## Development

### Project Structure
```
python_client/
├── api.py              # Main Flask application
├── tcp_client.py       # TCP communication module
├── validators.py       # Request validation utilities
├── test_tcp_client.py  # TCP server testing
├── requirements.txt    # Python dependencies
├── .env               # Configuration file
└── README.md          # This documentation
```

### Adding New Endpoints

1. Add TCP command to C# server
2. Add method to `tcp_client.py`
3. Add validation schema if needed
4. Add Flask route to `api.py`
5. Update documentation

## Troubleshooting

### Common Issues

1. **"Connection refused"**: Make sure MultipleFinger.exe is running
2. **"TCP connection disconnected"**: Check if C# application TCP server is active
3. **"Validation error"**: Check request format matches API documentation
4. **"Device not connected"**: Ensure fingerprint device is connected to C# application

### Logs

Enable debug logging by setting `DEBUG=True` in `.env` file.

### Support

For issues related to:
- **REST API**: Check Python logs and validate request format
- **TCP Communication**: Test with `test_tcp_client.py`
- **Fingerprint Operations**: Check C# application and device connection
