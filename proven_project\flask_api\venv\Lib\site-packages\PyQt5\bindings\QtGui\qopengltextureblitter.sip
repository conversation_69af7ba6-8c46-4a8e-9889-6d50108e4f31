// qopengltextureblitter.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_8_0 -)
%If (PyQt_OpenGL)

class QOpenGLTextureBlitter
{
%TypeHeaderCode
#include <qopengltextureblitter.h>
%End

public:
    QOpenGLTextureBlitter();
    ~QOpenGLTextureBlitter();

    enum Origin
    {
        OriginBottomLeft,
        OriginTopLeft,
    };

    bool create();
    bool isCreated() const;
    void destroy();
    bool supportsExternalOESTarget() const;
    void bind(GLenum target = GL_TEXTURE_2D);
    void release();
    void setRedBlueSwizzle(bool swizzle);
    void setOpacity(float opacity);
    void blit(GLuint texture, const QMatrix4x4 &targetTransform, QOpenGLTextureBlitter::Origin sourceOrigin);
    void blit(GLuint texture, const QMatrix4x4 &targetTransform, const QMatrix3x3 &sourceTransform);
    static QMatrix4x4 targetTransform(const QRectF &target, const QRect &viewport);
    static QMatrix3x3 sourceTransform(const QRectF &subTexture, const QSize &textureSize, QOpenGLTextureBlitter::Origin origin);

private:
    QOpenGLTextureBlitter(const QOpenGLTextureBlitter &);
};

%End
%End
