# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
These classes enable viewing and interacting with an OpenGL context in a wx.Window.
"""
#-- begin-_glcanvas --#

import wx

class _enum_22(IntEnum):
    WX_GL_RGBA = auto()
    WX_GL_BUFFER_SIZE = auto()
    WX_GL_LEVEL = auto()
    WX_GL_DOUBLEBUFFER = auto()
    WX_GL_STEREO = auto()
    WX_GL_AUX_BUFFERS = auto()
    WX_GL_MIN_RED = auto()
    WX_GL_MIN_GREEN = auto()
    WX_GL_MIN_BLUE = auto()
    WX_GL_MIN_ALPHA = auto()
    WX_GL_DEPTH_SIZE = auto()
    WX_GL_STENCIL_SIZE = auto()
    WX_GL_MIN_ACCUM_RED = auto()
    WX_GL_MIN_ACCUM_GREEN = auto()
    WX_GL_MIN_ACCUM_BLUE = auto()
    WX_GL_MIN_ACCUM_ALPHA = auto()
    WX_GL_SAMPLE_BUFFERS = auto()
    WX_GL_SAMPLES = auto()
    WX_GL_FRAMEBUFFER_SRGB = auto()
    WX_GL_MAJOR_VERSION = auto()
    WX_GL_MINOR_VERSION = auto()
    WX_GL_CORE_PROFILE = auto()
    WX_GL_COMPAT_PROFILE = auto()
    WX_GL_FORWARD_COMPAT = auto()
    WX_GL_ES2 = auto()
    WX_GL_DEBUG = auto()
    WX_GL_ROBUST_ACCESS = auto()
    WX_GL_NO_RESET_NOTIFY = auto()
    WX_GL_LOSE_ON_RESET = auto()
    WX_GL_RESET_ISOLATION = auto()
    WX_GL_RELEASE_FLUSH = auto()
    WX_GL_RELEASE_NONE = auto()
WX_GL_RGBA = _enum_22.WX_GL_RGBA
WX_GL_BUFFER_SIZE = _enum_22.WX_GL_BUFFER_SIZE
WX_GL_LEVEL = _enum_22.WX_GL_LEVEL
WX_GL_DOUBLEBUFFER = _enum_22.WX_GL_DOUBLEBUFFER
WX_GL_STEREO = _enum_22.WX_GL_STEREO
WX_GL_AUX_BUFFERS = _enum_22.WX_GL_AUX_BUFFERS
WX_GL_MIN_RED = _enum_22.WX_GL_MIN_RED
WX_GL_MIN_GREEN = _enum_22.WX_GL_MIN_GREEN
WX_GL_MIN_BLUE = _enum_22.WX_GL_MIN_BLUE
WX_GL_MIN_ALPHA = _enum_22.WX_GL_MIN_ALPHA
WX_GL_DEPTH_SIZE = _enum_22.WX_GL_DEPTH_SIZE
WX_GL_STENCIL_SIZE = _enum_22.WX_GL_STENCIL_SIZE
WX_GL_MIN_ACCUM_RED = _enum_22.WX_GL_MIN_ACCUM_RED
WX_GL_MIN_ACCUM_GREEN = _enum_22.WX_GL_MIN_ACCUM_GREEN
WX_GL_MIN_ACCUM_BLUE = _enum_22.WX_GL_MIN_ACCUM_BLUE
WX_GL_MIN_ACCUM_ALPHA = _enum_22.WX_GL_MIN_ACCUM_ALPHA
WX_GL_SAMPLE_BUFFERS = _enum_22.WX_GL_SAMPLE_BUFFERS
WX_GL_SAMPLES = _enum_22.WX_GL_SAMPLES
WX_GL_FRAMEBUFFER_SRGB = _enum_22.WX_GL_FRAMEBUFFER_SRGB
WX_GL_MAJOR_VERSION = _enum_22.WX_GL_MAJOR_VERSION
WX_GL_MINOR_VERSION = _enum_22.WX_GL_MINOR_VERSION
WX_GL_CORE_PROFILE = _enum_22.WX_GL_CORE_PROFILE
WX_GL_COMPAT_PROFILE = _enum_22.WX_GL_COMPAT_PROFILE
WX_GL_FORWARD_COMPAT = _enum_22.WX_GL_FORWARD_COMPAT
WX_GL_ES2 = _enum_22.WX_GL_ES2
WX_GL_DEBUG = _enum_22.WX_GL_DEBUG
WX_GL_ROBUST_ACCESS = _enum_22.WX_GL_ROBUST_ACCESS
WX_GL_NO_RESET_NOTIFY = _enum_22.WX_GL_NO_RESET_NOTIFY
WX_GL_LOSE_ON_RESET = _enum_22.WX_GL_LOSE_ON_RESET
WX_GL_RESET_ISOLATION = _enum_22.WX_GL_RESET_ISOLATION
WX_GL_RELEASE_FLUSH = _enum_22.WX_GL_RELEASE_FLUSH
WX_GL_RELEASE_NONE = _enum_22.WX_GL_RELEASE_NONE

class GLAttribsBase:
    """
    GLAttribsBase() -> None
    
    This is the base class for wxGLAttributes and wxGLContextAttrs.
    """

    def __init__(self) -> None:
        """
        GLAttribsBase() -> None
        
        This is the base class for wxGLAttributes and wxGLContextAttrs.
        """

    def AddAttribute(self, attribute: int) -> None:
        """
        AddAttribute(attribute) -> None
        
        Adds an integer value to the list of attributes.
        """

    def AddAttribBits(self, searchVal: int, combineVal: int) -> None:
        """
        AddAttribBits(searchVal, combineVal) -> None
        
        Combine (bitwise OR) a given value with the existing one, if any.
        """

    def SetNeedsARB(self, needsARB: bool=True) -> None:
        """
        SetNeedsARB(needsARB=True) -> None
        
        Sets the necessity of using special ARB-functions (e.g.
        """

    def Reset(self) -> None:
        """
        Reset() -> None
        
        Delete contents and sets ARB-flag to false.
        """

    def GetSize(self) -> int:
        """
        GetSize() -> int
        
        Returns the size of the internal list of attributes.
        """

    def NeedsARB(self) -> bool:
        """
        NeedsARB() -> bool
        
        Returns the current value of the ARB-flag.
        """
    @property
    def Size(self) -> int: ...
# end of class GLAttribsBase


class GLAttributes(GLAttribsBase):
    """
    This class is used for setting display attributes when drawing through
    OpenGL ("Pixel format" in MSW and OSX parlance, "Configs" in X11).
    """

    def RGBA(self) -> GLAttributes:
        """
        RGBA() -> GLAttributes
        
        Use true colour instead of colour index rendering for each pixel.
        """

    def BufferSize(self, val: int) -> GLAttributes:
        """
        BufferSize(val) -> GLAttributes
        
        Specifies the number of bits for colour buffer.
        """

    def Level(self, val: int) -> GLAttributes:
        """
        Level(val) -> GLAttributes
        
        Specifies the framebuffer level.
        """

    def DoubleBuffer(self) -> GLAttributes:
        """
        DoubleBuffer() -> GLAttributes
        
        Requests using double buffering.
        """

    def Stereo(self) -> GLAttributes:
        """
        Stereo() -> GLAttributes
        
        Use stereoscopic display.
        """

    def AuxBuffers(self, val: int) -> GLAttributes:
        """
        AuxBuffers(val) -> GLAttributes
        
        Specifies the number of auxiliary buffers.
        """

    def MinRGBA(self, mRed: int, mGreen: int, mBlue: int, mAlpha: int) -> GLAttributes:
        """
        MinRGBA(mRed, mGreen, mBlue, mAlpha) -> GLAttributes
        
        Specifies the minimal number of bits for each colour and alpha.
        """

    def Depth(self, val: int) -> GLAttributes:
        """
        Depth(val) -> GLAttributes
        
        Specifies number of bits for Z-buffer.
        """

    def Stencil(self, val: int) -> GLAttributes:
        """
        Stencil(val) -> GLAttributes
        
        Specifies number of bits for stencil buffer.
        """

    def MinAcumRGBA(self, mRed: int, mGreen: int, mBlue: int, mAlpha: int) -> GLAttributes:
        """
        MinAcumRGBA(mRed, mGreen, mBlue, mAlpha) -> GLAttributes
        
        Specifies the minimal number of bits for each accumulator channel.
        """

    def SampleBuffers(self, val: int) -> GLAttributes:
        """
        SampleBuffers(val) -> GLAttributes
        
        Use multi-sampling support (antialiasing).
        """

    def Samplers(self, val: int) -> GLAttributes:
        """
        Samplers(val) -> GLAttributes
        
        Specifies the number of samplers per pixel.
        """

    def FrameBuffersRGB(self) -> GLAttributes:
        """
        FrameBuffersRGB() -> GLAttributes
        
        Used to request a frame buffer sRGB capable.
        """

    def PlatformDefaults(self) -> GLAttributes:
        """
        PlatformDefaults() -> GLAttributes
        
        Set some typically needed attributes.
        """

    def Defaults(self) -> GLAttributes:
        """
        Defaults() -> GLAttributes
        
        wxWidgets defaults: RGBA, Z-depth 16 bits, double buffering, 1 sample
        buffer, 4 samplers.
        """

    def EndList(self) -> None:
        """
        EndList() -> None
        
        The set of attributes must end with this one; otherwise, the GPU may
        display nothing at all.
        """
# end of class GLAttributes


class GLContextAttrs(GLAttribsBase):
    """
    This class is used for setting context attributes.
    """

    def CoreProfile(self) -> GLContextAttrs:
        """
        CoreProfile() -> GLContextAttrs
        
        Request an OpenGL core profile for the context.
        """

    def MajorVersion(self, val: int) -> GLContextAttrs:
        """
        MajorVersion(val) -> GLContextAttrs
        
        Request specific OpenGL core major version number (>= 3).
        """

    def MinorVersion(self, val: int) -> GLContextAttrs:
        """
        MinorVersion(val) -> GLContextAttrs
        
        Request specific OpenGL core minor version number.
        """

    def OGLVersion(self, vmayor: int, vminor: int) -> GLContextAttrs:
        """
        OGLVersion(vmayor, vminor) -> GLContextAttrs
        
        An easy way of requesting an OpenGL version.
        """

    def CompatibilityProfile(self) -> GLContextAttrs:
        """
        CompatibilityProfile() -> GLContextAttrs
        
        Request a type of context with all OpenGL features from version 1.0 to
        the newest available by the GPU driver.
        """

    def ForwardCompatible(self) -> GLContextAttrs:
        """
        ForwardCompatible() -> GLContextAttrs
        
        Request a forward-compatible context.
        """

    def ES2(self) -> GLContextAttrs:
        """
        ES2() -> GLContextAttrs
        
        Request an ES or ES2 ("Embedded Subsystem") context.
        """

    def DebugCtx(self) -> GLContextAttrs:
        """
        DebugCtx() -> GLContextAttrs
        
        Request debugging functionality.
        """

    def Robust(self) -> GLContextAttrs:
        """
        Robust() -> GLContextAttrs
        
        Request robustness, or how OpenGL handles out-of-bounds buffer object
        accesses and graphics reset notification behaviours.
        """

    def NoResetNotify(self) -> GLContextAttrs:
        """
        NoResetNotify() -> GLContextAttrs
        
        With robustness enabled, never deliver notification of reset events.
        """

    def LoseOnReset(self) -> GLContextAttrs:
        """
        LoseOnReset() -> GLContextAttrs
        
        With robustness enabled, if graphics reset happens, all context state
        is lost.
        """

    def ResetIsolation(self) -> GLContextAttrs:
        """
        ResetIsolation() -> GLContextAttrs
        
        Request OpenGL to protect other applications or shared contexts from
        reset side-effects.
        """

    def ReleaseFlush(self, val: int=1) -> GLContextAttrs:
        """
        ReleaseFlush(val=1) -> GLContextAttrs
        
        Request OpenGL to avoid or not flushing pending commands when the
        context is made no longer current (released).
        """

    def PlatformDefaults(self) -> GLContextAttrs:
        """
        PlatformDefaults() -> GLContextAttrs
        
        Set platform specific defaults.
        """

    def EndList(self) -> None:
        """
        EndList() -> None
        
        The set of attributes must end with this one; otherwise, the GPU may
        display nothing at all.
        """
# end of class GLContextAttrs


class GLContext(Object):
    """
    GLContext(win, other=None, ctxAttrs=None) -> None
    
    An instance of a wxGLContext represents the state of an OpenGL state
    machine and the connection between OpenGL and the system.
    """

    def __init__(self, win: GLCanvas, other: Optional[GLContext]=None, ctxAttrs: Optional[GLContextAttrs]=None) -> None:
        """
        GLContext(win, other=None, ctxAttrs=None) -> None
        
        An instance of a wxGLContext represents the state of an OpenGL state
        machine and the connection between OpenGL and the system.
        """

    def IsOK(self) -> bool:
        """
        IsOK() -> bool
        
        Checks if the underlying OpenGL rendering context was correctly
        created by the system with the requested attributes.
        """

    def SetCurrent(self, win: GLCanvas) -> bool:
        """
        SetCurrent(win) -> bool
        
        Makes the OpenGL state that is represented by this rendering context
        current with the wxGLCanvas win.
        """
# end of class GLContext


class GLCanvas(Window):
    """
    GLCanvas(parent, dispAttrs, id=ID_ANY, pos=DefaultPosition, size=DefaultSize, style=0, name=GLCanvasName, palette=NullPalette) -> None
    GLCanvas(parent, id=wx.ID_ANY, attribList=None, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, name='GLCanvas', palette=wx.NullPalette)
    
    wxGLCanvas is a class for displaying OpenGL graphics.
    """

    @overload
    def __init__(self, TransferThis: Windowparent, id: int=ID_ANY, attribList: Optional[List[int]]=None, pos: Point=DefaultPosition, size: Size=DefaultSize, style: int=0, name: str="GLCanvas", palette: Palette=NullPalette) -> None:
        ...

    @overload
    def __init__(self, parent: Window, dispAttrs: GLAttributes, id: int=ID_ANY, pos: Point=DefaultPosition, size: Size=DefaultSize, style: int=0, name: str=GLCanvasName, palette: Palette=NullPalette) -> None:
        """
        GLCanvas(parent, dispAttrs, id=ID_ANY, pos=DefaultPosition, size=DefaultSize, style=0, name=GLCanvasName, palette=NullPalette) -> None
        GLCanvas(parent, id=wx.ID_ANY, attribList=None, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, name='GLCanvas', palette=wx.NullPalette)
        
        wxGLCanvas is a class for displaying OpenGL graphics.
        """

    def CreateSurface(self) -> bool:
        """
        CreateSurface() -> bool
        
        Re-creates EGLSurface.
        """

    def SetColour(self, colour: str) -> bool:
        """
        SetColour(colour) -> bool
        
        Sets the current colour for this window (using glcolor3f()), using the
        wxWidgets colour database to find a named colour.
        """

    def SetCurrent(self, context: GLContext) -> bool:
        """
        SetCurrent(context) -> bool
        
        Makes the OpenGL state that is represented by the OpenGL rendering
        context context current, i.e.
        """

    def SwapBuffers(self) -> bool:
        """
        SwapBuffers() -> bool
        
        Swaps the double-buffer of this window, making the back-buffer the
        front-buffer and vice versa, so that the output of the previous OpenGL
        commands is displayed on the window.
        """

    @overload
    @staticmethod
    def IsDisplaySupported(attribList: List[int]) -> bool:
        ...

    @overload
    @staticmethod
    def IsDisplaySupported(dispAttrs: GLAttributes) -> bool:
        """
        IsDisplaySupported(dispAttrs) -> bool
        IsDisplaySupported(attribList) -> bool
        
        Determines if a canvas having the specified attributes is available.
        """

    @staticmethod
    def IsExtensionSupported(extension: str) -> bool:
        """
        IsExtensionSupported(extension) -> bool
        
        Returns true if the extension with given name is supported.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: WindowVariant=WINDOW_VARIANT_NORMAL) -> VisualAttributes:
        """
        GetClassDefaultAttributes(variant=WINDOW_VARIANT_NORMAL) -> VisualAttributes
        """
# end of class GLCanvas

USE_GLCANVAS: int
#-- end-_glcanvas --#
