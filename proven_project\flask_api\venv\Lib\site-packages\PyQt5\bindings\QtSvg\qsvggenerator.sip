// qsvggenerator.sip generated by MetaSIP
//
// This file is part of the QtSvg Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSvgGenerator : public QPaintDevice
{
%TypeHeaderCode
#include <qsvggenerator.h>
%End

public:
    QSvgGenerator();
    virtual ~QSvgGenerator();
    QSize size() const;
    void setSize(const QSize &size);
    QString fileName() const;
    void setFileName(const QString &fileName);
    QIODevice *outputDevice() const;
    void setOutputDevice(QIODevice *outputDevice);
    int resolution() const;
    void setResolution(int resolution);
    QString title() const;
    void setTitle(const QString &title);
    QString description() const;
    void setDescription(const QString &description);
    QRect viewBox() const;
    QRectF viewBoxF() const;
    void setViewBox(const QRect &viewBox);
    void setViewBox(const QRectF &viewBox);

protected:
    virtual QPaintEngine *paintEngine() const;
    virtual int metric(QPaintDevice::PaintDeviceMetric metric) const;
};
