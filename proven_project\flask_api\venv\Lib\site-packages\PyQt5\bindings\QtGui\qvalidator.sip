// qvalidator.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QValidator : public QObject
{
%TypeHeaderCode
#include <qvalidator.h>
%End

public:
    explicit QValidator(QObject *parent /TransferThis/ = 0);
    virtual ~QValidator();

    enum State
    {
        Invalid,
        Intermediate,
        Acceptable,
    };

    virtual QValidator::State validate(QString & /In,Out/, int & /In,Out/) const = 0;
    virtual void fixup(QString & /In,Out/) const;
    void setLocale(const QLocale &locale);
    QLocale locale() const;

signals:
    void changed();
};

class QIntValidator : public QValidator
{
%TypeHeaderCode
#include <qvalidator.h>
%End

public:
    explicit QIntValidator(QObject *parent /TransferThis/ = 0);
    QIntValidator(int bottom, int top, QObject *parent /TransferThis/ = 0);
    virtual ~QIntValidator();
    virtual QValidator::State validate(QString & /In,Out/, int & /In,Out/) const;
    virtual void fixup(QString &input /In,Out/) const;
    void setBottom(int);
    void setTop(int);
    virtual void setRange(int bottom, int top);
    int bottom() const;
    int top() const;
};

class QDoubleValidator : public QValidator
{
%TypeHeaderCode
#include <qvalidator.h>
%End

public:
    explicit QDoubleValidator(QObject *parent /TransferThis/ = 0);
    QDoubleValidator(double bottom, double top, int decimals, QObject *parent /TransferThis/ = 0);
    virtual ~QDoubleValidator();
    virtual QValidator::State validate(QString & /In,Out/, int & /In,Out/) const;
    virtual void setRange(double minimum, double maximum, int decimals = 0);
    void setBottom(double);
    void setTop(double);
    void setDecimals(int);
    double bottom() const;
    double top() const;
    int decimals() const;

    enum Notation
    {
        StandardNotation,
        ScientificNotation,
    };

    void setNotation(QDoubleValidator::Notation);
    QDoubleValidator::Notation notation() const;
};

class QRegExpValidator : public QValidator
{
%TypeHeaderCode
#include <qvalidator.h>
%End

public:
    explicit QRegExpValidator(QObject *parent /TransferThis/ = 0);
    QRegExpValidator(const QRegExp &rx, QObject *parent /TransferThis/ = 0);
    virtual ~QRegExpValidator();
    virtual QValidator::State validate(QString &input /In,Out/, int &pos /In,Out/) const;
    void setRegExp(const QRegExp &rx);
    const QRegExp &regExp() const;
};

%If (Qt_5_1_0 -)

class QRegularExpressionValidator : public QValidator
{
%TypeHeaderCode
#include <qvalidator.h>
%End

public:
    explicit QRegularExpressionValidator(QObject *parent /TransferThis/ = 0);
    QRegularExpressionValidator(const QRegularExpression &re, QObject *parent /TransferThis/ = 0);
    virtual ~QRegularExpressionValidator();
    virtual QValidator::State validate(QString &input /In,Out/, int &pos /In,Out/) const;
    QRegularExpression regularExpression() const;
    void setRegularExpression(const QRegularExpression &re);
};

%End
