// qdeadlinetimer.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_8_0 -)

class QDeadlineTimer
{
%TypeHeaderCode
#include <qdeadlinetimer.h>
%End

public:
    enum ForeverConstant
    {
        Forever,
    };

    QDeadlineTimer(Qt::TimerType type /Constrained/ = Qt::CoarseTimer);
    QDeadlineTimer(QDeadlineTimer::ForeverConstant /Constrained/, Qt::TimerType type /Constrained/ = Qt::CoarseTimer);
    QDeadlineTimer(qint64 msecs, Qt::TimerType type /Constrained/ = Qt::CoarseTimer);
    void swap(QDeadlineTimer &other /Constrained/);
    bool isForever() const;
    bool hasExpired() const;
    Qt::TimerType timerType() const;
    void setTimerType(Qt::TimerType type);
    qint64 remainingTime() const;
    qint64 remainingTimeNSecs() const;
    void setRemainingTime(qint64 msecs, Qt::TimerType type = Qt::CoarseTimer);
    void setPreciseRemainingTime(qint64 secs, qint64 nsecs = 0, Qt::TimerType type = Qt::CoarseTimer);
    qint64 deadline() const;
    qint64 deadlineNSecs() const;
    void setDeadline(qint64 msecs, Qt::TimerType type = Qt::CoarseTimer);
    void setPreciseDeadline(qint64 secs, qint64 nsecs = 0, Qt::TimerType type = Qt::CoarseTimer);
    static QDeadlineTimer addNSecs(QDeadlineTimer dt, qint64 nsecs);
    static QDeadlineTimer current(Qt::TimerType type = Qt::CoarseTimer);
    QDeadlineTimer &operator+=(qint64 msecs);
    QDeadlineTimer &operator-=(qint64 msecs);
};

%End
%If (Qt_5_8_0 -)
bool operator==(QDeadlineTimer d1, QDeadlineTimer d2);
%End
%If (Qt_5_8_0 -)
bool operator!=(QDeadlineTimer d1, QDeadlineTimer d2);
%End
%If (Qt_5_8_0 -)
bool operator<(QDeadlineTimer d1, QDeadlineTimer d2);
%End
%If (Qt_5_8_0 -)
bool operator<=(QDeadlineTimer d1, QDeadlineTimer d2);
%End
%If (Qt_5_8_0 -)
bool operator>(QDeadlineTimer d1, QDeadlineTimer d2);
%End
%If (Qt_5_8_0 -)
bool operator>=(QDeadlineTimer d1, QDeadlineTimer d2);
%End
%If (Qt_5_8_0 -)
QDeadlineTimer operator+(QDeadlineTimer dt, qint64 msecs);
%End
%If (Qt_5_8_0 -)
QDeadlineTimer operator+(qint64 msecs, QDeadlineTimer dt);
%End
%If (Qt_5_8_0 -)
QDeadlineTimer operator-(QDeadlineTimer dt, qint64 msecs);
%End
%If (Qt_5_8_0 -)
qint64 operator-(QDeadlineTimer dt1, QDeadlineTimer dt2);
%End
