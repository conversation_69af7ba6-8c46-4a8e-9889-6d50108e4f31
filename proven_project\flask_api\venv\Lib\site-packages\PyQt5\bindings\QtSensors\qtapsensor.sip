// qtapsensor.sip generated by MetaSIP
//
// This file is part of the QtSensors Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_1_0 -)

class QTapReading : public QSensorReading /NoDefaultCtors/
{
%TypeHeaderCode
#include <qtapsensor.h>
%End

public:
    enum TapDirection
    {
        Undefined,
        X,
        Y,
        Z,
        X_Pos,
        Y_Pos,
        Z_Pos,
        X_Neg,
        Y_Neg,
        Z_Neg,
        X_Both,
        Y_Both,
        Z_Both,
    };

    QTapReading::TapDirection tapDirection() const;
    void setTapDirection(QTapReading::TapDirection tapDirection);
    bool isDoubleTap() const;
    void setDoubleTap(bool doubleTap);
};

%End
%If (Qt_5_1_0 -)

class QTapFilter : public QSensorFilter
{
%TypeHeaderCode
#include <qtapsensor.h>
%End

public:
    virtual bool filter(QTapReading *reading) = 0;
};

%End
%If (Qt_5_1_0 -)

class QTapSensor : public QSensor
{
%TypeHeaderCode
#include <qtapsensor.h>
%End

public:
    explicit QTapSensor(QObject *parent /TransferThis/ = 0);
    virtual ~QTapSensor();
    QTapReading *reading() const;
    bool returnDoubleTapEvents() const;
    void setReturnDoubleTapEvents(bool returnDoubleTapEvents);

signals:
    void returnDoubleTapEventsChanged(bool returnDoubleTapEvents);

private:
    QTapSensor(const QTapSensor &);
};

%End
