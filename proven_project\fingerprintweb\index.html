<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Fingerprint API Client</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    label {
      display: block;
      margin-top: 10px;
    }
    input, select, button {
      margin-top: 5px;
      padding: 8px;
      font-size: 1em;
    }
    button {
      margin-right: 10px;
      cursor: pointer;
    }
    #status {
      margin-top: 15px;
      font-weight: bold;
      color: #0b5394;
    }
    #response {
      margin-top: 10px;
      white-space: pre-wrap;
      background-color: #f4f4f4;
      padding: 10px;
      border: 1px solid #ccc;
      max-height: 300px;
      overflow-y: auto;
    }
    #bmpImage {
      margin-top: 20px;
      max-width: 300px;
      max-height: 300px;
      border: 1px solid #ccc;
    }
    .spinner {
      display: inline-block;
      width: 24px;
      height: 24px;
      border: 4px solid #ccc;
      border-top: 4px solid #4caf50;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      vertical-align: middle;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }  
  </style>
</head>
<body>
  <h1>Fingerprint API Client</h1>

  <label for="personId">Person ID / ລະຫັດບຸກຄົນ:</label>
  <input type="text" id="personId" placeholder="Enter person ID / ປ້ອນລະຫັດບຸກຄົນ" />

  <label for="fingerName">Finger Name / ຊື່ນິ້ວ:</label>
  <select id="fingerName">
    <option value="1">Right Thumb / ນິ້ວໂປ້ຂວາ</option>
    <option value="2">Right Index / ນິອງຊີ້ຂວາ</option>
    <option value="3">Right Middle / ນິ້ວກາງຂວາ</option>
    <option value="4">Right Ring / ນິ້ວນາງຂວາ</option>
    <option value="5">Right Small / ນິ້ວກ້ອຍຂວາ</option>
    <option value="6">Left Thumb / ນິ້ວໂປ້ຊ້າຍ</option>
    <option value="7">Left Index / ນິ້ວຊີ້ຊ້າຍ</option>
    <option value="8">Left Middle / ນິ້ວກາງຊ້າຍ</option>
    <option value="9">Left Ring / ນິ້ວນາງຊ້າຍ</option>
    <option value="10">Left Small / ນິ້ວກ້ອຍຊ້າຍ</option>
  </select>

  <div style="margin-top: 15px;">
    <button id="captureBtn">Capture / ສະແກນລາຍນິ້ວມື</button>
    <button id="verifyBtn">Verify / ຢືນຢັນລາຍນິ້ວມື</button>
    <button id="matchBtn">Match / ຊອກຄົ້ນລາຍນິ້ວມື</button>
  </div>

  <div id="status"></div>
  <div id="response"></div>
  <img id="bmpImage" alt="Fingerprint BMP Image" style="display:none;" />

  <script>
    const apiBaseUrl = 'http://localhost:5001';

    const personIdInput = document.getElementById('personId');
    const fingerNameSelect = document.getElementById('fingerName');
    const responseDiv = document.getElementById('response');
    const bmpImage = document.getElementById('bmpImage');
    const statusDiv = document.getElementById('status');
    const captureBtn = document.getElementById('captureBtn');
    const verifyBtn = document.getElementById('verifyBtn');
    const matchBtn = document.getElementById('matchBtn');

    function setInputsDisabled(disabled) {
      personIdInput.disabled = disabled;
      fingerNameSelect.disabled = disabled;
      captureBtn.disabled = disabled;
      verifyBtn.disabled = disabled;
      matchBtn.disabled = disabled;
    }

    function displayStatus(text) {
      statusDiv.textContent = text;
    }

    function displayResponse(data) {
      if (data.error) {
        responseDiv.innerHTML = `<span style="color: red;">❌ Error:</span> ${data.error}`;
      } else if (data.status === "success") {
        responseDiv.innerHTML = `<span style="color: green;">✅ Fingerprint successfully captured and saved.</span>`;
      } else if (data.status === "match") {
        responseDiv.innerHTML = `<span style="color: green;">✅ Match found!</span><br/><code>${data.message}</code>`;
      } else if (data.status === "no_match") {
        responseDiv.innerHTML = `<span style="color: orange;">❌ No match.</span><br/><code>${data.message}</code>`;
      } else if (data.status === "error") {
        responseDiv.innerHTML = `<span style="color: red;">❌ Error:</span><br/><code>${data.message}</code>`;
      } else {
        responseDiv.textContent = JSON.stringify(data, null, 2);
      }
    }

    function displayBmpImage(base64Bmp) {
      if (base64Bmp) {
        bmpImage.src = 'data:image/bmp;base64,' + base64Bmp;
        bmpImage.style.display = 'block';
      } else {
        bmpImage.style.display = 'none';
        bmpImage.src = '';
      }
    }

    async function callApi(endpoint, body, label) {
      // Step 1: Reset UI before request
      displayStatus(`⏳ Processing: ${label}...`);
      // responseDiv.innerHTML = `<span style="color:#999;">🔄 Please wait...</span>`;
      responseDiv.innerHTML = `<div><span class="spinner"></span>Processing <b>${label}</b>...</div>`;

      displayBmpImage(null);
      setInputsDisabled(true);

      try {
        const response = await fetch(apiBaseUrl + endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: body ? JSON.stringify(body) : null
        });

        const data = await response.json();
        displayResponse(data);
        displayStatus(`✅ Completed: ${label}`);

        if (data.bmp_base64) {
          displayBmpImage(data.bmp_base64);
        }
      } catch (error) {
        displayStatus(`❌ Error: ${error.message}`);
        displayResponse({ error: error.message });
        displayBmpImage(null);
      } finally {
        setInputsDisabled(false);
      }
    }

    captureBtn.addEventListener('click', () => {
      const person_id = personIdInput.value.trim();
      const finger_index = parseInt(fingerNameSelect.value);
      if (!person_id) {
        alert('Please enter a person ID.');
        return;
      }
      callApi('/capture', { person_id, finger_index }, 'Capture');
    });

    verifyBtn.addEventListener('click', () => {
      const person_id = personIdInput.value.trim();
      const finger_index = parseInt(fingerNameSelect.value);
      if (!person_id) {
        alert('Please enter a person ID.');
        return;
      }
      callApi('/verify', { person_id, finger_index }, 'Verify');
    });

    matchBtn.addEventListener('click', () => {
      callApi('/match', null, 'Match');
    });
  </script>

</body>
</html>
