{"format": 1, "restore": {"D:\\AratekTrustFinger\\FingerBridgeTestAppAFIS1\\FingerBridgeTestApp.csproj": {}}, "projects": {"D:\\AratekTrustFinger\\FingerBridgeTestAppAFIS1\\FingerBridgeTestApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AratekTrustFinger\\FingerBridgeTestAppAFIS1\\FingerBridgeTestApp.csproj", "projectName": "FingerBridge", "projectPath": "D:\\AratekTrustFinger\\FingerBridgeTestAppAFIS1\\FingerBridgeTestApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AratekTrustFinger\\FingerBridgeTestAppAFIS1\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"MySqlConnector": {"target": "Package", "version": "[2.4.0, )"}, "SourceAFIS": {"target": "Package", "version": "[3.14.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}