2025-06-27 12:54:02,312 INFO Starting Flask API service on http://0.0.0.0:5001
2025-06-27 12:54:02,327 INFO [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://**************:5001
2025-06-27 12:54:02,328 INFO [33mPress CTRL+C to quit[0m
2025-06-27 12:55:11,472 ERROR An error occurred when calling message handler
Traceback (most recent call last):
  File "D:\AratekTrustFinger\flask_api\venv\Lib\site-packages\pystray\_win32.py", line 401, in _dispatcher
    return int(icon._message_handlers.get(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AratekTrustFinger\flask_api\venv\Lib\site-packages\pystray\_win32.py", line 213, in _on_notify
    descriptors[index - 1](self)
  File "D:\AratekTrustFinger\flask_api\venv\Lib\site-packages\pystray\_base.py", line 324, in inner
    callback(self)
  File "D:\AratekTrustFinger\flask_api\venv\Lib\site-packages\pystray\_base.py", line 449, in __call__
    return self._action(icon, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AratekTrustFinger\flask_api\fingerprintapi.py", line 53, in show_about
    about_window = tk.Tk()
                   ^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\tkinter\__init__.py", line 2346, in __init__
    self.tk = _tkinter.create(screenName, baseName, className, interactive, wantobjects, useTk, sync, use)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: Can't find a usable init.tcl in the following directories: 
    {C:\Users\<USER>\AppData\Local\Programs\Python\Python313\tcl\tcl8.6} {C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lib/tcl8.6} {C:/Users/<USER>/AppData/Local/Programs/Python/lib/tcl8.6} {C:/Users/<USER>/AppData/Local/Programs/lib/tcl8.6} {C:/Users/<USER>/AppData/Local/Programs/Python/library} {C:/Users/<USER>/AppData/Local/Programs/library} {C:/Users/<USER>/AppData/Local/Programs/tcl8.6.15/library} {C:/Users/<USER>/AppData/Local/tcl8.6.15/library}



This probably means that Tcl wasn't installed properly.

2025-06-27 12:57:53,499 ERROR An error occurred when calling message handler
Traceback (most recent call last):
  File "D:\AratekTrustFinger\flask_api\venv\Lib\site-packages\pystray\_win32.py", line 401, in _dispatcher
    return int(icon._message_handlers.get(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AratekTrustFinger\flask_api\venv\Lib\site-packages\pystray\_win32.py", line 213, in _on_notify
    descriptors[index - 1](self)
  File "D:\AratekTrustFinger\flask_api\venv\Lib\site-packages\pystray\_base.py", line 324, in inner
    callback(self)
  File "D:\AratekTrustFinger\flask_api\venv\Lib\site-packages\pystray\_base.py", line 449, in __call__
    return self._action(icon, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AratekTrustFinger\flask_api\fingerprintapi.py", line 49, in show_about
    import win32gui
ModuleNotFoundError: No module named 'win32gui'
