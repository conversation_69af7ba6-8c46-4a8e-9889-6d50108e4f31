<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Minimal CSP Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 5px;
    }
    button {
      padding: 10px 20px;
      margin: 10px;
      border: 1px solid #ccc;
      background: #f8f9fa;
      cursor: pointer;
    }
    .result {
      margin-top: 10px;
      padding: 10px;
      background: #f8f9fa;
      border-left: 4px solid #007bff;
    }
    .success {
      border-left-color: #28a745;
      background: #d4edda;
    }
    .error {
      border-left-color: #dc3545;
      background: #f8d7da;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Minimal CSP Test</h1>
    <p>Testing if basic JavaScript works without CSP violations.</p>

    <button onclick="testBasic()">Test Basic JS</button>
    <button onclick="testAPI()">Test API Call</button>
    <button onclick="testCapture()">Test Capture</button>

    <div id="result" class="result" style="display: none;"></div>
  </div>

  <script>
    // Ultra-basic JavaScript - no modern features at all
    function showResult(message, type) {
      var resultDiv = document.getElementById('result');
      resultDiv.style.display = 'block';
      resultDiv.innerHTML = message;
      resultDiv.className = 'result ' + (type || '');
    }

    function testBasic() {
      showResult('✅ Basic JavaScript works!', 'success');
    }

    function testAPI() {
      var xhr = new XMLHttpRequest();
      xhr.open('GET', 'http://localhost:5001/api/health', true);
      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            showResult('✅ API connection works!', 'success');
          } else {
            showResult('❌ API connection failed: ' + xhr.status, 'error');
          }
        }
      };
      xhr.onerror = function() {
        showResult('❌ Network error', 'error');
      };
      xhr.send();
    }

    function testCapture() {
      var captureData = {
        finger_position: 12,
        operation_type: 'slaps',
        timeout: 30,
        save_image: true
      };

      var xhr = new XMLHttpRequest();
      xhr.open('POST', 'http://localhost:5001/api/fingerprint/capture', true);
      xhr.setRequestHeader('Content-Type', 'application/json');
      
      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            var result = JSON.parse(xhr.responseText);
            if (result.success) {
              showResult('✅ Capture API works!', 'success');
            } else {
              showResult('❌ Capture failed: ' + (result.error || result.message), 'error');
            }
          } else {
            showResult('❌ Capture API failed: ' + xhr.status, 'error');
          }
        }
      };
      
      xhr.onerror = function() {
        showResult('❌ Capture network error', 'error');
      };
      
      xhr.send(JSON.stringify(captureData));
    }
  </script>
</body>
</html>
