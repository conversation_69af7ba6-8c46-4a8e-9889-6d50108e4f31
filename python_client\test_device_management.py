#!/usr/bin/env python3
"""
Test Device Management Commands
Tests the new device management TCP commands: OPEN, CLOSE, STATUS
"""

import sys
import time
from tcp_client import TcpClient, TcpClientError

def test_device_info():
    """Test device info/status command"""
    print("\n📊 Testing DEVICE INFO command...")
    
    try:
        client = TcpClient()
        result = client.get_device_info()
        
        print(f"   📡 Response: {result}")
        
        # Parse the result to show device status
        if 'status' in result:
            status = result['status']
            if status == 'OPEN':
                print(f"   ✅ Device is OPEN")
                if 'device_name' in result:
                    print(f"   📱 Device: {result['device_name']} (ID: {result.get('device_id', 'Unknown')})")
                if 'serial_number' in result:
                    print(f"   🔢 Serial: {result['serial_number']}")
            else:
                print(f"   ❌ Device is CLOSED")
        
        return True
        
    except TcpClientError as e:
        print(f"   ❌ Device info error: {e}")
        return False

def test_open_device():
    """Test open device command"""
    print("\n🔓 Testing OPEN DEVICE command...")
    
    try:
        client = TcpClient()
        result = client.open_device()
        
        print(f"   📡 Response: {result}")
        
        # Parse the result
        if 'status' in result:
            status = result['status']
            if status == 'OPEN':
                print(f"   ✅ Device opened successfully")
                if 'device_name' in result:
                    print(f"   📱 Device: {result['device_name']} (ID: {result.get('device_id', 'Unknown')})")
            elif status == 'ERROR':
                print(f"   ❌ Failed to open device: {result.get('message', 'Unknown error')}")
            else:
                print(f"   ⚠️ Unexpected status: {status}")
        
        return True
        
    except TcpClientError as e:
        print(f"   ❌ Open device error: {e}")
        return False

def test_close_device():
    """Test close device command"""
    print("\n🔒 Testing CLOSE DEVICE command...")
    
    try:
        client = TcpClient()
        result = client.close_device()
        
        print(f"   📡 Response: {result}")
        
        # Parse the result
        if 'status' in result:
            status = result['status']
            if status == 'CLOSED':
                print(f"   ✅ Device closed successfully")
            else:
                print(f"   ⚠️ Unexpected status: {status}")
        
        return True
        
    except TcpClientError as e:
        print(f"   ❌ Close device error: {e}")
        return False

def main():
    """Run all device management tests"""
    print("🧪 Device Management TCP Commands Test")
    print("=" * 50)
    
    # Test connection first
    print("\n🔌 Testing TCP connection...")
    try:
        client = TcpClient()
        if client.test_connection():
            print("   ✅ TCP connection successful")
        else:
            print("   ❌ TCP connection failed")
            print("   💡 Make sure the C# bridge application is running on port 8123")
            return
    except Exception as e:
        print(f"   ❌ Connection test error: {e}")
        return
    
    # Test device management commands
    tests = [
        ("Device Info (Initial)", test_device_info),
        ("Open Device", test_open_device),
        ("Device Info (After Open)", test_device_info),
        ("Close Device", test_close_device),
        ("Device Info (After Close)", test_device_info),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
            if success:
                print(f"   ✅ {test_name} - PASSED")
            else:
                print(f"   ❌ {test_name} - FAILED")
        except Exception as e:
            print(f"   💥 {test_name} - EXCEPTION: {e}")
            results.append((test_name, False))
        
        # Small delay between tests
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} - {test_name}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All device management tests passed!")
    else:
        print("⚠️ Some tests failed. Check the C# bridge application logs.")

if __name__ == "__main__":
    main()
