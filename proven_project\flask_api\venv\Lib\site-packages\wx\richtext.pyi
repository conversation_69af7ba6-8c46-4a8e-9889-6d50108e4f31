# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
The :class:`RichTextCtrl` is a generic, ground-up implementation of a rich
text control capable of showing multiple text styles and images.  This module
contains the control and many supporting classes needed for using the features
of the :class:`RichTextCtrl`.

.. note:: Due to some internal dynamic initialization in wxWidgets, this
          module should be imported **before** the :class:`wx.App` object is
          created.
"""
#-- begin-_richtext --#
import wx.xml
import wx.html
import wx.adv

import wx
#-- end-_richtext --#
#-- begin-richtextbuffer --#
RICHTEXT_FIXED_WIDTH: int
RICHTEXT_FIXED_HEIGHT: int
RICHTEXT_VARIABLE_WIDTH: int
RICHTEXT_VARIABLE_HEIGHT: int
RICHTEXT_LAYOUT_SPECIFIED_RECT: int
RICHTEXT_DRAW_IGNORE_CACHE: int
RICHTEXT_DRAW_SELECTED: int
RICHTEXT_DRAW_PRINT: int
RICHTEXT_DRAW_GUIDELINES: int
RICHTEXT_FORMATTED: int
RICHTEXT_UNFORMATTED: int
RICHTEXT_CACHE_SIZE: int
RICHTEXT_HEIGHT_ONLY: int
RICHTEXT_SETSTYLE_NONE: int
RICHTEXT_SETSTYLE_WITH_UNDO: int
RICHTEXT_SETSTYLE_OPTIMIZE: int
RICHTEXT_SETSTYLE_PARAGRAPHS_ONLY: int
RICHTEXT_SETSTYLE_CHARACTERS_ONLY: int
RICHTEXT_SETSTYLE_RENUMBER: int
RICHTEXT_SETSTYLE_SPECIFY_LEVEL: int
RICHTEXT_SETSTYLE_RESET: int
RICHTEXT_SETSTYLE_REMOVE: int
RICHTEXT_SETPROPERTIES_NONE: int
RICHTEXT_SETPROPERTIES_WITH_UNDO: int
RICHTEXT_SETPROPERTIES_PARAGRAPHS_ONLY: int
RICHTEXT_SETPROPERTIES_CHARACTERS_ONLY: int
RICHTEXT_SETPROPERTIES_RESET: int
RICHTEXT_SETPROPERTIES_REMOVE: int
RICHTEXT_INSERT_NONE: int
RICHTEXT_INSERT_WITH_PREVIOUS_PARAGRAPH_STYLE: int
RICHTEXT_INSERT_INTERACTIVE: int
TEXT_ATTR_KEEP_FIRST_PARA_STYLE: int
SCRIPT_MUL_FACTOR: int
RICHTEXT_HANDLER_INCLUDE_STYLESHEET: int
RICHTEXT_HANDLER_SAVE_IMAGES_TO_MEMORY: int
RICHTEXT_HANDLER_SAVE_IMAGES_TO_FILES: int
RICHTEXT_HANDLER_SAVE_IMAGES_TO_BASE64: int
RICHTEXT_HANDLER_NO_HEADER_FOOTER: int
RICHTEXT_HANDLER_CONVERT_FACENAMES: int
RICHTEXT_HANDLER_USE_CSS: int

class _RichTextFileType(IntEnum):
    RICHTEXT_TYPE_ANY = auto()
    RICHTEXT_TYPE_TEXT = auto()
    RICHTEXT_TYPE_XML = auto()
    RICHTEXT_TYPE_HTML = auto()
    RICHTEXT_TYPE_RTF = auto()
    RICHTEXT_TYPE_PDF = auto()
RichTextFileType: TypeAlias = Union[_RichTextFileType, int]
RICHTEXT_TYPE_ANY = _RichTextFileType.RICHTEXT_TYPE_ANY
RICHTEXT_TYPE_TEXT = _RichTextFileType.RICHTEXT_TYPE_TEXT
RICHTEXT_TYPE_XML = _RichTextFileType.RICHTEXT_TYPE_XML
RICHTEXT_TYPE_HTML = _RichTextFileType.RICHTEXT_TYPE_HTML
RICHTEXT_TYPE_RTF = _RichTextFileType.RICHTEXT_TYPE_RTF
RICHTEXT_TYPE_PDF = _RichTextFileType.RICHTEXT_TYPE_PDF

class _RichTextHitTestFlags(IntFlag):
    RICHTEXT_HITTEST_NONE = auto()
    RICHTEXT_HITTEST_BEFORE = auto()
    RICHTEXT_HITTEST_AFTER = auto()
    RICHTEXT_HITTEST_ON = auto()
    RICHTEXT_HITTEST_OUTSIDE = auto()
    RICHTEXT_HITTEST_NO_NESTED_OBJECTS = auto()
    RICHTEXT_HITTEST_NO_FLOATING_OBJECTS = auto()
    RICHTEXT_HITTEST_HONOUR_ATOMIC = auto()
RichTextHitTestFlags: TypeAlias = Union[_RichTextHitTestFlags, int]
RICHTEXT_HITTEST_NONE = _RichTextHitTestFlags.RICHTEXT_HITTEST_NONE
RICHTEXT_HITTEST_BEFORE = _RichTextHitTestFlags.RICHTEXT_HITTEST_BEFORE
RICHTEXT_HITTEST_AFTER = _RichTextHitTestFlags.RICHTEXT_HITTEST_AFTER
RICHTEXT_HITTEST_ON = _RichTextHitTestFlags.RICHTEXT_HITTEST_ON
RICHTEXT_HITTEST_OUTSIDE = _RichTextHitTestFlags.RICHTEXT_HITTEST_OUTSIDE
RICHTEXT_HITTEST_NO_NESTED_OBJECTS = _RichTextHitTestFlags.RICHTEXT_HITTEST_NO_NESTED_OBJECTS
RICHTEXT_HITTEST_NO_FLOATING_OBJECTS = _RichTextHitTestFlags.RICHTEXT_HITTEST_NO_FLOATING_OBJECTS
RICHTEXT_HITTEST_HONOUR_ATOMIC = _RichTextHitTestFlags.RICHTEXT_HITTEST_HONOUR_ATOMIC

class _TextBoxAttrFlags(IntFlag):
    TEXT_BOX_ATTR_FLOAT = auto()
    TEXT_BOX_ATTR_CLEAR = auto()
    TEXT_BOX_ATTR_COLLAPSE_BORDERS = auto()
    TEXT_BOX_ATTR_VERTICAL_ALIGNMENT = auto()
    TEXT_BOX_ATTR_BOX_STYLE_NAME = auto()
    TEXT_BOX_ATTR_WHITESPACE = auto()
    TEXT_BOX_ATTR_CORNER_RADIUS = auto()
TextBoxAttrFlags: TypeAlias = Union[_TextBoxAttrFlags, int]
TEXT_BOX_ATTR_FLOAT = _TextBoxAttrFlags.TEXT_BOX_ATTR_FLOAT
TEXT_BOX_ATTR_CLEAR = _TextBoxAttrFlags.TEXT_BOX_ATTR_CLEAR
TEXT_BOX_ATTR_COLLAPSE_BORDERS = _TextBoxAttrFlags.TEXT_BOX_ATTR_COLLAPSE_BORDERS
TEXT_BOX_ATTR_VERTICAL_ALIGNMENT = _TextBoxAttrFlags.TEXT_BOX_ATTR_VERTICAL_ALIGNMENT
TEXT_BOX_ATTR_BOX_STYLE_NAME = _TextBoxAttrFlags.TEXT_BOX_ATTR_BOX_STYLE_NAME
TEXT_BOX_ATTR_WHITESPACE = _TextBoxAttrFlags.TEXT_BOX_ATTR_WHITESPACE
TEXT_BOX_ATTR_CORNER_RADIUS = _TextBoxAttrFlags.TEXT_BOX_ATTR_CORNER_RADIUS

class _TextAttrValueFlags(IntFlag):
    TEXT_ATTR_VALUE_VALID = auto()
    TEXT_ATTR_VALUE_VALID_MASK = auto()
TextAttrValueFlags: TypeAlias = Union[_TextAttrValueFlags, int]
TEXT_ATTR_VALUE_VALID = _TextAttrValueFlags.TEXT_ATTR_VALUE_VALID
TEXT_ATTR_VALUE_VALID_MASK = _TextAttrValueFlags.TEXT_ATTR_VALUE_VALID_MASK

class _TextAttrUnits(IntEnum):
    TEXT_ATTR_UNITS_TENTHS_MM = auto()
    TEXT_ATTR_UNITS_PIXELS = auto()
    TEXT_ATTR_UNITS_PERCENTAGE = auto()
    TEXT_ATTR_UNITS_POINTS = auto()
    TEXT_ATTR_UNITS_HUNDREDTHS_POINT = auto()
    TEXT_ATTR_UNITS_MASK = auto()
TextAttrUnits: TypeAlias = Union[_TextAttrUnits, int]
TEXT_ATTR_UNITS_TENTHS_MM = _TextAttrUnits.TEXT_ATTR_UNITS_TENTHS_MM
TEXT_ATTR_UNITS_PIXELS = _TextAttrUnits.TEXT_ATTR_UNITS_PIXELS
TEXT_ATTR_UNITS_PERCENTAGE = _TextAttrUnits.TEXT_ATTR_UNITS_PERCENTAGE
TEXT_ATTR_UNITS_POINTS = _TextAttrUnits.TEXT_ATTR_UNITS_POINTS
TEXT_ATTR_UNITS_HUNDREDTHS_POINT = _TextAttrUnits.TEXT_ATTR_UNITS_HUNDREDTHS_POINT
TEXT_ATTR_UNITS_MASK = _TextAttrUnits.TEXT_ATTR_UNITS_MASK

class _TextBoxAttrPosition(IntEnum):
    TEXT_BOX_ATTR_POSITION_STATIC = auto()
    TEXT_BOX_ATTR_POSITION_RELATIVE = auto()
    TEXT_BOX_ATTR_POSITION_ABSOLUTE = auto()
    TEXT_BOX_ATTR_POSITION_FIXED = auto()
    TEXT_BOX_ATTR_POSITION_MASK = auto()
TextBoxAttrPosition: TypeAlias = Union[_TextBoxAttrPosition, int]
TEXT_BOX_ATTR_POSITION_STATIC = _TextBoxAttrPosition.TEXT_BOX_ATTR_POSITION_STATIC
TEXT_BOX_ATTR_POSITION_RELATIVE = _TextBoxAttrPosition.TEXT_BOX_ATTR_POSITION_RELATIVE
TEXT_BOX_ATTR_POSITION_ABSOLUTE = _TextBoxAttrPosition.TEXT_BOX_ATTR_POSITION_ABSOLUTE
TEXT_BOX_ATTR_POSITION_FIXED = _TextBoxAttrPosition.TEXT_BOX_ATTR_POSITION_FIXED
TEXT_BOX_ATTR_POSITION_MASK = _TextBoxAttrPosition.TEXT_BOX_ATTR_POSITION_MASK

class _TextAttrBorderStyle(IntEnum):
    TEXT_BOX_ATTR_BORDER_NONE = auto()
    TEXT_BOX_ATTR_BORDER_SOLID = auto()
    TEXT_BOX_ATTR_BORDER_DOTTED = auto()
    TEXT_BOX_ATTR_BORDER_DASHED = auto()
    TEXT_BOX_ATTR_BORDER_DOUBLE = auto()
    TEXT_BOX_ATTR_BORDER_GROOVE = auto()
    TEXT_BOX_ATTR_BORDER_RIDGE = auto()
    TEXT_BOX_ATTR_BORDER_INSET = auto()
    TEXT_BOX_ATTR_BORDER_OUTSET = auto()
TextAttrBorderStyle: TypeAlias = Union[_TextAttrBorderStyle, int]
TEXT_BOX_ATTR_BORDER_NONE = _TextAttrBorderStyle.TEXT_BOX_ATTR_BORDER_NONE
TEXT_BOX_ATTR_BORDER_SOLID = _TextAttrBorderStyle.TEXT_BOX_ATTR_BORDER_SOLID
TEXT_BOX_ATTR_BORDER_DOTTED = _TextAttrBorderStyle.TEXT_BOX_ATTR_BORDER_DOTTED
TEXT_BOX_ATTR_BORDER_DASHED = _TextAttrBorderStyle.TEXT_BOX_ATTR_BORDER_DASHED
TEXT_BOX_ATTR_BORDER_DOUBLE = _TextAttrBorderStyle.TEXT_BOX_ATTR_BORDER_DOUBLE
TEXT_BOX_ATTR_BORDER_GROOVE = _TextAttrBorderStyle.TEXT_BOX_ATTR_BORDER_GROOVE
TEXT_BOX_ATTR_BORDER_RIDGE = _TextAttrBorderStyle.TEXT_BOX_ATTR_BORDER_RIDGE
TEXT_BOX_ATTR_BORDER_INSET = _TextAttrBorderStyle.TEXT_BOX_ATTR_BORDER_INSET
TEXT_BOX_ATTR_BORDER_OUTSET = _TextAttrBorderStyle.TEXT_BOX_ATTR_BORDER_OUTSET

class _TextAttrBorderFlags(IntFlag):
    TEXT_BOX_ATTR_BORDER_STYLE = auto()
    TEXT_BOX_ATTR_BORDER_COLOUR = auto()
TextAttrBorderFlags: TypeAlias = Union[_TextAttrBorderFlags, int]
TEXT_BOX_ATTR_BORDER_STYLE = _TextAttrBorderFlags.TEXT_BOX_ATTR_BORDER_STYLE
TEXT_BOX_ATTR_BORDER_COLOUR = _TextAttrBorderFlags.TEXT_BOX_ATTR_BORDER_COLOUR

class _TextAttrBorderWidth(IntEnum):
    TEXT_BOX_ATTR_BORDER_THIN = auto()
    TEXT_BOX_ATTR_BORDER_MEDIUM = auto()
    TEXT_BOX_ATTR_BORDER_THICK = auto()
TextAttrBorderWidth: TypeAlias = Union[_TextAttrBorderWidth, int]
TEXT_BOX_ATTR_BORDER_THIN = _TextAttrBorderWidth.TEXT_BOX_ATTR_BORDER_THIN
TEXT_BOX_ATTR_BORDER_MEDIUM = _TextAttrBorderWidth.TEXT_BOX_ATTR_BORDER_MEDIUM
TEXT_BOX_ATTR_BORDER_THICK = _TextAttrBorderWidth.TEXT_BOX_ATTR_BORDER_THICK

class _TextBoxAttrFloatStyle(IntEnum):
    TEXT_BOX_ATTR_FLOAT_NONE = auto()
    TEXT_BOX_ATTR_FLOAT_LEFT = auto()
    TEXT_BOX_ATTR_FLOAT_RIGHT = auto()
TextBoxAttrFloatStyle: TypeAlias = Union[_TextBoxAttrFloatStyle, int]
TEXT_BOX_ATTR_FLOAT_NONE = _TextBoxAttrFloatStyle.TEXT_BOX_ATTR_FLOAT_NONE
TEXT_BOX_ATTR_FLOAT_LEFT = _TextBoxAttrFloatStyle.TEXT_BOX_ATTR_FLOAT_LEFT
TEXT_BOX_ATTR_FLOAT_RIGHT = _TextBoxAttrFloatStyle.TEXT_BOX_ATTR_FLOAT_RIGHT

class _TextBoxAttrClearStyle(IntEnum):
    TEXT_BOX_ATTR_CLEAR_NONE = auto()
    TEXT_BOX_ATTR_CLEAR_LEFT = auto()
    TEXT_BOX_ATTR_CLEAR_RIGHT = auto()
    TEXT_BOX_ATTR_CLEAR_BOTH = auto()
TextBoxAttrClearStyle: TypeAlias = Union[_TextBoxAttrClearStyle, int]
TEXT_BOX_ATTR_CLEAR_NONE = _TextBoxAttrClearStyle.TEXT_BOX_ATTR_CLEAR_NONE
TEXT_BOX_ATTR_CLEAR_LEFT = _TextBoxAttrClearStyle.TEXT_BOX_ATTR_CLEAR_LEFT
TEXT_BOX_ATTR_CLEAR_RIGHT = _TextBoxAttrClearStyle.TEXT_BOX_ATTR_CLEAR_RIGHT
TEXT_BOX_ATTR_CLEAR_BOTH = _TextBoxAttrClearStyle.TEXT_BOX_ATTR_CLEAR_BOTH

class _TextBoxAttrCollapseMode(IntEnum):
    TEXT_BOX_ATTR_COLLAPSE_NONE = auto()
    TEXT_BOX_ATTR_COLLAPSE_FULL = auto()
TextBoxAttrCollapseMode: TypeAlias = Union[_TextBoxAttrCollapseMode, int]
TEXT_BOX_ATTR_COLLAPSE_NONE = _TextBoxAttrCollapseMode.TEXT_BOX_ATTR_COLLAPSE_NONE
TEXT_BOX_ATTR_COLLAPSE_FULL = _TextBoxAttrCollapseMode.TEXT_BOX_ATTR_COLLAPSE_FULL

class _TextBoxAttrVerticalAlignment(IntEnum):
    TEXT_BOX_ATTR_VERTICAL_ALIGNMENT_NONE = auto()
    TEXT_BOX_ATTR_VERTICAL_ALIGNMENT_TOP = auto()
    TEXT_BOX_ATTR_VERTICAL_ALIGNMENT_CENTRE = auto()
    TEXT_BOX_ATTR_VERTICAL_ALIGNMENT_BOTTOM = auto()
TextBoxAttrVerticalAlignment: TypeAlias = Union[_TextBoxAttrVerticalAlignment, int]
TEXT_BOX_ATTR_VERTICAL_ALIGNMENT_NONE = _TextBoxAttrVerticalAlignment.TEXT_BOX_ATTR_VERTICAL_ALIGNMENT_NONE
TEXT_BOX_ATTR_VERTICAL_ALIGNMENT_TOP = _TextBoxAttrVerticalAlignment.TEXT_BOX_ATTR_VERTICAL_ALIGNMENT_TOP
TEXT_BOX_ATTR_VERTICAL_ALIGNMENT_CENTRE = _TextBoxAttrVerticalAlignment.TEXT_BOX_ATTR_VERTICAL_ALIGNMENT_CENTRE
TEXT_BOX_ATTR_VERTICAL_ALIGNMENT_BOTTOM = _TextBoxAttrVerticalAlignment.TEXT_BOX_ATTR_VERTICAL_ALIGNMENT_BOTTOM

class _TextBoxAttrWhitespaceMode(IntEnum):
    TEXT_BOX_ATTR_WHITESPACE_NONE = auto()
    TEXT_BOX_ATTR_WHITESPACE_NORMAL = auto()
    TEXT_BOX_ATTR_WHITESPACE_NO_WRAP = auto()
    TEXT_BOX_ATTR_WHITESPACE_PREFORMATTED = auto()
    TEXT_BOX_ATTR_WHITESPACE_PREFORMATTED_LINE = auto()
    TEXT_BOX_ATTR_WHITESPACE_PREFORMATTED_WRAP = auto()
TextBoxAttrWhitespaceMode: TypeAlias = Union[_TextBoxAttrWhitespaceMode, int]
TEXT_BOX_ATTR_WHITESPACE_NONE = _TextBoxAttrWhitespaceMode.TEXT_BOX_ATTR_WHITESPACE_NONE
TEXT_BOX_ATTR_WHITESPACE_NORMAL = _TextBoxAttrWhitespaceMode.TEXT_BOX_ATTR_WHITESPACE_NORMAL
TEXT_BOX_ATTR_WHITESPACE_NO_WRAP = _TextBoxAttrWhitespaceMode.TEXT_BOX_ATTR_WHITESPACE_NO_WRAP
TEXT_BOX_ATTR_WHITESPACE_PREFORMATTED = _TextBoxAttrWhitespaceMode.TEXT_BOX_ATTR_WHITESPACE_PREFORMATTED
TEXT_BOX_ATTR_WHITESPACE_PREFORMATTED_LINE = _TextBoxAttrWhitespaceMode.TEXT_BOX_ATTR_WHITESPACE_PREFORMATTED_LINE
TEXT_BOX_ATTR_WHITESPACE_PREFORMATTED_WRAP = _TextBoxAttrWhitespaceMode.TEXT_BOX_ATTR_WHITESPACE_PREFORMATTED_WRAP

class _RichTextCommandId(IntEnum):
    RICHTEXT_INSERT = auto()
    RICHTEXT_DELETE = auto()
    RICHTEXT_CHANGE_ATTRIBUTES = auto()
    RICHTEXT_CHANGE_STYLE = auto()
    RICHTEXT_CHANGE_OBJECT = auto()
RichTextCommandId: TypeAlias = Union[_RichTextCommandId, int]
RICHTEXT_INSERT = _RichTextCommandId.RICHTEXT_INSERT
RICHTEXT_DELETE = _RichTextCommandId.RICHTEXT_DELETE
RICHTEXT_CHANGE_ATTRIBUTES = _RichTextCommandId.RICHTEXT_CHANGE_ATTRIBUTES
RICHTEXT_CHANGE_STYLE = _RichTextCommandId.RICHTEXT_CHANGE_STYLE
RICHTEXT_CHANGE_OBJECT = _RichTextCommandId.RICHTEXT_CHANGE_OBJECT
RichTextLineBreakChar: str

class TextAttrDimension:
    """
    TextAttrDimension() -> None
    TextAttrDimension(value, units=TEXT_ATTR_UNITS_TENTHS_MM) -> None
    
    A class representing a rich text dimension, including units and
    position.
    """

    @overload
    def __init__(self, value: int, units: TextAttrUnits=TEXT_ATTR_UNITS_TENTHS_MM) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        TextAttrDimension() -> None
        TextAttrDimension(value, units=TEXT_ATTR_UNITS_TENTHS_MM) -> None
        
        A class representing a rich text dimension, including units and
        position.
        """
    m_value: int
    m_flags: TextAttrDimensionFlags

    def Reset(self) -> None:
        """
        Reset() -> None
        
        Resets the dimension value and flags.
        """

    def EqPartial(self, dim: TextAttrDimension, weakTest: bool=True) -> bool:
        """
        EqPartial(dim, weakTest=True) -> bool
        
        Partial equality test.
        """

    def Apply(self, dim: TextAttrDimension, compareWith: Optional[TextAttrDimension]=None) -> bool:
        """
        Apply(dim, compareWith=None) -> bool
        
        Apply the dimension, but not those identical to compareWith if present.
        """

    def CollectCommonAttributes(self, attr: TextAttrDimension, clashingAttr: TextAttrDimension, absentAttr: TextAttrDimension) -> None:
        """
        CollectCommonAttributes(attr, clashingAttr, absentAttr) -> None
        
        Collects the attributes that are common to a range of content,
        building up a note of which attributes are absent in some objects and
        which clash in some objects.
        """

    def __eq__(self, dim: TextAttrDimension) -> bool:
        """
        """

    def GetValue(self) -> int:
        """
        GetValue() -> int
        
        Returns the integer value of the dimension.
        """

    def GetValueMM(self) -> float:
        """
        GetValueMM() -> float
        
        Returns the floating-pointing value of the dimension in mm.
        """

    def SetValueMM(self, value: float) -> None:
        """
        SetValueMM(value) -> None
        
        Sets the value of the dimension in mm.
        """

    @overload
    def SetValue(self, value: int, flags: TextAttrDimensionFlags) -> None:
        ...

    @overload
    def SetValue(self, dim: TextAttrDimension) -> None:
        ...

    @overload
    def SetValue(self, value: int) -> None:
        """
        SetValue(value) -> None
        SetValue(value, flags) -> None
        SetValue(dim) -> None
        
        Sets the integer value of the dimension.
        """

    def GetUnits(self) -> TextAttrUnits:
        """
        GetUnits() -> TextAttrUnits
        
        Gets the units of the dimension.
        """

    def SetUnits(self, units: TextAttrUnits) -> None:
        """
        SetUnits(units) -> None
        
        Sets the units of the dimension.
        """

    def GetPosition(self) -> TextBoxAttrPosition:
        """
        GetPosition() -> TextBoxAttrPosition
        
        Gets the position flags.
        """

    def SetPosition(self, pos: TextBoxAttrPosition) -> None:
        """
        SetPosition(pos) -> None
        
        Sets the position flags.
        """

    def IsValid(self) -> bool:
        """
        IsValid() -> bool
        
        Returns true if the dimension is valid.
        """

    def SetValid(self, b: bool) -> None:
        """
        SetValid(b) -> None
        
        Sets the valid flag.
        """

    def GetFlags(self) -> TextAttrDimensionFlags:
        """
        GetFlags() -> TextAttrDimensionFlags
        
        Gets the dimension flags.
        """

    def SetFlags(self, flags: TextAttrDimensionFlags) -> None:
        """
        SetFlags(flags) -> None
        
        Sets the dimension flags.
        """

    def __nonzero__(self) -> bool:
        """
        __nonzero__() -> bool
        """

    def __bool__(self) -> bool:
        """
        __bool__() -> bool
        """
    @property
    def Flags(self) -> TextAttrDimensionFlags: ...
    @Flags.setter
    def Flags(self, value: TextAttrDimensionFlags, /) -> None: ...
    @property
    def Position(self) -> TextBoxAttrPosition: ...
    @Position.setter
    def Position(self, value: TextBoxAttrPosition, /) -> None: ...
    @property
    def Units(self) -> TextAttrUnits: ...
    @Units.setter
    def Units(self, value: TextAttrUnits, /) -> None: ...
    @property
    def Value(self) -> int: ...
    @Value.setter
    def Value(self, value: int, /) -> None: ...
    @property
    def ValueMM(self) -> float: ...
    @ValueMM.setter
    def ValueMM(self, value: float, /) -> None: ...
# end of class TextAttrDimension


class TextAttrDimensions:
    """
    TextAttrDimensions() -> None
    
    A class for left, right, top and bottom dimensions.
    """

    def __init__(self) -> None:
        """
        TextAttrDimensions() -> None
        
        A class for left, right, top and bottom dimensions.
        """
    m_left: TextAttrDimension
    m_top: TextAttrDimension
    m_right: TextAttrDimension
    m_bottom: TextAttrDimension

    def Reset(self) -> None:
        """
        Reset() -> None
        
        Resets the value and flags for all dimensions.
        """

    def __eq__(self, dims: TextAttrDimensions) -> bool:
        """
        """

    def EqPartial(self, dims: TextAttrDimensions, weakTest: bool=True) -> bool:
        """
        EqPartial(dims, weakTest=True) -> bool
        
        Partial equality test.
        """

    def Apply(self, dims: TextAttrDimensions, compareWith: Optional[TextAttrDimensions]=None) -> bool:
        """
        Apply(dims, compareWith=None) -> bool
        
        Apply to 'this', but not if the same as compareWith.
        """

    def CollectCommonAttributes(self, attr: TextAttrDimensions, clashingAttr: TextAttrDimensions, absentAttr: TextAttrDimensions) -> None:
        """
        CollectCommonAttributes(attr, clashingAttr, absentAttr) -> None
        
        Collects the attributes that are common to a range of content,
        building up a note of which attributes are absent in some objects and
        which clash in some objects.
        """

    def RemoveStyle(self, attr: TextAttrDimensions) -> bool:
        """
        RemoveStyle(attr) -> bool
        
        Remove specified attributes from this object.
        """

    def GetLeft(self) -> TextAttrDimension:
        """
        GetLeft() -> TextAttrDimension
        """

    def GetRight(self) -> TextAttrDimension:
        """
        GetRight() -> TextAttrDimension
        """

    def GetTop(self) -> TextAttrDimension:
        """
        GetTop() -> TextAttrDimension
        """

    def GetBottom(self) -> TextAttrDimension:
        """
        GetBottom() -> TextAttrDimension
        """

    def IsValid(self) -> bool:
        """
        IsValid() -> bool
        
        Are all dimensions valid?
        """

    def __nonzero__(self) -> bool:
        """
        __nonzero__() -> bool
        """

    def __bool__(self) -> bool:
        """
        __bool__() -> bool
        """
    @property
    def Bottom(self) -> TextAttrDimension: ...
    @property
    def Left(self) -> TextAttrDimension: ...
    @property
    def Right(self) -> TextAttrDimension: ...
    @property
    def Top(self) -> TextAttrDimension: ...
# end of class TextAttrDimensions


class TextAttrSize:
    """
    TextAttrSize() -> None
    
    A class for representing width and height.
    """

    def __init__(self) -> None:
        """
        TextAttrSize() -> None
        
        A class for representing width and height.
        """
    m_width: TextAttrDimension
    m_height: TextAttrDimension

    def Reset(self) -> None:
        """
        Reset() -> None
        
        Resets the width and height dimensions.
        """

    def __eq__(self, size: TextAttrSize) -> bool:
        """
        """

    def EqPartial(self, size: TextAttrSize, weakTest: bool=True) -> bool:
        """
        EqPartial(size, weakTest=True) -> bool
        
        Partial equality test.
        """

    def Apply(self, dims: TextAttrSize, compareWith: Optional[TextAttrSize]=None) -> bool:
        """
        Apply(dims, compareWith=None) -> bool
        
        Apply to this object, but not if the same as compareWith.
        """

    def CollectCommonAttributes(self, attr: TextAttrSize, clashingAttr: TextAttrSize, absentAttr: TextAttrSize) -> None:
        """
        CollectCommonAttributes(attr, clashingAttr, absentAttr) -> None
        
        Collects the attributes that are common to a range of content,
        building up a note of which attributes are absent in some objects and
        which clash in some objects.
        """

    def RemoveStyle(self, attr: TextAttrSize) -> bool:
        """
        RemoveStyle(attr) -> bool
        
        Removes the specified attributes from this object.
        """

    def GetWidth(self) -> TextAttrDimension:
        """
        GetWidth() -> TextAttrDimension
        
        Returns the width.
        """

    @overload
    def SetWidth(self, dim: TextAttrDimension) -> None:
        ...

    @overload
    def SetWidth(self, value: int, flags: TextAttrDimensionFlags) -> None:
        """
        SetWidth(value, flags) -> None
        SetWidth(dim) -> None
        
        Sets the width.
        """

    def GetHeight(self) -> TextAttrDimension:
        """
        GetHeight() -> TextAttrDimension
        
        Gets the height.
        """

    @overload
    def SetHeight(self, dim: TextAttrDimension) -> None:
        ...

    @overload
    def SetHeight(self, value: int, flags: TextAttrDimensionFlags) -> None:
        """
        SetHeight(value, flags) -> None
        SetHeight(dim) -> None
        
        Sets the height.
        """

    def IsValid(self) -> bool:
        """
        IsValid() -> bool
        
        Is the size valid?
        """

    def __nonzero__(self) -> bool:
        """
        __nonzero__() -> bool
        """

    def __bool__(self) -> bool:
        """
        __bool__() -> bool
        """
    @property
    def Height(self) -> int: ...
    @Height.setter
    def Height(self, value: int, /) -> None: ...
    @property
    def Width(self) -> int: ...
    @Width.setter
    def Width(self, value: int, /) -> None: ...
# end of class TextAttrSize


class TextAttrDimensionConverter:
    """
    TextAttrDimensionConverter(dc, scale=1.0, parentSize=wx.DefaultSize) -> None
    TextAttrDimensionConverter(ppi, scale=1.0, parentSize=wx.DefaultSize) -> None
    
    A class to make it easier to convert dimensions.
    """

    @overload
    def __init__(self, ppi: int, scale: float=1.0, parentSize: wx.Size=wx.DefaultSize) -> None:
        ...

    @overload
    def __init__(self, dc: wx.DC, scale: float=1.0, parentSize: wx.Size=wx.DefaultSize) -> None:
        """
        TextAttrDimensionConverter(dc, scale=1.0, parentSize=wx.DefaultSize) -> None
        TextAttrDimensionConverter(ppi, scale=1.0, parentSize=wx.DefaultSize) -> None
        
        A class to make it easier to convert dimensions.
        """
    m_ppi: int
    m_scale: float
    m_parentSize: wx.Size

    def GetPixels(self, dim: TextAttrDimension, direction: int=wx.HORIZONTAL) -> int:
        """
        GetPixels(dim, direction=wx.HORIZONTAL) -> int
        
        Gets the pixel size for the given dimension.
        """

    def GetTenthsMM(self, dim: TextAttrDimension) -> int:
        """
        GetTenthsMM(dim) -> int
        
        Gets the mm size for the given dimension.
        """

    def ConvertTenthsMMToPixels(self, units: int) -> int:
        """
        ConvertTenthsMMToPixels(units) -> int
        
        Converts tenths of a mm to pixels.
        """

    def ConvertPixelsToTenthsMM(self, pixels: int) -> int:
        """
        ConvertPixelsToTenthsMM(pixels) -> int
        
        Converts pixels to tenths of a mm.
        """
# end of class TextAttrDimensionConverter


class TextAttrBorder:
    """
    TextAttrBorder() -> None
    
    A class representing a rich text object border.
    """

    def __init__(self) -> None:
        """
        TextAttrBorder() -> None
        
        A class representing a rich text object border.
        """
    m_borderStyle: int
    m_borderColour: int
    m_borderWidth: TextAttrDimension
    m_flags: int

    def __eq__(self, border: TextAttrBorder) -> bool:
        """
        """

    def Reset(self) -> None:
        """
        Reset() -> None
        
        Resets the border style, colour, width and flags.
        """

    def EqPartial(self, border: TextAttrBorder, weakTest: bool=True) -> bool:
        """
        EqPartial(border, weakTest=True) -> bool
        
        Partial equality test.
        """

    def Apply(self, border: TextAttrBorder, compareWith: Optional[TextAttrBorder]=None) -> bool:
        """
        Apply(border, compareWith=None) -> bool
        
        Applies the border to this object, but not if the same as compareWith.
        """

    def RemoveStyle(self, attr: TextAttrBorder) -> bool:
        """
        RemoveStyle(attr) -> bool
        
        Removes the specified attributes from this object.
        """

    def CollectCommonAttributes(self, attr: TextAttrBorder, clashingAttr: TextAttrBorder, absentAttr: TextAttrBorder) -> None:
        """
        CollectCommonAttributes(attr, clashingAttr, absentAttr) -> None
        
        Collects the attributes that are common to a range of content,
        building up a note of which attributes are absent in some objects and
        which clash in some objects.
        """

    def SetStyle(self, style: int) -> None:
        """
        SetStyle(style) -> None
        
        Sets the border style.
        """

    def GetStyle(self) -> int:
        """
        GetStyle() -> int
        
        Gets the border style.
        """

    @overload
    def SetColour(self, colour: wx.Colour) -> None:
        ...

    @overload
    def SetColour(self, colour: int) -> None:
        """
        SetColour(colour) -> None
        SetColour(colour) -> None
        
        Sets the border colour.
        """

    def GetColourLong(self) -> int:
        """
        GetColourLong() -> int
        
        Gets the colour as a long.
        """

    def GetColour(self) -> wx.Colour:
        """
        GetColour() -> wx.Colour
        
        Gets the colour.
        """

    def GetWidth(self) -> TextAttrDimension:
        """
        GetWidth() -> TextAttrDimension
        
        Gets the border width.
        """

    @overload
    def SetWidth(self, value: int, units: TextAttrUnits=TEXT_ATTR_UNITS_TENTHS_MM) -> None:
        ...

    @overload
    def SetWidth(self, width: TextAttrDimension) -> None:
        """
        SetWidth(width) -> None
        SetWidth(value, units=TEXT_ATTR_UNITS_TENTHS_MM) -> None
        
        Sets the border width.
        """

    def HasStyle(self) -> bool:
        """
        HasStyle() -> bool
        
        True if the border has a valid style.
        """

    def HasColour(self) -> bool:
        """
        HasColour() -> bool
        
        True if the border has a valid colour.
        """

    def HasWidth(self) -> bool:
        """
        HasWidth() -> bool
        
        True if the border has a valid width.
        """

    def IsValid(self) -> bool:
        """
        IsValid() -> bool
        
        True if the border is valid.
        """

    def IsDefault(self) -> bool:
        """
        IsDefault() -> bool
        
        True if the border has no attributes set.
        """

    def MakeValid(self) -> None:
        """
        MakeValid() -> None
        
        Set the valid flag for this border.
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        
        Returns the border flags.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Sets the border flags.
        """

    def AddFlag(self, flag: int) -> None:
        """
        AddFlag(flag) -> None
        
        Adds a border flag.
        """

    def RemoveFlag(self, flag: int) -> None:
        """
        RemoveFlag(flag) -> None
        
        Removes a border flag.
        """

    def __nonzero__(self) -> bool:
        """
        __nonzero__() -> bool
        """

    def __bool__(self) -> bool:
        """
        __bool__() -> bool
        """
    @property
    def Colour(self) -> int: ...
    @Colour.setter
    def Colour(self, value: int, /) -> None: ...
    @property
    def ColourLong(self) -> int: ...
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def Style(self) -> int: ...
    @Style.setter
    def Style(self, value: int, /) -> None: ...
    @property
    def Width(self) -> TextAttrDimension: ...
    @Width.setter
    def Width(self, value: TextAttrDimension, /) -> None: ...
# end of class TextAttrBorder


class TextAttrBorders:
    """
    TextAttrBorders() -> None
    
    A class representing a rich text object's borders.
    """

    def __init__(self) -> None:
        """
        TextAttrBorders() -> None
        
        A class representing a rich text object's borders.
        """
    m_left: TextAttrBorder
    m_right: TextAttrBorder
    m_top: TextAttrBorder
    m_bottom: TextAttrBorder

    def __eq__(self, borders: TextAttrBorders) -> bool:
        """
        """

    def SetStyle(self, style: int) -> None:
        """
        SetStyle(style) -> None
        
        Sets the style of all borders.
        """

    @overload
    def SetColour(self, colour: wx.Colour) -> None:
        ...

    @overload
    def SetColour(self, colour: int) -> None:
        """
        SetColour(colour) -> None
        SetColour(colour) -> None
        
        Sets colour of all borders.
        """

    @overload
    def SetWidth(self, value: int, units: TextAttrUnits=TEXT_ATTR_UNITS_TENTHS_MM) -> None:
        ...

    @overload
    def SetWidth(self, width: TextAttrDimension) -> None:
        """
        SetWidth(width) -> None
        SetWidth(value, units=TEXT_ATTR_UNITS_TENTHS_MM) -> None
        
        Sets the width of all borders.
        """

    def Reset(self) -> None:
        """
        Reset() -> None
        
        Resets all borders.
        """

    def EqPartial(self, borders: TextAttrBorders, weakTest: bool=True) -> bool:
        """
        EqPartial(borders, weakTest=True) -> bool
        
        Partial equality test.
        """

    def Apply(self, borders: TextAttrBorders, compareWith: Optional[TextAttrBorders]=None) -> bool:
        """
        Apply(borders, compareWith=None) -> bool
        
        Applies border to this object, but not if the same as compareWith.
        """

    def RemoveStyle(self, attr: TextAttrBorders) -> bool:
        """
        RemoveStyle(attr) -> bool
        
        Removes the specified attributes from this object.
        """

    def CollectCommonAttributes(self, attr: TextAttrBorders, clashingAttr: TextAttrBorders, absentAttr: TextAttrBorders) -> None:
        """
        CollectCommonAttributes(attr, clashingAttr, absentAttr) -> None
        
        Collects the attributes that are common to a range of content,
        building up a note of which attributes are absent in some objects and
        which clash in some objects.
        """

    def IsValid(self) -> bool:
        """
        IsValid() -> bool
        
        Returns true if at least one border is valid.
        """

    def GetLeft(self) -> TextAttrBorder:
        """
        GetLeft() -> TextAttrBorder
        """

    def GetRight(self) -> TextAttrBorder:
        """
        GetRight() -> TextAttrBorder
        """

    def GetTop(self) -> TextAttrBorder:
        """
        GetTop() -> TextAttrBorder
        """

    def GetBottom(self) -> TextAttrBorder:
        """
        GetBottom() -> TextAttrBorder
        """

    def __nonzero__(self) -> bool:
        """
        __nonzero__() -> bool
        """

    def __bool__(self) -> bool:
        """
        __bool__() -> bool
        """
    @property
    def Bottom(self) -> TextAttrBorder: ...
    @property
    def Left(self) -> TextAttrBorder: ...
    @property
    def Right(self) -> TextAttrBorder: ...
    @property
    def Top(self) -> TextAttrBorder: ...
# end of class TextAttrBorders


class TextAttrShadow:
    """
    TextAttrShadow() -> None
    
    A class representing a shadow.
    """

    def __init__(self) -> None:
        """
        TextAttrShadow() -> None
        
        A class representing a shadow.
        """
    m_flags: int
    m_shadowColour: int
    m_offsetX: TextAttrDimension
    m_offsetY: TextAttrDimension
    m_spread: TextAttrDimension
    m_blurDistance: TextAttrDimension
    m_opacity: TextAttrDimension

    def __eq__(self, shadow: TextAttrShadow) -> bool:
        """
        """

    def Reset(self) -> None:
        """
        Reset() -> None
        
        Resets the shadow.
        """

    def EqPartial(self, shadow: TextAttrShadow, weakTest: bool=True) -> bool:
        """
        EqPartial(shadow, weakTest=True) -> bool
        
        Partial equality test.
        """

    def Apply(self, shadow: TextAttrShadow, compareWith: Optional[TextAttrShadow]=None) -> bool:
        """
        Apply(shadow, compareWith=None) -> bool
        
        Applies the border to this object, but not if the same as compareWith.
        """

    def RemoveStyle(self, attr: TextAttrShadow) -> bool:
        """
        RemoveStyle(attr) -> bool
        
        Removes the specified attributes from this object.
        """

    def CollectCommonAttributes(self, attr: TextAttrShadow, clashingAttr: TextAttrShadow, absentAttr: TextAttrShadow) -> None:
        """
        CollectCommonAttributes(attr, clashingAttr, absentAttr) -> None
        
        Collects the attributes that are common to a range of content,
        building up a note of which attributes are absent in some objects and
        which clash in some objects.
        """

    @overload
    def SetColour(self, colour: wx.Colour) -> None:
        ...

    @overload
    def SetColour(self, colour: int) -> None:
        """
        SetColour(colour) -> None
        SetColour(colour) -> None
        
        Sets the shadow colour.
        """

    def GetColourLong(self) -> int:
        """
        GetColourLong() -> int
        
        Gets the colour as a long.
        """

    def GetColour(self) -> wx.Colour:
        """
        GetColour() -> wx.Colour
        
        Gets the colour.
        """

    def HasColour(self) -> bool:
        """
        HasColour() -> bool
        
        True if the shadow has a valid colour.
        """

    def GetOffsetX(self) -> TextAttrDimension:
        """
        GetOffsetX() -> TextAttrDimension
        
        Gets the shadow horizontal offset.
        """

    def SetOffsetX(self, offset: TextAttrDimension) -> None:
        """
        SetOffsetX(offset) -> None
        
        Sets the shadow horizontal offset.
        """

    def GetOffsetY(self) -> TextAttrDimension:
        """
        GetOffsetY() -> TextAttrDimension
        
        Gets the shadow vertical offset.
        """

    def SetOffsetY(self, offset: TextAttrDimension) -> None:
        """
        SetOffsetY(offset) -> None
        
        Sets the shadow vertical offset.
        """

    def GetSpread(self) -> TextAttrDimension:
        """
        GetSpread() -> TextAttrDimension
        
        Gets the shadow spread size.
        """

    def SetSpread(self, spread: TextAttrDimension) -> None:
        """
        SetSpread(spread) -> None
        
        Sets the shadow spread size.
        """

    def GetBlurDistance(self) -> TextAttrDimension:
        """
        GetBlurDistance() -> TextAttrDimension
        
        Gets the shadow blur distance.
        """

    def SetBlurDistance(self, blur: TextAttrDimension) -> None:
        """
        SetBlurDistance(blur) -> None
        
        Sets the shadow blur distance.
        """

    def GetOpacity(self) -> TextAttrDimension:
        """
        GetOpacity() -> TextAttrDimension
        
        Gets the shadow opacity.
        """

    def IsValid(self) -> bool:
        """
        IsValid() -> bool
        
        Returns true if the dimension is valid.
        """

    def SetValid(self, b: bool) -> None:
        """
        SetValid(b) -> None
        
        Sets the valid flag.
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        
        Returns the border flags.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Sets the border flags.
        """

    def AddFlag(self, flag: int) -> None:
        """
        AddFlag(flag) -> None
        
        Adds a border flag.
        """

    def RemoveFlag(self, flag: int) -> None:
        """
        RemoveFlag(flag) -> None
        
        Removes a border flag.
        """

    def SetOpacity(self, opacity: TextAttrDimension) -> None:
        """
        SetOpacity(opacity) -> None
        
        Sets the shadow opacity.
        """

    def IsDefault(self) -> bool:
        """
        IsDefault() -> bool
        
        True if the shadow has no attributes set.
        """
    @property
    def BlurDistance(self) -> TextAttrDimension: ...
    @BlurDistance.setter
    def BlurDistance(self, value: TextAttrDimension, /) -> None: ...
    @property
    def Colour(self) -> int: ...
    @Colour.setter
    def Colour(self, value: int, /) -> None: ...
    @property
    def ColourLong(self) -> int: ...
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def OffsetX(self) -> TextAttrDimension: ...
    @OffsetX.setter
    def OffsetX(self, value: TextAttrDimension, /) -> None: ...
    @property
    def OffsetY(self) -> TextAttrDimension: ...
    @OffsetY.setter
    def OffsetY(self, value: TextAttrDimension, /) -> None: ...
    @property
    def Opacity(self) -> TextAttrDimension: ...
    @Opacity.setter
    def Opacity(self, value: TextAttrDimension, /) -> None: ...
    @property
    def Spread(self) -> TextAttrDimension: ...
    @Spread.setter
    def Spread(self, value: TextAttrDimension, /) -> None: ...
# end of class TextAttrShadow


class TextBoxAttr:
    """
    TextBoxAttr() -> None
    TextBoxAttr(attr) -> None
    
    A class representing the box attributes of a rich text object.
    """

    @overload
    def __init__(self, attr: TextBoxAttr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        TextBoxAttr() -> None
        TextBoxAttr(attr) -> None
        
        A class representing the box attributes of a rich text object.
        """
    m_flags: int
    m_margins: TextAttrDimensions
    m_padding: TextAttrDimensions
    m_position: TextAttrDimensions
    m_size: TextAttrSize
    m_minSize: TextAttrSize
    m_maxSize: TextAttrSize
    m_border: TextAttrBorders
    m_outline: TextAttrBorders
    m_floatMode: TextBoxAttrFloatStyle
    m_clearMode: TextBoxAttrClearStyle
    m_collapseMode: TextBoxAttrCollapseMode
    m_verticalAlignment: TextBoxAttrVerticalAlignment
    m_whitespaceMode: TextBoxAttrWhitespaceMode
    m_cornerRadius: TextAttrDimension
    m_boxStyleName: str
    m_shadow: TextAttrShadow

    def Init(self) -> None:
        """
        Init() -> None
        
        Initialises this object.
        """

    def Reset(self) -> None:
        """
        Reset() -> None
        
        Resets this object.
        """

    def __eq__(self, attr: TextBoxAttr) -> bool:
        """
        """

    def EqPartial(self, attr: TextBoxAttr, weakTest: bool=True) -> bool:
        """
        EqPartial(attr, weakTest=True) -> bool
        
        Partial equality test, ignoring unset attributes.
        """

    def Apply(self, style: TextBoxAttr, compareWith: Optional[TextBoxAttr]=None) -> bool:
        """
        Apply(style, compareWith=None) -> bool
        
        Merges the given attributes.
        """

    def CollectCommonAttributes(self, attr: TextBoxAttr, clashingAttr: TextBoxAttr, absentAttr: TextBoxAttr) -> None:
        """
        CollectCommonAttributes(attr, clashingAttr, absentAttr) -> None
        
        Collects the attributes that are common to a range of content,
        building up a note of which attributes are absent in some objects and
        which clash in some objects.
        """

    def RemoveStyle(self, attr: TextBoxAttr) -> bool:
        """
        RemoveStyle(attr) -> bool
        
        Removes the specified attributes from this object.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Sets the flags.
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        
        Returns the flags.
        """

    def HasFlag(self, flag: TextBoxAttrFlags) -> bool:
        """
        HasFlag(flag) -> bool
        
        Is this flag present?
        """

    def RemoveFlag(self, flag: TextBoxAttrFlags) -> None:
        """
        RemoveFlag(flag) -> None
        
        Removes this flag.
        """

    def AddFlag(self, flag: TextBoxAttrFlags) -> None:
        """
        AddFlag(flag) -> None
        
        Adds this flag.
        """

    def IsDefault(self) -> bool:
        """
        IsDefault() -> bool
        
        Returns true if no attributes are set.
        """

    def GetFloatMode(self) -> TextBoxAttrFloatStyle:
        """
        GetFloatMode() -> TextBoxAttrFloatStyle
        
        Returns the float mode.
        """

    def SetFloatMode(self, mode: TextBoxAttrFloatStyle) -> None:
        """
        SetFloatMode(mode) -> None
        
        Sets the float mode.
        """

    def HasFloatMode(self) -> bool:
        """
        HasFloatMode() -> bool
        
        Returns true if float mode is active.
        """

    def IsFloating(self) -> bool:
        """
        IsFloating() -> bool
        
        Returns true if this object is floating.
        """

    def GetClearMode(self) -> TextBoxAttrClearStyle:
        """
        GetClearMode() -> TextBoxAttrClearStyle
        
        Returns the clear mode - whether to wrap text after object.
        """

    def SetClearMode(self, mode: TextBoxAttrClearStyle) -> None:
        """
        SetClearMode(mode) -> None
        
        Set the clear mode.
        """

    def HasClearMode(self) -> bool:
        """
        HasClearMode() -> bool
        
        Returns true if we have a clear flag.
        """

    def GetCollapseBorders(self) -> TextBoxAttrCollapseMode:
        """
        GetCollapseBorders() -> TextBoxAttrCollapseMode
        
        Returns the collapse mode - whether to collapse borders.
        """

    def SetCollapseBorders(self, collapse: TextBoxAttrCollapseMode) -> None:
        """
        SetCollapseBorders(collapse) -> None
        
        Sets the collapse mode - whether to collapse borders.
        """

    def HasCollapseBorders(self) -> bool:
        """
        HasCollapseBorders() -> bool
        
        Returns true if the collapse borders flag is present.
        """

    def GetWhitespaceMode(self) -> TextBoxAttrWhitespaceMode:
        """
        GetWhitespaceMode() -> TextBoxAttrWhitespaceMode
        
        Returns the whitespace mode.
        """

    def SetWhitespaceMode(self, whitespace: TextBoxAttrWhitespaceMode) -> None:
        """
        SetWhitespaceMode(whitespace) -> None
        
        Sets the whitespace mode.
        """

    def HasWhitespaceMode(self) -> bool:
        """
        HasWhitespaceMode() -> bool
        
        Returns true if the whitespace flag is present.
        """

    def HasCornerRadius(self) -> bool:
        """
        HasCornerRadius() -> bool
        
        Returns true if the corner radius flag is present.
        """

    def GetCornerRadius(self) -> TextAttrDimension:
        """
        GetCornerRadius() -> TextAttrDimension
        """

    def SetCornerRadius(self, dim: TextAttrDimension) -> None:
        """
        SetCornerRadius(dim) -> None
        
        Sets the corner radius value.
        """

    def GetVerticalAlignment(self) -> TextBoxAttrVerticalAlignment:
        """
        GetVerticalAlignment() -> TextBoxAttrVerticalAlignment
        
        Returns the vertical alignment.
        """

    def SetVerticalAlignment(self, verticalAlignment: TextBoxAttrVerticalAlignment) -> None:
        """
        SetVerticalAlignment(verticalAlignment) -> None
        
        Sets the vertical alignment.
        """

    def HasVerticalAlignment(self) -> bool:
        """
        HasVerticalAlignment() -> bool
        
        Returns true if a vertical alignment flag is present.
        """

    def GetMargins(self) -> TextAttrDimensions:
        """
        GetMargins() -> TextAttrDimensions
        
        Returns the margin values.
        """

    def GetLeftMargin(self) -> TextAttrDimension:
        """
        GetLeftMargin() -> TextAttrDimension
        
        Returns the left margin.
        """

    def GetRightMargin(self) -> TextAttrDimension:
        """
        GetRightMargin() -> TextAttrDimension
        
        Returns the right margin.
        """

    def GetTopMargin(self) -> TextAttrDimension:
        """
        GetTopMargin() -> TextAttrDimension
        
        Returns the top margin.
        """

    def GetBottomMargin(self) -> TextAttrDimension:
        """
        GetBottomMargin() -> TextAttrDimension
        
        Returns the bottom margin.
        """

    def GetPosition(self) -> TextAttrDimensions:
        """
        GetPosition() -> TextAttrDimensions
        
        Returns the position.
        """

    def GetLeft(self) -> TextAttrDimension:
        """
        GetLeft() -> TextAttrDimension
        
        Returns the left position.
        """

    def GetRight(self) -> TextAttrDimension:
        """
        GetRight() -> TextAttrDimension
        
        Returns the right position.
        """

    def GetTop(self) -> TextAttrDimension:
        """
        GetTop() -> TextAttrDimension
        
        Returns the top position.
        """

    def GetBottom(self) -> TextAttrDimension:
        """
        GetBottom() -> TextAttrDimension
        
        Returns the bottom position.
        """

    def GetPadding(self) -> TextAttrDimensions:
        """
        GetPadding() -> TextAttrDimensions
        
        Returns the padding values.
        """

    def GetLeftPadding(self) -> TextAttrDimension:
        """
        GetLeftPadding() -> TextAttrDimension
        
        Returns the left padding value.
        """

    def GetRightPadding(self) -> TextAttrDimension:
        """
        GetRightPadding() -> TextAttrDimension
        
        Returns the right padding value.
        """

    def GetTopPadding(self) -> TextAttrDimension:
        """
        GetTopPadding() -> TextAttrDimension
        
        Returns the top padding value.
        """

    def GetBottomPadding(self) -> TextAttrDimension:
        """
        GetBottomPadding() -> TextAttrDimension
        
        Returns the bottom padding value.
        """

    def GetBorder(self) -> TextAttrBorders:
        """
        GetBorder() -> TextAttrBorders
        
        Returns the borders.
        """

    def GetLeftBorder(self) -> TextAttrBorder:
        """
        GetLeftBorder() -> TextAttrBorder
        
        Returns the left border.
        """

    def GetTopBorder(self) -> TextAttrBorder:
        """
        GetTopBorder() -> TextAttrBorder
        
        Returns the top border.
        """

    def GetRightBorder(self) -> TextAttrBorder:
        """
        GetRightBorder() -> TextAttrBorder
        
        Returns the right border.
        """

    def GetBottomBorder(self) -> TextAttrBorder:
        """
        GetBottomBorder() -> TextAttrBorder
        
        Returns the bottom border.
        """

    def GetOutline(self) -> TextAttrBorders:
        """
        GetOutline() -> TextAttrBorders
        
        Returns the outline.
        """

    def GetLeftOutline(self) -> TextAttrBorder:
        """
        GetLeftOutline() -> TextAttrBorder
        
        Returns the left outline.
        """

    def GetTopOutline(self) -> TextAttrBorder:
        """
        GetTopOutline() -> TextAttrBorder
        
        Returns the top outline.
        """

    def GetRightOutline(self) -> TextAttrBorder:
        """
        GetRightOutline() -> TextAttrBorder
        
        Returns the right outline.
        """

    def GetBottomOutline(self) -> TextAttrBorder:
        """
        GetBottomOutline() -> TextAttrBorder
        
        Returns the bottom outline.
        """

    def GetSize(self) -> TextAttrSize:
        """
        GetSize() -> TextAttrSize
        
        Returns the object size.
        """

    def GetMinSize(self) -> TextAttrSize:
        """
        GetMinSize() -> TextAttrSize
        
        Returns the object minimum size.
        """

    def GetMaxSize(self) -> TextAttrSize:
        """
        GetMaxSize() -> TextAttrSize
        
        Returns the object maximum size.
        """

    def SetSize(self, sz: TextAttrSize) -> None:
        """
        SetSize(sz) -> None
        
        Sets the object size.
        """

    def SetMinSize(self, sz: TextAttrSize) -> None:
        """
        SetMinSize(sz) -> None
        
        Sets the object minimum size.
        """

    def SetMaxSize(self, sz: TextAttrSize) -> None:
        """
        SetMaxSize(sz) -> None
        
        Sets the object maximum size.
        """

    def GetWidth(self) -> TextAttrDimension:
        """
        GetWidth() -> TextAttrDimension
        
        Returns the object width.
        """

    def GetHeight(self) -> TextAttrDimension:
        """
        GetHeight() -> TextAttrDimension
        
        Returns the object height.
        """

    def GetBoxStyleName(self) -> str:
        """
        GetBoxStyleName() -> str
        
        Returns the box style name.
        """

    def SetBoxStyleName(self, name: str) -> None:
        """
        SetBoxStyleName(name) -> None
        
        Sets the box style name.
        """

    def HasBoxStyleName(self) -> bool:
        """
        HasBoxStyleName() -> bool
        
        Returns true if the box style name is present.
        """

    def GetShadow(self) -> TextAttrShadow:
        """
        GetShadow() -> TextAttrShadow
        
        Returns the box shadow attributes.
        """
    @property
    def Border(self) -> TextAttrBorders: ...
    @property
    def Bottom(self) -> TextAttrDimension: ...
    @property
    def BottomBorder(self) -> TextAttrBorder: ...
    @property
    def BottomMargin(self) -> TextAttrDimension: ...
    @property
    def BottomOutline(self) -> TextAttrBorder: ...
    @property
    def BottomPadding(self) -> TextAttrDimension: ...
    @property
    def BoxStyleName(self) -> str: ...
    @BoxStyleName.setter
    def BoxStyleName(self, value: str, /) -> None: ...
    @property
    def ClearMode(self) -> TextBoxAttrClearStyle: ...
    @ClearMode.setter
    def ClearMode(self, value: TextBoxAttrClearStyle, /) -> None: ...
    @property
    def CollapseBorders(self) -> TextBoxAttrCollapseMode: ...
    @CollapseBorders.setter
    def CollapseBorders(self, value: TextBoxAttrCollapseMode, /) -> None: ...
    @property
    def CornerRadius(self) -> TextAttrDimension: ...
    @CornerRadius.setter
    def CornerRadius(self, value: TextAttrDimension, /) -> None: ...
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def FloatMode(self) -> TextBoxAttrFloatStyle: ...
    @FloatMode.setter
    def FloatMode(self, value: TextBoxAttrFloatStyle, /) -> None: ...
    @property
    def Height(self) -> TextAttrDimension: ...
    @property
    def Left(self) -> TextAttrDimension: ...
    @property
    def LeftBorder(self) -> TextAttrBorder: ...
    @property
    def LeftMargin(self) -> TextAttrDimension: ...
    @property
    def LeftOutline(self) -> TextAttrBorder: ...
    @property
    def LeftPadding(self) -> TextAttrDimension: ...
    @property
    def Margins(self) -> TextAttrDimensions: ...
    @property
    def MaxSize(self) -> TextAttrSize: ...
    @MaxSize.setter
    def MaxSize(self, value: TextAttrSize, /) -> None: ...
    @property
    def MinSize(self) -> TextAttrSize: ...
    @MinSize.setter
    def MinSize(self, value: TextAttrSize, /) -> None: ...
    @property
    def Outline(self) -> TextAttrBorders: ...
    @property
    def Padding(self) -> TextAttrDimensions: ...
    @property
    def Position(self) -> TextAttrDimensions: ...
    @property
    def Right(self) -> TextAttrDimension: ...
    @property
    def RightBorder(self) -> TextAttrBorder: ...
    @property
    def RightMargin(self) -> TextAttrDimension: ...
    @property
    def RightOutline(self) -> TextAttrBorder: ...
    @property
    def RightPadding(self) -> TextAttrDimension: ...
    @property
    def Shadow(self) -> TextAttrShadow: ...
    @property
    def Size(self) -> TextAttrSize: ...
    @Size.setter
    def Size(self, value: TextAttrSize, /) -> None: ...
    @property
    def Top(self) -> TextAttrDimension: ...
    @property
    def TopBorder(self) -> TextAttrBorder: ...
    @property
    def TopMargin(self) -> TextAttrDimension: ...
    @property
    def TopOutline(self) -> TextAttrBorder: ...
    @property
    def TopPadding(self) -> TextAttrDimension: ...
    @property
    def VerticalAlignment(self) -> TextBoxAttrVerticalAlignment: ...
    @VerticalAlignment.setter
    def VerticalAlignment(self, value: TextBoxAttrVerticalAlignment, /) -> None: ...
    @property
    def WhitespaceMode(self) -> TextBoxAttrWhitespaceMode: ...
    @WhitespaceMode.setter
    def WhitespaceMode(self, value: TextBoxAttrWhitespaceMode, /) -> None: ...
    @property
    def Width(self) -> TextAttrDimension: ...
# end of class TextBoxAttr


class RichTextAttr(wx.TextAttr):
    """
    RichTextAttr(attr) -> None
    RichTextAttr(attr) -> None
    RichTextAttr() -> None
    
    A class representing enhanced attributes for rich text objects.
    """

    @overload
    def __init__(self, attr: RichTextAttr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        ...

    @overload
    def __init__(self, attr: wx.TextAttr) -> None:
        """
        RichTextAttr(attr) -> None
        RichTextAttr(attr) -> None
        RichTextAttr() -> None
        
        A class representing enhanced attributes for rich text objects.
        """
    m_textBoxAttr: TextBoxAttr

    def Copy(self, attr: RichTextAttr) -> None:
        """
        Copy(attr) -> None
        
        Copy function.
        """

    def __eq__(self, attr: RichTextAttr) -> bool:
        """
        """

    def EqPartial(self, attr: RichTextAttr, weakTest: bool=True) -> bool:
        """
        EqPartial(attr, weakTest=True) -> bool
        
        Partial equality test.
        """

    def Apply(self, style: RichTextAttr, compareWith: Optional[RichTextAttr]=None) -> bool:
        """
        Apply(style, compareWith=None) -> bool
        
        Merges the given attributes.
        """

    def CollectCommonAttributes(self, attr: RichTextAttr, clashingAttr: RichTextAttr, absentAttr: RichTextAttr) -> None:
        """
        CollectCommonAttributes(attr, clashingAttr, absentAttr) -> None
        
        Collects the attributes that are common to a range of content,
        building up a note of which attributes are absent in some objects and
        which clash in some objects.
        """

    def RemoveStyle(self, attr: RichTextAttr) -> bool:
        """
        RemoveStyle(attr) -> bool
        
        Removes the specified attributes from this object.
        """

    def GetTextBoxAttr(self) -> TextBoxAttr:
        """
        GetTextBoxAttr() -> TextBoxAttr
        
        Returns the text box attributes.
        """

    def SetTextBoxAttr(self, attr: TextBoxAttr) -> None:
        """
        SetTextBoxAttr(attr) -> None
        
        Set the text box attributes.
        """

    def IsDefault(self) -> bool:
        """
        IsDefault() -> bool
        
        Returns true if no attributes are set.
        """
    @property
    def TextBoxAttr(self) -> TextBoxAttr: ...
    @TextBoxAttr.setter
    def TextBoxAttr(self, value: TextBoxAttr, /) -> None: ...
# end of class RichTextAttr


class RichTextProperties(wx.Object):
    """
    RichTextProperties() -> None
    RichTextProperties(props) -> None
    
    A simple property class using wxVariants.
    """

    @overload
    def __init__(self, props: RichTextProperties) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RichTextProperties() -> None
        RichTextProperties(props) -> None
        
        A simple property class using wxVariants.
        """

    def __eq__(self, props: RichTextProperties) -> bool:
        """
        """

    def Copy(self, props: RichTextProperties) -> None:
        """
        Copy(props) -> None
        
        Copies from props.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Clears the properties.
        """

    def GetProperties(self) -> RichTextVariantArray:
        """
        GetProperties() -> RichTextVariantArray
        
        Returns the array of variants implementing the properties.
        """

    def SetProperties(self, props: RichTextVariantArray) -> None:
        """
        SetProperties(props) -> None
        
        Sets the array of variants.
        """

    def GetPropertyNames(self) -> List[str]:
        """
        GetPropertyNames() -> List[str]
        
        Returns all the property names.
        """

    def GetCount(self) -> int:
        """
        GetCount() -> int
        
        Returns a count of the properties.
        """

    def HasProperty(self, name: str) -> bool:
        """
        HasProperty(name) -> bool
        
        Returns true if the given property is found.
        """

    def Find(self, name: str) -> int:
        """
        Find(name) -> int
        
        Finds the given property.
        """

    def Remove(self, name: str) -> bool:
        """
        Remove(name) -> bool
        
        Removes the given property.
        """

    def GetProperty(self, name: str) -> Variant:
        """
        GetProperty(name) -> Variant
        
        Gets the property variant by name.
        """

    def FindOrCreateProperty(self, name: str) -> Variant:
        """
        FindOrCreateProperty(name) -> Variant
        
        Finds or creates a property with the given name, returning a pointer
        to the variant.
        """

    def GetPropertyString(self, name: str) -> str:
        """
        GetPropertyString(name) -> str
        
        Gets the value of the named property as a string.
        """

    def GetPropertyLong(self, name: str) -> int:
        """
        GetPropertyLong(name) -> int
        
        Gets the value of the named property as a long integer.
        """

    def GetPropertyBool(self, name: str) -> bool:
        """
        GetPropertyBool(name) -> bool
        
        Gets the value of the named property as a boolean.
        """

    def GetPropertyDouble(self, name: str) -> float:
        """
        GetPropertyDouble(name) -> float
        
        Gets the value of the named property as a double.
        """

    @overload
    def SetProperty(self, name: str, variant: Variant) -> None:
        ...

    @overload
    def SetProperty(self, name: str, value: str) -> None:
        ...

    @overload
    def SetProperty(self, name: str, value: str) -> None:
        ...

    @overload
    def SetProperty(self, name: str, value: int) -> None:
        ...

    @overload
    def SetProperty(self, name: str, value: float) -> None:
        ...

    @overload
    def SetProperty(self, variant: Variant) -> None:
        """
        SetProperty(variant) -> None
        SetProperty(name, variant) -> None
        SetProperty(name, value) -> None
        SetProperty(name, value) -> None
        SetProperty(name, value) -> None
        SetProperty(name, value) -> None
        
        Sets the property by passing a variant which contains a name and
        value.
        """

    def RemoveProperties(self, properties: RichTextProperties) -> None:
        """
        RemoveProperties(properties) -> None
        
        Removes the given properties from these properties.
        """

    def MergeProperties(self, properties: RichTextProperties) -> None:
        """
        MergeProperties(properties) -> None
        
        Merges the given properties with these properties.
        """
    @property
    def Count(self) -> int: ...
    @property
    def Properties(self) -> RichTextVariantArray: ...
    @Properties.setter
    def Properties(self, value: RichTextVariantArray, /) -> None: ...
    @property
    def PropertyNames(self) -> List[str]: ...
# end of class RichTextProperties


class RichTextFontTable(wx.Object):
    """
    RichTextFontTable() -> None
    RichTextFontTable(table) -> None
    
    Manages quick access to a pool of fonts for rendering rich text.
    """

    @overload
    def __init__(self, table: RichTextFontTable) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RichTextFontTable() -> None
        RichTextFontTable(table) -> None
        
        Manages quick access to a pool of fonts for rendering rich text.
        """

    def IsOk(self) -> bool:
        """
        IsOk() -> bool
        
        Returns true if the font table is valid.
        """

    def FindFont(self, fontSpec: RichTextAttr) -> wx.Font:
        """
        FindFont(fontSpec) -> wx.Font
        
        Finds a font for the given attribute object.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Clears the font table.
        """

    def __eq__(self, table: RichTextFontTable) -> bool:
        """
        """

    def __ne__(self, table: RichTextFontTable) -> bool:
        """
        """

    def SetFontScale(self, fontScale: float) -> None:
        """
        SetFontScale(fontScale) -> None
        
        Set the font scale factor.
        """
# end of class RichTextFontTable


class RichTextRange:
    """
    RichTextRange() -> None
    RichTextRange(start, end) -> None
    RichTextRange(range) -> None
    
    This stores beginning and end positions for a range of data.
    """

    @overload
    def __init__(self, start: int, end: int) -> None:
        ...

    @overload
    def __init__(self, range: Union[RichTextRange, wx._TwoInts]) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RichTextRange() -> None
        RichTextRange(start, end) -> None
        RichTextRange(range) -> None
        
        This stores beginning and end positions for a range of data.
        """

    def __eq__(self, range: Union[RichTextRange, wx._TwoInts]) -> bool:
        """
        """

    def __ne__(self, range: Union[RichTextRange, wx._TwoInts]) -> bool:
        """
        """

    def __sub__(self, range: Union[RichTextRange, wx._TwoInts]) -> RichTextRange:
        """
        """

    def __add__(self, range: Union[RichTextRange, wx._TwoInts]) -> RichTextRange:
        """
        """

    def SetRange(self, start: int, end: int) -> None:
        """
        SetRange(start, end) -> None
        
        Sets the range start and end positions.
        """

    def SetStart(self, start: int) -> None:
        """
        SetStart(start) -> None
        
        Sets the start position.
        """

    def GetStart(self) -> int:
        """
        GetStart() -> int
        
        Returns the start position.
        """

    def SetEnd(self, end: int) -> None:
        """
        SetEnd(end) -> None
        
        Sets the end position.
        """

    def GetEnd(self) -> int:
        """
        GetEnd() -> int
        
        Gets the end position.
        """

    def IsOutside(self, range: Union[RichTextRange, wx._TwoInts]) -> bool:
        """
        IsOutside(range) -> bool
        
        Returns true if this range is completely outside range.
        """

    def IsWithin(self, range: Union[RichTextRange, wx._TwoInts]) -> bool:
        """
        IsWithin(range) -> bool
        
        Returns true if this range is completely within range.
        """

    def Contains(self, pos: int) -> bool:
        """
        Contains(pos) -> bool
        
        Returns true if pos was within the range.
        """

    def LimitTo(self, range: Union[RichTextRange, wx._TwoInts]) -> bool:
        """
        LimitTo(range) -> bool
        
        Limit this range to be within range.
        """

    def GetLength(self) -> int:
        """
        GetLength() -> int
        
        Gets the length of the range.
        """

    def Swap(self) -> None:
        """
        Swap() -> None
        
        Swaps the start and end.
        """

    def ToInternal(self) -> RichTextRange:
        """
        ToInternal() -> RichTextRange
        
        Converts the API-standard range, whose end is one past the last
        character in the range, to the internal form, which uses the first and
        last character positions of the range.
        """

    def FromInternal(self) -> RichTextRange:
        """
        FromInternal() -> RichTextRange
        
        Converts the internal range, which uses the first and last character
        positions of the range, to the API-standard range, whose end is one
        past the last character in the range.
        """
    @property
    def End(self) -> int: ...
    @End.setter
    def End(self, value: int, /) -> None: ...
    @property
    def Length(self) -> int: ...
    @property
    def Start(self) -> int: ...
    @Start.setter
    def Start(self, value: int, /) -> None: ...

    def Get(self) -> Any:
        """
        Get() -> (start, end)
        
        Return the start and end properties as a tuple.
        """

    def GetIM(self):
        """
        Returns an immutable representation of the ``wx.RichTextRange`` object, based on ``namedtuple``.
        
        This new object is hashable and can be used as a dictionary key,
        be added to sets, etc.  It can be converted back into a real ``wx.RichTextRange``
        with a simple statement like this: ``obj = wx.RichTextRange(imObj)``.
        """

    def __str__(self):
        """
        
        """

    def __repr__(self):
        """
        
        """

    def __len__(self):
        """
        
        """

    def __nonzero__(self):
        """
        
        """

    def __bool__(self):
        """
        
        """

    def __reduce__(self):
        """
        
        """

    def __getitem__(self, idx):
        """
        
        """

    def __setitem__(self, idx, val):
        """
        
        """

    __safe_for_unpickling__ = True
# end of class RichTextRange


RICHTEXT_ALL = RichTextRange(-2, -2)
RICHTEXT_NONE = RichTextRange(-1, -1)
RICHTEXT_NO_SELECTION = RichTextRange(-2, -2)

class RichTextSelection:
    """
    RichTextSelection(sel) -> None
    RichTextSelection(range, container) -> None
    RichTextSelection() -> None
    
    Stores selection information.
    """

    @overload
    def __init__(self, range: Union[RichTextRange, wx._TwoInts], container: RichTextParagraphLayoutBox) -> None:
        ...

    @overload
    def __init__(self) -> None:
        ...

    @overload
    def __init__(self, sel: RichTextSelection) -> None:
        """
        RichTextSelection(sel) -> None
        RichTextSelection(range, container) -> None
        RichTextSelection() -> None
        
        Stores selection information.
        """
    m_ranges: RichTextRangeArray
    m_container: RichTextParagraphLayoutBox

    def Reset(self) -> None:
        """
        Reset() -> None
        
        Resets the selection.
        """

    @overload
    def Set(self, ranges: RichTextRangeArray, container: RichTextParagraphLayoutBox) -> None:
        ...

    @overload
    def Set(self, range: Union[RichTextRange, wx._TwoInts], container: RichTextParagraphLayoutBox) -> None:
        """
        Set(range, container) -> None
        Set(ranges, container) -> None
        
        Sets the selection.
        """

    def Add(self, range: Union[RichTextRange, wx._TwoInts]) -> None:
        """
        Add(range) -> None
        
        Adds a range to the selection.
        """

    def Copy(self, sel: RichTextSelection) -> None:
        """
        Copy(sel) -> None
        
        Copies from sel.
        """

    def __eq__(self, sel: RichTextSelection) -> bool:
        """
        """

    def GetRanges(self) -> RichTextRangeArray:
        """
        GetRanges() -> RichTextRangeArray
        
        Returns the selection ranges.
        """

    def SetRanges(self, ranges: RichTextRangeArray) -> None:
        """
        SetRanges(ranges) -> None
        
        Sets the selection ranges.
        """

    def GetCount(self) -> int:
        """
        GetCount() -> int
        
        Returns the number of ranges in the selection.
        """

    @overload
    def GetRange(self) -> RichTextRange:
        ...

    @overload
    def GetRange(self, i: int) -> RichTextRange:
        """
        GetRange(i) -> RichTextRange
        GetRange() -> RichTextRange
        
        Returns the range at the given index.
        """

    def SetRange(self, range: Union[RichTextRange, wx._TwoInts]) -> None:
        """
        SetRange(range) -> None
        
        Sets a single range.
        """

    def GetContainer(self) -> RichTextParagraphLayoutBox:
        """
        GetContainer() -> RichTextParagraphLayoutBox
        
        Returns the container for which the selection is valid.
        """

    def SetContainer(self, container: RichTextParagraphLayoutBox) -> None:
        """
        SetContainer(container) -> None
        
        Sets the container for which the selection is valid.
        """

    def IsValid(self) -> bool:
        """
        IsValid() -> bool
        
        Returns true if the selection is valid.
        """

    def GetSelectionForObject(self, obj: RichTextObject) -> RichTextRangeArray:
        """
        GetSelectionForObject(obj) -> RichTextRangeArray
        
        Returns the selection appropriate to the specified object, if any;
        returns an empty array if none at the level of the object's container.
        """

    @overload
    def WithinSelection(self, pos: int) -> bool:
        ...

    @overload
    @staticmethod
    def WithinSelection(pos: int, ranges: RichTextRangeArray) -> bool:
        ...

    @overload
    @staticmethod
    def WithinSelection(range: Union[RichTextRange, wx._TwoInts], ranges: RichTextRangeArray) -> bool:
        ...

    @overload
    def WithinSelection(self, pos: int, obj: RichTextObject) -> bool:
        """
        WithinSelection(pos, obj) -> bool
        WithinSelection(pos) -> bool
        WithinSelection(pos, ranges) -> bool
        WithinSelection(range, ranges) -> bool
        
        Returns true if the given position is within the selection.
        """

    def __nonzero__(self) -> bool:
        """
        __nonzero__() -> bool
        """

    def __bool__(self) -> bool:
        """
        __bool__() -> bool
        """
    @property
    def Container(self) -> RichTextParagraphLayoutBox: ...
    @Container.setter
    def Container(self, value: RichTextParagraphLayoutBox, /) -> None: ...
    @property
    def Count(self) -> int: ...
    @property
    def Range(self) -> Union[RichTextRange, wx._TwoInts]: ...
    @Range.setter
    def Range(self, value: Union[RichTextRange, wx._TwoInts], /) -> None: ...
    @property
    def Ranges(self) -> RichTextRangeArray: ...
    @Ranges.setter
    def Ranges(self, value: RichTextRangeArray, /) -> None: ...
# end of class RichTextSelection


class RichTextDrawingContext(wx.Object):
    """
    RichTextDrawingContext(buffer) -> None
    
    A class for passing information to drawing and measuring functions.
    """

    def __init__(self, buffer: RichTextBuffer) -> None:
        """
        RichTextDrawingContext(buffer) -> None
        
        A class for passing information to drawing and measuring functions.
        """
    m_buffer: RichTextBuffer
    m_enableVirtualAttributes: bool
    m_enableImages: bool
    m_enableDelayedImageLoading: bool
    m_layingOut: bool

    def Init(self) -> None:
        """
        Init() -> None
        """

    def HasVirtualAttributes(self, obj: RichTextObject) -> bool:
        """
        HasVirtualAttributes(obj) -> bool
        
        Does this object have virtual attributes? Virtual attributes can be
        provided for visual cues without affecting the actual styling.
        """

    def GetVirtualAttributes(self, obj: RichTextObject) -> RichTextAttr:
        """
        GetVirtualAttributes(obj) -> RichTextAttr
        
        Returns the virtual attributes for this object.
        """

    def ApplyVirtualAttributes(self, attr: RichTextAttr, obj: RichTextObject) -> bool:
        """
        ApplyVirtualAttributes(attr, obj) -> bool
        
        Applies any virtual attributes relevant to this object.
        """

    def GetVirtualSubobjectAttributesCount(self, obj: RichTextObject) -> int:
        """
        GetVirtualSubobjectAttributesCount(obj) -> int
        
        Gets the count for mixed virtual attributes for individual positions
        within the object.
        """

    def GetVirtualSubobjectAttributes(self, obj: RichTextObject, positions: List[int], attributes: RichTextAttrArray) -> int:
        """
        GetVirtualSubobjectAttributes(obj, positions, attributes) -> int
        
        Gets the mixed virtual attributes for individual positions within the
        object.
        """

    def HasVirtualText(self, obj: RichTextPlainText) -> bool:
        """
        HasVirtualText(obj) -> bool
        
        Do we have virtual text for this object? Virtual text allows an
        application to replace characters in an object for editing and display
        purposes, for example for highlighting special characters.
        """

    def GetVirtualText(self, obj: RichTextPlainText, text: str) -> bool:
        """
        GetVirtualText(obj, text) -> bool
        
        Gets the virtual text for this object.
        """

    def EnableVirtualAttributes(self, b: bool) -> None:
        """
        EnableVirtualAttributes(b) -> None
        
        Enables virtual attribute processing.
        """

    def GetVirtualAttributesEnabled(self) -> bool:
        """
        GetVirtualAttributesEnabled() -> bool
        
        Returns true if virtual attribute processing is enabled.
        """

    def EnableImages(self, b: bool) -> None:
        """
        EnableImages(b) -> None
        
        Enable or disable images.
        """

    def GetImagesEnabled(self) -> bool:
        """
        GetImagesEnabled() -> bool
        
        Returns true if images are enabled.
        """

    def SetLayingOut(self, b: bool) -> None:
        """
        SetLayingOut(b) -> None
        
        Set laying out flag.
        """

    def GetLayingOut(self) -> bool:
        """
        GetLayingOut() -> bool
        
        Returns true if laying out.
        """

    def EnableDelayedImageLoading(self, b: bool) -> None:
        """
        EnableDelayedImageLoading(b) -> None
        
        Enable or disable delayed image loading.
        """

    def GetDelayedImageLoading(self) -> bool:
        """
        GetDelayedImageLoading() -> bool
        
        Returns true if delayed image loading is enabled.
        """
    @property
    def DelayedImageLoading(self) -> bool: ...
    @property
    def ImagesEnabled(self) -> bool: ...
    @property
    def LayingOut(self) -> bool: ...
    @LayingOut.setter
    def LayingOut(self, value: bool, /) -> None: ...
    @property
    def VirtualAttributesEnabled(self) -> bool: ...
# end of class RichTextDrawingContext


class RichTextObject(wx.Object):
    """
    RichTextObject(parent=None) -> None
    
    This is the base for drawable rich text objects.
    """

    def __init__(self, parent: Optional[RichTextObject]=None) -> None:
        """
        RichTextObject(parent=None) -> None
        
        This is the base for drawable rich text objects.
        """

    def Draw(self, dc: wx.DC, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts], selection: RichTextSelection, rect: wx.Rect, descent: int, style: int) -> bool:
        """
        Draw(dc, context, range, selection, rect, descent, style) -> bool
        
        Draw the item, within the given range.
        """

    def Layout(self, dc: wx.DC, context: RichTextDrawingContext, rect: wx.Rect, parentRect: wx.Rect, style: int) -> bool:
        """
        Layout(dc, context, rect, parentRect, style) -> bool
        
        Lay the item out at the specified position with the given size
        constraint.
        """

    def HitTest(self, dc: wx.DC, context: RichTextDrawingContext, pt: wx.Point, flags: int=0) -> Tuple[int, int, RichTextObject, RichTextObject]:
        """
        HitTest(dc, context, pt, flags=0) -> Tuple[int, int, RichTextObject, RichTextObject]
        
        Hit-testing: returns a flag indicating hit test details, plus
        information about position.
        """

    def FindPosition(self, dc: wx.DC, context: RichTextDrawingContext, index: int, forceLineStart: bool) -> Tuple[bool, wx.Point, int]:
        """
        FindPosition(dc, context, index, forceLineStart) -> Tuple[bool, wx.Point, int]
        
        Finds the absolute position and row height for the given character
        position.
        """

    def GetBestSize(self) -> wx.Size:
        """
        GetBestSize() -> wx.Size
        
        Returns the best size, i.e. the ideal starting size for this object
        irrespective of available space.
        """

    def GetRangeSize(self, range: Union[RichTextRange, wx._TwoInts], size: wx.Size, descent: int, dc: wx.DC, context: RichTextDrawingContext, flags: int, position: wx.Point=wx.Point(0,0), parentSize: wx.Size=wx.DefaultSize, partialExtents: Optional[List[int]]=None) -> bool:
        """
        GetRangeSize(range, size, descent, dc, context, flags, position=wx.Point(0,0), parentSize=wx.DefaultSize, partialExtents=None) -> bool
        
        Returns the object size for the given range.
        """

    def DoSplit(self, pos: int) -> RichTextObject:
        """
        DoSplit(pos) -> RichTextObject
        
        Do a split from pos, returning an object containing the second part,
        and setting the first part in 'this'.
        """

    def CalculateRange(self, start: int) -> int:
        """
        CalculateRange(start) -> int
        
        Calculates the range of the object.
        """

    def DeleteRange(self, range: Union[RichTextRange, wx._TwoInts]) -> bool:
        """
        DeleteRange(range) -> bool
        
        Deletes the given range.
        """

    def IsEmpty(self) -> bool:
        """
        IsEmpty() -> bool
        
        Returns true if the object is empty.
        """

    def IsFloatable(self) -> bool:
        """
        IsFloatable() -> bool
        
        Returns true if this class of object is floatable.
        """

    def IsFloating(self) -> bool:
        """
        IsFloating() -> bool
        
        Returns true if this object is currently floating.
        """

    def GetFloatDirection(self) -> int:
        """
        GetFloatDirection() -> int
        
        Returns the floating direction.
        """

    def GetTextForRange(self, range: Union[RichTextRange, wx._TwoInts]) -> str:
        """
        GetTextForRange(range) -> str
        
        Returns any text in this object for the given range.
        """

    def CanMerge(self, object: RichTextObject, context: RichTextDrawingContext) -> bool:
        """
        CanMerge(object, context) -> bool
        
        Returns true if this object can merge itself with the given one.
        """

    def Merge(self, object: RichTextObject, context: RichTextDrawingContext) -> bool:
        """
        Merge(object, context) -> bool
        
        Returns true if this object merged itself with the given one.
        """

    def CanSplit(self, context: RichTextDrawingContext) -> bool:
        """
        CanSplit(context) -> bool
        
        Returns true if this object can potentially be split, by virtue of
        having different virtual attributes for individual sub-objects.
        """

    def Split(self, context: RichTextDrawingContext) -> RichTextObject:
        """
        Split(context) -> RichTextObject
        
        Returns the final object in the split objects if this object was split
        due to differences between sub-object virtual attributes.
        """

    def CanEditProperties(self) -> bool:
        """
        CanEditProperties() -> bool
        
        Returns true if we can edit the object's properties via a GUI.
        """

    def EditProperties(self, parent: wx.Window, buffer: RichTextBuffer) -> bool:
        """
        EditProperties(parent, buffer) -> bool
        
        Edits the object's properties via a GUI.
        """

    def GetPropertiesMenuLabel(self) -> str:
        """
        GetPropertiesMenuLabel() -> str
        
        Returns the label to be used for the properties context menu item.
        """

    def AcceptsFocus(self) -> bool:
        """
        AcceptsFocus() -> bool
        
        Returns true if objects of this class can accept the focus, i.e. a
        call to SetFocusObject is possible.
        """

    def ImportFromXML(self, buffer: RichTextBuffer, node: XmlNode, handler: RichTextXMLHandler, recurse: bool) -> bool:
        """
        ImportFromXML(buffer, node, handler, recurse) -> bool
        
        Imports this object from XML.
        """

    def UsesParagraphAttributes(self) -> bool:
        """
        UsesParagraphAttributes() -> bool
        
        Returns true if this object takes note of paragraph attributes (text
        and image objects don't).
        """

    def GetXMLNodeName(self) -> str:
        """
        GetXMLNodeName() -> str
        
        Returns the XML node name of this object.
        """

    def Invalidate(self, invalidRange: Union[RichTextRange, wx._TwoInts]=RICHTEXT_ALL) -> None:
        """
        Invalidate(invalidRange=RICHTEXT_ALL) -> None
        
        Invalidates the object at the given range.
        """

    def HandlesChildSelections(self) -> bool:
        """
        HandlesChildSelections() -> bool
        
        Returns true if this object can handle the selections of its children,
        fOr example a table.
        """

    def GetSelection(self, start: int, end: int) -> RichTextSelection:
        """
        GetSelection(start, end) -> RichTextSelection
        
        Returns a selection object specifying the selections between start and
        end character positions.
        """

    def GetCachedSize(self) -> wx.Size:
        """
        GetCachedSize() -> wx.Size
        
        Gets the cached object size as calculated by Layout.
        """

    def SetCachedSize(self, sz: wx.Size) -> None:
        """
        SetCachedSize(sz) -> None
        
        Sets the cached object size as calculated by Layout.
        """

    def GetMaxSize(self) -> wx.Size:
        """
        GetMaxSize() -> wx.Size
        
        Gets the maximum object size as calculated by Layout.
        """

    def SetMaxSize(self, sz: wx.Size) -> None:
        """
        SetMaxSize(sz) -> None
        
        Sets the maximum object size as calculated by Layout.
        """

    def GetMinSize(self) -> wx.Size:
        """
        GetMinSize() -> wx.Size
        
        Gets the minimum object size as calculated by Layout.
        """

    def SetMinSize(self, sz: wx.Size) -> None:
        """
        SetMinSize(sz) -> None
        
        Sets the minimum object size as calculated by Layout.
        """

    def GetNaturalSize(self) -> TextAttrSize:
        """
        GetNaturalSize() -> TextAttrSize
        
        Gets the 'natural' size for an object.
        """

    def GetPosition(self) -> wx.Point:
        """
        GetPosition() -> wx.Point
        
        Returns the object position in pixels.
        """

    def SetPosition(self, pos: wx.Point) -> None:
        """
        SetPosition(pos) -> None
        
        Sets the object position in pixels.
        """

    def GetAbsolutePosition(self) -> wx.Point:
        """
        GetAbsolutePosition() -> wx.Point
        
        Returns the absolute object position, by traversing up the
        child/parent hierarchy.
        """

    def GetRect(self) -> wx.Rect:
        """
        GetRect() -> wx.Rect
        
        Returns the rectangle enclosing the object.
        """

    def SetRange(self, range: Union[RichTextRange, wx._TwoInts]) -> None:
        """
        SetRange(range) -> None
        
        Sets the object's range within its container.
        """

    def GetRange(self) -> RichTextRange:
        """
        GetRange() -> RichTextRange
        
        Returns the object's range.
        """

    def SetOwnRange(self, range: Union[RichTextRange, wx._TwoInts]) -> None:
        """
        SetOwnRange(range) -> None
        
        Set the object's own range, for a top-level object with its own
        position space.
        """

    def GetOwnRange(self) -> RichTextRange:
        """
        GetOwnRange() -> RichTextRange
        
        Returns the object's own range (valid if top-level).
        """

    def GetOwnRangeIfTopLevel(self) -> RichTextRange:
        """
        GetOwnRangeIfTopLevel() -> RichTextRange
        
        Returns the object's own range only if a top-level object.
        """

    def IsComposite(self) -> bool:
        """
        IsComposite() -> bool
        
        Returns true if this object is composite.
        """

    def IsAtomic(self) -> bool:
        """
        IsAtomic() -> bool
        
        Returns true if no user editing can be done inside the object.
        """

    def GetParent(self) -> RichTextObject:
        """
        GetParent() -> RichTextObject
        
        Returns a pointer to the parent object.
        """

    def SetParent(self, parent: RichTextObject) -> None:
        """
        SetParent(parent) -> None
        
        Sets the pointer to the parent object.
        """

    def GetContainer(self) -> RichTextParagraphLayoutBox:
        """
        GetContainer() -> RichTextParagraphLayoutBox
        
        Returns the top-level container of this object.
        """

    def GetParentContainer(self) -> RichTextParagraphLayoutBox:
        """
        GetParentContainer() -> RichTextParagraphLayoutBox
        
        Returns the top-level container of this object.
        """

    @overload
    def SetMargins(self, leftMargin: int, rightMargin: int, topMargin: int, bottomMargin: int) -> None:
        ...

    @overload
    def SetMargins(self, margin: int) -> None:
        """
        SetMargins(margin) -> None
        SetMargins(leftMargin, rightMargin, topMargin, bottomMargin) -> None
        
        Set the margin around the object, in pixels.
        """

    def GetLeftMargin(self) -> int:
        """
        GetLeftMargin() -> int
        
        Returns the left margin of the object, in pixels.
        """

    def GetRightMargin(self) -> int:
        """
        GetRightMargin() -> int
        
        Returns the right margin of the object, in pixels.
        """

    def GetTopMargin(self) -> int:
        """
        GetTopMargin() -> int
        
        Returns the top margin of the object, in pixels.
        """

    def GetBottomMargin(self) -> int:
        """
        GetBottomMargin() -> int
        
        Returns the bottom margin of the object, in pixels.
        """

    def GetAvailableContentArea(self, dc: wx.DC, context: RichTextDrawingContext, outerRect: wx.Rect) -> wx.Rect:
        """
        GetAvailableContentArea(dc, context, outerRect) -> wx.Rect
        
        Calculates the available content space in the given rectangle, given
        the margins, border and padding specified in the object's attributes.
        """

    def LayoutToBestSize(self, dc: wx.DC, context: RichTextDrawingContext, buffer: RichTextBuffer, parentAttr: RichTextAttr, attr: RichTextAttr, availableParentSpace: wx.Rect, availableContainerSpace: wx.Rect, style: int) -> bool:
        """
        LayoutToBestSize(dc, context, buffer, parentAttr, attr, availableParentSpace, availableContainerSpace, style) -> bool
        
        Lays out the object first with a given amount of space, and then if no
        width was specified in attr, lays out the object again using the
        minimum size.
        """

    def AdjustAttributes(self, attr: RichTextAttr, context: RichTextDrawingContext) -> bool:
        """
        AdjustAttributes(attr, context) -> bool
        
        Adjusts the attributes for virtual attribute provision, collapsed
        borders, etc.
        """

    def SetAttributes(self, attr: RichTextAttr) -> None:
        """
        SetAttributes(attr) -> None
        
        Sets the object's attributes.
        """

    def GetAttributes(self) -> RichTextAttr:
        """
        GetAttributes() -> RichTextAttr
        
        Returns the object's attributes.
        """

    def GetProperties(self) -> RichTextProperties:
        """
        GetProperties() -> RichTextProperties
        
        Returns the object's properties.
        """

    def SetProperties(self, props: RichTextProperties) -> None:
        """
        SetProperties(props) -> None
        
        Sets the object's properties.
        """

    def SetDescent(self, descent: int) -> None:
        """
        SetDescent(descent) -> None
        
        Sets the stored descent value.
        """

    def GetDescent(self) -> int:
        """
        GetDescent() -> int
        
        Returns the stored descent value.
        """

    def GetBuffer(self) -> RichTextBuffer:
        """
        GetBuffer() -> RichTextBuffer
        
        Returns the containing buffer.
        """

    def SetName(self, name: str) -> None:
        """
        SetName(name) -> None
        
        Sets the identifying name for this object as a property using the
        "name" key.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns the identifying name for this object from the properties,
        using the "name" key.
        """

    def IsTopLevel(self) -> bool:
        """
        IsTopLevel() -> bool
        
        Returns true if this object is top-level, i.e. contains its own
        paragraphs, such as a text box.
        """

    def IsShown(self) -> bool:
        """
        IsShown() -> bool
        
        Returns true if the object will be shown, false otherwise.
        """

    def Show(self, show: bool) -> None:
        """
        Show(show) -> None
        
        Call to show or hide this object.
        """

    def Clone(self) -> RichTextObject:
        """
        Clone() -> RichTextObject
        
        Clones the object.
        """

    def Copy(self, obj: RichTextObject) -> None:
        """
        Copy(obj) -> None
        
        Copies the object.
        """

    def Reference(self) -> None:
        """
        Reference() -> None
        
        Reference-counting allows us to use the same object in multiple lists (not yet used).
        """

    def Dereference(self) -> None:
        """
        Dereference() -> None
        
        Reference-counting allows us to use the same object in multiple lists
        (not yet used).
        """

    def Move(self, pt: wx.Point) -> None:
        """
        Move(pt) -> None
        
        Moves the object recursively, by adding the offset from old to new.
        """

    @overload
    @staticmethod
    def ConvertTenthsMMToPixels(ppi: int, units: int, scale: float=1.0) -> int:
        ...

    @overload
    def ConvertTenthsMMToPixels(self, dc: wx.DC, units: int) -> int:
        """
        ConvertTenthsMMToPixels(dc, units) -> int
        ConvertTenthsMMToPixels(ppi, units, scale=1.0) -> int
        
        Converts units in tenths of a millimetre to device units.
        """

    @overload
    @staticmethod
    def ConvertPixelsToTenthsMM(ppi: int, pixels: int, scale: float=1.0) -> int:
        ...

    @overload
    def ConvertPixelsToTenthsMM(self, dc: wx.DC, pixels: int) -> int:
        """
        ConvertPixelsToTenthsMM(dc, pixels) -> int
        ConvertPixelsToTenthsMM(ppi, pixels, scale=1.0) -> int
        
        Convert units in pixels to tenths of a millimetre.
        """

    @staticmethod
    def DrawBoxAttributes(dc: wx.DC, buffer: RichTextBuffer, attr: RichTextAttr, boxRect: wx.Rect, flags: int=0, obj: Optional[RichTextObject]=None) -> bool:
        """
        DrawBoxAttributes(dc, buffer, attr, boxRect, flags=0, obj=None) -> bool
        
        Draws the borders and background for the given rectangle and
        attributes.
        """

    @staticmethod
    def DrawBorder(dc: wx.DC, buffer: RichTextBuffer, attr: RichTextAttr, borders: TextAttrBorders, rect: wx.Rect, flags: int=0) -> bool:
        """
        DrawBorder(dc, buffer, attr, borders, rect, flags=0) -> bool
        
        Draws a border.
        """

    @staticmethod
    def GetBoxRects(dc: wx.DC, buffer: RichTextBuffer, attr: RichTextAttr) -> Tuple[bool, wx.Rect, wx.Rect, wx.Rect, wx.Rect, wx.Rect]:
        """
        GetBoxRects(dc, buffer, attr) -> Tuple[bool, wx.Rect, wx.Rect, wx.Rect, wx.Rect, wx.Rect]
        
        Returns the various rectangles of the box model in pixels.
        """

    @staticmethod
    def GetTotalMargin(dc: wx.DC, buffer: RichTextBuffer, attr: RichTextAttr) -> Tuple[bool, int, int, int, int]:
        """
        GetTotalMargin(dc, buffer, attr) -> Tuple[bool, int, int, int, int]
        
        Returns the total margin for the object in pixels, taking into account
        margin, padding and border size.
        """

    @staticmethod
    def AdjustAvailableSpace(dc: wx.DC, buffer: RichTextBuffer, parentAttr: RichTextAttr, childAttr: RichTextAttr, availableParentSpace: wx.Rect, availableContainerSpace: wx.Rect) -> wx.Rect:
        """
        AdjustAvailableSpace(dc, buffer, parentAttr, childAttr, availableParentSpace, availableContainerSpace) -> wx.Rect
        
        Returns the rectangle which the child has available to it given
        restrictions specified in the child attribute, e.g.
        """
    @property
    def AbsolutePosition(self) -> wx.Point: ...
    @property
    def Attributes(self) -> RichTextAttr: ...
    @Attributes.setter
    def Attributes(self, value: RichTextAttr, /) -> None: ...
    @property
    def BestSize(self) -> wx.Size: ...
    @property
    def BottomMargin(self) -> int: ...
    @property
    def Buffer(self) -> RichTextBuffer: ...
    @property
    def CachedSize(self) -> wx.Size: ...
    @CachedSize.setter
    def CachedSize(self, value: wx.Size, /) -> None: ...
    @property
    def Container(self) -> RichTextParagraphLayoutBox: ...
    @property
    def Descent(self) -> int: ...
    @Descent.setter
    def Descent(self, value: int, /) -> None: ...
    @property
    def FloatDirection(self) -> int: ...
    @property
    def LeftMargin(self) -> int: ...
    @property
    def MaxSize(self) -> wx.Size: ...
    @MaxSize.setter
    def MaxSize(self, value: wx.Size, /) -> None: ...
    @property
    def MinSize(self) -> wx.Size: ...
    @MinSize.setter
    def MinSize(self, value: wx.Size, /) -> None: ...
    @property
    def Name(self) -> str: ...
    @Name.setter
    def Name(self, value: str, /) -> None: ...
    @property
    def NaturalSize(self) -> TextAttrSize: ...
    @property
    def OwnRange(self) -> Union[RichTextRange, wx._TwoInts]: ...
    @OwnRange.setter
    def OwnRange(self, value: Union[RichTextRange, wx._TwoInts], /) -> None: ...
    @property
    def OwnRangeIfTopLevel(self) -> RichTextRange: ...
    @property
    def Parent(self) -> RichTextObject: ...
    @Parent.setter
    def Parent(self, value: RichTextObject, /) -> None: ...
    @property
    def ParentContainer(self) -> RichTextParagraphLayoutBox: ...
    @property
    def Position(self) -> wx.Point: ...
    @Position.setter
    def Position(self, value: wx.Point, /) -> None: ...
    @property
    def Properties(self) -> RichTextProperties: ...
    @Properties.setter
    def Properties(self, value: RichTextProperties, /) -> None: ...
    @property
    def PropertiesMenuLabel(self) -> str: ...
    @property
    def Range(self) -> Union[RichTextRange, wx._TwoInts]: ...
    @Range.setter
    def Range(self, value: Union[RichTextRange, wx._TwoInts], /) -> None: ...
    @property
    def Rect(self) -> wx.Rect: ...
    @property
    def RightMargin(self) -> int: ...
    @property
    def TopMargin(self) -> int: ...
    @property
    def XMLNodeName(self) -> str: ...
# end of class RichTextObject


class RichTextCompositeObject(RichTextObject):
    """
    RichTextCompositeObject(parent=None) -> None
    
    Objects of this class can contain other objects.
    """

    def __init__(self, parent: Optional[RichTextObject]=None) -> None:
        """
        RichTextCompositeObject(parent=None) -> None
        
        Objects of this class can contain other objects.
        """

    def HitTest(self, dc: wx.DC, context: RichTextDrawingContext, pt: wx.Point, flags: int=0) -> Tuple[int, int, RichTextObject, RichTextObject]:
        """
        HitTest(dc, context, pt, flags=0) -> Tuple[int, int, RichTextObject, RichTextObject]
        
        Hit-testing: returns a flag indicating hit test details, plus
        information about position.
        """

    def FindPosition(self, dc: wx.DC, context: RichTextDrawingContext, index: int, forceLineStart: bool) -> Tuple[bool, wx.Point, int]:
        """
        FindPosition(dc, context, index, forceLineStart) -> Tuple[bool, wx.Point, int]
        
        Finds the absolute position and row height for the given character
        position.
        """

    def CalculateRange(self, start: int) -> int:
        """
        CalculateRange(start) -> int
        
        Calculates the range of the object.
        """

    def DeleteRange(self, range: Union[RichTextRange, wx._TwoInts]) -> bool:
        """
        DeleteRange(range) -> bool
        
        Deletes the given range.
        """

    def GetTextForRange(self, range: Union[RichTextRange, wx._TwoInts]) -> str:
        """
        GetTextForRange(range) -> str
        
        Returns any text in this object for the given range.
        """

    def GetRangeSize(self, range: Union[RichTextRange, wx._TwoInts], size: wx.Size, descent: int, dc: wx.DC, context: RichTextDrawingContext, flags: int, position: wx.Point=wx.Point(0,0), parentSize: wx.Size=wx.DefaultSize, partialExtents: Optional[List[int]]=None) -> bool:
        """
        GetRangeSize(range, size, descent, dc, context, flags, position=wx.Point(0,0), parentSize=wx.DefaultSize, partialExtents=None) -> bool
        
        Returns the object size for the given range.
        """

    def Invalidate(self, invalidRange: Union[RichTextRange, wx._TwoInts]=RICHTEXT_ALL) -> None:
        """
        Invalidate(invalidRange=RICHTEXT_ALL) -> None
        
        Invalidates the object at the given range.
        """

    def GetChildren(self) -> RichTextObjectList:
        """
        GetChildren() -> RichTextObjectList
        
        Returns the children.
        """

    def GetChildCount(self) -> int:
        """
        GetChildCount() -> int
        
        Returns the number of children.
        """

    def GetChild(self, n: int) -> RichTextObject:
        """
        GetChild(n) -> RichTextObject
        
        Returns the nth child.
        """

    def IsComposite(self) -> bool:
        """
        IsComposite() -> bool
        
        Returns true if this object is composite.
        """

    def IsAtomic(self) -> bool:
        """
        IsAtomic() -> bool
        
        Returns true if no user editing can be done inside the object.
        """

    def IsEmpty(self) -> bool:
        """
        IsEmpty() -> bool
        
        Returns true if the buffer is empty.
        """

    def GetChildAtPosition(self, pos: int) -> RichTextObject:
        """
        GetChildAtPosition(pos) -> RichTextObject
        
        Returns the child object at the given character position.
        """

    def Copy(self, obj: RichTextCompositeObject) -> None:
        """
        Copy(obj) -> None
        """

    def AppendChild(self, child: RichTextObject) -> int:
        """
        AppendChild(child) -> int
        
        Appends a child, returning the position.
        """

    def InsertChild(self, child: RichTextObject, inFrontOf: RichTextObject) -> bool:
        """
        InsertChild(child, inFrontOf) -> bool
        
        Inserts the child in front of the given object, or at the beginning.
        """

    def RemoveChild(self, child: RichTextObject, deleteChild: bool=False) -> bool:
        """
        RemoveChild(child, deleteChild=False) -> bool
        
        Removes and optionally deletes the specified child.
        """

    def DeleteChildren(self) -> bool:
        """
        DeleteChildren() -> bool
        
        Deletes all the children.
        """

    def Defragment(self, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts]=RICHTEXT_ALL) -> bool:
        """
        Defragment(context, range=RICHTEXT_ALL) -> bool
        
        Recursively merges all pieces that can be merged.
        """

    def Move(self, pt: wx.Point) -> None:
        """
        Move(pt) -> None
        
        Moves the object recursively, by adding the offset from old to new.
        """
    @property
    def ChildCount(self) -> int: ...
    @property
    def Children(self) -> RichTextObjectList: ...
# end of class RichTextCompositeObject


class RichTextParagraphLayoutBox(RichTextCompositeObject):
    """
    RichTextParagraphLayoutBox(parent=None) -> None
    RichTextParagraphLayoutBox(obj) -> None
    
    This class knows how to lay out paragraphs.
    """

    @overload
    def __init__(self, obj: RichTextParagraphLayoutBox) -> None:
        ...

    @overload
    def __init__(self, parent: Optional[RichTextObject]=None) -> None:
        """
        RichTextParagraphLayoutBox(parent=None) -> None
        RichTextParagraphLayoutBox(obj) -> None
        
        This class knows how to lay out paragraphs.
        """

    def HitTest(self, dc: wx.DC, context: RichTextDrawingContext, pt: wx.Point, flags: int=0) -> Tuple[int, int, RichTextObject, RichTextObject]:
        """
        HitTest(dc, context, pt, flags=0) -> Tuple[int, int, RichTextObject, RichTextObject]
        
        Hit-testing: returns a flag indicating hit test details, plus
        information about position.
        """

    def Draw(self, dc: wx.DC, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts], selection: RichTextSelection, rect: wx.Rect, descent: int, style: int) -> bool:
        """
        Draw(dc, context, range, selection, rect, descent, style) -> bool
        
        Draw the item, within the given range.
        """

    def Layout(self, dc: wx.DC, context: RichTextDrawingContext, rect: wx.Rect, parentRect: wx.Rect, style: int) -> bool:
        """
        Layout(dc, context, rect, parentRect, style) -> bool
        
        Lay the item out at the specified position with the given size
        constraint.
        """

    def GetRangeSize(self, range: Union[RichTextRange, wx._TwoInts], size: wx.Size, descent: int, dc: wx.DC, context: RichTextDrawingContext, flags: int, position: wx.Point=wx.Point(0,0), parentSize: wx.Size=wx.DefaultSize, partialExtents: Optional[List[int]]=None) -> bool:
        """
        GetRangeSize(range, size, descent, dc, context, flags, position=wx.Point(0,0), parentSize=wx.DefaultSize, partialExtents=None) -> bool
        
        Returns the object size for the given range.
        """

    def DeleteRange(self, range: Union[RichTextRange, wx._TwoInts]) -> bool:
        """
        DeleteRange(range) -> bool
        
        Deletes the given range.
        """

    def GetTextForRange(self, range: Union[RichTextRange, wx._TwoInts]) -> str:
        """
        GetTextForRange(range) -> str
        
        Returns any text in this object for the given range.
        """

    def ImportFromXML(self, buffer: RichTextBuffer, node: XmlNode, handler: RichTextXMLHandler, recurse: bool) -> bool:
        """
        ImportFromXML(buffer, node, handler, recurse) -> bool
        
        Imports this object from XML.
        """

    def GetXMLNodeName(self) -> str:
        """
        GetXMLNodeName() -> str
        
        Returns the XML node name of this object.
        """

    def AcceptsFocus(self) -> bool:
        """
        AcceptsFocus() -> bool
        
        Returns true if objects of this class can accept the focus, i.e. a
        call to SetFocusObject is possible.
        """

    def SetRichTextCtrl(self, ctrl: RichTextCtrl) -> None:
        """
        SetRichTextCtrl(ctrl) -> None
        
        Associates a control with the buffer, for operations that for example
        require refreshing the window.
        """

    def GetRichTextCtrl(self) -> RichTextCtrl:
        """
        GetRichTextCtrl() -> RichTextCtrl
        
        Returns the associated control.
        """

    def SetPartialParagraph(self, partialPara: bool) -> None:
        """
        SetPartialParagraph(partialPara) -> None
        
        Sets a flag indicating whether the last paragraph is partial or
        complete.
        """

    def GetPartialParagraph(self) -> bool:
        """
        GetPartialParagraph() -> bool
        
        Returns a flag indicating whether the last paragraph is partial or
        complete.
        """

    def GetStyleSheet(self) -> RichTextStyleSheet:
        """
        GetStyleSheet() -> RichTextStyleSheet
        
        Returns the style sheet associated with the overall buffer.
        """

    def IsTopLevel(self) -> bool:
        """
        IsTopLevel() -> bool
        
        Returns true if this object is top-level, i.e. contains its own
        paragraphs, such as a text box.
        """

    def InsertParagraphsWithUndo(self, buffer: RichTextBuffer, pos: int, paragraphs: RichTextParagraphLayoutBox, ctrl: RichTextCtrl, flags: int=0) -> bool:
        """
        InsertParagraphsWithUndo(buffer, pos, paragraphs, ctrl, flags=0) -> bool
        
        Submits a command to insert paragraphs.
        """

    def InsertTextWithUndo(self, buffer: RichTextBuffer, pos: int, text: str, ctrl: RichTextCtrl, flags: int=0) -> bool:
        """
        InsertTextWithUndo(buffer, pos, text, ctrl, flags=0) -> bool
        
        Submits a command to insert the given text.
        """

    def InsertNewlineWithUndo(self, buffer: RichTextBuffer, pos: int, ctrl: RichTextCtrl, flags: int=0) -> bool:
        """
        InsertNewlineWithUndo(buffer, pos, ctrl, flags=0) -> bool
        
        Submits a command to insert the given text.
        """

    def InsertImageWithUndo(self, buffer: RichTextBuffer, pos: int, imageBlock: RichTextImageBlock, ctrl: RichTextCtrl, flags: int, textAttr: RichTextAttr) -> bool:
        """
        InsertImageWithUndo(buffer, pos, imageBlock, ctrl, flags, textAttr) -> bool
        
        Submits a command to insert the given image.
        """

    def InsertFieldWithUndo(self, buffer: RichTextBuffer, pos: int, fieldType: str, properties: RichTextProperties, ctrl: RichTextCtrl, flags: int, textAttr: RichTextAttr) -> RichTextField:
        """
        InsertFieldWithUndo(buffer, pos, fieldType, properties, ctrl, flags, textAttr) -> RichTextField
        
        Submits a command to insert the given field.
        """

    def GetStyleForNewParagraph(self, buffer: RichTextBuffer, pos: int, caretPosition: bool=False, lookUpNewParaStyle: bool=False) -> RichTextAttr:
        """
        GetStyleForNewParagraph(buffer, pos, caretPosition=False, lookUpNewParaStyle=False) -> RichTextAttr
        
        Returns the style that is appropriate for a new paragraph at this
        position.
        """

    def InsertObjectWithUndo(self, buffer: RichTextBuffer, pos: int, object: RichTextObject, ctrl: RichTextCtrl, flags: int=0) -> RichTextObject:
        """
        InsertObjectWithUndo(buffer, pos, object, ctrl, flags=0) -> RichTextObject
        
        Inserts an object.
        """

    def DeleteRangeWithUndo(self, range: Union[RichTextRange, wx._TwoInts], ctrl: RichTextCtrl, buffer: RichTextBuffer) -> bool:
        """
        DeleteRangeWithUndo(range, ctrl, buffer) -> bool
        
        Submits a command to delete this range.
        """

    def DrawFloats(self, dc: wx.DC, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts], selection: RichTextSelection, rect: wx.Rect, descent: int, style: int) -> None:
        """
        DrawFloats(dc, context, range, selection, rect, descent, style) -> None
        
        Draws the floating objects in this buffer.
        """

    def MoveAnchoredObjectToParagraph(self, from_: RichTextParagraph, to_: RichTextParagraph, obj: RichTextObject) -> None:
        """
        MoveAnchoredObjectToParagraph(from_, to_, obj) -> None
        
        Moves an anchored object to another paragraph.
        """

    def Init(self) -> None:
        """
        Init() -> None
        
        Initializes the object.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Clears all the children.
        """

    def Reset(self) -> None:
        """
        Reset() -> None
        
        Clears and initializes with one blank paragraph.
        """

    def AddParagraph(self, text: str, paraStyle: Optional[RichTextAttr]=None) -> RichTextRange:
        """
        AddParagraph(text, paraStyle=None) -> RichTextRange
        
        Convenience function to add a paragraph of text.
        """

    def AddImage(self, image: wx.Image, paraStyle: Optional[RichTextAttr]=None) -> RichTextRange:
        """
        AddImage(image, paraStyle=None) -> RichTextRange
        
        Convenience function to add an image.
        """

    def AddParagraphs(self, text: str, paraStyle: Optional[RichTextAttr]=None) -> RichTextRange:
        """
        AddParagraphs(text, paraStyle=None) -> RichTextRange
        
        Adds multiple paragraphs, based on newlines.
        """

    def GetLineAtPosition(self, pos: int, caretPosition: bool=False) -> RichTextLine:
        """
        GetLineAtPosition(pos, caretPosition=False) -> RichTextLine
        
        Returns the line at the given position.
        """

    def GetLineAtYPosition(self, y: int) -> RichTextLine:
        """
        GetLineAtYPosition(y) -> RichTextLine
        
        Returns the line at the given y pixel position, or the last line.
        """

    def GetParagraphAtPosition(self, pos: int, caretPosition: bool=False) -> RichTextParagraph:
        """
        GetParagraphAtPosition(pos, caretPosition=False) -> RichTextParagraph
        
        Returns the paragraph at the given character or caret position.
        """

    def GetLineSizeAtPosition(self, pos: int, caretPosition: bool=False) -> wx.Size:
        """
        GetLineSizeAtPosition(pos, caretPosition=False) -> wx.Size
        
        Returns the line size at the given position.
        """

    def GetVisibleLineNumber(self, pos: int, caretPosition: bool=False, startOfLine: bool=False) -> int:
        """
        GetVisibleLineNumber(pos, caretPosition=False, startOfLine=False) -> int
        
        Given a position, returns the number of the visible line (potentially
        many to a paragraph), starting from zero at the start of the buffer.
        """

    def GetLineForVisibleLineNumber(self, lineNumber: int) -> RichTextLine:
        """
        GetLineForVisibleLineNumber(lineNumber) -> RichTextLine
        
        Given a line number, returns the corresponding wxRichTextLine object.
        """

    def GetLeafObjectAtPosition(self, position: int) -> RichTextObject:
        """
        GetLeafObjectAtPosition(position) -> RichTextObject
        
        Returns the leaf object in a paragraph at this position.
        """

    def GetParagraphAtLine(self, paragraphNumber: int) -> RichTextParagraph:
        """
        GetParagraphAtLine(paragraphNumber) -> RichTextParagraph
        
        Returns the paragraph by number.
        """

    def GetParagraphForLine(self, line: RichTextLine) -> RichTextParagraph:
        """
        GetParagraphForLine(line) -> RichTextParagraph
        
        Returns the paragraph for a given line.
        """

    def GetParagraphLength(self, paragraphNumber: int) -> int:
        """
        GetParagraphLength(paragraphNumber) -> int
        
        Returns the length of the paragraph.
        """

    def GetParagraphCount(self) -> int:
        """
        GetParagraphCount() -> int
        
        Returns the number of paragraphs.
        """

    def GetLineCount(self) -> int:
        """
        GetLineCount() -> int
        
        Returns the number of visible lines.
        """

    def GetParagraphText(self, paragraphNumber: int) -> str:
        """
        GetParagraphText(paragraphNumber) -> str
        
        Returns the text of the paragraph.
        """

    def XYToPosition(self, x: int, y: int) -> int:
        """
        XYToPosition(x, y) -> int
        
        Converts zero-based line column and paragraph number to a position.
        """

    def PositionToXY(self, pos: int, x: int, y: int) -> bool:
        """
        PositionToXY(pos, x, y) -> bool
        
        Converts a zero-based position to line column and paragraph number.
        """

    @overload
    def SetStyle(self, obj: RichTextObject, textAttr: RichTextAttr, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO) -> None:
        ...

    @overload
    def SetStyle(self, range: Union[RichTextRange, wx._TwoInts], style: RichTextAttr, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO) -> bool:
        """
        SetStyle(range, style, flags=RICHTEXT_SETSTYLE_WITH_UNDO) -> bool
        SetStyle(obj, textAttr, flags=RICHTEXT_SETSTYLE_WITH_UNDO) -> None
        
        Sets the attributes for the given range.
        """

    def GetStyle(self, position: int, style: RichTextAttr) -> bool:
        """
        GetStyle(position, style) -> bool
        
        Returns the combined text attributes for this position.
        """

    def GetUncombinedStyle(self, position: int, style: RichTextAttr) -> bool:
        """
        GetUncombinedStyle(position, style) -> bool
        
        Returns the content (uncombined) attributes for this position.
        """

    def DoGetStyle(self, position: int, style: RichTextAttr, combineStyles: bool=True) -> bool:
        """
        DoGetStyle(position, style, combineStyles=True) -> bool
        
        Implementation helper for GetStyle.
        """

    def GetStyleForRange(self, range: Union[RichTextRange, wx._TwoInts], style: RichTextAttr) -> bool:
        """
        GetStyleForRange(range, style) -> bool
        
        This function gets a style representing the common, combined
        attributes in the given range.
        """

    def CollectStyle(self, currentStyle: RichTextAttr, style: RichTextAttr, clashingAttr: RichTextAttr, absentAttr: RichTextAttr) -> bool:
        """
        CollectStyle(currentStyle, style, clashingAttr, absentAttr) -> bool
        
        Combines style with currentStyle for the purpose of summarising the
        attributes of a range of content.
        """

    @overload
    def SetListStyle(self, range: Union[RichTextRange, wx._TwoInts], defName: str, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom: int=1, specifiedLevel: int=-1) -> bool:
        ...

    @overload
    def SetListStyle(self, range: Union[RichTextRange, wx._TwoInts], styleDef: RichTextListStyleDefinition, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom: int=1, specifiedLevel: int=-1) -> bool:
        """
        SetListStyle(range, styleDef, flags=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom=1, specifiedLevel=-1) -> bool
        SetListStyle(range, defName, flags=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom=1, specifiedLevel=-1) -> bool
        
        Sets the list attributes for the given range, passing flags to
        determine how the attributes are set.
        """

    def ClearListStyle(self, range: Union[RichTextRange, wx._TwoInts], flags: int=RICHTEXT_SETSTYLE_WITH_UNDO) -> bool:
        """
        ClearListStyle(range, flags=RICHTEXT_SETSTYLE_WITH_UNDO) -> bool
        
        Clears the list style from the given range, clearing list-related
        attributes and applying any named paragraph style associated with each
        paragraph.
        """

    @overload
    def NumberList(self, range: Union[RichTextRange, wx._TwoInts], defName: str, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom: int=1, specifiedLevel: int=-1) -> bool:
        ...

    @overload
    def NumberList(self, range: Union[RichTextRange, wx._TwoInts], _def: Optional[RichTextListStyleDefinition]=None, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom: int=1, specifiedLevel: int=-1) -> bool:
        """
        NumberList(range, _def=None, flags=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom=1, specifiedLevel=-1) -> bool
        NumberList(range, defName, flags=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom=1, specifiedLevel=-1) -> bool
        
        Numbers the paragraphs in the given range.
        """

    @overload
    def PromoteList(self, promoteBy: int, range: Union[RichTextRange, wx._TwoInts], defName: str, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, specifiedLevel: int=-1) -> bool:
        ...

    @overload
    def PromoteList(self, promoteBy: int, range: Union[RichTextRange, wx._TwoInts], _def: Optional[RichTextListStyleDefinition]=None, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, specifiedLevel: int=-1) -> bool:
        """
        PromoteList(promoteBy, range, _def=None, flags=RICHTEXT_SETSTYLE_WITH_UNDO, specifiedLevel=-1) -> bool
        PromoteList(promoteBy, range, defName, flags=RICHTEXT_SETSTYLE_WITH_UNDO, specifiedLevel=-1) -> bool
        
        Promotes the list items within the given range.
        """

    def DoNumberList(self, range: Union[RichTextRange, wx._TwoInts], promotionRange: Union[RichTextRange, wx._TwoInts], promoteBy: int, styleDef: RichTextListStyleDefinition, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom: int=1, specifiedLevel: int=-1) -> bool:
        """
        DoNumberList(range, promotionRange, promoteBy, styleDef, flags=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom=1, specifiedLevel=-1) -> bool
        
        Helper for NumberList and PromoteList, that does renumbering and
        promotion simultaneously def can be NULL/empty to indicate that the
        existing list style should be used.
        """

    def FindNextParagraphNumber(self, previousParagraph: RichTextParagraph, attr: RichTextAttr) -> bool:
        """
        FindNextParagraphNumber(previousParagraph, attr) -> bool
        
        Fills in the attributes for numbering a paragraph after
        previousParagraph.
        """

    def SetProperties(self, range: Union[RichTextRange, wx._TwoInts], properties: RichTextProperties, flags: int=RICHTEXT_SETPROPERTIES_WITH_UNDO) -> bool:
        """
        SetProperties(range, properties, flags=RICHTEXT_SETPROPERTIES_WITH_UNDO) -> bool
        
        Sets the properties for the given range, passing flags to determine
        how the attributes are set.
        """

    def SetObjectPropertiesWithUndo(self, obj: RichTextObject, properties: RichTextProperties, objToSet: Optional[RichTextObject]=None) -> bool:
        """
        SetObjectPropertiesWithUndo(obj, properties, objToSet=None) -> bool
        
        Sets with undo the properties for the given object.
        """

    def HasCharacterAttributes(self, range: Union[RichTextRange, wx._TwoInts], style: RichTextAttr) -> bool:
        """
        HasCharacterAttributes(range, style) -> bool
        
        Test if this whole range has character attributes of the specified
        kind.
        """

    def HasParagraphAttributes(self, range: Union[RichTextRange, wx._TwoInts], style: RichTextAttr) -> bool:
        """
        HasParagraphAttributes(range, style) -> bool
        
        Test if this whole range has paragraph attributes of the specified
        kind.
        """

    def Clone(self) -> RichTextObject:
        """
        Clone() -> RichTextObject
        
        Clones the object.
        """

    def PrepareContent(self, container: RichTextParagraphLayoutBox) -> None:
        """
        PrepareContent(container) -> None
        
        Prepares the content just before insertion (or after buffer reset).
        """

    def InsertFragment(self, position: int, fragment: RichTextParagraphLayoutBox) -> bool:
        """
        InsertFragment(position, fragment) -> bool
        
        Insert fragment into this box at the given position.
        """

    def CopyFragment(self, range: Union[RichTextRange, wx._TwoInts], fragment: RichTextParagraphLayoutBox) -> bool:
        """
        CopyFragment(range, fragment) -> bool
        
        Make a copy of the fragment corresponding to the given range, putting
        it in fragment.
        """

    def ApplyStyleSheet(self, styleSheet: RichTextStyleSheet) -> bool:
        """
        ApplyStyleSheet(styleSheet) -> bool
        
        Apply the style sheet to the buffer, for example if the styles have
        changed.
        """

    def Copy(self, obj: RichTextParagraphLayoutBox) -> None:
        """
        Copy(obj) -> None
        """

    def UpdateRanges(self) -> None:
        """
        UpdateRanges() -> None
        
        Calculate ranges.
        """

    def GetText(self) -> str:
        """
        GetText() -> str
        
        Get all the text.
        """

    def SetDefaultStyle(self, style: RichTextAttr) -> bool:
        """
        SetDefaultStyle(style) -> bool
        
        Sets the default style, affecting the style currently being applied
        (for example, setting the default style to bold will cause
        subsequently inserted text to be bold).
        """

    def GetDefaultStyle(self) -> RichTextAttr:
        """
        GetDefaultStyle() -> RichTextAttr
        
        Returns the current default style, affecting the style currently being
        applied (for example, setting the default style to bold will cause
        subsequently inserted text to be bold).
        """

    def SetBasicStyle(self, style: RichTextAttr) -> None:
        """
        SetBasicStyle(style) -> None
        
        Sets the basic (overall) style.
        """

    def GetBasicStyle(self) -> RichTextAttr:
        """
        GetBasicStyle() -> RichTextAttr
        
        Returns the basic (overall) style.
        """

    def Invalidate(self, invalidRange: Union[RichTextRange, wx._TwoInts]=RICHTEXT_ALL) -> None:
        """
        Invalidate(invalidRange=RICHTEXT_ALL) -> None
        
        Invalidates the buffer.
        """

    def DoInvalidate(self, invalidRange: Union[RichTextRange, wx._TwoInts]) -> None:
        """
        DoInvalidate(invalidRange) -> None
        
        Do the (in)validation for this object only.
        """

    def InvalidateHierarchy(self, invalidRange: Union[RichTextRange, wx._TwoInts]=RICHTEXT_ALL) -> None:
        """
        InvalidateHierarchy(invalidRange=RICHTEXT_ALL) -> None
        
        Do the (in)validation both up and down the hierarchy.
        """

    def UpdateFloatingObjects(self, availableRect: wx.Rect, untilObj: Optional[RichTextObject]=None) -> bool:
        """
        UpdateFloatingObjects(availableRect, untilObj=None) -> bool
        
        Gather information about floating objects.
        """

    def GetInvalidRange(self, wholeParagraphs: bool=False) -> RichTextRange:
        """
        GetInvalidRange(wholeParagraphs=False) -> RichTextRange
        
        Get invalid range, rounding to entire paragraphs if argument is true.
        """

    def IsDirty(self) -> bool:
        """
        IsDirty() -> bool
        
        Returns true if this object needs layout.
        """

    def GetFloatCollector(self) -> RichTextFloatCollector:
        """
        GetFloatCollector() -> RichTextFloatCollector
        
        Returns the wxRichTextFloatCollector of this object.
        """

    def GetFloatingObjectCount(self) -> int:
        """
        GetFloatingObjectCount() -> int
        
        Returns the number of floating objects at this level.
        """

    def GetFloatingObjects(self, objects: RichTextObjectList) -> bool:
        """
        GetFloatingObjects(objects) -> bool
        
        Returns a list of floating objects.
        """
    @property
    def BasicStyle(self) -> RichTextAttr: ...
    @BasicStyle.setter
    def BasicStyle(self, value: RichTextAttr, /) -> None: ...
    @property
    def DefaultStyle(self) -> RichTextAttr: ...
    @DefaultStyle.setter
    def DefaultStyle(self, value: RichTextAttr, /) -> None: ...
    @property
    def FloatCollector(self) -> RichTextFloatCollector: ...
    @property
    def FloatingObjectCount(self) -> int: ...
    @property
    def InvalidRange(self) -> RichTextRange: ...
    @property
    def LineCount(self) -> int: ...
    @property
    def ParagraphCount(self) -> int: ...
    @property
    def PartialParagraph(self) -> bool: ...
    @PartialParagraph.setter
    def PartialParagraph(self, value: bool, /) -> None: ...
    @property
    def RichTextCtrl(self) -> RichTextCtrl: ...
    @RichTextCtrl.setter
    def RichTextCtrl(self, value: RichTextCtrl, /) -> None: ...
    @property
    def StyleSheet(self) -> RichTextStyleSheet: ...
    @property
    def Text(self) -> str: ...
    @property
    def XMLNodeName(self) -> str: ...
# end of class RichTextParagraphLayoutBox


class RichTextBox(RichTextParagraphLayoutBox):
    """
    RichTextBox(parent=None) -> None
    RichTextBox(obj) -> None
    
    This class implements a floating or inline text box, containing
    paragraphs.
    """

    @overload
    def __init__(self, obj: RichTextBox) -> None:
        ...

    @overload
    def __init__(self, parent: Optional[RichTextObject]=None) -> None:
        """
        RichTextBox(parent=None) -> None
        RichTextBox(obj) -> None
        
        This class implements a floating or inline text box, containing
        paragraphs.
        """

    def Draw(self, dc: wx.DC, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts], selection: RichTextSelection, rect: wx.Rect, descent: int, style: int) -> bool:
        """
        Draw(dc, context, range, selection, rect, descent, style) -> bool
        
        Draw the item, within the given range.
        """

    def GetXMLNodeName(self) -> str:
        """
        GetXMLNodeName() -> str
        
        Returns the XML node name of this object.
        """

    def CanEditProperties(self) -> bool:
        """
        CanEditProperties() -> bool
        
        Returns true if we can edit the object's properties via a GUI.
        """

    def EditProperties(self, parent: wx.Window, buffer: RichTextBuffer) -> bool:
        """
        EditProperties(parent, buffer) -> bool
        
        Edits the object's properties via a GUI.
        """

    def GetPropertiesMenuLabel(self) -> str:
        """
        GetPropertiesMenuLabel() -> str
        
        Returns the label to be used for the properties context menu item.
        """

    def Clone(self) -> RichTextObject:
        """
        Clone() -> RichTextObject
        
        Clones the object.
        """

    def Copy(self, obj: RichTextBox) -> None:
        """
        Copy(obj) -> None
        """
    @property
    def PropertiesMenuLabel(self) -> str: ...
    @property
    def XMLNodeName(self) -> str: ...
# end of class RichTextBox


class RichTextField(RichTextParagraphLayoutBox):
    """
    RichTextField(fieldType='', parent=None) -> None
    RichTextField(obj) -> None
    
    This class implements the general concept of a field, an object that
    represents additional functionality such as a footnote, a bookmark, a
    page number, a table of contents, and so on.
    """

    @overload
    def __init__(self, obj: RichTextField) -> None:
        ...

    @overload
    def __init__(self, fieldType: str='', parent: Optional[RichTextObject]=None) -> None:
        """
        RichTextField(fieldType='', parent=None) -> None
        RichTextField(obj) -> None
        
        This class implements the general concept of a field, an object that
        represents additional functionality such as a footnote, a bookmark, a
        page number, a table of contents, and so on.
        """

    def Draw(self, dc: wx.DC, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts], selection: RichTextSelection, rect: wx.Rect, descent: int, style: int) -> bool:
        """
        Draw(dc, context, range, selection, rect, descent, style) -> bool
        
        Draw the item, within the given range.
        """

    def Layout(self, dc: wx.DC, context: RichTextDrawingContext, rect: wx.Rect, parentRect: wx.Rect, style: int) -> bool:
        """
        Layout(dc, context, rect, parentRect, style) -> bool
        
        Lay the item out at the specified position with the given size
        constraint.
        """

    def GetRangeSize(self, range: Union[RichTextRange, wx._TwoInts], size: wx.Size, descent: int, dc: wx.DC, context: RichTextDrawingContext, flags: int, position: wx.Point=wx.Point(0,0), parentSize: wx.Size=wx.DefaultSize, partialExtents: Optional[List[int]]=None) -> bool:
        """
        GetRangeSize(range, size, descent, dc, context, flags, position=wx.Point(0,0), parentSize=wx.DefaultSize, partialExtents=None) -> bool
        
        Returns the object size for the given range.
        """

    def GetXMLNodeName(self) -> str:
        """
        GetXMLNodeName() -> str
        
        Returns the XML node name of this object.
        """

    def CanEditProperties(self) -> bool:
        """
        CanEditProperties() -> bool
        
        Returns true if we can edit the object's properties via a GUI.
        """

    def EditProperties(self, parent: wx.Window, buffer: RichTextBuffer) -> bool:
        """
        EditProperties(parent, buffer) -> bool
        
        Edits the object's properties via a GUI.
        """

    def GetPropertiesMenuLabel(self) -> str:
        """
        GetPropertiesMenuLabel() -> str
        
        Returns the label to be used for the properties context menu item.
        """

    def AcceptsFocus(self) -> bool:
        """
        AcceptsFocus() -> bool
        
        Returns true if objects of this class can accept the focus, i.e. a
        call to SetFocusObject is possible.
        """

    def CalculateRange(self, start: int) -> int:
        """
        CalculateRange(start) -> int
        
        Calculates the range of the object.
        """

    def IsAtomic(self) -> bool:
        """
        IsAtomic() -> bool
        
        If a field has children, we don't want the user to be able to edit it.
        """

    def IsEmpty(self) -> bool:
        """
        IsEmpty() -> bool
        
        Returns true if the buffer is empty.
        """

    def IsTopLevel(self) -> bool:
        """
        IsTopLevel() -> bool
        
        Returns true if this object is top-level, i.e. contains its own
        paragraphs, such as a text box.
        """

    def SetFieldType(self, fieldType: str) -> None:
        """
        SetFieldType(fieldType) -> None
        """

    def GetFieldType(self) -> str:
        """
        GetFieldType() -> str
        """

    def UpdateField(self, buffer: RichTextBuffer) -> bool:
        """
        UpdateField(buffer) -> bool
        
        Update the field; delegated to the associated field type.
        """

    def Clone(self) -> RichTextObject:
        """
        Clone() -> RichTextObject
        
        Clones the object.
        """

    def Copy(self, obj: RichTextField) -> None:
        """
        Copy(obj) -> None
        """
    @property
    def FieldType(self) -> str: ...
    @FieldType.setter
    def FieldType(self, value: str, /) -> None: ...
    @property
    def PropertiesMenuLabel(self) -> str: ...
    @property
    def XMLNodeName(self) -> str: ...
# end of class RichTextField


class RichTextFieldType(wx.Object):
    """
    RichTextFieldType(name='') -> None
    RichTextFieldType(fieldType) -> None
    
    The base class for custom field types.
    """

    @overload
    def __init__(self, fieldType: RichTextFieldType) -> None:
        ...

    @overload
    def __init__(self, name: str='') -> None:
        """
        RichTextFieldType(name='') -> None
        RichTextFieldType(fieldType) -> None
        
        The base class for custom field types.
        """

    def Copy(self, fieldType: RichTextFieldType) -> None:
        """
        Copy(fieldType) -> None
        """

    def Draw(self, obj: RichTextField, dc: wx.DC, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts], selection: RichTextSelection, rect: wx.Rect, descent: int, style: int) -> bool:
        """
        Draw(obj, dc, context, range, selection, rect, descent, style) -> bool
        
        Draw the item, within the given range.
        """

    def Layout(self, obj: RichTextField, dc: wx.DC, context: RichTextDrawingContext, rect: wx.Rect, parentRect: wx.Rect, style: int) -> bool:
        """
        Layout(obj, dc, context, rect, parentRect, style) -> bool
        
        Lay the item out at the specified position with the given size
        constraint.
        """

    def GetRangeSize(self, obj: RichTextField, range: Union[RichTextRange, wx._TwoInts], size: wx.Size, descent: int, dc: wx.DC, context: RichTextDrawingContext, flags: int, position: wx.Point=wx.Point(0,0), parentSize: wx.Size=wx.DefaultSize, partialExtents: Optional[List[int]]=None) -> bool:
        """
        GetRangeSize(obj, range, size, descent, dc, context, flags, position=wx.Point(0,0), parentSize=wx.DefaultSize, partialExtents=None) -> bool
        
        Returns the object size for the given range.
        """

    def CanEditProperties(self, obj: RichTextField) -> bool:
        """
        CanEditProperties(obj) -> bool
        
        Returns true if we can edit the object's properties via a GUI.
        """

    def EditProperties(self, obj: RichTextField, parent: wx.Window, buffer: RichTextBuffer) -> bool:
        """
        EditProperties(obj, parent, buffer) -> bool
        
        Edits the object's properties via a GUI.
        """

    def GetPropertiesMenuLabel(self, obj: RichTextField) -> str:
        """
        GetPropertiesMenuLabel(obj) -> str
        
        Returns the label to be used for the properties context menu item.
        """

    def UpdateField(self, buffer: RichTextBuffer, obj: RichTextField) -> bool:
        """
        UpdateField(buffer, obj) -> bool
        
        Update the field.
        """

    def IsTopLevel(self, obj: RichTextField) -> bool:
        """
        IsTopLevel(obj) -> bool
        
        Returns true if this object is top-level, i.e. contains its own
        paragraphs, such as a text box.
        """

    def SetName(self, name: str) -> None:
        """
        SetName(name) -> None
        
        Sets the field type name.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns the field type name.
        """
    @property
    def Name(self) -> str: ...
    @Name.setter
    def Name(self, value: str, /) -> None: ...
# end of class RichTextFieldType


class RichTextFieldTypeStandard(RichTextFieldType):
    """
    RichTextFieldTypeStandard(name, label, displayStyle=RICHTEXT_FIELD_STYLE_RECTANGLE) -> None
    RichTextFieldTypeStandard(name, bitmap, displayStyle=RICHTEXT_FIELD_STYLE_NO_BORDER) -> None
    RichTextFieldTypeStandard() -> None
    RichTextFieldTypeStandard(field) -> None
    
    A field type that can handle fields with text or bitmap labels, with a
    small range of styles for implementing rectangular fields and fields
    that can be used for start and end tags.
    """

    class _enum_44(IntEnum):
        RICHTEXT_FIELD_STYLE_COMPOSITE = auto()
        RICHTEXT_FIELD_STYLE_RECTANGLE = auto()
        RICHTEXT_FIELD_STYLE_NO_BORDER = auto()
        RICHTEXT_FIELD_STYLE_START_TAG = auto()
        RICHTEXT_FIELD_STYLE_END_TAG = auto()
    RICHTEXT_FIELD_STYLE_COMPOSITE = _enum_44.RICHTEXT_FIELD_STYLE_COMPOSITE
    RICHTEXT_FIELD_STYLE_RECTANGLE = _enum_44.RICHTEXT_FIELD_STYLE_RECTANGLE
    RICHTEXT_FIELD_STYLE_NO_BORDER = _enum_44.RICHTEXT_FIELD_STYLE_NO_BORDER
    RICHTEXT_FIELD_STYLE_START_TAG = _enum_44.RICHTEXT_FIELD_STYLE_START_TAG
    RICHTEXT_FIELD_STYLE_END_TAG = _enum_44.RICHTEXT_FIELD_STYLE_END_TAG

    @overload
    def __init__(self, name: str, bitmap: wx.Bitmap, displayStyle: int=RICHTEXT_FIELD_STYLE_NO_BORDER) -> None:
        ...

    @overload
    def __init__(self) -> None:
        ...

    @overload
    def __init__(self, field: RichTextFieldTypeStandard) -> None:
        ...

    @overload
    def __init__(self, name: str, label: str, displayStyle: int=RICHTEXT_FIELD_STYLE_RECTANGLE) -> None:
        """
        RichTextFieldTypeStandard(name, label, displayStyle=RICHTEXT_FIELD_STYLE_RECTANGLE) -> None
        RichTextFieldTypeStandard(name, bitmap, displayStyle=RICHTEXT_FIELD_STYLE_NO_BORDER) -> None
        RichTextFieldTypeStandard() -> None
        RichTextFieldTypeStandard(field) -> None
        
        A field type that can handle fields with text or bitmap labels, with a
        small range of styles for implementing rectangular fields and fields
        that can be used for start and end tags.
        """

    def Init(self) -> None:
        """
        Init() -> None
        
        Initialises the object.
        """

    def Copy(self, field: RichTextFieldTypeStandard) -> None:
        """
        Copy(field) -> None
        
        Copies the object.
        """

    def Draw(self, obj: RichTextField, dc: wx.DC, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts], selection: RichTextSelection, rect: wx.Rect, descent: int, style: int) -> bool:
        """
        Draw(obj, dc, context, range, selection, rect, descent, style) -> bool
        
        Draw the item, within the given range.
        """

    def Layout(self, obj: RichTextField, dc: wx.DC, context: RichTextDrawingContext, rect: wx.Rect, parentRect: wx.Rect, style: int) -> bool:
        """
        Layout(obj, dc, context, rect, parentRect, style) -> bool
        
        Lay the item out at the specified position with the given size
        constraint.
        """

    def GetRangeSize(self, obj: RichTextField, range: Union[RichTextRange, wx._TwoInts], size: wx.Size, descent: int, dc: wx.DC, context: RichTextDrawingContext, flags: int, position: wx.Point=wx.Point(0,0), parentSize: wx.Size=wx.DefaultSize, partialExtents: Optional[List[int]]=None) -> bool:
        """
        GetRangeSize(obj, range, size, descent, dc, context, flags, position=wx.Point(0,0), parentSize=wx.DefaultSize, partialExtents=None) -> bool
        
        Returns the object size for the given range.
        """

    def GetSize(self, obj: RichTextField, dc: wx.DC, context: RichTextDrawingContext, style: int) -> wx.Size:
        """
        GetSize(obj, dc, context, style) -> wx.Size
        
        Get the size of the field, given the label, font size, and so on.
        """

    def IsTopLevel(self, obj: RichTextField) -> bool:
        """
        IsTopLevel(obj) -> bool
        
        Returns true if the display type is wxRICHTEXT_FIELD_STYLE_COMPOSITE,
        false otherwise.
        """

    def SetLabel(self, label: str) -> None:
        """
        SetLabel(label) -> None
        
        Sets the text label for fields of this type.
        """

    def GetLabel(self) -> str:
        """
        GetLabel() -> str
        
        Returns the text label for fields of this type.
        """

    def SetBitmap(self, bitmap: wx.Bitmap) -> None:
        """
        SetBitmap(bitmap) -> None
        
        Sets the bitmap label for fields of this type.
        """

    def GetBitmap(self) -> wx.Bitmap:
        """
        GetBitmap() -> wx.Bitmap
        
        Gets the bitmap label for fields of this type.
        """

    def GetDisplayStyle(self) -> int:
        """
        GetDisplayStyle() -> int
        
        Gets the display style for fields of this type.
        """

    def SetDisplayStyle(self, displayStyle: int) -> None:
        """
        SetDisplayStyle(displayStyle) -> None
        
        Sets the display style for fields of this type.
        """

    def GetFont(self) -> wx.Font:
        """
        GetFont() -> wx.Font
        
        Gets the font used for drawing the text label.
        """

    def SetFont(self, font: wx.Font) -> None:
        """
        SetFont(font) -> None
        
        Sets the font used for drawing the text label.
        """

    def GetTextColour(self) -> wx.Colour:
        """
        GetTextColour() -> wx.Colour
        
        Gets the colour used for drawing the text label.
        """

    def SetTextColour(self, colour: wx.Colour) -> None:
        """
        SetTextColour(colour) -> None
        
        Sets the colour used for drawing the text label.
        """

    def GetBorderColour(self) -> wx.Colour:
        """
        GetBorderColour() -> wx.Colour
        
        Gets the colour used for drawing the field border.
        """

    def SetBorderColour(self, colour: wx.Colour) -> None:
        """
        SetBorderColour(colour) -> None
        
        Sets the colour used for drawing the field border.
        """

    def GetBackgroundColour(self) -> wx.Colour:
        """
        GetBackgroundColour() -> wx.Colour
        
        Gets the colour used for drawing the field background.
        """

    def SetBackgroundColour(self, colour: wx.Colour) -> None:
        """
        SetBackgroundColour(colour) -> None
        
        Sets the colour used for drawing the field background.
        """

    def SetVerticalPadding(self, padding: int) -> None:
        """
        SetVerticalPadding(padding) -> None
        
        Sets the vertical padding (the distance between the border and the
        text).
        """

    def GetVerticalPadding(self) -> int:
        """
        GetVerticalPadding() -> int
        
        Gets the vertical padding (the distance between the border and the
        text).
        """

    def SetHorizontalPadding(self, padding: int) -> None:
        """
        SetHorizontalPadding(padding) -> None
        
        Sets the horizontal padding (the distance between the border and the
        text).
        """

    def GetHorizontalPadding(self) -> int:
        """
        GetHorizontalPadding() -> int
        
        Sets the horizontal padding (the distance between the border and the
        text).
        """

    def SetHorizontalMargin(self, margin: int) -> None:
        """
        SetHorizontalMargin(margin) -> None
        
        Sets the horizontal margin surrounding the field object.
        """

    def GetHorizontalMargin(self) -> int:
        """
        GetHorizontalMargin() -> int
        
        Gets the horizontal margin surrounding the field object.
        """

    def SetVerticalMargin(self, margin: int) -> None:
        """
        SetVerticalMargin(margin) -> None
        
        Sets the vertical margin surrounding the field object.
        """

    def GetVerticalMargin(self) -> int:
        """
        GetVerticalMargin() -> int
        
        Gets the vertical margin surrounding the field object.
        """
    @property
    def BackgroundColour(self) -> wx.Colour: ...
    @BackgroundColour.setter
    def BackgroundColour(self, value: wx.Colour, /) -> None: ...
    @property
    def Bitmap(self) -> wx.Bitmap: ...
    @Bitmap.setter
    def Bitmap(self, value: wx.Bitmap, /) -> None: ...
    @property
    def BorderColour(self) -> wx.Colour: ...
    @BorderColour.setter
    def BorderColour(self, value: wx.Colour, /) -> None: ...
    @property
    def DisplayStyle(self) -> int: ...
    @DisplayStyle.setter
    def DisplayStyle(self, value: int, /) -> None: ...
    @property
    def Font(self) -> wx.Font: ...
    @Font.setter
    def Font(self, value: wx.Font, /) -> None: ...
    @property
    def HorizontalMargin(self) -> int: ...
    @HorizontalMargin.setter
    def HorizontalMargin(self, value: int, /) -> None: ...
    @property
    def HorizontalPadding(self) -> int: ...
    @HorizontalPadding.setter
    def HorizontalPadding(self, value: int, /) -> None: ...
    @property
    def Label(self) -> str: ...
    @Label.setter
    def Label(self, value: str, /) -> None: ...
    @property
    def TextColour(self) -> wx.Colour: ...
    @TextColour.setter
    def TextColour(self, value: wx.Colour, /) -> None: ...
    @property
    def VerticalMargin(self) -> int: ...
    @VerticalMargin.setter
    def VerticalMargin(self, value: int, /) -> None: ...
    @property
    def VerticalPadding(self) -> int: ...
    @VerticalPadding.setter
    def VerticalPadding(self, value: int, /) -> None: ...
# end of class RichTextFieldTypeStandard


class RichTextLine:
    """
    RichTextLine(parent) -> None
    RichTextLine(obj) -> None
    
    This object represents a line in a paragraph, and stores offsets from
    the start of the paragraph representing the start and end positions of
    the line.
    """

    @overload
    def __init__(self, obj: RichTextLine) -> None:
        ...

    @overload
    def __init__(self, parent: RichTextParagraph) -> None:
        """
        RichTextLine(parent) -> None
        RichTextLine(obj) -> None
        
        This object represents a line in a paragraph, and stores offsets from
        the start of the paragraph representing the start and end positions of
        the line.
        """

    @overload
    def SetRange(self, from_: int, to_: int) -> None:
        ...

    @overload
    def SetRange(self, range: Union[RichTextRange, wx._TwoInts]) -> None:
        """
        SetRange(range) -> None
        SetRange(from_, to_) -> None
        
        Sets the range associated with this line.
        """

    def GetParent(self) -> RichTextParagraph:
        """
        GetParent() -> RichTextParagraph
        
        Returns the parent paragraph.
        """

    def GetRange(self) -> RichTextRange:
        """
        GetRange() -> RichTextRange
        
        Returns the range.
        """

    def GetAbsoluteRange(self) -> RichTextRange:
        """
        GetAbsoluteRange() -> RichTextRange
        
        Returns the absolute range.
        """

    def GetSize(self) -> wx.Size:
        """
        GetSize() -> wx.Size
        
        Returns the line size as calculated by Layout.
        """

    def SetSize(self, sz: wx.Size) -> None:
        """
        SetSize(sz) -> None
        
        Sets the line size as calculated by Layout.
        """

    def GetPosition(self) -> wx.Point:
        """
        GetPosition() -> wx.Point
        
        Returns the object position relative to the parent.
        """

    def SetPosition(self, pos: wx.Point) -> None:
        """
        SetPosition(pos) -> None
        
        Sets the object position relative to the parent.
        """

    def GetAbsolutePosition(self) -> wx.Point:
        """
        GetAbsolutePosition() -> wx.Point
        
        Returns the absolute object position.
        """

    def GetRect(self) -> wx.Rect:
        """
        GetRect() -> wx.Rect
        
        Returns the rectangle enclosing the line.
        """

    def SetDescent(self, descent: int) -> None:
        """
        SetDescent(descent) -> None
        
        Sets the stored descent.
        """

    def GetDescent(self) -> int:
        """
        GetDescent() -> int
        
        Returns the stored descent.
        """

    def Init(self, parent: RichTextParagraph) -> None:
        """
        Init(parent) -> None
        
        Initialises the object.
        """

    def Copy(self, obj: RichTextLine) -> None:
        """
        Copy(obj) -> None
        
        Copies from obj.
        """

    def Clone(self) -> RichTextLine:
        """
        Clone() -> RichTextLine
        """
    @property
    def AbsolutePosition(self) -> wx.Point: ...
    @property
    def AbsoluteRange(self) -> RichTextRange: ...
    @property
    def Descent(self) -> int: ...
    @Descent.setter
    def Descent(self, value: int, /) -> None: ...
    @property
    def Parent(self) -> RichTextParagraph: ...
    @property
    def Position(self) -> wx.Point: ...
    @Position.setter
    def Position(self, value: wx.Point, /) -> None: ...
    @property
    def Range(self) -> Union[RichTextRange, wx._TwoInts]: ...
    @Range.setter
    def Range(self, value: Union[RichTextRange, wx._TwoInts], /) -> None: ...
    @property
    def Rect(self) -> wx.Rect: ...
    @property
    def Size(self) -> wx.Size: ...
    @Size.setter
    def Size(self, value: wx.Size, /) -> None: ...
# end of class RichTextLine


class RichTextParagraph(RichTextCompositeObject):
    """
    RichTextParagraph(parent=None, style=None) -> None
    RichTextParagraph(text, parent=None, paraStyle=None, charStyle=None) -> None
    RichTextParagraph(obj) -> None
    
    This object represents a single paragraph containing various objects
    such as text content, images, and further paragraph layout objects.
    """

    @overload
    def __init__(self, text: str, parent: Optional[RichTextObject]=None, paraStyle: Optional[RichTextAttr]=None, charStyle: Optional[RichTextAttr]=None) -> None:
        ...

    @overload
    def __init__(self, obj: RichTextParagraph) -> None:
        ...

    @overload
    def __init__(self, parent: Optional[RichTextObject]=None, style: Optional[RichTextAttr]=None) -> None:
        """
        RichTextParagraph(parent=None, style=None) -> None
        RichTextParagraph(text, parent=None, paraStyle=None, charStyle=None) -> None
        RichTextParagraph(obj) -> None
        
        This object represents a single paragraph containing various objects
        such as text content, images, and further paragraph layout objects.
        """

    def Init(self) -> None:
        """
        Init() -> None
        """

    def Draw(self, dc: wx.DC, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts], selection: RichTextSelection, rect: wx.Rect, descent: int, style: int) -> bool:
        """
        Draw(dc, context, range, selection, rect, descent, style) -> bool
        
        Draw the item, within the given range.
        """

    def Layout(self, dc: wx.DC, context: RichTextDrawingContext, rect: wx.Rect, parentRect: wx.Rect, style: int) -> bool:
        """
        Layout(dc, context, rect, parentRect, style) -> bool
        
        Lay the item out at the specified position with the given size
        constraint.
        """

    def GetRangeSize(self, range: Union[RichTextRange, wx._TwoInts], size: wx.Size, descent: int, dc: wx.DC, context: RichTextDrawingContext, flags: int, position: wx.Point=wx.Point(0,0), parentSize: wx.Size=wx.DefaultSize, partialExtents: Optional[List[int]]=None) -> bool:
        """
        GetRangeSize(range, size, descent, dc, context, flags, position=wx.Point(0,0), parentSize=wx.DefaultSize, partialExtents=None) -> bool
        
        Returns the object size for the given range.
        """

    def FindPosition(self, dc: wx.DC, context: RichTextDrawingContext, index: int, forceLineStart: bool) -> Tuple[bool, wx.Point, int]:
        """
        FindPosition(dc, context, index, forceLineStart) -> Tuple[bool, wx.Point, int]
        
        Finds the absolute position and row height for the given character
        position.
        """

    def HitTest(self, dc: wx.DC, context: RichTextDrawingContext, pt: wx.Point, flags: int=0) -> Tuple[int, int, RichTextObject, RichTextObject]:
        """
        HitTest(dc, context, pt, flags=0) -> Tuple[int, int, RichTextObject, RichTextObject]
        
        Hit-testing: returns a flag indicating hit test details, plus
        information about position.
        """

    def CalculateRange(self, start: int) -> int:
        """
        CalculateRange(start) -> int
        
        Calculates the range of the object.
        """

    def GetXMLNodeName(self) -> str:
        """
        GetXMLNodeName() -> str
        
        Returns the XML node name of this object.
        """

    def GetLines(self) -> Any:
        """
        GetLines() -> Any
        
        Returns the cached lines.
        """

    def Copy(self, obj: RichTextParagraph) -> None:
        """
        Copy(obj) -> None
        
        Copies the object.
        """

    def Clone(self) -> RichTextObject:
        """
        Clone() -> RichTextObject
        
        Clones the object.
        """

    def ClearLines(self) -> None:
        """
        ClearLines() -> None
        
        Clears the cached lines.
        """

    def ApplyParagraphStyle(self, line: RichTextLine, attr: RichTextAttr, rect: wx.Rect, dc: wx.DC) -> None:
        """
        ApplyParagraphStyle(line, attr, rect, dc) -> None
        
        Applies paragraph styles such as centering to the wrapped lines.
        """

    def InsertText(self, pos: int, text: str) -> bool:
        """
        InsertText(pos, text) -> bool
        
        Inserts text at the given position.
        """

    def SplitAt(self, pos: int, previousObject: Optional[RichTextObject]=None) -> RichTextObject:
        """
        SplitAt(pos, previousObject=None) -> RichTextObject
        
        Splits an object at this position if necessary, and returns the
        previous object, or NULL if inserting at the beginning.
        """

    def MoveToList(self, obj: RichTextObject, list: RichTextObjectList_) -> None:
        """
        MoveToList(obj, list) -> None
        
        Moves content to a list from this point.
        """

    def MoveFromList(self, list: RichTextObjectList_) -> None:
        """
        MoveFromList(list) -> None
        
        Adds content back from a list.
        """

    def GetContiguousPlainText(self, text: str, range: Union[RichTextRange, wx._TwoInts], fromStart: bool=True) -> bool:
        """
        GetContiguousPlainText(text, range, fromStart=True) -> bool
        
        Returns the plain text searching from the start or end of the range.
        """

    def FindWrapPosition(self, range: Union[RichTextRange, wx._TwoInts], dc: wx.DC, context: RichTextDrawingContext, availableSpace: int, wrapPosition: int, partialExtents: List[int]) -> bool:
        """
        FindWrapPosition(range, dc, context, availableSpace, wrapPosition, partialExtents) -> bool
        
        Finds a suitable wrap position.
        """

    def FindObjectAtPosition(self, position: int) -> RichTextObject:
        """
        FindObjectAtPosition(position) -> RichTextObject
        
        Finds the object at the given position.
        """

    def GetBulletText(self) -> str:
        """
        GetBulletText() -> str
        
        Returns the bullet text for this paragraph.
        """

    def AllocateLine(self, pos: int) -> RichTextLine:
        """
        AllocateLine(pos) -> RichTextLine
        
        Allocates or reuses a line object.
        """

    def ClearUnusedLines(self, lineCount: int) -> bool:
        """
        ClearUnusedLines(lineCount) -> bool
        
        Clears remaining unused line objects, if any.
        """

    @overload
    def GetCombinedAttributes(self, includingBoxAttr: bool=False) -> RichTextAttr:
        ...

    @overload
    def GetCombinedAttributes(self, contentStyle: RichTextAttr, includingBoxAttr: bool=False) -> RichTextAttr:
        """
        GetCombinedAttributes(contentStyle, includingBoxAttr=False) -> RichTextAttr
        GetCombinedAttributes(includingBoxAttr=False) -> RichTextAttr
        
        Returns combined attributes of the base style, paragraph style and
        character style.
        """

    def GetFirstLineBreakPosition(self, pos: int) -> int:
        """
        GetFirstLineBreakPosition(pos) -> int
        
        Returns the first position from pos that has a line break character.
        """

    def LayoutFloat(self, dc: wx.DC, context: RichTextDrawingContext, rect: wx.Rect, parentRect: wx.Rect, style: int, floatCollector: RichTextFloatCollector) -> None:
        """
        LayoutFloat(dc, context, rect, parentRect, style, floatCollector) -> None
        
        Lays out the floating objects.
        """

    def GetImpactedByFloatingObjects(self) -> int:
        """
        GetImpactedByFloatingObjects() -> int
        
        Whether the paragraph is impacted by floating objects from above.
        """

    def SetImpactedByFloatingObjects(self, i: int) -> None:
        """
        SetImpactedByFloatingObjects(i) -> None
        
        Sets whether the paragraph is impacted by floating objects from above.
        """

    @staticmethod
    def InitDefaultTabs() -> None:
        """
        InitDefaultTabs() -> None
        
        Creates a default tabstop array.
        """

    @staticmethod
    def ClearDefaultTabs() -> None:
        """
        ClearDefaultTabs() -> None
        
        Clears the default tabstop array.
        """

    @staticmethod
    def GetDefaultTabs() -> List[int]:
        """
        GetDefaultTabs() -> List[int]
        
        Returns the default tabstop array.
        """
    @property
    def BulletText(self) -> str: ...
    @property
    def CombinedAttributes(self) -> RichTextAttr: ...
    @property
    def ImpactedByFloatingObjects(self) -> int: ...
    @ImpactedByFloatingObjects.setter
    def ImpactedByFloatingObjects(self, value: int, /) -> None: ...
    @property
    def Lines(self) -> Any: ...
    @property
    def XMLNodeName(self) -> str: ...
# end of class RichTextParagraph


class RichTextPlainText(RichTextObject):
    """
    RichTextPlainText(text='', parent=None, style=None) -> None
    RichTextPlainText(obj) -> None
    
    This object represents a single piece of text.
    """

    @overload
    def __init__(self, obj: RichTextPlainText) -> None:
        ...

    @overload
    def __init__(self, text: str='', parent: Optional[RichTextObject]=None, style: Optional[RichTextAttr]=None) -> None:
        """
        RichTextPlainText(text='', parent=None, style=None) -> None
        RichTextPlainText(obj) -> None
        
        This object represents a single piece of text.
        """

    def Draw(self, dc: wx.DC, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts], selection: RichTextSelection, rect: wx.Rect, descent: int, style: int) -> bool:
        """
        Draw(dc, context, range, selection, rect, descent, style) -> bool
        
        Draw the item, within the given range.
        """

    def Layout(self, dc: wx.DC, context: RichTextDrawingContext, rect: wx.Rect, parentRect: wx.Rect, style: int) -> bool:
        """
        Layout(dc, context, rect, parentRect, style) -> bool
        
        Lay the item out at the specified position with the given size
        constraint.
        """

    def GetRangeSize(self, range: Union[RichTextRange, wx._TwoInts], size: wx.Size, descent: int, dc: wx.DC, context: RichTextDrawingContext, flags: int, position: wx.Point=wx.Point(0,0), parentSize: wx.Size=wx.DefaultSize, partialExtents: Optional[List[int]]=None) -> bool:
        """
        GetRangeSize(range, size, descent, dc, context, flags, position=wx.Point(0,0), parentSize=wx.DefaultSize, partialExtents=None) -> bool
        
        Returns the object size for the given range.
        """

    def GetTextForRange(self, range: Union[RichTextRange, wx._TwoInts]) -> str:
        """
        GetTextForRange(range) -> str
        
        Returns any text in this object for the given range.
        """

    def DoSplit(self, pos: int) -> RichTextObject:
        """
        DoSplit(pos) -> RichTextObject
        
        Do a split from pos, returning an object containing the second part,
        and setting the first part in 'this'.
        """

    def CalculateRange(self, start: int) -> int:
        """
        CalculateRange(start) -> int
        
        Calculates the range of the object.
        """

    def DeleteRange(self, range: Union[RichTextRange, wx._TwoInts]) -> bool:
        """
        DeleteRange(range) -> bool
        
        Deletes the given range.
        """

    def IsEmpty(self) -> bool:
        """
        IsEmpty() -> bool
        
        Returns true if the object is empty.
        """

    def CanMerge(self, object: RichTextObject, context: RichTextDrawingContext) -> bool:
        """
        CanMerge(object, context) -> bool
        
        Returns true if this object can merge itself with the given one.
        """

    def Merge(self, object: RichTextObject, context: RichTextDrawingContext) -> bool:
        """
        Merge(object, context) -> bool
        
        Returns true if this object merged itself with the given one.
        """

    def CanSplit(self, context: RichTextDrawingContext) -> bool:
        """
        CanSplit(context) -> bool
        
        Returns true if this object can potentially be split, by virtue of
        having different virtual attributes for individual sub-objects.
        """

    def Split(self, context: RichTextDrawingContext) -> RichTextObject:
        """
        Split(context) -> RichTextObject
        
        Returns the final object in the split objects if this object was split
        due to differences between sub-object virtual attributes.
        """

    def GetFirstLineBreakPosition(self, pos: int) -> int:
        """
        GetFirstLineBreakPosition(pos) -> int
        
        Get the first position from pos that has a line break character.
        """

    def UsesParagraphAttributes(self) -> bool:
        """
        UsesParagraphAttributes() -> bool
        
        Does this object take note of paragraph attributes? Text and image
        objects don't.
        """

    def ImportFromXML(self, buffer: RichTextBuffer, node: XmlNode, handler: RichTextXMLHandler, recurse: bool) -> bool:
        """
        ImportFromXML(buffer, node, handler, recurse) -> bool
        
        Imports this object from XML.
        """

    def GetXMLNodeName(self) -> str:
        """
        GetXMLNodeName() -> str
        
        Returns the XML node name of this object.
        """

    def GetText(self) -> str:
        """
        GetText() -> str
        
        Returns the text.
        """

    def SetText(self, text: str) -> None:
        """
        SetText(text) -> None
        
        Sets the text.
        """

    def Copy(self, obj: RichTextPlainText) -> None:
        """
        Copy(obj) -> None
        """

    def Clone(self) -> RichTextObject:
        """
        Clone() -> RichTextObject
        
        Clones the object.
        """
    @property
    def Text(self) -> str: ...
    @Text.setter
    def Text(self, value: str, /) -> None: ...
    @property
    def XMLNodeName(self) -> str: ...
# end of class RichTextPlainText


class RichTextImageBlock(wx.Object):
    """
    RichTextImageBlock() -> None
    RichTextImageBlock(block) -> None
    
    This class stores information about an image, in binary in-memory
    form.
    """

    @overload
    def __init__(self, block: RichTextImageBlock) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RichTextImageBlock() -> None
        RichTextImageBlock(block) -> None
        
        This class stores information about an image, in binary in-memory
        form.
        """

    def Init(self) -> None:
        """
        Init() -> None
        
        Initialises the block.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Clears the block.
        """

    @overload
    def MakeImageBlock(self, image: wx.Image, imageType: wx.BitmapType, quality: int=80) -> bool:
        ...

    @overload
    def MakeImageBlock(self, filename: str, imageType: wx.BitmapType, image: wx.Image, convertToJPEG: bool=True) -> bool:
        """
        MakeImageBlock(filename, imageType, image, convertToJPEG=True) -> bool
        MakeImageBlock(image, imageType, quality=80) -> bool
        
        Load the original image into a memory block.
        """

    def MakeImageBlockDefaultQuality(self, image: wx.Image, imageType: wx.BitmapType) -> bool:
        """
        MakeImageBlockDefaultQuality(image, imageType) -> bool
        
        Uses a const wxImage for efficiency, but can't set quality (only
        relevant for JPEG)
        """

    def DoMakeImageBlock(self, image: wx.Image, imageType: wx.BitmapType) -> bool:
        """
        DoMakeImageBlock(image, imageType) -> bool
        
        Makes the image block.
        """

    def Write(self, filename: str) -> bool:
        """
        Write(filename) -> bool
        
        Writes the block to a file.
        """

    def WriteHex(self, stream: wx.OutputStream) -> bool:
        """
        WriteHex(stream) -> bool
        
        Writes the data in hex to a stream.
        """

    def ReadHex(self, stream: wx.InputStream, length: int, imageType: wx.BitmapType) -> bool:
        """
        ReadHex(stream, length, imageType) -> bool
        
        Reads the data in hex from a stream.
        """

    def Copy(self, block: RichTextImageBlock) -> None:
        """
        Copy(block) -> None
        
        Copy from block.
        """

    def Load(self, image: wx.Image) -> bool:
        """
        Load(image) -> bool
        """

    def GetData(self) -> int:
        """
        GetData() -> int
        
        Returns the raw data.
        """

    def GetDataSize(self) -> int:
        """
        GetDataSize() -> int
        
        Returns the data size in bytes.
        """

    def GetImageType(self) -> wx.BitmapType:
        """
        GetImageType() -> wx.BitmapType
        
        Returns the image type.
        """

    def SetData(self, image: int) -> None:
        """
        SetData(image) -> None
        """

    def SetDataSize(self, size: int) -> None:
        """
        SetDataSize(size) -> None
        
        Sets the data size.
        """

    def SetImageType(self, imageType: wx.BitmapType) -> None:
        """
        SetImageType(imageType) -> None
        
        Sets the image type.
        """

    def IsOk(self) -> bool:
        """
        IsOk() -> bool
        
        Returns true if the data is non-NULL.
        """

    def Ok(self) -> bool:
        """
        Ok() -> bool
        """

    def GetExtension(self) -> str:
        """
        GetExtension() -> str
        
        Gets the extension for the block's type.
        """

    @overload
    @staticmethod
    def ReadBlock(filename: str, size: int) -> int:
        ...

    @overload
    @staticmethod
    def ReadBlock(stream: wx.InputStream, size: int) -> int:
        """
        ReadBlock(stream, size) -> int
        ReadBlock(filename, size) -> int
        
        Implementation.
        """

    @overload
    @staticmethod
    def WriteBlock(filename: str, block: int, size: int) -> bool:
        ...

    @overload
    @staticmethod
    def WriteBlock(stream: wx.OutputStream, block: int, size: int) -> bool:
        """
        WriteBlock(stream, block, size) -> bool
        WriteBlock(filename, block, size) -> bool
        
        Writes a memory block to stream.
        """
    @property
    def Data(self) -> int: ...
    @Data.setter
    def Data(self, value: int, /) -> None: ...
    @property
    def DataSize(self) -> int: ...
    @DataSize.setter
    def DataSize(self, value: int, /) -> None: ...
    @property
    def Extension(self) -> str: ...
    @property
    def ImageType(self) -> wx.BitmapType: ...
    @ImageType.setter
    def ImageType(self, value: wx.BitmapType, /) -> None: ...
# end of class RichTextImageBlock


class RichTextImage(RichTextObject):
    """
    RichTextImage(parent=None) -> None
    RichTextImage(image, parent=None, charStyle=None) -> None
    RichTextImage(imageBlock, parent=None, charStyle=None) -> None
    RichTextImage(obj) -> None
    
    This class implements a graphic object.
    """

    @overload
    def __init__(self, image: wx.Image, parent: Optional[RichTextObject]=None, charStyle: Optional[RichTextAttr]=None) -> None:
        ...

    @overload
    def __init__(self, imageBlock: RichTextImageBlock, parent: Optional[RichTextObject]=None, charStyle: Optional[RichTextAttr]=None) -> None:
        ...

    @overload
    def __init__(self, obj: RichTextImage) -> None:
        ...

    @overload
    def __init__(self, parent: Optional[RichTextObject]=None) -> None:
        """
        RichTextImage(parent=None) -> None
        RichTextImage(image, parent=None, charStyle=None) -> None
        RichTextImage(imageBlock, parent=None, charStyle=None) -> None
        RichTextImage(obj) -> None
        
        This class implements a graphic object.
        """

    def Draw(self, dc: wx.DC, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts], selection: RichTextSelection, rect: wx.Rect, descent: int, style: int) -> bool:
        """
        Draw(dc, context, range, selection, rect, descent, style) -> bool
        
        Draw the item, within the given range.
        """

    def Layout(self, dc: wx.DC, context: RichTextDrawingContext, rect: wx.Rect, parentRect: wx.Rect, style: int) -> bool:
        """
        Layout(dc, context, rect, parentRect, style) -> bool
        
        Lay the item out at the specified position with the given size
        constraint.
        """

    def GetRangeSize(self, range: Union[RichTextRange, wx._TwoInts], size: wx.Size, descent: int, dc: wx.DC, context: RichTextDrawingContext, flags: int, position: wx.Point=wx.Point(0,0), parentSize: wx.Size=wx.DefaultSize, partialExtents: Optional[List[int]]=None) -> bool:
        """
        GetRangeSize(range, size, descent, dc, context, flags, position=wx.Point(0,0), parentSize=wx.DefaultSize, partialExtents=None) -> bool
        
        Returns the object size for the given range.
        """

    def GetNaturalSize(self) -> TextAttrSize:
        """
        GetNaturalSize() -> TextAttrSize
        
        Returns the 'natural' size for this object - the image size.
        """

    def IsEmpty(self) -> bool:
        """
        IsEmpty() -> bool
        
        Returns true if the object is empty.
        """

    def CanEditProperties(self) -> bool:
        """
        CanEditProperties() -> bool
        
        Returns true if we can edit the object's properties via a GUI.
        """

    def EditProperties(self, parent: wx.Window, buffer: RichTextBuffer) -> bool:
        """
        EditProperties(parent, buffer) -> bool
        
        Edits the object's properties via a GUI.
        """

    def GetPropertiesMenuLabel(self) -> str:
        """
        GetPropertiesMenuLabel() -> str
        
        Returns the label to be used for the properties context menu item.
        """

    def UsesParagraphAttributes(self) -> bool:
        """
        UsesParagraphAttributes() -> bool
        
        Returns true if this object takes note of paragraph attributes (text
        and image objects don't).
        """

    def ImportFromXML(self, buffer: RichTextBuffer, node: XmlNode, handler: RichTextXMLHandler, recurse: bool) -> bool:
        """
        ImportFromXML(buffer, node, handler, recurse) -> bool
        
        Imports this object from XML.
        """

    def IsFloatable(self) -> bool:
        """
        IsFloatable() -> bool
        
        Returns true if this class of object is floatable.
        """

    def GetXMLNodeName(self) -> str:
        """
        GetXMLNodeName() -> str
        
        Returns the XML node name of this object.
        """

    def GetImageCache(self) -> wx.Bitmap:
        """
        GetImageCache() -> wx.Bitmap
        
        Returns the image cache (a scaled bitmap).
        """

    def SetImageCache(self, bitmap: wx.Bitmap) -> None:
        """
        SetImageCache(bitmap) -> None
        
        Sets the image cache.
        """

    def ResetImageCache(self) -> None:
        """
        ResetImageCache() -> None
        
        Resets the image cache.
        """

    def GetImageBlock(self) -> RichTextImageBlock:
        """
        GetImageBlock() -> RichTextImageBlock
        
        Returns the image block containing the raw data.
        """

    def GetOriginalImageSize(self) -> wx.Size:
        """
        GetOriginalImageSize() -> wx.Size
        
        Gets the original image size.
        """

    def SetOriginalImageSize(self, sz: wx.Size) -> None:
        """
        SetOriginalImageSize(sz) -> None
        
        Sets the original image size.
        """

    def Copy(self, obj: RichTextImage) -> None:
        """
        Copy(obj) -> None
        
        Copies the image object.
        """

    def Clone(self) -> RichTextObject:
        """
        Clone() -> RichTextObject
        
        Clones the image object.
        """

    def LoadImageCache(self, dc: wx.DC, context: RichTextDrawingContext, retImageSize: wx.Size, resetCache: bool=False, parentSize: wx.Size=wx.DefaultSize) -> bool:
        """
        LoadImageCache(dc, context, retImageSize, resetCache=False, parentSize=wx.DefaultSize) -> bool
        
        Creates a cached image at the required size.
        """

    def LoadAndScaleImageCache(self, image: wx.Image, sz: wx.Size, context: RichTextDrawingContext, changed: bool) -> Tuple[bool, bool]:
        """
        LoadAndScaleImageCache(image, sz, context, changed) -> Tuple[bool, bool]
        
        Do the loading and scaling.
        """

    def GetImageState(self) -> int:
        """
        GetImageState() -> int
        
        Gets the image state.
        """

    def SetImageState(self, state: int) -> None:
        """
        SetImageState(state) -> None
        
        Sets the image state.
        """
    @property
    def ImageBlock(self) -> RichTextImageBlock: ...
    @property
    def ImageCache(self) -> wx.Bitmap: ...
    @ImageCache.setter
    def ImageCache(self, value: wx.Bitmap, /) -> None: ...
    @property
    def ImageState(self) -> int: ...
    @ImageState.setter
    def ImageState(self, value: int, /) -> None: ...
    @property
    def NaturalSize(self) -> TextAttrSize: ...
    @property
    def OriginalImageSize(self) -> wx.Size: ...
    @OriginalImageSize.setter
    def OriginalImageSize(self, value: wx.Size, /) -> None: ...
    @property
    def PropertiesMenuLabel(self) -> str: ...
    @property
    def XMLNodeName(self) -> str: ...
# end of class RichTextImage


class RichTextBuffer(RichTextParagraphLayoutBox):
    """
    RichTextBuffer() -> None
    RichTextBuffer(obj) -> None
    
    This is a kind of paragraph layout box, used to represent the whole
    buffer.
    """

    @overload
    def __init__(self, obj: RichTextBuffer) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RichTextBuffer() -> None
        RichTextBuffer(obj) -> None
        
        This is a kind of paragraph layout box, used to represent the whole
        buffer.
        """

    def GetCommandProcessor(self) -> wx.CommandProcessor:
        """
        GetCommandProcessor() -> wx.CommandProcessor
        
        Returns the command processor.
        """

    def SetStyleSheet(self, styleSheet: RichTextStyleSheet) -> None:
        """
        SetStyleSheet(styleSheet) -> None
        
        Sets style sheet, if any.
        """

    def GetStyleSheet(self) -> RichTextStyleSheet:
        """
        GetStyleSheet() -> RichTextStyleSheet
        
        Returns the style sheet.
        """

    def SetStyleSheetAndNotify(self, sheet: RichTextStyleSheet) -> bool:
        """
        SetStyleSheetAndNotify(sheet) -> bool
        
        Sets the style sheet and sends a notification of the change.
        """

    def PushStyleSheet(self, styleSheet: RichTextStyleSheet) -> bool:
        """
        PushStyleSheet(styleSheet) -> bool
        
        Pushes the style sheet to the top of the style sheet stack.
        """

    def PopStyleSheet(self) -> RichTextStyleSheet:
        """
        PopStyleSheet() -> RichTextStyleSheet
        
        Pops the style sheet from the top of the style sheet stack.
        """

    def GetFontTable(self) -> RichTextFontTable:
        """
        GetFontTable() -> RichTextFontTable
        
        Returns the table storing fonts, for quick access and font reuse.
        """

    def SetFontTable(self, table: RichTextFontTable) -> None:
        """
        SetFontTable(table) -> None
        
        Sets table storing fonts, for quick access and font reuse.
        """

    def SetFontScale(self, fontScale: float) -> None:
        """
        SetFontScale(fontScale) -> None
        
        Sets the scale factor for displaying fonts, for example for more
        comfortable editing.
        """

    def GetFontScale(self) -> float:
        """
        GetFontScale() -> float
        
        Returns the scale factor for displaying fonts, for example for more
        comfortable editing.
        """

    def SetDimensionScale(self, dimScale: float) -> None:
        """
        SetDimensionScale(dimScale) -> None
        
        Sets the scale factor for displaying certain dimensions such as
        indentation and inter-paragraph spacing.
        """

    def GetDimensionScale(self) -> float:
        """
        GetDimensionScale() -> float
        
        Returns the scale factor for displaying certain dimensions such as
        indentation and inter-paragraph spacing.
        """

    def Init(self) -> None:
        """
        Init() -> None
        
        Initialisation.
        """

    def ResetAndClearCommands(self) -> None:
        """
        ResetAndClearCommands() -> None
        
        Clears the buffer, adds an empty paragraph, and clears the command
        processor.
        """

    @overload
    def LoadFile(self, stream: wx.InputStream, type: RichTextFileType=RICHTEXT_TYPE_ANY) -> bool:
        ...

    @overload
    def LoadFile(self, filename: str, type: RichTextFileType=RICHTEXT_TYPE_ANY) -> bool:
        """
        LoadFile(filename, type=RICHTEXT_TYPE_ANY) -> bool
        LoadFile(stream, type=RICHTEXT_TYPE_ANY) -> bool
        
        Loads content from a stream or file.
        """

    @overload
    def SaveFile(self, stream: wx.OutputStream, type: RichTextFileType=RICHTEXT_TYPE_ANY) -> bool:
        ...

    @overload
    def SaveFile(self, filename: str, type: RichTextFileType=RICHTEXT_TYPE_ANY) -> bool:
        """
        SaveFile(filename, type=RICHTEXT_TYPE_ANY) -> bool
        SaveFile(stream, type=RICHTEXT_TYPE_ANY) -> bool
        
        Saves content to a stream or file.
        """

    def SetHandlerFlags(self, flags: int) -> None:
        """
        SetHandlerFlags(flags) -> None
        
        Sets the handler flags, controlling loading and saving.
        """

    def GetHandlerFlags(self) -> int:
        """
        GetHandlerFlags() -> int
        
        Gets the handler flags, controlling loading and saving.
        """

    def AddParagraph(self, text: str, paraStyle: Optional[RichTextAttr]=None) -> RichTextRange:
        """
        AddParagraph(text, paraStyle=None) -> RichTextRange
        
        Convenience function to add a paragraph of text.
        """

    def BeginBatchUndo(self, cmdName: str) -> bool:
        """
        BeginBatchUndo(cmdName) -> bool
        
        Begin collapsing undo/redo commands.
        """

    def EndBatchUndo(self) -> bool:
        """
        EndBatchUndo() -> bool
        
        End collapsing undo/redo commands.
        """

    def BatchingUndo(self) -> bool:
        """
        BatchingUndo() -> bool
        
        Returns true if we are collapsing commands.
        """

    def SubmitAction(self, action: RichTextAction) -> bool:
        """
        SubmitAction(action) -> bool
        
        Submit the action immediately, or delay according to whether
        collapsing is on.
        """

    def GetBatchedCommand(self) -> RichTextCommand:
        """
        GetBatchedCommand() -> RichTextCommand
        
        Returns the collapsed command.
        """

    def BeginSuppressUndo(self) -> bool:
        """
        BeginSuppressUndo() -> bool
        
        Begin suppressing undo/redo commands.
        """

    def EndSuppressUndo(self) -> bool:
        """
        EndSuppressUndo() -> bool
        
        End suppressing undo/redo commands.
        """

    def SuppressingUndo(self) -> bool:
        """
        SuppressingUndo() -> bool
        
        Are we suppressing undo??
        """

    def CopyToClipboard(self, range: Union[RichTextRange, wx._TwoInts]) -> bool:
        """
        CopyToClipboard(range) -> bool
        
        Copy the range to the clipboard.
        """

    def PasteFromClipboard(self, position: int) -> bool:
        """
        PasteFromClipboard(position) -> bool
        
        Paste the clipboard content to the buffer.
        """

    def CanPasteFromClipboard(self) -> bool:
        """
        CanPasteFromClipboard() -> bool
        
        Returns true if we can paste from the clipboard.
        """

    def BeginStyle(self, style: RichTextAttr) -> bool:
        """
        BeginStyle(style) -> bool
        
        Begin using a style.
        """

    def EndStyle(self) -> bool:
        """
        EndStyle() -> bool
        
        End the style.
        """

    def EndAllStyles(self) -> bool:
        """
        EndAllStyles() -> bool
        
        End all styles.
        """

    def ClearStyleStack(self) -> None:
        """
        ClearStyleStack() -> None
        
        Clears the style stack.
        """

    def GetStyleStackSize(self) -> int:
        """
        GetStyleStackSize() -> int
        
        Returns the size of the style stack, for example to check correct
        nesting.
        """

    def BeginBold(self) -> bool:
        """
        BeginBold() -> bool
        
        Begins using bold.
        """

    def EndBold(self) -> bool:
        """
        EndBold() -> bool
        
        Ends using bold.
        """

    def BeginItalic(self) -> bool:
        """
        BeginItalic() -> bool
        
        Begins using italic.
        """

    def EndItalic(self) -> bool:
        """
        EndItalic() -> bool
        
        Ends using italic.
        """

    def BeginUnderline(self) -> bool:
        """
        BeginUnderline() -> bool
        
        Begins using underline.
        """

    def EndUnderline(self) -> bool:
        """
        EndUnderline() -> bool
        
        Ends using underline.
        """

    def BeginFontSize(self, pointSize: int) -> bool:
        """
        BeginFontSize(pointSize) -> bool
        
        Begins using point size.
        """

    def EndFontSize(self) -> bool:
        """
        EndFontSize() -> bool
        
        Ends using point size.
        """

    def BeginFont(self, font: wx.Font) -> bool:
        """
        BeginFont(font) -> bool
        
        Begins using this font.
        """

    def EndFont(self) -> bool:
        """
        EndFont() -> bool
        
        Ends using a font.
        """

    def BeginTextColour(self, colour: wx.Colour) -> bool:
        """
        BeginTextColour(colour) -> bool
        
        Begins using this colour.
        """

    def EndTextColour(self) -> bool:
        """
        EndTextColour() -> bool
        
        Ends using a colour.
        """

    def BeginAlignment(self, alignment: wx.TextAttrAlignment) -> bool:
        """
        BeginAlignment(alignment) -> bool
        
        Begins using alignment.
        """

    def EndAlignment(self) -> bool:
        """
        EndAlignment() -> bool
        
        Ends alignment.
        """

    def BeginLeftIndent(self, leftIndent: int, leftSubIndent: int=0) -> bool:
        """
        BeginLeftIndent(leftIndent, leftSubIndent=0) -> bool
        
        Begins using leftIndent for the left indent, and optionally
        leftSubIndent for the sub-indent.
        """

    def EndLeftIndent(self) -> bool:
        """
        EndLeftIndent() -> bool
        
        Ends left indent.
        """

    def BeginRightIndent(self, rightIndent: int) -> bool:
        """
        BeginRightIndent(rightIndent) -> bool
        
        Begins a right indent, specified in tenths of a millimetre.
        """

    def EndRightIndent(self) -> bool:
        """
        EndRightIndent() -> bool
        
        Ends right indent.
        """

    def BeginParagraphSpacing(self, before: int, after: int) -> bool:
        """
        BeginParagraphSpacing(before, after) -> bool
        
        Begins paragraph spacing; pass the before-paragraph and after-
        paragraph spacing in tenths of a millimetre.
        """

    def EndParagraphSpacing(self) -> bool:
        """
        EndParagraphSpacing() -> bool
        
        Ends paragraph spacing.
        """

    def BeginLineSpacing(self, lineSpacing: int) -> bool:
        """
        BeginLineSpacing(lineSpacing) -> bool
        
        Begins line spacing using the specified value.
        """

    def EndLineSpacing(self) -> bool:
        """
        EndLineSpacing() -> bool
        
        Ends line spacing.
        """

    def BeginNumberedBullet(self, bulletNumber: int, leftIndent: int, leftSubIndent: int, bulletStyle: int=wx.TEXT_ATTR_BULLET_STYLE_ARABIC|wx.TEXT_ATTR_BULLET_STYLE_PERIOD) -> bool:
        """
        BeginNumberedBullet(bulletNumber, leftIndent, leftSubIndent, bulletStyle=wx.TEXT_ATTR_BULLET_STYLE_ARABIC|wx.TEXT_ATTR_BULLET_STYLE_PERIOD) -> bool
        
        Begins numbered bullet.
        """

    def EndNumberedBullet(self) -> bool:
        """
        EndNumberedBullet() -> bool
        
        Ends numbered bullet.
        """

    def BeginSymbolBullet(self, symbol: str, leftIndent: int, leftSubIndent: int, bulletStyle: int=wx.TEXT_ATTR_BULLET_STYLE_SYMBOL) -> bool:
        """
        BeginSymbolBullet(symbol, leftIndent, leftSubIndent, bulletStyle=wx.TEXT_ATTR_BULLET_STYLE_SYMBOL) -> bool
        
        Begins applying a symbol bullet, using a character from the current
        font.
        """

    def EndSymbolBullet(self) -> bool:
        """
        EndSymbolBullet() -> bool
        
        Ends symbol bullet.
        """

    def BeginStandardBullet(self, bulletName: str, leftIndent: int, leftSubIndent: int, bulletStyle: int=wx.TEXT_ATTR_BULLET_STYLE_STANDARD) -> bool:
        """
        BeginStandardBullet(bulletName, leftIndent, leftSubIndent, bulletStyle=wx.TEXT_ATTR_BULLET_STYLE_STANDARD) -> bool
        
        Begins applying a standard bullet, using one of the standard bullet
        names (currently standard/circle or standard/square.
        """

    def EndStandardBullet(self) -> bool:
        """
        EndStandardBullet() -> bool
        
        Ends standard bullet.
        """

    def BeginCharacterStyle(self, characterStyle: str) -> bool:
        """
        BeginCharacterStyle(characterStyle) -> bool
        
        Begins named character style.
        """

    def EndCharacterStyle(self) -> bool:
        """
        EndCharacterStyle() -> bool
        
        Ends named character style.
        """

    def BeginParagraphStyle(self, paragraphStyle: str) -> bool:
        """
        BeginParagraphStyle(paragraphStyle) -> bool
        
        Begins named paragraph style.
        """

    def EndParagraphStyle(self) -> bool:
        """
        EndParagraphStyle() -> bool
        
        Ends named character style.
        """

    def BeginListStyle(self, listStyle: str, level: int=1, number: int=1) -> bool:
        """
        BeginListStyle(listStyle, level=1, number=1) -> bool
        
        Begins named list style.
        """

    def EndListStyle(self) -> bool:
        """
        EndListStyle() -> bool
        
        Ends named character style.
        """

    def BeginURL(self, url: str, characterStyle: str='') -> bool:
        """
        BeginURL(url, characterStyle='') -> bool
        
        Begins applying wxTEXT_ATTR_URL to the content.
        """

    def EndURL(self) -> bool:
        """
        EndURL() -> bool
        
        Ends URL.
        """

    def AddEventHandler(self, handler: wx.EvtHandler) -> bool:
        """
        AddEventHandler(handler) -> bool
        
        Adds an event handler.
        """

    def RemoveEventHandler(self, handler: wx.EvtHandler, deleteHandler: bool=False) -> bool:
        """
        RemoveEventHandler(handler, deleteHandler=False) -> bool
        
        Removes an event handler from the buffer's list of handlers, deleting
        the object if deleteHandler is true.
        """

    def ClearEventHandlers(self) -> None:
        """
        ClearEventHandlers() -> None
        
        Clear event handlers.
        """

    def SendEvent(self, event: wx.Event, sendToAll: bool=True) -> bool:
        """
        SendEvent(event, sendToAll=True) -> bool
        
        Send event to event handlers.
        """

    def HitTest(self, dc: wx.DC, context: RichTextDrawingContext, pt: wx.Point, flags: int=0) -> Tuple[int, int, RichTextObject, RichTextObject]:
        """
        HitTest(dc, context, pt, flags=0) -> Tuple[int, int, RichTextObject, RichTextObject]
        
        Hit-testing: returns a flag indicating hit test details, plus
        information about position.
        """

    def Copy(self, obj: RichTextBuffer) -> None:
        """
        Copy(obj) -> None
        
        Copies the buffer.
        """

    def Clone(self) -> RichTextObject:
        """
        Clone() -> RichTextObject
        
        Clones the buffer.
        """

    def InsertParagraphsWithUndo(self, pos: int, paragraphs: RichTextParagraphLayoutBox, ctrl: RichTextCtrl, flags: int=0) -> bool:
        """
        InsertParagraphsWithUndo(pos, paragraphs, ctrl, flags=0) -> bool
        
        Submits a command to insert paragraphs.
        """

    def InsertTextWithUndo(self, pos: int, text: str, ctrl: RichTextCtrl, flags: int=0) -> bool:
        """
        InsertTextWithUndo(pos, text, ctrl, flags=0) -> bool
        
        Submits a command to insert the given text.
        """

    def InsertNewlineWithUndo(self, pos: int, ctrl: RichTextCtrl, flags: int=0) -> bool:
        """
        InsertNewlineWithUndo(pos, ctrl, flags=0) -> bool
        
        Submits a command to insert a newline.
        """

    def InsertImageWithUndo(self, pos: int, imageBlock: RichTextImageBlock, ctrl: RichTextCtrl, flags: int=0, textAttr: RichTextAttr=RichTextAttr()) -> bool:
        """
        InsertImageWithUndo(pos, imageBlock, ctrl, flags=0, textAttr=RichTextAttr()) -> bool
        
        Submits a command to insert the given image.
        """

    def InsertObjectWithUndo(self, pos: int, object: RichTextObject, ctrl: RichTextCtrl, flags: int) -> RichTextObject:
        """
        InsertObjectWithUndo(pos, object, ctrl, flags) -> RichTextObject
        
        Submits a command to insert an object.
        """

    def DeleteRangeWithUndo(self, range: Union[RichTextRange, wx._TwoInts], ctrl: RichTextCtrl) -> bool:
        """
        DeleteRangeWithUndo(range, ctrl) -> bool
        
        Submits a command to delete this range.
        """

    def Modify(self, modify: bool=True) -> None:
        """
        Modify(modify=True) -> None
        
        Mark modified.
        """

    def IsModified(self) -> bool:
        """
        IsModified() -> bool
        
        Returns true if the buffer was modified.
        """

    def GetScale(self) -> float:
        """
        GetScale() -> float
        
        Returns the scale factor for calculating dimensions.
        """

    def SetScale(self, scale: float) -> None:
        """
        SetScale(scale) -> None
        
        Sets the scale factor for calculating dimensions.
        """

    @staticmethod
    def GetHandlers() -> RichTextFileHandlerList:
        """
        GetHandlers() -> RichTextFileHandlerList
        
        Returns the file handlers.
        """

    @staticmethod
    def AddHandler(handler: RichTextFileHandler) -> None:
        """
        AddHandler(handler) -> None
        
        Adds a file handler to the end.
        """

    @staticmethod
    def InsertHandler(handler: RichTextFileHandler) -> None:
        """
        InsertHandler(handler) -> None
        
        Inserts a file handler at the front.
        """

    @staticmethod
    def RemoveHandler(name: str) -> bool:
        """
        RemoveHandler(name) -> bool
        
        Removes a file handler.
        """

    @staticmethod
    def FindHandlerByType(imageType: RichTextFileType) -> RichTextFileHandler:
        """
        FindHandlerByType(imageType) -> RichTextFileHandler
        
        Finds a handler by type.
        """

    @staticmethod
    def FindHandlerByExtension(extension: str, imageType: RichTextFileType) -> RichTextFileHandler:
        """
        FindHandlerByExtension(extension, imageType) -> RichTextFileHandler
        
        Finds a file handler by extension and type.
        """

    @staticmethod
    def FindHandlerByName(name: str) -> RichTextFileHandler:
        """
        FindHandlerByName(name) -> RichTextFileHandler
        
        Finds a file handler by name.
        """

    @staticmethod
    def FindHandlerByFilename(filename: str, imageType: RichTextFileType) -> RichTextFileHandler:
        """
        FindHandlerByFilename(filename, imageType) -> RichTextFileHandler
        
        Finds a handler by filename or, if supplied, type.
        """

    @staticmethod
    def GetExtWildcard(combine: bool=False, save: bool=False) -> Any:
        """
        GetExtWildcard(combine=False, save=False) -> Any
        
        Gets a wildcard string for the file dialog based on all the currently
        loaded richtext file handlers, and a list that can be used to map
        those filter types to the file handler type.
        """

    @staticmethod
    def CleanUpHandlers() -> None:
        """
        CleanUpHandlers() -> None
        
        Clean up file handlers.
        """

    @staticmethod
    def InitStandardHandlers() -> None:
        """
        InitStandardHandlers() -> None
        
        Initialise the standard file handlers.
        """

    @staticmethod
    def GetDrawingHandlers() -> RichTextDrawingHandlerList:
        """
        GetDrawingHandlers() -> RichTextDrawingHandlerList
        
        Returns the drawing handlers.
        """

    @staticmethod
    def AddDrawingHandler(handler: RichTextDrawingHandler) -> None:
        """
        AddDrawingHandler(handler) -> None
        
        Adds a drawing handler to the end.
        """

    @staticmethod
    def InsertDrawingHandler(handler: RichTextDrawingHandler) -> None:
        """
        InsertDrawingHandler(handler) -> None
        
        Inserts a drawing handler at the front.
        """

    @staticmethod
    def RemoveDrawingHandler(name: str) -> bool:
        """
        RemoveDrawingHandler(name) -> bool
        
        Removes a drawing handler.
        """

    @staticmethod
    def FindDrawingHandler(name: str) -> RichTextDrawingHandler:
        """
        FindDrawingHandler(name) -> RichTextDrawingHandler
        
        Finds a drawing handler by name.
        """

    @staticmethod
    def CleanUpDrawingHandlers() -> None:
        """
        CleanUpDrawingHandlers() -> None
        
        Clean up drawing handlers.
        """

    @staticmethod
    def AddFieldType(fieldType: RichTextFieldType) -> None:
        """
        AddFieldType(fieldType) -> None
        
        Adds a field type.
        """

    @staticmethod
    def RemoveFieldType(name: str) -> bool:
        """
        RemoveFieldType(name) -> bool
        
        Removes a field type by name.
        """

    @staticmethod
    def FindFieldType(name: str) -> RichTextFieldType:
        """
        FindFieldType(name) -> RichTextFieldType
        
        Finds a field type by name.
        """

    @staticmethod
    def CleanUpFieldTypes() -> None:
        """
        CleanUpFieldTypes() -> None
        
        Cleans up field types.
        """

    @staticmethod
    def GetRenderer() -> RichTextRenderer:
        """
        GetRenderer() -> RichTextRenderer
        
        Returns the renderer object.
        """

    @staticmethod
    def SetRenderer(renderer: RichTextRenderer) -> None:
        """
        SetRenderer(renderer) -> None
        
        Sets renderer as the object to be used to render certain aspects of
        the content, such as bullets.
        """

    @staticmethod
    def GetBulletRightMargin() -> int:
        """
        GetBulletRightMargin() -> int
        
        Returns the minimum margin between bullet and paragraph in 10ths of a
        mm.
        """

    @staticmethod
    def SetBulletRightMargin(margin: int) -> None:
        """
        SetBulletRightMargin(margin) -> None
        
        Sets the minimum margin between bullet and paragraph in 10ths of a mm.
        """

    @staticmethod
    def GetBulletProportion() -> float:
        """
        GetBulletProportion() -> float
        
        Returns the factor to multiply by character height to get a reasonable
        bullet size.
        """

    @staticmethod
    def SetBulletProportion(prop: float) -> None:
        """
        SetBulletProportion(prop) -> None
        
        Sets the factor to multiply by character height to get a reasonable
        bullet size.
        """

    @staticmethod
    def GetFloatingLayoutMode() -> bool:
        """
        GetFloatingLayoutMode() -> bool
        
        Returns the floating layout mode.
        """

    @staticmethod
    def SetFloatingLayoutMode(mode: bool) -> None:
        """
        SetFloatingLayoutMode(mode) -> None
        
        Sets the floating layout mode.
        """
    @property
    def BatchedCommand(self) -> RichTextCommand: ...
    @property
    def CommandProcessor(self) -> wx.CommandProcessor: ...
    @property
    def DimensionScale(self) -> float: ...
    @DimensionScale.setter
    def DimensionScale(self, value: float, /) -> None: ...
    @property
    def FontScale(self) -> float: ...
    @FontScale.setter
    def FontScale(self, value: float, /) -> None: ...
    @property
    def FontTable(self) -> RichTextFontTable: ...
    @FontTable.setter
    def FontTable(self, value: RichTextFontTable, /) -> None: ...
    @property
    def HandlerFlags(self) -> int: ...
    @HandlerFlags.setter
    def HandlerFlags(self, value: int, /) -> None: ...
    @property
    def Scale(self) -> float: ...
    @Scale.setter
    def Scale(self, value: float, /) -> None: ...
    @property
    def StyleSheet(self) -> RichTextStyleSheet: ...
    @StyleSheet.setter
    def StyleSheet(self, value: RichTextStyleSheet, /) -> None: ...
    @property
    def StyleStackSize(self) -> int: ...
# end of class RichTextBuffer


class RichTextCell(RichTextBox):
    """
    RichTextCell(parent=None) -> None
    RichTextCell(obj) -> None
    
    wxRichTextCell is the cell in a table, in which the user can type.
    """

    @overload
    def __init__(self, obj: RichTextCell) -> None:
        ...

    @overload
    def __init__(self, parent: Optional[RichTextObject]=None) -> None:
        """
        RichTextCell(parent=None) -> None
        RichTextCell(obj) -> None
        
        wxRichTextCell is the cell in a table, in which the user can type.
        """

    def Draw(self, dc: wx.DC, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts], selection: RichTextSelection, rect: wx.Rect, descent: int, style: int) -> bool:
        """
        Draw(dc, context, range, selection, rect, descent, style) -> bool
        
        Draw the item, within the given range.
        """

    def HitTest(self, dc: wx.DC, context: RichTextDrawingContext, pt: wx.Point, flags: int=0) -> Tuple[int, int, RichTextObject, RichTextObject]:
        """
        HitTest(dc, context, pt, flags=0) -> Tuple[int, int, RichTextObject, RichTextObject]
        
        Hit-testing: returns a flag indicating hit test details, plus
        information about position.
        """

    def GetXMLNodeName(self) -> str:
        """
        GetXMLNodeName() -> str
        
        Returns the XML node name of this object.
        """

    def CanEditProperties(self) -> bool:
        """
        CanEditProperties() -> bool
        
        Returns true if we can edit the object's properties via a GUI.
        """

    def EditProperties(self, parent: wx.Window, buffer: RichTextBuffer) -> bool:
        """
        EditProperties(parent, buffer) -> bool
        
        Edits the object's properties via a GUI.
        """

    def GetPropertiesMenuLabel(self) -> str:
        """
        GetPropertiesMenuLabel() -> str
        
        Returns the label to be used for the properties context menu item.
        """

    def GetColSpan(self) -> int:
        """
        GetColSpan() -> int
        
        Returns the number of columns spanned by the cell.
        """

    def SetColSpan(self, span: int) -> None:
        """
        SetColSpan(span) -> None
        
        Set the number of columns spanned by the cell.
        """

    def GetRowSpan(self) -> int:
        """
        GetRowSpan() -> int
        
        Returns the number of rows spanned by the cell.
        """

    def SetRowSpan(self, span: int) -> None:
        """
        SetRowSpan(span) -> None
        
        Set the number of rows spanned by the cell.
        """

    def Clone(self) -> RichTextObject:
        """
        Clone() -> RichTextObject
        
        Clones the object.
        """

    def Copy(self, obj: RichTextCell) -> None:
        """
        Copy(obj) -> None
        """
    @property
    def ColSpan(self) -> int: ...
    @ColSpan.setter
    def ColSpan(self, value: int, /) -> None: ...
    @property
    def PropertiesMenuLabel(self) -> str: ...
    @property
    def RowSpan(self) -> int: ...
    @RowSpan.setter
    def RowSpan(self, value: int, /) -> None: ...
    @property
    def XMLNodeName(self) -> str: ...
# end of class RichTextCell


class RichTextTable(RichTextBox):
    """
    RichTextTable(parent=None) -> None
    RichTextTable(obj) -> None
    
    wxRichTextTable represents a table with arbitrary columns and rows.
    """

    @overload
    def __init__(self, obj: RichTextTable) -> None:
        ...

    @overload
    def __init__(self, parent: Optional[RichTextObject]=None) -> None:
        """
        RichTextTable(parent=None) -> None
        RichTextTable(obj) -> None
        
        wxRichTextTable represents a table with arbitrary columns and rows.
        """

    def Draw(self, dc: wx.DC, context: RichTextDrawingContext, range: Union[RichTextRange, wx._TwoInts], selection: RichTextSelection, rect: wx.Rect, descent: int, style: int) -> bool:
        """
        Draw(dc, context, range, selection, rect, descent, style) -> bool
        
        Draw the item, within the given range.
        """

    def HitTest(self, dc: wx.DC, context: RichTextDrawingContext, pt: wx.Point, flags: int=0) -> Tuple[int, int, RichTextObject, RichTextObject]:
        """
        HitTest(dc, context, pt, flags=0) -> Tuple[int, int, RichTextObject, RichTextObject]
        
        Hit-testing: returns a flag indicating hit test details, plus
        information about position.
        """

    def GetXMLNodeName(self) -> str:
        """
        GetXMLNodeName() -> str
        
        Returns the XML node name of this object.
        """

    def Layout(self, dc: wx.DC, context: RichTextDrawingContext, rect: wx.Rect, parentRect: wx.Rect, style: int) -> bool:
        """
        Layout(dc, context, rect, parentRect, style) -> bool
        
        Lay the item out at the specified position with the given size
        constraint.
        """

    def GetRangeSize(self, range: Union[RichTextRange, wx._TwoInts], size: wx.Size, descent: int, dc: wx.DC, context: RichTextDrawingContext, flags: int, position: wx.Point=wx.Point(0,0), parentSize: wx.Size=wx.DefaultSize, partialExtents: Optional[List[int]]=None) -> bool:
        """
        GetRangeSize(range, size, descent, dc, context, flags, position=wx.Point(0,0), parentSize=wx.DefaultSize, partialExtents=None) -> bool
        
        Returns the object size for the given range.
        """

    def DeleteRange(self, range: Union[RichTextRange, wx._TwoInts]) -> bool:
        """
        DeleteRange(range) -> bool
        
        Deletes the given range.
        """

    def GetTextForRange(self, range: Union[RichTextRange, wx._TwoInts]) -> str:
        """
        GetTextForRange(range) -> str
        
        Returns any text in this object for the given range.
        """

    def ImportFromXML(self, buffer: RichTextBuffer, node: XmlNode, handler: RichTextXMLHandler, recurse: bool) -> bool:
        """
        ImportFromXML(buffer, node, handler, recurse) -> bool
        
        Imports this object from XML.
        """

    def FindPosition(self, dc: wx.DC, context: RichTextDrawingContext, index: int, forceLineStart: bool) -> Tuple[bool, wx.Point, int]:
        """
        FindPosition(dc, context, index, forceLineStart) -> Tuple[bool, wx.Point, int]
        
        Finds the absolute position and row height for the given character
        position.
        """

    def CalculateRange(self, start: int) -> int:
        """
        CalculateRange(start) -> int
        
        Calculates the range of the object.
        """

    def HandlesChildSelections(self) -> bool:
        """
        HandlesChildSelections() -> bool
        
        Returns true if this object can handle the selections of its children,
        fOr example a table.
        """

    def GetSelection(self, start: int, end: int) -> RichTextSelection:
        """
        GetSelection(start, end) -> RichTextSelection
        
        Returns a selection object specifying the selections between start and
        end character positions.
        """

    def CanEditProperties(self) -> bool:
        """
        CanEditProperties() -> bool
        
        Returns true if we can edit the object's properties via a GUI.
        """

    def EditProperties(self, parent: wx.Window, buffer: RichTextBuffer) -> bool:
        """
        EditProperties(parent, buffer) -> bool
        
        Edits the object's properties via a GUI.
        """

    def GetPropertiesMenuLabel(self) -> str:
        """
        GetPropertiesMenuLabel() -> str
        
        Returns the label to be used for the properties context menu item.
        """

    def AcceptsFocus(self) -> bool:
        """
        AcceptsFocus() -> bool
        
        Returns true if objects of this class can accept the focus, i.e. a
        call to SetFocusObject is possible.
        """

    def GetCells(self) -> RichTextObjectPtrArrayArray:
        """
        GetCells() -> RichTextObjectPtrArrayArray
        
        Returns the cells array.
        """

    def GetRowCount(self) -> int:
        """
        GetRowCount() -> int
        
        Returns the row count.
        """

    def GetColumnCount(self) -> int:
        """
        GetColumnCount() -> int
        
        Returns the column count.
        """

    @overload
    def GetCell(self, pos: int) -> RichTextCell:
        ...

    @overload
    def GetCell(self, row: int, col: int) -> RichTextCell:
        """
        GetCell(row, col) -> RichTextCell
        GetCell(pos) -> RichTextCell
        
        Returns the cell at the given row/column position.
        """

    def GetCellRowColumnPosition(self, pos: int, row: int, col: int) -> bool:
        """
        GetCellRowColumnPosition(pos, row, col) -> bool
        
        Returns the row/column for a given character position.
        """

    def GetFocusedCell(self) -> wx.Position:
        """
        GetFocusedCell() -> wx.Position
        
        Returns the coordinates of the cell with keyboard focus, or (-1,-1) if
        none.
        """

    def ClearTable(self) -> None:
        """
        ClearTable() -> None
        
        Clears the table.
        """

    def CreateTable(self, rows: int, cols: int) -> bool:
        """
        CreateTable(rows, cols) -> bool
        
        Creates a table of the given dimensions.
        """

    def SetCellStyle(self, selection: RichTextSelection, style: RichTextAttr, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO) -> bool:
        """
        SetCellStyle(selection, style, flags=RICHTEXT_SETSTYLE_WITH_UNDO) -> bool
        
        Sets the attributes for the cells specified by the selection.
        """

    def DeleteRows(self, startRow: int, noRows: int=1) -> bool:
        """
        DeleteRows(startRow, noRows=1) -> bool
        
        Deletes rows from the given row position.
        """

    def DeleteColumns(self, startCol: int, noCols: int=1) -> bool:
        """
        DeleteColumns(startCol, noCols=1) -> bool
        
        Deletes columns from the given column position.
        """

    def AddRows(self, startRow: int, noRows: int=1, attr: RichTextAttr=RichTextAttr()) -> bool:
        """
        AddRows(startRow, noRows=1, attr=RichTextAttr()) -> bool
        
        Adds rows from the given row position.
        """

    def AddColumns(self, startCol: int, noCols: int=1, attr: RichTextAttr=RichTextAttr()) -> bool:
        """
        AddColumns(startCol, noCols=1, attr=RichTextAttr()) -> bool
        
        Adds columns from the given column position.
        """

    def Clone(self) -> RichTextObject:
        """
        Clone() -> RichTextObject
        
        Clones the object.
        """

    def Copy(self, obj: RichTextTable) -> None:
        """
        Copy(obj) -> None
        """
    @property
    def Cells(self) -> RichTextObjectPtrArrayArray: ...
    @property
    def ColumnCount(self) -> int: ...
    @property
    def FocusedCell(self) -> wx.Position: ...
    @property
    def PropertiesMenuLabel(self) -> str: ...
    @property
    def RowCount(self) -> int: ...
    @property
    def XMLNodeName(self) -> str: ...
# end of class RichTextTable


class RichTextObjectAddress:
    """
    RichTextObjectAddress(topLevelContainer, obj) -> None
    RichTextObjectAddress() -> None
    RichTextObjectAddress(address) -> None
    
    A class for specifying an object anywhere in an object hierarchy,
    without using a pointer, necessary since wxRTC commands may delete and
    recreate sub-objects so physical object addresses change.
    """

    @overload
    def __init__(self) -> None:
        ...

    @overload
    def __init__(self, address: RichTextObjectAddress) -> None:
        ...

    @overload
    def __init__(self, topLevelContainer: RichTextParagraphLayoutBox, obj: RichTextObject) -> None:
        """
        RichTextObjectAddress(topLevelContainer, obj) -> None
        RichTextObjectAddress() -> None
        RichTextObjectAddress(address) -> None
        
        A class for specifying an object anywhere in an object hierarchy,
        without using a pointer, necessary since wxRTC commands may delete and
        recreate sub-objects so physical object addresses change.
        """

    def Init(self) -> None:
        """
        Init() -> None
        """

    def Copy(self, address: RichTextObjectAddress) -> None:
        """
        Copy(address) -> None
        
        Copies the address.
        """

    def GetObject(self, topLevelContainer: RichTextParagraphLayoutBox) -> RichTextObject:
        """
        GetObject(topLevelContainer) -> RichTextObject
        
        Returns the object specified by the address, given a top level
        container.
        """

    def Create(self, topLevelContainer: RichTextParagraphLayoutBox, obj: RichTextObject) -> bool:
        """
        Create(topLevelContainer, obj) -> bool
        
        Creates the address given a container and an object.
        """

    def GetAddress(self) -> List[int]:
        """
        GetAddress() -> List[int]
        
        Returns the array of integers representing the object address.
        """

    def SetAddress(self, address: List[int]) -> None:
        """
        SetAddress(address) -> None
        
        Sets the address from an array of integers.
        """
    @property
    def Address(self) -> List[int]: ...
    @Address.setter
    def Address(self, value: List[int], /) -> None: ...
# end of class RichTextObjectAddress


class RichTextCommand(wx.Command):
    """
    RichTextCommand(name, id, buffer, container, ctrl, ignoreFirstTime=False) -> None
    RichTextCommand(name) -> None
    
    Implements a command on the undo/redo stack.
    """

    @overload
    def __init__(self, name: str) -> None:
        ...

    @overload
    def __init__(self, name: str, id: RichTextCommandId, buffer: RichTextBuffer, container: RichTextParagraphLayoutBox, ctrl: RichTextCtrl, ignoreFirstTime: bool=False) -> None:
        """
        RichTextCommand(name, id, buffer, container, ctrl, ignoreFirstTime=False) -> None
        RichTextCommand(name) -> None
        
        Implements a command on the undo/redo stack.
        """

    def Do(self) -> bool:
        """
        Do() -> bool
        
        Performs the command.
        """

    def Undo(self) -> bool:
        """
        Undo() -> bool
        
        Undoes the command.
        """

    def AddAction(self, action: RichTextAction) -> None:
        """
        AddAction(action) -> None
        
        Adds an action to the action list.
        """

    def ClearActions(self) -> None:
        """
        ClearActions() -> None
        
        Clears the action list.
        """

    def GetActions(self) -> RichTextActionList:
        """
        GetActions() -> RichTextActionList
        
        Returns the action list.
        """
    @property
    def Actions(self) -> RichTextActionList: ...
# end of class RichTextCommand


class RichTextAction(wx.Object):
    """
    RichTextAction(cmd, name, id, buffer, container, ctrl, ignoreFirstTime=False) -> None
    
    Implements a part of a command.
    """

    def __init__(self, cmd: RichTextCommand, name: str, id: RichTextCommandId, buffer: RichTextBuffer, container: RichTextParagraphLayoutBox, ctrl: RichTextCtrl, ignoreFirstTime: bool=False) -> None:
        """
        RichTextAction(cmd, name, id, buffer, container, ctrl, ignoreFirstTime=False) -> None
        
        Implements a part of a command.
        """

    def Do(self) -> bool:
        """
        Do() -> bool
        
        Performs the action.
        """

    def Undo(self) -> bool:
        """
        Undo() -> bool
        
        Undoes the action.
        """

    def UpdateAppearance(self, caretPosition: int, sendUpdateEvent: bool=False, oldFloatRect: wx.Rect=wx.Rect(), optimizationLineCharPositions: Optional[List[int]]=None, optimizationLineYPositions: Optional[List[int]]=None, isDoCmd: bool=True) -> None:
        """
        UpdateAppearance(caretPosition, sendUpdateEvent=False, oldFloatRect=wx.Rect(), optimizationLineCharPositions=None, optimizationLineYPositions=None, isDoCmd=True) -> None
        
        Updates the control appearance, optimizing if possible given
        information from the call to Layout.
        """

    def ApplyParagraphs(self, fragment: RichTextParagraphLayoutBox) -> None:
        """
        ApplyParagraphs(fragment) -> None
        
        Replaces the buffer paragraphs with the given fragment.
        """

    def GetNewParagraphs(self) -> RichTextParagraphLayoutBox:
        """
        GetNewParagraphs() -> RichTextParagraphLayoutBox
        
        Returns the new fragments.
        """

    def GetOldParagraphs(self) -> RichTextParagraphLayoutBox:
        """
        GetOldParagraphs() -> RichTextParagraphLayoutBox
        
        Returns the old fragments.
        """

    def GetAttributes(self) -> RichTextAttr:
        """
        GetAttributes() -> RichTextAttr
        
        Returns the attributes, for single-object commands.
        """

    def GetObject(self) -> RichTextObject:
        """
        GetObject() -> RichTextObject
        
        Returns the object to replace the one at the position defined by the
        container address and the action's range start position.
        """

    def StoreObject(self, obj: RichTextObject) -> None:
        """
        StoreObject(obj) -> None
        
        Stores the object to replace the one at the position defined by the
        container address without making an address for it.
        """

    def SetObject(self, obj: RichTextObject) -> None:
        """
        SetObject(obj) -> None
        
        Sets the object to replace the one at the position defined by the
        container address and the action's range start position.
        """

    def MakeObject(self, obj: RichTextObject) -> None:
        """
        MakeObject(obj) -> None
        
        Makes an address from the given object.
        """

    def SetOldAndNewObjects(self, oldObj: RichTextObject, newObj: RichTextObject) -> None:
        """
        SetOldAndNewObjects(oldObj, newObj) -> None
        
        Sets the existing and new objects, for use with
        wxRICHTEXT_CHANGE_OBJECT.
        """

    def CalculateRefreshOptimizations(self, optimizationLineCharPositions: List[int], optimizationLineYPositions: List[int], oldFloatRect: wx.Rect) -> None:
        """
        CalculateRefreshOptimizations(optimizationLineCharPositions, optimizationLineYPositions, oldFloatRect) -> None
        
        Calculate arrays for refresh optimization.
        """

    def SetPosition(self, pos: int) -> None:
        """
        SetPosition(pos) -> None
        
        Sets the position used for e.g.
        """

    def GetPosition(self) -> int:
        """
        GetPosition() -> int
        
        Returns the position used for e.g.
        """

    def SetRange(self, range: Union[RichTextRange, wx._TwoInts]) -> None:
        """
        SetRange(range) -> None
        
        Sets the range for e.g.
        """

    def GetRange(self) -> RichTextRange:
        """
        GetRange() -> RichTextRange
        
        Returns the range for e.g.
        """

    def GetContainerAddress(self) -> RichTextObjectAddress:
        """
        GetContainerAddress() -> RichTextObjectAddress
        
        Returns the address (nested position) of the container within the
        buffer being manipulated.
        """

    @overload
    def SetContainerAddress(self, container: RichTextParagraphLayoutBox, obj: RichTextObject) -> None:
        ...

    @overload
    def SetContainerAddress(self, address: RichTextObjectAddress) -> None:
        """
        SetContainerAddress(address) -> None
        SetContainerAddress(container, obj) -> None
        
        Sets the address (nested position) of the container within the buffer
        being manipulated.
        """

    def GetContainer(self) -> RichTextParagraphLayoutBox:
        """
        GetContainer() -> RichTextParagraphLayoutBox
        
        Returns the container that this action refers to, using the container
        address and top-level buffer.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns the action name.
        """

    def SetIgnoreFirstTime(self, b: bool) -> None:
        """
        SetIgnoreFirstTime(b) -> None
        
        Instructs the first Do() command should be skipped as it's already
        been applied.
        """

    def GetIgnoreFirstTime(self) -> bool:
        """
        GetIgnoreFirstTime() -> bool
        
        Returns true if the first Do() command should be skipped as it's
        already been applied.
        """
    @property
    def Attributes(self) -> RichTextAttr: ...
    @property
    def Container(self) -> RichTextParagraphLayoutBox: ...
    @property
    def ContainerAddress(self) -> RichTextObjectAddress: ...
    @ContainerAddress.setter
    def ContainerAddress(self, value: RichTextObjectAddress, /) -> None: ...
    @property
    def IgnoreFirstTime(self) -> bool: ...
    @IgnoreFirstTime.setter
    def IgnoreFirstTime(self, value: bool, /) -> None: ...
    @property
    def Name(self) -> str: ...
    @property
    def NewParagraphs(self) -> RichTextParagraphLayoutBox: ...
    @property
    def Object(self) -> RichTextObject: ...
    @Object.setter
    def Object(self, value: RichTextObject, /) -> None: ...
    @property
    def OldParagraphs(self) -> RichTextParagraphLayoutBox: ...
    @property
    def Position(self) -> int: ...
    @Position.setter
    def Position(self, value: int, /) -> None: ...
    @property
    def Range(self) -> Union[RichTextRange, wx._TwoInts]: ...
    @Range.setter
    def Range(self, value: Union[RichTextRange, wx._TwoInts], /) -> None: ...
# end of class RichTextAction


class RichTextFileHandler(wx.Object):
    """
    RichTextFileHandler(name='', ext='', type=0) -> None
    
    The base class for file handlers.
    """

    def __init__(self, name: str='', ext: str='', type: int=0) -> None:
        """
        RichTextFileHandler(name='', ext='', type=0) -> None
        
        The base class for file handlers.
        """

    @overload
    def LoadFile(self, buffer: RichTextBuffer, filename: str) -> bool:
        ...

    @overload
    def LoadFile(self, buffer: RichTextBuffer, stream: wx.InputStream) -> bool:
        """
        LoadFile(buffer, stream) -> bool
        LoadFile(buffer, filename) -> bool
        
        Loads the buffer from a stream.
        """

    @overload
    def SaveFile(self, buffer: RichTextBuffer, filename: str) -> bool:
        ...

    @overload
    def SaveFile(self, buffer: RichTextBuffer, stream: wx.OutputStream) -> bool:
        """
        SaveFile(buffer, stream) -> bool
        SaveFile(buffer, filename) -> bool
        
        Saves the buffer to a stream.
        """

    def CanHandle(self, filename: str) -> bool:
        """
        CanHandle(filename) -> bool
        
        Returns true if we handle this filename (if using files).
        """

    def CanSave(self) -> bool:
        """
        CanSave() -> bool
        
        Returns true if we can save using this handler.
        """

    def CanLoad(self) -> bool:
        """
        CanLoad() -> bool
        
        Returns true if we can load using this handler.
        """

    def IsVisible(self) -> bool:
        """
        IsVisible() -> bool
        
        Returns true if this handler should be visible to the user.
        """

    def SetVisible(self, visible: bool) -> None:
        """
        SetVisible(visible) -> None
        
        Sets whether the handler should be visible to the user (via the
        application's load and save dialogs).
        """

    def SetName(self, name: str) -> None:
        """
        SetName(name) -> None
        
        Sets the name of the handler.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns the name of the handler.
        """

    def SetExtension(self, ext: str) -> None:
        """
        SetExtension(ext) -> None
        
        Sets the default extension to recognise.
        """

    def GetExtension(self) -> str:
        """
        GetExtension() -> str
        
        Returns the default extension to recognise.
        """

    def SetType(self, type: int) -> None:
        """
        SetType(type) -> None
        
        Sets the handler type.
        """

    def GetType(self) -> int:
        """
        GetType() -> int
        
        Returns the handler type.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Sets flags that change the behaviour of loading or saving.
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        
        Returns flags controlling how loading and saving is done.
        """

    def SetEncoding(self, encoding: str) -> None:
        """
        SetEncoding(encoding) -> None
        
        Sets the encoding to use when saving a file.
        """

    def GetEncoding(self) -> str:
        """
        GetEncoding() -> str
        
        Returns the encoding to use when saving a file.
        """
    @property
    def Encoding(self) -> str: ...
    @Encoding.setter
    def Encoding(self, value: str, /) -> None: ...
    @property
    def Extension(self) -> str: ...
    @Extension.setter
    def Extension(self, value: str, /) -> None: ...
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def Name(self) -> str: ...
    @Name.setter
    def Name(self, value: str, /) -> None: ...
    @property
    def Type(self) -> int: ...
    @Type.setter
    def Type(self, value: int, /) -> None: ...

    def DoLoadFile(self, buffer: RichTextBuffer, stream: wx.InputStream) -> bool:
        """
        DoLoadFile(buffer, stream) -> bool
        
        Override to load content from stream into buffer.
        """

    def DoSaveFile(self, buffer: RichTextBuffer, stream: wx.OutputStream) -> bool:
        """
        DoSaveFile(buffer, stream) -> bool
        
        Override to save content to stream from buffer.
        """
# end of class RichTextFileHandler


class RichTextPlainTextHandler(RichTextFileHandler):
    """
    RichTextPlainTextHandler(name="Text", ext="txt", type=RICHTEXT_TYPE_TEXT) -> None
    
    Implements saving a buffer to plain text.
    """

    def __init__(self, name: str="Text", ext: str="txt", type: RichTextFileType=RICHTEXT_TYPE_TEXT) -> None:
        """
        RichTextPlainTextHandler(name="Text", ext="txt", type=RICHTEXT_TYPE_TEXT) -> None
        
        Implements saving a buffer to plain text.
        """

    def CanSave(self) -> bool:
        """
        CanSave() -> bool
        
        Returns true if we can save using this handler.
        """

    def CanLoad(self) -> bool:
        """
        CanLoad() -> bool
        
        Returns true if we can load using this handler.
        """

    def DoLoadFile(self, buffer: RichTextBuffer, stream: wx.InputStream) -> bool:
        """
        DoLoadFile(buffer, stream) -> bool
        
        Override to load content from stream into buffer.
        """

    def DoSaveFile(self, buffer: RichTextBuffer, stream: wx.OutputStream) -> bool:
        """
        DoSaveFile(buffer, stream) -> bool
        
        Override to save content to stream from buffer.
        """
# end of class RichTextPlainTextHandler


class RichTextDrawingHandler(wx.Object):
    """
    RichTextDrawingHandler(name='') -> None
    
    The base class for custom drawing handlers.
    """

    def __init__(self, name: str='') -> None:
        """
        RichTextDrawingHandler(name='') -> None
        
        The base class for custom drawing handlers.
        """

    def HasVirtualAttributes(self, obj: RichTextObject) -> bool:
        """
        HasVirtualAttributes(obj) -> bool
        
        Returns true if this object has virtual attributes that we can
        provide.
        """

    def GetVirtualAttributes(self, attr: RichTextAttr, obj: RichTextObject) -> bool:
        """
        GetVirtualAttributes(attr, obj) -> bool
        
        Provides virtual attributes that we can provide.
        """

    def GetVirtualSubobjectAttributesCount(self, obj: RichTextObject) -> int:
        """
        GetVirtualSubobjectAttributesCount(obj) -> int
        
        Gets the count for mixed virtual attributes for individual positions
        within the object.
        """

    def GetVirtualSubobjectAttributes(self, obj: RichTextObject, positions: List[int], attributes: RichTextAttrArray) -> int:
        """
        GetVirtualSubobjectAttributes(obj, positions, attributes) -> int
        
        Gets the mixed virtual attributes for individual positions within the
        object.
        """

    def HasVirtualText(self, obj: RichTextPlainText) -> bool:
        """
        HasVirtualText(obj) -> bool
        
        Do we have virtual text for this object? Virtual text allows an
        application to replace characters in an object for editing and display
        purposes, for example for highlighting special characters.
        """

    def GetVirtualText(self, obj: RichTextPlainText, text: str) -> bool:
        """
        GetVirtualText(obj, text) -> bool
        
        Gets the virtual text for this object.
        """

    def SetName(self, name: str) -> None:
        """
        SetName(name) -> None
        
        Sets the name of the handler.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns the name of the handler.
        """
    @property
    def Name(self) -> str: ...
    @Name.setter
    def Name(self, value: str, /) -> None: ...
# end of class RichTextDrawingHandler


class RichTextBufferDataObject(wx.DataObjectSimple):
    """
    RichTextBufferDataObject(richTextBuffer=None) -> None
    
    Implements a rich text data object for clipboard transfer.
    """

    def __init__(self, richTextBuffer: Optional[RichTextBuffer]=None) -> None:
        """
        RichTextBufferDataObject(richTextBuffer=None) -> None
        
        Implements a rich text data object for clipboard transfer.
        """

    def GetRichTextBuffer(self) -> RichTextBuffer:
        """
        GetRichTextBuffer() -> RichTextBuffer
        
        After a call to this function, the buffer is owned by the caller and
        it is responsible for deleting it.
        """

    def GetPreferredFormat(self, dir: wx.Direction) -> wx.DataFormat:
        """
        GetPreferredFormat(dir) -> wx.DataFormat
        
        Returns the preferred format for either rendering the data (if dir is
        Get, its default value) or for setting it.
        """

    @overload
    def GetDataSize(self, format: wx.DataFormat) -> int:
        ...

    @overload
    def GetDataSize(self) -> int:
        """
        GetDataSize() -> int
        GetDataSize(format) -> int
        
        Gets the size of our data.
        """

    @overload
    def GetDataHere(self, format: wx.DataFormat, buf: Any) -> bool:
        ...

    @overload
    def GetDataHere(self, buf: Any) -> bool:
        """
        GetDataHere(buf) -> bool
        GetDataHere(format, buf) -> bool
        
        Copy the data to the buffer, return true on success.
        """

    @overload
    def SetData(self, format: wx.DataFormat, len: int, buf: Any) -> bool:
        ...

    @overload
    def SetData(self, len: int, buf: Any) -> bool:
        """
        SetData(len, buf) -> bool
        SetData(format, len, buf) -> bool
        
        Copy the data from the buffer, return true on success.
        """

    @staticmethod
    def GetRichTextBufferFormatId() -> str:
        """
        GetRichTextBufferFormatId() -> str
        
        Returns the id for the new data format.
        """
    @property
    def DataSize(self) -> int: ...
    @property
    def RichTextBuffer(self) -> RichTextBuffer: ...
# end of class RichTextBufferDataObject


class RichTextRenderer(wx.Object):
    """
    RichTextRenderer() -> None
    
    This class isolates some common drawing functionality.
    """

    def __init__(self) -> None:
        """
        RichTextRenderer() -> None
        
        This class isolates some common drawing functionality.
        """

    def DrawStandardBullet(self, paragraph: RichTextParagraph, dc: wx.DC, attr: RichTextAttr, rect: wx.Rect) -> bool:
        """
        DrawStandardBullet(paragraph, dc, attr, rect) -> bool
        
        Draws a standard bullet, as specified by the value of GetBulletName.
        """

    def DrawTextBullet(self, paragraph: RichTextParagraph, dc: wx.DC, attr: RichTextAttr, rect: wx.Rect, text: str) -> bool:
        """
        DrawTextBullet(paragraph, dc, attr, rect, text) -> bool
        
        Draws a bullet that can be described by text, such as numbered or
        symbol bullets.
        """

    def DrawBitmapBullet(self, paragraph: RichTextParagraph, dc: wx.DC, attr: RichTextAttr, rect: wx.Rect) -> bool:
        """
        DrawBitmapBullet(paragraph, dc, attr, rect) -> bool
        
        Draws a bitmap bullet, where the bullet bitmap is specified by the
        value of GetBulletName.
        """

    def EnumerateStandardBulletNames(self, bulletNames: List[str]) -> bool:
        """
        EnumerateStandardBulletNames(bulletNames) -> bool
        
        Enumerate the standard bullet names currently supported.
        """

    def MeasureBullet(self, paragraph: RichTextParagraph, dc: wx.DC, attr: RichTextAttr, sz: wx.Size) -> bool:
        """
        MeasureBullet(paragraph, dc, attr, sz) -> bool
        
        Measure the bullet.
        """
# end of class RichTextRenderer


class RichTextStdRenderer(RichTextRenderer):
    """
    RichTextStdRenderer() -> None
    
    The standard renderer for drawing bullets.
    """

    def __init__(self) -> None:
        """
        RichTextStdRenderer() -> None
        
        The standard renderer for drawing bullets.
        """

    def DrawStandardBullet(self, paragraph: RichTextParagraph, dc: wx.DC, attr: RichTextAttr, rect: wx.Rect) -> bool:
        """
        DrawStandardBullet(paragraph, dc, attr, rect) -> bool
        
        Draws a standard bullet, as specified by the value of GetBulletName.
        """

    def DrawTextBullet(self, paragraph: RichTextParagraph, dc: wx.DC, attr: RichTextAttr, rect: wx.Rect, text: str) -> bool:
        """
        DrawTextBullet(paragraph, dc, attr, rect, text) -> bool
        
        Draws a bullet that can be described by text, such as numbered or
        symbol bullets.
        """

    def DrawBitmapBullet(self, paragraph: RichTextParagraph, dc: wx.DC, attr: RichTextAttr, rect: wx.Rect) -> bool:
        """
        DrawBitmapBullet(paragraph, dc, attr, rect) -> bool
        
        Draws a bitmap bullet, where the bullet bitmap is specified by the
        value of GetBulletName.
        """

    def EnumerateStandardBulletNames(self, bulletNames: List[str]) -> bool:
        """
        EnumerateStandardBulletNames(bulletNames) -> bool
        
        Enumerate the standard bullet names currently supported.
        """

    def MeasureBullet(self, paragraph: RichTextParagraph, dc: wx.DC, attr: RichTextAttr, sz: wx.Size) -> bool:
        """
        MeasureBullet(paragraph, dc, attr, sz) -> bool
        
        Measure the bullet.
        """
# end of class RichTextStdRenderer


def RichTextHasStyle(flags: int, style: int) -> bool:    """
    RichTextHasStyle(flags, style) -> bool
    """

def TextAttrEq(attr1: RichTextAttr, attr2: RichTextAttr) -> bool:    """
    TextAttrEq(attr1, attr2) -> bool
    
    Compare two attribute objects.
    """

def RichTextApplyStyle(destStyle: RichTextAttr, style: RichTextAttr, compareWith: Optional[RichTextAttr]=None) -> bool:    """
    RichTextApplyStyle(destStyle, style, compareWith=None) -> bool
    
    Apply one style to another.
    """

def RichTextRemoveStyle(destStyle: RichTextAttr, style: RichTextAttr) -> bool:    """
    RichTextRemoveStyle(destStyle, style) -> bool
    """

def RichTextCombineBitlists(valueA: int, valueB: int, flagsA: int, flagsB: int) -> bool:    """
    RichTextCombineBitlists(valueA, valueB, flagsA, flagsB) -> bool
    
    Combine two bitlists.
    """

def RichTextBitlistsEqPartial(valueA: int, valueB: int, flags: int) -> bool:    """
    RichTextBitlistsEqPartial(valueA, valueB, flags) -> bool
    
    Compare two bitlists.
    """

def RichTextSplitParaCharStyles(style: RichTextAttr, parStyle: RichTextAttr, charStyle: RichTextAttr) -> bool:    """
    RichTextSplitParaCharStyles(style, parStyle, charStyle) -> bool
    
    Split into paragraph and character styles.
    """

def RichTextTabsEq(tabs1: List[int], tabs2: List[int]) -> bool:    """
    RichTextTabsEq(tabs1, tabs2) -> bool
    
    Compare tabs.
    """

def RichTextDecimalToRoman(n: int) -> str:    """
    RichTextDecimalToRoman(n) -> str
    
    Convert a decimal to Roman numerals.
    """

def TextAttrCollectCommonAttributes(currentStyle: wx.TextAttr, attr: wx.TextAttr, clashingAttr: wx.TextAttr, absentAttr: wx.TextAttr) -> None:    """
    TextAttrCollectCommonAttributes(currentStyle, attr, clashingAttr, absentAttr) -> None
    """

def RichTextModuleInit() -> None:    """
    RichTextModuleInit() -> None
    """
class RichTextLineVector(Vector, RichTextLine):
    pass


from collections import namedtuple
_im_RichTextRange = namedtuple('_im_RichTextRange', ['Start', 'End'])
del namedtuple
#-- end-richtextbuffer --#
#-- begin-richtextctrl --#
RE_READONLY: int
RE_MULTILINE: int
RE_CENTRE_CARET: int
RE_CENTER_CARET: int
RICHTEXT_SHIFT_DOWN: int
RICHTEXT_CTRL_DOWN: int
RICHTEXT_ALT_DOWN: int
RICHTEXT_EX_NO_GUIDELINES: int
ID_RICHTEXT_PROPERTIES1: int
ID_RICHTEXT_PROPERTIES2: int
ID_RICHTEXT_PROPERTIES3: int

class _RichTextCtrlSelectionState(IntEnum):
    RichTextCtrlSelectionState_Normal = auto()
    RichTextCtrlSelectionState_CommonAncestor = auto()
RichTextCtrlSelectionState: TypeAlias = Union[_RichTextCtrlSelectionState, int]
RichTextCtrlSelectionState_Normal = _RichTextCtrlSelectionState.RichTextCtrlSelectionState_Normal
RichTextCtrlSelectionState_CommonAncestor = _RichTextCtrlSelectionState.RichTextCtrlSelectionState_CommonAncestor
wxEVT_RICHTEXT_LEFT_CLICK: int
wxEVT_RICHTEXT_RIGHT_CLICK: int
wxEVT_RICHTEXT_MIDDLE_CLICK: int
wxEVT_RICHTEXT_LEFT_DCLICK: int
wxEVT_RICHTEXT_RETURN: int
wxEVT_RICHTEXT_CHARACTER: int
wxEVT_RICHTEXT_CONSUMING_CHARACTER: int
wxEVT_RICHTEXT_DELETE: int
wxEVT_RICHTEXT_STYLESHEET_CHANGING: int
wxEVT_RICHTEXT_STYLESHEET_CHANGED: int
wxEVT_RICHTEXT_STYLESHEET_REPLACING: int
wxEVT_RICHTEXT_STYLESHEET_REPLACED: int
wxEVT_RICHTEXT_CONTENT_INSERTED: int
wxEVT_RICHTEXT_CONTENT_DELETED: int
wxEVT_RICHTEXT_STYLE_CHANGED: int
wxEVT_RICHTEXT_PROPERTIES_CHANGED: int
wxEVT_RICHTEXT_SELECTION_CHANGED: int
wxEVT_RICHTEXT_BUFFER_RESET: int
wxEVT_RICHTEXT_FOCUS_OBJECT_CHANGED: int

class RichTextContextMenuPropertiesInfo:
    """
    RichTextContextMenuPropertiesInfo() -> None
    
    wxRichTextContextMenuPropertiesInfo keeps track of objects that appear
    in the context menu, whose properties are available to be edited.
    """

    def __init__(self) -> None:
        """
        RichTextContextMenuPropertiesInfo() -> None
        
        wxRichTextContextMenuPropertiesInfo keeps track of objects that appear
        in the context menu, whose properties are available to be edited.
        """
    m_objects: RichTextObjectPtrArray
    m_labels: List[str]

    def Init(self) -> None:
        """
        Init() -> None
        
        Initialisation.
        """

    def AddItem(self, label: str, obj: RichTextObject) -> bool:
        """
        AddItem(label, obj) -> bool
        
        Adds an item.
        """

    def AddMenuItems(self, menu: wx.Menu, startCmd: int=ID_RICHTEXT_PROPERTIES1) -> int:
        """
        AddMenuItems(menu, startCmd=ID_RICHTEXT_PROPERTIES1) -> int
        
        Returns the number of menu items that were added.
        """

    def AddItems(self, ctrl: RichTextCtrl, container: RichTextObject, obj: RichTextObject) -> int:
        """
        AddItems(ctrl, container, obj) -> int
        
        Adds appropriate menu items for the current container and clicked on
        object (and container's parent, if appropriate).
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Clears the items.
        """

    def GetLabel(self, n: int) -> str:
        """
        GetLabel(n) -> str
        
        Returns the nth label.
        """

    def GetObject(self, n: int) -> RichTextObject:
        """
        GetObject(n) -> RichTextObject
        
        Returns the nth object.
        """

    def GetObjects(self) -> RichTextObjectPtrArray:
        """
        GetObjects() -> RichTextObjectPtrArray
        
        Returns the array of objects.
        """

    def GetLabels(self) -> List[str]:
        """
        GetLabels() -> List[str]
        
        Returns the array of labels.
        """

    def GetCount(self) -> int:
        """
        GetCount() -> int
        
        Returns the number of items.
        """
    @property
    def Count(self) -> int: ...
    @property
    def Labels(self) -> List[str]: ...
    @property
    def Objects(self) -> RichTextObjectPtrArray: ...
# end of class RichTextContextMenuPropertiesInfo


class RichTextCtrl(wx.Control):
    """
    RichTextCtrl() -> None
    RichTextCtrl(parent, id=-1, value='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=RE_MULTILINE, validator=wx.DefaultValidator, name=wx.TextCtrlNameStr) -> None
    
    wxRichTextCtrl provides a generic, ground-up implementation of a text
    control capable of showing multiple styles and images.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=-1, value: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=RE_MULTILINE, validator: wx.Validator=wx.DefaultValidator, name: str=wx.TextCtrlNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RichTextCtrl() -> None
        RichTextCtrl(parent, id=-1, value='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=RE_MULTILINE, validator=wx.DefaultValidator, name=wx.TextCtrlNameStr) -> None
        
        wxRichTextCtrl provides a generic, ground-up implementation of a text
        control capable of showing multiple styles and images.
        """

    def Create(self, parent: wx.Window, id: int=-1, value: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=RE_MULTILINE, validator: wx.Validator=wx.DefaultValidator, name: str=wx.TextCtrlNameStr) -> bool:
        """
        Create(parent, id=-1, value='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=RE_MULTILINE, validator=wx.DefaultValidator, name=wx.TextCtrlNameStr) -> bool
        
        Creates the underlying window.
        """

    def Init(self) -> None:
        """
        Init() -> None
        
        Initialises the members of the control.
        """

    def GetRange(self, from_: int, to_: int) -> str:
        """
        GetRange(from_, to_) -> str
        
        Gets the text for the given range.
        """

    def GetLineLength(self, lineNo: int) -> int:
        """
        GetLineLength(lineNo) -> int
        
        Returns the length of the specified line in characters.
        """

    def GetLineText(self, lineNo: int) -> str:
        """
        GetLineText(lineNo) -> str
        
        Returns the text for the given line.
        """

    def GetNumberOfLines(self) -> int:
        """
        GetNumberOfLines() -> int
        
        Returns the number of lines in the buffer.
        """

    def IsModified(self) -> bool:
        """
        IsModified() -> bool
        
        Returns true if the buffer has been modified.
        """

    def IsEditable(self) -> bool:
        """
        IsEditable() -> bool
        
        Returns true if the control is editable.
        """

    def IsSingleLine(self) -> bool:
        """
        IsSingleLine() -> bool
        
        Returns true if the control is single-line.
        """

    def IsMultiLine(self) -> bool:
        """
        IsMultiLine() -> bool
        
        Returns true if the control is multiline.
        """

    def GetSelection(self) -> RichTextSelection:
        """
        GetSelection() -> RichTextSelection
        """

    def GetStringSelection(self) -> str:
        """
        GetStringSelection() -> str
        
        Returns the text within the current selection range, if any.
        """

    def GetFilename(self) -> str:
        """
        GetFilename() -> str
        
        Gets the current filename associated with the control.
        """

    def SetFilename(self, filename: str) -> None:
        """
        SetFilename(filename) -> None
        
        Sets the current filename.
        """

    def SetDelayedLayoutThreshold(self, threshold: int) -> None:
        """
        SetDelayedLayoutThreshold(threshold) -> None
        
        Sets the size of the buffer beyond which layout is delayed during
        resizing.
        """

    def GetDelayedLayoutThreshold(self) -> int:
        """
        GetDelayedLayoutThreshold() -> int
        
        Gets the size of the buffer beyond which layout is delayed during
        resizing.
        """

    def GetFullLayoutRequired(self) -> bool:
        """
        GetFullLayoutRequired() -> bool
        """

    def SetFullLayoutRequired(self, b: bool) -> None:
        """
        SetFullLayoutRequired(b) -> None
        """

    def GetFullLayoutTime(self) -> int:
        """
        GetFullLayoutTime() -> int
        """

    def SetFullLayoutTime(self, t: int) -> None:
        """
        SetFullLayoutTime(t) -> None
        """

    def GetFullLayoutSavedPosition(self) -> int:
        """
        GetFullLayoutSavedPosition() -> int
        """

    def SetFullLayoutSavedPosition(self, p: int) -> None:
        """
        SetFullLayoutSavedPosition(p) -> None
        """

    def ForceDelayedLayout(self) -> None:
        """
        ForceDelayedLayout() -> None
        """

    def SetTextCursor(self, cursor: wx.Cursor) -> None:
        """
        SetTextCursor(cursor) -> None
        
        Sets the text (normal) cursor.
        """

    def GetTextCursor(self) -> wx.Cursor:
        """
        GetTextCursor() -> wx.Cursor
        
        Returns the text (normal) cursor.
        """

    def SetURLCursor(self, cursor: wx.Cursor) -> None:
        """
        SetURLCursor(cursor) -> None
        
        Sets the cursor to be used over URLs.
        """

    def GetURLCursor(self) -> wx.Cursor:
        """
        GetURLCursor() -> wx.Cursor
        
        Returns the cursor to be used over URLs.
        """

    def GetCaretAtLineStart(self) -> bool:
        """
        GetCaretAtLineStart() -> bool
        
        Returns true if we are showing the caret position at the start of a
        line instead of at the end of the previous one.
        """

    def SetCaretAtLineStart(self, atStart: bool) -> None:
        """
        SetCaretAtLineStart(atStart) -> None
        
        Sets a flag to remember that we are showing the caret position at the
        start of a line instead of at the end of the previous one.
        """

    def GetDragging(self) -> bool:
        """
        GetDragging() -> bool
        
        Returns true if we are extending a selection.
        """

    def SetDragging(self, dragging: bool) -> None:
        """
        SetDragging(dragging) -> None
        
        Sets a flag to remember if we are extending a selection.
        """

    def GetPreDrag(self) -> bool:
        """
        GetPreDrag() -> bool
        
        Are we trying to start Drag'n'Drop?
        """

    def SetPreDrag(self, pd: bool) -> None:
        """
        SetPreDrag(pd) -> None
        
        Set if we're trying to start Drag'n'Drop.
        """

    def GetDragStartPoint(self) -> wx.Point:
        """
        GetDragStartPoint() -> wx.Point
        
        Get the possible Drag'n'Drop start point.
        """

    def SetDragStartPoint(self, sp: wx.Point) -> None:
        """
        SetDragStartPoint(sp) -> None
        
        Set the possible Drag'n'Drop start point.
        """

    def GetDragStartTime(self) -> wx.DateTime:
        """
        GetDragStartTime() -> wx.DateTime
        
        Get the possible Drag'n'Drop start time.
        """

    def SetDragStartTime(self, st: wx.DateTime) -> None:
        """
        SetDragStartTime(st) -> None
        
        Set the possible Drag'n'Drop start time.
        """

    def GetContextMenu(self) -> wx.Menu:
        """
        GetContextMenu() -> wx.Menu
        
        Returns the current context menu.
        """

    def SetContextMenu(self, menu: wx.Menu) -> None:
        """
        SetContextMenu(menu) -> None
        
        Sets the current context menu.
        """

    def GetSelectionAnchor(self) -> int:
        """
        GetSelectionAnchor() -> int
        
        Returns an anchor so we know how to extend the selection.
        """

    def SetSelectionAnchor(self, anchor: int) -> None:
        """
        SetSelectionAnchor(anchor) -> None
        
        Sets an anchor so we know how to extend the selection.
        """

    def GetSelectionAnchorObject(self) -> RichTextObject:
        """
        GetSelectionAnchorObject() -> RichTextObject
        
        Returns the anchor object if selecting multiple containers.
        """

    def SetSelectionAnchorObject(self, anchor: RichTextObject) -> None:
        """
        SetSelectionAnchorObject(anchor) -> None
        
        Sets the anchor object if selecting multiple containers.
        """

    def GetContextMenuPropertiesInfo(self) -> RichTextContextMenuPropertiesInfo:
        """
        GetContextMenuPropertiesInfo() -> RichTextContextMenuPropertiesInfo
        
        Returns an object that stores information about context menu property
        item(s), in order to communicate between the context menu event
        handler and the code that responds to it.
        """

    def GetFocusObject(self) -> RichTextParagraphLayoutBox:
        """
        GetFocusObject() -> RichTextParagraphLayoutBox
        
        Returns the wxRichTextObject object that currently has the editing
        focus.
        """

    def StoreFocusObject(self, obj: RichTextParagraphLayoutBox) -> None:
        """
        StoreFocusObject(obj) -> None
        
        Setter for m_focusObject.
        """

    def SetFocusObject(self, obj: RichTextParagraphLayoutBox, setCaretPosition: bool=True) -> bool:
        """
        SetFocusObject(obj, setCaretPosition=True) -> bool
        
        Sets the wxRichTextObject object that currently has the editing focus.
        """

    def Invalidate(self) -> None:
        """
        Invalidate() -> None
        
        Invalidates the whole buffer to trigger painting later.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Clears the buffer content, leaving a single empty paragraph.
        """

    def Replace(self, from_: int, to_: int, value: str) -> None:
        """
        Replace(from_, to_, value) -> None
        
        Replaces the content in the specified range with the string specified by value.
        """

    def Remove(self, from_: int, to_: int) -> None:
        """
        Remove(from_, to_) -> None
        
        Removes the content in the specified range.
        """

    def LoadFile(self, file: str, type: int=RICHTEXT_TYPE_ANY) -> bool:
        """
        LoadFile(file, type=RICHTEXT_TYPE_ANY) -> bool
        
        Loads content into the control's buffer using the given type.
        """

    def DoLoadFile(self, file: str, fileType: int) -> bool:
        """
        DoLoadFile(file, fileType) -> bool
        
        Helper function for LoadFile().
        """

    def SaveFile(self, file: str='', type: int=RICHTEXT_TYPE_ANY) -> bool:
        """
        SaveFile(file='', type=RICHTEXT_TYPE_ANY) -> bool
        
        Saves the buffer content using the given type.
        """

    def DoSaveFile(self, file: str='', fileType: int=RICHTEXT_TYPE_ANY) -> bool:
        """
        DoSaveFile(file='', fileType=RICHTEXT_TYPE_ANY) -> bool
        
        Helper function for SaveFile().
        """

    def SetHandlerFlags(self, flags: int) -> None:
        """
        SetHandlerFlags(flags) -> None
        
        Sets flags that change the behaviour of loading or saving.
        """

    def GetHandlerFlags(self) -> int:
        """
        GetHandlerFlags() -> int
        
        Returns flags that change the behaviour of loading or saving.
        """

    def MarkDirty(self) -> None:
        """
        MarkDirty() -> None
        
        Marks the buffer as modified.
        """

    def DiscardEdits(self) -> None:
        """
        DiscardEdits() -> None
        
        Sets the buffer's modified status to false, and clears the buffer's
        command history.
        """

    def SetModified(self, modified: bool) -> None:
        """
        SetModified(modified) -> None
        """

    def SetMaxLength(self, len: int) -> None:
        """
        SetMaxLength(len) -> None
        
        Sets the maximum number of characters that may be entered in a single
        line text control.
        """

    def WriteText(self, text: str) -> None:
        """
        WriteText(text) -> None
        
        Writes text at the current position.
        """

    def AppendText(self, text: str) -> None:
        """
        AppendText(text) -> None
        
        Sets the insertion point to the end of the buffer and writes the text.
        """

    @overload
    def GetStyle(self, position: int, style: RichTextAttr) -> bool:
        ...

    @overload
    def GetStyle(self, position: int, style: RichTextAttr, container: RichTextParagraphLayoutBox) -> bool:
        ...

    @overload
    def GetStyle(self, position: int, style: wx.TextAttr) -> bool:
        """
        GetStyle(position, style) -> bool
        GetStyle(position, style) -> bool
        GetStyle(position, style, container) -> bool
        
        Gets the attributes at the given position.
        """

    @overload
    def SetStyle(self, start: int, end: int, style: RichTextAttr) -> bool:
        ...

    @overload
    def SetStyle(self, range: RichTextRange, style: wx.TextAttr) -> bool:
        ...

    @overload
    def SetStyle(self, range: RichTextRange, style: RichTextAttr) -> bool:
        ...

    @overload
    def SetStyle(self, obj: RichTextObject, textAttr: RichTextAttr, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO) -> None:
        ...

    @overload
    def SetStyle(self, start: int, end: int, style: wx.TextAttr) -> bool:
        """
        SetStyle(start, end, style) -> bool
        SetStyle(start, end, style) -> bool
        SetStyle(range, style) -> bool
        SetStyle(range, style) -> bool
        SetStyle(obj, textAttr, flags=RICHTEXT_SETSTYLE_WITH_UNDO) -> None
        
        Sets the attributes for the given range.
        """

    @overload
    def GetStyleForRange(self, range: RichTextRange, style: RichTextAttr) -> bool:
        ...

    @overload
    def GetStyleForRange(self, range: RichTextRange, style: RichTextAttr, container: RichTextParagraphLayoutBox) -> bool:
        ...

    @overload
    def GetStyleForRange(self, range: RichTextRange, style: wx.TextAttr) -> bool:
        """
        GetStyleForRange(range, style) -> bool
        GetStyleForRange(range, style) -> bool
        GetStyleForRange(range, style, container) -> bool
        
        Gets the attributes common to the specified range.
        """

    def SetStyleEx(self, range: RichTextRange, style: RichTextAttr, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO) -> bool:
        """
        SetStyleEx(range, style, flags=RICHTEXT_SETSTYLE_WITH_UNDO) -> bool
        
        Sets the attributes for the given range, passing flags to determine
        how the attributes are set.
        """

    @overload
    def GetUncombinedStyle(self, position: int, style: RichTextAttr, container: RichTextParagraphLayoutBox) -> bool:
        ...

    @overload
    def GetUncombinedStyle(self, position: int, style: RichTextAttr) -> bool:
        """
        GetUncombinedStyle(position, style) -> bool
        GetUncombinedStyle(position, style, container) -> bool
        
        Gets the attributes at the given position.
        """

    @overload
    def SetDefaultStyle(self, style: RichTextAttr) -> bool:
        ...

    @overload
    def SetDefaultStyle(self, style: wx.TextAttr) -> bool:
        """
        SetDefaultStyle(style) -> bool
        SetDefaultStyle(style) -> bool
        
        Sets the current default style, which can be used to change how
        subsequently inserted text is displayed.
        """

    def GetDefaultStyleEx(self) -> RichTextAttr:
        """
        GetDefaultStyleEx() -> RichTextAttr
        
        Returns the current default style, which can be used to change how
        subsequently inserted text is displayed.
        """

    @overload
    def SetListStyle(self, range: RichTextRange, defName: str, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom: int=1, specifiedLevel: int=-1) -> bool:
        ...

    @overload
    def SetListStyle(self, range: RichTextRange, styleDef: RichTextListStyleDefinition, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom: int=1, specifiedLevel: int=-1) -> bool:
        """
        SetListStyle(range, styleDef, flags=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom=1, specifiedLevel=-1) -> bool
        SetListStyle(range, defName, flags=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom=1, specifiedLevel=-1) -> bool
        
        Sets the list attributes for the given range, passing flags to
        determine how the attributes are set.
        """

    def ClearListStyle(self, range: RichTextRange, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO) -> bool:
        """
        ClearListStyle(range, flags=RICHTEXT_SETSTYLE_WITH_UNDO) -> bool
        
        Clears the list style from the given range, clearing list-related
        attributes and applying any named paragraph style associated with each
        paragraph.
        """

    @overload
    def NumberList(self, range: RichTextRange, defName: str, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom: int=1, specifiedLevel: int=-1) -> bool:
        ...

    @overload
    def NumberList(self, range: RichTextRange, _def: Optional[RichTextListStyleDefinition]=None, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom: int=1, specifiedLevel: int=-1) -> bool:
        """
        NumberList(range, _def=None, flags=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom=1, specifiedLevel=-1) -> bool
        NumberList(range, defName, flags=RICHTEXT_SETSTYLE_WITH_UNDO, startFrom=1, specifiedLevel=-1) -> bool
        
        Numbers the paragraphs in the given range.
        """

    @overload
    def PromoteList(self, promoteBy: int, range: RichTextRange, defName: str, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, specifiedLevel: int=-1) -> bool:
        ...

    @overload
    def PromoteList(self, promoteBy: int, range: RichTextRange, _def: Optional[RichTextListStyleDefinition]=None, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO, specifiedLevel: int=-1) -> bool:
        """
        PromoteList(promoteBy, range, _def=None, flags=RICHTEXT_SETSTYLE_WITH_UNDO, specifiedLevel=-1) -> bool
        PromoteList(promoteBy, range, defName, flags=RICHTEXT_SETSTYLE_WITH_UNDO, specifiedLevel=-1) -> bool
        
        Promotes or demotes the paragraphs in the given range.
        """

    def SetProperties(self, range: RichTextRange, properties: RichTextProperties, flags: int=RICHTEXT_SETPROPERTIES_WITH_UNDO) -> bool:
        """
        SetProperties(range, properties, flags=RICHTEXT_SETPROPERTIES_WITH_UNDO) -> bool
        
        Sets the properties for the given range, passing flags to determine
        how the attributes are set.
        """

    def Delete(self, range: RichTextRange) -> bool:
        """
        Delete(range) -> bool
        
        Deletes the content within the given range.
        """

    def XYToPosition(self, x: int, y: int) -> int:
        """
        XYToPosition(x, y) -> int
        
        Translates from column and line number to position.
        """

    def PositionToXY(self, pos: int) -> Tuple[bool, int, int]:
        """
        PositionToXY(pos) -> Tuple[bool, int, int]
        
        Converts a text position to zero-based column and line numbers.
        """

    def ShowPosition(self, pos: int) -> None:
        """
        ShowPosition(pos) -> None
        
        Scrolls the buffer so that the given position is in view.
        """

    def HitTest(self, pt: wx.Point) -> Tuple[wx.TextCtrlHitTestResult, int]:
        """
        HitTest(pt) -> Tuple[wx.TextCtrlHitTestResult, int]
        
        Finds the character at the given position in pixels.
        """

    def HitTestXY(self, pt: wx.Point) -> Tuple[wx.TextCtrlHitTestResult, TextCoord, TextCoord]:
        """
        HitTestXY(pt) -> Tuple[wx.TextCtrlHitTestResult, TextCoord, TextCoord]
        """

    def FindContainerAtPoint(self, pt: wx.Point, position: int, hit: int, hitObj: RichTextObject, flags: int=0) -> RichTextParagraphLayoutBox:
        """
        FindContainerAtPoint(pt, position, hit, hitObj, flags=0) -> RichTextParagraphLayoutBox
        
        Finds the container at the given point, which is assumed to be in
        client coordinates.
        """

    def Copy(self) -> None:
        """
        Copy() -> None
        
        Copies the selected content (if any) to the clipboard.
        """

    def Cut(self) -> None:
        """
        Cut() -> None
        
        Copies the selected content (if any) to the clipboard and deletes the
        selection.
        """

    def Paste(self) -> None:
        """
        Paste() -> None
        
        Pastes content from the clipboard to the buffer.
        """

    def DeleteSelection(self) -> None:
        """
        DeleteSelection() -> None
        
        Deletes the content in the selection, if any.
        """

    def CanCopy(self) -> bool:
        """
        CanCopy() -> bool
        
        Returns true if selected content can be copied to the clipboard.
        """

    def CanCut(self) -> bool:
        """
        CanCut() -> bool
        
        Returns true if selected content can be copied to the clipboard and
        deleted.
        """

    def CanPaste(self) -> bool:
        """
        CanPaste() -> bool
        
        Returns true if the clipboard content can be pasted to the buffer.
        """

    def CanDeleteSelection(self) -> bool:
        """
        CanDeleteSelection() -> bool
        
        Returns true if selected content can be deleted.
        """

    def Undo(self) -> None:
        """
        Undo() -> None
        
        Undoes the command at the top of the command history, if there is one.
        """

    def Redo(self) -> None:
        """
        Redo() -> None
        
        Redoes the current command.
        """

    def CanUndo(self) -> bool:
        """
        CanUndo() -> bool
        
        Returns true if there is a command in the command history that can be
        undone.
        """

    def CanRedo(self) -> bool:
        """
        CanRedo() -> bool
        
        Returns true if there is a command in the command history that can be
        redone.
        """

    def SetInsertionPoint(self, pos: int) -> None:
        """
        SetInsertionPoint(pos) -> None
        
        Sets the insertion point and causes the current editing style to be
        taken from the new position (unlike wxRichTextCtrl::SetCaretPosition).
        """

    def SetInsertionPointEnd(self) -> None:
        """
        SetInsertionPointEnd() -> None
        
        Sets the insertion point to the end of the text control.
        """

    def GetInsertionPoint(self) -> int:
        """
        GetInsertionPoint() -> int
        
        Returns the current insertion point.
        """

    def GetLastPosition(self) -> TextPos:
        """
        GetLastPosition() -> TextPos
        
        Returns the last position in the buffer.
        """

    @overload
    def SetSelection(self, sel: RichTextSelection) -> None:
        ...

    @overload
    def SetSelection(self, from_: int, to_: int) -> None:
        """
        SetSelection(from_, to_) -> None
        SetSelection(sel) -> None
        
        Sets the selection to the given range.
        """

    def SelectAll(self) -> None:
        """
        SelectAll() -> None
        
        Selects all the text in the buffer.
        """

    def SetEditable(self, editable: bool) -> None:
        """
        SetEditable(editable) -> None
        
        Makes the control editable, or not.
        """

    def HasSelection(self) -> bool:
        """
        HasSelection() -> bool
        
        Returns true if there is a selection and the object containing the
        selection was the same as the current focus object.
        """

    def HasUnfocusedSelection(self) -> bool:
        """
        HasUnfocusedSelection() -> bool
        
        Returns true if there was a selection, whether or not the current
        focus object is the same as the selection's container object.
        """

    @overload
    def WriteImage(self, bitmap: wx.Bitmap, bitmapType: wx.BitmapType=wx.BITMAP_TYPE_PNG, textAttr: RichTextAttr=RichTextAttr()) -> bool:
        ...

    @overload
    def WriteImage(self, filename: str, bitmapType: wx.BitmapType, textAttr: RichTextAttr=RichTextAttr()) -> bool:
        ...

    @overload
    def WriteImage(self, imageBlock: RichTextImageBlock, textAttr: RichTextAttr=RichTextAttr()) -> bool:
        ...

    @overload
    def WriteImage(self, image: wx.Image, bitmapType: wx.BitmapType=wx.BITMAP_TYPE_PNG, textAttr: RichTextAttr=RichTextAttr()) -> bool:
        """
        WriteImage(image, bitmapType=wx.BITMAP_TYPE_PNG, textAttr=RichTextAttr()) -> bool
        WriteImage(bitmap, bitmapType=wx.BITMAP_TYPE_PNG, textAttr=RichTextAttr()) -> bool
        WriteImage(filename, bitmapType, textAttr=RichTextAttr()) -> bool
        WriteImage(imageBlock, textAttr=RichTextAttr()) -> bool
        
        Write a bitmap or image at the current insertion point.
        """

    def WriteTextBox(self, textAttr: RichTextAttr=RichTextAttr()) -> RichTextBox:
        """
        WriteTextBox(textAttr=RichTextAttr()) -> RichTextBox
        
        Write a text box at the current insertion point, returning the text
        box.
        """

    def WriteField(self, fieldType: str, properties: RichTextProperties, textAttr: RichTextAttr=RichTextAttr()) -> RichTextField:
        """
        WriteField(fieldType, properties, textAttr=RichTextAttr()) -> RichTextField
        
        Writes a field at the current insertion point.
        """

    def WriteTable(self, rows: int, cols: int, tableAttr: RichTextAttr=RichTextAttr(), cellAttr: RichTextAttr=RichTextAttr()) -> RichTextTable:
        """
        WriteTable(rows, cols, tableAttr=RichTextAttr(), cellAttr=RichTextAttr()) -> RichTextTable
        
        Write a table at the current insertion point, returning the table.
        """

    def Newline(self) -> bool:
        """
        Newline() -> bool
        
        Inserts a new paragraph at the current insertion point.
        """

    def LineBreak(self) -> bool:
        """
        LineBreak() -> bool
        
        Inserts a line break at the current insertion point.
        """

    def SetBasicStyle(self, style: RichTextAttr) -> None:
        """
        SetBasicStyle(style) -> None
        
        Sets the basic (overall) style.
        """

    def GetBasicStyle(self) -> RichTextAttr:
        """
        GetBasicStyle() -> RichTextAttr
        
        Gets the basic (overall) style.
        """

    def BeginStyle(self, style: RichTextAttr) -> bool:
        """
        BeginStyle(style) -> bool
        
        Begins applying a style.
        """

    def EndStyle(self) -> bool:
        """
        EndStyle() -> bool
        
        Ends the current style.
        """

    def EndAllStyles(self) -> bool:
        """
        EndAllStyles() -> bool
        
        Ends application of all styles in the current style stack.
        """

    def BeginBold(self) -> bool:
        """
        BeginBold() -> bool
        
        Begins using bold.
        """

    def EndBold(self) -> bool:
        """
        EndBold() -> bool
        
        Ends using bold.
        """

    def BeginItalic(self) -> bool:
        """
        BeginItalic() -> bool
        
        Begins using italic.
        """

    def EndItalic(self) -> bool:
        """
        EndItalic() -> bool
        
        Ends using italic.
        """

    def BeginUnderline(self) -> bool:
        """
        BeginUnderline() -> bool
        
        Begins using underlining.
        """

    def EndUnderline(self) -> bool:
        """
        EndUnderline() -> bool
        
        End applying underlining.
        """

    def BeginFontSize(self, pointSize: int) -> bool:
        """
        BeginFontSize(pointSize) -> bool
        
        Begins using the given point size.
        """

    def EndFontSize(self) -> bool:
        """
        EndFontSize() -> bool
        
        Ends using a point size.
        """

    def BeginFont(self, font: wx.Font) -> bool:
        """
        BeginFont(font) -> bool
        
        Begins using this font.
        """

    def EndFont(self) -> bool:
        """
        EndFont() -> bool
        
        Ends using a font.
        """

    def BeginTextColour(self, colour: wx.Colour) -> bool:
        """
        BeginTextColour(colour) -> bool
        
        Begins using this colour.
        """

    def EndTextColour(self) -> bool:
        """
        EndTextColour() -> bool
        
        Ends applying a text colour.
        """

    def BeginAlignment(self, alignment: wx.TextAttrAlignment) -> bool:
        """
        BeginAlignment(alignment) -> bool
        
        Begins using alignment.
        """

    def EndAlignment(self) -> bool:
        """
        EndAlignment() -> bool
        
        Ends alignment.
        """

    def BeginLeftIndent(self, leftIndent: int, leftSubIndent: int=0) -> bool:
        """
        BeginLeftIndent(leftIndent, leftSubIndent=0) -> bool
        
        Begins applying a left indent and subindent in tenths of a millimetre.
        """

    def EndLeftIndent(self) -> bool:
        """
        EndLeftIndent() -> bool
        
        Ends left indent.
        """

    def BeginRightIndent(self, rightIndent: int) -> bool:
        """
        BeginRightIndent(rightIndent) -> bool
        
        Begins a right indent, specified in tenths of a millimetre.
        """

    def EndRightIndent(self) -> bool:
        """
        EndRightIndent() -> bool
        
        Ends right indent.
        """

    def BeginParagraphSpacing(self, before: int, after: int) -> bool:
        """
        BeginParagraphSpacing(before, after) -> bool
        
        Begins paragraph spacing; pass the before-paragraph and after-
        paragraph spacing in tenths of a millimetre.
        """

    def EndParagraphSpacing(self) -> bool:
        """
        EndParagraphSpacing() -> bool
        
        Ends paragraph spacing.
        """

    def BeginLineSpacing(self, lineSpacing: int) -> bool:
        """
        BeginLineSpacing(lineSpacing) -> bool
        
        Begins applying line spacing.
        """

    def EndLineSpacing(self) -> bool:
        """
        EndLineSpacing() -> bool
        
        Ends line spacing.
        """

    def BeginNumberedBullet(self, bulletNumber: int, leftIndent: int, leftSubIndent: int, bulletStyle: int=wx.TEXT_ATTR_BULLET_STYLE_ARABIC|wx.TEXT_ATTR_BULLET_STYLE_PERIOD) -> bool:
        """
        BeginNumberedBullet(bulletNumber, leftIndent, leftSubIndent, bulletStyle=wx.TEXT_ATTR_BULLET_STYLE_ARABIC|wx.TEXT_ATTR_BULLET_STYLE_PERIOD) -> bool
        
        Begins a numbered bullet.
        """

    def EndNumberedBullet(self) -> bool:
        """
        EndNumberedBullet() -> bool
        
        Ends application of a numbered bullet.
        """

    def BeginSymbolBullet(self, symbol: str, leftIndent: int, leftSubIndent: int, bulletStyle: int=wx.TEXT_ATTR_BULLET_STYLE_SYMBOL) -> bool:
        """
        BeginSymbolBullet(symbol, leftIndent, leftSubIndent, bulletStyle=wx.TEXT_ATTR_BULLET_STYLE_SYMBOL) -> bool
        
        Begins applying a symbol bullet, using a character from the current
        font.
        """

    def EndSymbolBullet(self) -> bool:
        """
        EndSymbolBullet() -> bool
        
        Ends applying a symbol bullet.
        """

    def BeginStandardBullet(self, bulletName: str, leftIndent: int, leftSubIndent: int, bulletStyle: int=wx.TEXT_ATTR_BULLET_STYLE_STANDARD) -> bool:
        """
        BeginStandardBullet(bulletName, leftIndent, leftSubIndent, bulletStyle=wx.TEXT_ATTR_BULLET_STYLE_STANDARD) -> bool
        
        Begins applying a symbol bullet.
        """

    def EndStandardBullet(self) -> bool:
        """
        EndStandardBullet() -> bool
        
        Begins applying a standard bullet.
        """

    def BeginCharacterStyle(self, characterStyle: str) -> bool:
        """
        BeginCharacterStyle(characterStyle) -> bool
        
        Begins using the named character style.
        """

    def EndCharacterStyle(self) -> bool:
        """
        EndCharacterStyle() -> bool
        
        Ends application of a named character style.
        """

    def BeginParagraphStyle(self, paragraphStyle: str) -> bool:
        """
        BeginParagraphStyle(paragraphStyle) -> bool
        
        Begins applying the named paragraph style.
        """

    def EndParagraphStyle(self) -> bool:
        """
        EndParagraphStyle() -> bool
        
        Ends application of a named paragraph style.
        """

    def BeginListStyle(self, listStyle: str, level: int=1, number: int=1) -> bool:
        """
        BeginListStyle(listStyle, level=1, number=1) -> bool
        
        Begins using a specified list style.
        """

    def EndListStyle(self) -> bool:
        """
        EndListStyle() -> bool
        
        Ends using a specified list style.
        """

    def BeginURL(self, url: str, characterStyle: str='') -> bool:
        """
        BeginURL(url, characterStyle='') -> bool
        
        Begins applying wxTEXT_ATTR_URL to the content.
        """

    def EndURL(self) -> bool:
        """
        EndURL() -> bool
        
        Ends applying a URL.
        """

    def SetDefaultStyleToCursorStyle(self) -> bool:
        """
        SetDefaultStyleToCursorStyle() -> bool
        
        Sets the default style to the style under the cursor.
        """

    def SelectNone(self) -> None:
        """
        SelectNone() -> None
        
        Cancels any selection.
        """

    def SelectWord(self, position: int) -> bool:
        """
        SelectWord(position) -> bool
        
        Selects the word at the given character position.
        """

    def GetSelectionRange(self) -> RichTextRange:
        """
        GetSelectionRange() -> RichTextRange
        
        Returns the selection range in character positions.
        """

    def SetSelectionRange(self, range: RichTextRange) -> None:
        """
        SetSelectionRange(range) -> None
        
        Sets the selection to the given range.
        """

    def GetInternalSelectionRange(self) -> RichTextRange:
        """
        GetInternalSelectionRange() -> RichTextRange
        
        Returns the selection range in character positions.
        """

    def SetInternalSelectionRange(self, range: RichTextRange) -> None:
        """
        SetInternalSelectionRange(range) -> None
        
        Sets the selection range in character positions.
        """

    def AddParagraph(self, text: str) -> RichTextRange:
        """
        AddParagraph(text) -> RichTextRange
        
        Adds a new paragraph of text to the end of the buffer.
        """

    def AddImage(self, image: wx.Image) -> RichTextRange:
        """
        AddImage(image) -> RichTextRange
        
        Adds an image to the control's buffer.
        """

    def LayoutContent(self, onlyVisibleRect: bool=False) -> bool:
        """
        LayoutContent(onlyVisibleRect=False) -> bool
        
        Lays out the buffer, which must be done before certain operations,
        such as setting the caret position.
        """

    def DoLayoutBuffer(self, buffer: RichTextBuffer, dc: wx.DC, context: RichTextDrawingContext, rect: wx.Rect, parentRect: wx.Rect, flags: int) -> None:
        """
        DoLayoutBuffer(buffer, dc, context, rect, parentRect, flags) -> None
        
        Implements layout.
        """

    def MoveCaret(self, pos: int, showAtLineStart: bool=False, container: Optional[RichTextParagraphLayoutBox]=None) -> bool:
        """
        MoveCaret(pos, showAtLineStart=False, container=None) -> bool
        
        Move the caret to the given character position.
        """

    def MoveRight(self, noPositions: int=1, flags: int=0) -> bool:
        """
        MoveRight(noPositions=1, flags=0) -> bool
        
        Moves right.
        """

    def MoveLeft(self, noPositions: int=1, flags: int=0) -> bool:
        """
        MoveLeft(noPositions=1, flags=0) -> bool
        
        Moves left.
        """

    def MoveUp(self, noLines: int=1, flags: int=0) -> bool:
        """
        MoveUp(noLines=1, flags=0) -> bool
        
        Moves to the start of the paragraph.
        """

    def MoveDown(self, noLines: int=1, flags: int=0) -> bool:
        """
        MoveDown(noLines=1, flags=0) -> bool
        
        Moves the caret down.
        """

    def MoveToLineEnd(self, flags: int=0) -> bool:
        """
        MoveToLineEnd(flags=0) -> bool
        
        Moves to the end of the line.
        """

    def MoveToLineStart(self, flags: int=0) -> bool:
        """
        MoveToLineStart(flags=0) -> bool
        
        Moves to the start of the line.
        """

    def MoveToParagraphEnd(self, flags: int=0) -> bool:
        """
        MoveToParagraphEnd(flags=0) -> bool
        
        Moves to the end of the paragraph.
        """

    def MoveToParagraphStart(self, flags: int=0) -> bool:
        """
        MoveToParagraphStart(flags=0) -> bool
        
        Moves to the start of the paragraph.
        """

    def MoveHome(self, flags: int=0) -> bool:
        """
        MoveHome(flags=0) -> bool
        
        Moves to the start of the buffer.
        """

    def MoveEnd(self, flags: int=0) -> bool:
        """
        MoveEnd(flags=0) -> bool
        
        Moves to the end of the buffer.
        """

    def PageUp(self, noPages: int=1, flags: int=0) -> bool:
        """
        PageUp(noPages=1, flags=0) -> bool
        
        Moves one or more pages up.
        """

    def PageDown(self, noPages: int=1, flags: int=0) -> bool:
        """
        PageDown(noPages=1, flags=0) -> bool
        
        Moves one or more pages down.
        """

    def WordLeft(self, noPages: int=1, flags: int=0) -> bool:
        """
        WordLeft(noPages=1, flags=0) -> bool
        
        Moves a number of words to the left.
        """

    def WordRight(self, noPages: int=1, flags: int=0) -> bool:
        """
        WordRight(noPages=1, flags=0) -> bool
        
        Move a number of words to the right.
        """

    def GetBuffer(self) -> RichTextBuffer:
        """
        GetBuffer() -> RichTextBuffer
        
        Returns the buffer associated with the control.
        """

    def BeginBatchUndo(self, cmdName: str) -> bool:
        """
        BeginBatchUndo(cmdName) -> bool
        
        Starts batching undo history for commands.
        """

    def EndBatchUndo(self) -> bool:
        """
        EndBatchUndo() -> bool
        
        Ends batching undo command history.
        """

    def BatchingUndo(self) -> bool:
        """
        BatchingUndo() -> bool
        
        Returns true if undo commands are being batched.
        """

    def BeginSuppressUndo(self) -> bool:
        """
        BeginSuppressUndo() -> bool
        
        Starts suppressing undo history for commands.
        """

    def EndSuppressUndo(self) -> bool:
        """
        EndSuppressUndo() -> bool
        
        Ends suppressing undo command history.
        """

    def SuppressingUndo(self) -> bool:
        """
        SuppressingUndo() -> bool
        
        Returns true if undo history suppression is on.
        """

    def HasCharacterAttributes(self, range: RichTextRange, style: RichTextAttr) -> bool:
        """
        HasCharacterAttributes(range, style) -> bool
        
        Test if this whole range has character attributes of the specified
        kind.
        """

    def HasParagraphAttributes(self, range: RichTextRange, style: RichTextAttr) -> bool:
        """
        HasParagraphAttributes(range, style) -> bool
        
        Test if this whole range has paragraph attributes of the specified
        kind.
        """

    def IsSelectionBold(self) -> bool:
        """
        IsSelectionBold() -> bool
        
        Returns true if all of the selection, or the content at the caret
        position, is bold.
        """

    def IsSelectionItalics(self) -> bool:
        """
        IsSelectionItalics() -> bool
        
        Returns true if all of the selection, or the content at the caret
        position, is italic.
        """

    def IsSelectionUnderlined(self) -> bool:
        """
        IsSelectionUnderlined() -> bool
        
        Returns true if all of the selection, or the content at the caret
        position, is underlined.
        """

    def DoesSelectionHaveTextEffectFlag(self, flag: int) -> bool:
        """
        DoesSelectionHaveTextEffectFlag(flag) -> bool
        
        Returns true if all of the selection, or the content at the current
        caret position, has the supplied wxTextAttrEffects flag(s).
        """

    def IsSelectionAligned(self, alignment: wx.TextAttrAlignment) -> bool:
        """
        IsSelectionAligned(alignment) -> bool
        
        Returns true if all of the selection is aligned according to the
        specified flag.
        """

    def ApplyBoldToSelection(self) -> bool:
        """
        ApplyBoldToSelection() -> bool
        
        Apples bold to the selection or the default style (undoable).
        """

    def ApplyItalicToSelection(self) -> bool:
        """
        ApplyItalicToSelection() -> bool
        
        Applies italic to the selection or the default style (undoable).
        """

    def ApplyUnderlineToSelection(self) -> bool:
        """
        ApplyUnderlineToSelection() -> bool
        
        Applies underline to the selection or the default style (undoable).
        """

    def ApplyTextEffectToSelection(self, flags: int) -> bool:
        """
        ApplyTextEffectToSelection(flags) -> bool
        
        Applies one or more wxTextAttrEffects flags to the selection
        (undoable).
        """

    def ApplyAlignmentToSelection(self, alignment: wx.TextAttrAlignment) -> bool:
        """
        ApplyAlignmentToSelection(alignment) -> bool
        
        Applies the given alignment to the selection or the default style
        (undoable).
        """

    def ApplyStyle(self, styleDef: RichTextStyleDefinition) -> bool:
        """
        ApplyStyle(styleDef) -> bool
        
        Applies the style sheet to the buffer, matching paragraph styles in
        the sheet against named styles in the buffer.
        """

    def SetStyleSheet(self, styleSheet: RichTextStyleSheet) -> None:
        """
        SetStyleSheet(styleSheet) -> None
        
        Sets the style sheet associated with the control.
        """

    def GetStyleSheet(self) -> RichTextStyleSheet:
        """
        GetStyleSheet() -> RichTextStyleSheet
        
        Returns the style sheet associated with the control, if any.
        """

    def PushStyleSheet(self, styleSheet: RichTextStyleSheet) -> bool:
        """
        PushStyleSheet(styleSheet) -> bool
        
        Push the style sheet to top of stack.
        """

    def PopStyleSheet(self) -> RichTextStyleSheet:
        """
        PopStyleSheet() -> RichTextStyleSheet
        
        Pops the style sheet from top of stack.
        """

    def ApplyStyleSheet(self, styleSheet: Optional[RichTextStyleSheet]=None) -> bool:
        """
        ApplyStyleSheet(styleSheet=None) -> bool
        
        Applies the style sheet to the buffer, for example if the styles have
        changed.
        """

    def ShowContextMenu(self, menu: wx.Menu, pt: wx.Point, addPropertyCommands: bool) -> bool:
        """
        ShowContextMenu(menu, pt, addPropertyCommands) -> bool
        
        Shows the given context menu, optionally adding appropriate property-
        editing commands for the current position in the object hierarchy.
        """

    def PrepareContextMenu(self, menu: wx.Menu, pt: wx.Point, addPropertyCommands: bool) -> int:
        """
        PrepareContextMenu(menu, pt, addPropertyCommands) -> int
        
        Prepares the context menu, optionally adding appropriate property-
        editing commands.
        """

    def CanEditProperties(self, obj: RichTextObject) -> bool:
        """
        CanEditProperties(obj) -> bool
        
        Returns true if we can edit the object's properties via a GUI.
        """

    def EditProperties(self, obj: RichTextObject, parent: wx.Window) -> bool:
        """
        EditProperties(obj, parent) -> bool
        
        Edits the object's properties via a GUI.
        """

    def GetPropertiesMenuLabel(self, obj: RichTextObject) -> str:
        """
        GetPropertiesMenuLabel(obj) -> str
        
        Gets the object's properties menu label.
        """

    def PrepareContent(self, container: RichTextParagraphLayoutBox) -> None:
        """
        PrepareContent(container) -> None
        
        Prepares the content just before insertion (or after buffer reset).
        """

    def CanDeleteRange(self, container: RichTextParagraphLayoutBox, range: RichTextRange) -> bool:
        """
        CanDeleteRange(container, range) -> bool
        
        Can we delete this range? Sends an event to the control.
        """

    def CanInsertContent(self, container: RichTextParagraphLayoutBox, pos: int) -> bool:
        """
        CanInsertContent(container, pos) -> bool
        
        Can we insert content at this position? Sends an event to the control.
        """

    def EnableVerticalScrollbar(self, enable: bool) -> None:
        """
        EnableVerticalScrollbar(enable) -> None
        
        Enable or disable the vertical scrollbar.
        """

    def GetVerticalScrollbarEnabled(self) -> bool:
        """
        GetVerticalScrollbarEnabled() -> bool
        
        Returns true if the vertical scrollbar is enabled.
        """

    def SetFontScale(self, fontScale: float, refresh: bool=False) -> None:
        """
        SetFontScale(fontScale, refresh=False) -> None
        
        Sets the scale factor for displaying fonts, for example for more
        comfortable editing.
        """

    def GetFontScale(self) -> float:
        """
        GetFontScale() -> float
        
        Returns the scale factor for displaying fonts, for example for more
        comfortable editing.
        """

    def SetDimensionScale(self, dimScale: float, refresh: bool=False) -> None:
        """
        SetDimensionScale(dimScale, refresh=False) -> None
        
        Sets the scale factor for displaying certain dimensions such as
        indentation and inter-paragraph spacing.
        """

    def GetDimensionScale(self) -> float:
        """
        GetDimensionScale() -> float
        
        Returns the scale factor for displaying certain dimensions such as
        indentation and inter-paragraph spacing.
        """

    def SetScale(self, scale: float, refresh: bool=False) -> None:
        """
        SetScale(scale, refresh=False) -> None
        
        Sets an overall scale factor for displaying and editing the content.
        """

    def GetScale(self) -> float:
        """
        GetScale() -> float
        
        Returns an overall scale factor for displaying and editing the
        content.
        """

    def GetUnscaledPoint(self, pt: wx.Point) -> wx.Point:
        """
        GetUnscaledPoint(pt) -> wx.Point
        
        Returns an unscaled point.
        """

    def GetScaledPoint(self, pt: wx.Point) -> wx.Point:
        """
        GetScaledPoint(pt) -> wx.Point
        
        Returns a scaled point.
        """

    def GetUnscaledSize(self, sz: wx.Size) -> wx.Size:
        """
        GetUnscaledSize(sz) -> wx.Size
        
        Returns an unscaled size.
        """

    def GetScaledSize(self, sz: wx.Size) -> wx.Size:
        """
        GetScaledSize(sz) -> wx.Size
        
        Returns a scaled size.
        """

    def GetUnscaledRect(self, rect: wx.Rect) -> wx.Rect:
        """
        GetUnscaledRect(rect) -> wx.Rect
        
        Returns an unscaled rectangle.
        """

    def GetScaledRect(self, rect: wx.Rect) -> wx.Rect:
        """
        GetScaledRect(rect) -> wx.Rect
        
        Returns a scaled rectangle.
        """

    def GetVirtualAttributesEnabled(self) -> bool:
        """
        GetVirtualAttributesEnabled() -> bool
        
        Returns true if this control can use virtual attributes and virtual
        text.
        """

    def EnableVirtualAttributes(self, b: bool) -> None:
        """
        EnableVirtualAttributes(b) -> None
        
        Pass true to let the control use virtual attributes.
        """

    def Command(self, event: wx.CommandEvent) -> None:
        """
        Command(event) -> None
        
        Sends the event to the control.
        """

    def OnDropFiles(self, event: wx.DropFilesEvent) -> None:
        """
        OnDropFiles(event) -> None
        
        Loads the first dropped file.
        """

    def OnCaptureLost(self, event: wx.MouseCaptureLostEvent) -> None:
        """
        OnCaptureLost(event) -> None
        """

    def OnSysColourChanged(self, event: wx.SysColourChangedEvent) -> None:
        """
        OnSysColourChanged(event) -> None
        """

    def OnCut(self, event: wx.CommandEvent) -> None:
        """
        OnCut(event) -> None
        
        Standard handler for the wxID_CUT command.
        """

    def OnCopy(self, event: wx.CommandEvent) -> None:
        """
        OnCopy(event) -> None
        
        Standard handler for the wxID_COPY command.
        """

    def OnPaste(self, event: wx.CommandEvent) -> None:
        """
        OnPaste(event) -> None
        
        Standard handler for the wxID_PASTE command.
        """

    def OnUndo(self, event: wx.CommandEvent) -> None:
        """
        OnUndo(event) -> None
        
        Standard handler for the wxID_UNDO command.
        """

    def OnRedo(self, event: wx.CommandEvent) -> None:
        """
        OnRedo(event) -> None
        
        Standard handler for the wxID_REDO command.
        """

    def OnSelectAll(self, event: wx.CommandEvent) -> None:
        """
        OnSelectAll(event) -> None
        
        Standard handler for the wxID_SELECTALL command.
        """

    def OnProperties(self, event: wx.CommandEvent) -> None:
        """
        OnProperties(event) -> None
        
        Standard handler for property commands.
        """

    def OnClear(self, event: wx.CommandEvent) -> None:
        """
        OnClear(event) -> None
        
        Standard handler for the wxID_CLEAR command.
        """

    def OnUpdateCut(self, event: wx.UpdateUIEvent) -> None:
        """
        OnUpdateCut(event) -> None
        
        Standard update handler for the wxID_CUT command.
        """

    def OnUpdateCopy(self, event: wx.UpdateUIEvent) -> None:
        """
        OnUpdateCopy(event) -> None
        
        Standard update handler for the wxID_COPY command.
        """

    def OnUpdatePaste(self, event: wx.UpdateUIEvent) -> None:
        """
        OnUpdatePaste(event) -> None
        
        Standard update handler for the wxID_PASTE command.
        """

    def OnUpdateUndo(self, event: wx.UpdateUIEvent) -> None:
        """
        OnUpdateUndo(event) -> None
        
        Standard update handler for the wxID_UNDO command.
        """

    def OnUpdateRedo(self, event: wx.UpdateUIEvent) -> None:
        """
        OnUpdateRedo(event) -> None
        
        Standard update handler for the wxID_REDO command.
        """

    def OnUpdateSelectAll(self, event: wx.UpdateUIEvent) -> None:
        """
        OnUpdateSelectAll(event) -> None
        
        Standard update handler for the wxID_SELECTALL command.
        """

    def OnUpdateProperties(self, event: wx.UpdateUIEvent) -> None:
        """
        OnUpdateProperties(event) -> None
        
        Standard update handler for property commands.
        """

    def OnUpdateClear(self, event: wx.UpdateUIEvent) -> None:
        """
        OnUpdateClear(event) -> None
        
        Standard update handler for the wxID_CLEAR command.
        """

    def OnContextMenu(self, event: wx.ContextMenuEvent) -> None:
        """
        OnContextMenu(event) -> None
        
        Shows a standard context menu with undo, redo, cut, copy, paste,
        clear, and select all commands.
        """

    def OnPaint(self, event: wx.PaintEvent) -> None:
        """
        OnPaint(event) -> None
        """

    def OnEraseBackground(self, event: wx.EraseEvent) -> None:
        """
        OnEraseBackground(event) -> None
        """

    def OnLeftClick(self, event: wx.MouseEvent) -> None:
        """
        OnLeftClick(event) -> None
        """

    def OnLeftUp(self, event: wx.MouseEvent) -> None:
        """
        OnLeftUp(event) -> None
        """

    def OnMoveMouse(self, event: wx.MouseEvent) -> None:
        """
        OnMoveMouse(event) -> None
        """

    def OnLeftDClick(self, event: wx.MouseEvent) -> None:
        """
        OnLeftDClick(event) -> None
        """

    def OnMiddleClick(self, event: wx.MouseEvent) -> None:
        """
        OnMiddleClick(event) -> None
        """

    def OnRightClick(self, event: wx.MouseEvent) -> None:
        """
        OnRightClick(event) -> None
        """

    def OnChar(self, event: wx.KeyEvent) -> None:
        """
        OnChar(event) -> None
        """

    def OnSize(self, event: wx.SizeEvent) -> None:
        """
        OnSize(event) -> None
        """

    def OnSetFocus(self, event: wx.FocusEvent) -> None:
        """
        OnSetFocus(event) -> None
        """

    def OnKillFocus(self, event: wx.FocusEvent) -> None:
        """
        OnKillFocus(event) -> None
        """

    def OnIdle(self, event: wx.IdleEvent) -> None:
        """
        OnIdle(event) -> None
        """

    def OnScroll(self, event: wx.ScrollWinEvent) -> None:
        """
        OnScroll(event) -> None
        """

    def SetFont(self, font: wx.Font) -> bool:
        """
        SetFont(font) -> bool
        
        Sets the font, and also the basic and default attributes (see
        wxRichTextCtrl::SetDefaultStyle).
        """

    def SetupScrollbars(self, atTop: bool=False) -> None:
        """
        SetupScrollbars(atTop=False) -> None
        
        A helper function setting up scrollbars, for example after a resize.
        """

    def KeyboardNavigate(self, keyCode: int, flags: int) -> bool:
        """
        KeyboardNavigate(keyCode, flags) -> bool
        
        Helper function implementing keyboard navigation.
        """

    def PaintBackground(self, dc: wx.DC) -> None:
        """
        PaintBackground(dc) -> None
        
        Paints the background.
        """

    def PaintAboveContent(self, WXUNUSED: wx.DC) -> None:
        """
        PaintAboveContent(WXUNUSED) -> None
        
        Other user defined painting after everything else (i.e. all text) is
        painted.
        """

    def DoWriteText(self, value: str, flags: int=0) -> None:
        """
        DoWriteText(value, flags=0) -> None
        """

    def ShouldInheritColours(self) -> bool:
        """
        ShouldInheritColours() -> bool
        
        Return true from here to allow the colours of this window to be
        changed by InheritAttributes().
        """

    def PositionCaret(self, container: Optional[RichTextParagraphLayoutBox]=None) -> None:
        """
        PositionCaret(container=None) -> None
        
        Internal function to position the visible caret according to the
        current caret position.
        """

    def ExtendSelection(self, oldPosition: int, newPosition: int, flags: int) -> bool:
        """
        ExtendSelection(oldPosition, newPosition, flags) -> bool
        
        Helper function for extending the selection, returning true if the
        selection was changed.
        """

    def ExtendCellSelection(self, table: RichTextTable, noRowSteps: int, noColSteps: int) -> bool:
        """
        ExtendCellSelection(table, noRowSteps, noColSteps) -> bool
        
        Extends a table selection in the given direction.
        """

    def StartCellSelection(self, table: RichTextTable, newCell: RichTextParagraphLayoutBox) -> bool:
        """
        StartCellSelection(table, newCell) -> bool
        
        Starts selecting table cells.
        """

    def ScrollIntoView(self, position: int, keyCode: int) -> bool:
        """
        ScrollIntoView(position, keyCode) -> bool
        
        Scrolls position into view.
        """

    def RefreshForSelectionChange(self, oldSelection: RichTextSelection, newSelection: RichTextSelection) -> bool:
        """
        RefreshForSelectionChange(oldSelection, newSelection) -> bool
        
        Refreshes the area affected by a selection change.
        """

    def SetCaretPosition(self, position: int, showAtLineStart: bool=False) -> None:
        """
        SetCaretPosition(position, showAtLineStart=False) -> None
        
        Sets the caret position.
        """

    def GetCaretPosition(self) -> int:
        """
        GetCaretPosition() -> int
        
        Returns the current caret position.
        """

    def GetAdjustedCaretPosition(self, caretPos: int) -> int:
        """
        GetAdjustedCaretPosition(caretPos) -> int
        
        The adjusted caret position is the character position adjusted to take
        into account whether we're at the start of a paragraph, in which case
        style information should be taken from the next position, not current
        one.
        """

    def MoveCaretForward(self, oldPosition: int) -> None:
        """
        MoveCaretForward(oldPosition) -> None
        
        Move the caret one visual step forward: this may mean setting a flag
        and keeping the same position if we're going from the end of one line
        to the start of the next, which may be the exact same caret position.
        """

    def MoveCaretBack(self, oldPosition: int) -> None:
        """
        MoveCaretBack(oldPosition) -> None
        
        Move the caret one visual step forward: this may mean setting a flag
        and keeping the same position if we're going from the end of one line
        to the start of the next, which may be the exact same caret position.
        """

    def GetCaretPositionForIndex(self, position: int, rect: wx.Rect, container: Optional[RichTextParagraphLayoutBox]=None) -> bool:
        """
        GetCaretPositionForIndex(position, rect, container=None) -> bool
        
        Returns the caret height and position for the given character
        position.
        """

    def GetVisibleLineForCaretPosition(self, caretPosition: int) -> RichTextLine:
        """
        GetVisibleLineForCaretPosition(caretPosition) -> RichTextLine
        
        Internal helper function returning the line for the visible caret
        position.
        """

    def GetCommandProcessor(self) -> wx.CommandProcessor:
        """
        GetCommandProcessor() -> wx.CommandProcessor
        
        Gets the command processor associated with the control's buffer.
        """

    def DeleteSelectedContent(self, newPos: Optional[int]=None) -> bool:
        """
        DeleteSelectedContent(newPos=None) -> bool
        
        Deletes content if there is a selection, e.g.
        """

    def GetPhysicalPoint(self, ptLogical: wx.Point) -> wx.Point:
        """
        GetPhysicalPoint(ptLogical) -> wx.Point
        
        Transforms logical (unscrolled) position to physical window position.
        """

    def GetLogicalPoint(self, ptPhysical: wx.Point) -> wx.Point:
        """
        GetLogicalPoint(ptPhysical) -> wx.Point
        
        Transforms physical window position to logical (unscrolled) position.
        """

    def FindNextWordPosition(self, direction: int=1) -> int:
        """
        FindNextWordPosition(direction=1) -> int
        
        Helper function for finding the caret position for the next word.
        """

    def IsPositionVisible(self, pos: int) -> bool:
        """
        IsPositionVisible(pos) -> bool
        
        Returns true if the given position is visible on the screen.
        """

    def GetFirstVisiblePosition(self) -> int:
        """
        GetFirstVisiblePosition() -> int
        
        Returns the first visible position in the current view.
        """

    def EnableImages(self, b: bool) -> None:
        """
        EnableImages(b) -> None
        
        Enable or disable images.
        """

    def GetImagesEnabled(self) -> bool:
        """
        GetImagesEnabled() -> bool
        
        Returns true if images are enabled.
        """

    def EnableDelayedImageLoading(self, b: bool) -> None:
        """
        EnableDelayedImageLoading(b) -> None
        
        Enable or disable delayed image loading.
        """

    def GetDelayedImageLoading(self) -> bool:
        """
        GetDelayedImageLoading() -> bool
        
        Returns true if delayed image loading is enabled.
        """

    def GetDelayedImageProcessingRequired(self) -> bool:
        """
        GetDelayedImageProcessingRequired() -> bool
        
        Gets the flag indicating that delayed image processing is required.
        """

    def SetDelayedImageProcessingRequired(self, b: bool) -> None:
        """
        SetDelayedImageProcessingRequired(b) -> None
        
        Sets the flag indicating that delayed image processing is required.
        """

    def GetDelayedImageProcessingTime(self) -> int:
        """
        GetDelayedImageProcessingTime() -> int
        
        Returns the last time delayed image processing was performed.
        """

    def SetDelayedImageProcessingTime(self, t: int) -> None:
        """
        SetDelayedImageProcessingTime(t) -> None
        
        Sets the last time delayed image processing was performed.
        """

    def GetCaretPositionForDefaultStyle(self) -> int:
        """
        GetCaretPositionForDefaultStyle() -> int
        
        Returns the caret position since the default formatting was changed.
        """

    def SetCaretPositionForDefaultStyle(self, pos: int) -> None:
        """
        SetCaretPositionForDefaultStyle(pos) -> None
        
        Set the caret position for the default style that the user is
        selecting.
        """

    def IsDefaultStyleShowing(self) -> bool:
        """
        IsDefaultStyleShowing() -> bool
        
        Returns true if the user has recently set the default style without
        moving the caret, and therefore the UI needs to reflect the default
        style and not the style at the caret.
        """

    def SetAndShowDefaultStyle(self, attr: RichTextAttr) -> None:
        """
        SetAndShowDefaultStyle(attr) -> None
        
        Sets attr as the default style and tells the control that the UI
        should reflect this attribute until the user moves the caret.
        """

    def GetFirstVisiblePoint(self) -> wx.Point:
        """
        GetFirstVisiblePoint() -> wx.Point
        
        Returns the first visible point in the window.
        """

    def GetValue(self) -> str:
        """
        GetValue() -> str
        
        Returns the content of the entire control as a string.
        """

    def SetValue(self, value: str) -> None:
        """
        SetValue(value) -> None
        
        Replaces existing content with the given text.
        """

    def ProcessBackKey(self, event: wx.KeyEvent, flags: int) -> bool:
        """
        ProcessBackKey(event, flags) -> bool
        
        Processes the back key.
        """

    def FindRangeForList(self, pos: int, isNumberedList: bool) -> RichTextRange:
        """
        FindRangeForList(pos, isNumberedList) -> RichTextRange
        
        Given a character position at which there is a list style, find the
        range encompassing the same list style by looking backwards and
        forwards.
        """

    def SetCaretPositionAfterClick(self, container: RichTextParagraphLayoutBox, position: int, hitTestFlags: int, extendSelection: bool=False) -> bool:
        """
        SetCaretPositionAfterClick(container, position, hitTestFlags, extendSelection=False) -> bool
        
        Sets up the caret for the given position and container, after a mouse
        click.
        """

    def FindCaretPositionForCharacterPosition(self, position: int, hitTestFlags: int, container: RichTextParagraphLayoutBox, caretLineStart: bool) -> int:
        """
        FindCaretPositionForCharacterPosition(position, hitTestFlags, container, caretLineStart) -> int
        
        Find the caret position for the combination of hit-test flags and
        character position.
        """

    def ProcessMouseMovement(self, container: RichTextParagraphLayoutBox, obj: RichTextObject, position: int, pos: wx.Point) -> bool:
        """
        ProcessMouseMovement(container, obj, position, pos) -> bool
        
        Processes mouse movement in order to change the cursor.
        """

    def DoGetValue(self) -> str:
        """
        DoGetValue() -> str
        """

    @overload
    def ProcessDelayedImageLoading(self, screenRect: wx.Rect, box: RichTextParagraphLayoutBox, loadCount: int) -> bool:
        ...

    @overload
    def ProcessDelayedImageLoading(self, refresh: bool) -> bool:
        """
        ProcessDelayedImageLoading(refresh) -> bool
        ProcessDelayedImageLoading(screenRect, box, loadCount) -> bool
        
        Do delayed image loading and garbage-collect other images.
        """

    def RequestDelayedImageProcessing(self) -> None:
        """
        RequestDelayedImageProcessing() -> None
        
        Request delayed image processing.
        """

    def OnTimer(self, event: wx.TimerEvent) -> None:
        """
        OnTimer(event) -> None
        
        Respond to timer events.
        """

    @staticmethod
    def GetAvailableFontNames() -> List[str]:
        """
        GetAvailableFontNames() -> List[str]
        
        Font names take a long time to retrieve, so cache them (on demand).
        """

    @staticmethod
    def ClearAvailableFontNames() -> None:
        """
        ClearAvailableFontNames() -> None
        
        Clears the cache of available font names.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    def GetDefaultStyle(self):
        """
        
        """

    @overload
    def SetMargins(self, left: int, top: int=-1) -> bool:
        ...

    @overload
    def SetMargins(self, pt: Point) -> bool:
        """
        SetMargins(pt) -> bool
        SetMargins(left, top=-1) -> bool
        
        Attempts to set the control margins.
        """

    @overload
    def AutoComplete(self, completer: TextCompleter) -> bool:
        ...

    @overload
    def AutoComplete(self, choices: List[str]) -> bool:
        """
        AutoComplete(choices) -> bool
        AutoComplete(completer) -> bool
        
        Call this function to enable auto-completion of the text typed in a
        single-line text control using the given choices.
        """

    def AutoCompleteFileNames(self) -> bool:
        """
        AutoCompleteFileNames() -> bool
        
        Call this function to enable auto-completion of the text typed in a
        single-line text control using all valid file system paths.
        """

    def AutoCompleteDirectories(self) -> bool:
        """
        AutoCompleteDirectories() -> bool
        
        Call this function to enable auto-completion of the text using the
        file system directories.
        """

    def ChangeValue(self, value: str) -> None:
        """
        ChangeValue(value) -> None
        
        Sets the new text control value.
        """

    def ForceUpper(self) -> None:
        """
        ForceUpper() -> None
        
        Convert all text entered into the control to upper case.
        """

    def IsEmpty(self) -> bool:
        """
        IsEmpty() -> bool
        
        Returns true if the control is currently empty.
        """

    def SetHint(self, hint: str) -> bool:
        """
        SetHint(hint) -> bool
        
        Sets a hint shown in an empty unfocused text control.
        """

    def GetHint(self) -> str:
        """
        GetHint() -> str
        
        Returns the current hint string.
        """

    def GetMargins(self) -> Point:
        """
        GetMargins() -> Point
        
        Returns the margins used by the control.
        """
    @property
    def BasicStyle(self) -> RichTextAttr: ...
    @BasicStyle.setter
    def BasicStyle(self, value: RichTextAttr, /) -> None: ...
    @property
    def Buffer(self) -> RichTextBuffer: ...
    @property
    def CaretAtLineStart(self) -> bool: ...
    @CaretAtLineStart.setter
    def CaretAtLineStart(self, value: bool, /) -> None: ...
    @property
    def CaretPosition(self) -> int: ...
    @CaretPosition.setter
    def CaretPosition(self, value: int, /) -> None: ...
    @property
    def CaretPositionForDefaultStyle(self) -> int: ...
    @CaretPositionForDefaultStyle.setter
    def CaretPositionForDefaultStyle(self, value: int, /) -> None: ...
    @property
    def CommandProcessor(self) -> wx.CommandProcessor: ...
    @property
    def ContextMenu(self) -> wx.Menu: ...
    @ContextMenu.setter
    def ContextMenu(self, value: wx.Menu, /) -> None: ...
    @property
    def ContextMenuPropertiesInfo(self) -> RichTextContextMenuPropertiesInfo: ...
    @property
    def DefaultStyle(self) -> wx.TextAttr: ...
    @DefaultStyle.setter
    def DefaultStyle(self, value: wx.TextAttr, /) -> None: ...
    @property
    def DefaultStyleEx(self) -> RichTextAttr: ...
    @property
    def DelayedImageLoading(self) -> bool: ...
    @property
    def DelayedImageProcessingRequired(self) -> bool: ...
    @DelayedImageProcessingRequired.setter
    def DelayedImageProcessingRequired(self, value: bool, /) -> None: ...
    @property
    def DelayedImageProcessingTime(self) -> int: ...
    @DelayedImageProcessingTime.setter
    def DelayedImageProcessingTime(self, value: int, /) -> None: ...
    @property
    def DelayedLayoutThreshold(self) -> int: ...
    @DelayedLayoutThreshold.setter
    def DelayedLayoutThreshold(self, value: int, /) -> None: ...
    @property
    def DimensionScale(self) -> float: ...
    @DimensionScale.setter
    def DimensionScale(self, value: float, /) -> None: ...
    @property
    def DragStartPoint(self) -> wx.Point: ...
    @DragStartPoint.setter
    def DragStartPoint(self, value: wx.Point, /) -> None: ...
    @property
    def DragStartTime(self) -> wx.DateTime: ...
    @DragStartTime.setter
    def DragStartTime(self, value: wx.DateTime, /) -> None: ...
    @property
    def Dragging(self) -> bool: ...
    @Dragging.setter
    def Dragging(self, value: bool, /) -> None: ...
    @property
    def Filename(self) -> str: ...
    @Filename.setter
    def Filename(self, value: str, /) -> None: ...
    @property
    def FirstVisiblePoint(self) -> wx.Point: ...
    @property
    def FirstVisiblePosition(self) -> int: ...
    @property
    def FocusObject(self) -> RichTextParagraphLayoutBox: ...
    @FocusObject.setter
    def FocusObject(self, value: RichTextParagraphLayoutBox, /) -> None: ...
    @property
    def FontScale(self) -> float: ...
    @FontScale.setter
    def FontScale(self, value: float, /) -> None: ...
    @property
    def FullLayoutRequired(self) -> bool: ...
    @FullLayoutRequired.setter
    def FullLayoutRequired(self, value: bool, /) -> None: ...
    @property
    def FullLayoutSavedPosition(self) -> int: ...
    @FullLayoutSavedPosition.setter
    def FullLayoutSavedPosition(self, value: int, /) -> None: ...
    @property
    def FullLayoutTime(self) -> int: ...
    @FullLayoutTime.setter
    def FullLayoutTime(self, value: int, /) -> None: ...
    @property
    def HandlerFlags(self) -> int: ...
    @HandlerFlags.setter
    def HandlerFlags(self, value: int, /) -> None: ...
    @property
    def Hint(self) -> str: ...
    @Hint.setter
    def Hint(self, value: str, /) -> None: ...
    @property
    def ImagesEnabled(self) -> bool: ...
    @property
    def InsertionPoint(self) -> int: ...
    @InsertionPoint.setter
    def InsertionPoint(self, value: int, /) -> None: ...
    @property
    def InternalSelectionRange(self) -> RichTextRange: ...
    @InternalSelectionRange.setter
    def InternalSelectionRange(self, value: RichTextRange, /) -> None: ...
    @property
    def LastPosition(self) -> TextPos: ...
    @property
    def Margins(self) -> Point: ...
    @Margins.setter
    def Margins(self, value: Point, /) -> None: ...
    @property
    def NumberOfLines(self) -> int: ...
    @property
    def PreDrag(self) -> bool: ...
    @PreDrag.setter
    def PreDrag(self, value: bool, /) -> None: ...
    @property
    def Scale(self) -> float: ...
    @Scale.setter
    def Scale(self, value: float, /) -> None: ...
    @property
    def Selection(self) -> int: ...
    @Selection.setter
    def Selection(self, value: int, /) -> None: ...
    @property
    def SelectionAnchor(self) -> int: ...
    @SelectionAnchor.setter
    def SelectionAnchor(self, value: int, /) -> None: ...
    @property
    def SelectionAnchorObject(self) -> RichTextObject: ...
    @SelectionAnchorObject.setter
    def SelectionAnchorObject(self, value: RichTextObject, /) -> None: ...
    @property
    def SelectionRange(self) -> RichTextRange: ...
    @SelectionRange.setter
    def SelectionRange(self, value: RichTextRange, /) -> None: ...
    @property
    def StringSelection(self) -> str: ...
    @property
    def StyleSheet(self) -> RichTextStyleSheet: ...
    @StyleSheet.setter
    def StyleSheet(self, value: RichTextStyleSheet, /) -> None: ...
    @property
    def TextCursor(self) -> wx.Cursor: ...
    @TextCursor.setter
    def TextCursor(self, value: wx.Cursor, /) -> None: ...
    @property
    def URLCursor(self) -> wx.Cursor: ...
    @URLCursor.setter
    def URLCursor(self, value: wx.Cursor, /) -> None: ...
    @property
    def Value(self) -> str: ...
    @Value.setter
    def Value(self, value: str, /) -> None: ...
    @property
    def VerticalScrollbarEnabled(self) -> bool: ...
    @property
    def VirtualAttributesEnabled(self) -> bool: ...

    def DoGetBestSize(self) -> wx.Size:
        """
        DoGetBestSize() -> wx.Size
        
        Currently this simply returns wxSize(10, 10).
        """

    def DoThaw(self) -> None:
        """
        DoThaw() -> None
        """
# end of class RichTextCtrl


class RichTextEvent(wx.NotifyEvent):
    """
    RichTextEvent(commandType=wx.wxEVT_NULL, winid=0) -> None
    RichTextEvent(event) -> None
    
    This is the event class for wxRichTextCtrl notifications.
    """

    @overload
    def __init__(self, event: RichTextEvent) -> None:
        ...

    @overload
    def __init__(self, commandType: EventType=wx.wxEVT_NULL, winid: int=0) -> None:
        """
        RichTextEvent(commandType=wx.wxEVT_NULL, winid=0) -> None
        RichTextEvent(event) -> None
        
        This is the event class for wxRichTextCtrl notifications.
        """

    def GetPosition(self) -> int:
        """
        GetPosition() -> int
        
        Returns the buffer position at which the event occurred.
        """

    def SetPosition(self, pos: int) -> None:
        """
        SetPosition(pos) -> None
        
        Sets the buffer position variable.
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        
        Returns flags indicating modifier keys pressed.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Sets flags indicating modifier keys pressed.
        """

    def GetOldStyleSheet(self) -> RichTextStyleSheet:
        """
        GetOldStyleSheet() -> RichTextStyleSheet
        
        Returns the old style sheet.
        """

    def SetOldStyleSheet(self, sheet: RichTextStyleSheet) -> None:
        """
        SetOldStyleSheet(sheet) -> None
        
        Sets the old style sheet variable.
        """

    def GetNewStyleSheet(self) -> RichTextStyleSheet:
        """
        GetNewStyleSheet() -> RichTextStyleSheet
        
        Returns the new style sheet.
        """

    def SetNewStyleSheet(self, sheet: RichTextStyleSheet) -> None:
        """
        SetNewStyleSheet(sheet) -> None
        
        Sets the new style sheet variable.
        """

    def GetRange(self) -> RichTextRange:
        """
        GetRange() -> RichTextRange
        
        Gets the range for the current operation.
        """

    def SetRange(self, range: RichTextRange) -> None:
        """
        SetRange(range) -> None
        
        Sets the range variable.
        """

    def GetCharacter(self) -> str:
        """
        GetCharacter() -> str
        
        Returns the character pressed, within a wxEVT_RICHTEXT_CHARACTER
        event.
        """

    def SetCharacter(self, ch: str) -> None:
        """
        SetCharacter(ch) -> None
        
        Sets the character variable.
        """

    def GetContainer(self) -> RichTextParagraphLayoutBox:
        """
        GetContainer() -> RichTextParagraphLayoutBox
        
        Returns the container for which the event is relevant.
        """

    def SetContainer(self, container: RichTextParagraphLayoutBox) -> None:
        """
        SetContainer(container) -> None
        
        Sets the container for which the event is relevant.
        """

    def GetOldContainer(self) -> RichTextParagraphLayoutBox:
        """
        GetOldContainer() -> RichTextParagraphLayoutBox
        
        Returns the old container, for a focus change event.
        """

    def SetOldContainer(self, container: RichTextParagraphLayoutBox) -> None:
        """
        SetOldContainer(container) -> None
        
        Sets the old container, for a focus change event.
        """

    def Clone(self) -> wx.Event:
        """
        Clone() -> wx.Event
        
        Returns a copy of the event.
        """
    @property
    def Character(self) -> str: ...
    @Character.setter
    def Character(self, value: str, /) -> None: ...
    @property
    def Container(self) -> RichTextParagraphLayoutBox: ...
    @Container.setter
    def Container(self, value: RichTextParagraphLayoutBox, /) -> None: ...
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def NewStyleSheet(self) -> RichTextStyleSheet: ...
    @NewStyleSheet.setter
    def NewStyleSheet(self, value: RichTextStyleSheet, /) -> None: ...
    @property
    def OldContainer(self) -> RichTextParagraphLayoutBox: ...
    @OldContainer.setter
    def OldContainer(self, value: RichTextParagraphLayoutBox, /) -> None: ...
    @property
    def OldStyleSheet(self) -> RichTextStyleSheet: ...
    @OldStyleSheet.setter
    def OldStyleSheet(self, value: RichTextStyleSheet, /) -> None: ...
    @property
    def Position(self) -> int: ...
    @Position.setter
    def Position(self, value: int, /) -> None: ...
    @property
    def Range(self) -> RichTextRange: ...
    @Range.setter
    def Range(self, value: RichTextRange, /) -> None: ...
# end of class RichTextEvent


EVT_RICHTEXT_LEFT_CLICK = wx.PyEventBinder(wxEVT_RICHTEXT_LEFT_CLICK)
EVT_RICHTEXT_RIGHT_CLICK = wx.PyEventBinder(wxEVT_RICHTEXT_RIGHT_CLICK)
EVT_RICHTEXT_MIDDLE_CLICK = wx.PyEventBinder(wxEVT_RICHTEXT_MIDDLE_CLICK)
EVT_RICHTEXT_LEFT_DCLICK = wx.PyEventBinder(wxEVT_RICHTEXT_LEFT_DCLICK)
EVT_RICHTEXT_RETURN = wx.PyEventBinder(wxEVT_RICHTEXT_RETURN)
EVT_RICHTEXT_CHARACTER = wx.PyEventBinder(wxEVT_RICHTEXT_CHARACTER)
EVT_RICHTEXT_DELETE = wx.PyEventBinder(wxEVT_RICHTEXT_DELETE)

EVT_RICHTEXT_STYLESHEET_CHANGING = wx.PyEventBinder(wxEVT_RICHTEXT_STYLESHEET_CHANGING)
EVT_RICHTEXT_STYLESHEET_CHANGED = wx.PyEventBinder(wxEVT_RICHTEXT_STYLESHEET_CHANGED)
EVT_RICHTEXT_STYLESHEET_REPLACING = wx.PyEventBinder(wxEVT_RICHTEXT_STYLESHEET_REPLACING)
EVT_RICHTEXT_STYLESHEET_REPLACED = wx.PyEventBinder(wxEVT_RICHTEXT_STYLESHEET_REPLACED)

EVT_RICHTEXT_CONTENT_INSERTED = wx.PyEventBinder(wxEVT_RICHTEXT_CONTENT_INSERTED)
EVT_RICHTEXT_CONTENT_DELETED = wx.PyEventBinder(wxEVT_RICHTEXT_CONTENT_DELETED)
EVT_RICHTEXT_STYLE_CHANGED = wx.PyEventBinder(wxEVT_RICHTEXT_STYLE_CHANGED)
EVT_RICHTEXT_STYLE_CHANGED = wx.PyEventBinder(wxEVT_RICHTEXT_PROPERTIES_CHANGED)
EVT_RICHTEXT_SELECTION_CHANGED = wx.PyEventBinder(wxEVT_RICHTEXT_SELECTION_CHANGED)
EVT_RICHTEXT_BUFFER_RESET = wx.PyEventBinder(wxEVT_RICHTEXT_BUFFER_RESET)
EVT_RICHTEXT_FOCUS_OBJECT_CHANGED = wx.PyEventBinder(wxEVT_RICHTEXT_FOCUS_OBJECT_CHANGED)
#-- end-richtextctrl --#
#-- begin-richtexthtml --#

class RichTextHTMLHandler(RichTextFileHandler):
    """
    RichTextHTMLHandler(name="HTML", ext="html", type=RICHTEXT_TYPE_HTML) -> None
    
    Handles HTML output (only) for wxRichTextCtrl content.
    """

    def __init__(self, name: str="HTML", ext: str="html", type: int=RICHTEXT_TYPE_HTML) -> None:
        """
        RichTextHTMLHandler(name="HTML", ext="html", type=RICHTEXT_TYPE_HTML) -> None
        
        Handles HTML output (only) for wxRichTextCtrl content.
        """

    def ClearTemporaryImageLocations(self) -> None:
        """
        ClearTemporaryImageLocations() -> None
        
        Clears the image locations generated by the last operation.
        """

    @overload
    @staticmethod
    def DeleteTemporaryImages(flags: int, imageLocations: List[str]) -> bool:
        ...

    @overload
    def DeleteTemporaryImages(self) -> bool:
        """
        DeleteTemporaryImages() -> bool
        DeleteTemporaryImages(flags, imageLocations) -> bool
        
        Deletes the in-memory or temporary files generated by the last
        operation.
        """

    def GetFontSizeMapping(self) -> List[int]:
        """
        GetFontSizeMapping() -> List[int]
        
        Returns the mapping for converting point sizes to HTML font sizes.
        """

    def GetTempDir(self) -> str:
        """
        GetTempDir() -> str
        
        Returns the directory used to store temporary image files.
        """

    def GetTemporaryImageLocations(self) -> List[str]:
        """
        GetTemporaryImageLocations() -> List[str]
        
        Returns the image locations for the last operation.
        """

    def SetFontSizeMapping(self, fontSizeMapping: List[int]) -> None:
        """
        SetFontSizeMapping(fontSizeMapping) -> None
        
        Sets the mapping for converting point sizes to HTML font sizes.
        """

    def SetTempDir(self, tempDir: str) -> None:
        """
        SetTempDir(tempDir) -> None
        
        Sets the directory for storing temporary files.
        """

    def SetTemporaryImageLocations(self, locations: List[str]) -> None:
        """
        SetTemporaryImageLocations(locations) -> None
        
        Sets the list of image locations generated by the last operation.
        """

    @staticmethod
    def SetFileCounter(counter: int) -> None:
        """
        SetFileCounter(counter) -> None
        
        Reset the file counter, in case, for example, the same names are
        required each time.
        """
    @property
    def FontSizeMapping(self) -> List[int]: ...
    @FontSizeMapping.setter
    def FontSizeMapping(self, value: List[int], /) -> None: ...
    @property
    def TempDir(self) -> str: ...
    @TempDir.setter
    def TempDir(self, value: str, /) -> None: ...
    @property
    def TemporaryImageLocations(self) -> List[str]: ...
    @TemporaryImageLocations.setter
    def TemporaryImageLocations(self, value: List[str], /) -> None: ...
# end of class RichTextHTMLHandler

#-- end-richtexthtml --#
#-- begin-richtextxml --#

class RichTextXMLHandler(RichTextFileHandler):
    """
    RichTextXMLHandler(name="XML", ext="xml", type=RICHTEXT_TYPE_XML) -> None
    
    A handler for loading and saving content in an XML format specific to
    wxRichTextBuffer.
    """

    def __init__(self, name: str="XML", ext: str="xml", type: int=RICHTEXT_TYPE_XML) -> None:
        """
        RichTextXMLHandler(name="XML", ext="xml", type=RICHTEXT_TYPE_XML) -> None
        
        A handler for loading and saving content in an XML format specific to
        wxRichTextBuffer.
        """

    def CanLoad(self) -> bool:
        """
        CanLoad() -> bool
        
        Returns true.
        """

    def CanSave(self) -> bool:
        """
        CanSave() -> bool
        
        Returns true.
        """

    def ExportXML(self, stream: wx.OutputStream, obj: RichTextObject, level: int) -> bool:
        """
        ExportXML(stream, obj, level) -> bool
        
        Recursively exports an object to the stream.
        """

    def ImportXML(self, buffer: RichTextBuffer, obj: RichTextObject, node: XmlNode) -> bool:
        """
        ImportXML(buffer, obj, node) -> bool
        
        Recursively imports an object.
        """

    @staticmethod
    def RegisterNodeName(nodeName: str, className: str) -> None:
        """
        RegisterNodeName(nodeName, className) -> None
        
        Call with XML node name, C++ class name so that wxRTC can read in the
        node.
        """

    @staticmethod
    def ClearNodeToClassMap() -> None:
        """
        ClearNodeToClassMap() -> None
        
        Cleans up the mapping between node name and C++ class.
        """
# end of class RichTextXMLHandler

#-- end-richtextxml --#
#-- begin-richtextprint --#

class _RichTextOddEvenPage(IntEnum):
    RICHTEXT_PAGE_ODD = auto()
    RICHTEXT_PAGE_EVEN = auto()
    RICHTEXT_PAGE_ALL = auto()
RichTextOddEvenPage: TypeAlias = Union[_RichTextOddEvenPage, int]
RICHTEXT_PAGE_ODD = _RichTextOddEvenPage.RICHTEXT_PAGE_ODD
RICHTEXT_PAGE_EVEN = _RichTextOddEvenPage.RICHTEXT_PAGE_EVEN
RICHTEXT_PAGE_ALL = _RichTextOddEvenPage.RICHTEXT_PAGE_ALL

class _RichTextPageLocation(IntEnum):
    RICHTEXT_PAGE_LEFT = auto()
    RICHTEXT_PAGE_CENTRE = auto()
    RICHTEXT_PAGE_RIGHT = auto()
RichTextPageLocation: TypeAlias = Union[_RichTextPageLocation, int]
RICHTEXT_PAGE_LEFT = _RichTextPageLocation.RICHTEXT_PAGE_LEFT
RICHTEXT_PAGE_CENTRE = _RichTextPageLocation.RICHTEXT_PAGE_CENTRE
RICHTEXT_PAGE_RIGHT = _RichTextPageLocation.RICHTEXT_PAGE_RIGHT

class RichTextHeaderFooterData(wx.Object):
    """
    RichTextHeaderFooterData() -> None
    RichTextHeaderFooterData(data) -> None
    
    This class represents header and footer data to be passed to the
    wxRichTextPrinting and wxRichTextPrintout classes.
    """

    @overload
    def __init__(self, data: RichTextHeaderFooterData) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RichTextHeaderFooterData() -> None
        RichTextHeaderFooterData(data) -> None
        
        This class represents header and footer data to be passed to the
        wxRichTextPrinting and wxRichTextPrintout classes.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Clears all text.
        """

    def Copy(self, data: RichTextHeaderFooterData) -> None:
        """
        Copy(data) -> None
        
        Copies the data.
        """

    def GetFont(self) -> wx.Font:
        """
        GetFont() -> wx.Font
        
        Returns the font specified for printing the header and footer.
        """

    def GetFooterMargin(self) -> int:
        """
        GetFooterMargin() -> int
        
        Returns the margin between the text and the footer.
        """

    def GetFooterText(self, page: RichTextOddEvenPage=RICHTEXT_PAGE_EVEN, location: RichTextPageLocation=RICHTEXT_PAGE_CENTRE) -> str:
        """
        GetFooterText(page=RICHTEXT_PAGE_EVEN, location=RICHTEXT_PAGE_CENTRE) -> str
        
        Returns the footer text on odd or even pages, and at a given position
        on the page (left, centre or right).
        """

    def GetHeaderMargin(self) -> int:
        """
        GetHeaderMargin() -> int
        
        Returns the margin between the text and the header.
        """

    def GetHeaderText(self, page: RichTextOddEvenPage=RICHTEXT_PAGE_EVEN, location: RichTextPageLocation=RICHTEXT_PAGE_CENTRE) -> str:
        """
        GetHeaderText(page=RICHTEXT_PAGE_EVEN, location=RICHTEXT_PAGE_CENTRE) -> str
        
        Returns the header text on odd or even pages, and at a given position
        on the page (left, centre or right).
        """

    def GetShowOnFirstPage(self) -> bool:
        """
        GetShowOnFirstPage() -> bool
        
        Returns true if the header and footer will be shown on the first page.
        """

    def GetText(self, headerFooter: int, page: RichTextOddEvenPage, location: RichTextPageLocation) -> str:
        """
        GetText(headerFooter, page, location) -> str
        
        Helper function for getting the header or footer text, odd or even
        pages, and at a given position on the page (left, centre or right).
        """

    def GetTextColour(self) -> wx.Colour:
        """
        GetTextColour() -> wx.Colour
        
        Returns the text colour for drawing the header and footer.
        """

    def Init(self) -> None:
        """
        Init() -> None
        
        Initialises the object.
        """

    def SetFont(self, font: wx.Font) -> None:
        """
        SetFont(font) -> None
        
        Sets the font for drawing the header and footer.
        """

    def SetFooterText(self, text: str, page: RichTextOddEvenPage=RICHTEXT_PAGE_ALL, location: RichTextPageLocation=RICHTEXT_PAGE_CENTRE) -> None:
        """
        SetFooterText(text, page=RICHTEXT_PAGE_ALL, location=RICHTEXT_PAGE_CENTRE) -> None
        
        Sets the footer text on odd or even pages, and at a given position on
        the page (left, centre or right).
        """

    def SetHeaderText(self, text: str, page: RichTextOddEvenPage=RICHTEXT_PAGE_ALL, location: RichTextPageLocation=RICHTEXT_PAGE_CENTRE) -> None:
        """
        SetHeaderText(text, page=RICHTEXT_PAGE_ALL, location=RICHTEXT_PAGE_CENTRE) -> None
        
        Sets the header text on odd or even pages, and at a given position on
        the page (left, centre or right).
        """

    def SetMargins(self, headerMargin: int, footerMargin: int) -> None:
        """
        SetMargins(headerMargin, footerMargin) -> None
        
        Sets the margins between text and header or footer, in tenths of a
        millimeter.
        """

    def SetShowOnFirstPage(self, showOnFirstPage: bool) -> None:
        """
        SetShowOnFirstPage(showOnFirstPage) -> None
        
        Pass true to show the header or footer on first page (the default).
        """

    def SetText(self, text: str, headerFooter: int, page: RichTextOddEvenPage, location: RichTextPageLocation) -> None:
        """
        SetText(text, headerFooter, page, location) -> None
        
        Helper function for setting the header or footer text, odd or even
        pages, and at a given position on the page (left, centre or right).
        """

    def SetTextColour(self, col: wx.Colour) -> None:
        """
        SetTextColour(col) -> None
        
        Sets the text colour for drawing the header and footer.
        """
    @property
    def Font(self) -> wx.Font: ...
    @Font.setter
    def Font(self, value: wx.Font, /) -> None: ...
    @property
    def FooterMargin(self) -> int: ...
    @property
    def FooterText(self) -> str: ...
    @FooterText.setter
    def FooterText(self, value: str, /) -> None: ...
    @property
    def HeaderMargin(self) -> int: ...
    @property
    def HeaderText(self) -> str: ...
    @HeaderText.setter
    def HeaderText(self, value: str, /) -> None: ...
    @property
    def ShowOnFirstPage(self) -> bool: ...
    @ShowOnFirstPage.setter
    def ShowOnFirstPage(self, value: bool, /) -> None: ...
    @property
    def TextColour(self) -> wx.Colour: ...
    @TextColour.setter
    def TextColour(self, value: wx.Colour, /) -> None: ...
# end of class RichTextHeaderFooterData


class RichTextPrintout(wx.Printout):
    """
    RichTextPrintout(title="Printout") -> None
    
    This class implements print layout for wxRichTextBuffer.
    """

    def __init__(self, title: str="Printout") -> None:
        """
        RichTextPrintout(title="Printout") -> None
        
        This class implements print layout for wxRichTextBuffer.
        """

    def CalculateScaling(self, dc: wx.DC, textRect: wx.Rect, headerRect: wx.Rect, footerRect: wx.Rect) -> None:
        """
        CalculateScaling(dc, textRect, headerRect, footerRect) -> None
        
        Calculates scaling and text, header and footer rectangles.
        """

    def GetHeaderFooterData(self) -> RichTextHeaderFooterData:
        """
        GetHeaderFooterData() -> RichTextHeaderFooterData
        
        Returns the header and footer data associated with the printout.
        """

    def GetPageInfo(self) -> Tuple[int, int, int, int]:
        """
        GetPageInfo() -> Tuple[int, int, int, int]
        
        Gets the page information.
        """

    def GetRichTextBuffer(self) -> RichTextBuffer:
        """
        GetRichTextBuffer() -> RichTextBuffer
        
        Returns a pointer to the buffer being rendered.
        """

    def HasPage(self, page: int) -> bool:
        """
        HasPage(page) -> bool
        
        Returns true if the given page exists in the printout.
        """

    def OnPreparePrinting(self) -> None:
        """
        OnPreparePrinting() -> None
        
        Prepares for printing, laying out the buffer and calculating
        pagination.
        """

    def OnPrintPage(self, page: int) -> bool:
        """
        OnPrintPage(page) -> bool
        
        Does the actual printing for this page.
        """

    def SetHeaderFooterData(self, data: RichTextHeaderFooterData) -> None:
        """
        SetHeaderFooterData(data) -> None
        
        Sets the header and footer data associated with the printout.
        """

    def SetMargins(self, top: int=254, bottom: int=254, left: int=254, right: int=254) -> None:
        """
        SetMargins(top=254, bottom=254, left=254, right=254) -> None
        
        Sets margins in 10ths of millimetre.
        """

    def SetRichTextBuffer(self, buffer: RichTextBuffer) -> None:
        """
        SetRichTextBuffer(buffer) -> None
        
        Sets the buffer to print.
        """
    @property
    def HeaderFooterData(self) -> RichTextHeaderFooterData: ...
    @HeaderFooterData.setter
    def HeaderFooterData(self, value: RichTextHeaderFooterData, /) -> None: ...
    @property
    def RichTextBuffer(self) -> RichTextBuffer: ...
    @RichTextBuffer.setter
    def RichTextBuffer(self, value: RichTextBuffer, /) -> None: ...
# end of class RichTextPrintout


class RichTextPrinting(wx.Object):
    """
    RichTextPrinting(name="Printing", parentWindow=None) -> None
    
    This class provides a simple interface for performing wxRichTextBuffer
    printing and previewing.
    """

    def __init__(self, name: str="Printing", parentWindow: Optional[wx.Window]=None) -> None:
        """
        RichTextPrinting(name="Printing", parentWindow=None) -> None
        
        This class provides a simple interface for performing wxRichTextBuffer
        printing and previewing.
        """

    def GetFooterText(self, page: RichTextOddEvenPage=RICHTEXT_PAGE_EVEN, location: RichTextPageLocation=RICHTEXT_PAGE_CENTRE) -> str:
        """
        GetFooterText(page=RICHTEXT_PAGE_EVEN, location=RICHTEXT_PAGE_CENTRE) -> str
        
        A convenience function to get the footer text.
        """

    def GetHeaderFooterData(self) -> RichTextHeaderFooterData:
        """
        GetHeaderFooterData() -> RichTextHeaderFooterData
        
        Returns the internal wxRichTextHeaderFooterData object.
        """

    def GetHeaderText(self, page: RichTextOddEvenPage=RICHTEXT_PAGE_EVEN, location: RichTextPageLocation=RICHTEXT_PAGE_CENTRE) -> str:
        """
        GetHeaderText(page=RICHTEXT_PAGE_EVEN, location=RICHTEXT_PAGE_CENTRE) -> str
        
        A convenience function to get the header text.
        """

    def GetPageSetupData(self) -> wx.PageSetupDialogData:
        """
        GetPageSetupData() -> wx.PageSetupDialogData
        
        Returns a pointer to the internal page setup data.
        """

    def GetParentWindow(self) -> wx.Window:
        """
        GetParentWindow() -> wx.Window
        
        Returns the parent window to be used for the preview window and
        printing wait dialog.
        """

    def GetPreviewRect(self) -> wx.Rect:
        """
        GetPreviewRect() -> wx.Rect
        
        Returns the dimensions to be used for the preview window.
        """

    def GetPrintData(self) -> wx.PrintData:
        """
        GetPrintData() -> wx.PrintData
        
        Returns a pointer to the internal print data.
        """

    def GetTitle(self) -> str:
        """
        GetTitle() -> str
        
        Returns the title of the preview window or printing wait caption.
        """

    def PageSetup(self) -> None:
        """
        PageSetup() -> None
        
        Shows the page setup dialog.
        """

    def PreviewBuffer(self, buffer: RichTextBuffer) -> bool:
        """
        PreviewBuffer(buffer) -> bool
        
        Shows a preview window for the given buffer.
        """

    def PreviewFile(self, richTextFile: str) -> bool:
        """
        PreviewFile(richTextFile) -> bool
        
        Shows a preview window for the given file.
        """

    def PrintBuffer(self, buffer: RichTextBuffer, showPrintDialog: bool=True) -> bool:
        """
        PrintBuffer(buffer, showPrintDialog=True) -> bool
        
        Prints the given buffer.
        """

    def PrintFile(self, richTextFile: str, showPrintDialog: bool=True) -> bool:
        """
        PrintFile(richTextFile, showPrintDialog=True) -> bool
        
        Prints the given file.
        """

    def SetFooterText(self, text: str, page: RichTextOddEvenPage=RICHTEXT_PAGE_ALL, location: RichTextPageLocation=RICHTEXT_PAGE_CENTRE) -> None:
        """
        SetFooterText(text, page=RICHTEXT_PAGE_ALL, location=RICHTEXT_PAGE_CENTRE) -> None
        
        A convenience function to set the footer text.
        """

    def SetHeaderFooterData(self, data: RichTextHeaderFooterData) -> None:
        """
        SetHeaderFooterData(data) -> None
        
        Sets the internal wxRichTextHeaderFooterData object.
        """

    def SetHeaderFooterFont(self, font: wx.Font) -> None:
        """
        SetHeaderFooterFont(font) -> None
        
        Sets the wxRichTextHeaderFooterData font.
        """

    def SetHeaderFooterTextColour(self, colour: wx.Colour) -> None:
        """
        SetHeaderFooterTextColour(colour) -> None
        
        Sets the wxRichTextHeaderFooterData text colour.
        """

    def SetHeaderText(self, text: str, page: RichTextOddEvenPage=RICHTEXT_PAGE_ALL, location: RichTextPageLocation=RICHTEXT_PAGE_CENTRE) -> None:
        """
        SetHeaderText(text, page=RICHTEXT_PAGE_ALL, location=RICHTEXT_PAGE_CENTRE) -> None
        
        A convenience function to set the header text.
        """

    def SetPageSetupData(self, pageSetupData: wx.PageSetupDialogData) -> None:
        """
        SetPageSetupData(pageSetupData) -> None
        
        Sets the page setup data.
        """

    def SetParentWindow(self, parent: wx.Window) -> None:
        """
        SetParentWindow(parent) -> None
        
        Sets the parent window to be used for the preview window and printing
        wait dialog.
        """

    def SetPreviewRect(self, rect: wx.Rect) -> None:
        """
        SetPreviewRect(rect) -> None
        
        Sets the dimensions to be used for the preview window.
        """

    def SetPrintData(self, printData: wx.PrintData) -> None:
        """
        SetPrintData(printData) -> None
        
        Sets the print data.
        """

    def SetShowOnFirstPage(self, show: bool) -> None:
        """
        SetShowOnFirstPage(show) -> None
        
        Pass true to show the header and footer on the first page.
        """

    def SetTitle(self, title: str) -> None:
        """
        SetTitle(title) -> None
        
        Pass the title of the preview window or printing wait caption.
        """
    @property
    def FooterText(self) -> str: ...
    @FooterText.setter
    def FooterText(self, value: str, /) -> None: ...
    @property
    def HeaderFooterData(self) -> RichTextHeaderFooterData: ...
    @HeaderFooterData.setter
    def HeaderFooterData(self, value: RichTextHeaderFooterData, /) -> None: ...
    @property
    def HeaderText(self) -> str: ...
    @HeaderText.setter
    def HeaderText(self, value: str, /) -> None: ...
    @property
    def PageSetupData(self) -> wx.PageSetupDialogData: ...
    @PageSetupData.setter
    def PageSetupData(self, value: wx.PageSetupDialogData, /) -> None: ...
    @property
    def ParentWindow(self) -> wx.Window: ...
    @ParentWindow.setter
    def ParentWindow(self, value: wx.Window, /) -> None: ...
    @property
    def PreviewRect(self) -> wx.Rect: ...
    @PreviewRect.setter
    def PreviewRect(self, value: wx.Rect, /) -> None: ...
    @property
    def PrintData(self) -> wx.PrintData: ...
    @PrintData.setter
    def PrintData(self, value: wx.PrintData, /) -> None: ...
    @property
    def Title(self) -> str: ...
    @Title.setter
    def Title(self, value: str, /) -> None: ...
# end of class RichTextPrinting

#-- end-richtextprint --#
#-- begin-richtextstyles --#

class RichTextStyleListCtrl(wx.Control):
    """
    RichTextStyleListCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
    RichTextStyleListCtrl() -> None
    
    This class incorporates a wxRichTextStyleListBox and a choice control
    that allows the user to select the category of style to view.
    """

    @overload
    def __init__(self) -> None:
        ...

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> None:
        """
        RichTextStyleListCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
        RichTextStyleListCtrl() -> None
        
        This class incorporates a wxRichTextStyleListBox and a choice control
        that allows the user to select the category of style to view.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> bool
        
        Creates the windows.
        """

    def GetRichTextCtrl(self) -> RichTextCtrl:
        """
        GetRichTextCtrl() -> RichTextCtrl
        
        Returns the associated rich text control, if any.
        """

    def GetStyleChoice(self) -> wx.Choice:
        """
        GetStyleChoice() -> wx.Choice
        
        Returns the wxChoice control used for selecting the style category.
        """

    def GetStyleListBox(self) -> RichTextStyleListBox:
        """
        GetStyleListBox() -> RichTextStyleListBox
        
        Returns the wxListBox control used to view the style list.
        """

    def GetStyleSheet(self) -> RichTextStyleSheet:
        """
        GetStyleSheet() -> RichTextStyleSheet
        
        Returns the associated style sheet, if any.
        """

    def GetStyleType(self) -> RichTextStyleListBox.wxRichTextStyleType:
        """
        GetStyleType() -> RichTextStyleListBox.wxRichTextStyleType
        
        Returns the type of style to show in the list box.
        """

    def SetRichTextCtrl(self, ctrl: RichTextCtrl) -> None:
        """
        SetRichTextCtrl(ctrl) -> None
        
        Associates the control with a wxRichTextCtrl.
        """

    def SetStyleSheet(self, styleSheet: RichTextStyleSheet) -> None:
        """
        SetStyleSheet(styleSheet) -> None
        
        Associates the control with a style sheet.
        """

    def SetStyleType(self, styleType: RichTextStyleListBox.wxRichTextStyleType) -> None:
        """
        SetStyleType(styleType) -> None
        
        Sets the style type to display.
        """

    def UpdateStyles(self) -> None:
        """
        UpdateStyles() -> None
        
        Updates the style list box.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def RichTextCtrl(self) -> RichTextCtrl: ...
    @RichTextCtrl.setter
    def RichTextCtrl(self, value: RichTextCtrl, /) -> None: ...
    @property
    def StyleChoice(self) -> wx.Choice: ...
    @property
    def StyleListBox(self) -> RichTextStyleListBox: ...
    @property
    def StyleSheet(self) -> RichTextStyleSheet: ...
    @StyleSheet.setter
    def StyleSheet(self, value: RichTextStyleSheet, /) -> None: ...
    @property
    def StyleType(self) -> RichTextStyleListBox.wxRichTextStyleType: ...
    @StyleType.setter
    def StyleType(self, value: RichTextStyleListBox.wxRichTextStyleType, /) -> None: ...
# end of class RichTextStyleListCtrl


class RichTextStyleListBox(wx.wx.html.HtmlListBox):
    """
    RichTextStyleListBox(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
    RichTextStyleListBox() -> None
    
    This is a listbox that can display the styles in a
    wxRichTextStyleSheet, and apply the selection to an associated
    wxRichTextCtrl.
    """

    class _RichTextStyleType(IntEnum):
        RICHTEXT_STYLE_ALL = auto()
        RICHTEXT_STYLE_PARAGRAPH = auto()
        RICHTEXT_STYLE_CHARACTER = auto()
        RICHTEXT_STYLE_LIST = auto()
        RICHTEXT_STYLE_BOX = auto()
    RichTextStyleType: TypeAlias = Union[_RichTextStyleType, int]
    RICHTEXT_STYLE_ALL = _RichTextStyleType.RICHTEXT_STYLE_ALL
    RICHTEXT_STYLE_PARAGRAPH = _RichTextStyleType.RICHTEXT_STYLE_PARAGRAPH
    RICHTEXT_STYLE_CHARACTER = _RichTextStyleType.RICHTEXT_STYLE_CHARACTER
    RICHTEXT_STYLE_LIST = _RichTextStyleType.RICHTEXT_STYLE_LIST
    RICHTEXT_STYLE_BOX = _RichTextStyleType.RICHTEXT_STYLE_BOX

    @overload
    def __init__(self) -> None:
        ...

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> None:
        """
        RichTextStyleListBox(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
        RichTextStyleListBox() -> None
        
        This is a listbox that can display the styles in a
        wxRichTextStyleSheet, and apply the selection to an associated
        wxRichTextCtrl.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> bool
        
        Creates the window.
        """

    def ApplyStyle(self, i: int) -> None:
        """
        ApplyStyle(i) -> None
        
        Applies the ith style to the associated rich text control.
        """

    def ConvertTenthsMMToPixels(self, dc: wx.DC, units: int) -> int:
        """
        ConvertTenthsMMToPixels(dc, units) -> int
        
        Converts units in tenths of a millimetre to device units.
        """

    def CreateHTML(self, styleDef: RichTextStyleDefinition) -> str:
        """
        CreateHTML(styleDef) -> str
        
        Creates a suitable HTML fragment for a definition.
        """

    def GetApplyOnSelection(self) -> bool:
        """
        GetApplyOnSelection() -> bool
        
        If the return value is true, clicking on a style name in the list will
        immediately apply the style to the associated rich text control.
        """

    def GetRichTextCtrl(self) -> RichTextCtrl:
        """
        GetRichTextCtrl() -> RichTextCtrl
        
        Returns the wxRichTextCtrl associated with this listbox.
        """

    def GetStyle(self, i: int) -> RichTextStyleDefinition:
        """
        GetStyle(i) -> RichTextStyleDefinition
        
        Gets a style for a listbox index.
        """

    def GetStyleSheet(self) -> RichTextStyleSheet:
        """
        GetStyleSheet() -> RichTextStyleSheet
        
        Returns the style sheet associated with this listbox.
        """

    def GetStyleType(self) -> RichTextStyleListBox.wxRichTextStyleType:
        """
        GetStyleType() -> RichTextStyleListBox.wxRichTextStyleType
        
        Returns the type of style to show in the list box.
        """

    def OnLeftDown(self, event: wx.MouseEvent) -> None:
        """
        OnLeftDown(event) -> None
        
        Implements left click behaviour, applying the clicked style to the
        wxRichTextCtrl.
        """

    def SetApplyOnSelection(self, applyOnSelection: bool) -> None:
        """
        SetApplyOnSelection(applyOnSelection) -> None
        
        If applyOnSelection is true, clicking on a style name in the list will
        immediately apply the style to the associated rich text control.
        """

    def SetRichTextCtrl(self, ctrl: RichTextCtrl) -> None:
        """
        SetRichTextCtrl(ctrl) -> None
        
        Associates the listbox with a wxRichTextCtrl.
        """

    def SetStyleSheet(self, styleSheet: RichTextStyleSheet) -> None:
        """
        SetStyleSheet(styleSheet) -> None
        
        Associates the control with a style sheet.
        """

    def SetStyleType(self, styleType: RichTextStyleListBox.wxRichTextStyleType) -> None:
        """
        SetStyleType(styleType) -> None
        
        Sets the style type to display.
        """

    def UpdateStyles(self) -> None:
        """
        UpdateStyles() -> None
        
        Updates the list from the associated style sheet.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ApplyOnSelection(self) -> bool: ...
    @ApplyOnSelection.setter
    def ApplyOnSelection(self, value: bool, /) -> None: ...
    @property
    def RichTextCtrl(self) -> RichTextCtrl: ...
    @RichTextCtrl.setter
    def RichTextCtrl(self, value: RichTextCtrl, /) -> None: ...
    @property
    def StyleSheet(self) -> RichTextStyleSheet: ...
    @StyleSheet.setter
    def StyleSheet(self, value: RichTextStyleSheet, /) -> None: ...
    @property
    def StyleType(self) -> RichTextStyleListBox.wxRichTextStyleType: ...
    @StyleType.setter
    def StyleType(self, value: RichTextStyleListBox.wxRichTextStyleType, /) -> None: ...

    def OnGetItem(self, n: int) -> str:
        """
        OnGetItem(n) -> str
        
        Returns the HTML for this item.
        """
# end of class RichTextStyleListBox


class RichTextStyleComboCtrl(wx.ComboCtrl):
    """
    RichTextStyleComboCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
    RichTextStyleComboCtrl() -> None
    
    This is a combo control that can display the styles in a
    wxRichTextStyleSheet, and apply the selection to an associated
    wxRichTextCtrl.
    """

    @overload
    def __init__(self) -> None:
        ...

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> None:
        """
        RichTextStyleComboCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
        RichTextStyleComboCtrl() -> None
        
        This is a combo control that can display the styles in a
        wxRichTextStyleSheet, and apply the selection to an associated
        wxRichTextCtrl.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> bool
        
        Creates the windows.
        """

    def GetRichTextCtrl(self) -> RichTextCtrl:
        """
        GetRichTextCtrl() -> RichTextCtrl
        
        Returns the wxRichTextCtrl associated with this control.
        """

    def GetStyleSheet(self) -> RichTextStyleSheet:
        """
        GetStyleSheet() -> RichTextStyleSheet
        
        Returns the style sheet associated with this control.
        """

    def SetRichTextCtrl(self, ctrl: RichTextCtrl) -> None:
        """
        SetRichTextCtrl(ctrl) -> None
        
        Associates the control with a wxRichTextCtrl.
        """

    def SetStyleSheet(self, styleSheet: RichTextStyleSheet) -> None:
        """
        SetStyleSheet(styleSheet) -> None
        
        Associates the control with a style sheet.
        """

    def UpdateStyles(self) -> None:
        """
        UpdateStyles() -> None
        
        Updates the combo control from the associated style sheet.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def RichTextCtrl(self) -> RichTextCtrl: ...
    @RichTextCtrl.setter
    def RichTextCtrl(self, value: RichTextCtrl, /) -> None: ...
    @property
    def StyleSheet(self) -> RichTextStyleSheet: ...
    @StyleSheet.setter
    def StyleSheet(self, value: RichTextStyleSheet, /) -> None: ...
# end of class RichTextStyleComboCtrl


class RichTextStyleDefinition(wx.Object):
    """
    RichTextStyleDefinition(name='') -> None
    
    This is a base class for paragraph and character styles.
    """

    def __init__(self, name: str='') -> None:
        """
        RichTextStyleDefinition(name='') -> None
        
        This is a base class for paragraph and character styles.
        """

    def GetBaseStyle(self) -> str:
        """
        GetBaseStyle() -> str
        
        Returns the style on which this style is based.
        """

    def GetDescription(self) -> str:
        """
        GetDescription() -> str
        
        Returns the style's description.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns the style name.
        """

    def GetStyle(self) -> RichTextAttr:
        """
        GetStyle() -> RichTextAttr
        """

    def GetStyleMergedWithBase(self, sheet: RichTextStyleSheet) -> RichTextAttr:
        """
        GetStyleMergedWithBase(sheet) -> RichTextAttr
        
        Returns the style attributes combined with the attributes of the
        specified base style, if any.
        """

    def SetBaseStyle(self, name: str) -> None:
        """
        SetBaseStyle(name) -> None
        
        Sets the name of the style that this style is based on.
        """

    def SetDescription(self, descr: str) -> None:
        """
        SetDescription(descr) -> None
        
        Sets the style description.
        """

    def SetName(self, name: str) -> None:
        """
        SetName(name) -> None
        
        Sets the name of the style.
        """

    def SetStyle(self, style: RichTextAttr) -> None:
        """
        SetStyle(style) -> None
        
        Sets the attributes for this style.
        """

    def GetProperties(self) -> RichTextProperties:
        """
        GetProperties() -> RichTextProperties
        
        Returns the definition's properties.
        """

    def SetProperties(self, props: RichTextProperties) -> None:
        """
        SetProperties(props) -> None
        
        Sets the definition's properties.
        """
    @property
    def BaseStyle(self) -> str: ...
    @BaseStyle.setter
    def BaseStyle(self, value: str, /) -> None: ...
    @property
    def Description(self) -> str: ...
    @Description.setter
    def Description(self, value: str, /) -> None: ...
    @property
    def Name(self) -> str: ...
    @Name.setter
    def Name(self, value: str, /) -> None: ...
    @property
    def Properties(self) -> RichTextProperties: ...
    @Properties.setter
    def Properties(self, value: RichTextProperties, /) -> None: ...
    @property
    def Style(self) -> RichTextAttr: ...
    @Style.setter
    def Style(self, value: RichTextAttr, /) -> None: ...
# end of class RichTextStyleDefinition


class RichTextParagraphStyleDefinition(RichTextStyleDefinition):
    """
    RichTextParagraphStyleDefinition(name='') -> None
    
    This class represents a paragraph style definition, usually added to a
    wxRichTextStyleSheet.
    """

    def __init__(self, name: str='') -> None:
        """
        RichTextParagraphStyleDefinition(name='') -> None
        
        This class represents a paragraph style definition, usually added to a
        wxRichTextStyleSheet.
        """

    def GetNextStyle(self) -> str:
        """
        GetNextStyle() -> str
        
        Returns the style that should normally follow this style.
        """

    def SetNextStyle(self, name: str) -> None:
        """
        SetNextStyle(name) -> None
        
        Sets the style that should normally follow this style.
        """
    @property
    def NextStyle(self) -> str: ...
    @NextStyle.setter
    def NextStyle(self, value: str, /) -> None: ...
# end of class RichTextParagraphStyleDefinition


class RichTextCharacterStyleDefinition(RichTextStyleDefinition):
    """
    RichTextCharacterStyleDefinition(name='') -> None
    
    This class represents a character style definition, usually added to a
    wxRichTextStyleSheet.
    """

    def __init__(self, name: str='') -> None:
        """
        RichTextCharacterStyleDefinition(name='') -> None
        
        This class represents a character style definition, usually added to a
        wxRichTextStyleSheet.
        """
# end of class RichTextCharacterStyleDefinition


class RichTextListStyleDefinition(RichTextParagraphStyleDefinition):
    """
    RichTextListStyleDefinition(name='') -> None
    
    This class represents a list style definition, usually added to a
    wxRichTextStyleSheet.
    """

    def __init__(self, name: str='') -> None:
        """
        RichTextListStyleDefinition(name='') -> None
        
        This class represents a list style definition, usually added to a
        wxRichTextStyleSheet.
        """

    def CombineWithParagraphStyle(self, indent: int, paraStyle: RichTextAttr, styleSheet: Optional[RichTextStyleSheet]=None) -> RichTextAttr:
        """
        CombineWithParagraphStyle(indent, paraStyle, styleSheet=None) -> RichTextAttr
        
        This function combines the given paragraph style with the list style's
        base attributes and level style matching the given indent, returning
        the combined attributes.
        """

    def FindLevelForIndent(self, indent: int) -> int:
        """
        FindLevelForIndent(indent) -> int
        
        This function finds the level (from 0 to 9) whose indentation
        attribute mostly closely matches indent (expressed in tenths of a
        millimetre).
        """

    def GetCombinedStyle(self, indent: int, styleSheet: Optional[RichTextStyleSheet]=None) -> RichTextAttr:
        """
        GetCombinedStyle(indent, styleSheet=None) -> RichTextAttr
        
        This function combines the list style's base attributes and the level
        style matching the given indent, returning the combined attributes.
        """

    def GetCombinedStyleForLevel(self, level: int, styleSheet: Optional[RichTextStyleSheet]=None) -> RichTextAttr:
        """
        GetCombinedStyleForLevel(level, styleSheet=None) -> RichTextAttr
        
        This function combines the list style's base attributes and the style
        for the specified level, returning the combined attributes.
        """

    def GetLevelAttributes(self, level: int) -> RichTextAttr:
        """
        GetLevelAttributes(level) -> RichTextAttr
        
        Returns the style for the given level.
        """

    def GetLevelCount(self) -> int:
        """
        GetLevelCount() -> int
        
        Returns the number of levels.
        """

    def IsNumbered(self, level: int) -> bool:
        """
        IsNumbered(level) -> bool
        
        Returns true if the given level has numbered list attributes.
        """

    def SetLevelAttributes(self, level: int, attr: RichTextAttr) -> None:
        """
        SetLevelAttributes(level, attr) -> None
        
        Sets the style for the given level.
        """
    @property
    def LevelCount(self) -> int: ...
# end of class RichTextListStyleDefinition


class RichTextStyleSheet(wx.Object):
    """
    RichTextStyleSheet() -> None
    
    A style sheet contains named paragraph and character styles that make
    it easy for a user to apply combinations of attributes to a
    wxRichTextCtrl.
    """

    def __init__(self) -> None:
        """
        RichTextStyleSheet() -> None
        
        A style sheet contains named paragraph and character styles that make
        it easy for a user to apply combinations of attributes to a
        wxRichTextCtrl.
        """

    def AddCharacterStyle(self, styleDef: RichTextCharacterStyleDefinition) -> bool:
        """
        AddCharacterStyle(styleDef) -> bool
        
        Adds a definition to the character style list.
        """

    def AddListStyle(self, styleDef: RichTextListStyleDefinition) -> bool:
        """
        AddListStyle(styleDef) -> bool
        
        Adds a definition to the list style list.
        """

    def AddParagraphStyle(self, styleDef: RichTextParagraphStyleDefinition) -> bool:
        """
        AddParagraphStyle(styleDef) -> bool
        
        Adds a definition to the paragraph style list.
        """

    def AddStyle(self, styleDef: RichTextStyleDefinition) -> bool:
        """
        AddStyle(styleDef) -> bool
        
        Adds a definition to the appropriate style list.
        """

    def DeleteStyles(self) -> None:
        """
        DeleteStyles() -> None
        
        Deletes all styles.
        """

    def FindCharacterStyle(self, name: str, recurse: bool=True) -> RichTextCharacterStyleDefinition:
        """
        FindCharacterStyle(name, recurse=True) -> RichTextCharacterStyleDefinition
        
        Finds a character definition by name.
        """

    def FindListStyle(self, name: str, recurse: bool=True) -> RichTextListStyleDefinition:
        """
        FindListStyle(name, recurse=True) -> RichTextListStyleDefinition
        
        Finds a list definition by name.
        """

    def FindParagraphStyle(self, name: str, recurse: bool=True) -> RichTextParagraphStyleDefinition:
        """
        FindParagraphStyle(name, recurse=True) -> RichTextParagraphStyleDefinition
        
        Finds a paragraph definition by name.
        """

    def FindStyle(self, name: str) -> RichTextStyleDefinition:
        """
        FindStyle(name) -> RichTextStyleDefinition
        
        Finds a style definition by name.
        """

    def GetCharacterStyle(self, n: int) -> RichTextCharacterStyleDefinition:
        """
        GetCharacterStyle(n) -> RichTextCharacterStyleDefinition
        
        Returns the nth character style.
        """

    def GetCharacterStyleCount(self) -> int:
        """
        GetCharacterStyleCount() -> int
        
        Returns the number of character styles.
        """

    def GetDescription(self) -> str:
        """
        GetDescription() -> str
        
        Returns the style sheet's description.
        """

    def GetListStyle(self, n: int) -> RichTextListStyleDefinition:
        """
        GetListStyle(n) -> RichTextListStyleDefinition
        
        Returns the nth list style.
        """

    def GetListStyleCount(self) -> int:
        """
        GetListStyleCount() -> int
        
        Returns the number of list styles.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns the style sheet's name.
        """

    def GetParagraphStyle(self, n: int) -> RichTextParagraphStyleDefinition:
        """
        GetParagraphStyle(n) -> RichTextParagraphStyleDefinition
        
        Returns the nth paragraph style.
        """

    def GetParagraphStyleCount(self) -> int:
        """
        GetParagraphStyleCount() -> int
        
        Returns the number of paragraph styles.
        """

    def RemoveCharacterStyle(self, styleDef: RichTextStyleDefinition, deleteStyle: bool=False) -> bool:
        """
        RemoveCharacterStyle(styleDef, deleteStyle=False) -> bool
        
        Removes a character style.
        """

    def RemoveListStyle(self, styleDef: RichTextStyleDefinition, deleteStyle: bool=False) -> bool:
        """
        RemoveListStyle(styleDef, deleteStyle=False) -> bool
        
        Removes a list style.
        """

    def RemoveParagraphStyle(self, styleDef: RichTextStyleDefinition, deleteStyle: bool=False) -> bool:
        """
        RemoveParagraphStyle(styleDef, deleteStyle=False) -> bool
        
        Removes a paragraph style.
        """

    def RemoveStyle(self, styleDef: RichTextStyleDefinition, deleteStyle: bool=False) -> bool:
        """
        RemoveStyle(styleDef, deleteStyle=False) -> bool
        
        Removes a style.
        """

    def SetDescription(self, descr: str) -> None:
        """
        SetDescription(descr) -> None
        
        Sets the style sheet's description.
        """

    def SetName(self, name: str) -> None:
        """
        SetName(name) -> None
        
        Sets the style sheet's name.
        """

    def GetProperties(self) -> RichTextProperties:
        """
        GetProperties() -> RichTextProperties
        
        Returns the sheet's properties.
        """

    def SetProperties(self, props: RichTextProperties) -> None:
        """
        SetProperties(props) -> None
        
        Sets the sheet's properties.
        """
    @property
    def CharacterStyleCount(self) -> int: ...
    @property
    def Description(self) -> str: ...
    @Description.setter
    def Description(self, value: str, /) -> None: ...
    @property
    def ListStyleCount(self) -> int: ...
    @property
    def Name(self) -> str: ...
    @Name.setter
    def Name(self, value: str, /) -> None: ...
    @property
    def ParagraphStyleCount(self) -> int: ...
    @property
    def Properties(self) -> RichTextProperties: ...
    @Properties.setter
    def Properties(self, value: RichTextProperties, /) -> None: ...
# end of class RichTextStyleSheet

#-- end-richtextstyles --#
#-- begin-richtextstyledlg --#
RICHTEXT_ORGANISER_DELETE_STYLES: int
RICHTEXT_ORGANISER_CREATE_STYLES: int
RICHTEXT_ORGANISER_APPLY_STYLES: int
RICHTEXT_ORGANISER_EDIT_STYLES: int
RICHTEXT_ORGANISER_RENAME_STYLES: int
RICHTEXT_ORGANISER_OK_CANCEL: int
RICHTEXT_ORGANISER_RENUMBER: int
RICHTEXT_ORGANISER_SHOW_CHARACTER: int
RICHTEXT_ORGANISER_SHOW_PARAGRAPH: int
RICHTEXT_ORGANISER_SHOW_LIST: int
RICHTEXT_ORGANISER_SHOW_BOX: int
RICHTEXT_ORGANISER_SHOW_ALL: int
RICHTEXT_ORGANISER_ORGANISE: int
RICHTEXT_ORGANISER_BROWSE: int
RICHTEXT_ORGANISER_BROWSE_NUMBERING: int

class RichTextStyleOrganiserDialog(wx.Dialog):
    """
    RichTextStyleOrganiserDialog() -> None
    RichTextStyleOrganiserDialog(flags, sheet, ctrl, parent, id=wx.ID_ANY, caption=_("StyleOrganiser"), pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_DIALOG_STYLE|wx.RESIZE_BORDER|wx.SYSTEM_MENU|wx.CLOSE_BOX) -> None
    
    This class shows a style sheet and allows the user to edit, add and
    remove styles.
    """

    @overload
    def __init__(self, flags: int, sheet: RichTextStyleSheet, ctrl: RichTextCtrl, parent: Optional[wx.Window], id: int=wx.ID_ANY, caption: str=_("StyleOrganiser"), pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.DEFAULT_DIALOG_STYLE|wx.RESIZE_BORDER|wx.SYSTEM_MENU|wx.CLOSE_BOX) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RichTextStyleOrganiserDialog() -> None
        RichTextStyleOrganiserDialog(flags, sheet, ctrl, parent, id=wx.ID_ANY, caption=_("StyleOrganiser"), pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_DIALOG_STYLE|wx.RESIZE_BORDER|wx.SYSTEM_MENU|wx.CLOSE_BOX) -> None
        
        This class shows a style sheet and allows the user to edit, add and
        remove styles.
        """

    def ApplyStyle(self, ctrl: Optional[RichTextCtrl]=None) -> bool:
        """
        ApplyStyle(ctrl=None) -> bool
        
        Applies the selected style to selection in the given control or the
        control passed to the constructor.
        """

    def Create(self, flags: int, sheet: RichTextStyleSheet, ctrl: RichTextCtrl, parent: wx.Window, id: int=wx.ID_ANY, caption: str=wx.GetTranslation("StyleOrganiser"), pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.Size(400,300), style: int=wx.DEFAULT_DIALOG_STYLE|wx.RESIZE_BORDER|wx.SYSTEM_MENU|wx.CLOSE_BOX) -> bool:
        """
        Create(flags, sheet, ctrl, parent, id=wx.ID_ANY, caption=wx.GetTranslation("StyleOrganiser"), pos=wx.DefaultPosition, size=wx.Size(400,300), style=wx.DEFAULT_DIALOG_STYLE|wx.RESIZE_BORDER|wx.SYSTEM_MENU|wx.CLOSE_BOX) -> bool
        
        Creates the dialog.
        """

    def GetRestartNumbering(self) -> bool:
        """
        GetRestartNumbering() -> bool
        
        Returns true if the user has opted to restart numbering.
        """

    def GetRichTextCtrl(self) -> RichTextCtrl:
        """
        GetRichTextCtrl() -> RichTextCtrl
        
        Returns the associated rich text control (if any).
        """

    def GetSelectedStyle(self) -> str:
        """
        GetSelectedStyle() -> str
        
        Returns selected style name.
        """

    def GetSelectedStyleDefinition(self) -> RichTextStyleDefinition:
        """
        GetSelectedStyleDefinition() -> RichTextStyleDefinition
        
        Returns selected style definition.
        """

    def GetStyleSheet(self) -> RichTextStyleSheet:
        """
        GetStyleSheet() -> RichTextStyleSheet
        
        Returns the associated style sheet.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Sets the flags used to control the interface presented to the user.
        """

    def SetRestartNumbering(self, restartNumbering: bool) -> None:
        """
        SetRestartNumbering(restartNumbering) -> None
        
        Checks or unchecks the restart numbering checkbox.
        """

    def SetRichTextCtrl(self, ctrl: RichTextCtrl) -> None:
        """
        SetRichTextCtrl(ctrl) -> None
        
        Sets the control to be associated with the dialog, for the purposes of
        applying a style to the selection.
        """

    def SetStyleSheet(self, sheet: RichTextStyleSheet) -> None:
        """
        SetStyleSheet(sheet) -> None
        
        Sets the associated style sheet.
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        
        Returns the flags used to control the interface presented to the user.
        """

    @staticmethod
    def SetShowToolTips(show: bool) -> None:
        """
        SetShowToolTips(show) -> None
        
        Determines whether tooltips will be shown.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def RestartNumbering(self) -> bool: ...
    @RestartNumbering.setter
    def RestartNumbering(self, value: bool, /) -> None: ...
    @property
    def RichTextCtrl(self) -> RichTextCtrl: ...
    @RichTextCtrl.setter
    def RichTextCtrl(self, value: RichTextCtrl, /) -> None: ...
    @property
    def SelectedStyle(self) -> str: ...
    @property
    def SelectedStyleDefinition(self) -> RichTextStyleDefinition: ...
    @property
    def StyleSheet(self) -> RichTextStyleSheet: ...
    @StyleSheet.setter
    def StyleSheet(self, value: RichTextStyleSheet, /) -> None: ...
# end of class RichTextStyleOrganiserDialog

#-- end-richtextstyledlg --#
#-- begin-richtextsymboldlg --#

class SymbolPickerDialog(wx.Dialog):
    """
    SymbolPickerDialog() -> None
    SymbolPickerDialog(symbol, initialFont, normalTextFont, parent, id=wx.ID_ANY, title=_("Symbols"), pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_DIALOG_STYLE|wx.RESIZE_BORDER|wx.CLOSE_BOX) -> None
    
    wxSymbolPickerDialog presents the user with a choice of fonts and a
    grid of available characters.
    """

    @overload
    def __init__(self, symbol: str, initialFont: str, normalTextFont: str, parent: Optional[wx.Window], id: int=wx.ID_ANY, title: str=_("Symbols"), pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.DEFAULT_DIALOG_STYLE|wx.RESIZE_BORDER|wx.CLOSE_BOX) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        SymbolPickerDialog() -> None
        SymbolPickerDialog(symbol, initialFont, normalTextFont, parent, id=wx.ID_ANY, title=_("Symbols"), pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_DIALOG_STYLE|wx.RESIZE_BORDER|wx.CLOSE_BOX) -> None
        
        wxSymbolPickerDialog presents the user with a choice of fonts and a
        grid of available characters.
        """

    def Create(self, symbol: str, initialFont: str, normalTextFont: str, parent: wx.Window, id: int=wx.ID_ANY, caption: str=wx.GetTranslation("Symbols"), pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.Size(400,300), style: int=wx.DEFAULT_DIALOG_STYLE|wx.RESIZE_BORDER|wx.CLOSE_BOX) -> bool:
        """
        Create(symbol, initialFont, normalTextFont, parent, id=wx.ID_ANY, caption=wx.GetTranslation("Symbols"), pos=wx.DefaultPosition, size=wx.Size(400,300), style=wx.DEFAULT_DIALOG_STYLE|wx.RESIZE_BORDER|wx.CLOSE_BOX) -> bool
        
        Creation: see the constructor for details about the parameters.
        """

    def GetFontName(self) -> str:
        """
        GetFontName() -> str
        
        Returns the font name (the font reflected in the font list).
        """

    def GetFromUnicode(self) -> bool:
        """
        GetFromUnicode() -> bool
        
        Returns true if the dialog is showing the full range of Unicode
        characters.
        """

    def GetNormalTextFontName(self) -> str:
        """
        GetNormalTextFontName() -> str
        
        Gets the font name used for displaying symbols in the absence of a
        selected font.
        """

    def GetSymbol(self) -> str:
        """
        GetSymbol() -> str
        
        Gets the current or initial symbol as a string.
        """

    def GetSymbolChar(self) -> int:
        """
        GetSymbolChar() -> int
        
        Gets the selected symbol character as an integer.
        """

    def HasSelection(self) -> bool:
        """
        HasSelection() -> bool
        
        Returns true if a symbol is selected.
        """

    def SetFontName(self, value: str) -> None:
        """
        SetFontName(value) -> None
        
        Sets the initial/selected font name.
        """

    def SetFromUnicode(self, value: bool) -> None:
        """
        SetFromUnicode(value) -> None
        
        Sets the internal flag indicating that the full Unicode range should
        be displayed.
        """

    def SetNormalTextFontName(self, value: str) -> None:
        """
        SetNormalTextFontName(value) -> None
        
        Sets the name of the font to be used in the absence of a selected
        font.
        """

    def SetSymbol(self, value: str) -> None:
        """
        SetSymbol(value) -> None
        
        Sets the symbol as a one or zero character string.
        """

    def SetUnicodeMode(self, unicodeMode: bool) -> None:
        """
        SetUnicodeMode(unicodeMode) -> None
        
        Sets Unicode display mode.
        """

    def UseNormalFont(self) -> bool:
        """
        UseNormalFont() -> bool
        
        Returns true if the has specified normal text - that is, there is no
        selected font.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def FontName(self) -> str: ...
    @FontName.setter
    def FontName(self, value: str, /) -> None: ...
    @property
    def FromUnicode(self) -> bool: ...
    @FromUnicode.setter
    def FromUnicode(self, value: bool, /) -> None: ...
    @property
    def NormalTextFontName(self) -> str: ...
    @NormalTextFontName.setter
    def NormalTextFontName(self, value: str, /) -> None: ...
    @property
    def Symbol(self) -> str: ...
    @Symbol.setter
    def Symbol(self, value: str, /) -> None: ...
    @property
    def SymbolChar(self) -> int: ...
# end of class SymbolPickerDialog

#-- end-richtextsymboldlg --#
#-- begin-richtextformatdlg --#
RICHTEXT_FORMAT_STYLE_EDITOR: int
RICHTEXT_FORMAT_FONT: int
RICHTEXT_FORMAT_TABS: int
RICHTEXT_FORMAT_BULLETS: int
RICHTEXT_FORMAT_INDENTS_SPACING: int

class RichTextFormattingDialogFactory(wx.Object):
    """
    RichTextFormattingDialogFactory() -> None
    
    This class provides pages for wxRichTextFormattingDialog, and allows
    other customization of the dialog.
    """

    def __init__(self) -> None:
        """
        RichTextFormattingDialogFactory() -> None
        
        This class provides pages for wxRichTextFormattingDialog, and allows
        other customization of the dialog.
        """

    def CreateButtons(self, dialog: RichTextFormattingDialog) -> bool:
        """
        CreateButtons(dialog) -> bool
        
        Creates the main dialog buttons.
        """

    def CreatePage(self, page: int, title: str, dialog: RichTextFormattingDialog) -> wx.Panel:
        """
        CreatePage(page, title, dialog) -> wx.Panel
        
        Creates a page, given a page identifier.
        """

    def CreatePages(self, pages: int, dialog: RichTextFormattingDialog) -> bool:
        """
        CreatePages(pages, dialog) -> bool
        
        Creates all pages under the dialog's book control, also calling
        AddPage().
        """

    def GetPageId(self, i: int) -> int:
        """
        GetPageId(i) -> int
        
        Enumerate all available page identifiers.
        """

    def GetPageIdCount(self) -> int:
        """
        GetPageIdCount() -> int
        
        Gets the number of available page identifiers.
        """

    def GetPageImage(self, id: int) -> int:
        """
        GetPageImage(id) -> int
        
        Gets the image index for the given page identifier.
        """

    def SetSheetStyle(self, dialog: RichTextFormattingDialog) -> bool:
        """
        SetSheetStyle(dialog) -> bool
        
        Set the property sheet style, called at the start of
        wxRichTextFormattingDialog::Create.
        """

    def ShowHelp(self, page: int, dialog: RichTextFormattingDialog) -> bool:
        """
        ShowHelp(page, dialog) -> bool
        
        Invokes help for the dialog.
        """
    @property
    def PageIdCount(self) -> int: ...
# end of class RichTextFormattingDialogFactory


class RichTextFormattingDialog(wx.wx.adv.PropertySheetDialog):
    """
    RichTextFormattingDialog() -> None
    RichTextFormattingDialog(flags, parent, title="Formatting", id=wx.ID_ANY, pos=wx.DefaultPosition, sz=wx.DefaultSize, style=wx.DEFAULT_DIALOG_STYLE) -> None
    
    This dialog allows the user to edit a character and/or paragraph
    style.
    """

    class _enum_45(IntEnum):
        Option_AllowPixelFontSize = auto()
    Option_AllowPixelFontSize = _enum_45.Option_AllowPixelFontSize

    @overload
    def __init__(self, flags: int, parent: Optional[wx.Window], title: str="Formatting", id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, sz: wx.Size=wx.DefaultSize, style: int=wx.DEFAULT_DIALOG_STYLE) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RichTextFormattingDialog() -> None
        RichTextFormattingDialog(flags, parent, title="Formatting", id=wx.ID_ANY, pos=wx.DefaultPosition, sz=wx.DefaultSize, style=wx.DEFAULT_DIALOG_STYLE) -> None
        
        This dialog allows the user to edit a character and/or paragraph
        style.
        """

    def ApplyStyle(self, ctrl: RichTextCtrl, range: RichTextRange, flags: int=RICHTEXT_SETSTYLE_WITH_UNDO|RICHTEXT_SETSTYLE_OPTIMIZE) -> bool:
        """
        ApplyStyle(ctrl, range, flags=RICHTEXT_SETSTYLE_WITH_UNDO|RICHTEXT_SETSTYLE_OPTIMIZE) -> bool
        
        Apply attributes to the given range, only changing attributes that
        need to be changed.
        """

    def Create(self, flags: int, parent: wx.Window, title: str=wx.GetTranslation("Formatting"), id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, sz: wx.Size=wx.DefaultSize, style: int=wx.DEFAULT_DIALOG_STYLE) -> bool:
        """
        Create(flags, parent, title=wx.GetTranslation("Formatting"), id=wx.ID_ANY, pos=wx.DefaultPosition, sz=wx.DefaultSize, style=wx.DEFAULT_DIALOG_STYLE) -> bool
        
        Creation: see wxRichTextFormattingDialog() "the constructor" for
        details about the parameters.
        """

    def GetAttributes(self) -> wx.TextAttr:
        """
        GetAttributes() -> wx.TextAttr
        """

    def GetImageList(self) -> wx.ImageList:
        """
        GetImageList() -> wx.ImageList
        
        Returns the image list associated with the dialog, used for example if
        showing the dialog as a toolbook.
        """

    def GetStyle(self, ctrl: RichTextCtrl, range: RichTextRange) -> bool:
        """
        GetStyle(ctrl, range) -> bool
        
        Gets common attributes from the given range and calls SetAttributes().
        """

    def GetStyleDefinition(self) -> RichTextStyleDefinition:
        """
        GetStyleDefinition() -> RichTextStyleDefinition
        
        Gets the associated style definition, if any.
        """

    def GetStyleSheet(self) -> RichTextStyleSheet:
        """
        GetStyleSheet() -> RichTextStyleSheet
        
        Gets the associated style sheet, if any.
        """

    def SetAttributes(self, attr: wx.TextAttr) -> None:
        """
        SetAttributes(attr) -> None
        
        Sets the attributes to be edited.
        """

    def SetOptions(self, options: int) -> None:
        """
        SetOptions(options) -> None
        
        Sets the dialog options, determining what the interface presents to
        the user.
        """

    def GetOptions(self) -> int:
        """
        GetOptions() -> int
        
        Gets the dialog options, determining what the interface presents to
        the user.
        """

    def HasOption(self, option: int) -> bool:
        """
        HasOption(option) -> bool
        
        Returns true if the given option is present.
        """

    def SetImageList(self, imageList: wx.ImageList) -> None:
        """
        SetImageList(imageList) -> None
        
        Sets the image list associated with the dialog's property sheet.
        """

    def SetStyle(self, style: wx.TextAttr, update: bool=True) -> bool:
        """
        SetStyle(style, update=True) -> bool
        
        Sets the attributes and optionally updates the display, if update is
        true.
        """

    def SetStyleDefinition(self, styleDef: RichTextStyleDefinition, sheet: RichTextStyleSheet, update: bool=True) -> bool:
        """
        SetStyleDefinition(styleDef, sheet, update=True) -> bool
        
        Sets the style definition and optionally update the display, if update
        is true.
        """

    def UpdateDisplay(self) -> bool:
        """
        UpdateDisplay() -> bool
        
        Updates the display.
        """

    @staticmethod
    def GetDialog(win: wx.Window) -> RichTextFormattingDialog:
        """
        GetDialog(win) -> RichTextFormattingDialog
        
        Helper for pages to get the top-level dialog.
        """

    @staticmethod
    def GetDialogAttributes(win: wx.Window) -> wx.TextAttr:
        """
        GetDialogAttributes(win) -> wx.TextAttr
        
        Helper for pages to get the attributes.
        """

    @staticmethod
    def GetDialogStyleDefinition(win: wx.Window) -> RichTextStyleDefinition:
        """
        GetDialogStyleDefinition(win) -> RichTextStyleDefinition
        
        Helper for pages to get the style.
        """

    @staticmethod
    def GetFormattingDialogFactory() -> RichTextFormattingDialogFactory:
        """
        GetFormattingDialogFactory() -> RichTextFormattingDialogFactory
        
        Returns the object to be used to customize the dialog and provide
        pages.
        """

    @staticmethod
    def SetFormattingDialogFactory(factory: RichTextFormattingDialogFactory) -> None:
        """
        SetFormattingDialogFactory(factory) -> None
        
        Sets the formatting factory object to be used for customization and
        page creation.
        """

    @staticmethod
    def GetRestoreLastPage() -> bool:
        """
        GetRestoreLastPage() -> bool
        
        Returns true if the dialog will restore the last-selected page.
        """

    @staticmethod
    def SetRestoreLastPage(b: bool) -> None:
        """
        SetRestoreLastPage(b) -> None
        
        Pass true if the dialog should restore the last-selected page.
        """

    @staticmethod
    def GetLastPage() -> int:
        """
        GetLastPage() -> int
        
        Returns the page identifier of the last page selected (not the control
        id).
        """

    @staticmethod
    def SetLastPage(lastPage: int) -> None:
        """
        SetLastPage(lastPage) -> None
        
        Sets the page identifier of the last page selected (not the control
        id).
        """

    @staticmethod
    def SetColourData(colourData: wx.ColourData) -> None:
        """
        SetColourData(colourData) -> None
        
        Sets the custom colour data for use by the colour dialog.
        """

    @staticmethod
    def GetColourData() -> wx.ColourData:
        """
        GetColourData() -> wx.ColourData
        
        Returns the custom colour data for use by the colour dialog.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Attributes(self) -> wx.TextAttr: ...
    @Attributes.setter
    def Attributes(self, value: wx.TextAttr, /) -> None: ...
    @property
    def ImageList(self) -> wx.ImageList: ...
    @ImageList.setter
    def ImageList(self, value: wx.ImageList, /) -> None: ...
    @property
    def Options(self) -> int: ...
    @Options.setter
    def Options(self, value: int, /) -> None: ...
    @property
    def StyleDefinition(self) -> RichTextStyleDefinition: ...
    @StyleDefinition.setter
    def StyleDefinition(self, value: RichTextStyleDefinition, /) -> None: ...
    @property
    def StyleSheet(self) -> RichTextStyleSheet: ...
# end of class RichTextFormattingDialog

#-- end-richtextformatdlg --#
