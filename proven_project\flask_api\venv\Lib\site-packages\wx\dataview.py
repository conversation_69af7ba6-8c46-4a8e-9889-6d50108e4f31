# This file is generated by wxPython's SIP generator.  Do not edit by hand.
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License

"""
The classes in this module provide views and data models for viewing tabular
or hierarchical data in a more advanced way than what is provided by classes
such as :ref:`wx.ListCtrl`, :ref:`wx.TreeCtrl`, etc.
"""

from ._dataview import *

import wx

def _DataViewCtrl_AssociateModel(self, model):
    """
    Associates a :class:`DataViewModel` with the control.
    Ownership of the model object is passed to C++, however it
    is reference counted so it can be shared with other views.
    """
    import wx.siplib
    wasPyOwned = wx.siplib.ispyowned(model)
    self._AssociateModel(model)
    # Ownership of the python object has just been transferred to
    # C++, so DecRef the C++ instance associated with this python
    # reference.
    if wasPyOwned:
        model.DecRef()
DataViewCtrl.AssociateModel = _DataViewCtrl_AssociateModel
del _DataViewCtrl_AssociateModel
def _DataViewCtrl_GetColumns(self):
    """
    Returns a list of column objects.
    """
    return [self.GetColumn(i) for i in range(self.GetColumnCount())]
DataViewCtrl.GetColumns = _DataViewCtrl_GetColumns
del _DataViewCtrl_GetColumns
DataViewCtrl.Columns = property(DataViewCtrl.GetColumns)
def _DataViewItemArray___repr__(self):
    return "DataViewItemArray: " + repr(list(self))
DataViewItemArray.__repr__ = _DataViewItemArray___repr__
del _DataViewItemArray___repr__
NullDataViewItem = DataViewItem()

class DataViewItemObjectMapper(object):
    """
    This class provides a mechanism for mapping between Python objects and the
    :class:`DataViewItem` objects used by the :class:`DataViewModel` for tracking the items in
    the view. The ID used for the item is the id() of the Python object. Use
    :meth:`ObjectToItem` to create a :class:`DataViewItem` using a Python object as its ID,
    and use :meth:`ItemToObject` to fetch that Python object again later for a given
    :class:`DataViewItem`.
    
    By default a regular dictionary is used to implement the ID to object
    mapping. Optionally a WeakValueDictionary can be useful when there will be
    a high turnover of objects and maintaining an extra reference to the
    objects would be unwise.  If weak references are used then the objects
    associated with data items must be weak-referenceable.  (Things like
    stock lists and dictionaries are not.)  See :meth:`UseWeakRefs`.
    
    This class is used in :class:`PyDataViewModel` as a mixin for convenience.
    """
    def __init__(self):
        self.mapper = dict()
        self.usingWeakRefs = False

    def ObjectToItem(self, obj):
        """
        Create a :class:`DataViewItem` for the object, and remember the ID-->obj mapping.
        """
        import sys
        oid = id(obj)
        while oid > sys.maxsize:
            # risk of conflict here... May need some more thought.
            oid -= sys.maxsize
        self.mapper[oid] = obj
        return DataViewItem(oid)

    def ItemToObject(self, item):
        """
        Retrieve the object that was used to create an item.
        """
        oid = int(item.GetID())
        return self.mapper[oid]

    def UseWeakRefs(self, flag):
        """
        Switch to or from using a weak value dictionary for keeping the ID to
        object map.
        """
        if flag == self.usingWeakRefs:
            return
        if flag:
            import weakref
            newmap = weakref.WeakValueDictionary()
        else:
            newmap = dict()
        newmap.update(self.mapper)
        self.mapper = newmap
        self.usingWeakRefs = flag


class PyDataViewModel(DataViewModel, DataViewItemObjectMapper):
    """
    A convenience class that is a :class:`DataViewModel` combined with an object mapper.
    """
    def __init__(self):
        DataViewModel.__init__(self)
        DataViewItemObjectMapper.__init__(self)


PyDataViewIndexListModel = wx.deprecated(DataViewIndexListModel)
PyDataViewVirtualListModel = wx.deprecated(DataViewVirtualListModel)

PyDataViewCustomRenderer = wx.deprecated(DataViewCustomRenderer,
                                         "Use DataViewCustomRenderer instead")

EVT_DATAVIEW_SELECTION_CHANGED         = wx.PyEventBinder( wxEVT_DATAVIEW_SELECTION_CHANGED, 1)
EVT_DATAVIEW_ITEM_ACTIVATED            = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_ACTIVATED, 1)
EVT_DATAVIEW_ITEM_COLLAPSED            = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_COLLAPSED, 1)
EVT_DATAVIEW_ITEM_EXPANDED             = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_EXPANDED, 1)
EVT_DATAVIEW_ITEM_COLLAPSING           = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_COLLAPSING, 1)
EVT_DATAVIEW_ITEM_EXPANDING            = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_EXPANDING, 1)
EVT_DATAVIEW_ITEM_START_EDITING        = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_START_EDITING, 1)
EVT_DATAVIEW_ITEM_EDITING_STARTED      = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_EDITING_STARTED, 1)
EVT_DATAVIEW_ITEM_EDITING_DONE         = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_EDITING_DONE, 1)
EVT_DATAVIEW_ITEM_VALUE_CHANGED        = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_VALUE_CHANGED, 1)
EVT_DATAVIEW_ITEM_CONTEXT_MENU         = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_CONTEXT_MENU, 1)
EVT_DATAVIEW_COLUMN_HEADER_CLICK       = wx.PyEventBinder( wxEVT_DATAVIEW_COLUMN_HEADER_CLICK, 1)
EVT_DATAVIEW_COLUMN_HEADER_RIGHT_CLICK = wx.PyEventBinder( wxEVT_DATAVIEW_COLUMN_HEADER_RIGHT_CLICK, 1)
EVT_DATAVIEW_COLUMN_SORTED             = wx.PyEventBinder( wxEVT_DATAVIEW_COLUMN_SORTED, 1)
EVT_DATAVIEW_COLUMN_REORDERED          = wx.PyEventBinder( wxEVT_DATAVIEW_COLUMN_REORDERED, 1)
EVT_DATAVIEW_ITEM_BEGIN_DRAG           = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_BEGIN_DRAG, 1)
EVT_DATAVIEW_ITEM_DROP_POSSIBLE        = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_DROP_POSSIBLE, 1)
EVT_DATAVIEW_ITEM_DROP                 = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_DROP, 1)
EVT_DATAVIEW_CACHE_HINT                = wx.PyEventBinder( wxEVT_DATAVIEW_CACHE_HINT, 1 )

# deprecated wxEVT aliases
wxEVT_COMMAND_DATAVIEW_SELECTION_CHANGED          = wxEVT_DATAVIEW_SELECTION_CHANGED
wxEVT_COMMAND_DATAVIEW_ITEM_ACTIVATED             = wxEVT_DATAVIEW_ITEM_ACTIVATED
wxEVT_COMMAND_DATAVIEW_ITEM_COLLAPSED             = wxEVT_DATAVIEW_ITEM_COLLAPSED
wxEVT_COMMAND_DATAVIEW_ITEM_EXPANDED              = wxEVT_DATAVIEW_ITEM_EXPANDED
wxEVT_COMMAND_DATAVIEW_ITEM_COLLAPSING            = wxEVT_DATAVIEW_ITEM_COLLAPSING
wxEVT_COMMAND_DATAVIEW_ITEM_EXPANDING             = wxEVT_DATAVIEW_ITEM_EXPANDING
wxEVT_COMMAND_DATAVIEW_ITEM_START_EDITING         = wxEVT_DATAVIEW_ITEM_START_EDITING
wxEVT_COMMAND_DATAVIEW_ITEM_EDITING_STARTED       = wxEVT_DATAVIEW_ITEM_EDITING_STARTED
wxEVT_COMMAND_DATAVIEW_ITEM_EDITING_DONE          = wxEVT_DATAVIEW_ITEM_EDITING_DONE
wxEVT_COMMAND_DATAVIEW_ITEM_VALUE_CHANGED         = wxEVT_DATAVIEW_ITEM_VALUE_CHANGED
wxEVT_COMMAND_DATAVIEW_ITEM_CONTEXT_MENU          = wxEVT_DATAVIEW_ITEM_CONTEXT_MENU
wxEVT_COMMAND_DATAVIEW_COLUMN_HEADER_CLICK        = wxEVT_DATAVIEW_COLUMN_HEADER_CLICK
wxEVT_COMMAND_DATAVIEW_COLUMN_HEADER_RIGHT_CLICK  = wxEVT_DATAVIEW_COLUMN_HEADER_RIGHT_CLICK
wxEVT_COMMAND_DATAVIEW_COLUMN_SORTED              = wxEVT_DATAVIEW_COLUMN_SORTED
wxEVT_COMMAND_DATAVIEW_COLUMN_REORDERED           = wxEVT_DATAVIEW_COLUMN_REORDERED
wxEVT_COMMAND_DATAVIEW_CACHE_HINT                 = wxEVT_DATAVIEW_CACHE_HINT
wxEVT_COMMAND_DATAVIEW_ITEM_BEGIN_DRAG            = wxEVT_DATAVIEW_ITEM_BEGIN_DRAG
wxEVT_COMMAND_DATAVIEW_ITEM_DROP_POSSIBLE         = wxEVT_DATAVIEW_ITEM_DROP_POSSIBLE
wxEVT_COMMAND_DATAVIEW_ITEM_DROP                  = wxEVT_DATAVIEW_ITEM_DROP

EVT_TREELIST_SELECTION_CHANGED = wx.PyEventBinder( wxEVT_TREELIST_SELECTION_CHANGED )
EVT_TREELIST_ITEM_EXPANDING =    wx.PyEventBinder( wxEVT_TREELIST_ITEM_EXPANDING )
EVT_TREELIST_ITEM_EXPANDED =     wx.PyEventBinder( wxEVT_TREELIST_ITEM_EXPANDED )
EVT_TREELIST_ITEM_CHECKED =      wx.PyEventBinder( wxEVT_TREELIST_ITEM_CHECKED )
EVT_TREELIST_ITEM_ACTIVATED =    wx.PyEventBinder( wxEVT_TREELIST_ITEM_ACTIVATED )
EVT_TREELIST_ITEM_CONTEXT_MENU = wx.PyEventBinder( wxEVT_TREELIST_ITEM_CONTEXT_MENU )
EVT_TREELIST_COLUMN_SORTED =     wx.PyEventBinder( wxEVT_TREELIST_COLUMN_SORTED )

# deprecated wxEVT aliases
wxEVT_COMMAND_TREELIST_SELECTION_CHANGED  = wxEVT_TREELIST_SELECTION_CHANGED
wxEVT_COMMAND_TREELIST_ITEM_EXPANDING     = wxEVT_TREELIST_ITEM_EXPANDING
wxEVT_COMMAND_TREELIST_ITEM_EXPANDED      = wxEVT_TREELIST_ITEM_EXPANDED
wxEVT_COMMAND_TREELIST_ITEM_CHECKED       = wxEVT_TREELIST_ITEM_CHECKED
wxEVT_COMMAND_TREELIST_ITEM_ACTIVATED     = wxEVT_TREELIST_ITEM_ACTIVATED
wxEVT_COMMAND_TREELIST_ITEM_CONTEXT_MENU  = wxEVT_TREELIST_ITEM_CONTEXT_MENU
wxEVT_COMMAND_TREELIST_COLUMN_SORTED      = wxEVT_TREELIST_COLUMN_SORTED

