using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MultipleFinger.Native
{
    public class DeviceManagerNative
    {
        private IntPtr deviceHandle;
        private bool isDeviceOpen = false;

        public bool IsDeviceOpen => isDeviceOpen;

        public int GetDeviceCount()
        {
            int count = 0;
            int result = TrustFingerNative.ARAFPSCAN_GetDeviceCount(ref count);
            if (result != 0)
            {
                throw new Exception($"Failed to get device count. Error code: {result}");
            }
            return count;
        }

        public void Initialize()
        {
            int result = TrustFingerNative.ARAFPSCAN_GlobalInit();
            if (result != 0 && result != -115) // -115 means already initialized
            {
                throw new Exception($"Failed to initialize TrustFinger SDK. Error code: {result}");
            }
        }

        public void OpenDevice(int deviceIndex = 0)
        {
            if (isDeviceOpen)
            {
                return;
            }

            int result = TrustFingerNative.ARAFPSCAN_OpenDevice(ref deviceHandle, deviceIndex);
            if (result != 0)
            {
                throw new Exception($"Failed to open device. Error code: {result}");
            }
            isDeviceOpen = true;
        }

        public void CloseDevice()
        {
            if (!isDeviceOpen)
            {
                return;
            }

            int result = TrustFingerNative.ARAFPSCAN_CloseDevice(ref deviceHandle);
            if (result != 0)
            {
                throw new Exception($"Failed to close device. Error code: {result}");
            }
            isDeviceOpen = false;
            deviceHandle = IntPtr.Zero;
        }

        public void Free()
        {
            TrustFingerNative.ARAFPSCAN_GlobalFree();
        }

        public event Action<int, IntPtr, int, int, IntPtr, uint> AcquisitionEvent;

        private ARAFPSCAN_MultiFingerAcquisitionEventsManagerCallback acquisitionCallback;

        public void StartMultiFingerAcquisition(MultiFingerParam param)
        {
            if (!isDeviceOpen)
            {
                throw new Exception("Device is not open.");
            }

            acquisitionCallback = new ARAFPSCAN_MultiFingerAcquisitionEventsManagerCallback(OnAcquisitionEvent);
            int result = TrustFingerNative.ARAFPSCAN_MultiFingerStartAcquisition(deviceHandle, param, acquisitionCallback);

            if (result != 0)
            {
                throw new Exception($"Failed to start multi-finger acquisition. Error code: {result}");
            }
        }

        public void StopMultiFingerAcquisition()
        {
            if (!isDeviceOpen)
            {
                return;
            }
            TrustFingerNative.ARAFPSCAN_MultiFingerStopAcquisition(deviceHandle);
        }

        private int OnAcquisitionEvent(int eventCode, IntPtr framePtr, int frameWidth, int frameHeight, IntPtr segmentImageList, uint numSegmentImage)
        {
            AcquisitionEvent?.Invoke(eventCode, framePtr, frameWidth, frameHeight, segmentImageList, numSegmentImage);
            return 0;
        }

        public DeviceDescription GetDeviceDescription(int deviceIndex)
        {
            if (!isDeviceOpen)
            {
                throw new Exception("Device is not open.");
            }

            var desc = new DeviceDescription
            {
                DeviceId = deviceIndex,
                ProductName = "TrustFinger Device",
                Manufacturer = "Aratek",
                ImagePixelWidth = 1600,
                ImagePixelHeight = 1500,
                Resolution = 500
            };

            return desc;
        }
    }
}