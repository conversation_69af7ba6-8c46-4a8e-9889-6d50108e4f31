// qqmlndefrecord.sip generated by MetaSIP
//
// This file is part of the QtNfc Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QQmlNdefRecord : public QObject
{
%TypeHeaderCode
#include <qqmlndefrecord.h>
%End

public:
    enum TypeNameFormat
    {
        Empty,
        NfcRtd,
        Mime,
        Uri,
        ExternalRtd,
        Unknown,
    };

    explicit QQmlNdefRecord(QObject *parent /TransferThis/ = 0);
    QQmlNdefRecord(const QNdefRecord &record, QObject *parent /TransferThis/ = 0);
%If (Qt_5_6_0 -)
    virtual ~QQmlNdefRecord();
%End
    QString type() const;
    void setType(const QString &t);
    void setTypeNameFormat(QQmlNdefRecord::TypeNameFormat typeNameFormat);
    QQmlNdefRecord::TypeNameFormat typeNameFormat() const;
    QNdefRecord record() const;
    void setRecord(const QNdefRecord &record);

signals:
    void typeChanged();
    void typeNameFormatChanged();
    void recordChanged();
};

%End
