"""Flask API for Slaps Fingerprint Management System

This module provides a RESTful API interface for slaps fingerprint operations including:
- Capturing slaps fingerprints (Left 4, Right 4, Two Thumbs)
- Capturing flat fingerprints (individual fingers 1-10)
- Verifying fingerprints against stored templates
- Matching fingerprints

The API communicates with the fingerprint bridge service via TCP socket on port 8123.

Author: AratekTrustFinger Team
"""

import logging
import os
import base64
import socket
import time
import sys

from flask import Flask, request, jsonify
from flask_cors import CORS

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

app = Flask(__name__)
CORS(app)

def capture_fingerprint_bmp(person_id, finger_index):
    """Capture a fingerprint image in BMP format.
    
    Establishes a connection to the fingerprint bridge service and requests
    a new fingerprint capture. The captured image is returned in base64 format.
    
    Args:
        person_id (str): Unique identifier for the person
        finger_index (int): Index of the finger being captured
                           1-10: Individual fingers
                           11: Two thumbs
                           12: Left four fingers
                           13: Right four fingers
    
    Returns:
        dict: Contains capture operation result
            - status: 'success' or 'error'
            - message: Operation result description
            - bmp_base64: Base64 encoded fingerprint image (if successful)
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(30)  # longer timeout for full scan
            s.connect(('127.0.0.1', 8123))
            command = f"CAPTURE {person_id} {finger_index}\n"
            s.sendall(command.encode())

            start_time = time.time()
            data = b""
            while True:
                chunk = s.recv(4096)
                if not chunk:
                    break
                data += chunk

            duration = time.time() - start_time
            print(f"[Bridge] Response received in {duration:.2f} sec")

            decoded = data.decode("utf-8", errors="ignore")
            lines = decoded.splitlines()

            result_message = "❌ No valid response"
            base64_bmp = ""

            for line in lines:
                clean_line = line.strip().lstrip("\ufeff")
                print(f"[Bridge] {clean_line}")
                if clean_line.startswith("BMP:"):
                    base64_bmp = clean_line[4:]
                elif clean_line.upper().startswith("OK") or "✅" in clean_line:
                    result_message = clean_line
                elif "ERROR" in clean_line.upper():
                    result_message = clean_line

            status = "success" if "OK" in result_message.upper() or "✅" in result_message else "error"

            return {
                "status": status,
                "message": result_message,
                "bmp_base64": base64_bmp
            }

    except socket.timeout:
        return {"status": "error", "message": "Socket timeout during capture"}
    except socket.error as e:
        return {"status": "error", "message": f"Socket error: {str(e)}"}
    except Exception as e:
        return {"status": "error", "message": f"Unexpected error: {str(e)}"}

# -----------------------------
# ✅ CAPTURE ENDPOINT
# -----------------------------
@app.route('/capture', methods=['POST'])
def capture():
    """Capture a new fingerprint and store it in the database.
    
    Request Body:
        person_id (str): The unique identifier of the person
        finger_index (int): The index of the finger
                           1-10: Individual fingers
                           11: Two thumbs
                           12: Left four fingers
                           13: Right four fingers
    
    Returns:
        JSON: Contains status, message, and base64 encoded BMP image
              - status: 'success' or 'error'
              - message: Description of the operation result
              - bmp_base64: Base64 encoded fingerprint image (if successful)
    """
    data = request.get_json()
    person_id = data.get('person_id')
    finger_index = int(data.get('finger_index', 1))

    if not person_id:
        logging.error("Missing person_id in /capture request")
        return jsonify({"status": "error", "message": "Missing person_id"}), 400

    # Map finger index to description for logging
    finger_descriptions = {
        1: "Right Thumb", 2: "Right Index", 3: "Right Middle", 4: "Right Ring", 5: "Right Little",
        6: "Left Thumb", 7: "Left Index", 8: "Left Middle", 9: "Left Ring", 10: "Left Little",
        11: "Two Thumbs", 12: "Left Four Fingers", 13: "Right Four Fingers"
    }
    
    finger_desc = finger_descriptions.get(finger_index, f"Unknown ({finger_index})")
    logging.info(f"Calling capture_fingerprint_bmp({person_id}, {finger_index}) - {finger_desc}")
    
    result = capture_fingerprint_bmp(person_id, finger_index)
    logging.info(f"Bridge response: {result}")
    return jsonify(result)

# -----------------------------
# ✅ VERIFY ENDPOINT (1:1 Matching)
# -----------------------------
@app.route('/verify', methods=['POST'])
def verify():
    """Verify a captured fingerprint against specific person's stored template (1:1).

    Request Body:
        person_id (str): The unique identifier of the person to verify against
        finger_index (int): The index of the finger to verify
                           1-10: Individual fingers

    Returns:
        JSON: Contains verification result
              - status: 'success' or 'error'
              - message: Description of the verification result
              - verified: true/false (if verification succeeded)
              - score: Similarity score
    """
    data = request.get_json()
    person_id = data.get('person_id')
    finger_index = int(data.get('finger_index', 1))

    if not person_id:
        logging.error("Missing person_id in /verify request")
        return jsonify({"status": "error", "message": "Missing person_id"}), 400

    finger_descriptions = {
        1: "Right Thumb", 2: "Right Index", 3: "Right Middle", 4: "Right Ring", 5: "Right Little",
        6: "Left Thumb", 7: "Left Index", 8: "Left Middle", 9: "Left Ring", 10: "Left Little"
    }

    finger_desc = finger_descriptions.get(finger_index, f"Unknown ({finger_index})")
    logging.info(f"Calling verify for {person_id}, {finger_index} - {finger_desc}")

    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(30)
            s.connect(('127.0.0.1', 8123))
            command = f"VERIFY {person_id} {finger_index}\n"
            s.sendall(command.encode())

            data = b""
            while True:
                chunk = s.recv(4096)
                if not chunk:
                    break
                data += chunk

            decoded = data.decode("utf-8", errors="ignore")
            lines = decoded.splitlines()

            result = {"status": "error", "message": "No response", "verified": False, "score": 0}

            for line in lines:
                clean_line = line.strip().lstrip("\ufeff")
                logging.info(f"[Verify] {clean_line}")

                if "VERIFIED=true" in clean_line:
                    result["status"] = "success"
                    result["verified"] = True
                    result["message"] = "✅ Verification successful"
                elif "VERIFIED=false" in clean_line:
                    result["status"] = "success"
                    result["verified"] = False
                    result["message"] = "❌ Verification failed"
                elif "SCORE=" in clean_line:
                    try:
                        score_part = clean_line.split("SCORE=")[1].split(",")[0]
                        result["score"] = int(score_part)
                    except:
                        pass
                elif "ERROR" in clean_line.upper():
                    result["message"] = clean_line

            return jsonify(result)

    except Exception as e:
        logging.error(f"Verify error: {str(e)}")
        return jsonify({"status": "error", "message": f"Verify error: {str(e)}", "verified": False, "score": 0})

# -----------------------------
# ✅ IDENTIFY ENDPOINT (1:N Matching)
# -----------------------------
@app.route('/identify', methods=['POST'])
def identify():
    """Identify a fingerprint against all stored templates (1:N matching)."""
    logging.info("Calling identify operation (1:N matching)")

    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(30)
            s.connect(('127.0.0.1', 8123))
            command = "IDENTIFY\n"
            s.sendall(command.encode())

            data = b""
            while True:
                chunk = s.recv(4096)
                if not chunk:
                    break
                data += chunk

            decoded = data.decode("utf-8", errors="ignore")
            lines = decoded.splitlines()

            result = {"status": "error", "message": "No response", "person_id": None, "finger": None, "score": 0}

            for line in lines:
                clean_line = line.strip().lstrip("\ufeff")
                logging.info(f"[Identify] {clean_line}")

                if "PERSON_ID=" in clean_line:
                    try:
                        person_part = clean_line.split("PERSON_ID=")[1].split(",")[0]
                        result["person_id"] = person_part
                        result["status"] = "success"
                        result["message"] = "✅ Identification successful"
                    except:
                        pass
                elif "FINGER=" in clean_line:
                    try:
                        finger_part = clean_line.split("FINGER=")[1].split(",")[0]
                        result["finger"] = finger_part
                    except:
                        pass
                elif "SCORE=" in clean_line:
                    try:
                        score_part = clean_line.split("SCORE=")[1].split(",")[0]
                        result["score"] = int(score_part)
                    except:
                        pass
                elif "ERROR" in clean_line.upper():
                    result["message"] = clean_line

            return jsonify(result)

    except Exception as e:
        logging.error(f"Identify error: {str(e)}")
        return jsonify({"status": "error", "message": f"Identify error: {str(e)}", "person_id": None, "finger": None, "score": 0})

# -----------------------------
# ✅ LEGACY MATCH ENDPOINT (Redirects to Identify)
# -----------------------------
@app.route('/match', methods=['POST'])
def match():
    """Legacy match endpoint - redirects to identify for compatibility."""
    return identify()  # Redirect to identify since they're the same in our system

# -----------------------------
# ✅ HEALTH CHECK
# -----------------------------
@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint."""
    return jsonify({"status": "ok", "message": "Slaps Fingerprint API is running"})

if __name__ == '__main__':
    logging.info("🚀 Starting Slaps Fingerprint API on port 5001...")
    app.run(host='0.0.0.0', port=5001, debug=True)
