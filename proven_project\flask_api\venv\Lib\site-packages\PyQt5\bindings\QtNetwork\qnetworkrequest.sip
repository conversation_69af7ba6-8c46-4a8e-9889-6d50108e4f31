// qnetworkrequest.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LIC<PERSON><PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkRequest
{
%TypeHeaderCode
#include <qnetworkrequest.h>
%End

public:
    enum KnownHeaders
    {
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        ContentD<PERSON><PERSON><PERSON>eader,
        User<PERSON><PERSON><PERSON>eader,
        ServerHeader,
%If (Qt_5_12_0 -)
        IfModifiedSinceHeader,
%End
%If (Qt_5_12_0 -)
        ETagHeader,
%End
%If (Qt_5_12_0 -)
        IfMatchHeader,
%End
%If (Qt_5_12_0 -)
        IfNoneMatchHeader,
%End
    };

    enum Attribute
    {
        HttpStatusCodeAttribute,
        HttpReasonPhraseAttribute,
        RedirectionTargetAttribute,
        ConnectionEncryptedAttribute,
        CacheLoadControlAttribute,
        CacheSaveControlAttribute,
        SourceIsFromCacheAttribute,
        DoNotBufferUploadDataAttribute,
        HttpPipeliningAllowedAttribute,
        HttpPipeliningWasUsedAttribute,
        CustomVerbAttribute,
        CookieLoadControlAttribute,
        AuthenticationReuseAttribute,
        CookieSaveControlAttribute,
        BackgroundRequestAttribute,
%If (Qt_5_3_0 -)
        SpdyAllowedAttribute,
%End
%If (Qt_5_3_0 -)
        SpdyWasUsedAttribute,
%End
%If (Qt_5_5_0 -)
        EmitAllUploadProgressSignalsAttribute,
%End
%If (Qt_5_6_0 -)
        FollowRedirectsAttribute,
%End
%If (Qt_5_8_0 -)
        HTTP2AllowedAttribute,
%End
%If (Qt_5_15_0 -)
        Http2AllowedAttribute,
%End
%If (Qt_5_8_0 -)
        HTTP2WasUsedAttribute,
%End
%If (Qt_5_15_0 -)
        Http2WasUsedAttribute,
%End
%If (Qt_5_9_0 -)
        OriginalContentLengthAttribute,
%End
%If (Qt_5_9_0 -)
        RedirectPolicyAttribute,
%End
%If (Qt_5_11_0 -)
        Http2DirectAttribute,
%End
%If (Qt_5_14_0 -)
        AutoDeleteReplyOnFinishAttribute,
%End
        User,
        UserMax,
    };

    enum CacheLoadControl
    {
        AlwaysNetwork,
        PreferNetwork,
        PreferCache,
        AlwaysCache,
    };

    enum LoadControl
    {
        Automatic,
        Manual,
    };

    enum Priority
    {
        HighPriority,
        NormalPriority,
        LowPriority,
    };

    explicit QNetworkRequest(const QUrl &url = QUrl());
    QNetworkRequest(const QNetworkRequest &other);
    ~QNetworkRequest();
    QUrl url() const;
    void setUrl(const QUrl &url);
    QVariant header(QNetworkRequest::KnownHeaders header) const;
    void setHeader(QNetworkRequest::KnownHeaders header, const QVariant &value);
    bool hasRawHeader(const QByteArray &headerName) const;
    QList<QByteArray> rawHeaderList() const;
    QByteArray rawHeader(const QByteArray &headerName) const;
    void setRawHeader(const QByteArray &headerName, const QByteArray &value);
    QVariant attribute(QNetworkRequest::Attribute code, const QVariant &defaultValue = QVariant()) const;
    void setAttribute(QNetworkRequest::Attribute code, const QVariant &value);
%If (PyQt_SSL)
    QSslConfiguration sslConfiguration() const;
%End
%If (PyQt_SSL)
    void setSslConfiguration(const QSslConfiguration &configuration);
%End
    bool operator==(const QNetworkRequest &other) const;
    bool operator!=(const QNetworkRequest &other) const;
    void setOriginatingObject(QObject *object /KeepReference/);
    QObject *originatingObject() const;
    QNetworkRequest::Priority priority() const;
    void setPriority(QNetworkRequest::Priority priority);
    void swap(QNetworkRequest &other /Constrained/);
%If (Qt_5_6_0 -)
    int maximumRedirectsAllowed() const;
%End
%If (Qt_5_6_0 -)
    void setMaximumRedirectsAllowed(int maximumRedirectsAllowed);
%End
%If (Qt_5_9_0 -)

    enum RedirectPolicy
    {
        ManualRedirectPolicy,
        NoLessSafeRedirectPolicy,
        SameOriginRedirectPolicy,
        UserVerifiedRedirectPolicy,
    };

%End
%If (Qt_5_13_0 -)
    QString peerVerifyName() const;
%End
%If (Qt_5_13_0 -)
    void setPeerVerifyName(const QString &peerName);
%End
%If (Qt_5_14_0 -)
    QHttp2Configuration http2Configuration() const;
%End
%If (Qt_5_14_0 -)
    void setHttp2Configuration(const QHttp2Configuration &configuration);
%End
%If (Qt_5_15_0 -)

    enum TransferTimeoutConstant
    {
        DefaultTransferTimeoutConstant,
    };

%End
%If (Qt_5_15_0 -)
    int transferTimeout() const;
%End
%If (Qt_5_15_0 -)
    void setTransferTimeout(int timeout = QNetworkRequest::TransferTimeoutConstant::DefaultTransferTimeoutConstant);
%End
};
