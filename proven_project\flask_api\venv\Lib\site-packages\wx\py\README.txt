=====================================
 PyCrust - The Flakiest Python Shell
=====================================

Half-baked by <PERSON> (<EMAIL>)

Orbtech - "Your source for Python programming expertise."
Sample all our half-baked Python goods at www.orbtech.com.


What is PyCrust?
----------------

PyCrust is an interactive Python environment written in Python.
PyCrust components can run standalone or be integrated into other
development environments and/or other Python applications.

PyCrust comes with an interactive Python shell (PyShell), an
interactive namespace/object tree control (PyFilling) and an
integrated, split-window combination of the two (PyCrust).


What is PyCrust good for?
-------------------------

Have you ever tried to bake a pie without one? Well, you shouldn't
build a Python program without a PyCrust either.


What else do I need to use PyCrust?
-----------------------------------

PyCrust requires Python 2.2 or later, and wxPython 2.4 or later.
PyCrust uses wxPython and the Scintilla wrapper (wxStyledTextCtrl).
Python is available at http://www.python.org/.  wxPython is available
at http://www.wxpython.org/.


Where can I get the latest version of PyCrust?
----------------------------------------------

The latest production version ships with wxPython.  The latest
developer version is available in the wxWindows CVS at:
http://cvs.wxwindows.org/viewcvs.cgi/


Where is the PyCrust project hosted?
------------------------------------

The old answer was "At SourceForge, of course." The SourceForge
summary page is still available at:
http://sourceforge.net/projects/pycrust/

The new answer is that there is no longer a need for a separate
project.  Simply install wxPython and you'll have everything you need.


I found a bug in PyCrust, what do I do with it?
-----------------------------------------------

You can send it to <NAME_EMAIL>.


I want a new feature added to PyCrust. Will you do it?
------------------------------------------------------

Flattery and money will get you anything. Short of that, you can send
me a request and I'll see what I can do.


Does PyCrust have a mailing list full of wonderful people?
----------------------------------------------------------

As a matter of fact, we do. Join the PyCrust mailing lists at:
http://sourceforge.net/mail/?group_id=31263


