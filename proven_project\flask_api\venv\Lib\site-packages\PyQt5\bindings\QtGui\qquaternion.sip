// qquaternion.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qquaternion.h>
%End

class QQuaternion
{
%TypeHeaderCode
#include <qquaternion.h>
%End

%PickleCode
    sipRes = Py_BuildValue((char *)"dddd", (double)sipCpp->scalar(),
            (double)sipCpp->x(), (double)sipCpp->y(), (double)sipCpp->z());
%End

public:
    QQuaternion();
    QQuaternion(float aScalar, float xpos, float ypos, float zpos);
    QQuaternion(float aScalar, const QVector3D &aVector);
    explicit QQuaternion(const QVector4D &aVector);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *scalar = PyFloat_FromDouble(sipCpp->scalar());
        PyObject *x = PyFloat_FromDouble(sipCpp->x());
        PyObject *y = PyFloat_FromDouble(sipCpp->y());
        PyObject *z = PyFloat_FromDouble(sipCpp->z());
        
        if (scalar && x && y && z)
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromFormat("PyQt5.QtGui.QQuaternion(%R, %R, %R, %R)",
                    scalar, x, y, z);
        #else
            sipRes = PyString_FromString("PyQt5.QtGui.QQuaternion(");
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(scalar));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(x));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(y));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(z));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
        }
        
        Py_XDECREF(scalar);
        Py_XDECREF(x);
        Py_XDECREF(y);
        Py_XDECREF(z);
%End

    float length() const;
    float lengthSquared() const;
    QQuaternion normalized() const;
    void normalize();
    QVector3D rotatedVector(const QVector3D &vector) const;
    static QQuaternion fromAxisAndAngle(const QVector3D &axis, float angle);
    static QQuaternion fromAxisAndAngle(float x, float y, float z, float angle);
    static QQuaternion slerp(const QQuaternion &q1, const QQuaternion &q2, float t);
    static QQuaternion nlerp(const QQuaternion &q1, const QQuaternion &q2, float t);
    bool isNull() const;
    bool isIdentity() const;
    float x() const;
    float y() const;
    float z() const;
    float scalar() const;
    void setX(float aX);
    void setY(float aY);
    void setZ(float aZ);
    void setScalar(float aScalar);
    QQuaternion conjugate() const;
    QQuaternion &operator+=(const QQuaternion &quaternion);
    QQuaternion &operator-=(const QQuaternion &quaternion);
    QQuaternion &operator*=(float factor);
    QQuaternion &operator*=(const QQuaternion &quaternion);
    QQuaternion &operator/=(float divisor);
    void setVector(const QVector3D &aVector);
    QVector3D vector() const;
    void setVector(float aX, float aY, float aZ);
    QVector4D toVector4D() const;
%If (Qt_5_5_0 -)
    void getAxisAndAngle(QVector3D *axis /Out/, float *angle) const;
%End
%If (Qt_5_5_0 -)
    void getEulerAngles(float *pitch, float *yaw, float *roll) const;
%End
%If (Qt_5_5_0 -)
    static QQuaternion fromEulerAngles(float pitch, float yaw, float roll);
%End
%If (Qt_5_5_0 -)
    QMatrix3x3 toRotationMatrix() const;
%End
%If (Qt_5_5_0 -)
    static QQuaternion fromRotationMatrix(const QMatrix3x3 &rot3x3);
%End
%If (Qt_5_5_0 -)
    void getAxes(QVector3D *xAxis /Out/, QVector3D *yAxis /Out/, QVector3D *zAxis /Out/) const;
%End
%If (Qt_5_5_0 -)
    static QQuaternion fromAxes(const QVector3D &xAxis, const QVector3D &yAxis, const QVector3D &zAxis);
%End
%If (Qt_5_5_0 -)
    static QQuaternion fromDirection(const QVector3D &direction, const QVector3D &up);
%End
%If (Qt_5_5_0 -)
    static QQuaternion rotationTo(const QVector3D &from, const QVector3D &to);
%End
%If (Qt_5_5_0 -)
    static float dotProduct(const QQuaternion &q1, const QQuaternion &q2);
%End
%If (Qt_5_5_0 -)
    QQuaternion inverted() const;
%End
%If (Qt_5_5_0 -)
    QQuaternion conjugated() const;
%End
%If (Qt_5_5_0 -)
    QVector3D toEulerAngles() const;
%End
%If (Qt_5_5_0 -)
    static QQuaternion fromEulerAngles(const QVector3D &eulerAngles);
%End
};

const QQuaternion operator*(const QQuaternion &q1, const QQuaternion &q2);
bool operator==(const QQuaternion &q1, const QQuaternion &q2);
bool operator!=(const QQuaternion &q1, const QQuaternion &q2);
const QQuaternion operator+(const QQuaternion &q1, const QQuaternion &q2);
const QQuaternion operator-(const QQuaternion &q1, const QQuaternion &q2);
const QQuaternion operator*(float factor, const QQuaternion &quaternion);
const QQuaternion operator*(const QQuaternion &quaternion, float factor);
const QQuaternion operator-(const QQuaternion &quaternion);
const QQuaternion operator/(const QQuaternion &quaternion, float divisor);
bool qFuzzyCompare(const QQuaternion &q1, const QQuaternion &q2);
QDataStream &operator<<(QDataStream &, const QQuaternion & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QQuaternion & /Constrained/) /ReleaseGIL/;
%If (Qt_5_5_0 -)
QVector3D operator*(const QQuaternion &quaternion, const QVector3D &vec);
%End
