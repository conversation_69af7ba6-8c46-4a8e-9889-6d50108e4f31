// qabstractprintdialog.sip generated by MetaSIP
//
// This file is part of the QtPrintSupport Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_Printer)

class QAbstractPrintDialog : public QDialog
{
%TypeHeaderCode
#include <qabstractprintdialog.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
    #if defined(SIP_FEATURE_PyQt_PrintDialog)
        {sipName_QPageSetupDialog, &sipType_QPageSetupDialog, -1, 1},
    #else
        {0, 0, -1, 1},
    #endif
    #if defined(SIP_FEATURE_PyQt_PrintPreviewWidget)
        {sipName_QPrintPreviewWidget, &sipType_QPrintPreviewWidget, -1, 2},
    #else
        {0, 0, -1, 2},
    #endif
    #if defined(SIP_FEATURE_PyQt_PrintPreviewDialog)
        {sipName_QPrintPreviewDialog, &sipType_QPrintPreviewDialog, -1, 3},
    #else
        {0, 0, -1, 3},
    #endif
    #if defined(SIP_FEATURE_PyQt_Printer)
        {sipName_QAbstractPrintDialog, &sipType_QAbstractPrintDialog, 4, -1},
    #else
        {0, 0, 4, -1},
    #endif
    #if defined(SIP_FEATURE_PyQt_PrintDialog)
        {sipName_QPrintDialog, &sipType_QPrintDialog, -1, -1},
    #else
        {0, 0, -1, -1},
    #endif
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum PrintRange
    {
        AllPages,
        Selection,
        PageRange,
        CurrentPage,
    };

%If (Py_v3)

    enum PrintDialogOption
    {
        None /PyName=None_/,
        PrintToFile,
        PrintSelection,
        PrintPageRange,
        PrintCollateCopies,
        PrintShowPageSize,
        PrintCurrentPage,
    };

%End
%If (!Py_v3)
// Backward compatible PrintDialogOption for Python v2.
// Note that we have to duplicate the whole enum because MetaSIP doesn't
// support handwritten code in enum definitions.

enum PrintDialogOption {
    None,
    None /PyName=None_/,
    PrintToFile,
    PrintSelection,
    PrintPageRange,
    PrintCollateCopies,
    PrintShowPageSize,
    PrintCurrentPage
};
%End
    typedef QFlags<QAbstractPrintDialog::PrintDialogOption> PrintDialogOptions;
%If (PyQt_PrintDialog)
    QAbstractPrintDialog(QPrinter *printer, QWidget *parent /TransferThis/ = 0);
    virtual ~QAbstractPrintDialog();
    virtual int exec() = 0 /PyName=exec_,ReleaseGIL/;
%If (Py_v3)
    virtual int exec() = 0 /ReleaseGIL/;
%End
    void setPrintRange(QAbstractPrintDialog::PrintRange range);
    QAbstractPrintDialog::PrintRange printRange() const;
    void setMinMax(int min, int max);
    int minPage() const;
    int maxPage() const;
    void setFromTo(int fromPage, int toPage);
    int fromPage() const;
    int toPage() const;
    QPrinter *printer() const;
    void setOptionTabs(const QList<QWidget *> &tabs);
%If (Qt_5_6_0 -)
    void setEnabledOptions(QAbstractPrintDialog::PrintDialogOptions options);
%End
%If (Qt_5_6_0 -)
    QAbstractPrintDialog::PrintDialogOptions enabledOptions() const;
%End

private:
    QAbstractPrintDialog(const QAbstractPrintDialog &);

public:
%End // PyQt_PrintDialog
};

%End
%If (PyQt_Printer)
QFlags<QAbstractPrintDialog::PrintDialogOption> operator|(QAbstractPrintDialog::PrintDialogOption f1, QFlags<QAbstractPrintDialog::PrintDialogOption> f2);
%End

%ModuleHeaderCode
// Imports from QtCore.
typedef sipErrorState (*pyqt5_qtprintsupport_get_connection_parts_t)(PyObject *, QObject *, const char *, bool, QObject **, QByteArray &);
extern pyqt5_qtprintsupport_get_connection_parts_t pyqt5_qtprintsupport_get_connection_parts;
%End

%ModuleCode
// Imports from QtCore.
pyqt5_qtprintsupport_get_connection_parts_t pyqt5_qtprintsupport_get_connection_parts;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt5_qtprintsupport_get_connection_parts = (pyqt5_qtprintsupport_get_connection_parts_t)sipImportSymbol("pyqt5_get_connection_parts");
Q_ASSERT(pyqt5_qtprintsupport_get_connection_parts);
%End
