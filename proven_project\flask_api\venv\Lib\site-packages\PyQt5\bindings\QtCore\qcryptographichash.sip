// qcryptographichash.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCryptographicHash
{
%TypeHeaderCode
#include <qcryptographichash.h>
%End

public:
    enum Algorithm
    {
        Md4,
        <PERSON>d5,
        <PERSON>ha1,
        <PERSON><PERSON>224,
        <PERSON><PERSON>256,
        <PERSON>ha384,
        <PERSON>ha512,
%If (Qt_5_1_0 -)
        Sha3_224,
%End
%If (Qt_5_1_0 -)
        Sha3_256,
%End
%If (Qt_5_1_0 -)
        Sha3_384,
%End
%If (Qt_5_1_0 -)
        Sha3_512,
%End
%If (Qt_5_9_2 -)
        Keccak_224,
%End
%If (Qt_5_9_2 -)
        Keccak_256,
%End
%If (Qt_5_9_2 -)
        Keccak_384,
%End
%If (Qt_5_9_2 -)
        Keccak_512,
%End
    };

    explicit QCryptographicHash(QCryptographicHash::Algorithm method);
    ~QCryptographicHash();
    void reset();
    void addData(const char *data /Array/, int length /ArraySize/);
    void addData(const QByteArray &data);
    bool addData(QIODevice *device);
    QByteArray result() const;
    static QByteArray hash(const QByteArray &data, QCryptographicHash::Algorithm method);
%If (Qt_5_12_0 -)
    static int hashLength(QCryptographicHash::Algorithm method);
%End

private:
    QCryptographicHash(const QCryptographicHash &);
};
