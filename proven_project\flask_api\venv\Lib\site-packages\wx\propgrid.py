# This file is generated by wxPython's SIP generator.  Do not edit by hand.
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License

from ._propgrid import *

import wx

PG_LABEL = "@!"
PG_LABEL_STRING = PG_LABEL
PG_NULL_BITMAP = wx.NullBitmap
PG_COLOUR_BLACK = wx.BLACK
PG_DEFAULT_IMAGE_SIZE = wx.Size(-1, -1)

def _PGProperty_SetAttributes(self, attributes):
    """
    Set the property's attributes from a Python dictionary.
    """
    for name,value in attributes.items():
        self.SetAttribute(name, value)
PGProperty.SetAttributes = _PGProperty_SetAttributes
del _PGProperty_SetAttributes
def _PGProperty_GetClientObject(self, n):
    """
    Alias for :meth:`GetClientData`
    """
    return self.GetClientData(n)
PGProperty.GetClientObject = _PGProperty_GetClientObject
del _PGProperty_GetClientObject
def _PGProperty_SetClientObject(self, n, data):
    """
    Alias for :meth:`SetClientData`
    """
    self.SetClientData(n, data)
PGProperty.SetClientObject = _PGProperty_SetClientObject
del _PGProperty_SetClientObject
def _PGChoices___getitem__(self, index):
    """
    Returns a reference to a :class:PGChoiceEntry using Python list syntax.
    """
    return self.Item(index)
PGChoices.__getitem__ = _PGChoices___getitem__
del _PGChoices___getitem__
def _PGChoices___len__(self):
    return self.GetCount()
PGChoices.__len__ = _PGChoices___len__
del _PGChoices___len__
PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

PG_ATTR_DEFAULT_VALUE             = u"DefaultValue"
PG_ATTR_MIN                       = u"Min"
PG_ATTR_MAX                       = u"Max"
PG_ATTR_UNITS                     = u"Units"
PG_ATTR_HINT                      = u"Hint"
PG_ATTR_INLINE_HELP               = PG_ATTR_HINT
PG_ATTR_AUTOCOMPLETE              = u"AutoComplete"
PG_BOOL_USE_CHECKBOX              = u"UseCheckbox"
PG_BOOL_USE_DOUBLE_CLICK_CYCLING  = u"UseDClickCycling"
PG_FLOAT_PRECISION                = u"Precision"
PG_STRING_PASSWORD                = u"Password"
PG_UINT_BASE                      = u"Base"
PG_UINT_PREFIX                    = u"Prefix"
PG_FILE_WILDCARD                  = u"Wildcard"
PG_FILE_SHOW_FULL_PATH            = u"ShowFullPath"
PG_FILE_SHOW_RELATIVE_PATH        = u"ShowRelativePath"
PG_FILE_INITIAL_PATH              = u"InitialPath"
PG_FILE_DIALOG_TITLE              = u"DialogTitle"
PG_DIALOG_TITLE                   = u"DialogTitle"
PG_FILE_DIALOG_STYLE              = u"DialogStyle"
PG_DIR_DIALOG_MESSAGE             = u"DialogMessage"
PG_ARRAY_DELIMITER                = u"Delimiter"
PG_DATE_FORMAT                    = u"DateFormat"
PG_DATE_PICKER_STYLE              = u"PickerStyle"
PG_ATTR_SPINCTRL_STEP             = u"Step"
PG_ATTR_SPINCTRL_WRAP             = u"Wrap"
PG_ATTR_SPINCTRL_MOTION           = u"MotionSpin"
PG_ATTR_SPINCTRL_MOTIONSPIN       = PG_ATTR_SPINCTRL_MOTION
PG_ATTR_MULTICHOICE_USERSTRINGMODE= u"UserStringMode"
PG_COLOUR_ALLOW_CUSTOM            = u"AllowCustom"
PG_COLOUR_HAS_ALPHA               = u"HasAlpha"

NullProperty                      = None
PGChoicesEmptyData                = None

def _PGMultiButton_AddButton(self, label, id=-2):
    """
    A simple wrapper around the PGMultiButton.Add method, for backwards compatibility.
    """
    self.Add(label, id)
PGMultiButton.AddButton = _PGMultiButton_AddButton
del _PGMultiButton_AddButton
def _PGMultiButton_AddBitmapButton(self, bitmap, id=-2):
    """
    A simple wrapper around the PGMultiButton.Add method, for backwards compatibility.
    """
    self.Add(bitmap, id)
PGMultiButton.AddBitmapButton = _PGMultiButton_AddBitmapButton
del _PGMultiButton_AddBitmapButton
def _PropertyGridInterface_MapType(self, class_, factory):
    """
    Registers Python type/class to property mapping.
    
    :param `factory`: Property builder function/class.
    """
    global _type2property
    if _type2property is None:
        raise AssertionError("call only after a propertygrid or "
                             "manager instance constructed")
    _type2property[class_] = factory
PropertyGridInterface.MapType = _PropertyGridInterface_MapType
del _PropertyGridInterface_MapType
def _PropertyGridInterface_DoDefaultTypeMappings(self):
    """
    Add built-in properties to the map.
    """
    import sys
    global _type2property
    if _type2property is not None:
        return
    _type2property = dict()
    
    _type2property[str] = StringProperty
    if sys.version_info.major < 2:
        _type2property[unicode] = StringProperty
    _type2property[int] = IntProperty
    _type2property[float] = FloatProperty
    _type2property[bool] = BoolProperty
    _type2property[list] = ArrayStringProperty
    _type2property[tuple] = ArrayStringProperty
    _type2property[wx.Font] = FontProperty
    _type2property[wx.Colour] = ColourProperty
    #_type2property[wx.Size] = SizeProperty
    #_type2property[wx.Point] = PointProperty
    #_type2property[wx.FontData] = FontDataProperty
PropertyGridInterface.DoDefaultTypeMappings = _PropertyGridInterface_DoDefaultTypeMappings
del _PropertyGridInterface_DoDefaultTypeMappings
def _PropertyGridInterface_DoDefaultValueTypeMappings(self):
    """
    Map pg value type ids to getter methods.
    """
    global _vt2getter
    if _vt2getter is not None:
        return
    _vt2getter = dict()
PropertyGridInterface.DoDefaultValueTypeMappings = _PropertyGridInterface_DoDefaultValueTypeMappings
del _PropertyGridInterface_DoDefaultValueTypeMappings
def _PropertyGridInterface_GetPropertyValues(self, dict_=None, as_strings=False, inc_attributes=False, flags=PG_ITERATE_PROPERTIES):
    """
    Returns all property values in the grid.
    
    :param `dict_`: A diftionary to fill with the property values.
        If not given, then a new one is created. The dict_ can be an
        object as well, in which case it's __dict__ is used.
    :param `as_strings`: if True, then string representations of values
        are fetched instead of native types. Useful for config and such.
    :param `inc_attributes`: if True, then property attributes are added
        in the form of ``"@<propname>@<attr>"``.
    :param `flags`: Flags to pass to the iterator. See :ref:`wx.propgrid.PG_ITERATOR_FLAGS`.
    :returns: A dictionary with values. It is always a dictionary,
        so if dict_ was an object with __dict__ attribute, then that
        attribute is returned.
    """
    if dict_ is None:
        dict_ = {}
    elif hasattr(dict_,'__dict__'):
        dict_ = dict_.__dict__
    
    getter = self.GetPropertyValue if not as_strings else self.GetPropertyValueAsString
    
    it = self.GetVIterator(flags)
    while not it.AtEnd():
        p = it.GetProperty()
        name = p.GetName()
        dict_[name] = getter(p)
    
        if inc_attributes:
            attrs = p.GetAttributes()
            if attrs and len(attrs):
                dict_['@%s@attr'%name] = attrs
    
        it.Next()
    
    return dict_
PropertyGridInterface.GetPropertyValues = _PropertyGridInterface_GetPropertyValues
del _PropertyGridInterface_GetPropertyValues
def _PropertyGridInterface_SetPropertyValues(self, dict_, autofill=False):
    """
    Sets property values from a dictionary.
    
    :param `dict_`: the source of the property values to set, which can be
        either a dictionary or an object with a __dict__ attribute.
    :param `autofill`: If true, keys with not relevant properties are
        auto-created. For more info, see :method:`AutoFill`.
    
    :note:
      * Keys starting with underscore are ignored.
      * Attributes can be set with entries named like "@<propname>@<attr>".
    """
    if dict_ is None:
        dict_ = {}
    elif hasattr(dict_,'__dict__'):
        dict_ = dict_.__dict__
    attr_dicts = []
    
    def set_sub_obj(k0, dict_):
        for k,v in dict_.items():
            if k[0] != '_':
                if k.endswith('@attr'):
                    attr_dicts.append((k[1:-5],v))
                else:
                    try:
                        self.SetPropertyValue(k,v)
                    except:
                        try:
                            if autofill:
                                self._AutoFillOne(k0,k,v)
                                continue
                        except:
                            if isinstance(v,dict):
                                set_sub_obj(k,v)
                            elif hasattr(v,'__dict__'):
                                set_sub_obj(k,v.__dict__)
    
        for k,v in attr_dicts:
            p = self.GetPropertyByName(k)
            if not p:
                raise AssertionError("No such property: '%s'"%k)
            for an,av in v.items():
                p.SetAttribute(an, av)
    
    
    cur_page = False
    is_manager = isinstance(self, PropertyGridManager)
    
    try:
        set_sub_obj(self.GetGrid().GetRoot(), dict_)
    except:
        import traceback
        traceback.print_exc()
    
    self.Refresh()
PropertyGridInterface.SetPropertyValues = _PropertyGridInterface_SetPropertyValues
del _PropertyGridInterface_SetPropertyValues
def _PropertyGridInterface__AutoFillMany(self,cat,dict_):
    for k,v in dict_.items():
        self._AutoFillOne(cat,k,v)
PropertyGridInterface._AutoFillMany = _PropertyGridInterface__AutoFillMany
del _PropertyGridInterface__AutoFillMany
def _PropertyGridInterface__AutoFillOne(self,cat,k,v):
    global _type2property
    factory = _type2property.get(v.__class__,None)
    if factory:
        self.AppendIn(cat, factory(k,k,v))
    elif hasattr(v,'__dict__'):
        cat2 = self.AppendIn(cat, PropertyCategory(k))
        self._AutoFillMany(cat2, v.__dict__)
    elif isinstance(v, dict):
        cat2 = self.AppendIn(cat, PropertyCategory(k))
        self._AutoFillMany(cat2, v)
    elif not k.startswith('_'):
        raise AssertionError("member '%s' is of unregistered type/"
                             "class '%s'"%(k,v.__class__))
PropertyGridInterface._AutoFillOne = _PropertyGridInterface__AutoFillOne
del _PropertyGridInterface__AutoFillOne
def _PropertyGridInterface_AutoFill(self, obj, parent=None):
    """
    "Clears properties and re-fills to match members and values of
    the given object or dictionary obj.
    """
    self.edited_objects[parent] = obj
    
    cur_page = False
    is_manager = isinstance(self, PropertyGridManager)
    
    if not parent:
        if is_manager:
            page = self.GetCurrentPage()
            page.Clear()
            parent = page.GetRoot()
        else:
            self.Clear()
            parent = self.GetGrid().GetRoot()
    else:
        it = self.GetIterator(PG_ITERATE_PROPERTIES, parent)
        it.Next()  # Skip the parent
        while not it.AtEnd():
            p = it.GetProperty()
            if not p.IsSomeParent(parent):
                break
    
            self.DeleteProperty(p)
    
            name = p.GetName()
            it.Next()
    
    if not is_manager or page == self.GetCurrentPage():
        self.Freeze()
        cur_page = True
    
    try:
        self._AutoFillMany(parent,obj.__dict__)
    except:
        import traceback
        traceback.print_exc()
    
    if cur_page:
        self.Thaw()
PropertyGridInterface.AutoFill = _PropertyGridInterface_AutoFill
del _PropertyGridInterface_AutoFill
def _PropertyGridInterface_RegisterEditor(self, editor, editorName=None):
    """
    Register a new editor, either an instance or a class.
    """
    if not isinstance(editor, PGEditor):
        editor = editor()
    if not editorName:
        editorName = editor.__class__.__name__
    try:
        self._editor_instances.append(editor)
    except:
        self._editor_instances = [editor]
    return PropertyGrid.DoRegisterEditorClass(editor, editorName)
PropertyGridInterface.RegisterEditor = _PropertyGridInterface_RegisterEditor
del _PropertyGridInterface_RegisterEditor
def _PropertyGridInterface_GetPropertyClientData(self, p):
    if isinstance(p, str):
        p = self.GetPropertyByName(p)
    return p.GetClientData()
PropertyGridInterface.GetPropertyClientData = _PropertyGridInterface_GetPropertyClientData
del _PropertyGridInterface_GetPropertyClientData
def _PropertyGridInterface_SetPropertyClientData(self, p, data):
    if isinstance(p, str):
        p = self.GetPropertyByName(p)
    return p.SetClientData(data)
PropertyGridInterface.SetPropertyClientData = _PropertyGridInterface_SetPropertyClientData
del _PropertyGridInterface_SetPropertyClientData
def _PropertyGridInterface_GetPyIterator(self, flags=PG_ITERATE_DEFAULT, firstProperty=None):
    """
    Returns a pythonic property iterator for a single :ref:`PropertyGrid`
    or page in :ref:`PropertyGridManager`. Arguments are same as for
    :ref:`GetIterator`.
    
    The following example demonstrates iterating absolutely all items in
    a single grid::
    
        iterator = propGrid.GetPyIterator(wx.propgrid.PG_ITERATE_ALL)
        for prop in iterator:
            print(prop)
    
    :see: `wx.propgrid.PropertyGridInterface.Properties`
          `wx.propgrid.PropertyGridInterface.Items`
    """
    it = self.GetIterator(flags, firstProperty)
    while not it.AtEnd():
        yield it.GetProperty()
        it.Next()
PropertyGridInterface.GetPyIterator = _PropertyGridInterface_GetPyIterator
del _PropertyGridInterface_GetPyIterator
def _PropertyGridInterface_GetPyVIterator(self, flags=PG_ITERATE_DEFAULT):
    """
    Similar to :ref:`GetVIterator` but returns a pythonic iterator.
    """
    it = self.GetVIterator(flags)
    while not it.AtEnd():
        yield it.GetProperty()
        it.Next()
PropertyGridInterface.GetPyVIterator = _PropertyGridInterface_GetPyVIterator
del _PropertyGridInterface_GetPyVIterator
def _PropertyGridInterface__Properties(self):
    """
    This attribute is a pythonic iterator over all properties in
    this `PropertyGrid` property container. It will only skip
    categories and private child properties. Usage is simple::
    
        for prop in propGrid.Properties:
            print(prop)
    
    :see: `wx.propgrid.PropertyGridInterface.Items`
          `wx.propgrid.PropertyGridInterface.GetPyIterator`
    """
    it = self.GetIterator(PG_ITERATE_NORMAL)
    while not it.AtEnd():
        yield it.GetProperty()
        it.Next()
PropertyGridInterface._Properties = _PropertyGridInterface__Properties
del _PropertyGridInterface__Properties
PropertyGridInterface.Properties = property(PropertyGridInterface._Properties)
def _PropertyGridInterface__Items(self):
    """
    This attribute is a pythonic iterator over all items in this
    `PropertyGrid` property container, excluding only private child
    properties. Usage is simple::
    
        for prop in propGrid.Items:
            print(prop)
    
    :see: `wx.propgrid.PropertyGridInterface.Properties`
          `wx.propgrid.PropertyGridInterface.GetPyVIterator`
    """
    it = self.GetVIterator(PG_ITERATE_NORMAL | PG_ITERATE_CATEGORIES)
    while not it.AtEnd():
        yield it.GetProperty()
        it.Next()
PropertyGridInterface._Items = _PropertyGridInterface__Items
del _PropertyGridInterface__Items
PropertyGridInterface.Items = property(PropertyGridInterface._Items)
_type2property = None
_vt2getter = None

PropertyGridInterface.GetValues = PropertyGridInterface.GetPropertyValues
PropertyGridInterface.SetValues = PropertyGridInterface.SetPropertyValues

def _ArrayPGProperty___repr__(self):
    return "ArrayPGProperty: " + repr(list(self))
ArrayPGProperty.__repr__ = _ArrayPGProperty___repr__
del _ArrayPGProperty___repr__
_PropertyGrid__init__orig = PropertyGrid.__init__
def _PropertyGrid__init__(self, *args, **kw):
    _PropertyGrid__init__orig(self, *args, **kw)
    self.DoDefaultTypeMappings()
    self.edited_objects = {}
    self.DoDefaultValueTypeMappings()
    if not hasattr(self.__class__, '_vt2setter'):
        self.__class__._vt2setter = {}
PropertyGrid.__init__ = _PropertyGrid__init__

EVT_PG_CHANGED = wx.PyEventBinder( wxEVT_PG_CHANGED, 1 )
EVT_PG_CHANGING = wx.PyEventBinder( wxEVT_PG_CHANGING, 1 )
EVT_PG_SELECTED = wx.PyEventBinder( wxEVT_PG_SELECTED, 1 )
EVT_PG_HIGHLIGHTED = wx.PyEventBinder( wxEVT_PG_HIGHLIGHTED, 1 )
EVT_PG_RIGHT_CLICK = wx.PyEventBinder( wxEVT_PG_RIGHT_CLICK, 1 )
EVT_PG_PAGE_CHANGED = wx.PyEventBinder( wxEVT_PG_PAGE_CHANGED, 1 )
EVT_PG_ITEM_COLLAPSED = wx.PyEventBinder( wxEVT_PG_ITEM_COLLAPSED, 1 )
EVT_PG_ITEM_EXPANDED = wx.PyEventBinder( wxEVT_PG_ITEM_EXPANDED, 1 )
EVT_PG_DOUBLE_CLICK = wx.PyEventBinder( wxEVT_PG_DOUBLE_CLICK, 1 )
EVT_PG_LABEL_EDIT_BEGIN = wx.PyEventBinder( wxEVT_PG_LABEL_EDIT_BEGIN, 1 )
EVT_PG_LABEL_EDIT_ENDING = wx.PyEventBinder( wxEVT_PG_LABEL_EDIT_ENDING, 1 )
EVT_PG_COL_BEGIN_DRAG = wx.PyEventBinder( wxEVT_PG_COL_BEGIN_DRAG, 1 )
EVT_PG_COL_DRAGGING = wx.PyEventBinder( wxEVT_PG_COL_DRAGGING, 1 )
EVT_PG_COL_END_DRAG = wx.PyEventBinder( wxEVT_PG_COL_END_DRAG, 1 )

PyArrayStringProperty = wx.deprecated(ArrayStringProperty, "Use ArrayStringProperty instead.")
PyChoiceEditor = wx.deprecated(PGChoiceEditor, "Use PGChoiceEditor instead.")
PyColourProperty = wx.deprecated(ColourProperty, "Use ColourProperty instead.")
PyComboBoxEditor = wx.deprecated(PGComboBoxEditor, "Use PGComboBoxEditor instead.")
PyEditEnumProperty = wx.deprecated(EditEnumProperty, "Use PGEditEnumProperty instead.")
PyEditor = wx.deprecated(PGEditor, "Use PGEditor instead.")
PyEditorDialogAdapter = wx.deprecated(PGEditorDialogAdapter, "Use PGEditorDialogAdapter instead.")
PyEnumProperty = wx.deprecated(EnumProperty, "Use EnumProperty instead.")
PyFileProperty = wx.deprecated(FileProperty, "Use FileProperty instead.")
PyFlagsProperty = wx.deprecated(FlagsProperty, "Use FlagsProperty instead.")
PyFloatProperty = wx.deprecated(FloatProperty, "Use FloatProperty instead.")
PyFontProperty = wx.deprecated(FontProperty, "Use FontProperty instead.")
PyIntProperty = wx.deprecated(IntProperty, "Use IntProperty instead.")
PyLongStringProperty = wx.deprecated(LongStringProperty, "Use LongStringProperty instead.")
PyProperty = wx.deprecated(PGProperty, "Use PGProperty instead.")
PyStringProperty = wx.deprecated(StringProperty, "Use StringProperty instead.")
PySystemColourProperty = wx.deprecated(SystemColourProperty, "Use SystemColourProperty instead.")
PyTextCtrlEditor = wx.deprecated(PGTextCtrlEditor, "Use PGTextCtrlEditor instead.")
PyUIntProperty = wx.deprecated(UIntProperty, "Use UIntProperty instead.")

@wx.deprecatedMsg("Use PropertyGrid.DoRegisterEditor instead")
def RegisterEditor(editor, editorName):
    return PropertyGrid.DoRegisterEditorClass(editor, editorName)

_PropertyGridManager__init__orig = PropertyGridManager.__init__
def _PropertyGridManager__init__(self, *args, **kw):
    _PropertyGridManager__init__orig(self, *args, **kw)
    self.DoDefaultTypeMappings()
    self.edited_objects = {}
    self.DoDefaultValueTypeMappings()
    if not hasattr(self.__class__, '_vt2setter'):
        self.__class__._vt2setter = {}
PropertyGridManager.__init__ = _PropertyGridManager__init__

