using System;
using System.Web.Http;
using System.Threading.Tasks;
using Aratek.TrustFinger;
using System.Drawing;
using System.IO;
using System.Drawing.Imaging;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Linq;
using System.Windows.Forms;
using MySql.Data.MySqlClient;

namespace MultiFingerDemo.Api
{
    public class FingerprintController : ApiController
    {
        private TrustFingerDeviceManager deviceManager = TrustFingerDeviceManager.Instance;

        public FingerprintController()
        {
            // Don't initialize device automatically to avoid conflicts
            // Device will be initialized on-demand when needed
        }

        private void InitializeDeviceIfNeeded()
        {
            deviceManager.OpenDevice(0);
        }

        [HttpGet]
        [Route("api/fingerprint/status")]
        public IHttpActionResult GetStatus()
        {
            return Ok(new {
                Status = "API Running",
                Timestamp = DateTime.Now,
                DeviceConnected = deviceManager.IsOpen
            });
        }

        [HttpPost]
        [Route("api/fingerprint/device/open")]
        public IHttpActionResult OpenDevice()
        {
            try
            {
                bool opened = deviceManager.OpenDevice(0);
                if (opened)
                {
                    return Ok(new {
                        Success = true,
                        Message = "Device opened successfully",
                        DeviceConnected = true
                    });
                }
                else
                {
                    return BadRequest("Failed to open device");
                }
            }
            catch (Exception ex)
            {
                return BadRequest("Device open error: " + ex.Message);
            }
        }

        [HttpPost]
        [Route("api/fingerprint/device/close")]
        public IHttpActionResult CloseDevice()
        {
            try
            {
                deviceManager.CloseDevice();
                return Ok(new {
                    Success = true,
                    Message = "Device closed successfully",
                    DeviceConnected = false
                });
            }
            catch (Exception ex)
            {
                return BadRequest("Device close error: " + ex.Message);
            }
        }

        [HttpGet]
        [Route("api/fingerprint/device/info")]
        public IHttpActionResult GetDeviceInfo()
        {
            try
            {
                InitializeDeviceIfNeeded();
                var device = deviceManager.GetDevice();
                if (device == null)
                    return BadRequest("Device not open");
                var desc = TrustFingerManager.GetDeviceDescription(0);
                return Ok(new {
                    SerialNumber = desc.SerialNumber,
                    Manufacturer = desc.Manufacturer,
                    ProductName = desc.ProductName,
                    ProductModel = desc.ProductModel,
                    FirmwareVersion = desc.FirmwareVersion,
                    HardwareVersion = desc.HardwareVersion,
                    ImageWidth = desc.ImagePixelWidth,
                    ImageHeight = desc.ImagePixelHeight,
                    Dpi = desc.Resolution,
                    DeviceId = desc.DeviceId
                });
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        [Route("api/fingerprint/capture")]
        public IHttpActionResult Capture([FromBody] CaptureRequest request)
        {
            try
            {
                InitializeDeviceIfNeeded();
                var device = deviceManager.GetDevice();
                if (device == null)
                    return BadRequest("Device not open");

                // Validate request
                if (request == null)
                    return BadRequest("Request body is required");

                // Get finger position and operation type from request
                int fingerPosition = request.FingerPosition;
                string operationType = request.OperationType ?? "flat";

                // For now, return a mock response to test the web interface
                // In a real implementation, this would use the MultiFingerStartAcquisition callback
                // or integrate with the existing CaptureForm logic

                // Generate mock template and image data for testing
                var mockTemplateData = new byte[512]; // Typical template size
                var mockImageData = GenerateMockFingerprintImage();
                var random = new Random();
                random.NextBytes(mockTemplateData);

                return Ok(new {
                    success = true,
                    message = $"Capture completed successfully for finger {fingerPosition} ({operationType})",
                    data = new {
                        TemplateData = Convert.ToBase64String(mockTemplateData),
                        ImageData = Convert.ToBase64String(mockImageData),
                        Quality = random.Next(60, 95), // Mock quality score
                        FingerPosition = fingerPosition,
                        OperationType = operationType,
                        CaptureTime = DateTime.Now
                    }
                });
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        [Route("api/fingerprint/launch-capture")]
        public IHttpActionResult LaunchCapture([FromBody] LaunchCaptureRequest request)
        {
            try
            {
                // Get the main form instance
                var mainForm = Application.OpenForms.OfType<MainForm>().FirstOrDefault();
                if (mainForm == null)
                {
                    return BadRequest("Main form not found. Please open the bridge application first.");
                }

                // Set the finger selections on the main form based on request
                mainForm.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        // Set finger selections based on request
                        if (request.FingerSelections != null)
                        {
                            // Reset selections
                            mainForm.nSelectFinger1 = 0;
                            mainForm.nSelectFinger2 = 0;

                            foreach (var selection in request.FingerSelections)
                            {
                                switch (selection.ToUpper())
                                {
                                    case "LEFT4":
                                        mainForm.nSelectFinger1 |= (1 << 1) | (1 << 2) | (1 << 3) | (1 << 4); // L2,L3,L4,L5
                                        break;
                                    case "RIGHT4":
                                        mainForm.nSelectFinger1 |= (1 << 6) | (1 << 7) | (1 << 8) | (1 << 9); // R2,R3,R4,R5
                                        break;
                                    case "THUMBS":
                                        mainForm.nSelectFinger1 |= (1 << 0) | (1 << 5); // L1,R1
                                        break;
                                }
                            }
                        }

                        // Trigger the capture button click programmatically
                        var startButton = mainForm.Controls.Find("start_button", true).FirstOrDefault() as Button;
                        if (startButton != null)
                        {
                            startButton.PerformClick();
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error launching capture: {ex.Message}");
                    }
                }));

                return Ok(new {
                    success = true,
                    message = "Capture form launched successfully. Please complete capture in the bridge application."
                });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet]
        [Route("api/fingerprint/get-captured-data")]
        public IHttpActionResult GetCapturedData()
        {
            try
            {
                // Get the main form instance to access captured data in memory
                var mainForm = Application.OpenForms.OfType<MainForm>().FirstOrDefault();
                if (mainForm == null)
                {
                    return BadRequest("Main form not found");
                }

                var capturedData = new List<object>();

                // Access the captured fingerprint data from MainForm memory
                // These are the private fields that store captured data before enrollment
                var mainFormType = mainForm.GetType();

                // Get captured images and features for each finger position
                var fingerPositions = new[]
                {
                    new { Position = 1, Name = "Left Thumb", ImageField = "flatleftthumb_pictureBox", FeatureField = "bFeatureLT", QualityField = "nIQLT" },
                    new { Position = 2, Name = "Left Index", ImageField = "flatleftindex_pictureBox", FeatureField = "bFeatureLI", QualityField = "nIQLI" },
                    new { Position = 3, Name = "Left Middle", ImageField = "flatleftmiddle_pictureBox", FeatureField = "bFeatureLM", QualityField = "nIQLM" },
                    new { Position = 4, Name = "Left Ring", ImageField = "flatleftring_pictureBox", FeatureField = "bFeatureLR", QualityField = "nIQLR" },
                    new { Position = 5, Name = "Left Little", ImageField = "flatleftlittle_pictureBox", FeatureField = "bFeatureLL", QualityField = "nIQLL" },
                    new { Position = 6, Name = "Right Thumb", ImageField = "flatrightthumb_pictureBox", FeatureField = "bFeatureRT", QualityField = "nIQRT" },
                    new { Position = 7, Name = "Right Index", ImageField = "flatrightindex_pictureBox", FeatureField = "bFeatureRI", QualityField = "nIQRI" },
                    new { Position = 8, Name = "Right Middle", ImageField = "flatrightmiddle_pictureBox", FeatureField = "bFeatureRM", QualityField = "nIQRM" },
                    new { Position = 9, Name = "Right Ring", ImageField = "flatrightring_pictureBox", FeatureField = "bFeatureRR", QualityField = "nIQRR" },
                    new { Position = 10, Name = "Right Little", ImageField = "flatrightlittle_pictureBox", FeatureField = "bFeatureRL", QualityField = "nIQRL" }
                };

                foreach (var finger in fingerPositions)
                {
                    try
                    {
                        // Get the PictureBox control
                        var pictureBox = mainForm.Controls.Find(finger.ImageField, true).FirstOrDefault() as PictureBox;
                        if (pictureBox?.Image != null)
                        {
                            // Convert image to base64
                            string imageData = null;
                            using (var ms = new MemoryStream())
                            {
                                pictureBox.Image.Save(ms, ImageFormat.Bmp);
                                imageData = Convert.ToBase64String(ms.ToArray());
                            }

                            // Get feature data using reflection
                            var featureField = mainFormType.GetField(finger.FeatureField,
                                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                            var qualityField = mainFormType.GetField(finger.QualityField,
                                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                            byte[] featureData = featureField?.GetValue(mainForm) as byte[];
                            int quality = qualityField != null ? (int)qualityField.GetValue(mainForm) : 0;

                            if (featureData != null)
                            {
                                capturedData.Add(new
                                {
                                    FingerPosition = finger.Position,
                                    FingerPositionName = finger.Name,
                                    ImageData = imageData,
                                    TemplateData = Convert.ToBase64String(featureData),
                                    Quality = quality,
                                    CaptureTime = DateTime.Now
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error getting data for {finger.Name}: {ex.Message}");
                    }
                }

                return Ok(new {
                    success = true,
                    data = capturedData,
                    message = $"Retrieved {capturedData.Count} captured fingerprints from bridge application"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        private string GetFingerPositionName(int position)
        {
            switch (position)
            {
                case 1: return "Left Thumb";
                case 2: return "Left Index";
                case 3: return "Left Middle";
                case 4: return "Left Ring";
                case 5: return "Left Little";
                case 6: return "Right Thumb";
                case 7: return "Right Index";
                case 8: return "Right Middle";
                case 9: return "Right Ring";
                case 10: return "Right Little";
                default: return $"Position {position}";
            }
        }

        private byte[] GenerateMockFingerprintImage()
        {
            // Create a simple mock BMP image for testing
            using (var bitmap = new Bitmap(200, 200, PixelFormat.Format24bppRgb))
            {
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    // Fill with light gray background
                    graphics.Clear(Color.LightGray);

                    // Create simple fingerprint-like pattern with lines
                    using (var pen = new Pen(Color.DarkGray, 2))
                    {
                        var random = new Random();
                        for (int i = 0; i < 20; i++)
                        {
                            int x1 = random.Next(0, 200);
                            int y1 = random.Next(0, 200);
                            int x2 = random.Next(0, 200);
                            int y2 = random.Next(0, 200);
                            graphics.DrawLine(pen, x1, y1, x2, y2);
                        }

                        // Add some curved lines to simulate fingerprint ridges
                        for (int i = 0; i < 10; i++)
                        {
                            var points = new Point[4];
                            for (int j = 0; j < 4; j++)
                            {
                                points[j] = new Point(random.Next(0, 200), random.Next(0, 200));
                            }
                            graphics.DrawCurve(pen, points);
                        }
                    }
                }

                using (var stream = new MemoryStream())
                {
                    bitmap.Save(stream, ImageFormat.Bmp);
                    return stream.ToArray();
                }
            }
        }

        [HttpPost]
        [Route("api/fingerprint/enroll")]
        public IHttpActionResult Enroll([FromBody] EnrollRequest request)
        {
            try
            {
                string connectionString = ConfigManager.GetDatabaseConnectionString();

                using (var conn = new MySqlConnection(connectionString))
                {
                    conn.Open();

                    // Get next available ID
                    string getMaxIdSql = "SELECT COALESCE(MAX(Id), 0) + 1 FROM enrollusers";
                    int nextId;
                    using (var getIdCmd = new MySqlCommand(getMaxIdSql, conn))
                    {
                        nextId = Convert.ToInt32(getIdCmd.ExecuteScalar());
                    }

                    string sql = @"INSERT INTO enrollusers (Id, UserId, CreatedTime, OperationType, FingerPosition, FingerPositionName, FingerData, FingerImage, ImageQuality)
                        VALUES (@id, @userId, @createdTime, @operationType, @fingerPosition, @fingerPositionName, @fingerData, @fingerImage, @imageQuality)";

                    using (var cmd = new MySqlCommand(sql, conn))
                    {
                        var createdTime = DateTime.Now;
                        cmd.Parameters.AddWithValue("@id", nextId);
                        cmd.Parameters.AddWithValue("@userId", request.UserId);
                        cmd.Parameters.AddWithValue("@createdTime", createdTime);
                        cmd.Parameters.AddWithValue("@operationType", 1);
                        cmd.Parameters.AddWithValue("@fingerPosition", request.FingerPosition);
                        cmd.Parameters.AddWithValue("@fingerPositionName", FormatHelper.FormatFingerprintPosition((FingerPosition)request.FingerPosition));
                        cmd.Parameters.AddWithValue("@fingerData", Convert.FromBase64String(request.TemplateData));
                        cmd.Parameters.AddWithValue("@fingerImage", !string.IsNullOrEmpty(request.ImageData) ? Convert.FromBase64String(request.ImageData) : (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@imageQuality", request.ImageQuality > 0 ? request.ImageQuality : (object)DBNull.Value);

                        cmd.ExecuteNonQuery();

                        return Ok(new {
                            success = true,
                            message = "Fingerprint enrollment successful",
                            data = new {
                                id = nextId,
                                userId = request.UserId,
                                fingerPosition = request.FingerPosition,
                                fingerPositionName = FormatHelper.FormatFingerprintPosition((FingerPosition)request.FingerPosition),
                                imageQuality = request.ImageQuality,
                                hasImage = !string.IsNullOrEmpty(request.ImageData),
                                enrollmentDate = createdTime
                            }
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        [Route("api/fingerprint/identify")]
        public IHttpActionResult Identify([FromBody] IdentifyRequest request)
        {
            try
            {
                var template = Convert.FromBase64String(request.TemplateData);
                var matches = new List<IdentifyResult>();
                var device = deviceManager.GetDevice();
                if (device == null)
                    return BadRequest("Device not open");

                string connectionString = ConfigManager.GetDatabaseConnectionString();

                using (var conn = new MySqlConnection(connectionString))
                {
                    conn.Open();

                    string sql = "SELECT UserId, FingerPosition, FingerPositionName, FingerData, FingerImage, ImageQuality, CreatedTime FROM enrollusers";
                    using (var cmd = new MySqlCommand(sql, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var fingerData = (byte[])reader["FingerData"];
                                var verifyResult = device.Verify(4, template, fingerData);
                                if (verifyResult.IsMatch)
                                {
                                    matches.Add(new IdentifyResult
                                    {
                                        UserId = reader["UserId"].ToString(),
                                        FingerPosition = Convert.ToInt32(reader["FingerPosition"]),
                                        FingerPositionName = reader["FingerPositionName"].ToString(),
                                        Score = verifyResult.Similarity,
                                        ImageData = reader["FingerImage"] != DBNull.Value ? Convert.ToBase64String((byte[])reader["FingerImage"]) : null,
                                        ImageQuality = reader["ImageQuality"] != DBNull.Value ? Convert.ToInt32(reader["ImageQuality"]) : 0,
                                        EnrollmentDate = Convert.ToDateTime(reader["CreatedTime"])
                                    });
                                }
                            }
                        }
                    }
                }
                return Ok(new { success = true, matches = matches });
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpGet]
        [Route("api/fingerprint/image/{userId}/{fingerPosition}")]
        public IHttpActionResult GetFingerprintImage(string userId, int fingerPosition)
        {
            try
            {
                using (var db = new TrustFingerDbContext())
                {
                    var enrolledUser = db.EnrollUser
                        .FirstOrDefault(u => u.UserId == userId && u.FingerPosition == fingerPosition);

                    if (enrolledUser == null)
                    {
                        return NotFound();
                    }

                    if (enrolledUser.FingerImage == null)
                    {
                        return Ok(new { success = false, message = "No image data available" });
                    }

                    return Ok(new {
                        success = true,
                        data = new {
                            userId = enrolledUser.UserId,
                            fingerPosition = enrolledUser.FingerPosition,
                            fingerPositionName = enrolledUser.FingerPositionName,
                            imageData = Convert.ToBase64String(enrolledUser.FingerImage),
                            imageQuality = enrolledUser.ImageQuality,
                            enrollmentDate = enrolledUser.CreatedTime
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }



        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Note: We don't dispose the device here as it's static
                // Device cleanup should be handled on application shutdown
            }
            base.Dispose(disposing);
        }
    }

    public class CaptureRequest
    {
        public int FingerPosition { get; set; }      // Specific finger position to capture (1-10)
        public string OperationType { get; set; } = "flat"; // "flat", "rolled", "slaps"
        public int Timeout { get; set; } = 30;      // Capture timeout in seconds
        public bool SaveImage { get; set; } = true; // Whether to include image data in response
    }

    public class EnrollRequest
    {
        public string UserId { get; set; }           // Prison management system ID
        public int FingerPosition { get; set; }
        public string TemplateData { get; set; }
        public string ImageData { get; set; }        // Base64 BMP image
        public int ImageQuality { get; set; }        // Quality score
        // UserName removed - will be retrieved from prison management system
    }

    public class IdentifyRequest
    {
        public string TemplateData { get; set; }
    }

    public class LaunchCaptureRequest
    {
        public List<string> FingerSelections { get; set; } // e.g., ["LEFT4", "RIGHT4", "THUMBS"]
        public int Timeout { get; set; } = 10;
        public string Level { get; set; } = "Medium";
        public bool LFD { get; set; } = true;
        public string ImageFormat { get; set; } = "BMP";
    }

    public class IdentifyResult
    {
        public string UserId { get; set; }           // Links to prison management system
        public int FingerPosition { get; set; }
        public string FingerPositionName { get; set; }
        public int Score { get; set; }
        public string ImageData { get; set; }        // Base64 BMP image
        public int ImageQuality { get; set; }        // Quality score
        public DateTime EnrollmentDate { get; set; }
        // UserName removed - will be retrieved from prison management system
    }
}