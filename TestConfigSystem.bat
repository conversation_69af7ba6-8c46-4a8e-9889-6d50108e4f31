@echo off
echo ========================================
echo   Testing Configuration System
echo ========================================
echo.

echo Current config.sys contents:
echo ----------------------------------------
type config.sys
echo ----------------------------------------
echo.

echo Testing with current configuration...
start "" "bin\Debug\MultipleFinger.exe"

echo.
echo Waiting 5 seconds for startup...
timeout /t 5 /nobreak >nul

echo.
echo Testing API endpoint...
curl -X POST http://localhost:9000/api/fingerprint/capture -H "Content-Type: application/json" -d "{}"

echo.
echo.
echo ========================================
echo Configuration Features:
echo 1. Database credentials from config.sys
echo 2. Dynamic API port configuration
echo 3. Dynamic web demo port configuration
echo 4. Beautiful fingerprint icon
echo 5. Enhanced context menu with:
echo    - Open Console
echo    - Sample Website
echo    - API Documentation
echo    - About
echo    - Exit
echo ========================================
echo.
echo Right-click the tray icon to test all features!
pause
