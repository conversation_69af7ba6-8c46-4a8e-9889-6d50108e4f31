using System;
using MultipleFinger.Native;

namespace MultiFingerDemo
{
    public enum LedStatus
    {
        Off = 0,
        On = 1
    }

    public class TrustFingerDevice
    {
        private IntPtr deviceHandle;

        public TrustFingerDevice(IntPtr handle)
        {
            this.deviceHandle = handle;
        }

        public int MultiFingerSetMissingFingers(byte byMissingFingerMask)
        {
            return TrustFingerNative.ARAFPSCAN_MultiFingerSetMissingFingers(byMissingFingerMask);
        }

        public int EnableLFD(bool pEnableLFD, int LFD_Level)
        {
            return TrustFingerNative.ARAFPSCAN_EnableLFD(this.deviceHandle, pEnableLFD, LFD_Level);
        }

        public int MultiFingerStopAcquisition()
        {
            return TrustFingerNative.ARAFPSCAN_MultiFingerStopAcquisition(this.deviceHandle);
        }

        public int MultiFingerStartAcquisition(MultiFingerParam MFparam, ARAFPSCAN_MultiFingerAcquisitionEventsManagerCallback captureEventsCallbackPtr)
        {
            return TrustFingerNative.ARAFPSCAN_MultiFingerStartAcquisition(this.deviceHandle, MFparam, captureEventsCallbackPtr);
        }

        public int SetLedStatus(int nLedIndex, LedStatus pStatus)
        {
            return TrustFingerNative.ARAFPSCAN_SetLedStatus(deviceHandle, nLedIndex, (int)pStatus);
        }

    }
}