// quuid.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QUuid
{
%TypeHeaderCode
#include <quuid.h>
%End

public:
    enum Variant
    {
        VarUnknown,
        NCS,
        DCE,
        Microsoft,
        Reserved,
    };

    enum Version
    {
        VerUnknown,
        Time,
        EmbeddedPOSIX,
        Md5,
        Name,
        Random,
        Sha1,
    };

%If (Qt_5_11_0 -)

    enum StringFormat
    {
        WithBraces,
        WithoutBraces,
        Id128,
    };

%End
    QUuid();
    QUuid(uint l, ushort w1, ushort w2, uchar b1 /PyInt/, uchar b2 /PyInt/, uchar b3 /PyInt/, uchar b4 /PyInt/, uchar b5 /PyInt/, uchar b6 /PyInt/, uchar b7 /PyInt/, uchar b8 /PyInt/);
    QUuid(const QString &);
    QUuid(const QByteArray &);
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *uni = qpycore_PyObject_FromQString(sipCpp->toString());
        
        if (uni)
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromFormat("PyQt5.QtCore.QUuid(%R)", uni);
        #else
            sipRes = PyString_FromFormat("PyQt5.QtCore.QUuid(");
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(uni));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
        
            Py_DECREF(uni);
        }
%End

    QString toString() const;
%If (Qt_5_11_0 -)
    QString toString(QUuid::StringFormat mode) const;
%End
    bool isNull() const;
    bool operator==(const QUuid &orig) const;
    bool operator!=(const QUuid &orig) const;
    bool operator<(const QUuid &other) const;
    bool operator>(const QUuid &other) const;
    static QUuid createUuid();
    static QUuid createUuidV3(const QUuid &ns, const QByteArray &baseData);
    static QUuid createUuidV5(const QUuid &ns, const QByteArray &baseData);
    static QUuid createUuidV3(const QUuid &ns, const QString &baseData);
    static QUuid createUuidV5(const QUuid &ns, const QString &baseData);
    QUuid::Variant variant() const;
    QUuid::Version version() const;
    QByteArray toByteArray() const;
%If (Qt_5_11_0 -)
    QByteArray toByteArray(QUuid::StringFormat mode) const;
%End
    QByteArray toRfc4122() const;
    static QUuid fromRfc4122(const QByteArray &);
};

QDataStream &operator<<(QDataStream &, const QUuid & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QUuid & /Constrained/) /ReleaseGIL/;
%If (Qt_5_5_0 -)
bool operator<=(const QUuid &lhs, const QUuid &rhs);
%End
%If (Qt_5_5_0 -)
bool operator>=(const QUuid &lhs, const QUuid &rhs);
%End
