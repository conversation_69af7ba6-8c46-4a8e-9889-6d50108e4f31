<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MySql.Data.EntityFramework</name>
    </assembly>
    <members>
        <member name="T:MySql.Data.EntityFramework.SqlFragmentVisitor">
            <summary>
            Visitor for SqlFragment and derived classes, useful for postprocessing and optimizations.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.ReplaceTableNameVisitor">
            <summary>
            Visitor to replace old table names with new table names. Used as part postprocessing of the code for fusing nested selects.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.OpType">
            <summary>
            Specifies the operation types supported.
            </summary>
        </member>
        <member name="M:MySql.Data.EntityFramework.SelectGenerator.TryFlatteningGroupBy(MySql.Data.EntityFramework.SqlFragment)">
            <summary>
            If input sqlFragment is a group by structure, is flattened to remove some nested correlation queries.
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.SqlGenerator.TryFusingSelect(MySql.Data.EntityFramework.InputFragment)">
            <summary>
            If current fragment is select and its from clause is another select, try fuse the inner select with the outer select.
            (Thus removing a nested query, which may have bad performance in Mysql).
            </summary>
            <param name="f">The fragment to probe and posibly optimize</param>
            <returns>The fragment fused, or the original one.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.SqlGenerator.TryPromoteToLike(System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.Data.Entity.Core.Common.CommandTrees.DbExpression,System.String)">
            <summary>
            Examines a binary expression to see if it is an special case suitable to conversion 
            to a more efficient and equivalent LIKE sql expression.
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <param name="op"></param>
            <returns></returns>
        </member>
        <member name="T:MySql.Data.EntityFramework.MySqlConnectionFactory">
            <summary>
            Used for creating connections in Code First 4.3.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.MySqlDependencyResolver">
            <summary>
            Provides the capability to resolve a dependency.
            </summary>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlDependencyResolver.GetService(System.Type,System.Object)">
            <summary>
            Attempts to resolve a dependency for a given contract type and optionally a given key.
            </summary>
            <param name="type">The base class that defines the dependency to be resolved.</param>
            <param name="key">Optionally, the key of the dependency to be resolved.</param>
            <returns>The resolved dependency.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlDependencyResolver.GetServices(System.Type,System.Object)">
            <summary>
            Attempts to resolve a dependency for all of the registered services with the given type and key combination.
            </summary>
            <param name="type">The base class that defines the dependency to be resolved.</param>
            <param name="key">Optionally, the key of the dependency to be resolved.</param>
            <returns>All services that resolve the dependency.</returns>
        </member>
        <member name="T:MySql.Data.EntityFramework.MySqlProviderInvariantName">
            <summary>
            Used to resolve a provider invariant name from a provider factory.
            </summary>
        </member>
        <member name="P:MySql.Data.EntityFramework.MySqlProviderInvariantName.Name">
            <summary>
            Gets the name of the provider.
            </summary>
        </member>
        <member name="P:MySql.Data.EntityFramework.MySqlProviderInvariantName.ProviderName">
            <summary>
            Gets the name of the provider.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.MySqlProviderFactoryResolver">
            <summary>
            Service that obtains the provider factory from a given connection.
            </summary>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlProviderFactoryResolver.ResolveProviderFactory(System.Data.Common.DbConnection)">
            <summary>
            Returns the <see cref="T:System.Data.Common.DbProviderFactory"/> for the given connection.
            </summary>
            <param name="connection">The database connection.</param>
            <returns>The provider factory for the connection.</returns>
        </member>
        <member name="T:MySql.Data.EntityFramework.MySqlManifestTokenResolver">
            <summary>
            Gets a provider manifest token for the given connection.
            </summary>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlManifestTokenResolver.ResolveManifestToken(System.Data.Common.DbConnection)">
            <summary>
            Returns the manifest token to use for the given connection.
            </summary>
            <param name="connection">The connection for which a manifest token is required.</param>
            <returns>The manifest token to use.</returns>
        </member>
        <member name="T:MySql.Data.EntityFramework.MySqlModelCacheKey">
            <summary>
            Represents a key value that uniquely identifies an Entity Framework model that has been loaded into memory. 
            </summary>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlModelCacheKey.Equals(System.Object)">
            <summary>
            Determines whether the current cached model key is equal to the specified cached
            model key.
            </summary>
            <param name="other">The cached model key to compare to the current cached model key.</param>
            <returns><c>true</c> if the current cached model key is equal to the specified cached model key;
            otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlModelCacheKey.GetHashCode">
            <summary>
            Returns the hash function for this cached model key.
            </summary>
            <returns>The hash function for this cached model key.</returns>
        </member>
        <member name="T:MySql.Data.EntityFramework.MySqlEFConfiguration">
            <summary>
            Defines the configuration of an application to be used with Entity Framework. 
            </summary>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlEFConfiguration.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:MySql.Data.EntityFramework.MySqlEFConfiguration"/> class.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.MySqlExecutionStrategy">
            <summary>
            An execution strategy tailored for handling MySql Server transient errors.
            </summary>
        </member>
        <member name="F:MySql.Data.EntityFramework.MySqlExecutionStrategy.errorsToRetryOn">
            <summary>
            Different back off algorithms used for different errors.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.BackoffAlgorithm">
            <summary>
            The base class for backoff algorithms.
            </summary>
            <remarks>Different transient error conditions require different approaches.</remarks>
        </member>
        <member name="M:MySql.Data.EntityFramework.BackoffAlgorithm.GetNextDelay">
            <summary>
            The default implementation is an exponential delay backoff.
            </summary>
            <returns></returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.BackoffAlgorithm.Reset">
            <summary>
            Resets a backoff algorithm, so they can be reused.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.BackoffAlgorithmErr1040">
            <summary>
            Back-off algorithm customized for the MySql error code 1040 - Too many connections.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.BackoffAlgorithmErr1614">
            <summary>
            Back-off algorithm for the Mysql error code 1614 - Transaction branch was rolled back: deadlock was detected.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.BackoffAlgorithmErr1205">
            <summary>
            Back-off algorithm customized for the MySql error code 1205 - Lock wait timeout exceeded; try restarting transaction.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.BackoffAlgorithmErr1213">
            <summary>
            Back-off algorithm customized for MySql error code 1213 - Deadlock found when trying to get lock; try restarting transaction.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.BackoffAlgorithmErr2006">
            <summary>
            Back-off algorithm customized for MySql error code 2006 - MySQL server has gone away.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.BackoffAlgorithmErr2013">
            <summary>
            Back-off algorithm customized for MySql error code 2013 - Lost connection to MySQL server during query.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.BackoffAlgorithmNdb">
            <summary>
            Back-off algorithm customized for MySql Cluster (NDB) errors.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.MySqlHistoryContext">
            <summary>
            This class is used by Code First Migrations to read and write migration history
            from the database.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.MySqlLogger">
            <summary>
            Provides the capability to write a log.
            </summary>  
        </member>
        <member name="T:MySql.Data.EntityFramework.MySqlMigrationCodeGenerator">
            <summary>
            Class used to customized code generation
            to avoid dbo. prefix added on table names.
            </summary>
        </member>
        <member name="T:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator">
            <summary>
            Implementation of a MySql's Sql generator for EF 4.3 data migrations.
            </summary>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AddColumnOperation)">
            <summary>
            Generates a migration operation to add a column.
            </summary>
            <param name="op">The operation that represents a column being added to a table.</param>
            <returns>A migration operation to add a column.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropColumnOperation)">
            <summary>
            Generates a migration operation to drop a column.
            </summary>
            <param name="op">The operation that represents a column being dropped from a table.</param>
            <returns>The migration operation to drop a column.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AlterColumnOperation)">
            <summary>
            Generates a migration operation to alter a column.
            </summary>
            <param name="op">The operation that represents altering an existing column.</param>
            <returns>A migration operation to alter a column.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.RenameColumnOperation)">
            <summary>
            Generates a migration operation to rename a column.
            </summary>
            <param name="op">The operation that represents a column being renamed.</param>
            <returns>A migration operation to rename a column.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AddForeignKeyOperation)">
            <summary>
            Generates a migration operation to add a foreign key.
            </summary>
            <param name="op">the operation that represents a foreing key constraint being added to a table.</param>
            <returns>A migration operation to add a foreign key constraint.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.ColumnModel)">
            <summary>
            Generates an SQL statement of a column model.
            </summary>
            <param name="op">The model that represents a column.</param>
            <returns>A string containing an SQL statement of a column model.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropForeignKeyOperation)">
            <summary>
            Generates a migration operation to drop a foreign key constraint from a table.
            </summary>
            <param name="op">The operation that represents a foreign key being added from a table.</param>
            <returns>A migration operation to drop a foreign key.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.CreateIndexOperation)">
            <summary>
            Generates a migration operation to create a database index.
            </summary>
            <param name="op">The operation that represents creating a database index.</param>
            <returns>A migration operation to create a database index.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropIndexOperation)">
            <summary>
            Generates a migration operation to drop an existing database index.
            </summary>
            <param name="op">The operation that represents dropping am existing database index.</param>
            <returns>A migration operation to drop an existing database index.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.CreateTableOperation)">
            <summary>
            Generates a migration operation to create a table.
            </summary>
            <param name="op">The operation that represents creating a table.</param>
            <returns>A migration operation to create a table.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropTableOperation)">
            <summary>
            Generates a migration operation to drop an existing table.
            </summary>
            <param name="op">The operation that represents dropping an existing table.</param>
            <returns>A migration operation to drop an existing table.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.AddPrimaryKeyOperation)">
            <summary>
            Generates a migration operation to add a primary key to a table.
            </summary>
            <param name="op">The operation that represents adding a primary key to a table.</param>
            <returns>A migration operation to add a primary key to a table.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.DropPrimaryKeyOperation)">
            <summary>
            Generates a migration operation to drpo an existing primary key.
            </summary>
            <param name="op">The operation that represents dropping an existing primary key.</param>
            <returns>A migration operation to drop an existing primary key.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.RenameTableOperation)">
            <summary>
            Generates a migration operation to rename an existing table.
            </summary>
            <param name="op">The operation that represents renaming an existing table.</param>
            <returns>A migration operation to rename an existing table.</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.MoveTableOperation)">
            <summary>
            Not implemented yet.
            </summary>
            <param name="op">NA</param>
            <returns>NA</returns>
        </member>
        <member name="M:MySql.Data.EntityFramework.MySqlMigrationSqlGenerator.Generate(System.Data.Entity.Migrations.Model.SqlOperation)">
            <summary>
            Generates a migration operation with a MySQL statement to be executed.
            </summary>
            <param name="op">The operation representing a MySQL statement to be executed directly against the database.</param>
            <returns>A migration operation with a MySQL statement to be executed.</returns>
        </member>
        <member name="T:MySql.Data.EntityFramework.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:MySql.Data.EntityFramework.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:MySql.Data.EntityFramework.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:MySql.Data.EntityFramework.Properties.Resources.ConnectionMustBeOfTypeMySqlConnection">
            <summary>
              Looks up a localized string similar to The connection parameter must reference an object of type MySql.Data.MySqlConnection.
            </summary>
        </member>
        <member name="P:MySql.Data.EntityFramework.Properties.Resources.NoStoreTypeForEdmType">
            <summary>
              Looks up a localized string similar to There is no store type corresponding to the EDM type &apos;{0}&apos; of primitive type &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:MySql.Data.EntityFramework.Properties.Resources.TypeNotSupported">
            <summary>
              Looks up a localized string similar to The underlying provider does not support the type &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:MySql.Data.EntityFramework.Properties.Resources.WrongFunctionResultType">
            <summary>
              Looks up a localized string similar to Result type of a function is expected to be a collection of RowType or PrimitiveType.
            </summary>
        </member>
        <member name="T:MySql.Data.MySqlClient.MySqlScriptServices">
            <summary>
            Constructs a script that generates a table.
            </summary>
        </member>
        <member name="T:MySql.Data.MySqlClient.MySqlProviderServices">
            <summary>
            The <see cref="T:System.Data.Entity.Core.Common.DbProviderServices"/> implementation for the MySqlClient provider for MySQL Server.
            </summary>
        </member>
        <member name="M:MySql.Data.MySqlClient.MySqlProviderServices.SetExpectedTypes(System.Data.Entity.Core.Common.CommandTrees.DbCommandTree,MySql.Data.EntityFramework.EFMySqlCommand)">
            <summary>
            Sets the expected column types
            </summary>
        </member>
        <member name="M:MySql.Data.MySqlClient.MySqlProviderServices.SetQueryExpectedTypes(System.Data.Entity.Core.Common.CommandTrees.DbQueryCommandTree,MySql.Data.EntityFramework.EFMySqlCommand)">
            <summary>
            Sets the expected column types for a given query command tree
            </summary>
        </member>
        <member name="M:MySql.Data.MySqlClient.MySqlProviderServices.SetFunctionExpectedTypes(System.Data.Entity.Core.Common.CommandTrees.DbFunctionCommandTree,MySql.Data.EntityFramework.EFMySqlCommand)">
            <summary>
            Sets the expected column types for a given function command tree
            </summary>
        </member>
    </members>
</doc>
