using Aratek.TrustFinger;
using System;

namespace MultiFingerDemo.Api
{
    public class TrustFingerDeviceManager
    {
        private static TrustFingerDeviceManager _instance = null;
        private static readonly object _lock = new object();
        private TrustFingerDevice _device = null;
        private bool _isOpen = false;
        private static bool _sdkInitialized = false;

        private TrustFingerDeviceManager() { }

        public static TrustFingerDeviceManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new TrustFingerDeviceManager();
                    }
                }
                return _instance;
            }
        }

        public bool OpenDevice(int index = 0)
        {
            lock (_lock)
            {
                Console.WriteLine($"[DeviceManager] OpenDevice called with index {index}");
                Console.WriteLine($"[DeviceManager] Current state - IsOpen: {_isOpen}, Device: {(_device != null ? "exists" : "null")}");

                if (!_sdkInitialized)
                {
                    Console.WriteLine("[DeviceManager] Initializing SDK for OpenDevice...");
                    TrustFingerManager.GlobalInitialize();
                    _sdkInitialized = true;
                }
                if (_device == null)
                {
                    Console.WriteLine("[DeviceManager] Creating new TrustFingerDevice...");
                    _device = new TrustFingerDevice();
                }
                if (!_isOpen)
                {
                    try
                    {
                        Console.WriteLine($"[DeviceManager] Attempting to open device {index}...");
                        _device.Open(index);
                        _isOpen = true;
                        Console.WriteLine("[DeviceManager] Device opened successfully");
                        return true;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[DeviceManager] Failed to open device: {ex.Message}");
                        _device = null;
                        _isOpen = false;
                        return false;
                    }
                }
                Console.WriteLine("[DeviceManager] Device already open");
                return true;
            }
        }

        public void CloseDevice()
        {
            lock (_lock)
            {
                if (_device != null && _isOpen)
                {
                    _device.Close();
                    _isOpen = false;
                    _device = null;
                }
            }
        }

        public TrustFingerDevice GetDevice()
        {
            lock (_lock)
            {
                return _isOpen ? _device : null;
            }
        }

        public bool IsOpen
        {
            get { lock (_lock) { return _isOpen; } }
        }

        /// <summary>
        /// Check if any TrustFinger devices are connected
        /// </summary>
        /// <returns>Number of connected devices</returns>
        public int GetConnectedDeviceCount()
        {
            lock (_lock)
            {
                try
                {
                    Console.WriteLine("[DeviceManager] GetConnectedDeviceCount called");
                    if (!_sdkInitialized)
                    {
                        Console.WriteLine("[DeviceManager] Initializing SDK...");
                        TrustFingerManager.GlobalInitialize();
                        _sdkInitialized = true;
                        Console.WriteLine("[DeviceManager] SDK initialized");
                    }

                    // Try to get device count using TrustFingerManager
                    int deviceCount = TrustFingerManager.GetDeviceCount();
                    Console.WriteLine($"[DeviceManager] Found {deviceCount} devices");
                    return deviceCount;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DeviceManager] Error getting device count: {ex.Message}");
                    return 0;
                }
            }
        }

        /// <summary>
        /// Check if a device is available and can be opened
        /// </summary>
        /// <param name="index">Device index to check</param>
        /// <returns>True if device is available</returns>
        public bool IsDeviceAvailable(int index = 0)
        {
            lock (_lock)
            {
                try
                {
                    if (!_sdkInitialized)
                    {
                        TrustFingerManager.GlobalInitialize();
                        _sdkInitialized = true;
                    }

                    // Try to get device description - if this succeeds, device is available
                    var desc = TrustFingerManager.GetDeviceDescription(index);
                    return desc != null && (desc.DeviceId == 800 || desc.DeviceId == 900 || desc.DeviceId == 303);
                }
                catch
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// Attempt to automatically open the first available device
        /// </summary>
        /// <returns>True if a device was successfully opened</returns>
        public bool AutoOpenDevice()
        {
            lock (_lock)
            {
                try
                {
                    if (_isOpen)
                        return true; // Already open

                    int deviceCount = GetConnectedDeviceCount();
                    if (deviceCount == 0)
                        return false; // No devices connected

                    // Try to open the first available device
                    for (int i = 0; i < deviceCount; i++)
                    {
                        if (IsDeviceAvailable(i))
                        {
                            bool opened = OpenDevice(i);
                            if (opened)
                                return true;
                        }
                    }

                    return false;
                }
                catch
                {
                    return false;
                }
            }
        }
    }
}