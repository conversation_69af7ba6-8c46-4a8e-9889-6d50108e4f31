using System;
using System.Collections.Generic;
using System.IO;

namespace MultiFingerDemo
{
    public static class ConfigManager
    {
        private static Dictionary<string, string> _config = new Dictionary<string, string>();
        private static bool _isLoaded = false;

        public static void LoadConfig()
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.sys");
                
                if (!File.Exists(configPath))
                {
                    // Create default config file if it doesn't exist
                    CreateDefaultConfig(configPath);
                }

                _config.Clear();
                string[] lines = File.ReadAllLines(configPath);

                foreach (string line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#") || line.StartsWith("//"))
                        continue;

                    int equalIndex = line.IndexOf('=');
                    if (equalIndex > 0)
                    {
                        string key = line.Substring(0, equalIndex).Trim().ToLower();
                        string value = line.Substring(equalIndex + 1).Trim();
                        _config[key] = value;
                    }
                }

                _isLoaded = true;
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show(
                    "Failed to load config.sys: " + ex.Message + "\nUsing default values.",
                    "Configuration Warning",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Warning);
                
                // Set default values
                SetDefaultValues();
                _isLoaded = true;
            }
        }

        private static void CreateDefaultConfig(string configPath)
        {
            string defaultConfig = @"# Database Configuration
host=localhost
user=root
password=sa
database=finger

# API Configuration
api_port=9000
web_demo_port=8080

# Application Settings
app_name=Fingerprint Bridge Service

# Fingerprint Capture Settings
capture_timeout=10
lfd_enabled=true
lfd_level=3
dry_level=5
quality_threshold=40
vendor_original_settings=true
override_finger_position=true
force_ring_finger_as_middle=true
";
            File.WriteAllText(configPath, defaultConfig);
        }

        private static void SetDefaultValues()
        {
            _config["host"] = "localhost";
            _config["user"] = "root";
            _config["password"] = "sa";
            _config["database"] = "finger";
            _config["api_port"] = "9000";
            _config["web_demo_port"] = "8080";
            _config["app_name"] = "Fingerprint Bridge Service";

            // Fingerprint capture settings - ORIGINAL VENDOR SETTINGS
            _config["capture_timeout"] = "10";
            _config["lfd_enabled"] = "true";
            _config["lfd_level"] = "3";
            _config["dry_level"] = "5";
            _config["quality_threshold"] = "40";
            _config["vendor_original_settings"] = "true";
        }

        public static string GetValue(string key, string defaultValue = "")
        {
            if (!_isLoaded)
                LoadConfig();

            key = key.ToLower();
            return _config.ContainsKey(key) ? _config[key] : defaultValue;
        }

        public static int GetIntValue(string key, int defaultValue = 0)
        {
            string value = GetValue(key);
            int result;
            return int.TryParse(value, out result) ? result : defaultValue;
        }

        public static bool GetBoolValue(string key, bool defaultValue = false)
        {
            string value = GetValue(key).ToLower();
            if (value == "true" || value == "1" || value == "yes")
                return true;
            if (value == "false" || value == "0" || value == "no")
                return false;
            return defaultValue;
        }

        public static string GetDatabaseConnectionString()
        {
            if (!_isLoaded)
                LoadConfig();

            string host = GetValue("host", "localhost");
            string user = GetValue("user", "root");
            string password = GetValue("password", "sa");
            string database = GetValue("database", "finger");

            // Use the same format as proven project - no port specification, uses default MySQL port
            return $"server={host};user={user};password={password};database={database}";
        }

        public static void SaveConfig()
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.sys");
                List<string> lines = new List<string>();

                lines.Add("# Database Configuration");
                lines.Add("host=" + GetValue("host", "localhost"));
                lines.Add("user=" + GetValue("user", "root"));
                lines.Add("password=" + GetValue("password", "sa"));
                lines.Add("database=" + GetValue("database", "finger"));
                lines.Add("");
                lines.Add("# API Configuration");
                lines.Add("api_port=" + GetValue("api_port", "9000"));
                lines.Add("web_demo_port=" + GetValue("web_demo_port", "8080"));
                lines.Add("");
                lines.Add("# Application Settings");
                lines.Add("app_name=" + GetValue("app_name", "Fingerprint Bridge Service"));
                lines.Add("");
                lines.Add("# Fingerprint Capture Settings");
                lines.Add("capture_timeout=" + GetValue("capture_timeout", "60"));
                lines.Add("lfd_enabled=" + GetValue("lfd_enabled", "false"));
                lines.Add("lfd_level=" + GetValue("lfd_level", "1"));
                lines.Add("dry_level=" + GetValue("dry_level", "6"));
                lines.Add("quality_threshold=" + GetValue("quality_threshold", "10"));
                lines.Add("vendor_original_settings=" + GetValue("vendor_original_settings", "true"));

                File.WriteAllLines(configPath, lines);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show(
                    "Failed to save config.sys: " + ex.Message,
                    "Configuration Error",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        public static void SetValue(string key, string value)
        {
            if (!_isLoaded)
                LoadConfig();

            _config[key.ToLower()] = value;
        }
    }
}
