# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
The `wx.ribbon` module contains a set of classes for writing a ribbon-based user interface.

At the most generic level, this is a combination of a tab control with a
toolbar. At a more functional level, it is similar to the user interface
present in recent versions of Microsoft Office and in Windows 10.
"""
#-- begin-_ribbon --#

import wx
#-- end-_ribbon --#
#-- begin-ribbon_control --#

class RibbonControl(wx.Control):
    """
    RibbonControl() -> None
    RibbonControl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, validator=wx.DefaultValidator, name=wx.ControlNameStr) -> None
    
    wxRibbonControl serves as a base class for all controls which share
    the ribbon characteristics of having a ribbon art provider, and
    (optionally) non-continuous resizing.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, validator: wx.Validator=wx.DefaultValidator, name: str=wx.ControlNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RibbonControl() -> None
        RibbonControl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, validator=wx.DefaultValidator, name=wx.ControlNameStr) -> None
        
        wxRibbonControl serves as a base class for all controls which share
        the ribbon characteristics of having a ribbon art provider, and
        (optionally) non-continuous resizing.
        """

    def SetArtProvider(self, art: RibbonArtProvider) -> None:
        """
        SetArtProvider(art) -> None
        
        Set the art provider to be used.
        """

    def GetArtProvider(self) -> RibbonArtProvider:
        """
        GetArtProvider() -> RibbonArtProvider
        
        Get the art provider to be used.
        """

    def IsSizingContinuous(self) -> bool:
        """
        IsSizingContinuous() -> bool
        """

    @overload
    def GetNextSmallerSize(self, direction: wx.Orientation, relative_to: wx.Size) -> wx.Size:
        ...

    @overload
    def GetNextSmallerSize(self, direction: wx.Orientation) -> wx.Size:
        """
        GetNextSmallerSize(direction) -> wx.Size
        GetNextSmallerSize(direction, relative_to) -> wx.Size
        
        If sizing is not continuous, then return a suitable size for the
        control which is smaller than the current size.
        """

    @overload
    def GetNextLargerSize(self, direction: wx.Orientation, relative_to: wx.Size) -> wx.Size:
        ...

    @overload
    def GetNextLargerSize(self, direction: wx.Orientation) -> wx.Size:
        """
        GetNextLargerSize(direction) -> wx.Size
        GetNextLargerSize(direction, relative_to) -> wx.Size
        
        If sizing is not continuous, then return a suitable size for the
        control which is larger than the current size.
        """

    def Realize(self) -> bool:
        """
        Realize() -> bool
        
        Perform initial size and layout calculations after children have been
        added, and/or realize children.
        """

    def Realise(self) -> bool:
        """
        Realise() -> bool
        
        Alias for Realize().
        """

    def GetAncestorRibbonBar(self) -> RibbonBar:
        """
        GetAncestorRibbonBar() -> RibbonBar
        
        Get the first ancestor which is a wxRibbonBar (or derived) or NULL if
        not having such parent.
        """

    def GetBestSizeForParentSize(self, parentSize: wx.Size) -> wx.Size:
        """
        GetBestSizeForParentSize(parentSize) -> wx.Size
        
        Finds the best width and height given the parent's width and height.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def AncestorRibbonBar(self) -> RibbonBar: ...
    @property
    def ArtProvider(self) -> RibbonArtProvider: ...
    @ArtProvider.setter
    def ArtProvider(self, value: RibbonArtProvider, /) -> None: ...

    def DoGetNextSmallerSize(self, direction: wx.Orientation, relative_to: wx.Size) -> wx.Size:
        """
        DoGetNextSmallerSize(direction, relative_to) -> wx.Size
        
        Implementation of GetNextSmallerSize().
        """

    def DoGetNextLargerSize(self, direction: wx.Orientation, relative_to: wx.Size) -> wx.Size:
        """
        DoGetNextLargerSize(direction, relative_to) -> wx.Size
        
        Implementation of GetNextLargerSize().
        """
# end of class RibbonControl

#-- end-ribbon_control --#
#-- begin-ribbon_page --#

class RibbonPage(RibbonControl):
    """
    RibbonPage() -> None
    RibbonPage(parent, id=wx.ID_ANY, label='', icon=wx.NullBitmap, style=0) -> None
    
    Container for related ribbon panels, and a tab within a ribbon bar.
    """

    @overload
    def __init__(self, parent: RibbonBar, id: int=wx.ID_ANY, label: str='', icon: wx.Bitmap=wx.NullBitmap, style: int=0) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RibbonPage() -> None
        RibbonPage(parent, id=wx.ID_ANY, label='', icon=wx.NullBitmap, style=0) -> None
        
        Container for related ribbon panels, and a tab within a ribbon bar.
        """

    def Create(self, parent: RibbonBar, id: int=wx.ID_ANY, label: str='', icon: wx.Bitmap=wx.NullBitmap, style: int=0) -> bool:
        """
        Create(parent, id=wx.ID_ANY, label='', icon=wx.NullBitmap, style=0) -> bool
        
        Create a ribbon page in two-step ribbon page construction.
        """

    def SetArtProvider(self, art: RibbonArtProvider) -> None:
        """
        SetArtProvider(art) -> None
        
        Set the art provider to be used.
        """

    def GetIcon(self) -> wx.Bitmap:
        """
        GetIcon() -> wx.Bitmap
        
        Get the icon used for the page in the ribbon bar tab area (only
        displayed if the ribbon bar is actually showing icons).
        """

    def SetSizeWithScrollButtonAdjustment(self, x: int, y: int, width: int, height: int) -> None:
        """
        SetSizeWithScrollButtonAdjustment(x, y, width, height) -> None
        
        Set the size of the page and the external scroll buttons (if any).
        """

    def AdjustRectToIncludeScrollButtons(self, rect: wx.Rect) -> None:
        """
        AdjustRectToIncludeScrollButtons(rect) -> None
        
        Expand a rectangle of the page to include external scroll buttons (if
        any).
        """

    def DismissExpandedPanel(self) -> bool:
        """
        DismissExpandedPanel() -> bool
        
        Dismiss the current externally expanded panel, if there is one.
        """

    def Realize(self) -> bool:
        """
        Realize() -> bool
        
        Perform a full re-layout of all panels on the page.
        """

    def ScrollLines(self, lines: int) -> bool:
        """
        ScrollLines(lines) -> bool
        
        Scroll the page by some amount up / down / left / right.
        """

    def ScrollPixels(self, pixels: int) -> bool:
        """
        ScrollPixels(pixels) -> bool
        
        Scroll the page by a set number of pixels up / down / left / right.
        """

    def ScrollSections(self, sections: int) -> bool:
        """
        ScrollSections(sections) -> bool
        
        Scroll the page by an entire child section.
        """

    def GetMajorAxis(self) -> wx.Orientation:
        """
        GetMajorAxis() -> wx.Orientation
        
        Get the direction in which ribbon panels are stacked within the page.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Icon(self) -> wx.Bitmap: ...
    @property
    def MajorAxis(self) -> wx.Orientation: ...
# end of class RibbonPage

#-- end-ribbon_page --#
#-- begin-ribbon_panel --#

class _RibbonPanelOption(IntEnum):
    RIBBON_PANEL_NO_AUTO_MINIMISE = auto()
    RIBBON_PANEL_EXT_BUTTON = auto()
    RIBBON_PANEL_MINIMISE_BUTTON = auto()
    RIBBON_PANEL_STRETCH = auto()
    RIBBON_PANEL_FLEXIBLE = auto()
    RIBBON_PANEL_DEFAULT_STYLE = auto()
RibbonPanelOption: TypeAlias = Union[_RibbonPanelOption, int]
RIBBON_PANEL_NO_AUTO_MINIMISE = _RibbonPanelOption.RIBBON_PANEL_NO_AUTO_MINIMISE
RIBBON_PANEL_EXT_BUTTON = _RibbonPanelOption.RIBBON_PANEL_EXT_BUTTON
RIBBON_PANEL_MINIMISE_BUTTON = _RibbonPanelOption.RIBBON_PANEL_MINIMISE_BUTTON
RIBBON_PANEL_STRETCH = _RibbonPanelOption.RIBBON_PANEL_STRETCH
RIBBON_PANEL_FLEXIBLE = _RibbonPanelOption.RIBBON_PANEL_FLEXIBLE
RIBBON_PANEL_DEFAULT_STYLE = _RibbonPanelOption.RIBBON_PANEL_DEFAULT_STYLE
wxEVT_RIBBONPANEL_EXTBUTTON_ACTIVATED: int

class RibbonPanel(RibbonControl):
    """
    RibbonPanel() -> None
    RibbonPanel(parent, id=wx.ID_ANY, label='', minimised_icon=wx.NullBitmap, pos=wx.DefaultPosition, size=wx.DefaultSize, style=RIBBON_PANEL_DEFAULT_STYLE) -> None
    
    Serves as a container for a group of (ribbon) controls.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, label: str='', minimised_icon: wx.Bitmap=wx.NullBitmap, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=RIBBON_PANEL_DEFAULT_STYLE) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RibbonPanel() -> None
        RibbonPanel(parent, id=wx.ID_ANY, label='', minimised_icon=wx.NullBitmap, pos=wx.DefaultPosition, size=wx.DefaultSize, style=RIBBON_PANEL_DEFAULT_STYLE) -> None
        
        Serves as a container for a group of (ribbon) controls.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, label: str='', icon: wx.Bitmap=wx.NullBitmap, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=RIBBON_PANEL_DEFAULT_STYLE) -> bool:
        """
        Create(parent, id=wx.ID_ANY, label='', icon=wx.NullBitmap, pos=wx.DefaultPosition, size=wx.DefaultSize, style=RIBBON_PANEL_DEFAULT_STYLE) -> bool
        
        Create a ribbon panel in two-step ribbon panel construction.
        """

    def GetMinimisedIcon(self) -> wx.Bitmap:
        """
        GetMinimisedIcon() -> wx.Bitmap
        
        Get the bitmap to be used in place of the panel children when it is
        minimised.
        """

    def HasExtButton(self) -> bool:
        """
        HasExtButton() -> bool
        
        Test if the panel has an extension button.
        """

    @overload
    def IsMinimised(self, at_size: wx.Size) -> bool:
        ...

    @overload
    def IsMinimised(self) -> bool:
        """
        IsMinimised() -> bool
        IsMinimised(at_size) -> bool
        
        Query if the panel is currently minimised.
        """

    def IsHovered(self) -> bool:
        """
        IsHovered() -> bool
        
        Query is the mouse is currently hovered over the panel.
        """

    def IsExtButtonHovered(self) -> bool:
        """
        IsExtButtonHovered() -> bool
        
        Query if the mouse is currently hovered over the extension button.
        """

    def CanAutoMinimise(self) -> bool:
        """
        CanAutoMinimise() -> bool
        
        Query if the panel can automatically minimise itself at small sizes.
        """

    def ShowExpanded(self) -> bool:
        """
        ShowExpanded() -> bool
        
        Show the panel externally expanded.
        """

    def HideExpanded(self) -> bool:
        """
        HideExpanded() -> bool
        
        Hide the panel's external expansion.
        """

    def SetArtProvider(self, art: RibbonArtProvider) -> None:
        """
        SetArtProvider(art) -> None
        
        Set the art provider to be used.
        """

    def Realize(self) -> bool:
        """
        Realize() -> bool
        
        Realize all children of the panel.
        """

    def GetExpandedDummy(self) -> RibbonPanel:
        """
        GetExpandedDummy() -> RibbonPanel
        
        Get the dummy panel of an expanded panel.
        """

    def GetExpandedPanel(self) -> RibbonPanel:
        """
        GetExpandedPanel() -> RibbonPanel
        
        Get the expanded panel of a dummy panel.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ExpandedDummy(self) -> RibbonPanel: ...
    @property
    def ExpandedPanel(self) -> RibbonPanel: ...
    @property
    def MinimisedIcon(self) -> wx.Bitmap: ...
# end of class RibbonPanel


class RibbonPanelEvent(wx.CommandEvent):
    """
    RibbonPanelEvent(command_type=wx.wxEVT_NULL, win_id=0, panel=None) -> None
    
    Event used to indicate various actions relating to a wxRibbonPanel.
    """

    def __init__(self, command_type: EventType=wx.wxEVT_NULL, win_id: int=0, panel: Optional[RibbonPanel]=None) -> None:
        """
        RibbonPanelEvent(command_type=wx.wxEVT_NULL, win_id=0, panel=None) -> None
        
        Event used to indicate various actions relating to a wxRibbonPanel.
        """

    def GetPanel(self) -> RibbonPanel:
        """
        GetPanel() -> RibbonPanel
        
        Returns the panel relating to this event.
        """

    def SetPanel(self, page: RibbonPanel) -> None:
        """
        SetPanel(page) -> None
        
        Sets the page relating to this event.
        """
    @property
    def Panel(self) -> RibbonPanel: ...
    @Panel.setter
    def Panel(self, value: RibbonPanel, /) -> None: ...
# end of class RibbonPanelEvent


EVT_RIBBONPANEL_EXTBUTTON_ACTIVATED = wx.PyEventBinder(wxEVT_RIBBONPANEL_EXTBUTTON_ACTIVATED, 1)
#-- end-ribbon_panel --#
#-- begin-ribbon_bar --#

class _RibbonBarOption(IntEnum):
    RIBBON_BAR_SHOW_PAGE_LABELS = auto()
    RIBBON_BAR_SHOW_PAGE_ICONS = auto()
    RIBBON_BAR_FLOW_HORIZONTAL = auto()
    RIBBON_BAR_FLOW_VERTICAL = auto()
    RIBBON_BAR_SHOW_PANEL_EXT_BUTTONS = auto()
    RIBBON_BAR_SHOW_PANEL_MINIMISE_BUTTONS = auto()
    RIBBON_BAR_ALWAYS_SHOW_TABS = auto()
    RIBBON_BAR_SHOW_TOGGLE_BUTTON = auto()
    RIBBON_BAR_SHOW_HELP_BUTTON = auto()
    RIBBON_BAR_DEFAULT_STYLE = auto()
    RIBBON_BAR_FOLDBAR_STYLE = auto()
RibbonBarOption: TypeAlias = Union[_RibbonBarOption, int]
RIBBON_BAR_SHOW_PAGE_LABELS = _RibbonBarOption.RIBBON_BAR_SHOW_PAGE_LABELS
RIBBON_BAR_SHOW_PAGE_ICONS = _RibbonBarOption.RIBBON_BAR_SHOW_PAGE_ICONS
RIBBON_BAR_FLOW_HORIZONTAL = _RibbonBarOption.RIBBON_BAR_FLOW_HORIZONTAL
RIBBON_BAR_FLOW_VERTICAL = _RibbonBarOption.RIBBON_BAR_FLOW_VERTICAL
RIBBON_BAR_SHOW_PANEL_EXT_BUTTONS = _RibbonBarOption.RIBBON_BAR_SHOW_PANEL_EXT_BUTTONS
RIBBON_BAR_SHOW_PANEL_MINIMISE_BUTTONS = _RibbonBarOption.RIBBON_BAR_SHOW_PANEL_MINIMISE_BUTTONS
RIBBON_BAR_ALWAYS_SHOW_TABS = _RibbonBarOption.RIBBON_BAR_ALWAYS_SHOW_TABS
RIBBON_BAR_SHOW_TOGGLE_BUTTON = _RibbonBarOption.RIBBON_BAR_SHOW_TOGGLE_BUTTON
RIBBON_BAR_SHOW_HELP_BUTTON = _RibbonBarOption.RIBBON_BAR_SHOW_HELP_BUTTON
RIBBON_BAR_DEFAULT_STYLE = _RibbonBarOption.RIBBON_BAR_DEFAULT_STYLE
RIBBON_BAR_FOLDBAR_STYLE = _RibbonBarOption.RIBBON_BAR_FOLDBAR_STYLE

class _RibbonDisplayMode(IntEnum):
    RIBBON_BAR_PINNED = auto()
    RIBBON_BAR_MINIMIZED = auto()
    RIBBON_BAR_EXPANDED = auto()
RibbonDisplayMode: TypeAlias = Union[_RibbonDisplayMode, int]
RIBBON_BAR_PINNED = _RibbonDisplayMode.RIBBON_BAR_PINNED
RIBBON_BAR_MINIMIZED = _RibbonDisplayMode.RIBBON_BAR_MINIMIZED
RIBBON_BAR_EXPANDED = _RibbonDisplayMode.RIBBON_BAR_EXPANDED
wxEVT_RIBBONBAR_PAGE_CHANGED: int
wxEVT_RIBBONBAR_PAGE_CHANGING: int
wxEVT_RIBBONBAR_TAB_MIDDLE_DOWN: int
wxEVT_RIBBONBAR_TAB_MIDDLE_UP: int
wxEVT_RIBBONBAR_TAB_RIGHT_DOWN: int
wxEVT_RIBBONBAR_TAB_RIGHT_UP: int
wxEVT_RIBBONBAR_TAB_LEFT_DCLICK: int
wxEVT_RIBBONBAR_TOGGLED: int
wxEVT_RIBBONBAR_HELP_CLICK: int

class RibbonBarEvent(wx.NotifyEvent):
    """
    RibbonBarEvent(command_type=wx.wxEVT_NULL, win_id=0, page=None) -> None
    
    Event used to indicate various actions relating to a wxRibbonBar.
    """

    def __init__(self, command_type: EventType=wx.wxEVT_NULL, win_id: int=0, page: Optional[RibbonPage]=None) -> None:
        """
        RibbonBarEvent(command_type=wx.wxEVT_NULL, win_id=0, page=None) -> None
        
        Event used to indicate various actions relating to a wxRibbonBar.
        """

    def GetPage(self) -> RibbonPage:
        """
        GetPage() -> RibbonPage
        
        Returns the page being changed to, or being clicked on.
        """

    def SetPage(self, page: RibbonPage) -> None:
        """
        SetPage(page) -> None
        
        Sets the page relating to this event.
        """
    @property
    def Page(self) -> RibbonPage: ...
    @Page.setter
    def Page(self, value: RibbonPage, /) -> None: ...
# end of class RibbonBarEvent


class RibbonPageTabInfo:
    """
    
    """
    rect: wx.Rect
    page: RibbonPage
    ideal_width: int
    small_begin_need_separator_width: int
    small_must_have_separator_width: int
    minimum_width: int
    active: bool
    hovered: bool
    highlight: bool
    shown: bool
# end of class RibbonPageTabInfo


class RibbonBar(RibbonControl):
    """
    RibbonBar() -> None
    RibbonBar(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=RIBBON_BAR_DEFAULT_STYLE) -> None
    
    Top-level control in a ribbon user interface.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=RIBBON_BAR_DEFAULT_STYLE) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RibbonBar() -> None
        RibbonBar(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=RIBBON_BAR_DEFAULT_STYLE) -> None
        
        Top-level control in a ribbon user interface.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=RIBBON_BAR_DEFAULT_STYLE) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=RIBBON_BAR_DEFAULT_STYLE) -> bool
        
        Create a ribbon bar in two-step ribbon bar construction.
        """

    def SetTabCtrlMargins(self, left: int, right: int) -> None:
        """
        SetTabCtrlMargins(left, right) -> None
        
        Set the margin widths (in pixels) on the left and right sides of the
        tab bar region of the ribbon bar.
        """

    def SetArtProvider(self, art: RibbonArtProvider) -> None:
        """
        SetArtProvider(art) -> None
        
        Set the art provider to be used be the ribbon bar.
        """

    @overload
    def SetActivePage(self, page: RibbonPage) -> bool:
        ...

    @overload
    def SetActivePage(self, page: int) -> bool:
        """
        SetActivePage(page) -> bool
        SetActivePage(page) -> bool
        
        Set the active page by index, without triggering any events.
        """

    def GetActivePage(self) -> int:
        """
        GetActivePage() -> int
        
        Get the index of the active page.
        """

    def GetPage(self, n: int) -> RibbonPage:
        """
        GetPage(n) -> RibbonPage
        
        Get a page by index.
        """

    def GetPageCount(self) -> int:
        """
        GetPageCount() -> int
        
        Get the number of pages in this bar.
        """

    def DismissExpandedPanel(self) -> bool:
        """
        DismissExpandedPanel() -> bool
        
        Dismiss the expanded panel of the currently active page.
        """

    def GetPageNumber(self, page: RibbonPage) -> int:
        """
        GetPageNumber(page) -> int
        
        Returns the number for a given ribbon bar page.
        """

    def DeletePage(self, n: int) -> None:
        """
        DeletePage(n) -> None
        
        Delete a single page from this ribbon bar.
        """

    def ClearPages(self) -> None:
        """
        ClearPages() -> None
        
        Delete all pages from the ribbon bar.
        """

    def IsPageShown(self, page: int) -> bool:
        """
        IsPageShown(page) -> bool
        
        Indicates whether the tab for the given page is shown to the user or
        not.
        """

    def ShowPage(self, page: int, show_tab: bool=True) -> None:
        """
        ShowPage(page, show_tab=True) -> None
        
        Show or hide the tab for a given page.
        """

    def HidePage(self, page: int) -> None:
        """
        HidePage(page) -> None
        
        Hides the tab for a given page.
        """

    def IsPageHighlighted(self, page: int) -> bool:
        """
        IsPageHighlighted(page) -> bool
        
        Indicates whether a tab is currently highlighted.
        """

    def AddPageHighlight(self, page: int, highlight: bool=True) -> None:
        """
        AddPageHighlight(page, highlight=True) -> None
        
        Highlight the specified tab.
        """

    def RemovePageHighlight(self, page: int) -> None:
        """
        RemovePageHighlight(page) -> None
        
        Changes a tab to not be highlighted.
        """

    @overload
    def ShowPanels(self, show: bool=True) -> None:
        ...

    @overload
    def ShowPanels(self, mode: RibbonDisplayMode) -> None:
        """
        ShowPanels(mode) -> None
        ShowPanels(show=True) -> None
        
        Shows or hide the panel area of the ribbon bar according to the given
        display mode.
        """

    def HidePanels(self) -> None:
        """
        HidePanels() -> None
        
        Hides the panel area of the ribbon bar.
        """

    def ArePanelsShown(self) -> bool:
        """
        ArePanelsShown() -> bool
        
        Indicates whether the panel area of the ribbon bar is shown.
        """

    def GetDisplayMode(self) -> RibbonDisplayMode:
        """
        GetDisplayMode() -> RibbonDisplayMode
        
        Returns the current display mode of the panel area.
        """

    def Realize(self) -> bool:
        """
        Realize() -> bool
        
        Perform initial layout and size calculations of the bar and its
        children.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ActivePage(self) -> int: ...
    @ActivePage.setter
    def ActivePage(self, value: int, /) -> None: ...
    @property
    def DisplayMode(self) -> RibbonDisplayMode: ...
    @property
    def PageCount(self) -> int: ...
# end of class RibbonBar


EVT_RIBBONBAR_PAGE_CHANGED    = wx.PyEventBinder(wxEVT_RIBBONBAR_PAGE_CHANGED, 1)
EVT_RIBBONBAR_PAGE_CHANGING   = wx.PyEventBinder(wxEVT_RIBBONBAR_PAGE_CHANGING,1)
EVT_RIBBONBAR_TAB_MIDDLE_DOWN = wx.PyEventBinder(wxEVT_RIBBONBAR_TAB_MIDDLE_DOWN, 1)
EVT_RIBBONBAR_TAB_MIDDLE_UP   = wx.PyEventBinder(wxEVT_RIBBONBAR_TAB_MIDDLE_UP, 1)
EVT_RIBBONBAR_TAB_RIGHT_DOWN  = wx.PyEventBinder(wxEVT_RIBBONBAR_TAB_RIGHT_DOWN, 1)
EVT_RIBBONBAR_TAB_RIGHT_UP    = wx.PyEventBinder(wxEVT_RIBBONBAR_TAB_RIGHT_UP, 1)
EVT_RIBBONBAR_TAB_LEFT_DCLICK = wx.PyEventBinder(wxEVT_RIBBONBAR_TAB_LEFT_DCLICK, 1)
EVT_RIBBONBAR_TOGGLED         = wx.PyEventBinder(wxEVT_RIBBONBAR_TOGGLED, 1)
EVT_RIBBONBAR_HELP_CLICK      = wx.PyEventBinder(wxEVT_RIBBONBAR_HELP_CLICK, 1)
#-- end-ribbon_bar --#
#-- begin-ribbon_art --#

class _RibbonArtSetting(IntEnum):
    RIBBON_ART_TAB_SEPARATION_SIZE = auto()
    RIBBON_ART_PAGE_BORDER_LEFT_SIZE = auto()
    RIBBON_ART_PAGE_BORDER_TOP_SIZE = auto()
    RIBBON_ART_PAGE_BORDER_RIGHT_SIZE = auto()
    RIBBON_ART_PAGE_BORDER_BOTTOM_SIZE = auto()
    RIBBON_ART_PANEL_X_SEPARATION_SIZE = auto()
    RIBBON_ART_PANEL_Y_SEPARATION_SIZE = auto()
    RIBBON_ART_TOOL_GROUP_SEPARATION_SIZE = auto()
    RIBBON_ART_GALLERY_BITMAP_PADDING_LEFT_SIZE = auto()
    RIBBON_ART_GALLERY_BITMAP_PADDING_RIGHT_SIZE = auto()
    RIBBON_ART_GALLERY_BITMAP_PADDING_TOP_SIZE = auto()
    RIBBON_ART_GALLERY_BITMAP_PADDING_BOTTOM_SIZE = auto()
    RIBBON_ART_PANEL_LABEL_FONT = auto()
    RIBBON_ART_BUTTON_BAR_LABEL_FONT = auto()
    RIBBON_ART_TAB_LABEL_FONT = auto()
    RIBBON_ART_BUTTON_BAR_LABEL_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_LABEL_DISABLED_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_HOVER_BORDER_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_ACTIVE_BORDER_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_GALLERY_BORDER_COLOUR = auto()
    RIBBON_ART_GALLERY_HOVER_BACKGROUND_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_BACKGROUND_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_FACE_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_HOVER_BACKGROUND_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_HOVER_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_HOVER_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_HOVER_FACE_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_ACTIVE_BACKGROUND_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_ACTIVE_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_ACTIVE_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_ACTIVE_FACE_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_DISABLED_BACKGROUND_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_DISABLED_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_DISABLED_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_GALLERY_BUTTON_DISABLED_FACE_COLOUR = auto()
    RIBBON_ART_GALLERY_ITEM_BORDER_COLOUR = auto()
    RIBBON_ART_TAB_LABEL_COLOUR = auto()
    RIBBON_ART_TAB_ACTIVE_LABEL_COLOUR = auto()
    RIBBON_ART_TAB_HOVER_LABEL_COLOUR = auto()
    RIBBON_ART_TAB_SEPARATOR_COLOUR = auto()
    RIBBON_ART_TAB_SEPARATOR_GRADIENT_COLOUR = auto()
    RIBBON_ART_TAB_CTRL_BACKGROUND_COLOUR = auto()
    RIBBON_ART_TAB_CTRL_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_TAB_HOVER_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_TAB_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR = auto()
    RIBBON_ART_TAB_HOVER_BACKGROUND_COLOUR = auto()
    RIBBON_ART_TAB_HOVER_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_TAB_ACTIVE_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_TAB_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR = auto()
    RIBBON_ART_TAB_ACTIVE_BACKGROUND_COLOUR = auto()
    RIBBON_ART_TAB_ACTIVE_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_TAB_BORDER_COLOUR = auto()
    RIBBON_ART_PANEL_BORDER_COLOUR = auto()
    RIBBON_ART_PANEL_BORDER_GRADIENT_COLOUR = auto()
    RIBBON_ART_PANEL_HOVER_BORDER_COLOUR = auto()
    RIBBON_ART_PANEL_HOVER_BORDER_GRADIENT_COLOUR = auto()
    RIBBON_ART_PANEL_MINIMISED_BORDER_COLOUR = auto()
    RIBBON_ART_PANEL_MINIMISED_BORDER_GRADIENT_COLOUR = auto()
    RIBBON_ART_PANEL_LABEL_BACKGROUND_COLOUR = auto()
    RIBBON_ART_PANEL_LABEL_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_PANEL_LABEL_COLOUR = auto()
    RIBBON_ART_PANEL_HOVER_LABEL_BACKGROUND_COLOUR = auto()
    RIBBON_ART_PANEL_HOVER_LABEL_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_PANEL_HOVER_LABEL_COLOUR = auto()
    RIBBON_ART_PANEL_MINIMISED_LABEL_COLOUR = auto()
    RIBBON_ART_PANEL_ACTIVE_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_PANEL_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR = auto()
    RIBBON_ART_PANEL_ACTIVE_BACKGROUND_COLOUR = auto()
    RIBBON_ART_PANEL_ACTIVE_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_PAGE_BORDER_COLOUR = auto()
    RIBBON_ART_PAGE_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_PAGE_BACKGROUND_TOP_GRADIENT_COLOUR = auto()
    RIBBON_ART_PAGE_BACKGROUND_COLOUR = auto()
    RIBBON_ART_PAGE_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_PAGE_HOVER_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_PAGE_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR = auto()
    RIBBON_ART_PAGE_HOVER_BACKGROUND_COLOUR = auto()
    RIBBON_ART_PAGE_HOVER_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_TOOLBAR_BORDER_COLOUR = auto()
    RIBBON_ART_TOOLBAR_HOVER_BORDER_COLOUR = auto()
    RIBBON_ART_TOOLBAR_FACE_COLOUR = auto()
    RIBBON_ART_TOOL_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_TOOL_BACKGROUND_TOP_GRADIENT_COLOUR = auto()
    RIBBON_ART_TOOL_BACKGROUND_COLOUR = auto()
    RIBBON_ART_TOOL_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_TOOL_HOVER_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_TOOL_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR = auto()
    RIBBON_ART_TOOL_HOVER_BACKGROUND_COLOUR = auto()
    RIBBON_ART_TOOL_HOVER_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_TOOL_ACTIVE_BACKGROUND_TOP_COLOUR = auto()
    RIBBON_ART_TOOL_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR = auto()
    RIBBON_ART_TOOL_ACTIVE_BACKGROUND_COLOUR = auto()
    RIBBON_ART_TOOL_ACTIVE_BACKGROUND_GRADIENT_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_LABEL_HIGHLIGHT_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_LABEL_HIGHLIGHT_GRADIENT_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_LABEL_HIGHLIGHT_TOP_COLOUR = auto()
    RIBBON_ART_BUTTON_BAR_LABEL_HIGHLIGHT_GRADIENT_TOP_COLOUR = auto()
RibbonArtSetting: TypeAlias = Union[_RibbonArtSetting, int]
RIBBON_ART_TAB_SEPARATION_SIZE = _RibbonArtSetting.RIBBON_ART_TAB_SEPARATION_SIZE
RIBBON_ART_PAGE_BORDER_LEFT_SIZE = _RibbonArtSetting.RIBBON_ART_PAGE_BORDER_LEFT_SIZE
RIBBON_ART_PAGE_BORDER_TOP_SIZE = _RibbonArtSetting.RIBBON_ART_PAGE_BORDER_TOP_SIZE
RIBBON_ART_PAGE_BORDER_RIGHT_SIZE = _RibbonArtSetting.RIBBON_ART_PAGE_BORDER_RIGHT_SIZE
RIBBON_ART_PAGE_BORDER_BOTTOM_SIZE = _RibbonArtSetting.RIBBON_ART_PAGE_BORDER_BOTTOM_SIZE
RIBBON_ART_PANEL_X_SEPARATION_SIZE = _RibbonArtSetting.RIBBON_ART_PANEL_X_SEPARATION_SIZE
RIBBON_ART_PANEL_Y_SEPARATION_SIZE = _RibbonArtSetting.RIBBON_ART_PANEL_Y_SEPARATION_SIZE
RIBBON_ART_TOOL_GROUP_SEPARATION_SIZE = _RibbonArtSetting.RIBBON_ART_TOOL_GROUP_SEPARATION_SIZE
RIBBON_ART_GALLERY_BITMAP_PADDING_LEFT_SIZE = _RibbonArtSetting.RIBBON_ART_GALLERY_BITMAP_PADDING_LEFT_SIZE
RIBBON_ART_GALLERY_BITMAP_PADDING_RIGHT_SIZE = _RibbonArtSetting.RIBBON_ART_GALLERY_BITMAP_PADDING_RIGHT_SIZE
RIBBON_ART_GALLERY_BITMAP_PADDING_TOP_SIZE = _RibbonArtSetting.RIBBON_ART_GALLERY_BITMAP_PADDING_TOP_SIZE
RIBBON_ART_GALLERY_BITMAP_PADDING_BOTTOM_SIZE = _RibbonArtSetting.RIBBON_ART_GALLERY_BITMAP_PADDING_BOTTOM_SIZE
RIBBON_ART_PANEL_LABEL_FONT = _RibbonArtSetting.RIBBON_ART_PANEL_LABEL_FONT
RIBBON_ART_BUTTON_BAR_LABEL_FONT = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_LABEL_FONT
RIBBON_ART_TAB_LABEL_FONT = _RibbonArtSetting.RIBBON_ART_TAB_LABEL_FONT
RIBBON_ART_BUTTON_BAR_LABEL_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_LABEL_COLOUR
RIBBON_ART_BUTTON_BAR_LABEL_DISABLED_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_LABEL_DISABLED_COLOUR
RIBBON_ART_BUTTON_BAR_HOVER_BORDER_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_HOVER_BORDER_COLOUR
RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_TOP_COLOUR
RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR
RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_COLOUR
RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_HOVER_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_BUTTON_BAR_ACTIVE_BORDER_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_ACTIVE_BORDER_COLOUR
RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_TOP_COLOUR
RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR
RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_COLOUR
RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_ACTIVE_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_GALLERY_BORDER_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BORDER_COLOUR
RIBBON_ART_GALLERY_HOVER_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_HOVER_BACKGROUND_COLOUR
RIBBON_ART_GALLERY_BUTTON_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_BACKGROUND_COLOUR
RIBBON_ART_GALLERY_BUTTON_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_GALLERY_BUTTON_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_BACKGROUND_TOP_COLOUR
RIBBON_ART_GALLERY_BUTTON_FACE_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_FACE_COLOUR
RIBBON_ART_GALLERY_BUTTON_HOVER_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_HOVER_BACKGROUND_COLOUR
RIBBON_ART_GALLERY_BUTTON_HOVER_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_HOVER_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_GALLERY_BUTTON_HOVER_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_HOVER_BACKGROUND_TOP_COLOUR
RIBBON_ART_GALLERY_BUTTON_HOVER_FACE_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_HOVER_FACE_COLOUR
RIBBON_ART_GALLERY_BUTTON_ACTIVE_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_ACTIVE_BACKGROUND_COLOUR
RIBBON_ART_GALLERY_BUTTON_ACTIVE_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_ACTIVE_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_GALLERY_BUTTON_ACTIVE_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_ACTIVE_BACKGROUND_TOP_COLOUR
RIBBON_ART_GALLERY_BUTTON_ACTIVE_FACE_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_ACTIVE_FACE_COLOUR
RIBBON_ART_GALLERY_BUTTON_DISABLED_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_DISABLED_BACKGROUND_COLOUR
RIBBON_ART_GALLERY_BUTTON_DISABLED_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_DISABLED_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_GALLERY_BUTTON_DISABLED_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_DISABLED_BACKGROUND_TOP_COLOUR
RIBBON_ART_GALLERY_BUTTON_DISABLED_FACE_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_BUTTON_DISABLED_FACE_COLOUR
RIBBON_ART_GALLERY_ITEM_BORDER_COLOUR = _RibbonArtSetting.RIBBON_ART_GALLERY_ITEM_BORDER_COLOUR
RIBBON_ART_TAB_LABEL_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_LABEL_COLOUR
RIBBON_ART_TAB_ACTIVE_LABEL_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_ACTIVE_LABEL_COLOUR
RIBBON_ART_TAB_HOVER_LABEL_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_HOVER_LABEL_COLOUR
RIBBON_ART_TAB_SEPARATOR_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_SEPARATOR_COLOUR
RIBBON_ART_TAB_SEPARATOR_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_SEPARATOR_GRADIENT_COLOUR
RIBBON_ART_TAB_CTRL_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_CTRL_BACKGROUND_COLOUR
RIBBON_ART_TAB_CTRL_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_CTRL_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_TAB_HOVER_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_HOVER_BACKGROUND_TOP_COLOUR
RIBBON_ART_TAB_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR
RIBBON_ART_TAB_HOVER_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_HOVER_BACKGROUND_COLOUR
RIBBON_ART_TAB_HOVER_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_HOVER_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_TAB_ACTIVE_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_ACTIVE_BACKGROUND_TOP_COLOUR
RIBBON_ART_TAB_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR
RIBBON_ART_TAB_ACTIVE_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_ACTIVE_BACKGROUND_COLOUR
RIBBON_ART_TAB_ACTIVE_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_ACTIVE_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_TAB_BORDER_COLOUR = _RibbonArtSetting.RIBBON_ART_TAB_BORDER_COLOUR
RIBBON_ART_PANEL_BORDER_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_BORDER_COLOUR
RIBBON_ART_PANEL_BORDER_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_BORDER_GRADIENT_COLOUR
RIBBON_ART_PANEL_HOVER_BORDER_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_HOVER_BORDER_COLOUR
RIBBON_ART_PANEL_HOVER_BORDER_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_HOVER_BORDER_GRADIENT_COLOUR
RIBBON_ART_PANEL_MINIMISED_BORDER_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_MINIMISED_BORDER_COLOUR
RIBBON_ART_PANEL_MINIMISED_BORDER_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_MINIMISED_BORDER_GRADIENT_COLOUR
RIBBON_ART_PANEL_LABEL_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_LABEL_BACKGROUND_COLOUR
RIBBON_ART_PANEL_LABEL_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_LABEL_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_PANEL_LABEL_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_LABEL_COLOUR
RIBBON_ART_PANEL_HOVER_LABEL_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_HOVER_LABEL_BACKGROUND_COLOUR
RIBBON_ART_PANEL_HOVER_LABEL_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_HOVER_LABEL_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_PANEL_HOVER_LABEL_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_HOVER_LABEL_COLOUR
RIBBON_ART_PANEL_MINIMISED_LABEL_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_MINIMISED_LABEL_COLOUR
RIBBON_ART_PANEL_ACTIVE_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_ACTIVE_BACKGROUND_TOP_COLOUR
RIBBON_ART_PANEL_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR
RIBBON_ART_PANEL_ACTIVE_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_ACTIVE_BACKGROUND_COLOUR
RIBBON_ART_PANEL_ACTIVE_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_PANEL_ACTIVE_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_PAGE_BORDER_COLOUR = _RibbonArtSetting.RIBBON_ART_PAGE_BORDER_COLOUR
RIBBON_ART_PAGE_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_PAGE_BACKGROUND_TOP_COLOUR
RIBBON_ART_PAGE_BACKGROUND_TOP_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_PAGE_BACKGROUND_TOP_GRADIENT_COLOUR
RIBBON_ART_PAGE_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_PAGE_BACKGROUND_COLOUR
RIBBON_ART_PAGE_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_PAGE_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_PAGE_HOVER_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_PAGE_HOVER_BACKGROUND_TOP_COLOUR
RIBBON_ART_PAGE_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_PAGE_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR
RIBBON_ART_PAGE_HOVER_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_PAGE_HOVER_BACKGROUND_COLOUR
RIBBON_ART_PAGE_HOVER_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_PAGE_HOVER_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_TOOLBAR_BORDER_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOLBAR_BORDER_COLOUR
RIBBON_ART_TOOLBAR_HOVER_BORDER_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOLBAR_HOVER_BORDER_COLOUR
RIBBON_ART_TOOLBAR_FACE_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOLBAR_FACE_COLOUR
RIBBON_ART_TOOL_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOL_BACKGROUND_TOP_COLOUR
RIBBON_ART_TOOL_BACKGROUND_TOP_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOL_BACKGROUND_TOP_GRADIENT_COLOUR
RIBBON_ART_TOOL_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOL_BACKGROUND_COLOUR
RIBBON_ART_TOOL_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOL_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_TOOL_HOVER_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOL_HOVER_BACKGROUND_TOP_COLOUR
RIBBON_ART_TOOL_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOL_HOVER_BACKGROUND_TOP_GRADIENT_COLOUR
RIBBON_ART_TOOL_HOVER_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOL_HOVER_BACKGROUND_COLOUR
RIBBON_ART_TOOL_HOVER_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOL_HOVER_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_TOOL_ACTIVE_BACKGROUND_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOL_ACTIVE_BACKGROUND_TOP_COLOUR
RIBBON_ART_TOOL_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOL_ACTIVE_BACKGROUND_TOP_GRADIENT_COLOUR
RIBBON_ART_TOOL_ACTIVE_BACKGROUND_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOL_ACTIVE_BACKGROUND_COLOUR
RIBBON_ART_TOOL_ACTIVE_BACKGROUND_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_TOOL_ACTIVE_BACKGROUND_GRADIENT_COLOUR
RIBBON_ART_BUTTON_BAR_LABEL_HIGHLIGHT_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_LABEL_HIGHLIGHT_COLOUR
RIBBON_ART_BUTTON_BAR_LABEL_HIGHLIGHT_GRADIENT_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_LABEL_HIGHLIGHT_GRADIENT_COLOUR
RIBBON_ART_BUTTON_BAR_LABEL_HIGHLIGHT_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_LABEL_HIGHLIGHT_TOP_COLOUR
RIBBON_ART_BUTTON_BAR_LABEL_HIGHLIGHT_GRADIENT_TOP_COLOUR = _RibbonArtSetting.RIBBON_ART_BUTTON_BAR_LABEL_HIGHLIGHT_GRADIENT_TOP_COLOUR

class _RibbonScrollButtonStyle(IntEnum):
    RIBBON_SCROLL_BTN_LEFT = auto()
    RIBBON_SCROLL_BTN_RIGHT = auto()
    RIBBON_SCROLL_BTN_UP = auto()
    RIBBON_SCROLL_BTN_DOWN = auto()
    RIBBON_SCROLL_BTN_DIRECTION_MASK = auto()
    RIBBON_SCROLL_BTN_NORMAL = auto()
    RIBBON_SCROLL_BTN_HOVERED = auto()
    RIBBON_SCROLL_BTN_ACTIVE = auto()
    RIBBON_SCROLL_BTN_STATE_MASK = auto()
    RIBBON_SCROLL_BTN_FOR_OTHER = auto()
    RIBBON_SCROLL_BTN_FOR_TABS = auto()
    RIBBON_SCROLL_BTN_FOR_PAGE = auto()
    RIBBON_SCROLL_BTN_FOR_MASK = auto()
RibbonScrollButtonStyle: TypeAlias = Union[_RibbonScrollButtonStyle, int]
RIBBON_SCROLL_BTN_LEFT = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_LEFT
RIBBON_SCROLL_BTN_RIGHT = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_RIGHT
RIBBON_SCROLL_BTN_UP = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_UP
RIBBON_SCROLL_BTN_DOWN = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_DOWN
RIBBON_SCROLL_BTN_DIRECTION_MASK = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_DIRECTION_MASK
RIBBON_SCROLL_BTN_NORMAL = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_NORMAL
RIBBON_SCROLL_BTN_HOVERED = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_HOVERED
RIBBON_SCROLL_BTN_ACTIVE = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_ACTIVE
RIBBON_SCROLL_BTN_STATE_MASK = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_STATE_MASK
RIBBON_SCROLL_BTN_FOR_OTHER = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_FOR_OTHER
RIBBON_SCROLL_BTN_FOR_TABS = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_FOR_TABS
RIBBON_SCROLL_BTN_FOR_PAGE = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_FOR_PAGE
RIBBON_SCROLL_BTN_FOR_MASK = _RibbonScrollButtonStyle.RIBBON_SCROLL_BTN_FOR_MASK

class _RibbonButtonKind(IntEnum):
    RIBBON_BUTTON_NORMAL = auto()
    RIBBON_BUTTON_DROPDOWN = auto()
    RIBBON_BUTTON_HYBRID = auto()
    RIBBON_BUTTON_TOGGLE = auto()
RibbonButtonKind: TypeAlias = Union[_RibbonButtonKind, int]
RIBBON_BUTTON_NORMAL = _RibbonButtonKind.RIBBON_BUTTON_NORMAL
RIBBON_BUTTON_DROPDOWN = _RibbonButtonKind.RIBBON_BUTTON_DROPDOWN
RIBBON_BUTTON_HYBRID = _RibbonButtonKind.RIBBON_BUTTON_HYBRID
RIBBON_BUTTON_TOGGLE = _RibbonButtonKind.RIBBON_BUTTON_TOGGLE

class RibbonArtProvider:
    """
    RibbonArtProvider() -> None
    
    wxRibbonArtProvider is responsible for drawing all the components of
    the ribbon interface.
    """

    def __init__(self) -> None:
        """
        RibbonArtProvider() -> None
        
        wxRibbonArtProvider is responsible for drawing all the components of
        the ribbon interface.
        """

    def Clone(self) -> RibbonArtProvider:
        """
        Clone() -> RibbonArtProvider
        
        Create a new art provider which is a clone of this one.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Set the style flags.
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        
        Get the previously set style flags.
        """

    def GetMetric(self, id: int) -> int:
        """
        GetMetric(id) -> int
        
        Get the value of a certain integer setting.
        """

    def SetMetric(self, id: int, new_val: int) -> None:
        """
        SetMetric(id, new_val) -> None
        
        Set the value of a certain integer setting to the value new_val.
        """

    def SetFont(self, id: int, font: wx.Font) -> None:
        """
        SetFont(id, font) -> None
        
        Set the value of a certain font setting to the value font.
        """

    def GetFont(self, id: int) -> wx.Font:
        """
        GetFont(id) -> wx.Font
        
        Get the value of a certain font setting.
        """

    def GetColour(self, id: int) -> wx.Colour:
        """
        GetColour(id) -> wx.Colour
        
        Get the value of a certain colour setting.
        """

    def SetColour(self, id: int, colour: wx.Colour) -> None:
        """
        SetColour(id, colour) -> None
        
        Set the value of a certain colour setting to the value colour.
        """

    def GetColor(self, id: int) -> wx.Colour:
        """
        GetColor(id) -> wx.Colour
        """

    def SetColor(self, id: int, color: wx.Colour) -> None:
        """
        SetColor(id, color) -> None
        """

    def GetColourScheme(self) -> Tuple[wx.Colour, wx.Colour, wx.Colour]:
        """
        GetColourScheme() -> Tuple[wx.Colour, wx.Colour, wx.Colour]
        
        Get the current colour scheme.
        """

    def SetColourScheme(self, primary: wx.Colour, secondary: wx.Colour, tertiary: wx.Colour) -> None:
        """
        SetColourScheme(primary, secondary, tertiary) -> None
        
        Set all applicable colour settings from a few base colours.
        """

    def DrawTabCtrlBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawTabCtrlBackground(dc, wnd, rect) -> None
        
        Draw the background of the tab region of a ribbon bar.
        """

    def DrawTab(self, dc: wx.DC, wnd: wx.Window, tab: RibbonPageTabInfo) -> None:
        """
        DrawTab(dc, wnd, tab) -> None
        
        Draw a single tab in the tab region of a ribbon bar.
        """

    def DrawTabSeparator(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, visibility: float) -> None:
        """
        DrawTabSeparator(dc, wnd, rect, visibility) -> None
        
        Draw a separator between two tabs in a ribbon bar.
        """

    def DrawPageBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawPageBackground(dc, wnd, rect) -> None
        
        Draw the background of a ribbon page.
        """

    def DrawScrollButton(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, style: int) -> None:
        """
        DrawScrollButton(dc, wnd, rect, style) -> None
        
        Draw a ribbon-style scroll button.
        """

    def DrawPanelBackground(self, dc: wx.DC, wnd: RibbonPanel, rect: wx.Rect) -> None:
        """
        DrawPanelBackground(dc, wnd, rect) -> None
        
        Draw the background and chrome for a ribbon panel.
        """

    def DrawGalleryBackground(self, dc: wx.DC, wnd: RibbonGallery, rect: wx.Rect) -> None:
        """
        DrawGalleryBackground(dc, wnd, rect) -> None
        
        Draw the background and chrome for a wxRibbonGallery control.
        """

    def DrawGalleryItemBackground(self, dc: wx.DC, wnd: RibbonGallery, rect: wx.Rect, item: RibbonGalleryItem) -> None:
        """
        DrawGalleryItemBackground(dc, wnd, rect, item) -> None
        
        Draw the background of a single item in a wxRibbonGallery control.
        """

    def DrawMinimisedPanel(self, dc: wx.DC, wnd: RibbonPanel, rect: wx.Rect, bitmap: wx.Bitmap) -> None:
        """
        DrawMinimisedPanel(dc, wnd, rect, bitmap) -> None
        
        Draw a minimised ribbon panel.
        """

    def DrawButtonBarBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawButtonBarBackground(dc, wnd, rect) -> None
        
        Draw the background for a wxRibbonButtonBar control.
        """

    def DrawButtonBarButton(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, kind: RibbonButtonKind, state: int, label: str, bitmap_large: wx.Bitmap, bitmap_small: wx.Bitmap) -> None:
        """
        DrawButtonBarButton(dc, wnd, rect, kind, state, label, bitmap_large, bitmap_small) -> None
        
        Draw a single button for a wxRibbonButtonBar control.
        """

    def DrawToolBarBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawToolBarBackground(dc, wnd, rect) -> None
        
        Draw the background for a wxRibbonToolBar control.
        """

    def DrawToolGroupBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawToolGroupBackground(dc, wnd, rect) -> None
        
        Draw the background for a group of tools on a wxRibbonToolBar control.
        """

    def DrawTool(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, bitmap: wx.Bitmap, kind: RibbonButtonKind, state: int) -> None:
        """
        DrawTool(dc, wnd, rect, bitmap, kind, state) -> None
        
        Draw a single tool (for a wxRibbonToolBar control).
        """

    def DrawToggleButton(self, dc: wx.DC, wnd: RibbonBar, rect: wx.Rect, mode: RibbonDisplayMode) -> None:
        """
        DrawToggleButton(dc, wnd, rect, mode) -> None
        
        Draw toggle button on wxRibbonBar.
        """

    def DrawHelpButton(self, dc: wx.DC, wnd: RibbonBar, rect: wx.Rect) -> None:
        """
        DrawHelpButton(dc, wnd, rect) -> None
        
        Draw help button on wxRibbonBar.
        """

    def GetBarTabWidth(self, dc: wx.DC, wnd: wx.Window, label: str, bitmap: wx.Bitmap, ideal: int, small_begin_need_separator: int, small_must_have_separator: int, minimum: int) -> None:
        """
        GetBarTabWidth(dc, wnd, label, bitmap, ideal, small_begin_need_separator, small_must_have_separator, minimum) -> None
        
        Calculate the ideal and minimum width (in pixels) of a tab in a ribbon
        bar.
        """

    def GetTabCtrlHeight(self, dc: wx.DC, wnd: wx.Window, pages: RibbonPageTabInfoArray) -> int:
        """
        GetTabCtrlHeight(dc, wnd, pages) -> int
        
        Calculate the height (in pixels) of the tab region of a ribbon bar.
        """

    def GetScrollButtonMinimumSize(self, dc: wx.DC, wnd: wx.Window, style: int) -> wx.Size:
        """
        GetScrollButtonMinimumSize(dc, wnd, style) -> wx.Size
        
        Calculate the minimum size (in pixels) of a scroll button.
        """

    def GetPanelSize(self, dc: wx.DC, wnd: RibbonPanel, client_size: wx.Size, client_offset: wx.Point) -> wx.Size:
        """
        GetPanelSize(dc, wnd, client_size, client_offset) -> wx.Size
        
        Calculate the size of a panel for a given client size.
        """

    def GetPanelClientSize(self, dc: wx.DC, wnd: RibbonPanel, size: wx.Size, client_offset: wx.Point) -> wx.Size:
        """
        GetPanelClientSize(dc, wnd, size, client_offset) -> wx.Size
        
        Calculate the client size of a panel for a given overall size.
        """

    def GetPanelExtButtonArea(self, dc: wx.DC, wnd: RibbonPanel, rect: wx.Rect) -> wx.Rect:
        """
        GetPanelExtButtonArea(dc, wnd, rect) -> wx.Rect
        
        Calculate the position and size of the panel extension button.
        """

    def GetGallerySize(self, dc: wx.DC, wnd: RibbonGallery, client_size: wx.Size) -> wx.Size:
        """
        GetGallerySize(dc, wnd, client_size) -> wx.Size
        
        Calculate the size of a wxRibbonGallery control for a given client
        size.
        """

    def GetGalleryClientSize(self, dc: wx.DC, wnd: RibbonGallery, size: wx.Size, client_offset: wx.Point, scroll_up_button: wx.Rect, scroll_down_button: wx.Rect, extension_button: wx.Rect) -> wx.Size:
        """
        GetGalleryClientSize(dc, wnd, size, client_offset, scroll_up_button, scroll_down_button, extension_button) -> wx.Size
        
        Calculate the client size of a wxRibbonGallery control for a given
        size.
        """

    def GetPageBackgroundRedrawArea(self, dc: wx.DC, wnd: RibbonPage, page_old_size: wx.Size, page_new_size: wx.Size) -> wx.Rect:
        """
        GetPageBackgroundRedrawArea(dc, wnd, page_old_size, page_new_size) -> wx.Rect
        
        Calculate the portion of a page background which needs to be redrawn
        when a page is resized.
        """

    def GetButtonBarButtonSize(self, dc: wx.DC, wnd: wx.Window, kind: RibbonButtonKind, size: RibbonButtonBarButtonState, label: str, text_min_width: int, bitmap_size_large: wx.Size, bitmap_size_small: wx.Size, button_size: wx.Size, normal_region: wx.Rect, dropdown_region: wx.Rect) -> bool:
        """
        GetButtonBarButtonSize(dc, wnd, kind, size, label, text_min_width, bitmap_size_large, bitmap_size_small, button_size, normal_region, dropdown_region) -> bool
        
        Calculate the size of a button within a wxRibbonButtonBar.
        """

    def GetButtonBarButtonTextWidth(self, dc: wx.DC, label: str, kind: RibbonButtonKind, size: RibbonButtonBarButtonState) -> int:
        """
        GetButtonBarButtonTextWidth(dc, label, kind, size) -> int
        
        Gets the width of the string if it is used as a wxRibbonButtonBar
        button label.
        """

    def GetMinimisedPanelMinimumSize(self, dc: wx.DC, wnd: RibbonPanel, desired_bitmap_size: wx.Size, expanded_panel_direction: wx.Direction) -> wx.Size:
        """
        GetMinimisedPanelMinimumSize(dc, wnd, desired_bitmap_size, expanded_panel_direction) -> wx.Size
        
        Calculate the size of a minimised ribbon panel.
        """

    def GetToolSize(self, dc: wx.DC, wnd: wx.Window, bitmap_size: wx.Size, kind: RibbonButtonKind, is_first: bool, is_last: bool, dropdown_region: wx.Rect) -> wx.Size:
        """
        GetToolSize(dc, wnd, bitmap_size, kind, is_first, is_last, dropdown_region) -> wx.Size
        
        Calculate the size of a tool within a wxRibbonToolBar.
        """

    def GetBarToggleButtonArea(self, rect: wx.Rect) -> wx.Rect:
        """
        GetBarToggleButtonArea(rect) -> wx.Rect
        
        Calculate the position and size of the ribbon's toggle button.
        """

    def GetRibbonHelpButtonArea(self, rect: wx.Rect) -> wx.Rect:
        """
        GetRibbonHelpButtonArea(rect) -> wx.Rect
        
        Calculate the position and size of the ribbon's help button.
        """
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
# end of class RibbonArtProvider


class RibbonMSWArtProvider(RibbonArtProvider):
    """
    RibbonMSWArtProvider(set_colour_scheme=True) -> None
    """

    def __init__(self, set_colour_scheme: bool=True) -> None:
        """
        RibbonMSWArtProvider(set_colour_scheme=True) -> None
        """

    def Clone(self) -> RibbonArtProvider:
        """
        Clone() -> RibbonArtProvider
        
        Create a new art provider which is a clone of this one.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Set the style flags.
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        
        Get the previously set style flags.
        """

    def GetMetric(self, id: int) -> int:
        """
        GetMetric(id) -> int
        
        Get the value of a certain integer setting.
        """

    def SetMetric(self, id: int, new_val: int) -> None:
        """
        SetMetric(id, new_val) -> None
        
        Set the value of a certain integer setting to the value new_val.
        """

    def SetFont(self, id: int, font: wx.Font) -> None:
        """
        SetFont(id, font) -> None
        
        Set the value of a certain font setting to the value font.
        """

    def GetFont(self, id: int) -> wx.Font:
        """
        GetFont(id) -> wx.Font
        
        Get the value of a certain font setting.
        """

    def GetColour(self, id: int) -> wx.Colour:
        """
        GetColour(id) -> wx.Colour
        
        Get the value of a certain colour setting.
        """

    def SetColour(self, id: int, colour: wx.Colour) -> None:
        """
        SetColour(id, colour) -> None
        
        Set the value of a certain colour setting to the value colour.
        """

    def GetColourScheme(self) -> Tuple[wx.Colour, wx.Colour, wx.Colour]:
        """
        GetColourScheme() -> Tuple[wx.Colour, wx.Colour, wx.Colour]
        
        Get the current colour scheme.
        """

    def SetColourScheme(self, primary: wx.Colour, secondary: wx.Colour, tertiary: wx.Colour) -> None:
        """
        SetColourScheme(primary, secondary, tertiary) -> None
        
        Set all applicable colour settings from a few base colours.
        """

    def GetTabCtrlHeight(self, dc: wx.DC, wnd: wx.Window, pages: RibbonPageTabInfoArray) -> int:
        """
        GetTabCtrlHeight(dc, wnd, pages) -> int
        
        Calculate the height (in pixels) of the tab region of a ribbon bar.
        """

    def DrawTabCtrlBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawTabCtrlBackground(dc, wnd, rect) -> None
        
        Draw the background of the tab region of a ribbon bar.
        """

    def DrawTab(self, dc: wx.DC, wnd: wx.Window, tab: RibbonPageTabInfo) -> None:
        """
        DrawTab(dc, wnd, tab) -> None
        
        Draw a single tab in the tab region of a ribbon bar.
        """

    def DrawTabSeparator(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, visibility: float) -> None:
        """
        DrawTabSeparator(dc, wnd, rect, visibility) -> None
        
        Draw a separator between two tabs in a ribbon bar.
        """

    def DrawPageBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawPageBackground(dc, wnd, rect) -> None
        
        Draw the background of a ribbon page.
        """

    def DrawScrollButton(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, style: int) -> None:
        """
        DrawScrollButton(dc, wnd, rect, style) -> None
        
        Draw a ribbon-style scroll button.
        """

    def DrawPanelBackground(self, dc: wx.DC, wnd: RibbonPanel, rect: wx.Rect) -> None:
        """
        DrawPanelBackground(dc, wnd, rect) -> None
        
        Draw the background and chrome for a ribbon panel.
        """

    def DrawGalleryBackground(self, dc: wx.DC, wnd: RibbonGallery, rect: wx.Rect) -> None:
        """
        DrawGalleryBackground(dc, wnd, rect) -> None
        
        Draw the background and chrome for a wxRibbonGallery control.
        """

    def DrawGalleryItemBackground(self, dc: wx.DC, wnd: RibbonGallery, rect: wx.Rect, item: RibbonGalleryItem) -> None:
        """
        DrawGalleryItemBackground(dc, wnd, rect, item) -> None
        
        Draw the background of a single item in a wxRibbonGallery control.
        """

    def DrawMinimisedPanel(self, dc: wx.DC, wnd: RibbonPanel, rect: wx.Rect, bitmap: wx.Bitmap) -> None:
        """
        DrawMinimisedPanel(dc, wnd, rect, bitmap) -> None
        
        Draw a minimised ribbon panel.
        """

    def DrawButtonBarBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawButtonBarBackground(dc, wnd, rect) -> None
        
        Draw the background for a wxRibbonButtonBar control.
        """

    def DrawButtonBarButton(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, kind: RibbonButtonKind, state: int, label: str, bitmap_large: wx.Bitmap, bitmap_small: wx.Bitmap) -> None:
        """
        DrawButtonBarButton(dc, wnd, rect, kind, state, label, bitmap_large, bitmap_small) -> None
        
        Draw a single button for a wxRibbonButtonBar control.
        """

    def DrawToolBarBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawToolBarBackground(dc, wnd, rect) -> None
        
        Draw the background for a wxRibbonToolBar control.
        """

    def DrawToolGroupBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawToolGroupBackground(dc, wnd, rect) -> None
        
        Draw the background for a group of tools on a wxRibbonToolBar control.
        """

    def DrawTool(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, bitmap: wx.Bitmap, kind: RibbonButtonKind, state: int) -> None:
        """
        DrawTool(dc, wnd, rect, bitmap, kind, state) -> None
        
        Draw a single tool (for a wxRibbonToolBar control).
        """

    def DrawToggleButton(self, dc: wx.DC, wnd: RibbonBar, rect: wx.Rect, mode: RibbonDisplayMode) -> None:
        """
        DrawToggleButton(dc, wnd, rect, mode) -> None
        
        Draw toggle button on wxRibbonBar.
        """

    def DrawHelpButton(self, dc: wx.DC, wnd: RibbonBar, rect: wx.Rect) -> None:
        """
        DrawHelpButton(dc, wnd, rect) -> None
        
        Draw help button on wxRibbonBar.
        """

    def GetBarTabWidth(self, dc: wx.DC, wnd: wx.Window, label: str, bitmap: wx.Bitmap, ideal: int, small_begin_need_separator: int, small_must_have_separator: int, minimum: int) -> None:
        """
        GetBarTabWidth(dc, wnd, label, bitmap, ideal, small_begin_need_separator, small_must_have_separator, minimum) -> None
        
        Calculate the ideal and minimum width (in pixels) of a tab in a ribbon
        bar.
        """

    def GetScrollButtonMinimumSize(self, dc: wx.DC, wnd: wx.Window, style: int) -> wx.Size:
        """
        GetScrollButtonMinimumSize(dc, wnd, style) -> wx.Size
        
        Calculate the minimum size (in pixels) of a scroll button.
        """

    def GetPanelSize(self, dc: wx.DC, wnd: RibbonPanel, client_size: wx.Size, client_offset: wx.Point) -> wx.Size:
        """
        GetPanelSize(dc, wnd, client_size, client_offset) -> wx.Size
        
        Calculate the size of a panel for a given client size.
        """

    def GetPanelClientSize(self, dc: wx.DC, wnd: RibbonPanel, size: wx.Size, client_offset: wx.Point) -> wx.Size:
        """
        GetPanelClientSize(dc, wnd, size, client_offset) -> wx.Size
        
        Calculate the client size of a panel for a given overall size.
        """

    def GetPanelExtButtonArea(self, dc: wx.DC, wnd: RibbonPanel, rect: wx.Rect) -> wx.Rect:
        """
        GetPanelExtButtonArea(dc, wnd, rect) -> wx.Rect
        
        Calculate the position and size of the panel extension button.
        """

    def GetGallerySize(self, dc: wx.DC, wnd: RibbonGallery, client_size: wx.Size) -> wx.Size:
        """
        GetGallerySize(dc, wnd, client_size) -> wx.Size
        
        Calculate the size of a wxRibbonGallery control for a given client
        size.
        """

    def GetGalleryClientSize(self, dc: wx.DC, wnd: RibbonGallery, size: wx.Size, client_offset: wx.Point, scroll_up_button: wx.Rect, scroll_down_button: wx.Rect, extension_button: wx.Rect) -> wx.Size:
        """
        GetGalleryClientSize(dc, wnd, size, client_offset, scroll_up_button, scroll_down_button, extension_button) -> wx.Size
        
        Calculate the client size of a wxRibbonGallery control for a given
        size.
        """

    def GetPageBackgroundRedrawArea(self, dc: wx.DC, wnd: RibbonPage, page_old_size: wx.Size, page_new_size: wx.Size) -> wx.Rect:
        """
        GetPageBackgroundRedrawArea(dc, wnd, page_old_size, page_new_size) -> wx.Rect
        
        Calculate the portion of a page background which needs to be redrawn
        when a page is resized.
        """

    def GetButtonBarButtonSize(self, dc: wx.DC, wnd: wx.Window, kind: RibbonButtonKind, size: RibbonButtonBarButtonState, label: str, text_min_width: int, bitmap_size_large: wx.Size, bitmap_size_small: wx.Size, button_size: wx.Size, normal_region: wx.Rect, dropdown_region: wx.Rect) -> bool:
        """
        GetButtonBarButtonSize(dc, wnd, kind, size, label, text_min_width, bitmap_size_large, bitmap_size_small, button_size, normal_region, dropdown_region) -> bool
        
        Calculate the size of a button within a wxRibbonButtonBar.
        """

    def GetButtonBarButtonTextWidth(self, dc: wx.DC, label: str, kind: RibbonButtonKind, size: RibbonButtonBarButtonState) -> int:
        """
        GetButtonBarButtonTextWidth(dc, label, kind, size) -> int
        
        Gets the width of the string if it is used as a wxRibbonButtonBar
        button label.
        """

    def GetMinimisedPanelMinimumSize(self, dc: wx.DC, wnd: RibbonPanel, desired_bitmap_size: wx.Size, expanded_panel_direction: wx.Direction) -> wx.Size:
        """
        GetMinimisedPanelMinimumSize(dc, wnd, desired_bitmap_size, expanded_panel_direction) -> wx.Size
        
        Calculate the size of a minimised ribbon panel.
        """

    def GetToolSize(self, dc: wx.DC, wnd: wx.Window, bitmap_size: wx.Size, kind: RibbonButtonKind, is_first: bool, is_last: bool, dropdown_region: wx.Rect) -> wx.Size:
        """
        GetToolSize(dc, wnd, bitmap_size, kind, is_first, is_last, dropdown_region) -> wx.Size
        
        Calculate the size of a tool within a wxRibbonToolBar.
        """

    def GetBarToggleButtonArea(self, rect: wx.Rect) -> wx.Rect:
        """
        GetBarToggleButtonArea(rect) -> wx.Rect
        
        Calculate the position and size of the ribbon's toggle button.
        """

    def GetRibbonHelpButtonArea(self, rect: wx.Rect) -> wx.Rect:
        """
        GetRibbonHelpButtonArea(rect) -> wx.Rect
        
        Calculate the position and size of the ribbon's help button.
        """
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
# end of class RibbonMSWArtProvider


class RibbonAUIArtProvider(RibbonMSWArtProvider):
    """
    RibbonAUIArtProvider() -> None
    """

    def __init__(self) -> None:
        """
        RibbonAUIArtProvider() -> None
        """

    def Clone(self) -> RibbonArtProvider:
        """
        Clone() -> RibbonArtProvider
        
        Create a new art provider which is a clone of this one.
        """

    def GetColour(self, id: int) -> wx.Colour:
        """
        GetColour(id) -> wx.Colour
        
        Get the value of a certain colour setting.
        """

    def SetColour(self, id: int, colour: wx.Colour) -> None:
        """
        SetColour(id, colour) -> None
        
        Set the value of a certain colour setting to the value colour.
        """

    def SetColourScheme(self, primary: wx.Colour, secondary: wx.Colour, tertiary: wx.Colour) -> None:
        """
        SetColourScheme(primary, secondary, tertiary) -> None
        
        Set all applicable colour settings from a few base colours.
        """

    def SetFont(self, id: int, font: wx.Font) -> None:
        """
        SetFont(id, font) -> None
        
        Set the value of a certain font setting to the value font.
        """

    def GetScrollButtonMinimumSize(self, dc: wx.DC, wnd: wx.Window, style: int) -> wx.Size:
        """
        GetScrollButtonMinimumSize(dc, wnd, style) -> wx.Size
        
        Calculate the minimum size (in pixels) of a scroll button.
        """

    def DrawScrollButton(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, style: int) -> None:
        """
        DrawScrollButton(dc, wnd, rect, style) -> None
        
        Draw a ribbon-style scroll button.
        """

    def GetPanelSize(self, dc: wx.DC, wnd: RibbonPanel, client_size: wx.Size, client_offset: wx.Point) -> wx.Size:
        """
        GetPanelSize(dc, wnd, client_size, client_offset) -> wx.Size
        
        Calculate the size of a panel for a given client size.
        """

    def GetPanelClientSize(self, dc: wx.DC, wnd: RibbonPanel, size: wx.Size, client_offset: wx.Point) -> wx.Size:
        """
        GetPanelClientSize(dc, wnd, size, client_offset) -> wx.Size
        
        Calculate the client size of a panel for a given overall size.
        """

    def GetPanelExtButtonArea(self, dc: wx.DC, wnd: RibbonPanel, rect: wx.Rect) -> wx.Rect:
        """
        GetPanelExtButtonArea(dc, wnd, rect) -> wx.Rect
        
        Calculate the position and size of the panel extension button.
        """

    def DrawTabCtrlBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawTabCtrlBackground(dc, wnd, rect) -> None
        
        Draw the background of the tab region of a ribbon bar.
        """

    def GetTabCtrlHeight(self, dc: wx.DC, wnd: wx.Window, pages: RibbonPageTabInfoArray) -> int:
        """
        GetTabCtrlHeight(dc, wnd, pages) -> int
        
        Calculate the height (in pixels) of the tab region of a ribbon bar.
        """

    def GetBarTabWidth(self, dc: wx.DC, wnd: wx.Window, label: str, bitmap: wx.Bitmap, ideal: int, small_begin_need_separator: int, small_must_have_separator: int, minimum: int) -> None:
        """
        GetBarTabWidth(dc, wnd, label, bitmap, ideal, small_begin_need_separator, small_must_have_separator, minimum) -> None
        
        Calculate the ideal and minimum width (in pixels) of a tab in a ribbon
        bar.
        """

    def DrawTab(self, dc: wx.DC, wnd: wx.Window, tab: RibbonPageTabInfo) -> None:
        """
        DrawTab(dc, wnd, tab) -> None
        
        Draw a single tab in the tab region of a ribbon bar.
        """

    def DrawTabSeparator(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, visibility: float) -> None:
        """
        DrawTabSeparator(dc, wnd, rect, visibility) -> None
        
        Draw a separator between two tabs in a ribbon bar.
        """

    def DrawPageBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawPageBackground(dc, wnd, rect) -> None
        
        Draw the background of a ribbon page.
        """

    def DrawPanelBackground(self, dc: wx.DC, wnd: RibbonPanel, rect: wx.Rect) -> None:
        """
        DrawPanelBackground(dc, wnd, rect) -> None
        
        Draw the background and chrome for a ribbon panel.
        """

    def DrawMinimisedPanel(self, dc: wx.DC, wnd: RibbonPanel, rect: wx.Rect, bitmap: wx.Bitmap) -> None:
        """
        DrawMinimisedPanel(dc, wnd, rect, bitmap) -> None
        
        Draw a minimised ribbon panel.
        """

    def DrawGalleryBackground(self, dc: wx.DC, wnd: RibbonGallery, rect: wx.Rect) -> None:
        """
        DrawGalleryBackground(dc, wnd, rect) -> None
        
        Draw the background and chrome for a wxRibbonGallery control.
        """

    def DrawGalleryItemBackground(self, dc: wx.DC, wnd: RibbonGallery, rect: wx.Rect, item: RibbonGalleryItem) -> None:
        """
        DrawGalleryItemBackground(dc, wnd, rect, item) -> None
        
        Draw the background of a single item in a wxRibbonGallery control.
        """

    def DrawButtonBarBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawButtonBarBackground(dc, wnd, rect) -> None
        
        Draw the background for a wxRibbonButtonBar control.
        """

    def DrawButtonBarButton(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, kind: RibbonButtonKind, state: int, label: str, bitmap_large: wx.Bitmap, bitmap_small: wx.Bitmap) -> None:
        """
        DrawButtonBarButton(dc, wnd, rect, kind, state, label, bitmap_large, bitmap_small) -> None
        
        Draw a single button for a wxRibbonButtonBar control.
        """

    def DrawToolBarBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawToolBarBackground(dc, wnd, rect) -> None
        
        Draw the background for a wxRibbonToolBar control.
        """

    def DrawToolGroupBackground(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect) -> None:
        """
        DrawToolGroupBackground(dc, wnd, rect) -> None
        
        Draw the background for a group of tools on a wxRibbonToolBar control.
        """

    def DrawTool(self, dc: wx.DC, wnd: wx.Window, rect: wx.Rect, bitmap: wx.Bitmap, kind: RibbonButtonKind, state: int) -> None:
        """
        DrawTool(dc, wnd, rect, bitmap, kind, state) -> None
        
        Draw a single tool (for a wxRibbonToolBar control).
        """
# end of class RibbonAUIArtProvider


if 'wxMSW' in wx.PlatformInfo:
    RibbonDefaultArtProvider = RibbonMSWArtProvider
else:
    RibbonDefaultArtProvider = RibbonAUIArtProvider
#-- end-ribbon_art --#
#-- begin-ribbon_buttonbar --#

class _RibbonButtonBarButtonState(IntEnum):
    RIBBON_BUTTONBAR_BUTTON_SMALL = auto()
    RIBBON_BUTTONBAR_BUTTON_MEDIUM = auto()
    RIBBON_BUTTONBAR_BUTTON_LARGE = auto()
    RIBBON_BUTTONBAR_BUTTON_SIZE_MASK = auto()
    RIBBON_BUTTONBAR_BUTTON_NORMAL_HOVERED = auto()
    RIBBON_BUTTONBAR_BUTTON_DROPDOWN_HOVERED = auto()
    RIBBON_BUTTONBAR_BUTTON_HOVER_MASK = auto()
    RIBBON_BUTTONBAR_BUTTON_NORMAL_ACTIVE = auto()
    RIBBON_BUTTONBAR_BUTTON_DROPDOWN_ACTIVE = auto()
    RIBBON_BUTTONBAR_BUTTON_ACTIVE_MASK = auto()
    RIBBON_BUTTONBAR_BUTTON_DISABLED = auto()
    RIBBON_BUTTONBAR_BUTTON_TOGGLED = auto()
    RIBBON_BUTTONBAR_BUTTON_STATE_MASK = auto()
RibbonButtonBarButtonState: TypeAlias = Union[_RibbonButtonBarButtonState, int]
RIBBON_BUTTONBAR_BUTTON_SMALL = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_SMALL
RIBBON_BUTTONBAR_BUTTON_MEDIUM = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_MEDIUM
RIBBON_BUTTONBAR_BUTTON_LARGE = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_LARGE
RIBBON_BUTTONBAR_BUTTON_SIZE_MASK = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_SIZE_MASK
RIBBON_BUTTONBAR_BUTTON_NORMAL_HOVERED = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_NORMAL_HOVERED
RIBBON_BUTTONBAR_BUTTON_DROPDOWN_HOVERED = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_DROPDOWN_HOVERED
RIBBON_BUTTONBAR_BUTTON_HOVER_MASK = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_HOVER_MASK
RIBBON_BUTTONBAR_BUTTON_NORMAL_ACTIVE = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_NORMAL_ACTIVE
RIBBON_BUTTONBAR_BUTTON_DROPDOWN_ACTIVE = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_DROPDOWN_ACTIVE
RIBBON_BUTTONBAR_BUTTON_ACTIVE_MASK = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_ACTIVE_MASK
RIBBON_BUTTONBAR_BUTTON_DISABLED = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_DISABLED
RIBBON_BUTTONBAR_BUTTON_TOGGLED = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_TOGGLED
RIBBON_BUTTONBAR_BUTTON_STATE_MASK = _RibbonButtonBarButtonState.RIBBON_BUTTONBAR_BUTTON_STATE_MASK
wxEVT_RIBBONBUTTONBAR_CLICKED: int
wxEVT_RIBBONBUTTONBAR_DROPDOWN_CLICKED: int

class RibbonButtonBar(RibbonControl):
    """
    RibbonButtonBar() -> None
    RibbonButtonBar(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
    
    A ribbon button bar is similar to a traditional toolbar.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RibbonButtonBar() -> None
        RibbonButtonBar(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
        
        A ribbon button bar is similar to a traditional toolbar.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> bool
        
        Create a button bar in two-step button bar construction.
        """

    @overload
    def AddButton(self, button_id: int, label: str, bitmap: wx.Bitmap, bitmap_small: wx.Bitmap=wx.NullBitmap, bitmap_disabled: wx.Bitmap=wx.NullBitmap, bitmap_small_disabled: wx.Bitmap=wx.NullBitmap, kind: RibbonButtonKind=RIBBON_BUTTON_NORMAL, help_string: str='') -> RibbonButtonBarButtonBase:
        ...

    @overload
    def AddButton(self, button_id: int, label: str, bitmap: wx.Bitmap, help_string: str, kind: RibbonButtonKind=RIBBON_BUTTON_NORMAL) -> RibbonButtonBarButtonBase:
        """
        AddButton(button_id, label, bitmap, help_string, kind=RIBBON_BUTTON_NORMAL) -> RibbonButtonBarButtonBase
        AddButton(button_id, label, bitmap, bitmap_small=wx.NullBitmap, bitmap_disabled=wx.NullBitmap, bitmap_small_disabled=wx.NullBitmap, kind=RIBBON_BUTTON_NORMAL, help_string='') -> RibbonButtonBarButtonBase
        
        Add a button to the button bar (simple version).
        """

    def AddDropdownButton(self, button_id: int, label: str, bitmap: wx.Bitmap, help_string: str='') -> RibbonButtonBarButtonBase:
        """
        AddDropdownButton(button_id, label, bitmap, help_string='') -> RibbonButtonBarButtonBase
        
        Add a dropdown button to the button bar (simple version).
        """

    def AddHybridButton(self, button_id: int, label: str, bitmap: wx.Bitmap, help_string: str='') -> RibbonButtonBarButtonBase:
        """
        AddHybridButton(button_id, label, bitmap, help_string='') -> RibbonButtonBarButtonBase
        
        Add a hybrid button to the button bar (simple version).
        """

    def AddToggleButton(self, button_id: int, label: str, bitmap: wx.Bitmap, help_string: str='') -> RibbonButtonBarButtonBase:
        """
        AddToggleButton(button_id, label, bitmap, help_string='') -> RibbonButtonBarButtonBase
        
        Add a toggle button to the button bar (simple version).
        """

    @overload
    def InsertButton(self, pos: int, button_id: int, label: str, bitmap: wx.Bitmap, bitmap_small: wx.Bitmap=wx.NullBitmap, bitmap_disabled: wx.Bitmap=wx.NullBitmap, bitmap_small_disabled: wx.Bitmap=wx.NullBitmap, kind: RibbonButtonKind=RIBBON_BUTTON_NORMAL, help_string: str='') -> RibbonButtonBarButtonBase:
        ...

    @overload
    def InsertButton(self, pos: int, button_id: int, label: str, bitmap: wx.Bitmap, help_string: str, kind: RibbonButtonKind=RIBBON_BUTTON_NORMAL) -> RibbonButtonBarButtonBase:
        """
        InsertButton(pos, button_id, label, bitmap, help_string, kind=RIBBON_BUTTON_NORMAL) -> RibbonButtonBarButtonBase
        InsertButton(pos, button_id, label, bitmap, bitmap_small=wx.NullBitmap, bitmap_disabled=wx.NullBitmap, bitmap_small_disabled=wx.NullBitmap, kind=RIBBON_BUTTON_NORMAL, help_string='') -> RibbonButtonBarButtonBase
        
        Inserts a button to the button bar (simple version) at the given
        position.
        """

    def InsertDropdownButton(self, pos: int, button_id: int, label: str, bitmap: wx.Bitmap, help_string: str='') -> RibbonButtonBarButtonBase:
        """
        InsertDropdownButton(pos, button_id, label, bitmap, help_string='') -> RibbonButtonBarButtonBase
        
        Inserts a dropdown button to the button bar (simple version) at the
        given position.
        """

    def InsertHybridButton(self, pos: int, button_id: int, label: str, bitmap: wx.Bitmap, help_string: str='') -> RibbonButtonBarButtonBase:
        """
        InsertHybridButton(pos, button_id, label, bitmap, help_string='') -> RibbonButtonBarButtonBase
        
        Inserts a hybrid button to the button bar (simple version) at the
        given position.
        """

    def InsertToggleButton(self, pos: int, button_id: int, label: str, bitmap: wx.Bitmap, help_string: str='') -> RibbonButtonBarButtonBase:
        """
        InsertToggleButton(pos, button_id, label, bitmap, help_string='') -> RibbonButtonBarButtonBase
        
        Inserts a toggle button to the button bar (simple version) at the
        given position.
        """

    def GetButtonCount(self) -> int:
        """
        GetButtonCount() -> int
        
        Returns the number of buttons in this button bar.
        """

    def SetItemClientData(self, item: RibbonButtonBarButtonBase, data: ClientData) -> None:
        """
        SetItemClientData(item, data) -> None
        
        Set the client object associated with a button.
        """

    def GetItemClientData(self, item: RibbonButtonBarButtonBase) -> ClientData:
        """
        GetItemClientData(item) -> ClientData
        
        Get the client object associated with a button.
        """

    def GetItem(self, n: int) -> RibbonButtonBarButtonBase:
        """
        GetItem(n) -> RibbonButtonBarButtonBase
        
        Returns the N-th button of the bar.
        """

    def GetItemById(self, id: int) -> RibbonButtonBarButtonBase:
        """
        GetItemById(id) -> RibbonButtonBarButtonBase
        
        Returns the first button having a given id or NULL if none matches.
        """

    def GetItemId(self, item: RibbonButtonBarButtonBase) -> int:
        """
        GetItemId(item) -> int
        
        Returns the id of a button.
        """

    def GetItemRect(self, button_id: int) -> wx.Rect:
        """
        GetItemRect(button_id) -> wx.Rect
        
        Returns the items's rect with coordinates relative to the button bar's
        parent, or a default-constructed rect if the tool is not found.
        """

    def Realize(self) -> bool:
        """
        Realize() -> bool
        
        Calculate button layouts and positions.
        """

    def ClearButtons(self) -> None:
        """
        ClearButtons() -> None
        
        Delete all buttons from the button bar.
        """

    def DeleteButton(self, button_id: int) -> bool:
        """
        DeleteButton(button_id) -> bool
        
        Delete a single button from the button bar.
        """

    def EnableButton(self, button_id: int, enable: bool=True) -> None:
        """
        EnableButton(button_id, enable=True) -> None
        
        Enable or disable a single button on the bar.
        """

    def ToggleButton(self, button_id: int, checked: bool) -> None:
        """
        ToggleButton(button_id, checked) -> None
        
        Set a toggle button to the checked or unchecked state.
        """

    def SetButtonIcon(self, button_id: int, bitmap: wx.Bitmap, bitmap_small: wx.Bitmap=wx.NullBitmap, bitmap_disabled: wx.Bitmap=wx.NullBitmap, bitmap_small_disabled: wx.Bitmap=wx.NullBitmap) -> None:
        """
        SetButtonIcon(button_id, bitmap, bitmap_small=wx.NullBitmap, bitmap_disabled=wx.NullBitmap, bitmap_small_disabled=wx.NullBitmap) -> None
        
        Changes the bitmap of an existing button.
        """

    def SetButtonText(self, button_id: int, label: str) -> None:
        """
        SetButtonText(button_id, label) -> None
        
        Changes the label text of an existing button.
        """

    @overload
    def SetButtonTextMinWidth(self, button_id: int, label: str) -> None:
        ...

    @overload
    def SetButtonTextMinWidth(self, button_id: int, min_width_medium: int, min_width_large: int) -> None:
        """
        SetButtonTextMinWidth(button_id, min_width_medium, min_width_large) -> None
        SetButtonTextMinWidth(button_id, label) -> None
        
        Sets the minimum width of the button label, to indicate to the
        wxRibbonArtProvider layout mechanism that this is the minimum required
        size.
        """

    def SetButtonMinSizeClass(self, button_id: int, min_size_class: RibbonButtonBarButtonState) -> None:
        """
        SetButtonMinSizeClass(button_id, min_size_class) -> None
        
        Sets the minimum size class of a ribbon button.
        """

    def SetButtonMaxSizeClass(self, button_id: int, max_size_class: RibbonButtonBarButtonState) -> None:
        """
        SetButtonMaxSizeClass(button_id, max_size_class) -> None
        
        Sets the maximum size class of a ribbon button.
        """

    def GetActiveItem(self) -> RibbonButtonBarButtonBase:
        """
        GetActiveItem() -> RibbonButtonBarButtonBase
        
        Returns the active item of the button bar or NULL if there is none.
        """

    def GetHoveredItem(self) -> RibbonButtonBarButtonBase:
        """
        GetHoveredItem() -> RibbonButtonBarButtonBase
        
        Returns the hovered item of the button bar or NULL if there is none.
        """

    def SetShowToolTipsForDisabled(self, show: bool) -> None:
        """
        SetShowToolTipsForDisabled(show) -> None
        
        Indicates whether tooltips are shown for disabled buttons.
        """

    def GetShowToolTipsForDisabled(self) -> bool:
        """
        GetShowToolTipsForDisabled() -> bool
        
        Sets whether tooltips should be shown for disabled buttons or not.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ActiveItem(self) -> RibbonButtonBarButtonBase: ...
    @property
    def ButtonCount(self) -> int: ...
    @property
    def HoveredItem(self) -> RibbonButtonBarButtonBase: ...
    @property
    def ShowToolTipsForDisabled(self) -> bool: ...
    @ShowToolTipsForDisabled.setter
    def ShowToolTipsForDisabled(self, value: bool, /) -> None: ...
# end of class RibbonButtonBar


class RibbonButtonBarEvent(wx.CommandEvent):
    """
    RibbonButtonBarEvent(command_type=wx.wxEVT_NULL, win_id=0, bar=None, button=None) -> None
    
    Event used to indicate various actions relating to a button on a
    wxRibbonButtonBar.
    """

    def __init__(self, command_type: EventType=wx.wxEVT_NULL, win_id: int=0, bar: Optional[RibbonButtonBar]=None, button: Optional[RibbonButtonBarButtonBase]=None) -> None:
        """
        RibbonButtonBarEvent(command_type=wx.wxEVT_NULL, win_id=0, bar=None, button=None) -> None
        
        Event used to indicate various actions relating to a button on a
        wxRibbonButtonBar.
        """

    def GetBar(self) -> RibbonButtonBar:
        """
        GetBar() -> RibbonButtonBar
        
        Returns the bar which contains the button which the event relates to.
        """

    def SetBar(self, bar: RibbonButtonBar) -> None:
        """
        SetBar(bar) -> None
        
        Sets the button bar relating to this event.
        """

    def GetButton(self) -> RibbonButtonBarButtonBase:
        """
        GetButton() -> RibbonButtonBarButtonBase
        
        Returns the button which the event relates to.
        """

    def SetButton(self, bar: RibbonButtonBarButtonBase) -> None:
        """
        SetButton(bar) -> None
        
        Sets the button relating to this event.
        """

    def PopupMenu(self, menu: wx.Menu) -> bool:
        """
        PopupMenu(menu) -> bool
        
        Display a popup menu as a result of this (dropdown clicked) event.
        """
    @property
    def Bar(self) -> RibbonButtonBar: ...
    @Bar.setter
    def Bar(self, value: RibbonButtonBar, /) -> None: ...
    @property
    def Button(self) -> RibbonButtonBarButtonBase: ...
    @Button.setter
    def Button(self, value: RibbonButtonBarButtonBase, /) -> None: ...
# end of class RibbonButtonBarEvent


EVT_RIBBONBUTTONBAR_CLICKED = wx.PyEventBinder( wxEVT_RIBBONBUTTONBAR_CLICKED, 1 )
EVT_RIBBONBUTTONBAR_DROPDOWN_CLICKED = wx.PyEventBinder( wxEVT_RIBBONBUTTONBAR_DROPDOWN_CLICKED, 1 )
#-- end-ribbon_buttonbar --#
#-- begin-ribbon_gallery --#

class _RibbonGalleryButtonState(IntEnum):
    RIBBON_GALLERY_BUTTON_NORMAL = auto()
    RIBBON_GALLERY_BUTTON_HOVERED = auto()
    RIBBON_GALLERY_BUTTON_ACTIVE = auto()
    RIBBON_GALLERY_BUTTON_DISABLED = auto()
RibbonGalleryButtonState: TypeAlias = Union[_RibbonGalleryButtonState, int]
RIBBON_GALLERY_BUTTON_NORMAL = _RibbonGalleryButtonState.RIBBON_GALLERY_BUTTON_NORMAL
RIBBON_GALLERY_BUTTON_HOVERED = _RibbonGalleryButtonState.RIBBON_GALLERY_BUTTON_HOVERED
RIBBON_GALLERY_BUTTON_ACTIVE = _RibbonGalleryButtonState.RIBBON_GALLERY_BUTTON_ACTIVE
RIBBON_GALLERY_BUTTON_DISABLED = _RibbonGalleryButtonState.RIBBON_GALLERY_BUTTON_DISABLED
wxEVT_RIBBONGALLERY_HOVER_CHANGED: int
wxEVT_RIBBONGALLERY_SELECTED: int
wxEVT_RIBBONGALLERY_CLICKED: int

class RibbonGallery(RibbonControl):
    """
    RibbonGallery() -> None
    RibbonGallery(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
    
    A ribbon gallery is like a wxListBox, but for bitmaps rather than
    strings.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RibbonGallery() -> None
        RibbonGallery(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
        
        A ribbon gallery is like a wxListBox, but for bitmaps rather than
        strings.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> bool
        
        Create a gallery in two-step gallery construction.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Remove all items from the gallery.
        """

    def IsEmpty(self) -> bool:
        """
        IsEmpty() -> bool
        
        Query if the gallery has no items in it.
        """

    def GetCount(self) -> int:
        """
        GetCount() -> int
        
        Get the number of items in the gallery.
        """

    def GetItem(self, n: int) -> RibbonGalleryItem:
        """
        GetItem(n) -> RibbonGalleryItem
        
        Get an item by index.
        """

    @overload
    def Append(self, bitmap: wx.Bitmap, id: int, clientData: ClientData) -> RibbonGalleryItem:
        ...

    @overload
    def Append(self, bitmap: wx.Bitmap, id: int) -> RibbonGalleryItem:
        """
        Append(bitmap, id) -> RibbonGalleryItem
        Append(bitmap, id, clientData) -> RibbonGalleryItem
        
        Add an item to the gallery (with no client data).
        """

    def SetItemClientData(self, item: RibbonGalleryItem, data: ClientData) -> None:
        """
        SetItemClientData(item, data) -> None
        
        Set the client object associated with a gallery item.
        """

    def GetItemClientData(self, item: RibbonGalleryItem) -> ClientData:
        """
        GetItemClientData(item) -> ClientData
        
        Get the client object associated with a gallery item.
        """

    def SetSelection(self, item: RibbonGalleryItem) -> None:
        """
        SetSelection(item) -> None
        
        Set the selection to the given item, or removes the selection if item
        == NULL.
        """

    def GetSelection(self) -> RibbonGalleryItem:
        """
        GetSelection() -> RibbonGalleryItem
        
        Get the currently selected item, or NULL if there is none.
        """

    def GetHoveredItem(self) -> RibbonGalleryItem:
        """
        GetHoveredItem() -> RibbonGalleryItem
        
        Get the currently hovered item, or NULL if there is none.
        """

    def GetActiveItem(self) -> RibbonGalleryItem:
        """
        GetActiveItem() -> RibbonGalleryItem
        
        Get the currently active item, or NULL if there is none.
        """

    def GetUpButtonState(self) -> RibbonGalleryButtonState:
        """
        GetUpButtonState() -> RibbonGalleryButtonState
        
        Get the state of the scroll up button.
        """

    def GetDownButtonState(self) -> RibbonGalleryButtonState:
        """
        GetDownButtonState() -> RibbonGalleryButtonState
        
        Get the state of the scroll down button.
        """

    def GetExtensionButtonState(self) -> RibbonGalleryButtonState:
        """
        GetExtensionButtonState() -> RibbonGalleryButtonState
        
        Get the state of the "extension" button.
        """

    def IsHovered(self) -> bool:
        """
        IsHovered() -> bool
        
        Query is the mouse is currently hovered over the gallery.
        """

    def ScrollLines(self, lines: int) -> bool:
        """
        ScrollLines(lines) -> bool
        
        Scroll the gallery contents by some amount.
        """

    def ScrollPixels(self, pixels: int) -> bool:
        """
        ScrollPixels(pixels) -> bool
        
        Scroll the gallery contents by some fine-grained amount.
        """

    def EnsureVisible(self, item: RibbonGalleryItem) -> None:
        """
        EnsureVisible(item) -> None
        
        Scroll the gallery to ensure that the given item is visible.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ActiveItem(self) -> RibbonGalleryItem: ...
    @property
    def Count(self) -> int: ...
    @property
    def DownButtonState(self) -> RibbonGalleryButtonState: ...
    @property
    def ExtensionButtonState(self) -> RibbonGalleryButtonState: ...
    @property
    def HoveredItem(self) -> RibbonGalleryItem: ...
    @property
    def Selection(self) -> RibbonGalleryItem: ...
    @Selection.setter
    def Selection(self, value: RibbonGalleryItem, /) -> None: ...
    @property
    def UpButtonState(self) -> RibbonGalleryButtonState: ...
# end of class RibbonGallery


class RibbonGalleryEvent(wx.CommandEvent):
    """
    RibbonGalleryEvent(command_type=wx.wxEVT_NULL, win_id=0, gallery=None, item=None) -> None
    """

    def __init__(self, command_type: EventType=wx.wxEVT_NULL, win_id: int=0, gallery: Optional[RibbonGallery]=None, item: Optional[RibbonGalleryItem]=None) -> None:
        """
        RibbonGalleryEvent(command_type=wx.wxEVT_NULL, win_id=0, gallery=None, item=None) -> None
        """

    def GetGallery(self) -> RibbonGallery:
        """
        GetGallery() -> RibbonGallery
        
        Returns the gallery which the event relates to.
        """

    def GetGalleryItem(self) -> RibbonGalleryItem:
        """
        GetGalleryItem() -> RibbonGalleryItem
        
        Returns the gallery item which the event relates to, or NULL if it
        does not relate to an item.
        """

    def SetGallery(self, gallery: RibbonGallery) -> None:
        """
        SetGallery(gallery) -> None
        
        Sets the gallery relating to this event.
        """

    def SetGalleryItem(self, item: RibbonGalleryItem) -> None:
        """
        SetGalleryItem(item) -> None
        
        Sets the gallery item relating to this event.
        """
    @property
    def Gallery(self) -> RibbonGallery: ...
    @Gallery.setter
    def Gallery(self, value: RibbonGallery, /) -> None: ...
    @property
    def GalleryItem(self) -> RibbonGalleryItem: ...
    @GalleryItem.setter
    def GalleryItem(self, value: RibbonGalleryItem, /) -> None: ...
# end of class RibbonGalleryEvent


EVT_RIBBONGALLERY_HOVER_CHANGED = wx.PyEventBinder( wxEVT_RIBBONGALLERY_HOVER_CHANGED, 1 )
EVT_RIBBONGALLERY_SELECTED = wx.PyEventBinder( wxEVT_RIBBONGALLERY_SELECTED, 1 )
EVT_RIBBONGALLERY_CLICKED = wx.PyEventBinder( wxEVT_RIBBONGALLERY_CLICKED, 1 )
#-- end-ribbon_gallery --#
#-- begin-ribbon_toolbar --#
wxEVT_RIBBONTOOLBAR_CLICKED: int
wxEVT_RIBBONTOOLBAR_DROPDOWN_CLICKED: int

class RibbonToolBar(RibbonControl):
    """
    RibbonToolBar() -> None
    RibbonToolBar(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
    
    A ribbon tool bar is similar to a traditional toolbar which has no
    labels.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        RibbonToolBar() -> None
        RibbonToolBar(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> None
        
        A ribbon tool bar is similar to a traditional toolbar which has no
        labels.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0) -> bool
        
        Create a tool bar in two-step tool bar construction.
        """

    @overload
    def AddTool(self, tool_id: int, bitmap: wx.Bitmap, bitmap_disabled: wx.Bitmap=wx.NullBitmap, help_string: str='', kind: RibbonButtonKind=RIBBON_BUTTON_NORMAL, clientData: Optional[PyUserData]=None) -> RibbonToolBarToolBase:
        ...

    @overload
    def AddTool(self, tool_id: int, bitmap: wx.Bitmap, help_string: str, kind: RibbonButtonKind=RIBBON_BUTTON_NORMAL) -> RibbonToolBarToolBase:
        """
        AddTool(tool_id, bitmap, help_string, kind=RIBBON_BUTTON_NORMAL) -> RibbonToolBarToolBase
        AddTool(tool_id, bitmap, bitmap_disabled=wx.NullBitmap, help_string='', kind=RIBBON_BUTTON_NORMAL, clientData=None) -> RibbonToolBarToolBase
        
        Add a tool to the tool bar (simple version).
        """

    def AddDropdownTool(self, tool_id: int, bitmap: wx.Bitmap, help_string: str='') -> RibbonToolBarToolBase:
        """
        AddDropdownTool(tool_id, bitmap, help_string='') -> RibbonToolBarToolBase
        
        Add a dropdown tool to the tool bar (simple version).
        """

    def AddHybridTool(self, tool_id: int, bitmap: wx.Bitmap, help_string: str='') -> RibbonToolBarToolBase:
        """
        AddHybridTool(tool_id, bitmap, help_string='') -> RibbonToolBarToolBase
        
        Add a hybrid tool to the tool bar (simple version).
        """

    def AddToggleTool(self, tool_id: int, bitmap: wx.Bitmap, help_string: str) -> RibbonToolBarToolBase:
        """
        AddToggleTool(tool_id, bitmap, help_string) -> RibbonToolBarToolBase
        
        Add a toggle tool to the tool bar (simple version).
        """

    def AddSeparator(self) -> RibbonToolBarToolBase:
        """
        AddSeparator() -> RibbonToolBarToolBase
        
        Add a separator to the tool bar.
        """

    @overload
    def InsertTool(self, pos: int, tool_id: int, bitmap: wx.Bitmap, bitmap_disabled: wx.Bitmap=wx.NullBitmap, help_string: str='', kind: RibbonButtonKind=RIBBON_BUTTON_NORMAL, clientData: Optional[PyUserData]=None) -> RibbonToolBarToolBase:
        ...

    @overload
    def InsertTool(self, pos: int, tool_id: int, bitmap: wx.Bitmap, help_string: str, kind: RibbonButtonKind=RIBBON_BUTTON_NORMAL) -> RibbonToolBarToolBase:
        """
        InsertTool(pos, tool_id, bitmap, help_string, kind=RIBBON_BUTTON_NORMAL) -> RibbonToolBarToolBase
        InsertTool(pos, tool_id, bitmap, bitmap_disabled=wx.NullBitmap, help_string='', kind=RIBBON_BUTTON_NORMAL, clientData=None) -> RibbonToolBarToolBase
        
        Insert a tool to the tool bar (simple version) as the specified
        position.
        """

    def InsertDropdownTool(self, pos: int, tool_id: int, bitmap: wx.Bitmap, help_string: str='') -> RibbonToolBarToolBase:
        """
        InsertDropdownTool(pos, tool_id, bitmap, help_string='') -> RibbonToolBarToolBase
        
        Insert a dropdown tool to the tool bar (simple version) as the
        specified position.
        """

    def InsertHybridTool(self, pos: int, tool_id: int, bitmap: wx.Bitmap, help_string: str='') -> RibbonToolBarToolBase:
        """
        InsertHybridTool(pos, tool_id, bitmap, help_string='') -> RibbonToolBarToolBase
        
        Insert a hybrid tool to the tool bar (simple version) as the specified
        position.
        """

    def InsertToggleTool(self, pos: int, tool_id: int, bitmap: wx.Bitmap, help_string: str='') -> RibbonToolBarToolBase:
        """
        InsertToggleTool(pos, tool_id, bitmap, help_string='') -> RibbonToolBarToolBase
        
        Insert a toggle tool to the tool bar (simple version) as the specified
        position.
        """

    def InsertSeparator(self, pos: int) -> RibbonToolBarToolBase:
        """
        InsertSeparator(pos) -> RibbonToolBarToolBase
        
        Insert a separator to the tool bar at the specified position.
        """

    def ClearTools(self) -> None:
        """
        ClearTools() -> None
        
        Deletes all the tools in the toolbar.
        """

    def DeleteTool(self, tool_id: int) -> bool:
        """
        DeleteTool(tool_id) -> bool
        
        Removes the specified tool from the toolbar and deletes it.
        """

    def DeleteToolByPos(self, pos: int) -> bool:
        """
        DeleteToolByPos(pos) -> bool
        
        This function behaves like DeleteTool() but it deletes the tool at the
        specified position and not the one with the given id.
        """

    def FindById(self, tool_id: int) -> RibbonToolBarToolBase:
        """
        FindById(tool_id) -> RibbonToolBarToolBase
        
        Returns a pointer to the tool opaque structure by id or NULL if no
        corresponding tool is found.
        """

    @overload
    def GetToolByPos(self, x: int, y: int) -> RibbonToolBarToolBase:
        ...

    @overload
    def GetToolByPos(self, pos: int) -> RibbonToolBarToolBase:
        """
        GetToolByPos(pos) -> RibbonToolBarToolBase
        GetToolByPos(x, y) -> RibbonToolBarToolBase
        
        Return the opaque pointer corresponding to the given tool.
        """

    def GetToolCount(self) -> int:
        """
        GetToolCount() -> int
        
        Returns the number of tools in the toolbar.
        """

    def GetToolId(self, tool: RibbonToolBarToolBase) -> int:
        """
        GetToolId(tool) -> int
        
        Return the id associated to the tool opaque structure.
        """

    def GetActiveTool(self) -> RibbonToolBarToolBase:
        """
        GetActiveTool() -> RibbonToolBarToolBase
        
        Returns the active item of the tool bar or NULL if there is none.
        """

    def GetToolClientData(self, tool_id: int) -> PyUserData:
        """
        GetToolClientData(tool_id) -> PyUserData
        
        Get any client data associated with the tool.
        """

    def GetToolEnabled(self, tool_id: int) -> bool:
        """
        GetToolEnabled(tool_id) -> bool
        
        Called to determine whether a tool is enabled (responds to user
        input).
        """

    def GetToolHelpString(self, tool_id: int) -> str:
        """
        GetToolHelpString(tool_id) -> str
        
        Returns the help string for the given tool.
        """

    def GetToolKind(self, tool_id: int) -> RibbonButtonKind:
        """
        GetToolKind(tool_id) -> RibbonButtonKind
        
        Return the kind of the given tool.
        """

    def GetToolPos(self, tool_id: int) -> int:
        """
        GetToolPos(tool_id) -> int
        
        Returns the tool position in the toolbar, or wxNOT_FOUND if the tool
        is not found.
        """

    def GetToolRect(self, tool_id: int) -> wx.Rect:
        """
        GetToolRect(tool_id) -> wx.Rect
        
        Returns the tool's rect with coordinates relative to the toolbar's
        parent, or a default-constructed rect if the tool is not found.
        """

    def GetToolState(self, tool_id: int) -> bool:
        """
        GetToolState(tool_id) -> bool
        
        Gets the on/off state of a toggle tool.
        """

    def Realize(self) -> bool:
        """
        Realize() -> bool
        
        Calculate tool layouts and positions.
        """

    def SetRows(self, nMin: int, nMax: int=-1) -> None:
        """
        SetRows(nMin, nMax=-1) -> None
        
        Set the number of rows to distribute tool groups over.
        """

    def SetToolClientData(self, tool_id: int, clientData: PyUserData) -> None:
        """
        SetToolClientData(tool_id, clientData) -> None
        
        Sets the client data associated with the tool.
        """

    def SetToolDisabledBitmap(self, tool_id: int, bitmap: wx.Bitmap) -> None:
        """
        SetToolDisabledBitmap(tool_id, bitmap) -> None
        
        Sets the bitmap to be used by the tool with the given ID when the tool
        is in a disabled state.
        """

    def SetToolHelpString(self, tool_id: int, helpString: str) -> None:
        """
        SetToolHelpString(tool_id, helpString) -> None
        
        Sets the help string shown in tooltip for the given tool.
        """

    def SetToolNormalBitmap(self, tool_id: int, bitmap: wx.Bitmap) -> None:
        """
        SetToolNormalBitmap(tool_id, bitmap) -> None
        
        Sets the bitmap to be used by the tool with the given ID.
        """

    def EnableTool(self, tool_id: int, enable: bool=True) -> None:
        """
        EnableTool(tool_id, enable=True) -> None
        
        Enable or disable a single tool on the bar.
        """

    def ToggleTool(self, tool_id: int, checked: bool) -> None:
        """
        ToggleTool(tool_id, checked) -> None
        
        Set a toggle tool to the checked or unchecked state.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ActiveTool(self) -> RibbonToolBarToolBase: ...
    @property
    def ToolCount(self) -> int: ...
# end of class RibbonToolBar


class RibbonToolBarEvent(wx.CommandEvent):
    """
    RibbonToolBarEvent(command_type=wx.wxEVT_NULL, win_id=0, bar=None) -> None
    """

    def __init__(self, command_type: EventType=wx.wxEVT_NULL, win_id: int=0, bar: Optional[RibbonToolBar]=None) -> None:
        """
        RibbonToolBarEvent(command_type=wx.wxEVT_NULL, win_id=0, bar=None) -> None
        """

    def GetBar(self) -> RibbonToolBar:
        """
        GetBar() -> RibbonToolBar
        """

    def SetBar(self, bar: RibbonToolBar) -> None:
        """
        SetBar(bar) -> None
        """

    def PopupMenu(self, menu: wx.Menu) -> bool:
        """
        PopupMenu(menu) -> bool
        """
    @property
    def Bar(self) -> RibbonToolBar: ...
    @Bar.setter
    def Bar(self, value: RibbonToolBar, /) -> None: ...
# end of class RibbonToolBarEvent


EVT_RIBBONTOOLBAR_CLICKED = wx.PyEventBinder( wxEVT_RIBBONTOOLBAR_CLICKED, 1 )
EVT_RIBBONTOOLBAR_DROPDOWN_CLICKED = wx.PyEventBinder( wxEVT_RIBBONTOOLBAR_DROPDOWN_CLICKED, 1 )
#-- end-ribbon_toolbar --#
