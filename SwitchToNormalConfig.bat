@echo off
echo Switching back to normal capture settings...

echo # Database Configuration > config.sys
echo host=localhost >> config.sys
echo user=root >> config.sys
echo password=sa >> config.sys
echo database=finger >> config.sys
echo. >> config.sys
echo # API Configuration >> config.sys
echo api_port=9000 >> config.sys
echo web_demo_port=8080 >> config.sys
echo. >> config.sys
echo # Application Settings >> config.sys
echo app_name=Fingerprint Bridge Service >> config.sys
echo. >> config.sys
echo # Fingerprint Capture Settings >> config.sys
echo capture_timeout=30 >> config.sys
echo lfd_enabled=false >> config.sys
echo lfd_level=1 >> config.sys
echo dry_level=5 >> config.sys
echo quality_threshold=25 >> config.sys

echo Configuration restored to normal! Please restart the application.
echo.
pause
