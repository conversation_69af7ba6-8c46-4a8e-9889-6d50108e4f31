// qqmlengine.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQmlImageProviderBase /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qqmlengine.h>
%End

public:
    enum ImageType
    {
        Image,
        Pixmap,
        Texture,
%If (Qt_5_6_0 -)
        ImageResponse,
%End
    };

    enum Flag
    {
        ForceAsynchronousImageLoading,
    };

    typedef QFlags<QQmlImageProviderBase::Flag> Flags;
    virtual ~QQmlImageProviderBase();
    virtual QQmlImageProviderBase::ImageType imageType() const = 0;
    virtual QQmlImageProviderBase::Flags flags() const = 0;

private:
    QQmlImageProviderBase();
};

QFlags<QQmlImageProviderBase::Flag> operator|(QQmlImageProviderBase::Flag f1, QFlags<QQmlImageProviderBase::Flag> f2);

class QQmlEngine : public QJSEngine
{
%TypeHeaderCode
#include <qqmlengine.h>
%End

public:
%If (Qt_5_6_1 -)
    explicit QQmlEngine(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QQmlEngine(QObject *parent /TransferThis/ = 0);
%End
    virtual ~QQmlEngine();
    QQmlContext *rootContext() const;
    void clearComponentCache();
    void trimComponentCache();
    QStringList importPathList() const;
    void setImportPathList(const QStringList &paths);
    void addImportPath(const QString &dir);
    QStringList pluginPathList() const;
    void setPluginPathList(const QStringList &paths);
    void addPluginPath(const QString &dir);
    bool addNamedBundle(const QString &name, const QString &fileName);
    bool importPlugin(const QString &filePath, const QString &uri, QList<QQmlError> *errors /GetWrapper/);
%MethodCode
        int orig_size = (a2 ? a2->size() : 0);
        
        sipRes = sipCpp->importPlugin(*a0, *a1, a2);
        
        if (a2)
        {
            for (int i = a2->size(); i > orig_size; --i)
            {
                QQmlError *new_error = new QQmlError(a2->at(i - orig_size - 1));
                PyObject *new_error_obj = sipConvertFromNewType(new_error, sipType_QQmlError, 0);
                
                if (!new_error_obj)
                {
                    delete new_error;
                    sipError = sipErrorFail;
                    break;
                }
                
                if (PyList_Insert(a2Wrapper, 0, new_error_obj) < 0)
                {
                    Py_DECREF(new_error_obj);
                    sipError = sipErrorFail;
                    break;
                }
                
                Py_DECREF(new_error_obj);
            }
        }
%End

    void setNetworkAccessManagerFactory(QQmlNetworkAccessManagerFactory * /KeepReference/);
    QQmlNetworkAccessManagerFactory *networkAccessManagerFactory() const;
    QNetworkAccessManager *networkAccessManager() const;
    void addImageProvider(const QString &id, QQmlImageProviderBase * /Transfer/);
    QQmlImageProviderBase *imageProvider(const QString &id) const;
    void removeImageProvider(const QString &id);
    void setIncubationController(QQmlIncubationController * /KeepReference/);
    QQmlIncubationController *incubationController() const;
    void setOfflineStoragePath(const QString &dir);
    QString offlineStoragePath() const;
    QUrl baseUrl() const;
    void setBaseUrl(const QUrl &);
    bool outputWarningsToStandardError() const;
    void setOutputWarningsToStandardError(bool);
    static QQmlContext *contextForObject(const QObject *);
    static void setContextForObject(QObject *, QQmlContext *);

    enum ObjectOwnership
    {
        CppOwnership,
        JavaScriptOwnership,
    };

    static void setObjectOwnership(QObject * /GetWrapper/, QQmlEngine::ObjectOwnership);
%MethodCode
        QQmlEngine::ObjectOwnership old = QQmlEngine::objectOwnership(a0);
        
        QQmlEngine::setObjectOwnership(a0, a1);
        
        if (old != a1 && !a0->parent())
        {
            if (old == QQmlEngine::CppOwnership)
                sipTransferTo(a0Wrapper, Py_None);
            else
                sipTransferBack(a0Wrapper);
        }
%End

    static QQmlEngine::ObjectOwnership objectOwnership(QObject *);

protected:
    virtual bool event(QEvent *);

signals:
    void quit();
    void warnings(const QList<QQmlError> &warnings);
%If (Qt_5_8_0 -)
    void exit(int retCode);
%End

public:
%If (Qt_5_9_0 -)
    QString offlineStorageDatabaseFilePath(const QString &databaseName) const;
%End

public slots:
%If (Qt_5_10_0 -)
    void retranslate();
%End

public:
%If (Qt_5_12_0 -)
    SIP_PYOBJECT singletonInstance(int qmlTypeId) /TypeHint="QObject"/;
%MethodCode
        QJSValue instance = sipCpp->singletonInstance<QJSValue>(a0);
        
        if (instance.isQObject())
        {
            sipRes = sipConvertFromType(instance.toQObject(), sipType_QObject, NULL);
                
            if (!sipRes)
                sipError = sipErrorFail;
        }
        else
        {
            sipRes = Py_None;
            Py_INCREF(sipRes);
        }
%End

%End
};
