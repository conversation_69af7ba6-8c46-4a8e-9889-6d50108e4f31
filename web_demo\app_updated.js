/**
 * MultipleFinger Bridge Web Demo - JavaScript
 * Updated to work with Python REST API (port 5000)
 */

document.addEventListener("DOMContentLoaded", function () {
  // Updated API endpoint for Python REST API
  const API_BASE_URL = "http://localhost:5001/api";

  console.log("MultipleFinger Web Demo starting...");
  console.log("API URL:", API_BASE_URL);

  // --- API Helper Functions ---
  async function apiRequest(endpoint, method = "GET", data = null) {
    try {
      const options = { 
        method, 
        headers: { 
          "Content-Type": "application/json",
          "Accept": "application/json"
        } 
      };
      
      if (data) {
        options.body = JSON.stringify(data);
      }
      
      console.log(`API Request: ${method} ${API_BASE_URL}/${endpoint}`, data);
      
      const response = await fetch(`${API_BASE_URL}/${endpoint}`, options);
      const result = await response.json();
      
      console.log(`API Response:`, result);
      
      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`);
      }
      
      return result;
    } catch (error) {
      console.error("API Request failed:", error);
      throw error;
    }
  }

  // --- Status and Health Check ---
  async function checkApiHealth() {
    try {
      const health = await apiRequest("health");
      const status = await apiRequest("status");
      
      updateStatusDisplay(health.data, status.data);
      return true;
    } catch (error) {
      updateStatusDisplay(null, null, error.message);
      return false;
    }
  }

  function updateStatusDisplay(healthData, statusData, error = null) {
    const statusElement = document.getElementById("apiStatus");
    const deviceElement = document.getElementById("deviceStatus");

    if (error) {
      if (statusElement) {
        statusElement.textContent = "❌ API Error: " + error;
        statusElement.style.color = "red";
      }
      if (deviceElement) {
        deviceElement.textContent = "❌ Disconnected";
        deviceElement.style.color = "red";
      }
      return;
    }

    if (statusElement && healthData) {
      const tcpStatus = healthData.tcp_connection === "connected" ? "✅" : "❌";
      statusElement.textContent = tcpStatus + " API: " + healthData.api_status;
      statusElement.style.color = healthData.tcp_connection === "connected" ? "green" : "red";
    }

    if (deviceElement && statusData) {
      const deviceStatus = statusData.device_connected ? "✅ Connected" : "❌ Disconnected";
      deviceElement.textContent = deviceStatus;
      deviceElement.style.color = statusData.device_connected ? "green" : "red";
    }
  }

  // --- Fingerprint Operations ---
  async function captureFingerprint(fingerPosition, operationType = "flat") {
    try {
      setMessage("Capturing fingerprint...", "info");
      
      const captureData = {
        finger_position: fingerPosition,
        operation_type: operationType,
        timeout: 30,
        save_image: true
      };
      
      const result = await apiRequest("fingerprint/capture", "POST", captureData);
      
      if (result.success) {
        setMessage(`Fingerprint captured successfully for position ${fingerPosition}`, "success");
        return result.data;
      } else {
        throw new Error(result.error || "Capture failed");
      }
    } catch (error) {
      setMessage(`Capture failed: ${error.message}`, "error");
      throw error;
    }
  }

  async function identifyFingerprint(templateData, threshold = 70) {
    try {
      setMessage("Identifying fingerprint...", "info");
      
      const identifyData = {
        template_data: templateData,
        threshold: threshold
      };
      
      const result = await apiRequest("fingerprint/identify", "POST", identifyData);
      
      if (result.success) {
        const matches = result.data.matches || [];
        if (matches.length > 0) {
          setMessage(`Found ${matches.length} match(es)`, "success");
          displayIdentificationResults(matches);
        } else {
          setMessage("No matches found", "info");
        }
        return result.data;
      } else {
        throw new Error(result.error || "Identification failed");
      }
    } catch (error) {
      setMessage(`Identification failed: ${error.message}`, "error");
      throw error;
    }
  }

  async function enrollFingerprint(userId, fingerPosition, templateData, imageData = "", imageQuality = 0) {
    try {
      setMessage("Enrolling fingerprint...", "info");
      
      const enrollData = {
        user_id: userId,
        finger_position: fingerPosition,
        template_data: templateData,
        image_data: imageData,
        image_quality: imageQuality
      };
      
      const result = await apiRequest("fingerprint/enroll", "POST", enrollData);
      
      if (result.success) {
        setMessage(`Fingerprint enrolled successfully for user: ${userId}`, "success");
        return result.data;
      } else {
        throw new Error(result.error || "Enrollment failed");
      }
    } catch (error) {
      setMessage(`Enrollment failed: ${error.message}`, "error");
      throw error;
    }
  }

  async function getCapturedData() {
    try {
      setMessage("Getting captured data...", "info");
      
      const result = await apiRequest("fingerprint/captured-data");
      
      if (result.success) {
        const capturedData = result.data.captured_data || [];
        setMessage(`Retrieved ${capturedData.length} captured fingerprint(s)`, "success");
        displayCapturedData(capturedData);
        return capturedData;
      } else {
        throw new Error(result.error || "Failed to get captured data");
      }
    } catch (error) {
      setMessage(`Get captured data failed: ${error.message}`, "error");
      throw error;
    }
  }

  async function getFingerPositions() {
    try {
      const result = await apiRequest("fingerprint/positions");
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error || "Failed to get finger positions");
      }
    } catch (error) {
      console.error("Failed to get finger positions:", error);
      return null;
    }
  }

  // --- UI Helper Functions ---
  function setMessage(message, type = "info") {
    const messageElement = document.getElementById("messageArea");
    if (messageElement) {
      // Clear previous content
      messageElement.textContent = "";

      // Create message div
      const messageDiv = document.createElement("div");
      messageDiv.className = "message " + (type === "error" ? "error" : type === "success" ? "success" : "info");
      messageDiv.textContent = message;

      messageElement.appendChild(messageDiv);

      // Auto-clear info messages after 5 seconds
      if (type === "info") {
        setTimeout(() => {
          if (messageElement.contains(messageDiv)) {
            messageElement.removeChild(messageDiv);
          }
        }, 5000);
      }
    }
    console.log(type.toUpperCase() + ": " + message);
  }

  function displayIdentificationResults(matches) {
    const resultsElement = document.getElementById("identificationResults");
    if (!resultsElement) return;

    // Clear previous results
    resultsElement.textContent = "";

    // Create title
    const title = document.createElement("h3");
    title.textContent = "Identification Results";
    resultsElement.appendChild(title);

    // Add each match
    matches.forEach(match => {
      const resultDiv = document.createElement("div");
      resultDiv.className = "result-item";

      const userIdP = document.createElement("p");
      userIdP.innerHTML = "<strong>User ID:</strong> " + match.user_id;

      const scoreP = document.createElement("p");
      scoreP.innerHTML = "<strong>Score:</strong> " + match.score;

      const positionP = document.createElement("p");
      positionP.innerHTML = "<strong>Position:</strong> " + (match.finger_position_name || match.finger_position);

      const timeP = document.createElement("p");
      timeP.innerHTML = "<strong>Match Time:</strong> " + (match.match_time || "N/A");

      resultDiv.appendChild(userIdP);
      resultDiv.appendChild(scoreP);
      resultDiv.appendChild(positionP);
      resultDiv.appendChild(timeP);
      resultsElement.appendChild(resultDiv);
    });
  }

  function displayCapturedData(capturedData) {
    const dataElement = document.getElementById("capturedDataDisplay");
    if (!dataElement) return;

    // Clear previous data
    dataElement.textContent = "";

    // Create title
    const title = document.createElement("h3");
    title.textContent = "Captured Data";
    dataElement.appendChild(title);

    // Add each captured item
    capturedData.forEach(data => {
      const capturedDiv = document.createElement("div");
      capturedDiv.className = "captured-item";

      const positionP = document.createElement("p");
      positionP.innerHTML = "<strong>Position:</strong> " + (data.finger_position_name || data.finger_position);

      const qualityP = document.createElement("p");
      qualityP.innerHTML = "<strong>Quality:</strong> " + (data.quality || "N/A");

      const timeP = document.createElement("p");
      timeP.innerHTML = "<strong>Capture Time:</strong> " + (data.capture_time || "N/A");

      capturedDiv.appendChild(positionP);
      capturedDiv.appendChild(qualityP);
      capturedDiv.appendChild(timeP);
      dataElement.appendChild(capturedDiv);
    });
  }

  // --- Event Handlers ---
  function setupEventHandlers() {
    // Health check button
    const healthCheckBtn = document.getElementById("healthCheckBtn");
    if (healthCheckBtn) {
      healthCheckBtn.addEventListener("click", checkApiHealth);
    }

    // Capture buttons
    const captureButtons = document.querySelectorAll("[data-capture-position]");
    captureButtons.forEach(button => {
      button.addEventListener("click", async (e) => {
        const position = parseInt(e.target.dataset.capturePosition);
        const operationType = e.target.dataset.operationType || "flat";
        
        try {
          await captureFingerprint(position, operationType);
        } catch (error) {
          console.error("Capture failed:", error);
        }
      });
    });

    // Get captured data button
    const getCapturedBtn = document.getElementById("getCapturedBtn");
    if (getCapturedBtn) {
      getCapturedBtn.addEventListener("click", getCapturedData);
    }

    // Enroll form
    const enrollForm = document.getElementById("enrollForm");
    if (enrollForm) {
      enrollForm.addEventListener("submit", async (e) => {
        e.preventDefault();
        const formData = new FormData(enrollForm);
        const userId = formData.get("userId");
        const fingerPosition = parseInt(formData.get("fingerPosition"));
        const templateData = formData.get("templateData");
        
        if (!userId || !fingerPosition || !templateData) {
          setMessage("Please fill in all required fields", "error");
          return;
        }
        
        try {
          await enrollFingerprint(userId, fingerPosition, templateData);
        } catch (error) {
          console.error("Enrollment failed:", error);
        }
      });
    }

    // Identify form
    const identifyForm = document.getElementById("identifyForm");
    if (identifyForm) {
      identifyForm.addEventListener("submit", async (e) => {
        e.preventDefault();
        const formData = new FormData(identifyForm);
        const templateData = formData.get("templateData");
        const threshold = parseInt(formData.get("threshold")) || 70;
        
        if (!templateData) {
          setMessage("Please provide template data", "error");
          return;
        }
        
        try {
          await identifyFingerprint(templateData, threshold);
        } catch (error) {
          console.error("Identification failed:", error);
        }
      });
    }
  }

  // --- Initialization ---
  async function initialize() {
    console.log("Initializing MultipleFinger Web Demo...");
    
    // Setup event handlers
    setupEventHandlers();
    
    // Initial health check
    await checkApiHealth();
    
    // Load finger positions
    const positions = await getFingerPositions();
    if (positions) {
      console.log("Available finger positions:", positions);
    }
    
    setMessage("Web demo initialized. Ready for fingerprint operations.", "success");
  }

  // --- Global Functions (for inline event handlers) ---
  window.MultiFinger = {
    captureFingerprint,
    identifyFingerprint,
    enrollFingerprint,
    getCapturedData,
    checkApiHealth,
    setMessage
  };

  // Start initialization
  initialize().catch(error => {
    console.error("Initialization failed:", error);
    setMessage(`Initialization failed: ${error.message}`, "error");
  });
});
