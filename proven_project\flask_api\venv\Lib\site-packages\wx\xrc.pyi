# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
The classes in this module enable loading widgets and layout from XML.
"""
#-- begin-_xrc --#
import wx.xml

import wx
ID_NONE = wx.ID_NONE  # Needed for some parameter defaults in this module

class _XmlResourceFlags(IntFlag):
    XRC_USE_LOCALE = auto()
    XRC_NO_SUBCLASSING = auto()
    XRC_NO_RELOADING = auto()
    XRC_USE_ENVVARS = auto()
XmlResourceFlags: TypeAlias = Union[_XmlResourceFlags, int]
XRC_USE_LOCALE = _XmlResourceFlags.XRC_USE_LOCALE
XRC_NO_SUBCLASSING = _XmlResourceFlags.XRC_NO_SUBCLASSING
XRC_NO_RELOADING = _XmlResourceFlags.XRC_NO_RELOADING
XRC_USE_ENVVARS = _XmlResourceFlags.XRC_USE_ENVVARS

class XmlResource(Object):
    """
    XmlResource(filemask, flags=XRC_USE_LOCALE, domain='') -> None
    XmlResource(flags=XRC_USE_LOCALE, domain='') -> None
    
    This is the main class for interacting with the XML-based resource
    system.
    """

    @overload
    def __init__(self, flags: int=XRC_USE_LOCALE, domain: str='') -> None:
        ...

    @overload
    def __init__(self, filemask: str, flags: int=XRC_USE_LOCALE, domain: str='') -> None:
        """
        XmlResource(filemask, flags=XRC_USE_LOCALE, domain='') -> None
        XmlResource(flags=XRC_USE_LOCALE, domain='') -> None
        
        This is the main class for interacting with the XML-based resource
        system.
        """

    def AddHandler(self, handler: XmlResourceHandler) -> None:
        """
        AddHandler(handler) -> None
        
        Initializes only a specific handler (or custom handler).
        """

    def InsertHandler(self, handler: XmlResourceHandler) -> None:
        """
        InsertHandler(handler) -> None
        
        Add a new handler at the beginning of the handler list.
        """

    def AttachUnknownControl(self, name: str, control: Window, parent: Optional[Window]=None) -> bool:
        """
        AttachUnknownControl(name, control, parent=None) -> bool
        
        Attaches an unknown control to the given panel/window/dialog.
        """

    def ClearHandlers(self) -> None:
        """
        ClearHandlers() -> None
        
        Removes all handlers and deletes them (this means that any handlers
        added using AddHandler() must be allocated on the heap).
        """

    def CompareVersion(self, major: int, minor: int, release: int, revision: int) -> int:
        """
        CompareVersion(major, minor, release, revision) -> int
        
        Compares the XRC version to the argument.
        """

    def GetDomain(self) -> str:
        """
        GetDomain() -> str
        
        Returns the domain (message catalog) that will be used to load
        translatable strings in the XRC.
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        
        Returns flags, which may be a bitlist of wxXmlResourceFlags
        enumeration values.
        """

    def GetResourceNode(self, name: str) -> XmlNode:
        """
        GetResourceNode(name) -> XmlNode
        
        Returns the wxXmlNode containing the definition of the object with the
        given name or NULL.
        """

    def GetVersion(self) -> int:
        """
        GetVersion() -> int
        
        Returns version information (a.b.c.d = d + 256*c + 2562*b + 2563*a).
        """

    def InitAllHandlers(self) -> None:
        """
        InitAllHandlers() -> None
        
        Initializes handlers for all supported controls/windows.
        """

    def Load(self, filemask: str) -> bool:
        """
        Load(filemask) -> bool
        
        Loads resources from XML files that match given filemask.
        """

    def LoadDocument(self, doc: XmlDocument, name: str="") -> bool:
        """
        LoadDocument(doc, name="") -> bool
        
        Load resources from the XML document containing them.
        """

    def LoadFile(self, file: str) -> bool:
        """
        LoadFile(file) -> bool
        
        Simpler form of Load() for loading a single XRC file.
        """

    def LoadAllFiles(self, dirname: str) -> bool:
        """
        LoadAllFiles(dirname) -> bool
        
        Loads all .xrc files from directory dirname.
        """

    def LoadBitmap(self, name: str) -> Bitmap:
        """
        LoadBitmap(name) -> Bitmap
        
        Loads a bitmap resource from a file.
        """

    @overload
    def LoadDialog(self, dlg: Dialog, parent: Window, name: str) -> bool:
        ...

    @overload
    def LoadDialog(self, parent: Window, name: str) -> Dialog:
        """
        LoadDialog(parent, name) -> Dialog
        LoadDialog(dlg, parent, name) -> bool
        
        Loads a dialog.
        """

    @overload
    def LoadFrame(self, frame: Frame, parent: Window, name: str) -> bool:
        ...

    @overload
    def LoadFrame(self, parent: Window, name: str) -> Frame:
        """
        LoadFrame(parent, name) -> Frame
        LoadFrame(frame, parent, name) -> bool
        
        Loads a frame from the resource.
        """

    def LoadIcon(self, name: str) -> Icon:
        """
        LoadIcon(name) -> Icon
        
        Loads an icon resource from a file.
        """

    def LoadMenu(self, name: str) -> Menu:
        """
        LoadMenu(name) -> Menu
        
        Loads menu from resource.
        """

    @overload
    def LoadMenuBar(self, name: str) -> MenuBar:
        ...

    @overload
    def LoadMenuBar(self, parent: Window, name: str) -> MenuBar:
        """
        LoadMenuBar(parent, name) -> MenuBar
        LoadMenuBar(name) -> MenuBar
        
        Loads a menubar from resource.
        """

    @overload
    def LoadObject(self, instance: Object, parent: Window, name: str, classname: str) -> bool:
        ...

    @overload
    def LoadObject(self, parent: Window, name: str, classname: str) -> Object:
        """
        LoadObject(parent, name, classname) -> Object
        LoadObject(instance, parent, name, classname) -> bool
        
        Load an object from the resource specifying both the resource name and
        the class name.
        """

    @overload
    def LoadObjectRecursively(self, instance: Object, parent: Window, name: str, classname: str) -> bool:
        ...

    @overload
    def LoadObjectRecursively(self, parent: Window, name: str, classname: str) -> Object:
        """
        LoadObjectRecursively(parent, name, classname) -> Object
        LoadObjectRecursively(instance, parent, name, classname) -> bool
        
        Load an object from anywhere in the resource tree.
        """

    @overload
    def LoadPanel(self, panel: Panel, parent: Window, name: str) -> bool:
        ...

    @overload
    def LoadPanel(self, parent: Window, name: str) -> Panel:
        """
        LoadPanel(parent, name) -> Panel
        LoadPanel(panel, parent, name) -> bool
        
        Loads a panel.
        """

    def LoadToolBar(self, parent: Window, name: str) -> ToolBar:
        """
        LoadToolBar(parent, name) -> ToolBar
        
        Loads a toolbar.
        """

    def SetDomain(self, domain: str) -> None:
        """
        SetDomain(domain) -> None
        
        Sets the domain (message catalog) that will be used to load
        translatable strings in the XRC.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Sets flags (bitlist of wxXmlResourceFlags enumeration values).
        """

    def Unload(self, filename: str) -> bool:
        """
        Unload(filename) -> bool
        
        This function unloads a resource previously loaded by Load().
        """

    @staticmethod
    def AddSubclassFactory(factory: XmlSubclassFactory) -> None:
        """
        AddSubclassFactory(factory) -> None
        
        Registers subclasses factory for use in XRC.
        """

    @staticmethod
    def FindXRCIDById(numId: int) -> str:
        """
        FindXRCIDById(numId) -> str
        
        Returns a string ID corresponding to the given numeric ID.
        """

    @staticmethod
    def Get() -> XmlResource:
        """
        Get() -> XmlResource
        
        Gets the global resources object or creates one if none exists.
        """

    @staticmethod
    def GetXRCID(str_id: str, value_if_not_found: int=ID_NONE) -> int:
        """
        GetXRCID(str_id, value_if_not_found=ID_NONE) -> int
        
        Returns a numeric ID that is equivalent to the string ID used in an
        XML resource.
        """

    @staticmethod
    def Set(res: XmlResource) -> XmlResource:
        """
        Set(res) -> XmlResource
        
        Sets the global resources object and returns a pointer to the previous one (may be NULL).
        """

    def LoadFromBuffer(self, data: PyBuffer) -> bool:
        """
        LoadFromBuffer(data) -> bool
        
        Load the resource from a bytes string or other data buffer compatible
        object.
        """

    LoadFromString = wx.deprecated(LoadFromBuffer, 'Use LoadFromBuffer instead')
    @property
    def Domain(self) -> str: ...
    @Domain.setter
    def Domain(self, value: str, /) -> None: ...
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def Version(self) -> int: ...
# end of class XmlResource


class XmlResourceHandler(Object):
    """
    XmlResourceHandler() -> None
    
    wxSizerXmlHandler is a class for resource handlers capable of creating
    a wxSizer object from an XML node.
    """

    def __init__(self) -> None:
        """
        XmlResourceHandler() -> None
        
        wxSizerXmlHandler is a class for resource handlers capable of creating
        a wxSizer object from an XML node.
        """

    def CreateResource(self, node: XmlNode, parent: Object, instance: Object) -> Object:
        """
        CreateResource(node, parent, instance) -> Object
        
        Creates an object (menu, dialog, control, ...) from an XML node.
        """

    def DoCreateResource(self) -> Object:
        """
        DoCreateResource() -> Object
        
        Called from CreateResource after variables were filled.
        """

    def CanHandle(self, node: XmlNode) -> bool:
        """
        CanHandle(node) -> bool
        
        Returns true if it understands this node and can create a resource
        from it, false otherwise.
        """

    def SetParentResource(self, res: XmlResource) -> None:
        """
        SetParentResource(res) -> None
        
        Sets the parent resource.
        """
    @property
    def Animation(self) -> Animation: ...
    @property
    def Bitmap(self) -> Bitmap: ...
    @property
    def BitmapBundle(self) -> BitmapBundle: ...
    @property
    def Class(self) -> str: ...
    @property
    def CurFileSystem(self) -> FileSystem: ...
    @property
    def Font(self) -> Font: ...
    @property
    def ID(self) -> int: ...
    @property
    def Icon(self) -> Icon: ...
    @property
    def ImageList(self) -> ImageList: ...
    @property
    def Instance(self) -> Object: ...
    @property
    def Name(self) -> str: ...
    @property
    def Node(self) -> XmlNode: ...
    @property
    def Parent(self) -> Object: ...
    @property
    def ParentAsWindow(self) -> Window: ...
    @property
    def Position(self) -> Point: ...
    @property
    def Resource(self) -> XmlResource: ...
    @property
    def Size(self) -> Size: ...
    @property
    def Style(self) -> int: ...

    def AddStyle(self, name: str, value: int) -> None:
        """
        AddStyle(name, value) -> None
        
        Add a style flag (e.g.
        """

    def AddWindowStyles(self) -> None:
        """
        AddWindowStyles() -> None
        
        Add styles common to all wxWindow-derived classes.
        """

    def CreateChildren(self, parent: Object, this_hnd_only: bool=False) -> None:
        """
        CreateChildren(parent, this_hnd_only=False) -> None
        
        Creates children.
        """

    def CreateChildrenPrivately(self, parent: Object, rootnode: Optional[XmlNode]=None) -> None:
        """
        CreateChildrenPrivately(parent, rootnode=None) -> None
        
        Helper function.
        """

    def CreateResFromNode(self, node: XmlNode, parent: Object, instance: Optional[Object]=None) -> Object:
        """
        CreateResFromNode(node, parent, instance=None) -> Object
        
        Creates a resource from a node.
        """

    def GetAnimation(self, param: str="animation", ctrl: Optional[AnimationCtrl]=None) -> Animation:
        """
        GetAnimation(param="animation", ctrl=None) -> Animation
        
        Creates an animation (see wxAnimation) from the filename specified in
        param.
        """

    @overload
    def GetBitmap(self, node: XmlNode, defaultArtClient: ArtClient=ART_OTHER, size: Size=DefaultSize) -> Bitmap:
        ...

    @overload
    def GetBitmap(self, param: str="bitmap", defaultArtClient: ArtClient=ART_OTHER, size: Size=DefaultSize) -> Bitmap:
        """
        GetBitmap(param="bitmap", defaultArtClient=ART_OTHER, size=DefaultSize) -> Bitmap
        GetBitmap(node, defaultArtClient=ART_OTHER, size=DefaultSize) -> Bitmap
        
        Gets a bitmap.
        """

    @overload
    def GetBitmapBundle(self, node: XmlNode, defaultArtClient: ArtClient=ART_OTHER, size: Size=DefaultSize) -> BitmapBundle:
        ...

    @overload
    def GetBitmapBundle(self, param: str="bitmap", defaultArtClient: ArtClient=ART_OTHER, size: Size=DefaultSize) -> BitmapBundle:
        """
        GetBitmapBundle(param="bitmap", defaultArtClient=ART_OTHER, size=DefaultSize) -> BitmapBundle
        GetBitmapBundle(node, defaultArtClient=ART_OTHER, size=DefaultSize) -> BitmapBundle
        
        Gets a bitmap bundle.
        """

    def GetBool(self, param: str, defaultv: bool=False) -> bool:
        """
        GetBool(param, defaultv=False) -> bool
        
        Gets a bool flag (1, t, yes, on, true are true, everything else is
        false).
        """

    def GetColour(self, param: str, defaultColour: Colour=NullColour) -> Colour:
        """
        GetColour(param, defaultColour=NullColour) -> Colour
        
        Gets colour in HTML syntax (#RRGGBB).
        """

    def GetCurFileSystem(self) -> FileSystem:
        """
        GetCurFileSystem() -> FileSystem
        
        Returns the current file system.
        """

    def GetDimension(self, param: str, defaultv: int=0, windowToUse: Window=0) -> int:
        """
        GetDimension(param, defaultv=0, windowToUse=0) -> int
        
        Gets a dimension (may be in dialog units).
        """

    def GetDirection(self, param: str, dirDefault: Direction=LEFT) -> Direction:
        """
        GetDirection(param, dirDefault=LEFT) -> Direction
        
        Gets a direction.
        """

    def GetFont(self, param: str="font") -> Font:
        """
        GetFont(param="font") -> Font
        
        Gets a font.
        """

    def GetID(self) -> int:
        """
        GetID() -> int
        
        Returns the XRCID.
        """

    @overload
    def GetIcon(self, node: XmlNode, defaultArtClient: ArtClient=ART_OTHER, size: Size=DefaultSize) -> Icon:
        ...

    @overload
    def GetIcon(self, param: str="icon", defaultArtClient: ArtClient=ART_OTHER, size: Size=DefaultSize) -> Icon:
        """
        GetIcon(param="icon", defaultArtClient=ART_OTHER, size=DefaultSize) -> Icon
        GetIcon(node, defaultArtClient=ART_OTHER, size=DefaultSize) -> Icon
        
        Returns an icon.
        """

    def GetIconBundle(self, param: str, defaultArtClient: ArtClient=ART_OTHER) -> IconBundle:
        """
        GetIconBundle(param, defaultArtClient=ART_OTHER) -> IconBundle
        
        Returns an icon bundle.
        """

    def GetImageList(self, param: str="imagelist") -> ImageList:
        """
        GetImageList(param="imagelist") -> ImageList
        
        Creates an image list from the param markup data.
        """

    def GetLong(self, param: str, defaultv: int=0) -> int:
        """
        GetLong(param, defaultv=0) -> int
        
        Gets the integer value from the parameter.
        """

    def GetFloat(self, param: str, defaultv: float=0) -> float:
        """
        GetFloat(param, defaultv=0) -> float
        
        Gets a float value from the parameter.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns the resource name.
        """

    def IsObjectNode(self, node: XmlNode) -> bool:
        """
        IsObjectNode(node) -> bool
        
        Checks if the given node is an object node.
        """

    def GetNodeContent(self, node: XmlNode) -> str:
        """
        GetNodeContent(node) -> str
        
        Gets node content from wxXML_ENTITY_NODE.
        """

    def GetNodeParent(self, node: XmlNode) -> XmlNode:
        """
        GetNodeParent(node) -> XmlNode
        
        Gets the parent of the node given.
        """

    def GetNodeNext(self, node: XmlNode) -> XmlNode:
        """
        GetNodeNext(node) -> XmlNode
        
        Gets the next sibling node related to the given node, possibly NULL.
        """

    def GetNodeChildren(self, node: XmlNode) -> XmlNode:
        """
        GetNodeChildren(node) -> XmlNode
        
        Gets the first child of the given node or NULL.
        """

    def GetParamNode(self, param: str) -> XmlNode:
        """
        GetParamNode(param) -> XmlNode
        
        Finds the node or returns NULL.
        """

    @overload
    def GetParamValue(self, node: XmlNode) -> str:
        ...

    @overload
    def GetParamValue(self, param: str) -> str:
        """
        GetParamValue(param) -> str
        GetParamValue(node) -> str
        
        Finds the parameter value or returns the empty string.
        """

    def GetPosition(self, param: str="pos") -> Point:
        """
        GetPosition(param="pos") -> Point
        
        Gets the position (may be in dialog units).
        """

    def GetSize(self, param: str="size", windowToUse: Window=0) -> Size:
        """
        GetSize(param="size", windowToUse=0) -> Size
        
        Gets the size (may be in dialog units).
        """

    def GetStyle(self, param: str="style", defaults: int=0) -> int:
        """
        GetStyle(param="style", defaults=0) -> int
        
        Gets style flags from text in form "flag | flag2| flag3 |..." Only
        understands flags added with AddStyle().
        """

    def GetText(self, param: str, translate: bool=True) -> str:
        """
        GetText(param, translate=True) -> str
        
        Gets text from param and does some conversions:
        """

    def HasParam(self, param: str) -> bool:
        """
        HasParam(param) -> bool
        
        Check to see if a parameter exists.
        """

    def IsOfClass(self, node: XmlNode, classname: str) -> bool:
        """
        IsOfClass(node, classname) -> bool
        
        Convenience function.
        """

    def SetupWindow(self, wnd: Window) -> None:
        """
        SetupWindow(wnd) -> None
        
        Sets common window options.
        """

    @overload
    def ReportError(self, message: str) -> None:
        ...

    @overload
    def ReportError(self, context: XmlNode, message: str) -> None:
        """
        ReportError(context, message) -> None
        ReportError(message) -> None
        
        Reports error in XRC resources to the user.
        """

    def ReportParamError(self, param: str, message: str) -> None:
        """
        ReportParamError(param, message) -> None
        
        Like ReportError(wxXmlNode*, const wxString&), but uses the node of
        parameter param of the currently processed object as the context.
        """

    def GetResource(self) -> XmlResource:
        """
        GetResource() -> XmlResource
        
        After CreateResource has been called this will return the current
        wxXmlResource object.
        """

    def GetNode(self) -> XmlNode:
        """
        GetNode() -> XmlNode
        
        After CreateResource has been called this will return the XML node
        being processed.
        """

    def GetClass(self) -> str:
        """
        GetClass() -> str
        
        After CreateResource has been called this will return the class name
        of the XML resource node being processed.
        """

    def GetParent(self) -> Object:
        """
        GetParent() -> Object
        
        After CreateResource has been called this will return the current
        item's parent, if any.
        """

    def GetInstance(self) -> Object:
        """
        GetInstance() -> Object
        
        After CreateResource has been called this will return the instance
        that the XML resource content should be created upon, if it has
        already been created.
        """

    def GetParentAsWindow(self) -> Window:
        """
        GetParentAsWindow() -> Window
        
        After CreateResource has been called this will return the item's
        parent as a wxWindow.
        """
# end of class XmlResourceHandler


@wx.deprecated
def EmptyXmlResource(flags=XRC_USE_LOCALE, domain=""):
    """
    A compatibility wrapper for the XmlResource(flags, domain) constructor
    """
    pass

def XRCID(str_id, value_if_not_found=wx.ID_NONE):
    """
    Returns a numeric ID that is equivalent to the string ID used in an XML resource.
    """
    pass

def XRCCTRL(window, str_id, *ignoreargs):
    """
    Returns the child window associated with the string ID in an XML resource.
    """
    pass

class XmlSubclassFactory:
    """
    XmlSubclassFactory() -> None
    """

    def __init__(self) -> None:
        """
        XmlSubclassFactory() -> None
        """

    def Create(self, className: str) -> Object:
        """
        Create(className) -> Object
        """
# end of class XmlSubclassFactory


# Create a factory for handling the subclass property of XRC's
# object tag.  This factory will search for the specified
# package.module.class and will try to instantiate it for XRC's
# use.  The class must support instantiation with no parameters and
# delayed creation of the UI widget (aka 2-phase create).

def _my_import(name):
    try:
        mod = __import__(name)
    except ImportError:
        import traceback
        print(traceback.format_exc())
        raise
    components = name.split('.')
    for comp in components[1:]:
        mod = getattr(mod, comp)
    return mod

class XmlSubclassFactory_Python(XmlSubclassFactory):
    def __init__(self):
        XmlSubclassFactory.__init__(self)

    def Create(self, className):
        assert className.find('.') != -1, "Module name must be specified!"
        mname = className[:className.rfind('.')]
        cname = className[className.rfind('.')+1:]
        module = _my_import(mname)
        klass = getattr(module, cname)
        inst = klass()
        return inst

XmlResource.AddSubclassFactory(XmlSubclassFactory_Python())
#-- end-_xrc --#
