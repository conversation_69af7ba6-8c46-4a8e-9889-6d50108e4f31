﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace MultiFingerDemo
{
    public partial class IdentifyResult : Form
    {
        //private DataGridView songsDataGridView = new DataGridView();
        private Button addNewRowButton = new Button();
        private Button deleteRowButton = new Button();
        private Panel buttonPanel = new Panel();

        public delegate void TransferIdentifyCloseDelegate();
        public event TransferIdentifyCloseDelegate TransferIdentifyClosePre;

        public IdentifyResult()
        {
            InitializeComponent();
        }

        private void IdentifyResult_Load(object sender, EventArgs e)
        {
            SetupDataGridView();
        }


        private void SetupDataGridView()
        {
            this.Controls.Add(songsDataGridView);
            
            songsDataGridView.ColumnCount = 5;

            songsDataGridView.ColumnHeadersBorderStyle =
                DataGridViewHeaderBorderStyle.Single;
            songsDataGridView.CellBorderStyle = DataGridViewCellBorderStyle.Single;
            songsDataGridView.GridColor = Color.Black;
            songsDataGridView.RowHeadersVisible = false;
            songsDataGridView.AutoSizeColumnsMode =
                DataGridViewAutoSizeColumnsMode.Fill;

            songsDataGridView.Columns[0].Name = "Finger Name";
            songsDataGridView.Columns[1].Name = "User ID";
            songsDataGridView.Columns[2].Name = "User Name";
            songsDataGridView.Columns[3].Name = "Finger position";
            songsDataGridView.Columns[4].Name = "Score";
            for(int i =0;i<songsDataGridView.Columns.Count;i++)
            {
                songsDataGridView.Columns[i].SortMode = DataGridViewColumnSortMode.NotSortable;
            }

            songsDataGridView.RowsDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        }

        public void TransferResultPre(string[] rowData)
        {
            songsDataGridView.Rows.Add(rowData);

            /*
            string[] row0 = { "Right Thumb",         "", "", "", "" };
            string[] row1 = { "Right Index Finger",  "", "", "", "" };
            string[] row2 = { "Right Middle Finger", "", "", "", "" };
            string[] row3 = { "Right Ring Finger",   "", "", "", "" };
            string[] row4 = { "Right Little Finger", "", "", "", "" };
            string[] row5 = { "Left Thumb",          "", "", "", "" };
            string[] row6 = { "Left Index Finger",   "", "", "", "" };
            string[] row7 = { "Left Middle Finger",  "", "", "", "" };
            string[] row8 = { "Left Ring Finger",    "", "", "", "" };
            string[] row9 = { "Left Little Finger",  "", "", "", ""};

            songsDataGridView.Rows.Add(row0);
            songsDataGridView.Rows.Add(row1);
            songsDataGridView.Rows.Add(row2);
            songsDataGridView.Rows.Add(row3);
            songsDataGridView.Rows.Add(row4);
            songsDataGridView.Rows.Add(row5);
            songsDataGridView.Rows.Add(row6);
            songsDataGridView.Rows.Add(row7);
            songsDataGridView.Rows.Add(row8);
            songsDataGridView.Rows.Add(row9);
            */
        }

        private void IdentifyResult_FormClosed(object sender, FormClosedEventArgs e)
        {
            TransferIdentifyClosePre();
        }
    }
}
