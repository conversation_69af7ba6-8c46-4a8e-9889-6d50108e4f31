// qqmllist.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQmlListReference
{
%TypeHeaderCode
#include <qqmllist.h>
%End

public:
    QQmlListReference();
    QQmlListReference(QObject *, const char *property, QQmlEngine *engine = 0);
    QQmlListReference(const QQmlListReference &);
    ~QQmlListReference();
    bool isValid() const;
    QObject *object() const;
    const QMetaObject *listElementType() const;
    bool canAppend() const;
    bool canAt() const;
    bool canClear() const;
    bool canCount() const;
    bool isManipulable() const;
    bool isReadable() const;
    bool append(QObject *) const;
    QObject *at(int) const;
    bool clear() const;
    int count() const;
%If (Qt_5_15_0 -)
    bool canReplace() const;
%End
%If (Qt_5_15_0 -)
    bool canRemoveLast() const;
%End
%If (Qt_5_15_0 -)
    bool replace(int, QObject *) const;
%End
%If (Qt_5_15_0 -)
    bool removeLast() const;
%End
};
