// qquickwidget.sip generated by MetaSIP
//
// This file is part of the QtQuickWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_3_0 -)

class QQuickWidget : public QWidget
{
%TypeHeaderCode
#include <qquickwidget.h>
%End

%ConvertToSubClassCode
    sipType = (sipCpp->inherits(sipName_QQuickWidget) ? sipType_QQuickWidget : 0);
%End

public:
    explicit QQuickWidget(QWidget *parent /TransferThis/ = 0);
    QQuickWidget(QQmlEngine *engine, QWidget *parent /TransferThis/);
    QQuickWidget(const QUrl &source, QWidget *parent /TransferThis/ = 0);
    virtual ~QQuickWidget();
    QUrl source() const;
    QQmlEngine *engine() const;
    QQmlContext *rootContext() const;
    QQuickItem *rootObject() const;

    enum ResizeMode
    {
        SizeViewToRootObject,
        SizeRootObjectToView,
    };

    QQuickWidget::ResizeMode resizeMode() const;
    void setResizeMode(QQuickWidget::ResizeMode);

    enum Status
    {
        Null,
        Ready,
        Loading,
        Error,
    };

    QQuickWidget::Status status() const;
    QList<QQmlError> errors() const;
    virtual QSize sizeHint() const;
    QSize initialSize() const;
    void setFormat(const QSurfaceFormat &format);
    QSurfaceFormat format() const;

public slots:
    void setSource(const QUrl &) /ReleaseGIL/;

signals:
    void statusChanged(QQuickWidget::Status);
    void sceneGraphError(QQuickWindow::SceneGraphError error, const QString &message);

protected:
    virtual void resizeEvent(QResizeEvent *);
    virtual void timerEvent(QTimerEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void keyReleaseEvent(QKeyEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void mouseDoubleClickEvent(QMouseEvent *);
    virtual void showEvent(QShowEvent *);
    virtual void hideEvent(QHideEvent *);
    virtual void wheelEvent(QWheelEvent *);
    virtual bool event(QEvent *);
%If (Qt_5_3_1 -)
    virtual void focusInEvent(QFocusEvent *event);
%End
%If (Qt_5_3_1 -)
    virtual void focusOutEvent(QFocusEvent *event);
%End
%If (Qt_5_5_0 -)
    virtual void dragEnterEvent(QDragEnterEvent *);
%End
%If (Qt_5_5_0 -)
    virtual void dragMoveEvent(QDragMoveEvent *);
%End
%If (Qt_5_5_0 -)
    virtual void dragLeaveEvent(QDragLeaveEvent *);
%End
%If (Qt_5_5_0 -)
    virtual void dropEvent(QDropEvent *);
%End
%If (Qt_5_8_0 -)
    virtual void paintEvent(QPaintEvent *event);
%End

public:
%If (Qt_5_4_0 -)
    QImage grabFramebuffer() const;
%End
%If (Qt_5_4_0 -)
    void setClearColor(const QColor &color);
%End
%If (Qt_5_5_0 -)
    QQuickWindow *quickWindow() const;
%End

protected:
%If (Qt_5_11_0 -)
    virtual bool focusNextPrevChild(bool next);
%End
};

%End
