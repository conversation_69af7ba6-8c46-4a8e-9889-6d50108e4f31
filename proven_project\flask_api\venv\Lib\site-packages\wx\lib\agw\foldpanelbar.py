# --------------------------------------------------------------------------- #
# FOLDPANELBAR wxPython IMPLEMENTATION
# Ported From <PERSON><PERSON> & <PERSON> (Extended Demo) C++ Code By:
#
# <PERSON>, @ 23 Mar 2005
# Latest Revision: 16 Jul 2012, 15.00 GMT
#
#
# TODO List
#
# All The C++ TODOs Are Still Alive. I Am Not Able to Read <PERSON><PERSON>'s Mind
# So I Don't Really Know What Will Be The New Features/Additions He Will
# Make On His Code. At The Moment They Are:
#
# 1. OnPaint Function In CaptionBar Class:
# TODO: Maybe First A Memory Dc Should Draw All, And Then Paint It On The
# Caption. This Way A Flickering Arrow During Resize Is Not Visible.
#
# 2. OnChar Function In CaptionBar Class:
# TODO: This Is Easy To Do But I Don't Have Any Useful Idea On Which Kind
# Of Features To Add. Does Anyone Have An Intelligent Idea?
#
# 3. AddFoldPanelWindow Function In FoldPanelBar Class:
# TODO: Take Old And New Heights, And If Difference, Reposition All The
# Lower Panels. This Is Because The User Can Add New wxWindow Controls
# Somewhere In Between When Other Panels Are Already Present.
# Don't Know What It Means. Probably Is My Poor English...
#
# 4. OnSizePanel Function In FoldPanelBar Class:
# TODO: A Smart Way To Check Whether The Old - New Width Of The
# Panel Changed, If So No Need To Resize The Fold Panel Items
#
#
# DONE List:
#
# 1. Implemented Styles Like FPB_SINGLE_FOLD and FPB_EXCLUSIVE_FOLD
# Thanks To E. A. Tacao For His Nice Suggestions.
#
# 2. Added Some Maquillage To FoldPanelBar: When The Mouse Enters The Icon
# Region, It Is Changed To wx.CURSOR_HAND.
#
#
# For The Original TODO List From Jorgen, Please Refer To:
# http://www.solidsteel.nl/jorg/components/foldpanel/wxFoldPanelBar.php#todo_list
#
#
#
# For All Kind Of Problems, Requests Of Enhancements And Bug Reports, Please
# Write To Me At:
#
# <EMAIL>
# <EMAIL>
#
# Or, Obviously, To The wxPython Mailing List!!!
#
# Tags:        phoenix-port, unittest, documented, py3-port
#
# End Of Comments
# --------------------------------------------------------------------------- #


"""
:class:`~wx.lib.agw.foldpanelbar.FoldPanelBar` is a control that contains multiple panels, which can be expanded
or collapsed.


Description
===========

The :class:`FoldPanelBar` is a control that contains multiple panels (of type
:class:`FoldPanelItem`) that can be expanded or collapsed. The captionbar of
the :class:`FoldPanelBar` can be customized by setting it to a horizontal gradient
style, vertical gradient style, a single colour, a rectangle or filled
rectangle. The `FoldPanel` items can be collapsed in place or to the
bottom of the control. :class:`wx.Window` derived controls can be added
dynamically, and separated by separator lines.


How does it work
----------------

The internals of the :class:`FoldPanelBar` is a list of :class:`FoldPanelItem` objects. Through
the reference of `FoldPanel` these panels can be controlled by adding new controls
to a FoldPanel or adding new FoldPanels to the :class:`FoldPanelBar`.

The :class:`CaptionBar` fires events to the parent (container of all panel items) when a
sub-panel needs resizing (either folding or expanding). The fold or expand process
is simply a resize of the panel so it looks like all controls on it are gone. All
controls are still child of the `FoldPanel` they are located on. If they don't
handle the event (and they won't) then the owner of the :class:`FoldPanelBar` gets the
events.

This is what you need to handle the controls. There isn't much to it just
a lot of calculations to see what panel belongs where. There are no sizers
involved in the panels, everything is purely x-y positioning.


What can it do and what not?
----------------------------

a) What it can do:

   * Run-time addition of panels (no deletion just yet);
   * Run time addition of controls to the panel (it will be resized accordingly);
   * Creating panels in collapsed mode or expanded mode;
   * Various modes of caption behaviour and filling to make it more appealing;
   * Panels can be folded and collapsed (or all of them) to allow more space.

b) What it cannot do:

   * Selection of a panel like in a listctrl;
   * Dragging and dropping the panels;
   * Re-ordering the panels (not yet).


Usage
=====

Usage example::

    import wx
    import wx.lib.agw.foldpanelbar as fpb

    class MyFrame(wx.Frame):

        def __init__(self, parent):

            wx.Frame.__init__(self, parent, -1, "FoldPanelBar Demo")

            text_ctrl = wx.TextCtrl(self, -1, size=(400, 100), style=wx.TE_MULTILINE)

            panel_bar = fpb.FoldPanelBar(self, -1, agwStyle=fpb.FPB_VERTICAL)

            fold_panel = panel_bar.AddFoldPanel("Thing")
            thing = wx.TextCtrl(fold_panel, -1, size=(400, -1), style=wx.TE_MULTILINE)

            panel_bar.AddFoldPanelWindow(fold_panel, thing)

            main_sizer = wx.BoxSizer(wx.VERTICAL)
            main_sizer.Add(text_ctrl, 1, wx.EXPAND)
            main_sizer.Add(panel_bar, 1, wx.EXPAND)
            self.SetSizer(main_sizer)


    # our normal wxApp-derived class, as usual

    app = wx.App(0)

    frame = MyFrame(None)
    app.SetTopWindow(frame)
    frame.Show()

    app.MainLoop()



Supported Platforms
===================

:class:`FoldPanelBar` is supported on the following platforms:
  * Windows (Verified on Windows XP, 2000)
  * Linux/Unix (GTK2) (Thanks to Toni Brkic and Robin Dunn)
  * Mac OSX (Thanks to Robin Dunn for the :class:`CaptionBar` size patch)


Window Styles
=============

This class supports the following window styles:

========================== =========== ==================================================
Window Styles              Hex Value   Description
========================== =========== ==================================================
``FPB_SINGLE_FOLD``                0x1 Single fold forces other panels to close when they are open, and only opens the current panel. This will allow the open panel to gain the full size left in the client area.
``FPB_COLLAPSE_TO_BOTTOM``         0x2 All panels are stacked to the bottom. When they are expanded again they show up at the top.
``FPB_EXCLUSIVE_FOLD``             0x4 ``FPB_SINGLE_FOLD`` style plus the panels will be stacked at the bottom.
``FPB_HORIZONTAL``                 0x8 :class:`FoldPanelBar` will be horizontal.
``FPB_VERTICAL``                  0x10 :class:`FoldPanelBar` will be vertical.
========================== =========== ==================================================


Events Processing
=================

This class processes the following events:

================== ==================================================
Event Name         Description
================== ==================================================
``EVT_CAPTIONBAR`` The user has pressed the caption bar: :class:`FoldPanelBar` will either expand or collapse the underlying panel.
================== ==================================================


License And Version
===================

:class:`FoldPanelBar` is distributed under the wxPython license.

Latest Revision: Andrea Gavana @ 16 Jul 2012, 15.00 GMT

Version 0.6

"""

import wx

#----------------------------------------------------------------------
# Collapsed And Expanded Bitmap Images
# Created With img2py.py
#----------------------------------------------------------------------
from wx.lib.embeddedimage import PyEmbeddedImage

CollapsedIcon = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAADdJ"
    "REFUOI1jZGRiZqAEMFGke/Ab8P/f3/8D5wKY7YRcQRsXoNuKzxXUdwEu23CJU+wCxtG8wAAA"
    "mvUb+vltJD8AAAAASUVORK5CYII=")

ExpandedIcon = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAEJJ"
    "REFUOI1jZGRiZqAEMFGke1AYwIIu8P/f3/+4FDMyMTNS3QUYBmCzBZ84bQIR3TZcttPOBci2"
    "4rOdKi5gHM0LDACevARXGc9htQAAAABJRU5ErkJggg==")

#----------------------------------------------------------------------
# FOLDPANELBAR Starts Here
#----------------------------------------------------------------------

# CAPTIONBAR STYLES
#
#- CAPTIONBAR_GRADIENT_V: Draws a vertical gradient from top to bottom
#- CAPTIONBAR_GRADIENT_H: Draws a horizontal gradient from left to right
#- CAPTIONBAR_SINGLE: Draws a single filled rectangle to draw the caption
#- CAPTIONBAR_RECTANGLE: Draws a single colour with a rectangle around the caption
#- CAPTIONBAR_FILLED_RECTANGLE: Draws a filled rectangle and a border around it

CAPTIONBAR_NOSTYLE            = 0
""" The :class:`CaptionBar` has no style bit set. """
CAPTIONBAR_GRADIENT_V         = 1
""" Draws a vertical gradient from top to bottom. """
CAPTIONBAR_GRADIENT_H         = 2
""" Draws a vertical gradient from left to right. """
CAPTIONBAR_SINGLE             = 3
""" Draws a single filled rectangle to draw the caption. """
CAPTIONBAR_RECTANGLE          = 4
""" Draws a single colour with a rectangle around the caption. """
CAPTIONBAR_FILLED_RECTANGLE   = 5
""" Draws a filled rectangle and a border around it. """

FPB_EXTRA_X = 10
""" Extra horizontal padding, in pixels. """
FPB_EXTRA_Y = 4
""" Extra vertical padding, in pixels. """

# pixels of the bmp to be aligned from the right filled with space
FPB_BMP_RIGHTSPACE = 2
""" Pixels of the bmp to be aligned from the right filled with space. """

# Now supported! Single fold forces
# other panels to close when they are open, and only opens the current panel.
# This will allow the open panel to gain the full size left in the client area
FPB_SINGLE_FOLD = 0x0001
""" Single fold forces other panels to close when they are open, and only opens the current panel. This will allow the open panel to gain the full size left in the client area."""

# All panels are stacked to the bottom. When they are expanded again they
# show up at the top
FPB_COLLAPSE_TO_BOTTOM = 0x0002
""" All panels are stacked to the bottom. When they are expanded again they show up at the top. """

# Now supported! Single fold plus panels
# will be stacked at the bottom
FPB_EXCLUSIVE_FOLD = 0x0004
""" ``FPB_SINGLE_FOLD`` style plus the panels will be stacked at the bottom. """

# Orientation Flag
FPB_HORIZONTAL = 0x0008
""" :class:`FoldPanelBar` will be horizontal. """
FPB_VERTICAL = 0x0010
""" :class:`FoldPanelBar` will be vertical. """

# FoldPanelItem default settings
FPB_ALIGN_LEFT = 0
""" Aligns left instead of fitting the width of the child window to be added. Use either this one or ``FPB_ALIGN_WIDTH``. """
FPB_ALIGN_WIDTH = 1
""" The :class:`wx.Window` to be added will be aligned to fit the width of the FoldPanel when it is resized. Very handy for sizer items, buttons and text boxes. """

FPB_DEFAULT_LEFTSPACING = 5
FPB_DEFAULT_RIGHTSPACING = 10
FPB_DEFAULT_SPACING = 8

FPB_DEFAULT_LEFTLINESPACING = 2
FPB_DEFAULT_RIGHTLINESPACING = 2


# ------------------------------------------------------------------------------ #
# class CaptionBarStyle
# ------------------------------------------------------------------------------ #

class CaptionBarStyle(object):
    """
    This class encapsulates the styles you wish to set for the
    :class:`CaptionBar` (this is the part of the `FoldPanel` where the caption
    is displayed). It can either be applied at creation time be
    reapplied when styles need to be changed.

    At construction time, all styles are set to their default
    transparency.  This means none of the styles will be applied to
    the :class:`CaptionBar` in question, meaning it will be created using the
    default internals. When setting i.e the colour, font or panel
    style, these styles become active to be used.
    """

    def __init__(self):
        """ Default constructor for this class. """

        self.ResetDefaults()


    def ResetDefaults(self):
        """ Resets default :class:`CaptionBarStyle`. """
        self._firstColourUsed = False
        self._secondColourUsed = False
        self._textColourUsed = False
        self._captionFontUsed = False
        self._captionStyleUsed = False
        self._captionStyle = CAPTIONBAR_GRADIENT_V


    # ------- CaptionBar Font -------

    def SetCaptionFont(self, font):
        """
        Sets font for the caption bar.

        :param `font`: a valid :class:`wx.Font` object.

        :note: If this is not set, the font property is undefined and will not be used.
         Use :meth:`~CaptionBarStyle.CaptionFontUsed` to check if this style is used.
        """

        self._captionFont = font
        self._captionFontUsed = True


    def CaptionFontUsed(self):
        """ Checks if the caption bar font is set. """

        return self._captionFontUsed


    def GetCaptionFont(self):
        """
        Returns the font for the caption bar.

        :note: Please be warned this will result in an assertion failure when
         this property is not previously set.

        :see: :meth:`~CaptionBarStyle.SetCaptionFont`, :meth:`~CaptionBarStyle.CaptionFontUsed`
        """

        return self._captionFont


    # ------- First Colour -------

    def SetFirstColour(self, colour):
        """
        Sets first colour for the caption bar.

        :param `colour`: a valid :class:`wx.Colour` object.

        :note: If this is not set, the colour property is undefined and will not be used.
         Use :meth:`~CaptionBarStyle.FirstColourUsed` to check if this style is used.
        """

        self._firstColour = colour
        self._firstColourUsed = True


    def FirstColourUsed(self):
        """ Checks if the first colour of the caption bar is set."""

        return self._firstColourUsed


    def GetFirstColour(self):
        """
        Returns the first colour for the caption bar.

        :note: Please be warned this will result in an assertion failure when
         this property is not previously set.

        :see: :meth:`~CaptionBarStyle.SetFirstColour`, :meth:`~CaptionBarStyle.FirstColourUsed`
        """

        return self._firstColour


    # ------- Second Colour -------

    def SetSecondColour(self, colour):
        """
        Sets second colour for the caption bar.

        :param `colour`: a valid :class:`wx.Colour` object.

        :note: If this is not set, the colour property is undefined and will not be used.
         Use :meth:`~CaptionBarStyle.SecondColourUsed` to check if this style is used.
        """

        self._secondColour = colour
        self._secondColourUsed = True


    def SecondColourUsed(self):
        """ Checks if the second colour of the caption bar is set."""

        return self._secondColourUsed


    def GetSecondColour(self):
        """
        Returns the second colour for the caption bar.

        :note: Please be warned this will result in an assertion failure when
         this property is not previously set.

        :see: :meth:`~CaptionBarStyle.SetSecondColour`, :meth:`~CaptionBarStyle.SecondColourUsed`
        """

        return self._secondColour


    # ------- Caption Text Colour -------

    def SetCaptionColour(self, colour):
        """
        Sets caption colour for the caption bar.

        :param `colour`: a valid :class:`wx.Colour` object.

        :note: If this is not set, the colour property is undefined and will not be used.
         Use :meth:`~CaptionBarStyle.CaptionColourUsed` to check if this style is used.
        """

        self._textColour = colour
        self._textColourUsed = True


    def CaptionColourUsed(self):
        """ Checks if the caption colour of the caption bar is set."""

        return self._textColourUsed


    def GetCaptionColour(self):
        """
        Returns the caption colour for the caption bar.

        :note: Please be warned this will result in an assertion failure
         when this property is not previously set.

        :see: :meth:`~CaptionBarStyle.SetCaptionColour`, :meth:`~CaptionBarStyle.CaptionColourUsed`
        """

        return self._textColour


    # ------- CaptionStyle  -------

    def SetCaptionStyle(self, style):
        """
        Sets caption style for the caption bar.

        :param `style`: can be one of the following bits:

         =============================== ======= =============================
         Caption Style                    Value  Description
         =============================== ======= =============================
         ``CAPTIONBAR_GRADIENT_V``             1 Draws a vertical gradient from top to bottom
         ``CAPTIONBAR_GRADIENT_H``             2 Draws a horizontal gradient from left to right
         ``CAPTIONBAR_SINGLE``                 3 Draws a single filled rectangle to draw the caption
         ``CAPTIONBAR_RECTANGLE``              4 Draws a single colour with a rectangle around the caption
         ``CAPTIONBAR_FILLED_RECTANGLE``       5 Draws a filled rectangle and a border around it
         =============================== ======= =============================

        :note: If this is not set, the property is undefined and will not be used.
         Use :meth:`~CaptionBarStyle.CaptionStyleUsed` to check if this style is used.
        """

        self._captionStyle = style
        self._captionStyleUsed = True


    def CaptionStyleUsed(self):
        """ Checks if the caption style of the caption bar is set."""

        return self._captionStyleUsed


    def GetCaptionStyle(self):
        """
        Returns the caption style for the caption bar.

        :note: Please be warned this will result in an assertion failure
         when this property is not previously set.

        :see: :meth:`~CaptionBarStyle.SetCaptionStyle`, :meth:`~CaptionBarStyle.CaptionStyleUsed`
        """

        return self._captionStyle


#-----------------------------------#
#        CaptionBarEvent
#-----------------------------------#
wxEVT_CAPTIONBAR = wx.NewEventType()
EVT_CAPTIONBAR = wx.PyEventBinder(wxEVT_CAPTIONBAR, 0)
""" The user has pressed the caption bar: :class:`FoldPanelBar` will either expand or""" \
""" collapse the underlying panel. """

# Define Empty CaptionBar Style
EmptyCaptionBarStyle = CaptionBarStyle()

# ---------------------------------------------------------------------------- #
# class CaptionBarEvent
# ---------------------------------------------------------------------------- #

class CaptionBarEvent(wx.CommandEvent):
    """
    This event will be sent when a ``EVT_CAPTIONBAR`` is mapped in the parent.
    It is to notify the parent that the bar is now in collapsed or expanded
    state. The parent should re-arrange the associated windows accordingly
    """

    def __init__(self, evtType):
        """
        Default class constructor.

        :param `evtType`: the event type.
        """

        wx.CommandEvent.__init__(self, evtType)


    def GetFoldStatus(self):
        """
        Returns whether the bar is expanded or collapsed. ``True`` means
        expanded.
        """

        return not self._bar.IsCollapsed()


    def GetBar(self):
        """ Returns the selected :class:`CaptionBar`. """

        return self._bar


    def SetTag(self, tag):
        """
        Assigns a tag to the selected :class:`CaptionBar`.

        :param `tag`: an instance of :class:`FoldPanelBar`.
        """

        self._tag = tag


    def GetTag(self):
        """ Returns the tag assigned to the selected :class:`CaptionBar`. """

        return self._tag


    def SetBar(self, bar):
        """
        Sets the bar associated with this event.

        :param `bar`: an instance of :class:`CaptionBar`.

        :note: Should not be used by any other than the originator of the event.
        """

        self._bar = bar


# -------------------------------------------------------------------------------- #
# class CaptionBar
# -------------------------------------------------------------------------------- #

class CaptionBar(wx.Window):
    """
    This class is a graphical caption component that consists of a
    caption and a clickable arrow.

    The :class:`CaptionBar` fires an event ``EVT_CAPTIONBAR`` which is a
    :class:`CaptionBarEvent`. This event can be caught and the parent window
    can act upon the collapsed or expanded state of the bar (which is
    actually just the icon which changed). The parent panel can
    reduce size or expand again.
    """

    def __init__(self, parent, id, pos, size, caption="",
                 foldIcons=None, cbstyle=None,
                 rightIndent=FPB_BMP_RIGHTSPACE,
                 iconWidth=16, iconHeight=16, collapsed=False):
        """
        Default class constructor.

        :param `parent`: the :class:`CaptionBar` parent window;
        :param `id`: an identifier for the control: a value of -1 is taken to mean a default;
        :param `pos`: the control position. A value of (-1, -1) indicates a default position,
         chosen by either the windowing system or wxPython, depending on platform;
        :param `size`: the control size. A value of (-1, -1) indicates a default size,
         chosen by either the windowing system or wxPython, depending on platform;
        :param `caption`: the string to be displayed in :class:`CaptionBar`;
        :param `foldIcons`: an instance of :class:`wx.ImageList` containing the icons to display
         next to the caption text;
        :param `cbstyle`: the :class:`CaptionBar` window style. Must be an instance of
         :class:`CaptionBarStyle`;
        :param `rightIndent`: number of pixels of the bmp to be aligned from the right filled
         with space;
        :param `iconWidth`: the :class:`CaptionBar` icon width;
        :param `iconHeight`: the :class:`CaptionBar` icon height;
        :param `collapsed`: ``True`` if the :class:`CaptionBar` should start in the collapsed state,
         ``False`` otherwise.
        """

        wx.Window.__init__(self, parent, wx.ID_ANY, pos=pos,
                           size=(20,20), style=wx.NO_BORDER)

        self._controlCreated = False
        self._collapsed = collapsed
        self.ApplyCaptionStyle(cbstyle, True)

        if foldIcons is None:
            foldIcons = wx.ImageList(16, 16)

            bmp = ExpandedIcon.GetBitmap()
            foldIcons.Add(bmp)
            bmp = CollapsedIcon.GetBitmap()
            foldIcons.Add(bmp)

        # set initial size
        if foldIcons:
            assert foldIcons.GetImageCount() > 1
            iconWidth, iconHeight = foldIcons.GetSize(0)

        self._caption = caption
        self._foldIcons = foldIcons
        self._style = cbstyle
        self._rightIndent = rightIndent
        self._iconWidth = iconWidth
        self._iconHeight = iconHeight
        self._oldSize = wx.Size(20,20)

        self._controlCreated = True

        self.Bind(wx.EVT_PAINT, self.OnPaint)
        self.Bind(wx.EVT_SIZE, self.OnSize)
        self.Bind(wx.EVT_MOUSE_EVENTS, self.OnMouseEvent)
        self.Bind(wx.EVT_CHAR, self.OnChar)


    def ApplyCaptionStyle(self, cbstyle=None, applyDefault=True):
        """
        Applies the style defined in `cbstyle` to the :class:`CaptionBar`.

        :param `cbstyle`: an instance of :class:`CaptionBarStyle`;
        :param `applyDefault`: if ``True``, the colours used in the :class:`CaptionBarStyle`
         will be reset to their default values.
        """

        if cbstyle is None:
            cbstyle = EmptyCaptionBarStyle

        newstyle = cbstyle

        if applyDefault:

            # get first colour from style or make it default
            if not newstyle.FirstColourUsed():
                newstyle.SetFirstColour(wx.WHITE)

            # get second colour from style or make it default
            if not newstyle.SecondColourUsed():
                # make the second colour slightly darker then the background
                colour = self.GetParent().GetBackgroundColour()
                r, g, b = int(colour.Red()), int(colour.Green()), int(colour.Blue())
                colour = ((r >> 1) + 20, (g >> 1) + 20, (b >> 1) + 20)
                newstyle.SetSecondColour(wx.Colour(colour[0], colour[1], colour[2]))

            # get text colour
            if not newstyle.CaptionColourUsed():
                newstyle.SetCaptionColour(wx.BLACK)

            # get font colour
            if not newstyle.CaptionFontUsed():
                newstyle.SetCaptionFont(self.GetParent().GetFont())

            # apply caption style
            if not newstyle.CaptionStyleUsed():
                newstyle.SetCaptionStyle(CAPTIONBAR_GRADIENT_V)

        self._style = newstyle


    def SetCaptionStyle(self, cbstyle=None, applyDefault=True):
        """
        Sets :class:`CaptionBar` styles with :class:`CaptionBarStyle` class.

        :param `cbstyle`: an instance of :class:`CaptionBarStyle`;
        :param `applyDefault`: if ``True``, the colours used in the :class:`CaptionBarStyle`
         will be reset to their default values.

        :note: All styles that are actually set, are applied. If you set `applyDefault`
         to ``True``, all other (not defined) styles will be set to default. If it is
         ``False``, the styles which are not set in the :class:`CaptionBarStyle` will be ignored.
        """

        if cbstyle is None:
            cbstyle = EmptyCaptionBarStyle

        self.ApplyCaptionStyle(cbstyle, applyDefault)
        self.Refresh()


    def GetCaptionStyle(self):
        """
        Returns the current style of the captionbar in a :class:`CaptionBarStyle` class.

        :note: This can be used to change and set back the changes.
        """

        return self._style


    def IsCollapsed(self):
        """ Returns whether the status of the bar is expanded or collapsed. """

        return self._collapsed


    def SetRightIndent(self, pixels):
        """
        Sets the amount of pixels on the right from which the bitmap
        is trailing.

        :param `pixels`: the number of pixels on the right from which the bitmap
         is trailing. If this is 0, it will be drawn all the way to the right,
         default is equal to ``FPB_BMP_RIGHTSPACE``. Assign this before
         assigning an image list to prevent a redraw.
        """

        assert pixels >= 0
        self._rightIndent = pixels
        if self._foldIcons:
            self.Refresh()


    def Collapse(self):
        """
        This sets the internal state/representation to collapsed.

        :note: This does not trigger a :class:`CaptionBarEvent` to be sent to the
         parent.
        """

        self._collapsed = True
        self.RedrawIconBitmap()


    def Expand(self):
        """
        This sets the internal state/representation to expanded.

        :note: This does not trigger a :class:`CaptionBarEvent` to be sent to the
         parent.
        """

        self._collapsed = False
        self.RedrawIconBitmap()


    def SetBoldFont(self):
        """ Sets the :class:`CaptionBar` font weight to bold."""

        self.GetFont().SetWeight(wx.FONTWEIGHT_BOLD)


    def SetNormalFont(self):
        """ Sets the :class:`CaptionBar` font weight to normal."""

        self.GetFont().SetWeight(wx.FONTWEIGHT_NORMAL)


    def IsVertical(self):
        """
        Returns whether the :class:`CaptionBar` has a default orientation or not.
        Default is vertical.
        """

        fld = self.GetParent().GetGrandParent()
        if isinstance(fld, FoldPanelBar):
            return self.GetParent().GetGrandParent().IsVertical()
        else:
            raise Exception("ERROR: Wrong Parent " + repr(fld))


    def OnPaint(self, event):
        """
        Handles the ``wx.EVT_PAINT`` event for :class:`CaptionBar`.

        :param `event`: a :class:`PaintEvent` event to be processed.
        """

        if not self._controlCreated:
            event.Skip()
            return

        dc = wx.PaintDC(self)
        wndRect = self.GetRect()
        vertical = self.IsVertical()

        # TODO: Maybe first a memory DC should draw all, and then paint it on
        # the caption. This way a flickering arrow during resize is not visible

        self.FillCaptionBackground(dc)
        dc.SetFont(self._style.GetCaptionFont())
        dc.SetTextForeground(self._style.GetCaptionColour())

        if vertical:
            dc.DrawText(self._caption, 4, FPB_EXTRA_Y//2)
        else:
            dc.DrawRotatedText(self._caption, FPB_EXTRA_Y//2,
                               wndRect.GetBottom() - 4, 90)

        # draw small icon, either collapsed or expanded
        # based on the state of the bar. If we have any bmp's

        if self._foldIcons:

            index = self._collapsed

            if vertical:
                drw = wndRect.GetRight() - self._iconWidth - self._rightIndent
                self._foldIcons.Draw(index, dc, drw,
                                     (wndRect.GetHeight() - self._iconHeight)//2,
                                     wx.IMAGELIST_DRAW_TRANSPARENT)
            else:
                self._foldIcons.Draw(index, dc,
                                     (wndRect.GetWidth() - self._iconWidth)//2,
                                     self._rightIndent, wx.IMAGELIST_DRAW_TRANSPARENT)

##        event.Skip()


    def FillCaptionBackground(self, dc):
        """
        Fills the background of the caption with either a gradient or
        a solid colour.

        :param `dc`: an instance of :class:`wx.DC`.
        """

        style = self._style.GetCaptionStyle()

        if style == CAPTIONBAR_GRADIENT_V:
            if self.IsVertical():
                self.DrawVerticalGradient(dc, self.GetRect())
            else:
                self.DrawHorizontalGradient(dc, self.GetRect())

        elif style == CAPTIONBAR_GRADIENT_H:
            if self.IsVertical():
                self.DrawHorizontalGradient(dc, self.GetRect())
            else:
                self.DrawVerticalGradient(dc, self.GetRect())

        elif style == CAPTIONBAR_SINGLE:
            self.DrawSingleColour(dc, self.GetRect())
        elif style == CAPTIONBAR_RECTANGLE or style == CAPTIONBAR_FILLED_RECTANGLE:
            self.DrawSingleRectangle(dc, self.GetRect())
        else:
            raise Exception("STYLE Error: Undefined Style Selected: " + repr(style))


    def OnMouseEvent(self, event):
        """
        Handles the ``wx.EVT_MOUSE_EVENTS`` event for :class:`CaptionBar`.

        :param `event`: a :class:`MouseEvent` event to be processed.

        :note: This method catches the mouse click-double click. If clicked on
         the arrow (single) or double on the caption we change state and an event
         must be fired to let this panel collapse or expand.
        """

        send_event = False
        vertical = self.IsVertical()

        if event.LeftDown() and self._foldIcons:

            pt = event.GetPosition()
            rect = self.GetRect()

            drw = (rect.GetWidth() - self._iconWidth - self._rightIndent)
            if vertical and pt.x > drw or not vertical and \
               pt.y < (self._iconHeight + self._rightIndent):
                send_event = True

        elif event.LeftDClick():
            self.SetCursor(wx.Cursor(wx.CURSOR_ARROW))
            send_event = True

        elif event.Entering() and self._foldIcons:
            pt = event.GetPosition()
            rect = self.GetRect()

            drw = (rect.GetWidth() - self._iconWidth - self._rightIndent)
            if vertical and pt.x > drw or not vertical and \
               pt.y < (self._iconHeight + self._rightIndent):
                self.SetCursor(wx.Cursor(wx.CURSOR_HAND))
            else:
                self.SetCursor(wx.Cursor(wx.CURSOR_ARROW))

        elif event.Leaving():
            self.SetCursor(wx.Cursor(wx.CURSOR_ARROW))

        elif event.Moving():
            pt = event.GetPosition()
            rect = self.GetRect()

            drw = (rect.GetWidth() - self._iconWidth - self._rightIndent)
            if vertical and pt.x > drw or not vertical and \
               pt.y < (self._iconHeight + self._rightIndent):
                self.SetCursor(wx.Cursor(wx.CURSOR_HAND))
            else:
                self.SetCursor(wx.Cursor(wx.CURSOR_ARROW))

        # send the collapse, expand event to the parent

        if send_event:
            event = CaptionBarEvent(wxEVT_CAPTIONBAR)
            event.SetId(self.GetId())
            event.SetEventObject(self)
            event.SetBar(self)
            self.GetEventHandler().ProcessEvent(event)


    def OnChar(self, event):
        """
        Handles the ``wx.EVT_CHAR`` event for :class:`CaptionBar`.

        :param `event`: a :class:`KeyEvent` event to be processed.

        :note: This method currently does nothing.
        """

        # TODO: Anything here?
        event.Skip()


    def DoGetBestSize(self):
        """
        Returns the best size for this panel, based upon the font
        assigned to this window, and the caption string.

        :note: Overridden from :class:`wx.Window`.
        """

        if self.IsVertical():
            x, y = self.GetTextExtent(self._caption)
        else:
            y, x = self.GetTextExtent(self._caption)

        if x < self._iconWidth:
            x = self._iconWidth

        if y < self._iconHeight:
            y = self._iconHeight

        # TODO: The extra FPB_EXTRA_X constants should be adjustable as well

        return wx.Size(x + FPB_EXTRA_X, y + FPB_EXTRA_Y)


    def DrawVerticalGradient(self, dc, rect):
        """
        Gradient fill from colour 1 to colour 2 from top to bottom.

        :param `dc`: an instance of :class:`wx.DC`;
        :param `rect`: the :class:`CaptionBar` client rectangle.
        """

        if  rect.height < 1 or rect.width < 1:
            return

        dc.SetPen(wx.TRANSPARENT_PEN)

        # calculate gradient coefficients
        col2 = self._style.GetSecondColour()
        col1 = self._style.GetFirstColour()

        r1, g1, b1 = int(col1.Red()), int(col1.Green()), int(col1.Blue())
        r2, g2, b2 = int(col2.Red()), int(col2.Green()), int(col2.Blue())

        flrect = float(rect.height)

        rstep = float((r2 - r1)) / flrect
        gstep = float((g2 - g1)) / flrect
        bstep = float((b2 - b1)) / flrect

        rf, gf, bf = 0, 0, 0

        for y in range(rect.y, rect.y + rect.height):
            currCol = (int(r1 + rf), int(g1 + gf), int(b1 + bf))

            dc.SetBrush(wx.Brush(currCol, wx.BRUSHSTYLE_SOLID))
            dc.DrawRectangle(rect.x, rect.y + (y - rect.y), rect.width, rect.height)
            rf = rf + rstep
            gf = gf + gstep
            bf = bf + bstep


    def DrawHorizontalGradient(self, dc, rect):
        """
        Gradient fill from colour 1 to colour 2 from left to right.

        :param `dc`: an instance of :class:`wx.DC`;
        :param `rect`: the :class:`CaptionBar` client rectangle.
        """

        if rect.height < 1 or rect.width < 1:
            return

        dc.SetPen(wx.TRANSPARENT_PEN)

        # calculate gradient coefficients
        col2 = self._style.GetSecondColour()
        col1 = self._style.GetFirstColour()

        r1, g1, b1 = int(col1.Red()), int(col1.Green()), int(col1.Blue())
        r2, g2, b2 = int(col2.Red()), int(col2.Green()), int(col2.Blue())

        flrect = float(rect.width)

        rstep = float((r2 - r1)) / flrect
        gstep = float((g2 - g1)) / flrect
        bstep = float((b2 - b1)) / flrect

        rf, gf, bf = 0, 0, 0

        for x in range(rect.x, rect.x + rect.width):
            currCol = (r1 + rf, g1 + gf, b1 + bf)

            dc.SetBrush(wx.Brush(currCol, wx.BRUSHSTYLE_SOLID))
            dc.DrawRectangle(rect.x + (x - rect.x), rect.y, 1, rect.height)
            rf = rf + rstep
            gf = gf + gstep
            bf = bf + bstep


    def DrawSingleColour(self, dc, rect):
        """
        Single colour fill for :class:`CaptionBar`.

        :param `dc`: an instance of :class:`wx.DC`;
        :param `rect`: the :class:`CaptionBar` client rectangle.
        """

        if rect.height < 1 or rect.width < 1:
            return

        dc.SetPen(wx.TRANSPARENT_PEN)

        # draw simple rectangle
        dc.SetBrush(wx.Brush(self._style.GetFirstColour(), wx.BRUSHSTYLE_SOLID))
        dc.DrawRectangle(rect.x, rect.y, rect.width, rect.height)


    def DrawSingleRectangle(self, dc, rect):
        """
        Single rectangle for :class:`CaptionBar`.

        :param `dc`: an instance of :class:`wx.DC`;
        :param `rect`: the :class:`CaptionBar` client rectangle.
        """

        if rect.height < 2 or rect.width < 1:
            return

        # single frame, set up internal fill colour

        if self._style.GetCaptionStyle() == CAPTIONBAR_RECTANGLE:
            colour = self.GetParent().GetBackgroundColour()
            br = wx.Brush(colour, wx.BRUSHSTYLE_SOLID)
        else:
            colour = self._style.GetFirstColour()
            br = wx.Brush(colour, wx.BRUSHSTYLE_SOLID)

        # setup the pen frame

        pen = wx.Pen(self._style.GetSecondColour())
        dc.SetPen(pen)
        dc.SetBrush(br)
        dc.DrawRectangle(rect.x, rect.y, rect.width, rect.height - 1)

        bgpen = wx.Pen(self.GetParent().GetBackgroundColour())
        dc.SetPen(bgpen)
        dc.DrawLine(rect.x, rect.y + rect.height - 1, rect.x + rect.width,
                    rect.y + rect.height - 1)


    def OnSize(self, event):
        """
        Handles the ``wx.EVT_SIZE`` event for :class:`CaptionBar`.

        :param `event`: a :class:`wx.SizeEvent` event to be processed.
        """

        if not self._controlCreated:
            event.Skip()
            return

        size = event.GetSize()

        if self._foldIcons:

            # What I am doing here is simply invalidating the part of the window
            # exposed. So when I make a rect with as width the newly exposed part,
            # and the x,y of the old window size origin, I don't need a bitmap
            # calculation in it, or do I ? The bitmap needs redrawing anyway.
            # Leave it like this until I figured it out.

            # set rect to redraw as old bitmap area which is entitled to redraw

            rect = wx.Rect(size.GetWidth() - self._iconWidth - self._rightIndent, 0,
                           self._iconWidth + self._rightIndent,
                           self._iconWidth + self._rightIndent)

            # adjust rectangle when more is slided so we need to redraw all
            # the old stuff but not all (ugly flickering)

            diffX = size.GetWidth() - self._oldSize.GetWidth()

            if diffX > 1:

                # adjust the rect with all the crap to redraw

                rect.SetWidth(rect.GetWidth() + diffX + 10)
                rect.SetX(rect.GetX() - diffX - 10)

            self.RefreshRect(rect)

        else:

            rect = self.GetRect()
            self.RefreshRect(rect)

        self._oldSize = size


    def RedrawIconBitmap(self):
        """ Redraws the icons (if they exists). """

        if self._foldIcons:

            # invalidate the bitmap area and force a redraw

            rect = self.GetRect()

            rect.SetX(rect.GetWidth() - self._iconWidth - self._rightIndent)
            rect.SetWidth(self._iconWidth + self._rightIndent)
            self.RefreshRect(rect)


# ---------------------------------------------------------------------------------- #
# class FoldPanelBar
# ---------------------------------------------------------------------------------- #

class FoldPanelBar(wx.Panel):
    """
    The :class:`FoldPanelBar` is a class which can maintain a list of
    collapsible panels. Once a panel is collapsed, only it's caption
    bar is visible to the user. This will provide more space for the
    other panels, or allow the user to close panels which are not used
    often to get the most out of the work area.

    This control is easy to use. Simply create it as a child for a
    panel or sash window, and populate panels with
    :meth:`FoldPanelBar.AddFoldPanel() <FoldPanelBar.AddFoldPanel>`. Then use the
    :meth:`FoldPanelBar.AddFoldPanelWindow() <FoldPanelBar.AddFoldPanelWindow>` to add
    :class:`wx.Window` derived controls to the current fold panel. Use
    :meth:`FoldPanelBar.AddFoldPanelSeparator() <FoldPanelBar.AddFoldPanelSeparator>` to put separators between the groups of
    controls that need a visual separator to group them
    together. After all is constructed, the user can fold the panels
    by double clicking on the bar or single click on the arrow, which
    will indicate the collapsed or expanded state.
    """

    def __init__(self, parent, id=-1, pos=wx.DefaultPosition, size=wx.DefaultSize,
                 style=wx.TAB_TRAVERSAL|wx.NO_BORDER, agwStyle=0):
        """
        Default class constructor.

        :param `parent`: the :class:`FoldPanelBar` parent window;
        :param `id`: an identifier for the control: a value of -1 is taken to mean a default;
        :param `pos`: the control position. A value of (-1, -1) indicates a default position,
         chosen by either the windowing system or wxPython, depending on platform;
        :param `size`: the control size. A value of (-1, -1) indicates a default size,
         chosen by either the windowing system or wxPython, depending on platform;
        :param `style`: the underlying :class:`Panel` window style;
        :param `agwStyle`: the AGW-specific :class:`FoldPanelBar` window style. It can be one of the
         following bits:

         ========================== =========== ==================================================
         Window Styles              Hex Value   Description
         ========================== =========== ==================================================
         ``FPB_SINGLE_FOLD``                0x1 Single fold forces other panels to close when they are open, and only opens the current panel. This will allow the open panel to gain the full size left in the client area.
         ``FPB_COLLAPSE_TO_BOTTOM``         0x2 All panels are stacked to the bottom. When they are expanded again they show up at the top.
         ``FPB_EXCLUSIVE_FOLD``             0x4 ``FPB_SINGLE_FOLD`` style plus the panels will be stacked at the bottom.
         ``FPB_HORIZONTAL``                 0x4 :class:`FoldPanelBar` will be horizontal.
         ``FPB_VERTICAL``                   0x8 :class:`FoldPanelBar` will be vertical.
         ========================== =========== ==================================================
        """

        self._controlCreated = False

        # make sure there is any orientation
        if not agwStyle & (FPB_HORIZONTAL | FPB_VERTICAL):
            agwStyle = agwStyle | FPB_VERTICAL

        if agwStyle & FPB_HORIZONTAL:
            self._isVertical = False
        else:
            self._isVertical = True

        self._agwStyle = agwStyle

        # create the panel (duh!). This causes a size event, which we are going
        # to skip when we are not initialised

        wx.Panel.__init__(self, parent, id, pos, size, style)

        # the fold panel area

        self._foldPanel = wx.Panel(self, wx.ID_ANY, pos, size,
                                   wx.NO_BORDER | wx.TAB_TRAVERSAL)

        self._controlCreated = True
        self._panels = []

        self.Bind(EVT_CAPTIONBAR, self.OnPressCaption)
        self.Bind(wx.EVT_SIZE, self.OnSizePanel)


    def AddFoldPanel(self, caption="", collapsed=False, foldIcons=None, cbstyle=None):
        """
        Adds a fold panel to the list of panels.

        :param `caption`: the caption to be displayed in the associated :class:`CaptionBar`;
        :param `collapsed`: if set to ``True``, the panel is collapsed initially;
        :param `foldIcons`: an instance of :class:`wx.ImageList` containing the icons to display
         next to the caption text;
        :param `cbstyle`: an instance of :class:`CaptionBarStyle`.

        :note: The FoldPanel item which is returned, can be used as a reference to perform
         actions upon the fold panel like collapsing it, expanding it, or deleting it
         from the list. Use this foldpanel to add windows to it.

        :see: :meth:`~FoldPanelBar.AddFoldPanelWindow` and :meth:`~FoldPanelBar.AddFoldPanelSeparator` to see how to add
         items derived from :class:`wx.Window` to the panels.
        """

        if cbstyle is None:
            cbstyle = EmptyCaptionBarStyle

        # create a fold panel item, which is first only the caption.
        # the user can now add a panel area which will be folded in
        # when pressed.

        if foldIcons is None:
            foldIcons = wx.ImageList(16, 16)

            bmp = ExpandedIcon.GetBitmap()
            foldIcons.Add(bmp)
            bmp = CollapsedIcon.GetBitmap()
            foldIcons.Add(bmp)

        item = FoldPanelItem(self._foldPanel, -1, caption=caption,
                             foldIcons=foldIcons,
                             collapsed=collapsed, cbstyle=cbstyle)

        pos = 0
        if len(self._panels) > 0:
            pos = self._panels[-1].GetItemPos() + self._panels[-1].GetPanelLength()

        item.Reposition(pos)
        self._panels.append(item)

        return item


    def AddFoldPanelWindow(self, panel, window, flags=FPB_ALIGN_WIDTH,
                           spacing=FPB_DEFAULT_SPACING,
                           leftSpacing=FPB_DEFAULT_LEFTLINESPACING,
                           rightSpacing=FPB_DEFAULT_RIGHTLINESPACING):
        """
        Adds a :class:`wx.Window` derived instance to the referenced fold panel.

        :param `panel`: an instance of :class:`FoldPanelItem`;
        :param `window`: the window we wish to add to the fold panel, an instance
         of :class:`wx.Window`;
        :param `flags`: can be one of the following bits:

         ====================== ======= ====================================
         Align Flag              Value  Description
         ====================== ======= ====================================
         ``FPB_ALIGN_WIDTH``          1 The :class:`wx.Window` to be added will be aligned to fit the width of the FoldPanel when it is resized. Very handy for sizer items, buttons and text boxes.
         ``FPB_ALIGN_LEFT``           0 Aligns left instead of fitting the width of the child window to be added. Use either this one or ``FPB_ALIGN_WIDTH``.
         ====================== ======= ====================================

        :param `spacing`: the :class:`wx.Window` to be added can be slightly indented from
         left and right so it is more visibly placed in the fold panel. Use `spacing` > 0
         to give the control an y offset from the previous :class:`wx.Window` added;
        :param `leftSpacing`: give the :class:`wx.Window` added a slight indent from the left;
        :param `rightSpacing`: give the :class:`wx.Window` added a slight indent from the right;

        :note: Make the window be a child of the fold panel!

         The following example adds a FoldPanel to the :class:`FoldPanelBar` and
         adds two :class:`wx.Window` derived controls to the FoldPanel::

               # Create the FoldPanelBar
               m_pnl = FoldPanelBar(self, wx.ID_ANY, wx.DefaultPosition, wx.DefaultSize, agwStyle=0x2)

               # Add a foldpanel to the control. "Test me" is the caption and it is
               # initially not collapsed.
               item = m_pnl.AddFoldPanel("Test me", False)

               # Now add a button to the fold panel. Mind that the button should be
               # made child of the FoldPanel and not of the main form.
               m_pnl.AddFoldPanelWindow(item, wx.Button(item, ID_COLLAPSEME, "Collapse Me"))

               # Add a separator between the two controls. This is purely a visual
               # line that can have a certain colour and also the indents and width
               # aligning like a control.
               m_pnl.AddFoldPanelSeparator(item)

               # Now add a text ctrl. Also very easy. Align this on width so that
               # when the control gets wider the text control also sizes along.
               m_pnl.AddFoldPanelWindow(item, wx.TextCtrl(item, wx.ID_ANY, "Comment"), FPB_ALIGN_WIDTH, FPB_DEFAULT_SPACING, 20)

        """

        try:
            item = self._panels.index(panel)
        except:
            raise Exception("ERROR: Invalid Panel Passed To AddFoldPanelWindow: " + repr(panel))

        panel.AddWindow(window, flags, spacing, leftSpacing, rightSpacing)

        # TODO: Take old and new height, and if difference, reposition all the lower
        # panels this is because the user can add new wxWindow controls somewhere in
        # between when other panels are already present.

        return 0


    def AddFoldPanelSeparator(self, panel, colour=wx.BLACK,
                              spacing=FPB_DEFAULT_SPACING,
                              leftSpacing=FPB_DEFAULT_LEFTLINESPACING,
                              rightSpacing=FPB_DEFAULT_RIGHTLINESPACING):
        """
        Adds a separator line to the current fold panel.

        The separator is a simple line which is drawn and is no real
        component. It can be used to separate groups of controls
        which belong to each other.

        :param `colour`: the separator colour, an instance of :class:`wx.Colour`;
        :param `spacing`: the separator to be added can be slightly indented from
         left and right so it is more visibly placed in the fold panel. Use `spacing` > 0
         to give the control an y offset from the previous :class:`wx.Window` added;
        :param `leftSpacing`: give the added separator a slight indent from the left;
        :param `rightSpacing`: give the added separator a slight indent from the right.
        """

        try:
            item = self._panels.index(panel)
        except:
            raise Exception("ERROR: Invalid Panel Passed To AddFoldPanelSeparator: " + repr(panel))

        panel.AddSeparator(colour, spacing, leftSpacing, rightSpacing)
        return 0


    def OnSizePanel(self, event):
        """
        Handles the ``wx.EVT_SIZE`` event for :class:`FoldPanelBar`.

        :param `event`: a :class:`wx.SizeEvent` event to be processed.
        """

        # skip all stuff when we are not initialised yet

        if not self._controlCreated:
            event.Skip()
            return

        foldrect = self.GetRect()

        # fold panel itself. If too little space,
        # don't show it

        foldrect.SetX(0)
        foldrect.SetY(0)

        self._foldPanel.SetSize(foldrect[2:])

        if self._agwStyle & FPB_COLLAPSE_TO_BOTTOM or self._agwStyle & FPB_EXCLUSIVE_FOLD:
            rect = self.RepositionCollapsedToBottom()
            vertical = self.IsVertical()
            if vertical and rect.GetHeight() > 0 or not vertical and rect.GetWidth() > 0:
                self.RefreshRect(rect)

        # TODO: A smart way to check whether the old - new width of the
        # panel changed, if so no need to resize the fold panel items

        self.RedisplayFoldPanelItems()


    def OnPressCaption(self, event):
        """
        Handles the ``wx.EVT_CAPTIONBAR`` event for :class:`CaptionBar`.

        :param `event`: a :class:`CaptionBarEvent` event to be processed.
        """

        # act upon the folding or expanding status of the bar
        # to expand or collapse the panel(s)

        if event.GetFoldStatus():
            self.Collapse(event.GetTag())
        else:
            self.Expand(event.GetTag())



    def RefreshPanelsFrom(self, item):
        """
        Refreshes all the panels from given one down to last one.

        :param `item`: the first :class:`FoldPanelItem` to be refreshed.
        """

        try:
            i = self._panels.index(item)
        except:
            raise Exception("ERROR: Invalid Panel Passed To RefreshPanelsFrom: " + repr(item))

        self.Freeze()

        # if collapse to bottom is on, the panels that are not expanded
        # should be drawn at the bottom. All panels that are expanded
        # are drawn on top. The last expanded panel gets all the extra space

        if self._agwStyle & FPB_COLLAPSE_TO_BOTTOM or self._agwStyle & FPB_EXCLUSIVE_FOLD:

            offset = 0

            for panels in self._panels:

                if panels.IsExpanded():
                    offset = offset + panels.Reposition(offset)

            # put all non collapsed panels at the bottom where there is space,
            # else put them right behind the expanded ones

            self.RepositionCollapsedToBottom()

        else:

            pos = self._panels[i].GetItemPos() + self._panels[i].GetPanelLength()
            for j in range(i+1, len(self._panels)):
                pos = pos + self._panels[j].Reposition(pos)

        self.Thaw()


    def RedisplayFoldPanelItems(self):
        """ Resizes the fold panels so they match the width. """

        # resize them all. No need to reposition
        for panels in self._panels:
            panels.ResizePanel()
            panels.Refresh()


    def RepositionCollapsedToBottom(self):
        """
        Repositions all the collapsed panels to the bottom.

        When it is not possible to align them to the bottom, stick them behind
        the visible panels.
        """

        value = wx.Rect(0,0,0,0)
        vertical = self.IsVertical()

        # determine whether the number of panels left
        # times the size of their captions is enough
        # to be placed in the left over space

        expanded = 0
        collapsed = 0
        collapsed, expanded, values = self.GetPanelsLength(collapsed, expanded)

        # if no room stick them behind the normal ones, else
        # at the bottom

        if (vertical and [self.GetSize().GetHeight()] or \
            [self.GetSize().GetWidth()])[0] - expanded - collapsed < 0:
            offset = expanded
        else:

            # value is the region which is left unpainted
            # I will send it back as 'slack' so it does not need to
            # be recalculated.

            value.SetHeight(self.GetSize().GetHeight())
            value.SetWidth(self.GetSize().GetWidth())

            if vertical:
                value.SetY(expanded)
                value.SetHeight(value.GetHeight() - expanded)
            else:
                value.SetX(expanded)
                value.SetWidth(value.GetWidth() - expanded)

            offset = (vertical and [self.GetSize().GetHeight()] or \
                      [self.GetSize().GetWidth()])[0] - collapsed


        # go reposition

        for panels in self._panels:
            if not panels.IsExpanded():
                offset = offset + panels.Reposition(offset)

        return value


    def GetPanelsLength(self, collapsed, expanded):
        """
        Returns the length of the panels that are expanded and
        collapsed.

        :param `collapsed`: the current value of the collapsed panels;
        :param `expanded`: the current value of the expanded panels.

        :note: This is useful to determine quickly what size is used to display,
         and what is left at the bottom (right) to align the collapsed panels.
        """

        value = 0

        # assumed here that all the panels that are expanded
        # are positioned after each other from 0,0 to end.

        for j in range(0, len(self._panels)):
            offset = self._panels[j].GetPanelLength()
            value = value + offset
            if self._panels[j].IsExpanded():
                expanded = expanded + offset
            else:
                collapsed = collapsed + offset

        return collapsed, expanded, value


    def Collapse(self, foldpanel):
        """
        Collapses the given fold panel reference, and updates the foldpanel bar.

        :param `foldpanel`: an instance of :class:`FoldPanelItem`.

        :note: With the ``FPB_COLLAPSE_TO_BOTTOM`` style set, all collapsed captions
         are put at the bottom of the control. In the normal mode, they stay where
         they are.
        """

        try:
            item = self._panels.index(foldpanel)
        except:
            raise Exception("ERROR: Invalid Panel Passed To Collapse: " + repr(foldpanel))

        foldpanel.Collapse()
        self.RefreshPanelsFrom(foldpanel)


    def Expand(self, foldpanel):
        """
        Expands the given fold panel reference, and updates the foldpanel bar.

        :param `foldpanel`: an instance of :class:`FoldPanelItem`.

        :note: With the ``FPB_COLLAPSE_TO_BOTTOM`` style set, they will be removed
         from the bottom and the order where the panel originally was placed is
         restored.
        """

        fpbextrastyle = 0

        if self._agwStyle & FPB_SINGLE_FOLD or self._agwStyle & FPB_EXCLUSIVE_FOLD:
            fpbextrastyle = 1
            for panel in self._panels:
                panel.Collapse()

        foldpanel.Expand()

        if fpbextrastyle:
            if self._agwStyle & FPB_EXCLUSIVE_FOLD:
                self.RepositionCollapsedToBottom()
            self.RefreshPanelsFrom(self._panels[0])
        else:
            self.RefreshPanelsFrom(foldpanel)


    def ApplyCaptionStyle(self, foldpanel, cbstyle):
        """
        Sets the style of the caption bar (:class:`CaptionBar`) of the fold panel.

        :param `foldpanel`: an instance of :class:`FoldPanelItem`;
        :param `cbstyle`: an instance of :class:`CaptionBarStyle`.

        :note:

         The changes are applied immediately. All styles not set in the
         :class:`CaptionBarStyle` class are not applied. Use the :class:`CaptionBar` reference
         to indicate what captionbar you want to apply the style to. To apply one
         style to all :class:`CaptionBar` items, use :meth:`~FoldPanelBar.ApplyCaptionStyleAll`.
        """

        foldpanel.ApplyCaptionStyle(cbstyle)


    def ApplyCaptionStyleAll(self, cbstyle):
        """
        Sets the style of all the caption bars of the fold panel.
        The changes are applied immediately.

        :param `cbstyle`: an instance of :class:`CaptionBarStyle`.
        """

        for panels in self._panels:
            self.ApplyCaptionStyle(panels, cbstyle)


    def GetCaptionStyle(self, foldpanel):
        """
        Returns the currently used caption style for the fold panel.

        It is returned as a :class:`CaptionBarStyle` class. After modifying it, it can
        be set again.

        :param `foldpanel`: an instance of :class:`FoldPanelItem`.
        """

        return foldpanel.GetCaptionStyle()


    def IsVertical(self):
        """
        Returns whether the :class:`CaptionBar` has default orientation or not.
        Default is vertical.
        """

        return self._isVertical


    def GetFoldPanel(self, item):
        """
        Returns the panel associated with the index `item`.

        :param `item`: an integer representing the :class:`FoldPanelItem` in the list of
         panels in this :class:`FoldPanelBar`.
        """

        try:
            ind = self._panels[item]
            return self._panels[item]
        except:
            raise Exception("ERROR: List Index Out Of Range Or Bad Item Passed: " + repr(item) + \
                            ". Item Should Be An Integer Between " + repr(0) + " And " + \
                            repr(len(self._panels)))


    def GetCount(self):
        """ Returns the number of panels in the :class:`FoldPanelBar`. """

        try:
            return len(self._panels)
        except:
            raise Exception("ERROR: No Panels Have Been Added To FoldPanelBar")



# --------------------------------------------------------------------------------- #
# class FoldPanelItem
# --------------------------------------------------------------------------------- #

class FoldPanelItem(wx.Panel):
    """
    This class is a child sibling of the :class:`FoldPanelBar` class. It will
    contain a :class:`CaptionBar` class for receiving of events, and a the
    rest of the area can be populated by a :class:`Panel` derived class.
    """

    def __init__(self, parent, id=wx.ID_ANY, caption="", foldIcons=None,
                 collapsed=False, cbstyle=None):
        """
        Default class constructor.

        :param `parent`: the :class:`FoldPanelItem` parent window;
        :param `id`: an identifier for the control: a value of -1 is taken to mean a default;
        :param `caption`: the string to be displayed in :class:`CaptionBar`;
        :param `foldIcons`: an instance of :class:`wx.ImageList` containing the icons to display
         next to the caption text;
        :param `collapsed`: ``True`` if the :class:`CaptionBar` should start in the collapsed state,
         ``False`` otherwise;
        :param `cbstyle`: the :class:`CaptionBar` window style. Must be an instance of
         :class:`CaptionBarStyle`.
        """

        wx.Panel.__init__(self, parent, id, wx.Point(0,0), style=wx.CLIP_CHILDREN)
        self._controlCreated = False
        self._UserSize = 0
        self._PanelSize = 0
        self._LastInsertPos = 0
        self._itemPos = 0
        self._userSized = False

        if foldIcons is None:
            foldIcons = wx.ImageList(16, 16)

            bmp = ExpandedIcon.GetBitmap()
            foldIcons.Add(bmp)
            bmp = CollapsedIcon.GetBitmap()
            foldIcons.Add(bmp)

        self._foldIcons = foldIcons
        if cbstyle is None:
            cbstyle = EmptyCaptionBarStyle

        # create the caption bar, in collapsed or expanded state

        self._captionBar = CaptionBar(self, wx.ID_ANY, wx.Point(0,0),
                                      size=wx.DefaultSize, caption=caption,
                                      foldIcons=foldIcons, cbstyle=cbstyle)

        if collapsed:
            self._captionBar.Collapse()

        self._controlCreated = True

        # make initial size for component, if collapsed, the
        # size is determined on the panel height and won't change

        size = self._captionBar.GetSize()

        self._PanelSize = (self.IsVertical() and [size.GetHeight()] or \
                           [size.GetWidth()])[0]

        self._LastInsertPos = self._PanelSize
        self._items = []

        self.Bind(EVT_CAPTIONBAR, self.OnPressCaption)
        self.Bind(wx.EVT_PAINT, self.OnPaint)


    def AddWindow(self, window, flags=FPB_ALIGN_WIDTH, spacing=FPB_DEFAULT_SPACING,
                  leftSpacing=FPB_DEFAULT_LEFTLINESPACING,
                  rightSpacing=FPB_DEFAULT_RIGHTLINESPACING):
        """
        Adds a window item to the list of items on this panel.

        :param `window`: an instance of :class:`wx.Window`;
        :param `flags`: can be one of the following bits:

         ====================== ======= ====================================
         Align Flag              Value  Description
         ====================== ======= ====================================
         ``FPB_ALIGN_WIDTH``          1 The :class:`wx.Window` to be added will be aligned to fit the width of the FoldPanel when it is resized. Very handy for sizer items, buttons and text boxes.
         ``FPB_ALIGN_LEFT``           0 Aligns left instead of fitting the width of the child window to be added. Use either this one or ``FPB_ALIGN_WIDTH``.
         ====================== ======= ====================================

        :param `spacing`: reserves a number of pixels before the window element;
        :param `leftSpacing`: an indent, in pixels;
        :param `rightSpacing`: a right spacing, only relevant when the style
         ``FPB_ALIGN_WIDTH`` is chosen.
        """

        wi = FoldWindowItem(self, window, Type="WINDOW", flags=flags, spacing=spacing,
                            leftSpacing=leftSpacing, rightSpacing=rightSpacing)

        self._items.append(wi)

        vertical = self.IsVertical()

        self._spacing = spacing
        self._leftSpacing = leftSpacing
        self._rightSpacing = rightSpacing

        xpos = (vertical and [leftSpacing] or [self._LastInsertPos + spacing])[0]
        ypos = (vertical and [self._LastInsertPos + spacing] or [leftSpacing])[0]

        window.SetSize(xpos, ypos, -1, -1, wx.SIZE_USE_EXISTING)

        self._LastInsertPos = self._LastInsertPos + wi.GetWindowLength(vertical)
        self.ResizePanel()


    def AddSeparator(self, colour=wx.BLACK, spacing=FPB_DEFAULT_SPACING,
                     leftSpacing=FPB_DEFAULT_LEFTSPACING,
                     rightSpacing=FPB_DEFAULT_RIGHTSPACING):
        """
        Adds a separator item to the list of items on this panel.

        :param `colour`: the separator colour, an instance of :class:`wx.Colour`;
        :param `spacing`: the separator to be added can be slightly indented from
         left and right so it is more visibly placed in the fold panel. Use `spacing` > 0
         to give the control an y offset from the previous :class:`wx.Window` added;
        :param `leftSpacing`: give the added separator a slight indent from the left;
        :param `rightSpacing`: give the added separator a slight indent from the right.
        """

        wi = FoldWindowItem(self, window=None, Type="SEPARATOR",
                            flags=FPB_ALIGN_WIDTH, y=self._LastInsertPos,
                            colour=colour, spacing=spacing, leftSpacing=leftSpacing,
                            rightSpacing=rightSpacing)

        self._items.append(wi)
        self._LastInsertPos = self._LastInsertPos + \
                              wi.GetWindowLength(self.IsVertical())

        self.ResizePanel()


    def Reposition(self, pos):
        """
        Repositions this :class:`FoldPanelItem` and reports the length occupied
        for the next :class:`FoldPanelItem` in the list.

        :param `pos`: the new item position.
        """

        # NOTE: Call Resize before Reposition when an item is added, because the new
        # size needed will be calculated by Resize. Of course the relative position
        # of the controls have to be correct in respect to the caption bar

        self.Freeze()

        vertical = self.IsVertical()
        xpos = (vertical and [-1] or [pos])[0]
        ypos = (vertical and [pos] or [-1])[0]

        self.SetSize(xpos, ypos, -1, -1, wx.SIZE_USE_EXISTING)
        self._itemPos = pos

        self.Thaw()

        return self.GetPanelLength()


    def OnPressCaption(self, event):
        """
        Handles the ``wx.EVT_CAPTIONBAR`` event for :class:`FoldPanelItem`.

        :param `event`: a :class:`CaptionBarEvent` event to be processed.
        """

        # tell the upper container we are responsible
        # for this event, so it can fold the panel item
        # and do a refresh

        event.SetTag(self)
        event.Skip()


    def ResizePanel(self):
        """ Resizes the panel. """

        # prevent unnecessary updates by blocking repaints for a sec

        self.Freeze()

        vertical = self.IsVertical()
        # force this panel to take the width of the parent panel and the y of the
        # user or calculated width (which will be recalculated by the contents here)


        if self._captionBar.IsCollapsed():
            size = self._captionBar.GetSize()
            self._PanelSize = (vertical and [size.GetHeight()] or [size.GetWidth()])[0]
        else:
            size = self.GetBestSize()
            self._PanelSize = (vertical and [size.GetHeight()] or [size.GetWidth()])[0]

            if self._UserSize:
                if vertical:
                    size.SetHeight(self._UserSize)
                else:
                    size.SetWidth(self._UserSize)

        pnlsize = self.GetParent().GetSize()

        if vertical:
            size.SetWidth(pnlsize.GetWidth())
        else:
            size.SetHeight(pnlsize.GetHeight())

        # resize caption bar
        xsize = (vertical and [size.GetWidth()] or [-1])[0]
        ysize = (vertical and [-1] or [size.GetHeight()])[0]

        self._captionBar.SetSize((xsize, ysize))

        # resize the panel
        self.SetSize(size)

        # go by all the controls and call Layout

        for items in self._items:
            items.ResizeItem((vertical and [size.GetWidth()] or \
                              [size.GetHeight()])[0], vertical)

        self.Thaw()


    def OnPaint(self, event):
        """
        Handles the ``wx.EVT_PAINT`` event for :class:`FoldPanelItem`.

        :param `event`: a :class:`PaintEvent` event to be processed.
        """

        # draw all the items that are lines

        dc = wx.PaintDC(self)
        vertical = self.IsVertical()

        for item in self._items:

            if item.GetType() == "SEPARATOR":
                pen = wx.Pen(item.GetLineColour(), 1, wx.PENSTYLE_SOLID)
                dc.SetPen(pen)
                a = item.GetLeftSpacing()
                b = item.GetLineY() + item.GetSpacing()
                c = item.GetLineLength()
                d = a + c

                if vertical:
                    dc.DrawLine(a, b, d, b)
                else:
                    dc.DrawLine(b, a, b, d)

        event.Skip()


    def IsVertical(self):
        """
        Returns whether the :class:`CaptionBar` has default orientation or not.
        Default is vertical.
        """

        # grandparent of FoldPanelItem is FoldPanelBar
        # default is vertical

        if isinstance(self.GetGrandParent(), FoldPanelBar):
            return self.GetGrandParent().IsVertical()
        else:
            raise Exception("ERROR: Wrong Parent " + repr(self.GetGrandParent()))


    def IsExpanded(self):
        """
        Returns expanded or collapsed status.  If the panel is
        expanded, ``True`` is returned.
        """

        return not self._captionBar.IsCollapsed()


    def GetItemPos(self):
        """ Returns item's position. """

        return self._itemPos


    def Collapse(self):
        """
        Internal method.

        This should not be called by the user, because it doesn't trigger the
        parent  to tell it that we are collapsed or expanded, it only changes
        visual state.
        """

        self._captionBar.Collapse()
        self.ResizePanel()


    def Expand(self):
        """
        Internal method.

        This should not be called by the user, because it doesn't trigger the
        parent to tell it that we are collapsed or expanded, it only changes
        visual state.
        """

        self._captionBar.Expand()
        self.ResizePanel()


    def GetPanelLength(self):
        """ Returns size of panel. """

        if self._captionBar.IsCollapsed():
            return self.GetCaptionLength()
        elif self._userSized:
            return self._UserSize

        return self._PanelSize


    def GetCaptionLength(self):
        """ Returns height of caption only. This is for folding calculation purposes. """

        size = self._captionBar.GetSize()
        return (self.IsVertical() and [size.GetHeight()] or [size.GetWidth()])[0]


    def ApplyCaptionStyle(self, cbstyle):
        """ Applies the style defined in `cbstyle` to the :class:`CaptionBar`."""

        self._captionBar.SetCaptionStyle(cbstyle)


    def GetCaptionStyle(self):
        """
        Returns the current style of the captionbar in a :class:`CaptionBarStyle` class.

        This can be used to change and set back the changes.
        """

        return self._captionBar.GetCaptionStyle()


# ----------------------------------------------------------------------------------- #
# class FoldWindowItem
# ----------------------------------------------------------------------------------- #

class FoldWindowItem(object):
    """
    This class is a child sibling of the :class:`FoldPanelItem` class. It
    will contain :class:`wx.Window` that can be either a separator (a coloured
    line simulated by a :class:`wx.Window`) or a wxPython controls (such as a
    :class:`Button`, a :class:`ListCtrl` etc...).
    """

    def __init__(self, parent, window=None, **kw):
        """
        Default class constructor

        :param `parent`: the :class:`FoldWindowItem` parent;
        :param `window`: the window contained in this item.

        :keyword `Type`: can be "WINDOW" or "SEPARATOR";
        :keyword `lineColour`: the separator colour (meaningful for separators only);
        :keyword `y`: the separator y position (meaningful for separators only);
        :keyword `flags`: the alignment flags;
        :keyword `spacing`: reserves a number of pixels before the window/separator element;
        :keyword `leftSpacing`: an indent, in pixels;
        :keyword `rightSpacing`: a right spacing, only relevant when the style
         ``FPB_ALIGN_WIDTH`` is chosen.

        :see: :meth:`FoldPanelBar.AddFoldPanelWindow() <FoldPanelBar.AddFoldPanelWindow>` for a list of valid alignment flags.
        """

        if "Type" not in kw:
            raise Exception('ERROR: Missing Window Type Information. This Should Be "WINDOW" Or "SEPARATOR"')

        if kw.get("Type") == "WINDOW":
            # Window constructor. This initialises the class as a wx.Window Type

            if "flags" in kw:
                self._flags = kw.get("flags")
            else:
                self._flags = FPB_ALIGN_WIDTH
            if "spacing" in kw:
                self._spacing = kw.get("spacing")
            else:
                self._spacing = FPB_DEFAULT_SPACING
            if "leftSpacing" in kw:
                self._leftSpacing = kw.get("leftSpacing")
            else:
                self._leftSpacing = FPB_DEFAULT_LEFTSPACING
            if "rightSpacing" in kw:
                self._rightSpacing = kw.get("rightSpacing")
            else:
                self._rightSpacing = FPB_DEFAULT_RIGHTSPACING

            self._lineY = 0
            self._sepLineColour = None
            self._wnd = window


        elif kw.get("Type") == "SEPARATOR":
            # separator constructor. This initialises the class as a separator type

            if "y" in kw:
                self._lineY = kw.get("y")
            else:
                raise Exception("ERROR: Undefined Y Position For The Separator")
            if "lineColour" in kw:
                self._sepLineColour = kw.get("lineColour")
            else:
                self._sepLineColour = wx.BLACK
            if "flags" in kw:
                self._flags = kw.get("flags")
            else:
                self._flags = FPB_ALIGN_WIDTH
            if "spacing" in kw:
                self._spacing = kw.get("spacing")
            else:
                self._spacing = FPB_DEFAULT_SPACING
            if "leftSpacing" in kw:
                self._leftSpacing = kw.get("leftSpacing")
            else:
                self._leftSpacing = FPB_DEFAULT_LEFTSPACING
            if "rightSpacing" in kw:
                self._rightSpacing = kw.get("rightSpacing")
            else:
                self._rightSpacing = FPB_DEFAULT_RIGHTSPACING

            self._wnd = window

        else:
            raise Exception("ERROR: Undefined Window Type Selected: " + repr(kw.get("Type")))

        self._type = kw.get("Type")
        self._lineLength = 0


    def GetType(self):
        """ Returns the :class:`FoldWindowItem` type. """

        return self._type


    def GetLineY(self):
        """ Returns the y position of the separator. """

        return self._lineY


    def GetLineLength(self):
        """ Returns the separator line length. """

        return self._lineLength


    def GetLineColour(self):
        """ Returns the separator line colour. """

        return self._sepLineColour


    def GetLeftSpacing(self):
        """ Returns the left indent of :class:`FoldWindowItem`. """

        return self._leftSpacing


    def GetRightSpacing(self):
        """ Returns the right indent of :class:`FoldWindowItem`. """

        return self._rightSpacing


    def GetSpacing(self):
        """ Returns the spacing of :class:`FoldWindowItem`. """

        return self._spacing


    def GetWindowLength(self, vertical=True):
        """
        Returns space needed by the window if type is :class:`FoldWindowItem`
        "WINDOW" and returns the total size plus the extra spacing.

        :param `vertical`: ``True`` if the parent :class:`FoldPanelBar` is in vertical
         mode.
        """

        value = 0
        if self._type == "WINDOW":
            size = self._wnd.GetSize()
            value = (vertical and [size.GetHeight()] or [size.GetWidth()])[0] + \
                    self._spacing

        elif self._type == "SEPARATOR":
            value = 1 + self._spacing

        return value


    def ResizeItem(self, size, vertical=True):
        """
        Resizes the element, whatever it is.

        A separator or line will be always aligned by width or height
        depending on orientation of the whole panel.

        :param `size`: the maximum size available for the :class:`FoldWindowItem`;
        :param `vertical`: ``True`` if the parent :class:`FoldPanelBar` is in vertical
         mode.
        """

        if self._flags & FPB_ALIGN_WIDTH:
            # align by taking full width
            mySize = size - self._leftSpacing - self._rightSpacing

            if mySize < 0:
                mySize = 10  # can't have negative width

            if self._type == "SEPARATOR":
                self._lineLength = mySize
            else:
                xsize = (vertical and [mySize] or [-1])[0]
                ysize = (vertical and [-1] or [mySize])[0]

                self._wnd.SetSize((xsize, ysize))


if __name__ == '__main__':

    import wx

    class MyFrame(wx.Frame):

        def __init__(self, parent):

            wx.Frame.__init__(self, parent, -1, "FoldPanelBar Demo")

            text_ctrl = wx.TextCtrl(self, -1, size=(400, 100), style=wx.TE_MULTILINE)

            panel_bar = FoldPanelBar(self, -1, agwStyle=FPB_VERTICAL)

            fold_panel = panel_bar.AddFoldPanel("Thing")
            thing = wx.TextCtrl(fold_panel, -1, size=(400, -1), style=wx.TE_MULTILINE)

            panel_bar.AddFoldPanelWindow(fold_panel, thing)

            main_sizer = wx.BoxSizer(wx.VERTICAL)
            main_sizer.Add(text_ctrl, 1, wx.EXPAND)
            main_sizer.Add(panel_bar, 1, wx.EXPAND)
            self.SetSizer(main_sizer)


    # our normal wxApp-derived class, as usual

    app = wx.App(0)

    frame = MyFrame(None)
    app.SetTopWindow(frame)
    frame.Show()

    app.MainLoop()
