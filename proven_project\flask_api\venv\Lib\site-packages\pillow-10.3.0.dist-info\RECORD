PIL/BdfFontFile.py,sha256=eOiPya2ngPj43BlnsoBAhA-m5a8tn2-b-jpHFy9WIIQ,3602
PIL/BlpImagePlugin.py,sha256=fkdwoq9-33R-PVaZK4rwLUECvvVUKH9QSN4IcegPoyE,16032
PIL/BmpImagePlugin.py,sha256=dvuz1d0WW6okH1l37-nDudX7Hi6CaQ1YfwMAILUR8Aw,18205
PIL/BufrStubImagePlugin.py,sha256=wlxSRFKTh1Pe1ihNhjwlU5jqfzQJml6S125-5O8unYg,1666
PIL/ContainerIO.py,sha256=BTz6Qlz-VyDmurXnWpQU-lAevLxgcsOcEGZP0CtvSKc,3302
PIL/CurImagePlugin.py,sha256=i5RavrbD_Dmm-sGTGi4GBebFysK-XvRr5mm8jMeu7qY,1816
PIL/DcxImagePlugin.py,sha256=NJLUi8N5yt1xojdPZmpozC6Xq39k1K9KxBJpMVbAXhA,2073
PIL/DdsImagePlugin.py,sha256=bsAI3Wz2FTswlN7Qp2ryKuZOMI8VNYAvemUxHunV5AU,17283
PIL/EpsImagePlugin.py,sha256=NyaEPif3KuSdPnrUoDhI4fh2VDrNslVOzwJ8rKIQCOU,16283
PIL/ExifTags.py,sha256=LvHb4g-nwrIP0mPY7tGeNw41cLFihc0-VRmNTADvLCo,10134
PIL/FitsImagePlugin.py,sha256=Ezm1Ch4FyMhTxKBNX89OTgrEKdy9OET8odQHiqyFrmA,4638
PIL/FliImagePlugin.py,sha256=pToYAjyIyuoe8aWr_YBVVMAYXi5jXrtuhf4uakKb3l8,4737
PIL/FontFile.py,sha256=iLSV32yQetLnE4SgG8HnHb2FdqkqFBjY9n--E6u5UE0,3711
PIL/FpxImagePlugin.py,sha256=nIdCc6vAWAbUhC7zGp4I26HdbkIYvBplEHtDtRgEvtc,7238
PIL/FtexImagePlugin.py,sha256=ceA1P4ucQa9mCGY2x0LQTg6haq1ESKVekgnlk20EwOU,3581
PIL/GbrImagePlugin.py,sha256=S6JTUcCgjYV6sRdVrc9ZiPTr5kcLuVtPlYU21kjApRU,3048
PIL/GdImageFile.py,sha256=5SZA0952NckwJYsproC-ykj_UfVUjiGxNiohzRYf2fE,2897
PIL/GifImagePlugin.py,sha256=aCA1mtOI-GCBqn2mEjjuPWWVi-8KKjo2sVe_2KgTLSw,38827
PIL/GimpGradientFile.py,sha256=Uxi1xSOmyfHi6RJhbyPD-Uuq6yvhirAT98RjvdOAeyY,3567
PIL/GimpPaletteFile.py,sha256=mPqJ60BA6-bX5fza39rSQ1zGUqMiAeqx-eRX3ZRrD1A,1437
PIL/GribStubImagePlugin.py,sha256=0tq7qbqox15DBxH-LSoqnAYmaPMQbu55-vJisJbxPEg,1660
PIL/Hdf5StubImagePlugin.py,sha256=TTWZfdfzIhKMwbIa3lDqcUki7wlT-GI3JL4eb8t9iwM,1663
PIL/IcnsImagePlugin.py,sha256=eypnvxilZLtobRfKdUo72gdJIhvp_cdIPf7u8Yf7UbA,12396
PIL/IcoImagePlugin.py,sha256=pGiF7ItnDHQuhLqvPUEQe2ll08DwkRx4CL7YwruuxT4,11998
PIL/ImImagePlugin.py,sha256=F3em4QykU3owg-8ZgBsBBp7XgIkgHwjXZtkC51SwO-8,11275
PIL/Image.py,sha256=Wf4zYjFbr6KAvhBuPZE0SsdPeEuS3lTsGldiM5MNqAs,141051
PIL/ImageChops.py,sha256=hZ8EPUPlQIzugsEedV8trkKX0jBCDGb6Cszma6ZeMZQ,8257
PIL/ImageCms.py,sha256=k57KudvQqQbFYMDKLEfK17Der085g-CaU8vMmi5pTz0,42455
PIL/ImageColor.py,sha256=GplI-TAcZMmufd-06Jy40hllJbS4cEsfaG3LGuP3_Dk,9514
PIL/ImageDraw.py,sha256=YRBKtPdhR2X0xY_p3cvNy7rR4B_1H5i2bHdM-CLf8BI,38849
PIL/ImageDraw2.py,sha256=xfb7_k7u6rcWUUAdyCnDwL1saBmvJ5YDEPDUNnPAGMU,5728
PIL/ImageEnhance.py,sha256=ZqE_R7iZaxDOuV9Y0taSakmoyP-s4xJ5OizSdBgh-0M,3329
PIL/ImageFile.py,sha256=2suZ89rl2RxwQSxm2hucNwCJlBLxsknrBXbsmmVFD_E,25218
PIL/ImageFilter.py,sha256=7sKp_veu-mAyUuPWCstCEs1oiVpUuK5NUuygcXST0F4,17598
PIL/ImageFont.py,sha256=mVWmRjgDlM4lVAIKyeOdyjYeHASew_gl69RDHhYk9SM,61820
PIL/ImageGrab.py,sha256=_PLbpsP4X5qZPdFMCzYaFOedTiGT_wzHOVs5WbzoGPs,6284
PIL/ImageMath.py,sha256=Jf7Fu0bRtQ3euo_v6En9X7dZvU9wghHyCGWYp7wrtfY,11837
PIL/ImageMode.py,sha256=XMCH0yS-ZslCUKo5_hMOmDCr-_e08OZuPhTQGCbqS3Y,2866
PIL/ImageMorph.py,sha256=p2gMoPzbgi9dMhPDwsIaYzEM8PTz7eiavk0uNX4dK90,8748
PIL/ImageOps.py,sha256=RT7ngnAap7iQmeO9Pt9K_ExXeoEOrag01q1izWDMdA0,25496
PIL/ImagePalette.py,sha256=80vc6yoQW46QCicpi0G6wq_j2FSE_cAYNyjeEv_YP4Q,8128
PIL/ImagePath.py,sha256=ZnnJuvQNtbKRhCmr61TEmEh1vVV5_90WMEPL8Opy5l8,391
PIL/ImageQt.py,sha256=iyzXxYgZYf9Y27J-pxeaRpRnkJyu6y9ZUwtfofmMukw,6157
PIL/ImageSequence.py,sha256=jyVU7FmpmrvkBASsZIN-fI21g9DUcCzmSmj0FxS4Fng,2278
PIL/ImageShow.py,sha256=Fb-malG0qQHgjt0t__CU7U8dwau_0KVvgd27-qaRF3k,9792
PIL/ImageStat.py,sha256=lnzEWMbFmxBu-kuWVCaOZRT_qH-AJBRVE0OL3toQg-M,3853
PIL/ImageTk.py,sha256=tFvNW2NHf84TKr7mxE0_bosk_IUo3I7Iw6PjvNQMyY4,8780
PIL/ImageTransform.py,sha256=6Oq-8knArMY9p5twoNV_J6GYN6FewZMWXuZ3TbBZnHI,4036
PIL/ImageWin.py,sha256=rYZTiv3ahrz2f52dGeZPMEWkJ8riiT0kchFU5f7OVV0,7457
PIL/ImtImagePlugin.py,sha256=TFLgRU0ko5gAxrsKUqGkK_Y8o233OVKLNJKE4IeOac8,2761
PIL/IptcImagePlugin.py,sha256=a7nQ5VkZHUgrR22VUp2ACYrVM1RTPck5a3EqspEgDzw,6370
PIL/Jpeg2KImagePlugin.py,sha256=DcPcERXNHtomyN1zJO_gbROhNQFZVbXlslxKdCXHE5M,12317
PIL/JpegImagePlugin.py,sha256=BKpHWkErNvKZeCymF95Os-oAAWImLLBoCKmtD0MffUs,30446
PIL/JpegPresets.py,sha256=0XoRcIdU_U1szfxfQEt_YgarmdB1INfTpbEgCanBbns,12664
PIL/McIdasImagePlugin.py,sha256=KNmMyMzyaBz_pjUhtxi0Fjtj6MdjzrT-b1P_LgC10gg,1979
PIL/MicImagePlugin.py,sha256=KfxwPw4cfn5qzKLxjrSqkntAGrJNjXBFmwWLXmMathQ,2722
PIL/MpegImagePlugin.py,sha256=jZVKPSiXk4gKVZUbzbUuDQUJpMUw8_W-uQAUeiTIt6U,2092
PIL/MpoImagePlugin.py,sha256=Jvl6Dja-YAnNr6yTeSU3ctWIugK2YwXzOLo9nNqBIFc,5969
PIL/MspImagePlugin.py,sha256=hcKvTMBxZlEURZehbuKIV8t3Dc9ZPMY4LklaWx-cSEA,6028
PIL/PSDraw.py,sha256=XcnamR_C_wE94qVU9mVChdxPGTLsKfkQyyqZphWmuHY,6790
PIL/PaletteFile.py,sha256=nTk5yXjngne55qRAsTk8SIbbWorE_ljw_9qAZ8orCVk,1215
PIL/PalmImagePlugin.py,sha256=YoMpei-CYeeZEdGIIlPkaIeJVwE5cxSYLdHhpLxkOUk,9405
PIL/PcdImagePlugin.py,sha256=F_8URHMLJxGt_MhONZzako6F3qYcC6RkUZrKgr0FjTM,1689
PIL/PcfFontFile.py,sha256=RkM5wUp3SgRpQhpsTBEtk8uuFrQPnBSYBryOmcoRphQ,7401
PIL/PcxImagePlugin.py,sha256=0XsvYIZru4nwPOpRe0CCJ5qCeuWuAJ-0Cb442AmuBlE,6436
PIL/PdfImagePlugin.py,sha256=QAHjwSlDVAck85aLy0d5-QiMI89jjVBggdxEDr3Ouzw,9152
PIL/PdfParser.py,sha256=x2RwjMql4jDRgqcbNgLGD-ddWBSbQb7WRKPO1Ysy7-I,35694
PIL/PixarImagePlugin.py,sha256=90zIgbzb8-bACCrJtQD2ubQmp_x5jGBOoWpjsS7Y038,1818
PIL/PngImagePlugin.py,sha256=nQCobjUtCziWEZYYxtK7IaNT4g7yseIkLbHLhLnAXQY,48352
PIL/PpmImagePlugin.py,sha256=iq6SHqvB8_XPkIghWuVnmg3JBwSzytDZgZlruKZ2D9E,12579
PIL/PsdImagePlugin.py,sha256=b8Wh5uDablJHG36lGyR5KKuYq88PF4TBRMn_xT1SXr4,8012
PIL/PyAccess.py,sha256=f7xhjB3-WpRNha4txNm9nx0OphEP7LdbUsVuaPsX9Dw,10284
PIL/QoiImagePlugin.py,sha256=wk0iPjWfksiUwBR7Oh8Ojvv8zOf9xgzTHK83vbE2M6U,3928
PIL/SgiImagePlugin.py,sha256=m6ktCmk901-DRWyfTBYm2CfTM-e5kHKc2PltgnzYRO4,6636
PIL/SpiderImagePlugin.py,sha256=X9_JONb350b0pY98RV-Iik9uBbxuLWCAltS-tIoa8zg,9785
PIL/SunImagePlugin.py,sha256=JT8IrJC7JIDBgdfIBzC-HSzxnoD4-W1aybpBDW0L0aU,4640
PIL/TarIO.py,sha256=57Vykz4J7Tvey9X1o9Wb2-MCZ14ditGStpQtbpaG_d4,1812
PIL/TgaImagePlugin.py,sha256=VTESnvGWjByXq7H1YjeVmsPTJDhWjyWHfXvQfvDJlSo,7189
PIL/TiffImagePlugin.py,sha256=DewO_3m_FCGAzaEFGP_kAXFUhuJo6L-c42A4bEXuW7s,79378
PIL/TiffTags.py,sha256=1p_pgfh-GuSav3LWesRkYB0HyLNkbvBTpTsqeQA9VMM,17232
PIL/WalImageFile.py,sha256=ot4zGTEp8udYiLYLfOlBrXWo_OSaCO2Bjq-G0Mft5Cs,5679
PIL/WebPImagePlugin.py,sha256=U2nwKiW1cYfG5MAIB0lp7-dz78BQRyU7cgCtb1SpJmg,11892
PIL/WmfImagePlugin.py,sha256=K0RRLB76i_DLBA93_K1Z2SrMnm7hL79eaAEquYE9Ftc,4905
PIL/XVThumbImagePlugin.py,sha256=nlqdy2bGDbqG9Sl-62YQcfN0x8JAUgUQDo5a3CrNPQU,2162
PIL/XbmImagePlugin.py,sha256=jcIu824UIAe7zuuJFAosiT3Qt0cmxQOGWkifZEaM0nk,2739
PIL/XpmImagePlugin.py,sha256=RSBr0PeyL9przum1p-FEqo3iuAPXGI8NbPAwMDUBPwM,3352
PIL/__init__.py,sha256=98abxVfn8od1jJaTIr65YrYrIb7zMKbOJ5o68ryE2O0,2094
PIL/__main__.py,sha256=X8eIpGlmHfnp7zazp5mdav228Itcf2lkiMP0tLU6X9c,140
PIL/__pycache__/BdfFontFile.cpython-312.pyc,,
PIL/__pycache__/BlpImagePlugin.cpython-312.pyc,,
PIL/__pycache__/BmpImagePlugin.cpython-312.pyc,,
PIL/__pycache__/BufrStubImagePlugin.cpython-312.pyc,,
PIL/__pycache__/ContainerIO.cpython-312.pyc,,
PIL/__pycache__/CurImagePlugin.cpython-312.pyc,,
PIL/__pycache__/DcxImagePlugin.cpython-312.pyc,,
PIL/__pycache__/DdsImagePlugin.cpython-312.pyc,,
PIL/__pycache__/EpsImagePlugin.cpython-312.pyc,,
PIL/__pycache__/ExifTags.cpython-312.pyc,,
PIL/__pycache__/FitsImagePlugin.cpython-312.pyc,,
PIL/__pycache__/FliImagePlugin.cpython-312.pyc,,
PIL/__pycache__/FontFile.cpython-312.pyc,,
PIL/__pycache__/FpxImagePlugin.cpython-312.pyc,,
PIL/__pycache__/FtexImagePlugin.cpython-312.pyc,,
PIL/__pycache__/GbrImagePlugin.cpython-312.pyc,,
PIL/__pycache__/GdImageFile.cpython-312.pyc,,
PIL/__pycache__/GifImagePlugin.cpython-312.pyc,,
PIL/__pycache__/GimpGradientFile.cpython-312.pyc,,
PIL/__pycache__/GimpPaletteFile.cpython-312.pyc,,
PIL/__pycache__/GribStubImagePlugin.cpython-312.pyc,,
PIL/__pycache__/Hdf5StubImagePlugin.cpython-312.pyc,,
PIL/__pycache__/IcnsImagePlugin.cpython-312.pyc,,
PIL/__pycache__/IcoImagePlugin.cpython-312.pyc,,
PIL/__pycache__/ImImagePlugin.cpython-312.pyc,,
PIL/__pycache__/Image.cpython-312.pyc,,
PIL/__pycache__/ImageChops.cpython-312.pyc,,
PIL/__pycache__/ImageCms.cpython-312.pyc,,
PIL/__pycache__/ImageColor.cpython-312.pyc,,
PIL/__pycache__/ImageDraw.cpython-312.pyc,,
PIL/__pycache__/ImageDraw2.cpython-312.pyc,,
PIL/__pycache__/ImageEnhance.cpython-312.pyc,,
PIL/__pycache__/ImageFile.cpython-312.pyc,,
PIL/__pycache__/ImageFilter.cpython-312.pyc,,
PIL/__pycache__/ImageFont.cpython-312.pyc,,
PIL/__pycache__/ImageGrab.cpython-312.pyc,,
PIL/__pycache__/ImageMath.cpython-312.pyc,,
PIL/__pycache__/ImageMode.cpython-312.pyc,,
PIL/__pycache__/ImageMorph.cpython-312.pyc,,
PIL/__pycache__/ImageOps.cpython-312.pyc,,
PIL/__pycache__/ImagePalette.cpython-312.pyc,,
PIL/__pycache__/ImagePath.cpython-312.pyc,,
PIL/__pycache__/ImageQt.cpython-312.pyc,,
PIL/__pycache__/ImageSequence.cpython-312.pyc,,
PIL/__pycache__/ImageShow.cpython-312.pyc,,
PIL/__pycache__/ImageStat.cpython-312.pyc,,
PIL/__pycache__/ImageTk.cpython-312.pyc,,
PIL/__pycache__/ImageTransform.cpython-312.pyc,,
PIL/__pycache__/ImageWin.cpython-312.pyc,,
PIL/__pycache__/ImtImagePlugin.cpython-312.pyc,,
PIL/__pycache__/IptcImagePlugin.cpython-312.pyc,,
PIL/__pycache__/Jpeg2KImagePlugin.cpython-312.pyc,,
PIL/__pycache__/JpegImagePlugin.cpython-312.pyc,,
PIL/__pycache__/JpegPresets.cpython-312.pyc,,
PIL/__pycache__/McIdasImagePlugin.cpython-312.pyc,,
PIL/__pycache__/MicImagePlugin.cpython-312.pyc,,
PIL/__pycache__/MpegImagePlugin.cpython-312.pyc,,
PIL/__pycache__/MpoImagePlugin.cpython-312.pyc,,
PIL/__pycache__/MspImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PSDraw.cpython-312.pyc,,
PIL/__pycache__/PaletteFile.cpython-312.pyc,,
PIL/__pycache__/PalmImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PcdImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PcfFontFile.cpython-312.pyc,,
PIL/__pycache__/PcxImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PdfImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PdfParser.cpython-312.pyc,,
PIL/__pycache__/PixarImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PngImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PpmImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PsdImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PyAccess.cpython-312.pyc,,
PIL/__pycache__/QoiImagePlugin.cpython-312.pyc,,
PIL/__pycache__/SgiImagePlugin.cpython-312.pyc,,
PIL/__pycache__/SpiderImagePlugin.cpython-312.pyc,,
PIL/__pycache__/SunImagePlugin.cpython-312.pyc,,
PIL/__pycache__/TarIO.cpython-312.pyc,,
PIL/__pycache__/TgaImagePlugin.cpython-312.pyc,,
PIL/__pycache__/TiffImagePlugin.cpython-312.pyc,,
PIL/__pycache__/TiffTags.cpython-312.pyc,,
PIL/__pycache__/WalImageFile.cpython-312.pyc,,
PIL/__pycache__/WebPImagePlugin.cpython-312.pyc,,
PIL/__pycache__/WmfImagePlugin.cpython-312.pyc,,
PIL/__pycache__/XVThumbImagePlugin.cpython-312.pyc,,
PIL/__pycache__/XbmImagePlugin.cpython-312.pyc,,
PIL/__pycache__/XpmImagePlugin.cpython-312.pyc,,
PIL/__pycache__/__init__.cpython-312.pyc,,
PIL/__pycache__/__main__.cpython-312.pyc,,
PIL/__pycache__/_binary.cpython-312.pyc,,
PIL/__pycache__/_deprecate.cpython-312.pyc,,
PIL/__pycache__/_tkinter_finder.cpython-312.pyc,,
PIL/__pycache__/_typing.cpython-312.pyc,,
PIL/__pycache__/_util.cpython-312.pyc,,
PIL/__pycache__/_version.cpython-312.pyc,,
PIL/__pycache__/features.cpython-312.pyc,,
PIL/__pycache__/report.cpython-312.pyc,,
PIL/_binary.py,sha256=cb9p-_mwzBYumlVsWbnoTWsrLo59towA6atLOZvjO3w,2662
PIL/_deprecate.py,sha256=5WrrZE3Q65nRF3pwwRN9wsmY4lqFOJayT6Uxt-i9tf0,2071
PIL/_imaging.cp312-win_amd64.pyd,sha256=ueQecTfPx7hz6WraHEc7q_1hbQrXh4Ihu2jEO3AZAGc,2331648
PIL/_imaging.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_imagingcms.cp312-win_amd64.pyd,sha256=oLszKY3MUtqWb3qTTEg44y4gQ46425KdStEv0EL9CSI,262656
PIL/_imagingcms.pyi,sha256=yWAXWNFGvBLu0qE-tCcX8I6KqBPzXF26MRAQjEqWJXQ,4578
PIL/_imagingft.cp312-win_amd64.pyd,sha256=v5FP5K_7qkOqgeIOXAUKgILtgf82QTqm17KMHxeja28,1799168
PIL/_imagingft.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_imagingmath.cp312-win_amd64.pyd,sha256=fzUw5s6ZWCyydb4Vu8e-XgvDwa7qvxtsOvCflmj2UoQ,24064
PIL/_imagingmath.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_imagingmorph.cp312-win_amd64.pyd,sha256=B2-9XMbMgGeG2o-WCfBct6lZAFpVlc4zOH8XcSEzWQs,13312
PIL/_imagingmorph.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_imagingtk.cp312-win_amd64.pyd,sha256=Gvow3ul4joPA1FZ4ySHhq59Fun3_PoRTPRjmKSj691Y,14848
PIL/_tkinter_finder.py,sha256=jKydPAxnrytggsZQHB6kAQep6A9kzRNyx_nToT4ClKY,561
PIL/_typing.py,sha256=7xejt4npjlT2_nXbUiJ8LCg6LcCa-eY9C_LFLWoKovk,809
PIL/_util.py,sha256=ifUUlojtqTnWOxQFrwNCpqO1gjzkFIWovj7uBnq6DrY,844
PIL/_version.py,sha256=NMkBRjrxaAOp-khUUL4kJITCOlk_jbuLfChl6AHnStE,91
PIL/_webp.cp312-win_amd64.pyd,sha256=01sEM7Eo6dv5AMrA-Pc-eHNd5xe0bIyjvRWpTaE1Yg0,407552
PIL/_webp.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/features.py,sha256=cVk2qCZDxbfMDMVWebDtY1CzIf0zlSR_5sNJCwBx7Ko,10499
PIL/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
PIL/report.py,sha256=6m7NOv1a24577ZiJoxX89ip5JeOgf2O1F95f6-1K5aM,105
pillow-10.3.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pillow-10.3.0.dist-info/LICENSE,sha256=1oQxavDU2-TxBdNmMTMW80YoXqQ_AeFm1229LQUjmMM,56527
pillow-10.3.0.dist-info/METADATA,sha256=zkrKLKWZ1ZfEWEQIhILvcdCHAsQDK42tNpY2ySjikSQ,9375
pillow-10.3.0.dist-info/RECORD,,
pillow-10.3.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pillow-10.3.0.dist-info/WHEEL,sha256=fZWyj_84lK0cA-ZNCsdwhbJl0OTrpWkxInEn424qrSs,102
pillow-10.3.0.dist-info/top_level.txt,sha256=riZqrk-hyZqh5f1Z0Zwii3dKfxEsByhu9cU9IODF-NY,4
pillow-10.3.0.dist-info/zip-safe,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
