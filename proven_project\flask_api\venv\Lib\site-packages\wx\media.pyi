# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
The ``wx.media`` module provides a widget class that allows displaying various
types of media, such as video and audio files and streaming, using native
system components.  The wxWidgets media classes are an optional part of the
build so it may not always be available on your build of wxPython.
"""
#-- begin-_media --#

import wx
#-- end-_media --#
#-- begin-mediactrl --#
MC_NO_AUTORESIZE: int

class _MediaState(IntEnum):
    MEDIASTATE_STOPPED = auto()
    MEDIASTATE_PAUSED = auto()
    MEDIASTATE_PLAYING = auto()
MediaState: TypeAlias = Union[_MediaState, int]
MEDIASTATE_STOPPED = _MediaState.MEDIASTATE_STOPPED
MEDIASTATE_PAUSED = _MediaState.MEDIASTATE_PAUSED
MEDIASTATE_PLAYING = _MediaState.MEDIASTATE_PLAYING

class _MediaCtrlPlayerControls(IntEnum):
    MEDIACTRLPLAYERCONTROLS_NONE = auto()
    MEDIACTRLPLAYERCONTROLS_STEP = auto()
    MEDIACTRLPLAYERCONTROLS_VOLUME = auto()
    MEDIACTRLPLAYERCONTROLS_DEFAULT = auto()
MediaCtrlPlayerControls: TypeAlias = Union[_MediaCtrlPlayerControls, int]
MEDIACTRLPLAYERCONTROLS_NONE = _MediaCtrlPlayerControls.MEDIACTRLPLAYERCONTROLS_NONE
MEDIACTRLPLAYERCONTROLS_STEP = _MediaCtrlPlayerControls.MEDIACTRLPLAYERCONTROLS_STEP
MEDIACTRLPLAYERCONTROLS_VOLUME = _MediaCtrlPlayerControls.MEDIACTRLPLAYERCONTROLS_VOLUME
MEDIACTRLPLAYERCONTROLS_DEFAULT = _MediaCtrlPlayerControls.MEDIACTRLPLAYERCONTROLS_DEFAULT
wxEVT_MEDIA_LOADED: int
wxEVT_MEDIA_STOP: int
wxEVT_MEDIA_FINISHED: int
wxEVT_MEDIA_STATECHANGED: int
wxEVT_MEDIA_PLAY: int
wxEVT_MEDIA_PAUSE: int

class MediaCtrl(wx.Control):
    """
    MediaCtrl() -> None
    MediaCtrl(parent, id=-1, fileName='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, szBackend='', validator=wx.DefaultValidator, name="mediaCtrl") -> None
    
    wxMediaCtrl is a class for displaying various types of media, such as
    videos, audio files, natively through native codecs.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=-1, fileName: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, szBackend: str='', validator: wx.Validator=wx.DefaultValidator, name: str="mediaCtrl") -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        MediaCtrl() -> None
        MediaCtrl(parent, id=-1, fileName='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, szBackend='', validator=wx.DefaultValidator, name="mediaCtrl") -> None
        
        wxMediaCtrl is a class for displaying various types of media, such as
        videos, audio files, natively through native codecs.
        """

    def Create(self, parent: wx.Window, id: int=-1, fileName: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, szBackend: str='', validator: wx.Validator=wx.DefaultValidator, name: str="mediaCtrl") -> bool:
        """
        Create(parent, id=-1, fileName='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, szBackend='', validator=wx.DefaultValidator, name="mediaCtrl") -> bool
        
        Creates this control.
        """

    def GetBestSize(self) -> wx.Size:
        """
        GetBestSize() -> wx.Size
        
        Obtains the best size relative to the original/natural size of the
        video, if there is any.
        """

    def GetPlaybackRate(self) -> float:
        """
        GetPlaybackRate() -> float
        
        Obtains the playback rate, or speed of the media.
        """

    def GetState(self) -> MediaState:
        """
        GetState() -> MediaState
        
        Obtains the state the playback of the media is in.
        """

    def GetVolume(self) -> float:
        """
        GetVolume() -> float
        
        Gets the volume of the media from a 0.0 to 1.0 range.
        """

    def Length(self) -> FileOffset:
        """
        Length() -> FileOffset
        
        Obtains the length - the total amount of time the media has in
        milliseconds.
        """

    def Load(self, fileName: str) -> bool:
        """
        Load(fileName) -> bool
        
        Loads the file that fileName refers to.
        """

    def LoadURI(self, uri: str) -> bool:
        """
        LoadURI(uri) -> bool
        
        Loads the location that uri refers to.
        """

    def LoadURIWithProxy(self, uri: str, proxy: str) -> bool:
        """
        LoadURIWithProxy(uri, proxy) -> bool
        
        Loads the location that uri refers to with the proxy proxy.
        """

    def Pause(self) -> bool:
        """
        Pause() -> bool
        
        Pauses playback of the media.
        """

    def Play(self) -> bool:
        """
        Play() -> bool
        
        Resumes playback of the media.
        """

    def Seek(self, where: FileOffset, mode: wx.SeekMode=wx.FromStart) -> FileOffset:
        """
        Seek(where, mode=wx.FromStart) -> FileOffset
        
        Seeks to a position within the media.
        """

    def SetPlaybackRate(self, dRate: float) -> bool:
        """
        SetPlaybackRate(dRate) -> bool
        
        Sets the playback rate, or speed of the media, to that referred by
        dRate.
        """

    def SetVolume(self, dVolume: float) -> bool:
        """
        SetVolume(dVolume) -> bool
        
        Sets the volume of the media from a 0.0 to 1.0 range to that referred
        by dVolume.
        """

    def ShowPlayerControls(self, flags: MediaCtrlPlayerControls=MEDIACTRLPLAYERCONTROLS_DEFAULT) -> bool:
        """
        ShowPlayerControls(flags=MEDIACTRLPLAYERCONTROLS_DEFAULT) -> bool
        
        A special feature to wxMediaCtrl.
        """

    def Stop(self) -> bool:
        """
        Stop() -> bool
        
        Stops the media.
        """

    def Tell(self) -> FileOffset:
        """
        Tell() -> FileOffset
        
        Obtains the current position in time within the media in milliseconds.
        """
    @property
    def BestSize(self) -> wx.Size: ...
    @property
    def PlaybackRate(self) -> float: ...
    @PlaybackRate.setter
    def PlaybackRate(self, value: float, /) -> None: ...
    @property
    def State(self) -> MediaState: ...
    @property
    def Volume(self) -> float: ...
    @Volume.setter
    def Volume(self, value: float, /) -> None: ...
# end of class MediaCtrl


class MediaEvent(wx.NotifyEvent):
    """
    MediaEvent(commandType=wx.wxEVT_NULL, winid=0) -> None
    
    Event wxMediaCtrl uses.
    """

    def __init__(self, commandType: EventType=wx.wxEVT_NULL, winid: int=0) -> None:
        """
        MediaEvent(commandType=wx.wxEVT_NULL, winid=0) -> None
        
        Event wxMediaCtrl uses.
        """
# end of class MediaEvent

USE_MEDIACTRL: int

EVT_MEDIA_LOADED = wx.PyEventBinder( wxEVT_MEDIA_LOADED )
EVT_MEDIA_STOP = wx.PyEventBinder( wxEVT_MEDIA_STOP )
EVT_MEDIA_FINISHED = wx.PyEventBinder( wxEVT_MEDIA_FINISHED )
EVT_MEDIA_STATECHANGED = wx.PyEventBinder( wxEVT_MEDIA_STATECHANGED )
EVT_MEDIA_PLAY = wx.PyEventBinder( wxEVT_MEDIA_PLAY )
EVT_MEDIA_PAUSE = wx.PyEventBinder( wxEVT_MEDIA_PAUSE )

MEDIABACKEND_DIRECTSHOW = "wxAMMediaBackend"
MEDIABACKEND_MCI        = "wxMCIMediaBackend"
MEDIABACKEND_QUICKTIME  = "wxQTMediaBackend"
MEDIABACKEND_GSTREAMER  = "wxGStreamerMediaBackend"
MEDIABACKEND_REALPLAYER = "wxRealPlayerMediaBackend"
MEDIABACKEND_WMP10      = "wxWMP10MediaBackend"
#-- end-mediactrl --#
