<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>APIS TrustFinger - MultiFinger Bridge Interface</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f0f0;
    }
    
    .main-container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 20px;
      font-size: 24px;
    }

    /* Top Controls Section */
    .top-section {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }
    
    .control-box {
      border: 2px solid #ccc;
      padding: 15px;
      flex: 1;
      border-radius: 5px;
    }
    
    .control-box h3 {
      margin-top: 0;
      font-size: 14px;
      color: #333;
    }

    /* Device Controls */
    .device-controls {
      margin-bottom: 15px;
    }
    
    .btn {
      padding: 8px 16px;
      margin: 3px;
      border: 1px solid #ccc;
      background: #f8f9fa;
      cursor: pointer;
      font-size: 12px;
      border-radius: 3px;
    }
    
    .btn:hover {
      background: #e9ecef;
    }
    
    .btn:disabled {
      background: #6c757d;
      color: white;
      cursor: not-allowed;
    }
    
    .btn-primary {
      background: #007bff;
      color: white;
      border-color: #007bff;
    }
    
    .btn-success {
      background: #28a745;
      color: white;
      border-color: #28a745;
    }
    
    .btn-info {
      background: #17a2b8;
      color: white;
      border-color: #17a2b8;
    }

    /* Input Fields */
    .input-row {
      margin: 10px 0;
    }
    
    .input-row label {
      display: inline-block;
      width: 80px;
      font-size: 12px;
    }
    
    .input-row input, .input-row select {
      padding: 4px;
      font-size: 12px;
      width: 200px;
    }

    /* Finger Selection Grid */
    .finger-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 10px;
      margin: 15px 0;
    }
    
    .finger-item {
      text-align: center;
    }
    
    .finger-item input[type="checkbox"] {
      margin-bottom: 5px;
    }
    
    .finger-item label {
      display: block;
      font-size: 11px;
    }

    /* Device Info Display */
    .device-info {
      font-size: 11px;
      margin: 10px 0;
    }
    
    .device-info-row {
      margin: 3px 0;
    }
    
    .device-info-row label {
      display: inline-block;
      width: 100px;
    }

    /* Message Display */
    .message {
      padding: 8px;
      margin: 10px 0;
      border-radius: 3px;
      font-size: 12px;
    }
    
    .message-success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .message-error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .message-info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    /* Tab Control */
    .tab-control {
      margin-top: 20px;
      border: 1px solid #ccc;
      border-radius: 5px;
    }
    
    .tab-headers {
      display: flex;
      background: #f8f9fa;
      border-bottom: 1px solid #ccc;
      border-radius: 5px 5px 0 0;
    }
    
    .tab-header {
      padding: 8px 16px;
      border-right: 1px solid #ccc;
      cursor: pointer;
      font-size: 12px;
      background: #f8f9fa;
    }
    
    .tab-header:last-child {
      border-right: none;
    }
    
    .tab-header.active {
      background: white;
      border-bottom: 1px solid white;
      margin-bottom: -1px;
    }
    
    .tab-content {
      padding: 20px;
      min-height: 400px;
      background: white;
    }

    /* Image Display Areas */
    .image-display {
      display: flex;
      gap: 20px;
      margin: 20px 0;
      justify-content: center;
    }
    
    .image-box {
      text-align: center;
      flex: 1;
      max-width: 300px;
    }
    
    .image-box h4 {
      font-size: 14px;
      margin-bottom: 10px;
    }
    
    .image-preview {
      width: 280px;
      height: 220px;
      border: 2px solid #000;
      background: white;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .image-preview img {
      max-width: 100%;
      max-height: 100%;
    }
    
    .nfiq-label {
      font-size: 11px;
      margin-top: 5px;
      color: #666;
    }

    /* Individual Finger Images */
    .finger-images {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 15px;
      margin: 20px 0;
    }
    
    .finger-image-box {
      text-align: center;
    }
    
    .finger-image-box h5 {
      font-size: 11px;
      margin-bottom: 5px;
    }
    
    .finger-image-preview {
      width: 120px;
      height: 150px;
      border: 1px solid #ccc;
      background: white;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .finger-image-preview img {
      max-width: 100%;
      max-height: 100%;
    }

    /* Spinner */
    .spinner {
      display: inline-block;
      width: 24px;
      height: 24px;
      border: 4px solid #ccc;
      border-top: 4px solid #4caf50;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      vertical-align: middle;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Hand Images */
    .hand-images {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
    }
    
    .hand-images img {
      width: 150px;
    }

    /* Status Display */
    #status {
      margin-top: 15px;
      font-weight: bold;
      color: #0b5394;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="main-container">
    <h1>APIS TrustFinger</h1>

    <!-- Top Section (mirroring MainForm layout) -->
    <div class="top-section">
      <!-- User Info Box (left side) -->
      <div class="control-box">
        <h3>User Information</h3>

        <!-- Device Controls -->
        <div class="device-controls">
          <button id="openDeviceBtn" class="btn btn-primary">Open Device</button>
          <button id="captureBtn" class="btn" disabled>Capture</button>
          <button id="clearBtn" class="btn">Clear</button>
        </div>

        <!-- User Input Fields -->
        <div class="input-row">
          <label>User ID:</label>
          <input type="text" id="userIdInput" placeholder="Prisoner ID">
        </div>

        <div class="input-row">
          <label>User Name:</label>
          <input type="text" id="userNameInput" placeholder="User Name">
        </div>

        <!-- Action Buttons -->
        <div style="margin: 15px 0;">
          <button id="enrollBtn" class="btn btn-success" disabled>Enroll</button>
          <button id="identifyBtn" class="btn btn-info" disabled>Identify</button>
          <button id="clearDbBtn" class="btn" style="display: none;">Clear DB</button>
        </div>

        <!-- Device Info Display -->
        <div class="device-info">
          <div class="device-info-row">
            <label>Manufacturer:</label>
            <span id="manufacturerInfo">APIS Co.,LTD.</span>
          </div>
          <div class="device-info-row">
            <label>Device ID:</label>
            <span id="deviceIdInfo">N/A</span>
          </div>
          <div class="device-info-row">
            <label>Image Width:</label>
            <span id="imageWidthInfo">N/A</span>
          </div>
          <div class="device-info-row">
            <label>Image Height:</label>
            <span id="imageHeightInfo">N/A</span>
          </div>
          <div class="device-info-row">
            <label>FW Version:</label>
            <span id="firmwareInfo">N/A</span>
          </div>
          <div class="device-info-row">
            <label>Image DPI:</label>
            <span id="resolutionInfo">N/A</span>
          </div>
          <div class="device-info-row">
            <label>S/N:</label>
            <span id="serialNumberInfo">N/A</span>
          </div>
        </div>
      </div>

      <!-- Finger Select Box (right side) -->
      <div class="control-box">
        <h3>Finger Select</h3>

        <!-- Slaps Selection (only visible when Slaps tab is active) -->
        <div id="slapsSelection" style="margin-bottom: 20px;">
          <div style="display: flex; gap: 15px; flex-wrap: wrap; justify-content: center;">
            <div class="finger-item">
              <input type="checkbox" id="leftFourCheck" disabled>
              <label for="leftFourCheck">Left 4</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="rightFourCheck" disabled>
              <label for="rightFourCheck">Right 4</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="twoThumbsCheck" disabled>
              <label for="twoThumbsCheck">2 Thumbs</label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Message Display -->
    <div id="messageDisplay" style="display: none;"></div>

    <!-- Status Display -->
    <div id="status">Checking device status...</div>
  </div>

  <script>
    // ES5 Compatible JavaScript - No modern features that trigger CSP
    var API_BASE_URL = 'http://localhost:5001/api';
    var deviceOpen = false;
    var currentTab = 0;
    var capturedTemplates = {};
    var selectedFingers = [];
    
    // Simple finger mapping
    var slapsMapping = {
      'leftfour': { id: 12, name: 'Left Four Fingers' },
      'rightfour': { id: 13, name: 'Right Four Fingers' },
      'twothumbs': { id: 11, name: 'Two Thumbs' }
    };

    // DOM elements
    var openDeviceBtn = document.getElementById('openDeviceBtn');
    var captureBtn = document.getElementById('captureBtn');
    var clearBtn = document.getElementById('clearBtn');
    var enrollBtn = document.getElementById('enrollBtn');
    var identifyBtn = document.getElementById('identifyBtn');
    var userIdInput = document.getElementById('userIdInput');
    var userNameInput = document.getElementById('userNameInput');
    var statusDiv = document.getElementById('status');
    var messageDisplay = document.getElementById('messageDisplay');
    var leftFourCheck = document.getElementById('leftFourCheck');
    var rightFourCheck = document.getElementById('rightFourCheck');
    var twoThumbsCheck = document.getElementById('twoThumbsCheck');

    // Update device control states
    function updateDeviceControls() {
      captureBtn.disabled = !deviceOpen;
      enrollBtn.disabled = !deviceOpen;
      identifyBtn.disabled = !deviceOpen;
      leftFourCheck.disabled = !deviceOpen;
      rightFourCheck.disabled = !deviceOpen;
      twoThumbsCheck.disabled = !deviceOpen;
      openDeviceBtn.textContent = deviceOpen ? 'Close Device' : 'Open Device';
    }

    // Display status message
    function displayStatus(message, type) {
      statusDiv.textContent = message;
      if (type === 'error') {
        statusDiv.style.color = '#dc3545';
      } else if (type === 'success') {
        statusDiv.style.color = '#28a745';
      } else {
        statusDiv.style.color = '#0b5394';
      }
    }

    // Display message with styling
    function displayMessage(message, type) {
      messageDisplay.innerHTML = message;
      messageDisplay.className = 'message message-' + (type || 'info');
      messageDisplay.style.display = 'block';

      setTimeout(function() {
        messageDisplay.style.display = 'none';
      }, 5000);
    }

    // Simple API call using XMLHttpRequest (ES5 compatible)
    function callAPI(endpoint, method, data, callback) {
      var xhr = new XMLHttpRequest();
      var url = API_BASE_URL + '/' + endpoint;

      xhr.open(method || 'GET', url, true);
      xhr.setRequestHeader('Content-Type', 'application/json');

      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          try {
            var result = JSON.parse(xhr.responseText);
            if (xhr.status >= 200 && xhr.status < 300) {
              callback(null, result);
            } else {
              callback(new Error(result.error || 'HTTP ' + xhr.status), null);
            }
          } catch (e) {
            callback(new Error('Invalid JSON response'), null);
          }
        }
      };

      xhr.onerror = function() {
        callback(new Error('Network error'), null);
      };

      if (data && method === 'POST') {
        xhr.send(JSON.stringify(data));
      } else {
        xhr.send();
      }
    }

    // Check device status
    function checkDeviceStatus() {
      displayStatus('Checking device status...', 'info');

      callAPI('health', 'GET', null, function(error, healthResult) {
        if (error) {
          displayStatus('Bridge application not running', 'error');
          displayMessage('Bridge application is not running. Please start the MultiFingerDemo.exe application first.', 'error');
          deviceOpen = false;
          updateDeviceControls();
          return;
        }

        if (!healthResult.success || !healthResult.data || healthResult.data.tcp_connection !== 'connected') {
          displayStatus('Bridge application not running', 'error');
          displayMessage('Bridge application is not running. Please start the MultiFingerDemo.exe application first.', 'error');
          deviceOpen = false;
          updateDeviceControls();
          return;
        }

        callAPI('status', 'GET', null, function(statusError, statusResult) {
          if (statusError) {
            displayStatus('Device status check failed', 'error');
            displayMessage('Failed to get device status from bridge application.', 'error');
            deviceOpen = false;
            updateDeviceControls();
            return;
          }

          if (statusResult.success && statusResult.data) {
            var isDeviceConnected = statusResult.data.DeviceConnected || statusResult.data.device_connected || false;

            if (isDeviceConnected) {
              deviceOpen = true;
              displayStatus('Device is open and ready', 'success');
              displayMessage('Device is connected and ready for fingerprint operations.', 'success');
            } else {
              deviceOpen = false;
              displayStatus('Device not connected', 'info');
              displayMessage('Device is not connected. Please ensure the device is plugged in and the C# bridge application has opened it.', 'info');
            }
          } else {
            displayStatus('Unable to check device status', 'error');
            displayMessage('Failed to get device status from bridge application.', 'error');
            deviceOpen = false;
          }

          updateDeviceControls();
        });
      });
    }

    // Handle finger selection
    function handleFingerSelection(fingerId, isChecked) {
      if (isChecked) {
        if (selectedFingers.indexOf(fingerId) === -1) {
          selectedFingers.push(fingerId);
        }
      } else {
        var index = selectedFingers.indexOf(fingerId);
        if (index > -1) {
          selectedFingers.splice(index, 1);
        }
      }
    }

    // Start capture
    function startCapture() {
      if (!deviceOpen) {
        displayMessage('Please open device first', 'error');
        return;
      }

      if (selectedFingers.length === 0) {
        displayMessage('Please select at least one option: Left 4, Right 4, or 2 Thumbs', 'error');
        return;
      }

      displayStatus('Capturing fingerprints...', 'info');
      var captureResults = [];
      var completedCaptures = 0;

      for (var i = 0; i < selectedFingers.length; i++) {
        var fingerId = selectedFingers[i];
        var fingerMapping = slapsMapping[fingerId];

        if (!fingerMapping) {
          captureResults.push(fingerId + ' failed: Unknown finger');
          completedCaptures++;
          continue;
        }

        var captureData = {
          finger_position: fingerMapping.id,
          operation_type: 'slaps',
          timeout: 30,
          save_image: true
        };

        console.log('Calling capture API for ' + fingerId + ' with data:', captureData);
        displayStatus('Capturing ' + fingerId + '...', 'info');

        (function(currentFingerId, currentFingerName) {
          callAPI('fingerprint/capture', 'POST', captureData, function(error, result) {
            console.log('Capture API result for ' + currentFingerId + ':', result);

            if (error) {
              console.error('Capture error for ' + currentFingerId + ':', error);
              captureResults.push(currentFingerId + ' failed: ' + error.message);
            } else if (result.success && result.data) {
              capturedTemplates[currentFingerId] = {
                template: result.data.template_data || '',
                image: result.data.image_data || '',
                quality: result.data.quality || 'N/A',
                fingerPosition: fingerMapping.id,
                operationType: 'slaps'
              };
              captureResults.push(currentFingerName + ' captured successfully');
            } else {
              var errorMsg = result.error || result.message || 'Unknown error';
              console.error('Capture failed for ' + currentFingerId + ':', result);
              captureResults.push(currentFingerId + ' failed: ' + errorMsg);
            }

            completedCaptures++;
            if (completedCaptures === selectedFingers.length) {
              var message = captureResults.join(', ');
              var messageType = 'success';
              for (var j = 0; j < captureResults.length; j++) {
                if (captureResults[j].indexOf('failed') !== -1) {
                  messageType = 'error';
                  break;
                }
              }
              displayMessage(message, messageType);
              displayStatus('Capture completed', 'success');
            }
          });
        })(fingerId, fingerMapping.name);
      }
    }

    // Event handlers
    function onOpenDeviceClick() {
      displayMessage('Device opening/closing is handled by the C# bridge application. Please use the MainForm application to open/close the device.', 'info');
      setTimeout(function() {
        checkDeviceStatus();
      }, 1000);
    }

    function onCaptureClick() {
      startCapture();
    }

    function onClearClick() {
      capturedTemplates = {};
      displayStatus('Images cleared', 'info');
    }

    function onLeftFourChange() {
      handleFingerSelection('leftfour', leftFourCheck.checked);
    }

    function onRightFourChange() {
      handleFingerSelection('rightfour', rightFourCheck.checked);
    }

    function onTwoThumbsChange() {
      handleFingerSelection('twothumbs', twoThumbsCheck.checked);
    }

    // Initialize the interface
    function initializeInterface() {
      updateDeviceControls();

      // Add event listeners
      openDeviceBtn.addEventListener('click', onOpenDeviceClick);
      captureBtn.addEventListener('click', onCaptureClick);
      clearBtn.addEventListener('click', onClearClick);
      leftFourCheck.addEventListener('change', onLeftFourChange);
      rightFourCheck.addEventListener('change', onRightFourChange);
      twoThumbsCheck.addEventListener('change', onTwoThumbsChange);

      // Check device status on page load
      checkDeviceStatus();

      // Set up periodic status checking (every 5 seconds)
      setInterval(checkDeviceStatus, 5000);
    }

    // Initialize when page loads
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeInterface);
    } else {
      initializeInterface();
    }
  </script>
</body>
</html>
