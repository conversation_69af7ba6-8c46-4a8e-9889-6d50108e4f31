#!/usr/bin/env python
#----------------------------------------------------------------------
# Name:        PyWrap.py
# Purpose:     PyWrap is a command line utility that runs a wxPython program with
#              additional runtime-tools, such as PyCrust.
# Tags:        phoenix-port
#----------------------------------------------------------------------
"""PyWrap is a command line utility that runs a wxPython program with
additional runtime-tools, such as PyCrust."""

__author__ = "<PERSON> <<EMAIL>>"

import wx
from wx import py

import os
import sys

def wrap(app):
    frame = py.crust.CrustFrame()
    frame.SetSize((750, 525))
    frame.Show(True)
    frame.shell.interp.locals['app'] = app
    app.MainLoop()

def main(modulename=None):
    sys.path.insert(0, os.curdir)
    if not modulename:
        if len(sys.argv) < 2:
            print("Please specify a module name.")
            raise SystemExit
        modulename = sys.argv[1]
        if modulename.endswith('.py'):
            modulename = modulename[:-3]
    module = __import__(modulename)
    # Find the App class.
    App = None
    d = module.__dict__
    for item in d:
        try:
            if issubclass(d[item], wx.App):
                App = d[item]
        except (NameError, TypeError):
            pass
    if App is None:
        print("No App class was found.")
        raise SystemExit
    app = App()
    wrap(app)

if __name__ == '__main__':
    main()
