# Database Configuration
host=localhost
user=root
password=sa
database=finger

# API Configuration
api_port=9000
web_demo_port=8080

# Application Settings
app_name=Fingerprint Bridge Service

# RING FINGER EMERGENCY SETTINGS
# Use these settings ONLY for ring finger capture issues
capture_timeout=120
lfd_enabled=false
lfd_level=1
dry_level=6
quality_threshold=5
ring_finger_bypass=true
force_single_finger_mode=true
