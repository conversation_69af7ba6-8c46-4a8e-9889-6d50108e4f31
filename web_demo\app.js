document.addEventListener("DOMContentLoaded", function () {
  // API endpoint - matches test_rest_api.py configuration
  const API_BASE_URL = "http://localhost:5001";

  // Add debug logging
  console.log("Web demo starting...");
  console.log("API URL:", API_BASE_URL);

  // --- API Helper ---
  async function apiRequest(endpoint, method = "GET", data = null) {
    const options = { method, headers: { "Content-Type": "application/json" } };
    if (data) options.body = JSON.stringify(data);
    // Add /api prefix to match Python Flask API endpoints
    const fullUrl = `${API_BASE_URL}/api/${endpoint}`;
    const res = await fetch(fullUrl, options);
    if (!res.ok) throw new Error(await res.text());
    return await res.json();
  }

  // --- Tab and UI State ---
  let currentTab = "slaps";
  let selectedSlapsFingers = new Set();
  let selectedRolledFinger = null;
  let capturedTemplates = {}; // { finger: { template, image, nfiq } }
  let deviceOpen = false;

  // --- MainForm Finger Mapping ---
  const slapsFingers = [
    { id: "L1", label: "Left Thumb" },
    { id: "L2", label: "Left Index" },
    { id: "L3", label: "Left Middle" },
    { id: "L4", label: "Left Ring" },
    { id: "L5", label: "Left Little" },
    { id: "R1", label: "Right Thumb" },
    { id: "R2", label: "Right Index" },
    { id: "R3", label: "Right Middle" },
    { id: "R4", label: "Right Ring" },
    { id: "R5", label: "Right Little" },
  ];
  const rolledFingers = slapsFingers;

  // --- Tab Switching ---
  function switchTab(tab) {
    const ids = [
      "tab-slaps",
      "tab-flat",
      "tab-rolled",
      "tab-content-slaps",
      "tab-content-flat",
      "tab-content-rolled",
    ];
    ids.forEach((id) => {
      if (!document.getElementById(id)) {
        console.error("Element not found:", id);
      }
    });
    currentTab = tab;
    // Remove 'active' from all tabs and tab-contents
    document.getElementById("tab-slaps").classList.remove("active");
    document.getElementById("tab-flat").classList.remove("active");
    document.getElementById("tab-rolled").classList.remove("active");
    document.getElementById("tab-content-slaps").classList.remove("active");
    document.getElementById("tab-content-flat").classList.remove("active");
    document.getElementById("tab-content-rolled").classList.remove("active");
    // Add 'active' to the selected tab and content
    if (tab === "slaps") {
      document.getElementById("tab-slaps").classList.add("active");
      document.getElementById("tab-content-slaps").classList.add("active");
    } else if (tab === "flat") {
      document.getElementById("tab-flat").classList.add("active");
      document.getElementById("tab-content-flat").classList.add("active");
    } else if (tab === "rolled") {
      document.getElementById("tab-rolled").classList.add("active");
      document.getElementById("tab-content-rolled").classList.add("active");
    }
    renderFingerCheckboxes();
    updateButtonStates();
  }

  window.switchTab = switchTab;

  // --- Utility Functions ---
  function setTip(msg, isError) {
    const tipLabel = document.getElementById("tipLabel");
    if (tipLabel) {
      tipLabel.textContent = msg;
      tipLabel.style.color = isError ? "#b00" : "#007bff";
    }
  }

  function updateButtonStates() {
    // Enable/disable buttons based on device and selection
    const captureBtn = document.getElementById("captureBtn");
    const enrollBtn = document.getElementById("enrollBtn");
    const identifyBtn = document.getElementById("identifyBtn");
    const captureRolledBtn = document.getElementById("captureRolledBtn");
    const enrollRolledBtn = document.getElementById("enrollRolledBtn");
    const identifyRolledBtn = document.getElementById("identifyRolledBtn");
    const captureFlatBtn = document.getElementById("captureFlatBtn");
    const enrollFlatBtn = document.getElementById("enrollFlatBtn");
    const identifyFlatBtn = document.getElementById("identifyFlatBtn");

    // Check selections for each tab
    const left4 = document.getElementById("slaps_left4");
    const right4 = document.getElementById("slaps_right4");
    const thumbs = document.getElementById("slaps_thumbs");
    const slapsSelected =
      (left4 && left4.checked) ||
      (right4 && right4.checked) ||
      (thumbs && thumbs.checked);

    // Determine which tab is currently active by checking tab content visibility
    let activeTabId = 'slaps'; // default
    if (document.getElementById('tab-content-slaps').classList.contains('active')) {
      activeTabId = 'slaps';
    } else if (document.getElementById('tab-content-flat').classList.contains('active')) {
      activeTabId = 'flat';
    } else if (document.getElementById('tab-content-rolled').classList.contains('active')) {
      activeTabId = 'rolled';
    }

    // Check if current tab has valid selection
    let hasValidSelection = false;
    if (activeTabId === 'slaps') {
      hasValidSelection = slapsSelected;
    } else if (activeTabId === 'rolled') {
      hasValidSelection = !!selectedRolledFinger;
    } else if (activeTabId === 'flat') {
      hasValidSelection = !!window.selectedFlatFinger;
    }

    // Capture button: enabled if device is open and current tab has valid selection
    if (captureBtn) captureBtn.disabled = !deviceOpen || !hasValidSelection;
    if (enrollBtn) enrollBtn.disabled = !deviceOpen || !slapsSelected;
    if (identifyBtn) identifyBtn.disabled = !deviceOpen || !slapsSelected;
    if (captureRolledBtn)
      captureRolledBtn.disabled = !deviceOpen || !selectedRolledFinger;
    if (enrollRolledBtn)
      enrollRolledBtn.disabled = !deviceOpen || !selectedRolledFinger;
    if (identifyRolledBtn)
      identifyRolledBtn.disabled = !deviceOpen || !selectedRolledFinger;
    if (captureFlatBtn)
      captureFlatBtn.disabled = !deviceOpen || !window.selectedFlatFinger;
    if (enrollFlatBtn)
      enrollFlatBtn.disabled = !deviceOpen || !window.selectedFlatFinger;
    if (identifyFlatBtn)
      identifyFlatBtn.disabled = !deviceOpen || !window.selectedFlatFinger;
  }

  // --- Render Finger Checkboxes ---
  function updateSelectedSlapsFingers() {
    selectedSlapsFingers.clear();
    const left4 = document.getElementById("slaps_left4");
    const right4 = document.getElementById("slaps_right4");
    const thumbs = document.getElementById("slaps_thumbs");
    if (left4 && left4.checked) {
      ["L2", "L3", "L4", "L5"].forEach((f) => selectedSlapsFingers.add(f));
    }
    if (right4 && right4.checked) {
      ["R2", "R3", "R4", "R5"].forEach((f) => selectedSlapsFingers.add(f));
    }
    if (thumbs && thumbs.checked) {
      ["L1", "R1"].forEach((f) => selectedSlapsFingers.add(f));
    }
  }

  function renderFingerCheckboxes() {
    if (currentTab === "slaps") {
      // Slaps: only three static checkboxes
      // Attach event listeners if not already attached
      const left4 = document.getElementById("slaps_left4");
      const right4 = document.getElementById("slaps_right4");
      const thumbs = document.getElementById("slaps_thumbs");
      if (left4 && !left4._listenerAdded) {
        left4.addEventListener("change", function () {
          updateSelectedSlapsFingers();
          updateButtonStates();
        });
        left4._listenerAdded = true;
      }
      if (right4 && !right4._listenerAdded) {
        right4.addEventListener("change", function () {
          updateSelectedSlapsFingers();
          updateButtonStates();
        });
        right4._listenerAdded = true;
      }
      if (thumbs && !thumbs._listenerAdded) {
        thumbs.addEventListener("change", function () {
          updateSelectedSlapsFingers();
          updateButtonStates();
        });
        thumbs._listenerAdded = true;
      }
      // Initialize selectedSlapsFingers on render
      updateSelectedSlapsFingers();
    } else if (currentTab === "flat") {
      // Flat: single-select (like rolled)
      const flatBox = document.getElementById("flatFingerCheckboxes");
      if (!flatBox) return;
      flatBox.innerHTML = "";
      rolledFingers.forEach((f) => {
        const label = document.createElement("label");
        const cb = document.createElement("input");
        cb.type = "checkbox";
        cb.id = `flat_${f.id}`;
        cb.addEventListener("change", () => {
          if (cb.checked) {
            // Uncheck all others
            rolledFingers.forEach((ff) => {
              if (ff.id !== f.id) {
                const other = document.getElementById(`flat_${ff.id}`);
                if (other) other.checked = false;
              }
            });
            window.selectedFlatFinger = f.id;
          } else {
            window.selectedFlatFinger = null;
          }
          updateButtonStates();
        });
        label.appendChild(cb);
        label.appendChild(document.createTextNode(" " + f.label));
        flatBox.appendChild(label);
      });
    } else if (currentTab === "rolled") {
      const rolledBox = document.getElementById("rolledFingerCheckboxes");
      if (!rolledBox) return;
      rolledBox.innerHTML = "";
      rolledFingers.forEach((f) => {
        const label = document.createElement("label");
        const cb = document.createElement("input");
        cb.type = "checkbox";
        cb.id = `rolled_${f.id}`;
        cb.addEventListener("change", () => {
          if (cb.checked) {
            // Uncheck all others
            rolledFingers.forEach((ff) => {
              if (ff.id !== f.id) {
                const other = document.getElementById(`rolled_${ff.id}`);
                if (other) other.checked = false;
              }
            });
            selectedRolledFinger = f.id;
          } else {
            selectedRolledFinger = null;
          }
          updateButtonStates();
        });
        label.appendChild(cb);
        label.appendChild(document.createTextNode(" " + f.label));
        rolledBox.appendChild(label);
      });
    }
  }

  // --- Device Controls ---
  document.getElementById("openDeviceBtn").onclick = async function () {
    try {
      // Use status endpoint to check device (your Python API doesn't have device/open)
      const status = await apiRequest("status", "GET");
      if (status.success) {
        deviceOpen = true;
        setTip("Device status checked - ready", false);
        updateButtonStates();
      }
    } catch (e) {
      setTip("Failed to check device status", true);
      console.error("Device Status API error:", e);
    }
  };
  document.getElementById("closeDeviceBtn").onclick = async function () {
    try {
      // Your Python API doesn't have device/close endpoint
      deviceOpen = false;
      setTip("Device connection closed", false);
      updateButtonStates();
    } catch (e) {
      setTip("Failed to close device", true);
    }
  };
  document.getElementById("deviceInfoBtn").onclick = async function () {
    try {
      const info = await apiRequest("status", "GET");
      if (info.success && info.data) {
        document.getElementById(
          "deviceInfo"
        ).textContent = `Status: ${info.data.api_status || 'Unknown'}, TCP: ${info.data.tcp_connection || 'Unknown'}`;
      }
    } catch (e) {
      document.getElementById("deviceInfo").textContent =
        "Device info unavailable";
    }
  };


  // --- Capture/Enroll/Identify Buttons ---
  document.getElementById("captureBtn").onclick = async function () {
    if (!deviceOpen) return;

    // Determine which tab is currently active by checking tab content visibility
    let activeTabId = 'slaps'; // default
    if (document.getElementById('tab-content-slaps').classList.contains('active')) {
      activeTabId = 'slaps';
    } else if (document.getElementById('tab-content-flat').classList.contains('active')) {
      activeTabId = 'flat';
    } else if (document.getElementById('tab-content-rolled').classList.contains('active')) {
      activeTabId = 'rolled';
    }

    setTip("Capturing...", false);

    try {
      if (activeTabId === 'slaps') {
        // Capture for slaps tab - capture each selected finger
        if (selectedSlapsFingers.size === 0) return;
        for (const fid of selectedSlapsFingers) {
          const response = await apiRequest("fingerprint/capture", "POST", {
            finger_position: fid,
            operation_type: "slaps"
          });
          if (response.success && response.data) {
            capturedTemplates[fid] = {
              template: response.data.TemplateData || response.data.templateData,
              image: response.data.ImageData || response.data.imageData,
              nfiq: response.data.Quality || response.data.quality || "N/A",
            };
          } else {
            setTip(response.message || "Capture failed", true);
            return;
          }
        }
        updateSlapsPreviews();
      } else if (activeTabId === 'rolled') {
        // Capture for rolled tab
        if (!selectedRolledFinger) return;
        const response = await apiRequest("fingerprint/capture", "POST", {
          finger_position: selectedRolledFinger,
          operation_type: "rolled"
        });
        if (response.success && response.data) {
          capturedTemplates[selectedRolledFinger] = {
            template: response.data.TemplateData || response.data.templateData,
            image: response.data.ImageData || response.data.imageData,
            nfiq: response.data.Quality || response.data.quality || "N/A",
          };
        } else {
          setTip(response.message || "Capture failed", true);
          return;
        }
        updateRolledPreviews();
      } else if (activeTabId === 'flat') {
        // Capture for flat tab
        if (!window.selectedFlatFinger) return;
        const response = await apiRequest("fingerprint/capture", "POST", {
          finger_position: window.selectedFlatFinger,
          operation_type: "flat"
        });
        if (response.success && response.data) {
          capturedTemplates[window.selectedFlatFinger] = {
            template: response.data.TemplateData || response.data.templateData,
            image: response.data.ImageData || response.data.imageData,
            nfiq: response.data.Quality || response.data.quality || "N/A",
          };
        } else {
          setTip(response.message || "Capture failed", true);
          return;
        }
        updateFlatPreviews();
      }

      setTip("Capture complete", false);
    } catch (e) {
      setTip("Capture error: " + e.message, true);
    }

    updateButtonStates();
  };
  document.getElementById("enrollBtn").onclick = async function () {
    if (!deviceOpen || selectedSlapsFingers.size === 0) return;
    setTip("Enrolling...", false);
    const userId = document.getElementById("userId").value.trim();
    for (const fid of selectedSlapsFingers) {
      const data = capturedTemplates[fid];
      if (!data) continue;
      try {
        const response = await apiRequest("fingerprint/enroll", "POST", {
          user_id: userId,
          finger_position: fingerIdToPosition(fid),
          template_data: data.template,
          image_data: data.image,
          image_quality: data.nfiq,
        });
        if (response.success) {
          setTip(`Enroll successful for ${fid}`, false);
        } else {
          setTip(response.message || "Enroll failed", true);
        }
      } catch (e) {
        setTip("Enroll error: " + e.message, true);
      }
    }
  };
  document.getElementById("identifyBtn").onclick = async function () {
    if (!deviceOpen || selectedSlapsFingers.size === 0) return;
    setTip("Identifying...", false);
    for (const fid of selectedSlapsFingers) {
      const data = capturedTemplates[fid];
      if (!data) continue;
      try {
        const response = await apiRequest("fingerprint/identify", "POST", {
          template_data: data.template,
        });
        if (
          response.success &&
          response.matches &&
          response.matches.length > 0
        ) {
          const match = response.matches[0];
          setTip(
            `Identified: Prisoner ${match.UserId || match.userId} (${
              match.FingerPositionName || match.fingerPositionName
            }, Score: ${match.Score || match.score})`,
            false
          );
        } else {
          setTip("No match found", true);
        }
      } catch (e) {
        setTip("Identify error: " + e.message, true);
      }
    }
  };
  document.getElementById("captureRolledBtn").onclick = async function () {
    if (!deviceOpen || !selectedRolledFinger) return;
    setTip("Capturing rolled...", false);
    try {
      const response = await apiRequest("fingerprint/capture", "POST", {
        finger_position: selectedRolledFinger,
        operation_type: "rolled"
      });
      if (response.success && response.data) {
        capturedTemplates[selectedRolledFinger] = {
          template: response.data.TemplateData || response.data.templateData,
          image: response.data.ImageData || response.data.imageData,
          nfiq: response.data.Quality || response.data.quality || "N/A",
        };
      } else {
        setTip(response.message || "Capture failed", true);
      }
    } catch (e) {
      setTip("Capture error: " + e.message, true);
    }
    updateRolledPreviews();
    setTip("Capture complete", false);
    updateButtonStates();
  };
  document.getElementById("enrollRolledBtn").onclick = async function () {
    if (!deviceOpen || !selectedRolledFinger) return;
    setTip("Enrolling rolled...", false);
    const userId = document.getElementById("userId").value.trim();
    const data = capturedTemplates[selectedRolledFinger];
    if (!data) return;
    try {
      const response = await apiRequest("fingerprint/enroll", "POST", {
        user_id: userId,
        finger_position: fingerIdToPosition(selectedRolledFinger),
        template_data: data.template,
        image_data: data.image,
        image_quality: data.nfiq,
      });
      if (response.success) {
        setTip(`Enroll successful for ${selectedRolledFinger}`, false);
      } else {
        setTip(response.message || "Enroll failed", true);
      }
    } catch (e) {
      setTip("Enroll error: " + e.message, true);
    }
  };
  document.getElementById("identifyRolledBtn").onclick = async function () {
    if (!deviceOpen || !selectedRolledFinger) return;
    setTip("Identifying rolled...", false);
    const data = capturedTemplates[selectedRolledFinger];
    if (!data) return;
    try {
      const response = await apiRequest("fingerprint/identify", "POST", {
        template_data: data.template,
      });
      if (response.success && response.matches && response.matches.length > 0) {
        const match = response.matches[0];
        setTip(
          `Identified: Prisoner ${match.UserId || match.userId} (${
            match.FingerPositionName || match.fingerPositionName
          }, Score: ${match.Score || match.score})`,
          false
        );
      } else {
        setTip("No match found", true);
      }
    } catch (e) {
      setTip("Identify error: " + e.message, true);
    }
  };

  // --- Flat Fingerprints Button Handlers ---
  document.getElementById("captureFlatBtn").onclick = async function () {
    if (!deviceOpen || !window.selectedFlatFinger) return;
    setTip("Capturing flat...", false);
    try {
      const response = await apiRequest("fingerprint/capture", "POST", {
        finger_position: window.selectedFlatFinger,
        operation_type: "flat"
      });
      if (response.success && response.data) {
        capturedTemplates[window.selectedFlatFinger] = {
          template: response.data.TemplateData || response.data.templateData,
          image: response.data.ImageData || response.data.imageData,
          nfiq: response.data.Quality || response.data.quality || "N/A",
        };
      } else {
        setTip(response.message || "Capture failed", true);
      }
    } catch (e) {
      setTip("Capture error: " + e.message, true);
    }
    updateFlatPreviews();
    setTip("Capture complete", false);
    updateButtonStates();
  };
  document.getElementById("enrollFlatBtn").onclick = async function () {
    if (!deviceOpen || !window.selectedFlatFinger) return;
    setTip("Enrolling flat...", false);
    const userId = document.getElementById("userId").value.trim();
    const data = capturedTemplates[window.selectedFlatFinger];
    if (!data) return;
    try {
      const response = await apiRequest("fingerprint/enroll", "POST", {
        user_id: userId,
        finger_position: fingerIdToPosition(window.selectedFlatFinger),
        template_data: data.template,
        image_data: data.image,
        image_quality: data.nfiq,
      });
      if (response.success) {
        setTip(`Enroll successful for ${window.selectedFlatFinger}`, false);
      } else {
        setTip(response.message || "Enroll failed", true);
      }
    } catch (e) {
      setTip("Enroll error: " + e.message, true);
    }
  };
  document.getElementById("identifyFlatBtn").onclick = async function () {
    if (!deviceOpen || !window.selectedFlatFinger) return;
    setTip("Identifying flat...", false);
    const data = capturedTemplates[window.selectedFlatFinger];
    if (!data) return;
    try {
      const response = await apiRequest("fingerprint/identify", "POST", {
        template_data: data.template,
      });
      if (response.success && response.matches && response.matches.length > 0) {
        const match = response.matches[0];
        setTip(
          `Identified: Prisoner ${match.UserId || match.userId} (${
            match.FingerPositionName || match.fingerPositionName
          }, Score: ${match.Score || match.score})`,
          false
        );
      } else {
        setTip("No match found", true);
      }
    } catch (e) {
      setTip("Identify error: " + e.message, true);
    }
  };

  // Helper: Map finger ID to backend position number
  function fingerIdToPosition(fid) {
    const map = {
      L1: 6,
      L2: 7,
      L3: 8,
      L4: 9,
      L5: 10,
      R1: 1,
      R2: 2,
      R3: 3,
      R4: 4,
      R5: 5,
    };
    return map[fid] || 1;
  }

  // Update previews to show real images and NFIQ
  function updateSlapsPreviews() {
    // For demo, show first selected finger's image and NFIQ in each preview
    const left = ["L2", "L3", "L4", "L5"].find((f) =>
      selectedSlapsFingers.has(f)
    );
    const right = ["R2", "R3", "R4", "R5"].find((f) =>
      selectedSlapsFingers.has(f)
    );
    const thumbs = ["L1", "R1"].every((f) => selectedSlapsFingers.has(f));

    // Left Four Fingers
    if (left && capturedTemplates[left] && capturedTemplates[left].image) {
      document.getElementById(
        "leftFourPreview"
      ).src = `data:image/bmp;base64,${capturedTemplates[left].image}`;
      document.getElementById("leftFourPreview").style.display = "block";
      document.getElementById(
        "nfiqLeftFour"
      ).textContent = `NFIQ=${capturedTemplates[left].nfiq}`;
    } else {
      document.getElementById("leftFourPreview").style.display = "none";
      document.getElementById("nfiqLeftFour").textContent = "NFIQ=N/A";
    }

    // Right Four Fingers
    if (right && capturedTemplates[right] && capturedTemplates[right].image) {
      document.getElementById(
        "rightFourPreview"
      ).src = `data:image/bmp;base64,${capturedTemplates[right].image}`;
      document.getElementById("rightFourPreview").style.display = "block";
      document.getElementById(
        "nfiqRightFour"
      ).textContent = `NFIQ=${capturedTemplates[right].nfiq}`;
    } else {
      document.getElementById("rightFourPreview").style.display = "none";
      document.getElementById("nfiqRightFour").textContent = "NFIQ=N/A";
    }

    // Two Thumbs
    if (thumbs && capturedTemplates["L1"] && capturedTemplates["L1"].image) {
      document.getElementById(
        "twoThumbsPreview"
      ).src = `data:image/bmp;base64,${capturedTemplates["L1"].image}`;
      document.getElementById("twoThumbsPreview").style.display = "block";
      document.getElementById(
        "nfiqTwoThumbs"
      ).textContent = `NFIQ=${capturedTemplates["L1"].nfiq}`;
    } else {
      document.getElementById("twoThumbsPreview").style.display = "none";
      document.getElementById("nfiqTwoThumbs").textContent = "NFIQ=N/A";
    }
  }

  function updateRolledPreviews() {
    if (!selectedRolledFinger) return;

    const rolledPreviewsDiv = document.getElementById("rolledPreviews");
    if (!rolledPreviewsDiv) return;

    rolledPreviewsDiv.innerHTML = "";

    const data = capturedTemplates[selectedRolledFinger];
    if (data && data.image) {
      const previewCol = document.createElement("div");
      previewCol.className = "preview-col";

      const title = document.createElement("h4");
      title.textContent = rolledFingers.find(f => f.id === selectedRolledFinger)?.label || selectedRolledFinger;

      const img = document.createElement("img");
      img.src = `data:image/bmp;base64,${data.image}`;
      img.style.maxWidth = "200px";

      const nfiqLabel = document.createElement("div");
      nfiqLabel.className = "nfiq-label";
      nfiqLabel.textContent = `NFIQ=${data.nfiq}`;

      previewCol.appendChild(title);
      previewCol.appendChild(img);
      previewCol.appendChild(nfiqLabel);
      rolledPreviewsDiv.appendChild(previewCol);
    }
  }

  function updateFlatPreviews() {
    if (!window.selectedFlatFinger) return;

    const flatPreviewsDiv = document.getElementById("flatPreviews");
    if (!flatPreviewsDiv) return;

    flatPreviewsDiv.innerHTML = "";

    const data = capturedTemplates[window.selectedFlatFinger];
    if (data && data.image) {
      const previewCol = document.createElement("div");
      previewCol.className = "preview-col";

      const title = document.createElement("h4");
      title.textContent = rolledFingers.find(f => f.id === window.selectedFlatFinger)?.label || window.selectedFlatFinger;

      const img = document.createElement("img");
      img.src = `data:image/bmp;base64,${data.image}`;
      img.style.maxWidth = "200px";

      const nfiqLabel = document.createElement("div");
      nfiqLabel.className = "nfiq-label";
      nfiqLabel.textContent = `NFIQ=${data.nfiq}`;

      previewCol.appendChild(title);
      previewCol.appendChild(img);
      previewCol.appendChild(nfiqLabel);
      flatPreviewsDiv.appendChild(previewCol);
    }
  }

});

  // Removed closeCaptureDialog - using simplified approach

  // Removed resetCaptureDialog - using simplified approach

  // Removed setupCapturePreview - using simplified approach

  // Removed old capture dialog functions - using simplified approach

  // Event listeners for capture dialog - removed as we're using simplified approach

});
