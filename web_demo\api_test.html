<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API Test - MultiFinger Bridge</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .test-section {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .test-section h3 {
      margin-top: 0;
      color: #333;
    }
    button {
      padding: 8px 16px;
      margin: 5px;
      border: 1px solid #ccc;
      background: #f8f9fa;
      cursor: pointer;
      border-radius: 3px;
    }
    button:hover {
      background: #e9ecef;
    }
    .result {
      margin-top: 10px;
      padding: 10px;
      background: #f8f9fa;
      border-left: 4px solid #007bff;
      white-space: pre-wrap;
      font-family: monospace;
      font-size: 12px;
    }
    .success {
      border-left-color: #28a745;
      background: #d4edda;
    }
    .error {
      border-left-color: #dc3545;
      background: #f8d7da;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>MultiFinger Bridge API Test</h1>
    <p>This page tests the communication between the web interface and the C# bridge application.</p>

    <div class="test-section">
      <h3>1. Health Check</h3>
      <button onclick="testHealth()">Test Health</button>
      <div id="healthResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
      <h3>2. Device Status</h3>
      <button onclick="testStatus()">Test Status</button>
      <div id="statusResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
      <h3>3. Finger Positions</h3>
      <button onclick="testPositions()">Test Positions</button>
      <div id="positionsResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
      <h3>4. All Tests</h3>
      <button onclick="runAllTests()">Run All Tests</button>
    </div>
  </div>

  <script>
    const API_BASE_URL = 'http://localhost:5001/api';

    async function callAPI(endpoint, method = 'GET', data = null) {
      try {
        const options = {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          }
        };
        
        if (data && method === 'POST') {
          options.body = JSON.stringify(data);
        }
        
        const response = await fetch(`${API_BASE_URL}/${endpoint}`, options);
        const result = await response.json();
        
        return {
          success: response.ok,
          status: response.status,
          data: result
        };
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    }

    function displayResult(elementId, result, label) {
      const element = document.getElementById(elementId);
      element.style.display = 'block';
      
      if (result.success) {
        element.className = 'result success';
        element.textContent = `✅ ${label} SUCCESS\n\n${JSON.stringify(result.data, null, 2)}`;
      } else {
        element.className = 'result error';
        element.textContent = `❌ ${label} FAILED\n\nError: ${result.error || 'Unknown error'}\nStatus: ${result.status || 'N/A'}\nResponse: ${JSON.stringify(result.data, null, 2)}`;
      }
    }

    async function testHealth() {
      console.log('Testing health endpoint...');
      const result = await callAPI('health');
      displayResult('healthResult', result, 'Health Check');
    }

    async function testStatus() {
      console.log('Testing status endpoint...');
      const result = await callAPI('status');
      displayResult('statusResult', result, 'Device Status');
    }

    async function testPositions() {
      console.log('Testing positions endpoint...');
      const result = await callAPI('fingerprint/positions');
      displayResult('positionsResult', result, 'Finger Positions');
    }

    async function runAllTests() {
      console.log('Running all tests...');
      await testHealth();
      await new Promise(resolve => setTimeout(resolve, 500));
      await testStatus();
      await new Promise(resolve => setTimeout(resolve, 500));
      await testPositions();
    }

    // Auto-run tests on page load
    window.addEventListener('DOMContentLoaded', () => {
      console.log('Page loaded, running initial tests...');
      setTimeout(runAllTests, 1000);
    });
  </script>
</body>
</html>
