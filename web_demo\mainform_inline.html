<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>APIS TrustFinger - MultiFinger Bridge Interface</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f0f0f0;
    }
    
    .main-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 20px;
    }

    .control-section {
      margin: 20px 0;
      padding: 15px;
      border: 2px solid #ccc;
      border-radius: 5px;
    }
    
    .control-section h3 {
      margin-top: 0;
      color: #333;
    }

    label {
      display: block;
      margin-top: 10px;
      font-weight: bold;
    }
    
    input, select, button {
      margin-top: 5px;
      padding: 8px;
      font-size: 1em;
    }
    
    button {
      margin-right: 10px;
      cursor: pointer;
      border: 1px solid #ccc;
      background: #f8f9fa;
      border-radius: 3px;
    }
    
    button:hover {
      background: #e9ecef;
    }
    
    button:disabled {
      background: #6c757d;
      color: white;
      cursor: not-allowed;
    }
    
    .btn-primary {
      background: #007bff;
      color: white;
      border-color: #007bff;
    }
    
    .btn-success {
      background: #28a745;
      color: white;
      border-color: #28a745;
    }

    .finger-selection {
      display: flex;
      gap: 20px;
      margin: 15px 0;
      flex-wrap: wrap;
    }
    
    .finger-item {
      display: flex;
      align-items: center;
      gap: 5px;
    }

    #status {
      margin-top: 15px;
      font-weight: bold;
      color: #0b5394;
    }
    
    #response {
      margin-top: 10px;
      white-space: pre-wrap;
      background-color: #f4f4f4;
      padding: 10px;
      border: 1px solid #ccc;
      max-height: 300px;
      overflow-y: auto;
    }
    
    #bmpImage {
      margin-top: 20px;
      max-width: 300px;
      max-height: 300px;
      border: 1px solid #ccc;
    }

    .device-info {
      font-size: 12px;
      margin: 10px 0;
    }
    
    .device-info-row {
      margin: 3px 0;
    }
    
    .device-info-row label {
      display: inline-block;
      width: 100px;
      font-weight: normal;
    }
  </style>
</head>
<body onload="initPage()">
  <div class="main-container">
    <h1>APIS TrustFinger</h1>

    <!-- Device Status Section -->
    <div class="control-section">
      <h3>Device Status</h3>
      <button id="openDeviceBtn" class="btn-primary" onclick="refreshDeviceStatus()">Refresh Status</button>
      <div id="status">Checking device status...</div>
      
      <!-- Device Info Display -->
      <div class="device-info">
        <div class="device-info-row">
          <label>Status:</label>
          <span id="deviceStatus">Checking...</span>
        </div>
        <div class="device-info-row">
          <label>Connection:</label>
          <span id="connectionStatus">Unknown</span>
        </div>
      </div>
    </div>

    <!-- User Information Section -->
    <div class="control-section">
      <h3>User Information</h3>
      
      <label for="personId">User ID:</label>
      <input type="text" id="personId" placeholder="Enter User ID" />
      
      <label for="userName">User Name:</label>
      <input type="text" id="userName" placeholder="Enter User Name" />
    </div>

    <!-- Finger Selection Section -->
    <div class="control-section">
      <h3>Finger Selection (Slaps)</h3>
      
      <div class="finger-selection">
        <div class="finger-item">
          <input type="checkbox" id="leftFourCheck" disabled onchange="handleFingerChange('leftfour', this.checked)">
          <label for="leftFourCheck">Left Four Fingers</label>
        </div>
        <div class="finger-item">
          <input type="checkbox" id="rightFourCheck" disabled onchange="handleFingerChange('rightfour', this.checked)">
          <label for="rightFourCheck">Right Four Fingers</label>
        </div>
        <div class="finger-item">
          <input type="checkbox" id="twoThumbsCheck" disabled onchange="handleFingerChange('twothumbs', this.checked)">
          <label for="twoThumbsCheck">Two Thumbs</label>
        </div>
      </div>
    </div>

    <!-- Action Buttons Section -->
    <div class="control-section">
      <h3>Actions</h3>
      
      <button id="captureBtn" disabled onclick="startCapture()">Capture</button>
      <button id="enrollBtn" class="btn-success" disabled onclick="startEnroll()">Enroll</button>
      <button id="identifyBtn" disabled onclick="startIdentify()">Identify</button>
      <button id="clearBtn" onclick="clearData()">Clear</button>
    </div>

    <!-- Results Section -->
    <div class="control-section">
      <h3>Results</h3>
      <div id="response"></div>
      <img id="bmpImage" alt="Fingerprint BMP Image" style="display:none;" />
    </div>
  </div>

  <script>
    // Global variables
    var apiBaseUrl = 'http://localhost:5001/api';
    var deviceOpen = false;
    var capturedTemplates = {};
    var selectedFingers = [];

    // Finger mapping for slaps
    var slapsMapping = {
      'leftfour': { id: 12, name: 'Left Four Fingers' },
      'rightfour': { id: 13, name: 'Right Four Fingers' },
      'twothumbs': { id: 11, name: 'Two Thumbs' }
    };

    // DOM elements
    var statusDiv = document.getElementById('status');
    var responseDiv = document.getElementById('response');
    var deviceStatusSpan = document.getElementById('deviceStatus');
    var connectionStatusSpan = document.getElementById('connectionStatus');
    var bmpImage = document.getElementById('bmpImage');
    var personIdInput = document.getElementById('personId');
    var captureBtn = document.getElementById('captureBtn');
    var enrollBtn = document.getElementById('enrollBtn');
    var identifyBtn = document.getElementById('identifyBtn');
    var leftFourCheck = document.getElementById('leftFourCheck');
    var rightFourCheck = document.getElementById('rightFourCheck');
    var twoThumbsCheck = document.getElementById('twoThumbsCheck');

    function displayStatus(text) {
      statusDiv.textContent = text;
    }

    function displayResponse(data) {
      if (data.error) {
        responseDiv.innerHTML = '<span style="color: red;">❌ Error:</span> ' + data.error;
      } else if (data.status === "success" || data.success) {
        responseDiv.innerHTML = '<span style="color: green;">✅ Operation completed successfully.</span>';
        if (data.message) {
          responseDiv.innerHTML += '<br/>' + data.message;
        }
      } else {
        responseDiv.textContent = JSON.stringify(data, null, 2);
      }
    }

    function setInputsDisabled(disabled) {
      captureBtn.disabled = disabled || !deviceOpen;
      enrollBtn.disabled = disabled || !deviceOpen;
      identifyBtn.disabled = disabled || !deviceOpen;
      leftFourCheck.disabled = disabled || !deviceOpen;
      rightFourCheck.disabled = disabled || !deviceOpen;
      twoThumbsCheck.disabled = disabled || !deviceOpen;
    }

    function handleFingerChange(fingerId, isChecked) {
      if (isChecked) {
        if (selectedFingers.indexOf(fingerId) === -1) {
          selectedFingers.push(fingerId);
        }
      } else {
        var index = selectedFingers.indexOf(fingerId);
        if (index > -1) {
          selectedFingers.splice(index, 1);
        }
      }
    }

    // Simple XMLHttpRequest wrapper
    function makeApiCall(endpoint, method, data, callback) {
      var xhr = new XMLHttpRequest();
      var url = apiBaseUrl + '/' + endpoint;
      
      xhr.open(method || 'GET', url, true);
      xhr.setRequestHeader('Content-Type', 'application/json');
      
      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          try {
            var result = JSON.parse(xhr.responseText);
            if (xhr.status >= 200 && xhr.status < 300) {
              callback(null, result);
            } else {
              callback(new Error(result.error || 'HTTP ' + xhr.status), null);
            }
          } catch (e) {
            callback(new Error('Invalid JSON response'), null);
          }
        }
      };
      
      xhr.onerror = function() {
        callback(new Error('Network error'), null);
      };
      
      if (data && method === 'POST') {
        xhr.send(JSON.stringify(data));
      } else {
        xhr.send();
      }
    }

    function checkDeviceStatus() {
      displayStatus('Checking device status...');
      
      makeApiCall('health', 'GET', null, function(healthError, healthData) {
        if (healthError) {
          connectionStatusSpan.textContent = 'Error';
          deviceStatusSpan.textContent = 'Unknown';
          deviceOpen = false;
          displayStatus('❌ Failed to check device status');
          displayResponse({ error: 'Failed to connect to bridge application' });
          setInputsDisabled(false);
          return;
        }
        
        console.log('Health check result:', healthData);
        
        if (healthData.success && healthData.data && healthData.data.tcp_connection === 'connected') {
          connectionStatusSpan.textContent = 'Connected';
          
          makeApiCall('status', 'GET', null, function(statusError, statusData) {
            if (statusError) {
              deviceStatusSpan.textContent = 'Unknown';
              deviceOpen = false;
              displayStatus('❌ Failed to get device status');
              displayResponse({ error: 'Failed to get device status' });
              setInputsDisabled(false);
              return;
            }
            
            console.log('Device status result:', statusData);
            
            if (statusData.success && statusData.data) {
              var isDeviceConnected = statusData.data.DeviceConnected || statusData.data.device_connected || false;
              
              if (isDeviceConnected) {
                deviceOpen = true;
                deviceStatusSpan.textContent = 'Open and Ready';
                displayStatus('✅ Device is open and ready for fingerprint operations');
                displayResponse({ status: 'success', message: 'Device connected and ready. You can now capture fingerprints.' });
              } else {
                deviceOpen = false;
                deviceStatusSpan.textContent = 'Not Connected';
                displayStatus('❌ Device not connected');
                displayResponse({ error: 'Device is not connected. Please ensure the device is plugged in and opened in MainForm.' });
              }
            } else {
              deviceOpen = false;
              deviceStatusSpan.textContent = 'Unknown';
              displayStatus('❌ Invalid device status response');
              displayResponse({ error: 'Invalid device status response' });
            }
            
            setInputsDisabled(false);
          });
        } else {
          connectionStatusSpan.textContent = 'Disconnected';
          deviceStatusSpan.textContent = 'Unknown';
          deviceOpen = false;
          displayStatus('❌ Bridge application not running');
          displayResponse({ error: 'Bridge application is not running. Please start MultiFingerDemo.exe first.' });
          setInputsDisabled(false);
        }
      });
    }

    function refreshDeviceStatus() {
      checkDeviceStatus();
    }

    function startCapture() {
      if (!deviceOpen) {
        displayResponse({ error: 'Please ensure device is connected first' });
        return;
      }

      if (selectedFingers.length === 0) {
        displayResponse({ error: 'Please select at least one finger option' });
        return;
      }

      displayStatus('Capturing fingerprints...');
      setInputsDisabled(true);

      var captureResults = [];
      var completedCaptures = 0;

      for (var i = 0; i < selectedFingers.length; i++) {
        var fingerId = selectedFingers[i];
        var fingerMapping = slapsMapping[fingerId];

        if (!fingerMapping) {
          captureResults.push(fingerId + ' failed: Unknown finger');
          completedCaptures++;
          continue;
        }

        var captureData = {
          finger_position: fingerMapping.id,
          operation_type: 'slaps',
          timeout: 30,
          save_image: true
        };

        console.log('Calling capture API for ' + fingerId + ':', captureData);

        (function(currentFingerId, currentFingerName) {
          makeApiCall('fingerprint/capture', 'POST', captureData, function(error, result) {
            console.log('Capture API result for ' + currentFingerId + ':', result);

            if (error) {
              console.error('Capture error for ' + currentFingerId + ':', error);
              captureResults.push(currentFingerId + ' failed: ' + error.message);
            } else if (result.success && result.data) {
              capturedTemplates[currentFingerId] = {
                template: result.data.template_data || '',
                image: result.data.image_data || '',
                quality: result.data.quality || 'N/A'
              };

              if (result.data.image_data) {
                bmpImage.src = 'data:image/bmp;base64,' + result.data.image_data;
                bmpImage.style.display = 'block';
              }

              captureResults.push(currentFingerName + ' captured successfully');
            } else {
              var errorMsg = result.error || result.message || 'Unknown error';
              console.error('Capture failed for ' + currentFingerId + ':', result);
              captureResults.push(currentFingerId + ' failed: ' + errorMsg);
            }

            completedCaptures++;
            if (completedCaptures === selectedFingers.length) {
              var message = captureResults.join(', ');
              var hasErrors = false;
              for (var j = 0; j < captureResults.length; j++) {
                if (captureResults[j].indexOf('failed') !== -1) {
                  hasErrors = true;
                  break;
                }
              }
              displayResponse({ status: hasErrors ? 'error' : 'success', message: message });
              displayStatus('Capture completed');
              setInputsDisabled(false);
            }
          });
        })(fingerId, fingerMapping.name);
      }
    }

    function startEnroll() {
      var userId = personIdInput.value.trim();
      if (!userId) {
        displayResponse({ error: 'Please enter a User ID' });
        return;
      }

      if (Object.keys(capturedTemplates).length === 0) {
        displayResponse({ error: 'Please capture fingerprints first' });
        return;
      }

      displayStatus('Enrolling fingerprints...');
      setInputsDisabled(true);

      var enrollResults = [];
      var completedEnrolls = 0;
      var templateEntries = Object.keys(capturedTemplates);

      for (var i = 0; i < templateEntries.length; i++) {
        var fingerId = templateEntries[i];
        var templateData = capturedTemplates[fingerId];

        if (!templateData.template) {
          completedEnrolls++;
          continue;
        }

        var fingerMapping = slapsMapping[fingerId];
        var enrollData = {
          user_id: userId,
          finger_position: fingerMapping.id,
          template_data: templateData.template,
          image_data: templateData.image || '',
          image_quality: parseInt(templateData.quality) || 0
        };

        (function(currentFingerId, currentFingerName) {
          makeApiCall('fingerprint/enroll', 'POST', enrollData, function(error, result) {
            if (error) {
              enrollResults.push(currentFingerName + ' enroll failed: ' + error.message);
            } else if (result.success) {
              enrollResults.push(currentFingerName + ' enrolled successfully');
            } else {
              var errorMsg = result.error || result.message || 'Unknown error';
              enrollResults.push(currentFingerName + ' enroll failed: ' + errorMsg);
            }

            completedEnrolls++;
            if (completedEnrolls === templateEntries.length) {
              var message = enrollResults.join(', ');
              var hasErrors = false;
              for (var j = 0; j < enrollResults.length; j++) {
                if (enrollResults[j].indexOf('failed') !== -1) {
                  hasErrors = true;
                  break;
                }
              }
              displayResponse({ status: hasErrors ? 'error' : 'success', message: message });
              displayStatus('Enroll completed');

              if (!hasErrors) {
                capturedTemplates = {};
                bmpImage.style.display = 'none';
              }

              setInputsDisabled(false);
            }
          });
        })(fingerId, fingerMapping.name);
      }
    }

    function startIdentify() {
      if (Object.keys(capturedTemplates).length === 0) {
        displayResponse({ error: 'Please capture fingerprints first' });
        return;
      }

      displayStatus('Identifying fingerprints...');
      setInputsDisabled(true);

      var identifyResults = [];
      var completedIdentifies = 0;
      var templateEntries = Object.keys(capturedTemplates);

      for (var i = 0; i < templateEntries.length; i++) {
        var fingerId = templateEntries[i];
        var templateData = capturedTemplates[fingerId];

        if (!templateData.template) {
          completedIdentifies++;
          continue;
        }

        var fingerMapping = slapsMapping[fingerId];
        var identifyData = {
          template_data: templateData.template,
          threshold: 70
        };

        (function(currentFingerId, currentFingerName) {
          makeApiCall('fingerprint/identify', 'POST', identifyData, function(error, result) {
            if (error) {
              identifyResults.push(currentFingerName + ': Error - ' + error.message);
            } else if (result.success && result.data) {
              var userId = result.data.user_id || 'Unknown';
              var score = result.data.score || 'N/A';
              identifyResults.push(currentFingerName + ': ' + userId + ' (Score: ' + score + ')');
            } else {
              identifyResults.push(currentFingerName + ': No match');
            }

            completedIdentifies++;
            if (completedIdentifies === templateEntries.length) {
              var message = identifyResults.join(', ');
              displayResponse({ status: 'success', message: message });
              displayStatus('Identify completed');
              setInputsDisabled(false);
            }
          });
        })(fingerId, fingerMapping.name);
      }
    }

    function clearData() {
      capturedTemplates = {};
      bmpImage.style.display = 'none';
      responseDiv.innerHTML = '';
      displayStatus('Data cleared');
    }

    function initPage() {
      checkDeviceStatus();
      setInterval(checkDeviceStatus, 5000);
    }
  </script>
</body>
</html>
