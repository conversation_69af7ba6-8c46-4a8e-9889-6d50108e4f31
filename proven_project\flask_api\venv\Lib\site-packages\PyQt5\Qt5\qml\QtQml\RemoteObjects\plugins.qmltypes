import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtQml.RemoteObjects 1.0'

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        name: "QRemoteObjectAbstractPersistedStore"
        prototype: "QObject"
        exports: ["QtQml.RemoteObjects/PersistedStore 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "QRemoteObjectNode"
        prototype: "QObject"
        exports: ["QtQml.RemoteObjects/Node 1.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "ErrorCode"
            values: {
                "NoError": 0,
                "RegistryNotAcquired": 1,
                "RegistryAlreadyHosted": 2,
                "NodeIsNoServer": 3,
                "ServerAlreadyCreated": 4,
                "UnintendedRegistryHosting": 5,
                "OperationNotValidOnClientNode": 6,
                "SourceNotRegistered": 7,
                "MissingObjectName": 8,
                "HostUrlInvalid": 9,
                "ProtocolMismatch": 10,
                "ListenFailed": 11
            }
        }
        Property { name: "registryUrl"; type: "QUrl" }
        Property {
            name: "persistedStore"
            type: "QRemoteObjectAbstractPersistedStore"
            isPointer: true
        }
        Property { name: "heartbeatInterval"; type: "int" }
        Signal {
            name: "remoteObjectAdded"
            Parameter { type: "QRemoteObjectSourceLocation" }
        }
        Signal {
            name: "remoteObjectRemoved"
            Parameter { type: "QRemoteObjectSourceLocation" }
        }
        Signal {
            name: "error"
            Parameter { name: "errorCode"; type: "QRemoteObjectNode::ErrorCode" }
        }
        Signal {
            name: "heartbeatIntervalChanged"
            Parameter { name: "heartbeatInterval"; type: "int" }
        }
        Method {
            name: "connectToNode"
            type: "bool"
            Parameter { name: "address"; type: "QUrl" }
        }
    }
    Component {
        name: "QRemoteObjectSettingsStore"
        prototype: "QRemoteObjectAbstractPersistedStore"
        exports: ["QtQml.RemoteObjects/SettingsStore 1.0"]
        exportMetaObjectRevisions: [0]
    }
}
