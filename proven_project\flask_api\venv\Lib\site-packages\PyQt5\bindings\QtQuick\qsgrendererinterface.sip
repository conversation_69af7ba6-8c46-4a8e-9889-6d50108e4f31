// qsgrendererinterface.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_8_0 -)

class QSGRendererInterface /NoDefaultCtors/
{
%TypeHeaderCode
#include <qsgrendererinterface.h>
%End

public:
    enum GraphicsApi
    {
        Unknown,
        Software,
        OpenGL,
        Direct3D12,
%If (Qt_5_9_0 -)
        OpenVG,
%End
%If (Qt_5_14_0 -)
        OpenGLRhi,
%End
%If (Qt_5_14_0 -)
        Direct3D11Rhi,
%End
%If (Qt_5_14_0 -)
        VulkanRhi,
%End
%If (Qt_5_14_0 -)
        MetalRhi,
%End
%If (Qt_5_14_0 -)
        NullRhi,
%End
    };

    enum Resource
    {
        DeviceResource,
        CommandQueueResource,
        CommandListResource,
        PainterResource,
%If (Qt_5_14_0 -)
        RhiResource,
%End
%If (Qt_5_14_0 -)
        PhysicalDeviceResource,
%End
%If (Qt_5_14_0 -)
        OpenGLContextResource,
%End
%If (Qt_5_14_0 -)
        DeviceContextResource,
%End
%If (Qt_5_14_0 -)
        CommandEncoderResource,
%End
%If (Qt_5_14_0 -)
        VulkanInstanceResource,
%End
%If (Qt_5_14_0 -)
        RenderPassResource,
%End
    };

    enum ShaderType
    {
        UnknownShadingLanguage,
        GLSL,
        HLSL,
%If (Qt_5_14_0 -)
        RhiShader,
%End
    };

    enum ShaderCompilationType
    {
        RuntimeCompilation,
        OfflineCompilation,
    };

    typedef QFlags<QSGRendererInterface::ShaderCompilationType> ShaderCompilationTypes;

    enum ShaderSourceType
    {
        ShaderSourceString,
        ShaderSourceFile,
        ShaderByteCode,
    };

    typedef QFlags<QSGRendererInterface::ShaderSourceType> ShaderSourceTypes;
    virtual ~QSGRendererInterface();
    virtual QSGRendererInterface::GraphicsApi graphicsApi() const = 0;
    virtual void *getResource(QQuickWindow *window, QSGRendererInterface::Resource resource) const;
    virtual void *getResource(QQuickWindow *window, const char *resource) const;
    virtual QSGRendererInterface::ShaderType shaderType() const = 0;
    virtual QSGRendererInterface::ShaderCompilationTypes shaderCompilationType() const = 0;
    virtual QSGRendererInterface::ShaderSourceTypes shaderSourceType() const = 0;
%If (Qt_5_14_0 -)
    static bool isApiRhiBased(QSGRendererInterface::GraphicsApi api);
%End
};

%End
%If (Qt_5_8_0 -)
QFlags<QSGRendererInterface::ShaderCompilationType> operator|(QSGRendererInterface::ShaderCompilationType f1, QFlags<QSGRendererInterface::ShaderCompilationType> f2);
%End
%If (Qt_5_8_0 -)
QFlags<QSGRendererInterface::ShaderSourceType> operator|(QSGRendererInterface::ShaderSourceType f1, QFlags<QSGRendererInterface::ShaderSourceType> f2);
%End
