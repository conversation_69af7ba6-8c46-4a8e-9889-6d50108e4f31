// qbluetoothserver.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QBluetoothServer : public QObject
{
%TypeHeaderCode
#include <qbluetoothserver.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QBluetoothDeviceDiscoveryAgent, &sipType_QBluetoothDeviceDiscoveryAgent, -1, 1},
        {sipName_QBluetoothServiceDiscoveryAgent, &sipType_QBluetoothServiceDiscoveryAgent, -1, 2},
    #if QT_VERSION >= 0x050400
        {sipName_QLowEnergyService, &sipType_QLowEnergyService, -1, 3},
    #else
        {0, 0, -1, 3},
    #endif
        {sipName_QBluetoothTransferReply, &sipType_QBluetoothTransferReply, -1, 4},
        {sipName_QBluetoothTransferManager, &sipType_QBluetoothTransferManager, -1, 5},
        {sipName_QBluetoothServer, &sipType_QBluetoothServer, -1, 6},
    #if QT_VERSION >= 0x050400
        {sipName_QLowEnergyController, &sipType_QLowEnergyController, -1, 7},
    #else
        {0, 0, -1, 7},
    #endif
        {sipName_QBluetoothSocket, &sipType_QBluetoothSocket, -1, 8},
        {sipName_QBluetoothLocalDevice, &sipType_QBluetoothLocalDevice, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum Error
    {
        NoError,
        UnknownError,
        PoweredOffError,
        InputOutputError,
        ServiceAlreadyRegisteredError,
        UnsupportedProtocolError,
    };

    QBluetoothServer(QBluetoothServiceInfo::Protocol serverType, QObject *parent /TransferThis/ = 0);
    virtual ~QBluetoothServer();
    void close() /ReleaseGIL/;
    bool listen(const QBluetoothAddress &address = QBluetoothAddress(), quint16 port = 0) /ReleaseGIL/;
    QBluetoothServiceInfo listen(const QBluetoothUuid &uuid, const QString &serviceName = QString()) /ReleaseGIL/;
    bool isListening() const;
    void setMaxPendingConnections(int numConnections);
    int maxPendingConnections() const;
    bool hasPendingConnections() const;
    QBluetoothSocket *nextPendingConnection() /Factory/;
    QBluetoothAddress serverAddress() const;
    quint16 serverPort() const;
    void setSecurityFlags(QBluetooth::SecurityFlags security);
    QBluetooth::SecurityFlags securityFlags() const;
    QBluetoothServiceInfo::Protocol serverType() const;
    QBluetoothServer::Error error() const;

signals:
    void newConnection();
    void error(QBluetoothServer::Error);
};

%End
