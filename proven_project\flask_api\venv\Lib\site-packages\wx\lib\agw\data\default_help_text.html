<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
   <META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=iso-8859-1">
   <META NAME="GENERATOR" CONTENT="Mozilla/4.06 [en] (X11; I; Linux 2.0.35 i686) [Netscape]">
<TITLE>Configure Keyboard Shortcuts Help</TITLE>
</HEAD>

<h3>Configure Keyboard Shortcuts Help</h3>

Keyboard shortcuts can be useful tools for speeding up your workflow when working with <YOUR
APPLICATION>. Many tools and features have keyboard shortcuts assigned by default.

<p><br>

However, if you want to add a keyboard shortcut to a feature that doesn't have one, or change
an existing shortcut to one that feels more intuitive to you, <THIS APPLICATION> offers an easy
way to do this using the <i>Keyboard Shortcut Editor</i>. Just follow the steps below to start
customizing the <APPLICATION> to better suit the way that you work.

<p><br>


<h5>1. Open Subsection if Necessary</h5>

In the <i>Configure Keyboard Shortcuts</i> you can open sub-sections by clicking the small box
with a + sign in it next to each section name. In the screen grab, you can see I've opened the <i>Options</i>
sub-section as I'm going to add a keyboard shortcut to the <i>OptionsItem 1</i>.

<p><br>

<a href="ShortcutEditor_1.png"><img src="ShortcutEditor_1_thumb.png" alt="Open Subsections" align=center></a>
<br>

<p><br>


<h5>2. Assign New Keyboard Shortcut</h5>

Now you need to scroll to the tool or command that you want to edit and click on it to select it. When selected,
the text for that tool in the <i>Shortcut</i> column changes to read 'New accelerator...' and you can press
the key or combination of keys you want to assign as a shortcut.

<p><br>

<a href="ShortcutEditor_2.png"><img src="ShortcutEditor_2_thumb.png" alt="Assign New Keyboard Shortcut" align=center></a>
<br>

<p><br>

<h5>3. Remove or Save Shortcuts</h5>

I've changed the <i>OptionsItem 1</i>'s keyboard shortcut to Shift+Ctrl+F by pressing the Shift, Ctrl and F keys
simultaneously. If you want to remove a keyboard shortcut from any tool or command, just click on it to select it
and then when the 'New accelerator...' text displays, press the backspace key and the text will change to 'Disabled'.

Once you're happy that your keyboard shortcuts are set up as you wish, simply click <i>OK</i>.

<p><br>

<a href="ShortcutEditor_3.png"><img src="ShortcutEditor_3_thumb.png" alt="Remove or Save Shortcuts" align=center></a>
<br>

<p><br>

<h5>4. Beware of Reassigning Existing Shortcuts</h5>

If you thought my choice of Shift+Ctrl+F was an odd selection, I chose it because it was a keyboard combination
that hadn't already been assigned to any tool or command. If you try to assign a keyboard shortcut that is already
in use, an alert will open telling you what the shortcut is currently being used for. If you want to keep the
original shortcut, just click the <i>Cancel</i> button, otherwise click <i>Reassign shortcut</i> to make the
shortcut apply to your new selection.

<p><br>

<a href="ShortcutEditor_4.png"><img src="ShortcutEditor_4_thumb.png" alt="Beware of Reassigning Existing Shortcuts" align=center></a>
<br>

<p><br>


<h5>5. Don't Go Shortcut Crazy!</h5>

Don't feel that every tool or command should have a keyboard shortcut assigned to it and that you need to memorize
all of them. We all use applications in different ways - often using different tools and techniques to achieve
similar results - so concentrate on the tools that you use.

Taking some time to customize <YOUR APPLICATION> to work in a way that suits you can be a good investment of your time.
A well thought out series of keyboard shortcuts can have a dramatic effect on your workflow.

<p><br>


<h5>6. Useful Tips</h5>

<ul>
<li> Try to analyze how you work and which tools you use on a regular basis, then look at assigning easily accessed
keyboard combinations to these tools and commands only. </li>
<li>Don't be afraid to try out different shortcuts, or to reassign default shortcuts to new tools or commands.</li>
<li>Assigning shortcuts that are grouped together on the keyboard may mean you can select tools without moving
your hand, but using shortcuts that are more spaced out may reduce the possibility of you selecting the incorrect
tool. This will be down to personal preference.</li>
</ul>

<p><br>
