#----------------------------------------------------------------------------
# Name:        wx.lib.mixins.grid
# Purpose:     Helpful mix-in classes for wx.Grid
#
# Author:      <PERSON>
#
# Created:     5-June-2001
# Copyright:   (c) 2001-2020 by Total Control Software
# Licence:     wxWindows license
#----------------------------------------------------------------------------
# 12/14/2003 - <PERSON> (<EMAIL>)
#
# o 2.5 compatibility update.
# o Untested
#
# 12/21/2003 - <PERSON> (<EMAIL>)
#
# o wxGridAutoEditMixin -> GridAutoEditMixin
#

import  wx
import  wx.grid

#----------------------------------------------------------------------------


class GridAutoEditMixin:
    """A mix-in class that automatically enables the grid edit control when
    a cell is selected.

    If your class hooks EVT_GRID_SELECT_CELL be sure to call event.Skip so
    this handler will be called too.
    """

    def __init__(self):
        self.Bind(wx.grid.EVT_GRID_SELECT_CELL, self.__OnSelectCell)


    def __DoEnableEdit(self):
        if self.CanEnableCellControl():
            self.EnableCellEditControl()


    def __OnSelectCell(self, evt):
        wx.CallAfter(self.__DoEnableEdit)
        evt.Skip()

