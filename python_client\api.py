"""
Flask REST API for MultipleFinger Bridge
Provides HTTP endpoints that communicate with C# TCP server
"""

import os
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
from tcp_client import TcpClient, TcpClientError

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
TCP_HOST = os.getenv('TCP_HOST', 'localhost')
TCP_PORT = int(os.getenv('TCP_PORT', '8123'))  # Updated default to 8123
API_PORT = int(os.getenv('API_PORT', '5001'))  # Default to 5001 if not set
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'

# Force port 5001 to avoid conflicts
API_PORT = 5001

# Debug: Print configuration
print(f"Configuration loaded:")
print(f"  TCP_HOST: {TCP_HOST}")
print(f"  TCP_PORT: {TCP_PORT} (MainForm.cs TCP Bridge)")
print(f"  API_PORT: {API_PORT}")
print(f"  DEBUG: {DEBUG}")
print(f"  .env file path: {os.path.abspath('.env')}")
print(f"  Current working directory: {os.getcwd()}")

# Create TCP client instance
tcp_client = TcpClient(TCP_HOST, TCP_PORT)


# Simple validation functions (avoiding Marshmallow version issues)
def validate_capture_request(data):
    """Validate capture request data"""
    errors = {}

    # Required fields
    if 'finger_position' not in data:
        errors['finger_position'] = 'Required field'
    elif not isinstance(data['finger_position'], int) or not (1 <= data['finger_position'] <= 14):
        errors['finger_position'] = 'Must be an integer between 1 and 14 (1-10 for individual fingers, 11-14 for slaps)'

    # Optional fields with defaults
    data.setdefault('user_id', 'TEMP_USER')
    if not isinstance(data['user_id'], str) or not data['user_id'].strip():
        errors['user_id'] = 'Must be a non-empty string'

    data.setdefault('operation_type', 'flat')
    if data['operation_type'] not in ['flat', 'rolled', 'slaps']:
        errors['operation_type'] = 'Must be one of: flat, rolled, slaps'

    data.setdefault('timeout', 30)
    if not isinstance(data['timeout'], int) or not (1 <= data['timeout'] <= 300):
        errors['timeout'] = 'Must be an integer between 1 and 300'

    data.setdefault('save_image', True)
    if not isinstance(data['save_image'], bool):
        errors['save_image'] = 'Must be a boolean'

    return errors


def validate_identify_request(data):
    """Validate identify request data"""
    errors = {}

    # finger_index is required for IDENTIFY command
    if 'finger_index' not in data:
        errors['finger_index'] = 'Required field'
    elif not isinstance(data['finger_index'], int) or data['finger_index'] < 1 or data['finger_index'] > 13:
        errors['finger_index'] = 'Must be an integer between 1 and 13 (1-10 for individual fingers, 11=Two Thumbs, 12=Left Four, 13=Right Four)'

    # MainForm.cs IDENTIFY command doesn't use template_data or threshold
    # These are optional for compatibility
    data.setdefault('template_data', '')
    data.setdefault('threshold', 70)

    if 'threshold' in data and not isinstance(data['threshold'], int) or not (0 <= data['threshold'] <= 100):
        errors['threshold'] = 'Must be an integer between 0 and 100'

    return errors


def validate_verify_request(data):
    """Validate verify request data"""
    errors = {}

    if 'user_id' not in data:
        errors['user_id'] = 'Required field'
    elif not isinstance(data['user_id'], str) or not data['user_id'].strip():
        errors['user_id'] = 'Must be a non-empty string'

    if 'finger_position' not in data:
        errors['finger_position'] = 'Required field'
    elif not isinstance(data['finger_position'], int) or not (1 <= data['finger_position'] <= 10):
        errors['finger_position'] = 'Must be an integer between 1 and 10 (individual fingers only for verify)'

    return errors


def validate_enroll_request(data):
    """Validate enroll request data"""
    errors = {}

    if 'user_id' not in data:
        errors['user_id'] = 'Required field'
    elif not isinstance(data['user_id'], str) or not data['user_id'].strip():
        errors['user_id'] = 'Must be a non-empty string'

    if 'finger_position' not in data:
        errors['finger_position'] = 'Required field'
    elif not isinstance(data['finger_position'], int) or not (1 <= data['finger_position'] <= 13):
        errors['finger_position'] = 'Must be an integer between 1 and 13 (1-10 for individual fingers, 11=Two Thumbs, 12=Left Four, 13=Right Four)'

    if 'template_data' not in data:
        errors['template_data'] = 'Required field'
    elif not isinstance(data['template_data'], str):
        errors['template_data'] = 'Must be a string'

    data.setdefault('image_data', '')
    data.setdefault('image_quality', 0)

    if not isinstance(data['image_quality'], int) or not (0 <= data['image_quality'] <= 100):
        errors['image_quality'] = 'Must be an integer between 0 and 100'

    return errors


# Error handlers
def handle_validation_error(errors):
    return jsonify({
        'success': False,
        'error': 'Validation error',
        'details': errors
    }), 400


@app.errorhandler(TcpClientError)
def handle_tcp_error(e):
    return jsonify({
        'success': False,
        'error': 'Communication error',
        'details': str(e)
    }), 503


@app.errorhandler(Exception)
def handle_general_error(e):
    logger.error(f"Unexpected error: {e}")
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'details': str(e) if DEBUG else 'An unexpected error occurred'
    }), 500


# API Routes
@app.route('/api/status', methods=['GET'])
def get_status():
    """Get server and device status"""
    try:
        status_data = tcp_client.get_status()
        return jsonify({
            'success': True,
            'data': status_data
        })
    except TcpClientError as e:
        raise e


@app.route('/api/device/open', methods=['POST'])
def open_device():
    """Open fingerprint device"""
    try:
        result = tcp_client.send_command("OPEN_DEVICE")
        return jsonify({
            'success': True,
            'data': result
        })
    except TcpClientError as e:
        raise e


@app.route('/api/device/close', methods=['POST'])
def close_device():
    """Close fingerprint device"""
    try:
        logger.info("Attempting to close device...")
        result = tcp_client.send_command("CLOSE")
        logger.info(f"Close device result: {result}")
        return jsonify({
            'success': True,
            'data': result
        })
    except TcpClientError as e:
        logger.error(f"TcpClientError in close_device: {e}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected error in close_device: {e}")
        raise TcpClientError(f"Close device failed: {str(e)}")


@app.route('/api/device/info', methods=['GET'])
def get_device_info():
    """Get detailed device information"""
    try:
        result = tcp_client.send_command("DEVICE_INFO")
        return jsonify({
            'success': True,
            'data': result
        })
    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/capture-direct', methods=['POST'])
def capture_direct():
    """Direct capture like proven project - waits for real device interaction"""
    try:
        data = request.json or {}
        finger_index = data.get('finger_index', 1)

        logger.info(f"Direct capture: finger_index={finger_index}")

        # Use direct TCP socket like proven project
        import socket
        import time

        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(30)  # 30 second timeout for real capture
            s.connect(('127.0.0.1', 8123))
            command = f"CAPTURE {finger_index}\n"
            s.sendall(command.encode())

            logger.info(f"Sent command: {command.strip()}")
            setTip = lambda msg: logger.info(f"Status: {msg}")
            setTip("⏳ Waiting for fingerprint capture...")

            start_time = time.time()
            data_received = b""
            while True:
                chunk = s.recv(4096)
                if not chunk:
                    break
                data_received += chunk

            duration = time.time() - start_time
            logger.info(f"Response received in {duration:.2f} seconds")

            decoded = data_received.decode("utf-8", errors="ignore")
            lines = decoded.splitlines()

            result_message = "❌ No valid response"
            base64_bmp = ""

            for line in lines:
                clean_line = line.strip().lstrip("\ufeff")
                logger.info(f"Bridge response: {clean_line}")
                if clean_line.startswith("BMP:"):
                    base64_bmp = clean_line[4:]
                elif clean_line.upper().startswith("OK") or "✅" in clean_line:
                    result_message = clean_line
                elif "ERROR" in clean_line.upper():
                    result_message = clean_line

            status = "success" if "OK" in result_message.upper() or "✅" in result_message else "error"

            return jsonify({
                'success': status == "success",
                'data': {
                    'message': result_message,
                    'bmp_base64': base64_bmp,
                    'duration': duration,
                    'finger_index': finger_index
                }
            })

    except socket.timeout:
        return jsonify({
            'success': False,
            'error': 'Capture timeout - no fingerprint detected within 30 seconds'
        })
    except Exception as e:
        logger.error(f"Direct capture error: {e}")
        return jsonify({
            'success': False,
            'error': f'Capture failed: {str(e)}'
        })


@app.route('/api/fingerprint/capture-flat', methods=['POST'])
def capture_flat():
    """Direct flat capture - waits for real device interaction"""
    try:
        data = request.json or {}
        finger_index = data.get('finger_index', 1)

        if finger_index < 1 or finger_index > 10:
            return jsonify({
                'success': False,
                'error': 'Flat capture only supports fingers 1-10'
            })

        logger.info(f"Direct flat capture: finger_index={finger_index}")

        # Use direct TCP socket for flat capture
        import socket
        import time

        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(30)  # 30 second timeout for real capture
            s.connect(('127.0.0.1', 8123))
            command = f"CAPTURE_FLAT {finger_index}\n"
            s.sendall(command.encode())

            logger.info(f"Sent command: {command.strip()}")

            start_time = time.time()
            data_received = b""
            while True:
                chunk = s.recv(4096)
                if not chunk:
                    break
                data_received += chunk

            duration = time.time() - start_time
            logger.info(f"Flat capture response received in {duration:.2f} seconds")

            decoded = data_received.decode("utf-8", errors="ignore")
            lines = decoded.splitlines()

            result_message = "❌ No valid response"
            base64_bmp = ""

            for line in lines:
                clean_line = line.strip().lstrip("\ufeff")
                logger.info(f"Bridge response: {clean_line}")
                if clean_line.startswith("BMP:"):
                    base64_bmp = clean_line[4:]
                elif clean_line.upper().startswith("OK") or "✅" in clean_line:
                    result_message = clean_line
                elif "ERROR" in clean_line.upper():
                    result_message = clean_line

            status = "success" if "OK" in result_message.upper() or "✅" in result_message else "error"

            return jsonify({
                'success': status == "success",
                'data': {
                    'message': result_message,
                    'bmp_base64': base64_bmp,
                    'duration': duration,
                    'finger_index': finger_index,
                    'capture_type': 'flat'
                }
            })

    except socket.timeout:
        return jsonify({
            'success': False,
            'error': 'Flat capture timeout - no fingerprint detected within 30 seconds'
        })
    except Exception as e:
        logger.error(f"Direct flat capture error: {e}")
        return jsonify({
            'success': False,
            'error': f'Flat capture failed: {str(e)}'
        })


@app.route('/api/fingerprint/capture-rolled', methods=['POST'])
def capture_rolled():
    """Direct rolled capture - waits for real device interaction"""
    try:
        data = request.json or {}
        finger_index = data.get('finger_index', 1)

        if finger_index < 1 or finger_index > 10:
            return jsonify({
                'success': False,
                'error': 'Rolled capture only supports fingers 1-10'
            })

        logger.info(f"Direct rolled capture: finger_index={finger_index}")

        # Use direct TCP socket for rolled capture
        import socket
        import time

        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(30)  # 30 second timeout for real capture
            s.connect(('127.0.0.1', 8123))
            command = f"CAPTURE_ROLLED {finger_index}\n"
            s.sendall(command.encode())

            logger.info(f"Sent command: {command.strip()}")

            start_time = time.time()
            data_received = b""
            while True:
                chunk = s.recv(4096)
                if not chunk:
                    break
                data_received += chunk

            duration = time.time() - start_time
            logger.info(f"Rolled capture response received in {duration:.2f} seconds")

            decoded = data_received.decode("utf-8", errors="ignore")
            lines = decoded.splitlines()

            result_message = "❌ No valid response"
            base64_bmp = ""

            for line in lines:
                clean_line = line.strip().lstrip("\ufeff")
                logger.info(f"Bridge response: {clean_line}")
                if clean_line.startswith("BMP:"):
                    base64_bmp = clean_line[4:]
                elif clean_line.upper().startswith("OK") or "✅" in clean_line:
                    result_message = clean_line
                elif "ERROR" in clean_line.upper():
                    result_message = clean_line

            status = "success" if "OK" in result_message.upper() or "✅" in result_message else "error"

            return jsonify({
                'success': status == "success",
                'data': {
                    'message': result_message,
                    'bmp_base64': base64_bmp,
                    'duration': duration,
                    'finger_index': finger_index,
                    'capture_type': 'rolled'
                }
            })

    except socket.timeout:
        return jsonify({
            'success': False,
            'error': 'Rolled capture timeout - no fingerprint detected within 30 seconds'
        })
    except Exception as e:
        logger.error(f"Direct rolled capture error: {e}")
        return jsonify({
            'success': False,
            'error': f'Rolled capture failed: {str(e)}'
        })


@app.route('/api/fingerprint/capture-and-save', methods=['POST'])
def capture_and_save():
    """New unified capture and save endpoint - captures fingerprint and automatically saves to database"""
    try:
        data = request.json or {}
        person_id = data.get('person_id', '').strip()
        finger_index = data.get('finger_index', 1)

        # Validation
        if not person_id:
            return jsonify({
                'success': False,
                'error': 'person_id is required and cannot be empty'
            })

        if not isinstance(finger_index, int) or finger_index < 1 or finger_index > 13:
            return jsonify({
                'success': False,
                'error': 'finger_index must be between 1-13 (1-10: individual fingers, 11: two thumbs, 12: left 4 fingers, 13: right 4 fingers)'
            })

        logger.info(f"Capture and Save: person_id={person_id}, finger_index={finger_index}")

        # Use direct TCP socket to call new CAPTURE_AND_SAVE command
        import socket
        import time
        import json

        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(45)  # Longer timeout for capture + save process
            s.connect(('127.0.0.1', 8888))  # Use port 8888 for new TCP bridge
            command = f"CAPTURE_AND_SAVE {person_id} {finger_index}\n"
            s.sendall(command.encode())

            logger.info(f"Sent command: {command.strip()}")

            start_time = time.time()
            data_received = b""
            while True:
                chunk = s.recv(4096)
                if not chunk:
                    break
                data_received += chunk
                # Check if we have complete JSON response
                if b'}' in data_received:
                    break

            duration = time.time() - start_time
            logger.info(f"Capture and Save response received in {duration:.2f} seconds")

            decoded = data_received.decode("utf-8", errors="ignore")
            lines = decoded.splitlines()

            # Look for JSON response
            json_response = None
            result_message = "❌ No valid response"

            for line in lines:
                clean_line = line.strip().lstrip("\ufeff")
                logger.info(f"Bridge response: {clean_line}")

                # Try to parse JSON response
                if clean_line.startswith('{') and clean_line.endswith('}'):
                    try:
                        json_response = json.loads(clean_line)
                        logger.info(f"Parsed JSON response: {json_response}")
                        break
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse JSON: {clean_line}")

                # Fallback to text response
                if clean_line.upper().startswith("OK") or "✅" in clean_line:
                    result_message = clean_line
                elif "ERROR" in clean_line.upper():
                    result_message = clean_line

            # Return structured response
            if json_response:
                return jsonify({
                    'success': json_response.get('status') == 'success',
                    'data': {
                        'person_id': json_response.get('person_id'),
                        'message': json_response.get('message'),
                        'fingers': json_response.get('fingers', []),
                        'duration': duration
                    }
                })
            else:
                # Fallback response
                status = "success" if "OK" in result_message.upper() or "✅" in result_message else "error"
                return jsonify({
                    'success': status == "success",
                    'data': {
                        'person_id': person_id,
                        'message': result_message,
                        'finger_index': finger_index,
                        'duration': duration
                    }
                })

    except socket.timeout:
        return jsonify({
            'success': False,
            'error': 'Capture and Save timeout - operation took longer than 45 seconds'
        })
    except Exception as e:
        logger.error(f"Capture and Save error: {e}")
        return jsonify({
            'success': False,
            'error': f'Capture and Save failed: {str(e)}'
        })


@app.route('/api/fingerprint/capture', methods=['POST'])
def capture_fingerprint():
    """Initiate fingerprint capture"""
    try:
        # Get and validate request data
        data = request.json or {}
        errors = validate_capture_request(data)

        if errors:
            return handle_validation_error(errors)

        # Send capture command using MainForm.cs protocol
        result = tcp_client.capture_fingerprint(
            finger_position=data['finger_position'],
            user_id=data['user_id'],
            operation_type=data['operation_type'],
            timeout=data['timeout'],
            save_image=data['save_image']
        )

        return jsonify({
            'success': True,
            'data': result
        })

    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/identify', methods=['POST'])
def identify_fingerprint():
    """Identify a fingerprint template (1:N matching)"""
    try:
        # Get and validate request data
        data = request.json or {}
        errors = validate_identify_request(data)

        if errors:
            return handle_validation_error(errors)

        # Send identify command using MainForm.cs protocol
        result = tcp_client.identify_fingerprint(
            finger_index=data['finger_index'],
            template_data=data.get('template_data'),
            threshold=data['threshold']
        )

        return jsonify({
            'success': True,
            'data': result
        })

    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/verify', methods=['POST'])
def verify_fingerprint():
    """Verify a fingerprint against specific user template (1:1 matching)"""
    try:
        # Get and validate request data
        data = request.json or {}
        errors = validate_verify_request(data)

        if errors:
            return handle_validation_error(errors)

        # Send verify command using MainForm.cs protocol
        result = tcp_client.verify_fingerprint(
            user_id=data['user_id'],
            finger_position=data['finger_position']
        )

        return jsonify({
            'success': True,
            'data': result
        })

    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/enroll', methods=['POST'])
def enroll_fingerprint():
    """Enroll a fingerprint template"""
    try:
        # Get and validate request data
        data = request.json or {}
        errors = validate_enroll_request(data)

        if errors:
            return handle_validation_error(errors)

        # Send enroll command using MainForm.cs protocol
        result = tcp_client.enroll_fingerprint(
            user_id=data['user_id'],
            finger_position=data['finger_position'],  # Required parameter
            template_data=data.get('template_data', ''),
            image_data=data.get('image_data', ''),
            image_quality=data.get('image_quality', 0)
        )

        return jsonify({
            'success': True,
            'data': result
        })

    except TcpClientError as e:
        raise e


@app.route('/api/fingerprint/captured-data', methods=['GET'])
def get_captured_data():
    """Get captured fingerprint data from bridge application"""
    try:
        result = tcp_client.get_captured_data()
        return jsonify({
            'success': True,
            'data': result
        })
    except TcpClientError as e:
        raise e


@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Test TCP connection
        tcp_connected = tcp_client.test_connection()
        
        return jsonify({
            'success': True,
            'data': {
                'api_status': 'running',
                'tcp_connection': 'connected' if tcp_connected else 'disconnected',
                'tcp_host': TCP_HOST,
                'tcp_port': TCP_PORT
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Health check failed',
            'details': str(e)
        }), 503


@app.route('/api/fingerprint/positions', methods=['GET'])
def get_finger_positions():
    """Get available finger positions"""
    positions = {
        1: "Right Thumb",
        2: "Right Index",
        3: "Right Middle",
        4: "Right Ring",
        5: "Right Little",
        6: "Left Thumb",
        7: "Left Index",
        8: "Left Middle",
        9: "Left Ring",
        10: "Left Little",
        11: "Two Thumbs",
        12: "Left Four Fingers",
        13: "Right Four Fingers",
        14: "Two Fingers"
    }

    return jsonify({
        'success': True,
        'data': {
            'positions': positions,
            'operation_types': ["flat", "rolled", "slaps"]
        }
    })


@app.route('/', methods=['GET'])
def index():
    """API information endpoint"""
    return jsonify({
        'name': 'MultipleFinger Bridge REST API',
        'version': '1.0.0',
        'description': 'REST API wrapper for MultipleFinger C# application',
        'endpoints': {
            'GET /api/status': 'Get server and device status',
            'POST /api/device/open': 'Open fingerprint device',
            'POST /api/device/close': 'Close fingerprint device',
            'GET /api/device/info': 'Get detailed device information',
            'POST /api/fingerprint/capture': 'Initiate fingerprint capture',
            'POST /api/fingerprint/capture-and-save': 'NEW: Unified capture and save to database',
            'POST /api/fingerprint/identify': 'Identify fingerprint template (1:N matching)',
            'POST /api/fingerprint/verify': 'Verify fingerprint against specific user (1:1 matching)',
            'POST /api/fingerprint/enroll': 'Enroll fingerprint template',
            'GET /api/fingerprint/captured-data': 'Get captured data',
            'GET /api/fingerprint/positions': 'Get finger positions',
            'GET /api/health': 'Health check'
        },
        'tcp_server': {
            'host': TCP_HOST,
            'port': TCP_PORT
        }
    })


if __name__ == '__main__':
    logger.info(f"Starting MultipleFinger Bridge REST API on port {API_PORT}")
    logger.info(f"TCP Server: {TCP_HOST}:{TCP_PORT}")
    
    app.run(
        host='0.0.0.0',
        port=API_PORT,
        debug=DEBUG
    )
