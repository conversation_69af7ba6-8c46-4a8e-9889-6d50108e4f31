<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MultipleFinger Bridge - Simple Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .status-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn:hover { opacity: 0.8; }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .finger-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .finger-btn {
            padding: 15px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            background: white;
            transition: background-color 0.2s;
        }
        .finger-btn:hover { background-color: #f8f9fa; }
        .finger-btn.selected { background-color: #007bff; color: white; }
        .finger-btn.captured { background-color: #28a745; color: white; }
        .results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MultipleFinger Bridge - Simple Interface</h1>
            <p>Direct API Interface (No CSP Issues)</p>
        </div>

        <!-- Status Panel -->
        <div class="status-panel">
            <h3>System Status</h3>
            <p><strong>API Status:</strong> <span id="apiStatus">Checking...</span></p>
            <p><strong>Device Status:</strong> <span id="deviceStatus">Checking...</span></p>
            <button id="refreshBtn" class="btn btn-primary">Refresh Status</button>
        </div>

        <!-- Fingerprint Selection -->
        <div class="form-group">
            <label for="operationType">Operation Type:</label>
            <select id="operationType">
                <option value="flat">Flat</option>
                <option value="rolled">Rolled</option>
                <option value="slaps">Slaps</option>
            </select>
        </div>

        <div class="finger-grid">
            <div class="finger-btn" data-position="1">Right Thumb (1)</div>
            <div class="finger-btn" data-position="2">Right Index (2)</div>
            <div class="finger-btn" data-position="3">Right Middle (3)</div>
            <div class="finger-btn" data-position="4">Right Ring (4)</div>
            <div class="finger-btn" data-position="5">Right Little (5)</div>
            <div class="finger-btn" data-position="6">Left Thumb (6)</div>
            <div class="finger-btn" data-position="7">Left Index (7)</div>
            <div class="finger-btn" data-position="8">Left Middle (8)</div>
            <div class="finger-btn" data-position="9">Left Ring (9)</div>
            <div class="finger-btn" data-position="10">Left Little (10)</div>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin: 20px 0;">
            <button id="captureBtn" class="btn btn-primary">Capture Selected</button>
            <button id="getCapturedBtn" class="btn btn-warning">Get Captured Data</button>
        </div>

        <!-- Enrollment Section -->
        <div class="form-group">
            <label for="userId">User ID for Enrollment:</label>
            <input type="text" id="userId" placeholder="Enter user ID">
        </div>
        <div style="text-align: center;">
            <button id="enrollBtn" class="btn btn-success">Enroll Selected Finger</button>
            <button id="identifyBtn" class="btn btn-warning">Identify</button>
        </div>

        <!-- Results Display -->
        <div id="results" class="results" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5001/api';
        let selectedFinger = null;

        // DOM elements
        const apiStatusEl = document.getElementById('apiStatus');
        const deviceStatusEl = document.getElementById('deviceStatus');
        const operationTypeEl = document.getElementById('operationType');
        const userIdEl = document.getElementById('userId');
        const resultsEl = document.getElementById('results');
        const refreshBtn = document.getElementById('refreshBtn');
        const captureBtn = document.getElementById('captureBtn');
        const getCapturedBtn = document.getElementById('getCapturedBtn');
        const enrollBtn = document.getElementById('enrollBtn');
        const identifyBtn = document.getElementById('identifyBtn');

        // Utility functions
        function showResults(content) {
            resultsEl.innerHTML = content;
            resultsEl.style.display = 'block';
        }

        function setButtonsDisabled(disabled) {
            captureBtn.disabled = disabled;
            getCapturedBtn.disabled = disabled;
            enrollBtn.disabled = disabled;
            identifyBtn.disabled = disabled;
            refreshBtn.disabled = disabled;
        }

        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method,
                    headers: { 'Content-Type': 'application/json' }
                };
                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(API_BASE_URL + '/' + endpoint, options);
                const result = await response.json();
                return result;
            } catch (error) {
                throw new Error('API call failed: ' + error.message);
            }
        }

        // Status check
        async function checkStatus() {
            try {
                const health = await apiCall('health');
                const status = await apiCall('status');
                
                if (health.success) {
                    const tcpStatus = health.data.tcp_connection === 'connected' ? '✅' : '❌';
                    apiStatusEl.textContent = tcpStatus + ' API: ' + health.data.api_status;
                    apiStatusEl.style.color = health.data.tcp_connection === 'connected' ? 'green' : 'red';
                }
                
                if (status.success) {
                    const deviceStatus = status.data.device_connected ? '✅ Connected' : '❌ Disconnected';
                    deviceStatusEl.textContent = deviceStatus;
                    deviceStatusEl.style.color = status.data.device_connected ? 'green' : 'red';
                }
            } catch (error) {
                apiStatusEl.textContent = '❌ API Error: ' + error.message;
                apiStatusEl.style.color = 'red';
                deviceStatusEl.textContent = '❌ Disconnected';
                deviceStatusEl.style.color = 'red';
            }
        }

        // Finger selection
        document.querySelectorAll('.finger-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove previous selection
                document.querySelectorAll('.finger-btn').forEach(b => b.classList.remove('selected'));
                // Select this finger
                this.classList.add('selected');
                selectedFinger = parseInt(this.dataset.position);
            });
        });

        // Event listeners
        refreshBtn.addEventListener('click', checkStatus);

        captureBtn.addEventListener('click', async function() {
            if (!selectedFinger) {
                alert('Please select a finger first');
                return;
            }

            setButtonsDisabled(true);
            showResults('<div><span class="spinner"></span> Capturing fingerprint...</div>');

            try {
                const result = await apiCall('fingerprint/capture', 'POST', {
                    finger_position: selectedFinger,
                    operation_type: operationTypeEl.value,
                    timeout: 30,
                    save_image: true
                });

                if (result.success) {
                    showResults('<div style="color: green;">✅ Fingerprint captured successfully!</div><pre>' + JSON.stringify(result.data, null, 2) + '</pre>');
                    // Mark finger as captured
                    document.querySelector(`[data-position="${selectedFinger}"]`).classList.add('captured');
                } else {
                    showResults('<div style="color: red;">❌ Capture failed: ' + (result.error || 'Unknown error') + '</div>');
                }
            } catch (error) {
                showResults('<div style="color: red;">❌ Error: ' + error.message + '</div>');
            } finally {
                setButtonsDisabled(false);
            }
        });

        getCapturedBtn.addEventListener('click', async function() {
            setButtonsDisabled(true);
            showResults('<div><span class="spinner"></span> Getting captured data...</div>');

            try {
                const result = await apiCall('fingerprint/captured-data');
                if (result.success) {
                    showResults('<div style="color: green;">✅ Captured data retrieved!</div><pre>' + JSON.stringify(result.data, null, 2) + '</pre>');
                } else {
                    showResults('<div style="color: red;">❌ Failed to get data: ' + (result.error || 'Unknown error') + '</div>');
                }
            } catch (error) {
                showResults('<div style="color: red;">❌ Error: ' + error.message + '</div>');
            } finally {
                setButtonsDisabled(false);
            }
        });

        enrollBtn.addEventListener('click', async function() {
            if (!selectedFinger) {
                alert('Please select a finger first');
                return;
            }
            if (!userIdEl.value.trim()) {
                alert('Please enter a user ID');
                return;
            }

            setButtonsDisabled(true);
            showResults('<div><span class="spinner"></span> Enrolling fingerprint...</div>');

            try {
                // First get captured data for the selected finger
                const capturedResult = await apiCall('fingerprint/captured-data');
                if (!capturedResult.success || !capturedResult.data.length) {
                    throw new Error('No captured data available. Please capture a fingerprint first.');
                }

                // Find data for selected finger
                const fingerData = capturedResult.data.find(d => d.finger_position === selectedFinger);
                if (!fingerData) {
                    throw new Error('No captured data for selected finger. Please capture it first.');
                }

                // Enroll the fingerprint
                const result = await apiCall('fingerprint/enroll', 'POST', {
                    user_id: userIdEl.value.trim(),
                    finger_position: selectedFinger,
                    template_data: fingerData.template_data || '',
                    image_data: fingerData.image_data || '',
                    image_quality: fingerData.quality || 0
                });

                if (result.success) {
                    showResults('<div style="color: green;">✅ Fingerprint enrolled successfully!</div><pre>' + JSON.stringify(result.data, null, 2) + '</pre>');
                } else {
                    showResults('<div style="color: red;">❌ Enrollment failed: ' + (result.error || 'Unknown error') + '</div>');
                }
            } catch (error) {
                showResults('<div style="color: red;">❌ Error: ' + error.message + '</div>');
            } finally {
                setButtonsDisabled(false);
            }
        });

        identifyBtn.addEventListener('click', async function() {
            if (!selectedFinger) {
                alert('Please select a finger first');
                return;
            }

            setButtonsDisabled(true);
            showResults('<div><span class="spinner"></span> Identifying fingerprint...</div>');

            try {
                // First get captured data for the selected finger
                const capturedResult = await apiCall('fingerprint/captured-data');
                if (!capturedResult.success || !capturedResult.data.length) {
                    throw new Error('No captured data available. Please capture a fingerprint first.');
                }

                // Find data for selected finger
                const fingerData = capturedResult.data.find(d => d.finger_position === selectedFinger);
                if (!fingerData) {
                    throw new Error('No captured data for selected finger. Please capture it first.');
                }

                // Identify the fingerprint
                const result = await apiCall('fingerprint/identify', 'POST', {
                    template_data: fingerData.template_data || '',
                    threshold: 70
                });

                if (result.success) {
                    showResults('<div style="color: green;">✅ Identification completed!</div><pre>' + JSON.stringify(result.data, null, 2) + '</pre>');
                } else {
                    showResults('<div style="color: red;">❌ Identification failed: ' + (result.error || 'Unknown error') + '</div>');
                }
            } catch (error) {
                showResults('<div style="color: red;">❌ Error: ' + error.message + '</div>');
            } finally {
                setButtonsDisabled(false);
            }
        });

        // Initialize
        checkStatus();
    </script>
</body>
</html>
