// qlowenergyadvertisingdata.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_7_0 -)

class QLowEnergyAdvertisingData
{
%TypeHeaderCode
#include <qlowenergyadvertisingdata.h>
%End

public:
    QLowEnergyAdvertisingData();
    QLowEnergyAdvertisingData(const QLowEnergyAdvertisingData &other);
    ~QLowEnergyAdvertisingData();
    void setLocalName(const QString &name);
    QString localName() const;
    static quint16 invalidManufacturerId();
    void setManufacturerData(quint16 id, const QByteArray &data);
    quint16 manufacturerId() const;
    QByteArray manufacturerData() const;
    void setIncludePowerLevel(bool doInclude);
    bool includePowerLevel() const;

    enum Discoverability
    {
        DiscoverabilityNone,
        DiscoverabilityLimited,
        DiscoverabilityGeneral,
    };

    void setDiscoverability(QLowEnergyAdvertisingData::Discoverability mode);
    QLowEnergyAdvertisingData::Discoverability discoverability() const;
    void setServices(const QList<QBluetoothUuid> &services);
    QList<QBluetoothUuid> services() const;
    void setRawData(const QByteArray &data);
    QByteArray rawData() const;
    void swap(QLowEnergyAdvertisingData &other);
};

%End
%If (Qt_5_7_0 -)
bool operator==(const QLowEnergyAdvertisingData &data1, const QLowEnergyAdvertisingData &data2);
%End
%If (Qt_5_7_0 -)
bool operator!=(const QLowEnergyAdvertisingData &data1, const QLowEnergyAdvertisingData &data2);
%End
