# wx.lib.plot

A simple, light-weight plotting package for wxPython Phoenix.

Based on wxPlotCanvas
Written by <PERSON><PERSON>, <PERSON><PERSON>;
Ported to wxPython: <PERSON><PERSON>, Feb 1999

This is a simple, light weight plotting module that can be used with
Boa or easily integrated into your own wxPython application. The
emphasis is on small size and fast plotting for large data sets. It
has a reasonable number of features to do line and scatter graphs
easily as well as simple bar graphs. It is not as sophisticated or
as powerful as SciPy Plt or Chaco. Both of these are great packages
but consume huge amounts of computer resources for simple plots.
They can be found at http://scipy.com
