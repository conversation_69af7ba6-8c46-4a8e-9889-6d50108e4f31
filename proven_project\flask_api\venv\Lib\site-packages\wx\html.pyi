# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
This module contains a widget class and supporting classes for a generic HTML
renderer.  It supports only a subset of the HTML standards, and no Javascript
or CSS, but it is relatively lightweight and has no platform dependencies.  It
is suitable for displaying simple HTML documents, such as the application's
documentation or built-in help pages.

.. note:: Due to some internal dynamic initialization in wxWidgets, this
          module should be imported **before** the :class:`wx.App` object is
          created.
"""
#-- begin-_html --#

import wx
#-- end-_html --#
#-- begin-htmldefs --#
HTML_ALIGN_LEFT: int
HTML_ALIGN_RIGHT: int
HTML_ALIGN_JUSTIFY: int
HTML_ALIGN_TOP: int
HTML_ALIGN_BOTTOM: int
HTML_ALIGN_CENTER: int
HTML_CLR_FOREGROUND: int
HTML_CLR_BACKGROUND: int
HTML_CLR_TRANSPARENT_BACKGROUND: int
HTML_UNITS_PIXELS: int
HTML_UNITS_PERCENT: int
HTML_INDENT_LEFT: int
HTML_INDENT_RIGHT: int
HTML_INDENT_TOP: int
HTML_INDENT_BOTTOM: int
HTML_INDENT_HORIZONTAL: int
HTML_INDENT_VERTICAL: int
HTML_INDENT_ALL: int
HTML_COND_ISANCHOR: int
HTML_COND_ISIMAGEMAP: int
HTML_COND_USER: int
INT_MAX: int
#-- end-htmldefs --#
#-- begin-htmlcell --#

class _HtmlSelectionState(IntEnum):
    HTML_SEL_OUT = auto()
    HTML_SEL_IN = auto()
    HTML_SEL_CHANGING = auto()
HtmlSelectionState: TypeAlias = Union[_HtmlSelectionState, int]
HTML_SEL_OUT = _HtmlSelectionState.HTML_SEL_OUT
HTML_SEL_IN = _HtmlSelectionState.HTML_SEL_IN
HTML_SEL_CHANGING = _HtmlSelectionState.HTML_SEL_CHANGING

class _enum_27(IntEnum):
    HTML_FIND_EXACT = auto()
    HTML_FIND_NEAREST_BEFORE = auto()
    HTML_FIND_NEAREST_AFTER = auto()
HTML_FIND_EXACT = _enum_27.HTML_FIND_EXACT
HTML_FIND_NEAREST_BEFORE = _enum_27.HTML_FIND_NEAREST_BEFORE
HTML_FIND_NEAREST_AFTER = _enum_27.HTML_FIND_NEAREST_AFTER

class _HtmlScriptMode(IntEnum):
    HTML_SCRIPT_NORMAL = auto()
    HTML_SCRIPT_SUB = auto()
    HTML_SCRIPT_SUP = auto()
HtmlScriptMode: TypeAlias = Union[_HtmlScriptMode, int]
HTML_SCRIPT_NORMAL = _HtmlScriptMode.HTML_SCRIPT_NORMAL
HTML_SCRIPT_SUB = _HtmlScriptMode.HTML_SCRIPT_SUB
HTML_SCRIPT_SUP = _HtmlScriptMode.HTML_SCRIPT_SUP

class HtmlSelection:
    """
    HtmlSelection() -> None
    """

    def __init__(self) -> None:
        """
        HtmlSelection() -> None
        """

    @overload
    def Set(self, fromCell: HtmlCell, toCell: HtmlCell) -> None:
        ...

    @overload
    def Set(self, fromPos: wx.Point, fromCell: HtmlCell, toPos: wx.Point, toCell: HtmlCell) -> None:
        """
        Set(fromPos, fromCell, toPos, toCell) -> None
        Set(fromCell, toCell) -> None
        """

    def GetFromCell(self) -> HtmlCell:
        """
        GetFromCell() -> HtmlCell
        """

    def GetToCell(self) -> HtmlCell:
        """
        GetToCell() -> HtmlCell
        """

    def GetFromPos(self) -> wx.Point:
        """
        GetFromPos() -> wx.Point
        """

    def GetToPos(self) -> wx.Point:
        """
        GetToPos() -> wx.Point
        """

    def ClearFromToCharacterPos(self) -> None:
        """
        ClearFromToCharacterPos() -> None
        """

    def AreFromToCharacterPosSet(self) -> bool:
        """
        AreFromToCharacterPosSet() -> bool
        """

    def SetFromCharacterPos(self, pos: int) -> None:
        """
        SetFromCharacterPos(pos) -> None
        """

    def SetToCharacterPos(self, pos: int) -> None:
        """
        SetToCharacterPos(pos) -> None
        """

    def GetFromCharacterPos(self) -> int:
        """
        GetFromCharacterPos() -> int
        """

    def GetToCharacterPos(self) -> int:
        """
        GetToCharacterPos() -> int
        """

    def IsEmpty(self) -> bool:
        """
        IsEmpty() -> bool
        """
    @property
    def FromCell(self) -> HtmlCell: ...
    @property
    def FromCharacterPos(self) -> int: ...
    @FromCharacterPos.setter
    def FromCharacterPos(self, value: int, /) -> None: ...
    @property
    def FromPos(self) -> wx.Point: ...
    @property
    def ToCell(self) -> HtmlCell: ...
    @property
    def ToCharacterPos(self) -> int: ...
    @ToCharacterPos.setter
    def ToCharacterPos(self, value: int, /) -> None: ...
    @property
    def ToPos(self) -> wx.Point: ...
# end of class HtmlSelection


class HtmlRenderingState:
    """
    HtmlRenderingState() -> None
    
    Selection state is passed to wxHtmlCell::Draw so that it can render
    itself differently e.g.
    """

    def __init__(self) -> None:
        """
        HtmlRenderingState() -> None
        
        Selection state is passed to wxHtmlCell::Draw so that it can render
        itself differently e.g.
        """

    def SetSelectionState(self, s: HtmlSelectionState) -> None:
        """
        SetSelectionState(s) -> None
        """

    def GetSelectionState(self) -> HtmlSelectionState:
        """
        GetSelectionState() -> HtmlSelectionState
        """

    def SetFgColour(self, c: wx.Colour) -> None:
        """
        SetFgColour(c) -> None
        """

    def GetFgColour(self) -> wx.Colour:
        """
        GetFgColour() -> wx.Colour
        """

    def SetBgColour(self, c: wx.Colour) -> None:
        """
        SetBgColour(c) -> None
        """

    def GetBgColour(self) -> wx.Colour:
        """
        GetBgColour() -> wx.Colour
        """

    def SetBgMode(self, m: int) -> None:
        """
        SetBgMode(m) -> None
        """

    def GetBgMode(self) -> int:
        """
        GetBgMode() -> int
        """
    @property
    def BgColour(self) -> wx.Colour: ...
    @BgColour.setter
    def BgColour(self, value: wx.Colour, /) -> None: ...
    @property
    def BgMode(self) -> int: ...
    @BgMode.setter
    def BgMode(self, value: int, /) -> None: ...
    @property
    def FgColour(self) -> wx.Colour: ...
    @FgColour.setter
    def FgColour(self, value: wx.Colour, /) -> None: ...
    @property
    def SelectionState(self) -> HtmlSelectionState: ...
    @SelectionState.setter
    def SelectionState(self, value: HtmlSelectionState, /) -> None: ...
# end of class HtmlRenderingState


class HtmlRenderingStyle:
    """
    wxHtmlSelection is data holder with information about text selection.
    """

    def GetSelectedTextColour(self, clr: wx.Colour) -> wx.Colour:
        """
        GetSelectedTextColour(clr) -> wx.Colour
        
        Returns the colour to use for the selected text.
        """

    def GetSelectedTextBgColour(self, clr: wx.Colour) -> wx.Colour:
        """
        GetSelectedTextBgColour(clr) -> wx.Colour
        
        Returns the colour to use for the selected text's background.
        """
# end of class HtmlRenderingStyle


class HtmlRenderingInfo:
    """
    HtmlRenderingInfo() -> None
    
    This class contains information given to cells when drawing them.
    """

    def __init__(self) -> None:
        """
        HtmlRenderingInfo() -> None
        
        This class contains information given to cells when drawing them.
        """

    def SetSelection(self, s: HtmlSelection) -> None:
        """
        SetSelection(s) -> None
        
        Accessors.
        """

    def GetSelection(self) -> HtmlSelection:
        """
        GetSelection() -> HtmlSelection
        """

    def SetStyle(self, style: HtmlRenderingStyle) -> None:
        """
        SetStyle(style) -> None
        """

    def GetStyle(self) -> HtmlRenderingStyle:
        """
        GetStyle() -> HtmlRenderingStyle
        """

    def GetState(self) -> HtmlRenderingState:
        """
        GetState() -> HtmlRenderingState
        """
    @property
    def Selection(self) -> HtmlSelection: ...
    @Selection.setter
    def Selection(self, value: HtmlSelection, /) -> None: ...
    @property
    def State(self) -> HtmlRenderingState: ...
    @property
    def Style(self) -> HtmlRenderingStyle: ...
    @Style.setter
    def Style(self, value: HtmlRenderingStyle, /) -> None: ...
# end of class HtmlRenderingInfo


class HtmlCell(wx.Object):
    """
    HtmlCell() -> None
    
    Internal data structure.
    """

    def __init__(self) -> None:
        """
        HtmlCell() -> None
        
        Internal data structure.
        """

    def AdjustPagebreak(self, pagebreak: int, pageHeight: int) -> Tuple[bool, int]:
        """
        AdjustPagebreak(pagebreak, pageHeight) -> Tuple[bool, int]
        
        This method is called when paginating HTML, e.g. when printing.
        """

    def Draw(self, dc: wx.DC, x: int, y: int, view_y1: int, view_y2: int, info: HtmlRenderingInfo) -> None:
        """
        Draw(dc, x, y, view_y1, view_y2, info) -> None
        
        Renders the cell.
        """

    def DrawInvisible(self, dc: wx.DC, x: int, y: int, info: HtmlRenderingInfo) -> None:
        """
        DrawInvisible(dc, x, y, info) -> None
        
        This method is called instead of Draw() when the cell is certainly out
        of the screen (and thus invisible).
        """

    def Find(self, condition: int, param: str) -> HtmlCell:
        """
        Find(condition, param) -> HtmlCell
        
        Returns pointer to itself if this cell matches condition (or if any of
        the cells following in the list matches), NULL otherwise.
        """

    def FindCellByPos(self, x: int, y: int, flags: int=HTML_FIND_EXACT) -> HtmlCell:
        """
        FindCellByPos(x, y, flags=HTML_FIND_EXACT) -> HtmlCell
        
        Find a cell inside this cell positioned at the given coordinates
        (relative to this's positions).
        """

    def GetDescent(self) -> int:
        """
        GetDescent() -> int
        
        Returns descent value of the cell (m_Descent member).
        """

    def GetFirstChild(self) -> HtmlCell:
        """
        GetFirstChild() -> HtmlCell
        
        Returns pointer to the first cell in the list.
        """

    def GetHeight(self) -> int:
        """
        GetHeight() -> int
        
        Returns height of the cell (m_Height member).
        """

    def GetId(self) -> str:
        """
        GetId() -> str
        
        Returns unique cell identifier if there is any, the empty string
        otherwise.
        """

    def GetLink(self, x: int=0, y: int=0) -> HtmlLinkInfo:
        """
        GetLink(x=0, y=0) -> HtmlLinkInfo
        
        Returns hypertext link if associated with this cell or NULL otherwise.
        """

    def GetMouseCursor(self, window: HtmlWindowInterface) -> wx.Cursor:
        """
        GetMouseCursor(window) -> wx.Cursor
        
        Returns cursor to show when mouse pointer is over the cell.
        """

    def GetMouseCursorAt(self, window: HtmlWindowInterface, rePos: wx.Point) -> wx.Cursor:
        """
        GetMouseCursorAt(window, rePos) -> wx.Cursor
        
        Returns cursor to show when mouse pointer is over the specified point.
        """

    def GetNext(self) -> HtmlCell:
        """
        GetNext() -> HtmlCell
        
        Returns pointer to the next cell in list (see htmlcell.h if you're
        interested in details).
        """

    def GetParent(self) -> HtmlContainerCell:
        """
        GetParent() -> HtmlContainerCell
        
        Returns pointer to parent container.
        """

    def GetPosX(self) -> int:
        """
        GetPosX() -> int
        
        Returns X position within parent (the value is relative to parent's
        upper left corner).
        """

    def GetPosY(self) -> int:
        """
        GetPosY() -> int
        
        Returns Y position within parent (the value is relative to parent's
        upper left corner).
        """

    def GetWidth(self) -> int:
        """
        GetWidth() -> int
        
        Returns width of the cell (m_Width member).
        """

    def Layout(self, w: int) -> None:
        """
        Layout(w) -> None
        
        Layouts the cell.
        """

    def ProcessMouseClick(self, window: HtmlWindowInterface, pos: wx.Point, event: wx.MouseEvent) -> bool:
        """
        ProcessMouseClick(window, pos, event) -> bool
        
        This function is simple event handler.
        """

    def SetId(self, id: str) -> None:
        """
        SetId(id) -> None
        
        Sets unique cell identifier.
        """

    def SetLink(self, link: HtmlLinkInfo) -> None:
        """
        SetLink(link) -> None
        
        Sets the hypertext link associated with this cell.
        """

    def SetNext(self, cell: HtmlCell) -> None:
        """
        SetNext(cell) -> None
        
        Sets the next cell in the list.
        """

    def SetParent(self, p: HtmlContainerCell) -> None:
        """
        SetParent(p) -> None
        
        Sets parent container of this cell.
        """

    def SetPos(self, x: int, y: int) -> None:
        """
        SetPos(x, y) -> None
        
        Sets the cell's position within parent container.
        """

    def ConvertToText(self, sel: HtmlSelection) -> str:
        """
        ConvertToText(sel) -> str
        
        Converts the cell into text representation.
        """

    def GetAbsPos(self, rootCell: Optional[HtmlCell]=None) -> wx.Point:
        """
        GetAbsPos(rootCell=None) -> wx.Point
        """

    def GetRootCell(self) -> HtmlCell:
        """
        GetRootCell() -> HtmlCell
        """
    @property
    def AbsPos(self) -> wx.Point: ...
    @property
    def Descent(self) -> int: ...
    @property
    def FirstChild(self) -> HtmlCell: ...
    @property
    def Height(self) -> int: ...
    @property
    def Id(self) -> str: ...
    @Id.setter
    def Id(self, value: str, /) -> None: ...
    @property
    def Link(self) -> HtmlLinkInfo: ...
    @Link.setter
    def Link(self, value: HtmlLinkInfo, /) -> None: ...
    @property
    def Next(self) -> HtmlCell: ...
    @Next.setter
    def Next(self, value: HtmlCell, /) -> None: ...
    @property
    def Parent(self) -> HtmlContainerCell: ...
    @Parent.setter
    def Parent(self, value: HtmlContainerCell, /) -> None: ...
    @property
    def PosX(self) -> int: ...
    @property
    def PosY(self) -> int: ...
    @property
    def RootCell(self) -> HtmlCell: ...
    @property
    def Width(self) -> int: ...
# end of class HtmlCell


class HtmlContainerCell(HtmlCell):
    """
    HtmlContainerCell(parent) -> None
    
    The wxHtmlContainerCell class is an implementation of a cell that may
    contain more cells in it.
    """

    def __init__(self, parent: HtmlContainerCell) -> None:
        """
        HtmlContainerCell(parent) -> None
        
        The wxHtmlContainerCell class is an implementation of a cell that may
        contain more cells in it.
        """

    def Detach(self, cell: HtmlCell) -> None:
        """
        Detach(cell) -> None
        
        Detach a child cell.
        """

    def GetAlignHor(self) -> int:
        """
        GetAlignHor() -> int
        
        Returns container's horizontal alignment.
        """

    def GetAlignVer(self) -> int:
        """
        GetAlignVer() -> int
        
        Returns container's vertical alignment.
        """

    def GetBackgroundColour(self) -> wx.Colour:
        """
        GetBackgroundColour() -> wx.Colour
        
        Returns the background colour of the container or wxNullColour if no
        background colour is set.
        """

    def GetIndent(self, ind: int) -> int:
        """
        GetIndent(ind) -> int
        
        Returns the indentation.
        """

    def GetIndentUnits(self, ind: int) -> int:
        """
        GetIndentUnits(ind) -> int
        
        Returns the units of indentation for ind where ind is one of the
        wxHTML_INDENT_* constants.
        """

    def InsertCell(self, cell: HtmlCell) -> None:
        """
        InsertCell(cell) -> None
        
        Inserts a new cell into the container.
        """

    def SetAlign(self, tag: HtmlTag) -> None:
        """
        SetAlign(tag) -> None
        
        Sets the container's alignment (both horizontal and vertical)
        according to the values stored in tag.
        """

    def SetAlignHor(self, al: int) -> None:
        """
        SetAlignHor(al) -> None
        
        Sets the container's horizontal alignment.
        """

    def SetAlignVer(self, al: int) -> None:
        """
        SetAlignVer(al) -> None
        
        Sets the container's vertical alignment.
        """

    def SetBackgroundColour(self, clr: wx.Colour) -> None:
        """
        SetBackgroundColour(clr) -> None
        
        Sets the background colour for this container.
        """

    def SetBorder(self, clr1: wx.Colour, clr2: wx.Colour, border: int=1) -> None:
        """
        SetBorder(clr1, clr2, border=1) -> None
        
        Sets the border (frame) colours.
        """

    def SetIndent(self, i: int, what: int, units: int=HTML_UNITS_PIXELS) -> None:
        """
        SetIndent(i, what, units=HTML_UNITS_PIXELS) -> None
        
        Sets the indentation (free space between borders of container and
        subcells).
        """

    def SetMinHeight(self, h: int, align: int=HTML_ALIGN_TOP) -> None:
        """
        SetMinHeight(h, align=HTML_ALIGN_TOP) -> None
        
        Sets minimal height of the container.
        """

    @overload
    def SetWidthFloat(self, tag: HtmlTag, pixel_scale: float=1.0) -> None:
        ...

    @overload
    def SetWidthFloat(self, w: int, units: int) -> None:
        """
        SetWidthFloat(w, units) -> None
        SetWidthFloat(tag, pixel_scale=1.0) -> None
        
        Sets floating width adjustment.
        """
    @property
    def AlignHor(self) -> int: ...
    @AlignHor.setter
    def AlignHor(self, value: int, /) -> None: ...
    @property
    def AlignVer(self) -> int: ...
    @AlignVer.setter
    def AlignVer(self, value: int, /) -> None: ...
    @property
    def BackgroundColour(self) -> wx.Colour: ...
    @BackgroundColour.setter
    def BackgroundColour(self, value: wx.Colour, /) -> None: ...
# end of class HtmlContainerCell


class HtmlLinkInfo(wx.Object):
    """
    HtmlLinkInfo() -> None
    HtmlLinkInfo(href, target='') -> None
    
    This class stores all necessary information about hypertext links (as
    represented by <A> tag in HTML documents).
    """

    @overload
    def __init__(self, href: str, target: str='') -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        HtmlLinkInfo() -> None
        HtmlLinkInfo(href, target='') -> None
        
        This class stores all necessary information about hypertext links (as
        represented by <A> tag in HTML documents).
        """

    def GetEvent(self) -> wx.MouseEvent:
        """
        GetEvent() -> wx.MouseEvent
        
        Return pointer to event that generated OnLinkClicked() event.
        """

    def GetHref(self) -> str:
        """
        GetHref() -> str
        
        Return HREF value of the <A> tag.
        """

    def GetHtmlCell(self) -> HtmlCell:
        """
        GetHtmlCell() -> HtmlCell
        
        Return pointer to the cell that was clicked.
        """

    def GetTarget(self) -> str:
        """
        GetTarget() -> str
        
        Return TARGET value of the <A> tag (this value is used to specify in
        which frame should be the page pointed by GetHref() Href opened).
        """
    @property
    def Event(self) -> wx.MouseEvent: ...
    @property
    def Href(self) -> str: ...
    @property
    def HtmlCell(self) -> HtmlCell: ...
    @property
    def Target(self) -> str: ...
# end of class HtmlLinkInfo


class HtmlColourCell(HtmlCell):
    """
    HtmlColourCell(clr, flags=HTML_CLR_FOREGROUND) -> None
    
    This cell changes the colour of either the background or the
    foreground.
    """

    def __init__(self, clr: wx.Colour, flags: int=HTML_CLR_FOREGROUND) -> None:
        """
        HtmlColourCell(clr, flags=HTML_CLR_FOREGROUND) -> None
        
        This cell changes the colour of either the background or the
        foreground.
        """
# end of class HtmlColourCell


class HtmlWidgetCell(HtmlCell):
    """
    HtmlWidgetCell(wnd, w=0) -> None
    
    wxHtmlWidgetCell is a class that provides a connection between HTML
    cells and widgets (an object derived from wxWindow).
    """

    def __init__(self, wnd: wx.Window, w: int=0) -> None:
        """
        HtmlWidgetCell(wnd, w=0) -> None
        
        wxHtmlWidgetCell is a class that provides a connection between HTML
        cells and widgets (an object derived from wxWindow).
        """
# end of class HtmlWidgetCell


class HtmlWordCell(HtmlCell):
    """
    HtmlWordCell(word, dc) -> None
    
    This html cell represents a single word or text fragment in the
    document stream.
    """

    def __init__(self, word: str, dc: wx.DC) -> None:
        """
        HtmlWordCell(word, dc) -> None
        
        This html cell represents a single word or text fragment in the
        document stream.
        """
# end of class HtmlWordCell


class HtmlWordWithTabsCell(HtmlWordCell):
    """
    HtmlWordWithTabsCell(word, wordOrig, linepos, dc) -> None
    
    wxHtmlWordCell is a specialization for storing text fragments with
    embedded tab characters.
    """

    def __init__(self, word: str, wordOrig: str, linepos: int, dc: wx.DC) -> None:
        """
        HtmlWordWithTabsCell(word, wordOrig, linepos, dc) -> None
        
        wxHtmlWordCell is a specialization for storing text fragments with
        embedded tab characters.
        """
# end of class HtmlWordWithTabsCell


class HtmlFontCell(HtmlCell):
    """
    HtmlFontCell(font) -> None
    
    This cell represents a font change in the document stream.
    """

    def __init__(self, font: wx.Font) -> None:
        """
        HtmlFontCell(font) -> None
        
        This cell represents a font change in the document stream.
        """
# end of class HtmlFontCell

#-- end-htmlcell --#
#-- begin-htmlfilt --#

class HtmlFilter(wx.Object):
    """
    HtmlFilter() -> None
    
    This class is the parent class of input filters for wxHtmlWindow.
    """

    def __init__(self) -> None:
        """
        HtmlFilter() -> None
        
        This class is the parent class of input filters for wxHtmlWindow.
        """

    def CanRead(self, file: wx.FSFile) -> bool:
        """
        CanRead(file) -> bool
        
        Returns true if this filter is capable of reading file file.
        """

    def ReadFile(self, file: wx.FSFile) -> str:
        """
        ReadFile(file) -> str
        
        Reads the file and returns string with HTML document.
        """
# end of class HtmlFilter

#-- end-htmlfilt --#
#-- begin-htmltag --#

class HtmlTag:
    """
    This class represents a single HTML tag.
    """

    def GetAllParams(self) -> str:
        """
        GetAllParams() -> str
        
        Returns a string containing all parameters.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns tag's name.
        """

    def GetParam(self, par: str, with_quotes: bool=False) -> str:
        """
        GetParam(par, with_quotes=False) -> str
        
        Returns the value of the parameter.
        """

    def GetParamAsColour(self, par: str) -> Tuple[bool, wx.Colour]:
        """
        GetParamAsColour(par) -> Tuple[bool, wx.Colour]
        
        Interprets tag parameter par as colour specification and saves its
        value into wxColour variable pointed by clr.
        """

    def GetParamAsInt(self, par: str) -> Tuple[bool, int]:
        """
        GetParamAsInt(par) -> Tuple[bool, int]
        
        Interprets tag parameter par as an integer and saves its value into
        int variable pointed by value.
        """

    def GetParamAsString(self, par: str, value: str) -> bool:
        """
        GetParamAsString(par, value) -> bool
        
        Get the value of the parameter.
        """

    def HasEnding(self) -> bool:
        """
        HasEnding() -> bool
        
        Returns true if this tag is paired with ending tag, false otherwise.
        """

    def HasParam(self, par: str) -> bool:
        """
        HasParam(par) -> bool
        
        Returns true if the tag has a parameter of the given name.
        """

    @staticmethod
    def ParseAsColour(str: str) -> Tuple[bool, wx.Colour]:
        """
        ParseAsColour(str) -> Tuple[bool, wx.Colour]
        
        Parses the given string as an HTML colour.
        """
    @property
    def AllParams(self) -> str: ...
    @property
    def Name(self) -> str: ...
# end of class HtmlTag

#-- end-htmltag --#
#-- begin-htmlpars --#

class _HtmlURLType(IntEnum):
    HTML_URL_PAGE = auto()
    HTML_URL_IMAGE = auto()
    HTML_URL_OTHER = auto()
HtmlURLType: TypeAlias = Union[_HtmlURLType, int]
HTML_URL_PAGE = _HtmlURLType.HTML_URL_PAGE
HTML_URL_IMAGE = _HtmlURLType.HTML_URL_IMAGE
HTML_URL_OTHER = _HtmlURLType.HTML_URL_OTHER

class HtmlTagHandler(wx.Object):
    """
    HtmlTagHandler() -> None
    """

    def __init__(self) -> None:
        """
        HtmlTagHandler() -> None
        """

    def GetSupportedTags(self) -> str:
        """
        GetSupportedTags() -> str
        
        Returns list of supported tags.
        """

    def HandleTag(self, tag: HtmlTag) -> bool:
        """
        HandleTag(tag) -> bool
        
        This is the core method of each handler.
        """

    def SetParser(self, parser: HtmlParser) -> None:
        """
        SetParser(parser) -> None
        
        Assigns parser to this handler.
        """

    def GetParser(self) -> HtmlParser:
        """
        GetParser() -> HtmlParser
        
        Returns the parser associated with this tag handler.
        """
    @property
    def Parser(self) -> HtmlParser: ...
    @Parser.setter
    def Parser(self, value: HtmlParser, /) -> None: ...
    @property
    def SupportedTags(self) -> str: ...

    def ParseInner(self, tag: HtmlTag) -> None:
        """
        ParseInner(tag) -> None
        
        This method calls parser's wxHtmlParser::DoParsing method for the
        string between this tag and the paired ending tag:
        """

    def ParseInnerSource(self, source: str) -> None:
        """
        ParseInnerSource(source) -> None
        
        Parses given source as if it was tag's inner code (see
        wxHtmlParser::GetInnerSource).
        """
# end of class HtmlTagHandler


class HtmlParser:
    """
    HtmlParser() -> None
    
    Classes derived from this handle the generic parsing of HTML
    documents: it scans the document and divide it into blocks of tags
    (where one block consists of beginning and ending tag and of text
    between these two tags).
    """

    def __init__(self) -> None:
        """
        HtmlParser() -> None
        
        Classes derived from this handle the generic parsing of HTML
        documents: it scans the document and divide it into blocks of tags
        (where one block consists of beginning and ending tag and of text
        between these two tags).
        """

    def AddTagHandler(self, handler: HtmlTagHandler) -> None:
        """
        AddTagHandler(handler) -> None
        
        Adds handler to the internal list ( hash table) of handlers.
        """

    def DoParsing(self) -> None:
        """
        DoParsing() -> None
        
        Parses the whole m_Source.
        """

    def DoneParser(self) -> None:
        """
        DoneParser() -> None
        
        This must be called after DoParsing().
        """

    def GetFS(self) -> wx.FileSystem:
        """
        GetFS() -> wx.FileSystem
        
        Returns pointer to the file system.
        """

    def GetProduct(self) -> wx.Object:
        """
        GetProduct() -> wx.Object
        
        Returns product of parsing.
        """

    def GetSource(self) -> str:
        """
        GetSource() -> str
        
        Returns pointer to the source being parsed.
        """

    def InitParser(self, source: str) -> None:
        """
        InitParser(source) -> None
        
        Setups the parser for parsing the source string.
        """

    def OpenURL(self, type: HtmlURLType, url: str) -> wx.FSFile:
        """
        OpenURL(type, url) -> wx.FSFile
        
        Opens given URL and returns wxFSFile object that can be used to read
        data from it.
        """

    def Parse(self, source: str) -> wx.Object:
        """
        Parse(source) -> wx.Object
        
        Proceeds parsing of the document.
        """

    def PopTagHandler(self) -> None:
        """
        PopTagHandler() -> None
        
        Restores parser's state before last call to PushTagHandler().
        """

    def PushTagHandler(self, handler: HtmlTagHandler, tags: str) -> None:
        """
        PushTagHandler(handler, tags) -> None
        
        Forces the handler to handle additional tags (not returned by
        wxHtmlTagHandler::GetSupportedTags).
        """

    def SetFS(self, fs: wx.FileSystem) -> None:
        """
        SetFS(fs) -> None
        
        Sets the virtual file system that will be used to request additional
        files.
        """

    def StopParsing(self) -> None:
        """
        StopParsing() -> None
        
        Call this function to interrupt parsing from a tag handler.
        """
    @property
    def FS(self) -> wx.FileSystem: ...
    @FS.setter
    def FS(self, value: wx.FileSystem, /) -> None: ...
    @property
    def Product(self) -> wx.Object: ...
    @property
    def Source(self) -> str: ...

    def AddTag(self, tag: HtmlTag) -> None:
        """
        AddTag(tag) -> None
        
        This may (and may not) be overwritten in derived class.
        """
# end of class HtmlParser

#-- end-htmlpars --#
#-- begin-htmlwin --#
HW_SCROLLBAR_NEVER: int
HW_SCROLLBAR_AUTO: int
HW_NO_SELECTION: int
HW_DEFAULT_STYLE: int

class _HtmlOpeningStatus(IntEnum):
    HTML_OPEN = auto()
    HTML_BLOCK = auto()
    HTML_REDIRECT = auto()
HtmlOpeningStatus: TypeAlias = Union[_HtmlOpeningStatus, int]
HTML_OPEN = _HtmlOpeningStatus.HTML_OPEN
HTML_BLOCK = _HtmlOpeningStatus.HTML_BLOCK
HTML_REDIRECT = _HtmlOpeningStatus.HTML_REDIRECT
wxEVT_HTML_CELL_CLICKED: int
wxEVT_HTML_CELL_HOVER: int
wxEVT_HTML_LINK_CLICKED: int

class HtmlWindowInterface:
    """
    HtmlWindowInterface() -> None
    
    Abstract interface to a HTML rendering window (such as wxHtmlWindow or
    wxHtmlListBox) that is passed to wxHtmlWinParser.
    """

    class _HTMLCursor(IntEnum):
        HTMLCursor_Default = auto()
        HTMLCursor_Link = auto()
        HTMLCursor_Text = auto()
    HTMLCursor: TypeAlias = Union[_HTMLCursor, int]
    HTMLCursor_Default = _HTMLCursor.HTMLCursor_Default
    HTMLCursor_Link = _HTMLCursor.HTMLCursor_Link
    HTMLCursor_Text = _HTMLCursor.HTMLCursor_Text

    def __init__(self) -> None:
        """
        HtmlWindowInterface() -> None
        
        Abstract interface to a HTML rendering window (such as wxHtmlWindow or
        wxHtmlListBox) that is passed to wxHtmlWinParser.
        """

    def SetHTMLWindowTitle(self, title: str) -> None:
        """
        SetHTMLWindowTitle(title) -> None
        
        Called by the parser to set window's title to given text.
        """

    def OnHTMLLinkClicked(self, link: HtmlLinkInfo) -> None:
        """
        OnHTMLLinkClicked(link) -> None
        
        Called when a link is clicked.
        """

    def OnHTMLOpeningURL(self, type: HtmlURLType, url: str) -> Tuple[HtmlOpeningStatus, str]:
        """
        OnHTMLOpeningURL(type, url) -> Tuple[HtmlOpeningStatus, str]
        
        Called when the parser needs to open another URL (e.g.
        """

    def HTMLCoordsToWindow(self, cell: HtmlCell, pos: wx.Point) -> wx.Point:
        """
        HTMLCoordsToWindow(cell, pos) -> wx.Point
        
        Converts coordinates pos relative to given cell to physical
        coordinates in the window.
        """

    def GetHTMLWindow(self) -> wx.Window:
        """
        GetHTMLWindow() -> wx.Window
        
        Returns the window used for rendering (may be NULL).
        """

    def GetHTMLBackgroundColour(self) -> wx.Colour:
        """
        GetHTMLBackgroundColour() -> wx.Colour
        
        Returns background colour to use by default.
        """

    def SetHTMLBackgroundColour(self, clr: wx.Colour) -> None:
        """
        SetHTMLBackgroundColour(clr) -> None
        
        Sets window's background to colour clr.
        """

    def SetHTMLBackgroundImage(self, bmpBg: wx.BitmapBundle) -> None:
        """
        SetHTMLBackgroundImage(bmpBg) -> None
        
        Sets window's background to given bitmap.
        """

    def SetHTMLStatusText(self, text: str) -> None:
        """
        SetHTMLStatusText(text) -> None
        
        Sets status bar text.
        """

    def GetHTMLCursor(self, type: HtmlWindowInterface.HTMLCursor) -> wx.Cursor:
        """
        GetHTMLCursor(type) -> wx.Cursor
        
        Returns mouse cursor of given type.
        """
    @property
    def HTMLBackgroundColour(self) -> wx.Colour: ...
    @HTMLBackgroundColour.setter
    def HTMLBackgroundColour(self, value: wx.Colour, /) -> None: ...
    @property
    def HTMLWindow(self) -> wx.Window: ...
# end of class HtmlWindowInterface


class HtmlWindow(wx.ScrolledWindow, HtmlWindowInterface):
    """
    HtmlWindow() -> None
    HtmlWindow(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=HW_DEFAULT_STYLE, name="htmlWindow") -> None
    
    wxHtmlWindow is probably the only class you will directly use unless
    you want to do something special (like adding new tag handlers or MIME
    filters).
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=HW_DEFAULT_STYLE, name: str="htmlWindow") -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        HtmlWindow() -> None
        HtmlWindow(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=HW_DEFAULT_STYLE, name="htmlWindow") -> None
        
        wxHtmlWindow is probably the only class you will directly use unless
        you want to do something special (like adding new tag handlers or MIME
        filters).
        """

    def AppendToPage(self, source: str) -> bool:
        """
        AppendToPage(source) -> bool
        
        Appends HTML fragment to currently displayed text and refreshes the
        window.
        """

    def GetInternalRepresentation(self) -> HtmlContainerCell:
        """
        GetInternalRepresentation() -> HtmlContainerCell
        
        Returns pointer to the top-level container.
        """

    def GetOpenedAnchor(self) -> str:
        """
        GetOpenedAnchor() -> str
        
        Returns anchor within currently opened page (see
        wxHtmlWindow::GetOpenedPage).
        """

    def GetOpenedPage(self) -> str:
        """
        GetOpenedPage() -> str
        
        Returns full location of the opened page.
        """

    def GetOpenedPageTitle(self) -> str:
        """
        GetOpenedPageTitle() -> str
        
        Returns title of the opened page or wxEmptyString if the current page
        does not contain <TITLE> tag.
        """

    def GetParser(self) -> HtmlWinParser:
        """
        GetParser() -> HtmlWinParser
        
        Returns a pointer to the current parser.
        """

    def GetRelatedFrame(self) -> wx.Frame:
        """
        GetRelatedFrame() -> wx.Frame
        
        Returns the related frame.
        """

    def HistoryBack(self) -> bool:
        """
        HistoryBack() -> bool
        
        Moves back to the previous page.
        """

    def HistoryCanBack(self) -> bool:
        """
        HistoryCanBack() -> bool
        
        Returns true if it is possible to go back in the history i.e.
        """

    def HistoryCanForward(self) -> bool:
        """
        HistoryCanForward() -> bool
        
        Returns true if it is possible to go forward in the history i.e.
        """

    def HistoryClear(self) -> None:
        """
        HistoryClear() -> None
        
        Clears history.
        """

    def HistoryForward(self) -> bool:
        """
        HistoryForward() -> bool
        
        Moves to next page in history.
        """

    def LoadFile(self, filename: str) -> bool:
        """
        LoadFile(filename) -> bool
        
        Loads an HTML page from a file and displays it.
        """

    def LoadPage(self, location: str) -> bool:
        """
        LoadPage(location) -> bool
        
        Unlike SetPage() this function first loads the HTML page from location
        and then displays it.
        """

    def OnLinkClicked(self, link: HtmlLinkInfo) -> None:
        """
        OnLinkClicked(link) -> None
        
        Called when user clicks on hypertext link.
        """

    def OnOpeningURL(self, type: HtmlURLType, url: str) -> Tuple[HtmlOpeningStatus, str]:
        """
        OnOpeningURL(type, url) -> Tuple[HtmlOpeningStatus, str]
        
        Called when an URL is being opened (either when the user clicks on a
        link or an image is loaded).
        """

    def OnSetTitle(self, title: str) -> None:
        """
        OnSetTitle(title) -> None
        
        Called on parsing <TITLE> tag.
        """

    def ReadCustomization(self, cfg: wx.ConfigBase, path: str='') -> None:
        """
        ReadCustomization(cfg, path='') -> None
        
        This reads custom settings from wxConfig.
        """

    def SelectAll(self) -> None:
        """
        SelectAll() -> None
        
        Selects all text in the window.
        """

    def SelectLine(self, pos: wx.Point) -> None:
        """
        SelectLine(pos) -> None
        
        Selects the line of text that pos points at.
        """

    def SelectWord(self, pos: wx.Point) -> None:
        """
        SelectWord(pos) -> None
        
        Selects the word at position pos.
        """

    def SelectionToText(self) -> str:
        """
        SelectionToText() -> str
        
        Returns the current selection as plain text.
        """

    def SetBorders(self, b: int) -> None:
        """
        SetBorders(b) -> None
        
        This function sets the space between border of window and HTML
        contents.
        """

    def SetFonts(self, normal_face: str, fixed_face: str, sizes: List[int]) -> None:
        """
        SetFonts(normal_face, fixed_face, sizes) -> None
        
        This function sets font sizes and faces.
        """

    def SetStandardFonts(self, size: int=-1, normal_face: str='', fixed_face: str='') -> None:
        """
        SetStandardFonts(size=-1, normal_face='', fixed_face='') -> None
        
        Sets default font sizes and/or default font size.
        """

    def SetPage(self, source: str) -> bool:
        """
        SetPage(source) -> bool
        
        Sets the source of a page and displays it, for example:
        """

    def SetRelatedFrame(self, frame: wx.Frame, format: str) -> None:
        """
        SetRelatedFrame(frame, format) -> None
        
        Sets the frame in which page title will be displayed.
        """

    @overload
    def SetRelatedStatusBar(self, statusbar: wx.StatusBar, index: int=0) -> None:
        ...

    @overload
    def SetRelatedStatusBar(self, index: int) -> None:
        """
        SetRelatedStatusBar(index) -> None
        SetRelatedStatusBar(statusbar, index=0) -> None
        
        After calling SetRelatedFrame(), this sets statusbar slot where
        messages will be displayed.
        """

    def ToText(self) -> str:
        """
        ToText() -> str
        
        Returns content of currently displayed page as plain text.
        """

    def WriteCustomization(self, cfg: wx.ConfigBase, path: str='') -> None:
        """
        WriteCustomization(cfg, path='') -> None
        
        Saves custom settings into wxConfig.
        """

    @staticmethod
    def AddFilter(filter: HtmlFilter) -> None:
        """
        AddFilter(filter) -> None
        
        Adds an input filter to the static list of available filters.
        """

    @staticmethod
    def GetDefaultHTMLCursor(type: HTMLCursor) -> wx.Cursor:
        """
        GetDefaultHTMLCursor(type) -> wx.Cursor
        
        Retrieves the default cursor for a given HTMLCursor type.
        """

    @staticmethod
    def SetDefaultHTMLCursor(type: HTMLCursor, cursor: wx.Cursor) -> None:
        """
        SetDefaultHTMLCursor(type, cursor) -> None
        
        Sets the default cursor for a given HTMLCursor type.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def InternalRepresentation(self) -> HtmlContainerCell: ...
    @property
    def OpenedAnchor(self) -> str: ...
    @property
    def OpenedPage(self) -> str: ...
    @property
    def OpenedPageTitle(self) -> str: ...
    @property
    def Parser(self) -> HtmlWinParser: ...
    @property
    def RelatedFrame(self) -> wx.Frame: ...
    @RelatedFrame.setter
    def RelatedFrame(self, value: wx.Frame, /) -> None: ...

    def OnCellClicked(self, cell: HtmlCell, x: int, y: int, event: wx.MouseEvent) -> bool:
        """
        OnCellClicked(cell, x, y, event) -> bool
        
        This method is called when a mouse button is clicked inside
        wxHtmlWindow.
        """

    def OnCellMouseHover(self, cell: HtmlCell, x: int, y: int) -> None:
        """
        OnCellMouseHover(cell, x, y) -> None
        
        This method is called when a mouse moves over an HTML cell.
        """

    def ScrollToAnchor(self, anchor: str) -> bool:
        """
        ScrollToAnchor(anchor) -> bool
        """
# end of class HtmlWindow


class HtmlLinkEvent(wx.CommandEvent):
    """
    HtmlLinkEvent(id, linkinfo) -> None
    
    This event class is used for the events generated by wxHtmlWindow.
    """

    def __init__(self, id: int, linkinfo: HtmlLinkInfo) -> None:
        """
        HtmlLinkEvent(id, linkinfo) -> None
        
        This event class is used for the events generated by wxHtmlWindow.
        """

    def GetLinkInfo(self) -> HtmlLinkInfo:
        """
        GetLinkInfo() -> HtmlLinkInfo
        
        Returns the wxHtmlLinkInfo which contains info about the cell clicked
        and the hyperlink it contains.
        """
    @property
    def LinkInfo(self) -> HtmlLinkInfo: ...
# end of class HtmlLinkEvent


class HtmlCellEvent(wx.CommandEvent):
    """
    HtmlCellEvent(commandType, id, cell, point, ev) -> None
    
    This event class is used for the events generated by wxHtmlWindow.
    """

    def __init__(self, commandType: EventType, id: int, cell: HtmlCell, point: wx.Point, ev: wx.MouseEvent) -> None:
        """
        HtmlCellEvent(commandType, id, cell, point, ev) -> None
        
        This event class is used for the events generated by wxHtmlWindow.
        """

    def GetCell(self) -> HtmlCell:
        """
        GetCell() -> HtmlCell
        
        Returns the wxHtmlCellEvent associated with the event.
        """

    def GetLinkClicked(self) -> bool:
        """
        GetLinkClicked() -> bool
        
        Returns true if SetLinkClicked(true) has previously been called; false
        otherwise.
        """

    def GetPoint(self) -> wx.Point:
        """
        GetPoint() -> wx.Point
        
        Returns the wxPoint associated with the event.
        """

    def GetMouseEvent(self) -> wx.MouseEvent:
        """
        GetMouseEvent() -> wx.MouseEvent
        
        Returns the wxMouseEvent associated with the event.
        """

    def SetLinkClicked(self, linkclicked: bool) -> None:
        """
        SetLinkClicked(linkclicked) -> None
        
        Call this function with linkclicked set to true if the cell which has
        been clicked contained a link or false otherwise (which is the
        default).
        """
    @property
    def Cell(self) -> HtmlCell: ...
    @property
    def LinkClicked(self) -> bool: ...
    @LinkClicked.setter
    def LinkClicked(self, value: bool, /) -> None: ...
    @property
    def MouseEvent(self) -> wx.MouseEvent: ...
    @property
    def Point(self) -> wx.Point: ...
# end of class HtmlCellEvent


EVT_HTML_CELL_CLICKED = wx.PyEventBinder( wxEVT_HTML_CELL_CLICKED, 1 )
EVT_HTML_CELL_HOVER   = wx.PyEventBinder( wxEVT_HTML_CELL_HOVER, 1 )
EVT_HTML_LINK_CLICKED = wx.PyEventBinder( wxEVT_HTML_LINK_CLICKED, 1 )

# deprecated wxEVT aliases
wxEVT_COMMAND_HTML_CELL_CLICKED  = wxEVT_HTML_CELL_CLICKED
wxEVT_COMMAND_HTML_CELL_HOVER    = wxEVT_HTML_CELL_HOVER
wxEVT_COMMAND_HTML_LINK_CLICKED  = wxEVT_HTML_LINK_CLICKED
#-- end-htmlwin --#
#-- begin-htmlprint --#

class _enum_28(IntEnum):
    PAGE_ODD = auto()
    PAGE_EVEN = auto()
    PAGE_ALL = auto()
PAGE_ODD = _enum_28.PAGE_ODD
PAGE_EVEN = _enum_28.PAGE_EVEN
PAGE_ALL = _enum_28.PAGE_ALL

class HtmlDCRenderer(wx.Object):
    """
    HtmlDCRenderer() -> None
    
    This class can render HTML document into a specified area of a DC.
    """

    def __init__(self) -> None:
        """
        HtmlDCRenderer() -> None
        
        This class can render HTML document into a specified area of a DC.
        """

    def GetTotalWidth(self) -> int:
        """
        GetTotalWidth() -> int
        
        Returns the width of the HTML text in pixels.
        """

    def GetTotalHeight(self) -> int:
        """
        GetTotalHeight() -> int
        
        Returns the height of the HTML text in pixels.
        """

    def FindNextPageBreak(self, pos: int) -> int:
        """
        FindNextPageBreak(pos) -> int
        
        Finds the next page break after the specified (vertical) position.
        """

    def Render(self, x: int, y: int, from_: int=0, to_: int=INT_MAX) -> None:
        """
        Render(x, y, from_=0, to_=INT_MAX) -> None
        
        Renders HTML text to the DC.
        """

    def SetDC(self, dc: wx.DC, pixel_scale: float=1.0) -> None:
        """
        SetDC(dc, pixel_scale=1.0) -> None
        
        Assign DC instance to the renderer.
        """

    def SetFonts(self, normal_face: str, fixed_face: str, sizes: List[int]) -> None:
        """
        SetFonts(normal_face, fixed_face, sizes) -> None
        
        This function sets font sizes and faces.
        """

    def SetStandardFonts(self, size: int=-1, normal_face: str='', fixed_face: str='') -> None:
        """
        SetStandardFonts(size=-1, normal_face='', fixed_face='') -> None
        
        Sets font sizes to be relative to the given size or the system default
        size; use either specified or default font.
        """

    def SetHtmlText(self, html: str, basepath: str='', isdir: bool=True) -> None:
        """
        SetHtmlText(html, basepath='', isdir=True) -> None
        
        Assign text to the renderer.
        """

    def SetHtmlCell(self, cell: HtmlContainerCell) -> None:
        """
        SetHtmlCell(cell) -> None
        
        Associate the given HTML contents to the renderer.
        """

    def SetSize(self, width: int, height: int) -> None:
        """
        SetSize(width, height) -> None
        
        Set size of output rectangle, in pixels.
        """
    @property
    def TotalHeight(self) -> int: ...
    @property
    def TotalWidth(self) -> int: ...
# end of class HtmlDCRenderer


class HtmlEasyPrinting(wx.Object):
    """
    HtmlEasyPrinting(name="Printing", parentWindow=None) -> None
    
    This class provides very simple interface to printing architecture.
    """

    class _PromptMode(IntEnum):
        Prompt_Never = auto()
        Prompt_Once = auto()
        Prompt_Always = auto()
    PromptMode: TypeAlias = Union[_PromptMode, int]
    Prompt_Never = _PromptMode.Prompt_Never
    Prompt_Once = _PromptMode.Prompt_Once
    Prompt_Always = _PromptMode.Prompt_Always

    def __init__(self, name: str="Printing", parentWindow: Optional[wx.Window]=None) -> None:
        """
        HtmlEasyPrinting(name="Printing", parentWindow=None) -> None
        
        This class provides very simple interface to printing architecture.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns the current name being used for preview frames and setup
        dialogs.
        """

    def GetPageSetupData(self) -> wx.PageSetupDialogData:
        """
        GetPageSetupData() -> wx.PageSetupDialogData
        
        Returns a pointer to wxPageSetupDialogData instance used by this
        class.
        """

    def GetParentWindow(self) -> wx.Window:
        """
        GetParentWindow() -> wx.Window
        
        Gets the parent window for dialogs.
        """

    def GetPrintData(self) -> wx.PrintData:
        """
        GetPrintData() -> wx.PrintData
        
        Returns pointer to wxPrintData instance used by this class.
        """

    def PageSetup(self) -> None:
        """
        PageSetup() -> None
        
        Display page setup dialog and allows the user to modify settings.
        """

    def PreviewFile(self, htmlfile: str) -> bool:
        """
        PreviewFile(htmlfile) -> bool
        
        Preview HTML file.
        """

    def PreviewText(self, htmltext: str, basepath: str='') -> bool:
        """
        PreviewText(htmltext, basepath='') -> bool
        
        Preview HTML text (not file!).
        """

    def PrintFile(self, htmlfile: str) -> bool:
        """
        PrintFile(htmlfile) -> bool
        
        Print HTML file.
        """

    def PrintText(self, htmltext: str, basepath: str='') -> bool:
        """
        PrintText(htmltext, basepath='') -> bool
        
        Print HTML text (not file!).
        """

    def SetFonts(self, normal_face: str, fixed_face: str, sizes: List[int]) -> None:
        """
        SetFonts(normal_face, fixed_face, sizes) -> None
        
        Sets fonts.
        """

    def SetName(self, name: str) -> None:
        """
        SetName(name) -> None
        
        Sets the name used for preview frames and setup dialogs.
        """

    def SetStandardFonts(self, size: int=-1, normal_face: str='', fixed_face: str='') -> None:
        """
        SetStandardFonts(size=-1, normal_face='', fixed_face='') -> None
        
        Sets default font sizes and/or default font size.
        """

    def SetFooter(self, footer: str, pg: int=PAGE_ALL) -> None:
        """
        SetFooter(footer, pg=PAGE_ALL) -> None
        
        Set page footer.
        """

    def SetHeader(self, header: str, pg: int=PAGE_ALL) -> None:
        """
        SetHeader(header, pg=PAGE_ALL) -> None
        
        Set page header.
        """

    def SetParentWindow(self, window: wx.Window) -> None:
        """
        SetParentWindow(window) -> None
        
        Sets the parent window for dialogs.
        """

    def SetPromptMode(self, promptMode: PromptMode) -> None:
        """
        SetPromptMode(promptMode) -> None
        
        Enable or disable showing the dialog before printing.
        """
    @property
    def Name(self) -> str: ...
    @Name.setter
    def Name(self, value: str, /) -> None: ...
    @property
    def PageSetupData(self) -> wx.PageSetupDialogData: ...
    @property
    def ParentWindow(self) -> wx.Window: ...
    @ParentWindow.setter
    def ParentWindow(self, value: wx.Window, /) -> None: ...
    @property
    def PrintData(self) -> wx.PrintData: ...
# end of class HtmlEasyPrinting


class HtmlPrintout(wx.Printout):
    """
    HtmlPrintout(title="Printout") -> None
    
    This class serves as printout class for HTML documents.
    """

    def __init__(self, title: str="Printout") -> None:
        """
        HtmlPrintout(title="Printout") -> None
        
        This class serves as printout class for HTML documents.
        """

    def SetFonts(self, normal_face: str, fixed_face: str, sizes: List[int]) -> None:
        """
        SetFonts(normal_face, fixed_face, sizes) -> None
        
        This function sets font sizes and faces.
        """

    def SetFooter(self, footer: str, pg: int=PAGE_ALL) -> None:
        """
        SetFooter(footer, pg=PAGE_ALL) -> None
        
        Set page footer.
        """

    def SetHeader(self, header: str, pg: int=PAGE_ALL) -> None:
        """
        SetHeader(header, pg=PAGE_ALL) -> None
        
        Set page header.
        """

    def SetHtmlFile(self, htmlfile: str) -> None:
        """
        SetHtmlFile(htmlfile) -> None
        
        Prepare the class for printing this HTML file.
        """

    def SetHtmlText(self, html: str, basepath: str='', isdir: bool=True) -> None:
        """
        SetHtmlText(html, basepath='', isdir=True) -> None
        
        Prepare the class for printing this HTML text.
        """

    @overload
    def SetMargins(self, pageSetupData: wx.PageSetupDialogData) -> None:
        ...

    @overload
    def SetMargins(self, top: float=25.2, bottom: float=25.2, left: float=25.2, right: float=25.2, spaces: float=5) -> None:
        """
        SetMargins(top=25.2, bottom=25.2, left=25.2, right=25.2, spaces=5) -> None
        SetMargins(pageSetupData) -> None
        
        Sets margins in millimeters.
        """

    @staticmethod
    def AddFilter(filter: HtmlFilter) -> None:
        """
        AddFilter(filter) -> None
        
        Adds a filter to the static list of filters for wxHtmlPrintout.
        """
# end of class HtmlPrintout

#-- end-htmlprint --#
#-- begin-htmlwinpars --#

class HtmlWinTagHandler(HtmlTagHandler):
    """
    HtmlWinTagHandler() -> None
    
    This is basically wxHtmlTagHandler except that it is extended with
    protected member m_WParser pointing to the wxHtmlWinParser object
    (value of this member is identical to wxHtmlParser's m_Parser).
    """

    def __init__(self) -> None:
        """
        HtmlWinTagHandler() -> None
        
        This is basically wxHtmlTagHandler except that it is extended with
        protected member m_WParser pointing to the wxHtmlWinParser object
        (value of this member is identical to wxHtmlParser's m_Parser).
        """

    def SetParser(self, parser: HtmlParser) -> None:
        """
        SetParser(parser) -> None
        
        Assigns parser to this handler.
        """

    def GetParser(self) -> HtmlWinParser:
        """
        GetParser() -> HtmlWinParser
        """
    @property
    def Parser(self) -> HtmlParser: ...
    @Parser.setter
    def Parser(self, value: HtmlParser, /) -> None: ...
# end of class HtmlWinTagHandler


class HtmlWinParser(HtmlParser):
    """
    HtmlWinParser(wndIface=None) -> None
    
    This class is derived from wxHtmlParser and its main goal is to parse
    HTML input so that it can be displayed in wxHtmlWindow.
    """

    def __init__(self, wndIface: Optional[HtmlWindowInterface]=None) -> None:
        """
        HtmlWinParser(wndIface=None) -> None
        
        This class is derived from wxHtmlParser and its main goal is to parse
        HTML input so that it can be displayed in wxHtmlWindow.
        """

    def CloseContainer(self) -> HtmlContainerCell:
        """
        CloseContainer() -> HtmlContainerCell
        
        Closes the container, sets actual container to the parent one and
        returns pointer to it (see Cells and Containers).
        """

    def CreateCurrentFont(self) -> wx.Font:
        """
        CreateCurrentFont() -> wx.Font
        
        Creates font based on current setting (see SetFontSize(),
        SetFontBold(), SetFontItalic(), SetFontFixed(),
        wxHtmlWinParser::SetFontUnderlined) and returns pointer to it.
        """

    def GetActualColor(self) -> wx.Colour:
        """
        GetActualColor() -> wx.Colour
        
        Returns actual text colour.
        """

    def GetAlign(self) -> int:
        """
        GetAlign() -> int
        
        Returns default horizontal alignment.
        """

    def GetCharHeight(self) -> int:
        """
        GetCharHeight() -> int
        
        Returns (average) char height in standard font.
        """

    def GetCharWidth(self) -> int:
        """
        GetCharWidth() -> int
        
        Returns average char width in standard font.
        """

    def GetContainer(self) -> HtmlContainerCell:
        """
        GetContainer() -> HtmlContainerCell
        
        Returns pointer to the currently opened container (see Cells and
        Containers).
        """

    def GetDC(self) -> wx.DC:
        """
        GetDC() -> wx.DC
        
        Returns pointer to the DC used during parsing.
        """

    def GetFontBold(self) -> int:
        """
        GetFontBold() -> int
        
        Returns true if actual font is bold, false otherwise.
        """

    def GetFontFace(self) -> str:
        """
        GetFontFace() -> str
        
        Returns actual font face name.
        """

    def GetFontFixed(self) -> int:
        """
        GetFontFixed() -> int
        
        Returns true if actual font is fixed face, false otherwise.
        """

    def GetFontItalic(self) -> int:
        """
        GetFontItalic() -> int
        
        Returns true if actual font is italic, false otherwise.
        """

    def GetFontSize(self) -> int:
        """
        GetFontSize() -> int
        
        Returns actual font size (HTML size varies from -2 to +4)
        """

    def GetFontUnderlined(self) -> int:
        """
        GetFontUnderlined() -> int
        
        Returns true if actual font is underlined, false otherwise.
        """

    def GetLink(self) -> HtmlLinkInfo:
        """
        GetLink() -> HtmlLinkInfo
        
        Returns actual hypertext link.
        """

    def GetLinkColor(self) -> wx.Colour:
        """
        GetLinkColor() -> wx.Colour
        
        Returns the colour of hypertext link text.
        """

    def GetWindowInterface(self) -> HtmlWindowInterface:
        """
        GetWindowInterface() -> HtmlWindowInterface
        
        Returns associated window (wxHtmlWindow).
        """

    def OpenContainer(self) -> HtmlContainerCell:
        """
        OpenContainer() -> HtmlContainerCell
        
        Opens new container and returns pointer to it (see Cells and
        Containers).
        """

    def SetActualColor(self, clr: wx.Colour) -> None:
        """
        SetActualColor(clr) -> None
        
        Sets actual text colour.
        """

    def SetAlign(self, a: int) -> None:
        """
        SetAlign(a) -> None
        
        Sets default horizontal alignment (see
        wxHtmlContainerCell::SetAlignHor).
        """

    def SetContainer(self, c: HtmlContainerCell) -> HtmlContainerCell:
        """
        SetContainer(c) -> HtmlContainerCell
        
        Allows you to directly set opened container.
        """

    def SetDC(self, dc: wx.DC, pixel_scale: float=1.0e+0) -> None:
        """
        SetDC(dc, pixel_scale=1.0e+0) -> None
        
        Sets the DC.
        """

    def SetFontBold(self, x: int) -> None:
        """
        SetFontBold(x) -> None
        
        Sets bold flag of actualfont.
        """

    def SetFontFace(self, face: str) -> None:
        """
        SetFontFace(face) -> None
        
        Sets current font face to face.
        """

    def SetFontFixed(self, x: int) -> None:
        """
        SetFontFixed(x) -> None
        
        Sets fixed face flag of actualfont.
        """

    def SetFontItalic(self, x: int) -> None:
        """
        SetFontItalic(x) -> None
        
        Sets italic flag of actualfont.
        """

    def SetFontSize(self, s: int) -> None:
        """
        SetFontSize(s) -> None
        
        Sets actual font size (HTML size varies from 1 to 7).
        """

    def SetFontUnderlined(self, x: int) -> None:
        """
        SetFontUnderlined(x) -> None
        
        Sets underlined flag of actualfont.
        """

    def SetFonts(self, normal_face: str, fixed_face: str, sizes: List[int]) -> None:
        """
        SetFonts(normal_face, fixed_face, sizes) -> None
        
        Sets fonts.
        """

    def SetLink(self, link: HtmlLinkInfo) -> None:
        """
        SetLink(link) -> None
        
        Sets actual hypertext link.
        """

    def SetLinkColor(self, clr: wx.Colour) -> None:
        """
        SetLinkColor(clr) -> None
        
        Sets colour of hypertext link.
        """
    @property
    def ActualColor(self) -> wx.Colour: ...
    @ActualColor.setter
    def ActualColor(self, value: wx.Colour, /) -> None: ...
    @property
    def Align(self) -> int: ...
    @Align.setter
    def Align(self, value: int, /) -> None: ...
    @property
    def CharHeight(self) -> int: ...
    @property
    def CharWidth(self) -> int: ...
    @property
    def Container(self) -> HtmlContainerCell: ...
    @Container.setter
    def Container(self, value: HtmlContainerCell, /) -> None: ...
    @property
    def DC(self) -> wx.DC: ...
    @DC.setter
    def DC(self, value: wx.DC, /) -> None: ...
    @property
    def FontBold(self) -> int: ...
    @FontBold.setter
    def FontBold(self, value: int, /) -> None: ...
    @property
    def FontFace(self) -> str: ...
    @FontFace.setter
    def FontFace(self, value: str, /) -> None: ...
    @property
    def FontFixed(self) -> int: ...
    @FontFixed.setter
    def FontFixed(self, value: int, /) -> None: ...
    @property
    def FontItalic(self) -> int: ...
    @FontItalic.setter
    def FontItalic(self, value: int, /) -> None: ...
    @property
    def FontSize(self) -> int: ...
    @FontSize.setter
    def FontSize(self, value: int, /) -> None: ...
    @property
    def FontUnderlined(self) -> int: ...
    @FontUnderlined.setter
    def FontUnderlined(self, value: int, /) -> None: ...
    @property
    def Link(self) -> HtmlLinkInfo: ...
    @Link.setter
    def Link(self, value: HtmlLinkInfo, /) -> None: ...
    @property
    def LinkColor(self) -> wx.Colour: ...
    @LinkColor.setter
    def LinkColor(self, value: wx.Colour, /) -> None: ...
    @property
    def WindowInterface(self) -> HtmlWindowInterface: ...
# end of class HtmlWinParser


def HtmlWinParser_AddTagHandler(self, tagHandlerClass: Any) -> None:
    """
    HtmlWinParser_AddTagHandler(tagHandlerClass) -> None
    """
#-- end-htmlwinpars --#
#-- begin-helpdata --#

class HtmlBookRecord:
    """
    HtmlBookRecord(bookfile, basepath, title, start) -> None
    
    Helper class for wxHtmlHelpData.
    """

    def __init__(self, bookfile: str, basepath: str, title: str, start: str) -> None:
        """
        HtmlBookRecord(bookfile, basepath, title, start) -> None
        
        Helper class for wxHtmlHelpData.
        """

    def GetBookFile(self) -> str:
        """
        GetBookFile() -> str
        """

    def GetTitle(self) -> str:
        """
        GetTitle() -> str
        """

    def GetStart(self) -> str:
        """
        GetStart() -> str
        """

    def GetBasePath(self) -> str:
        """
        GetBasePath() -> str
        """

    def SetContentsRange(self, start: int, end: int) -> None:
        """
        SetContentsRange(start, end) -> None
        """

    def GetContentsStart(self) -> int:
        """
        GetContentsStart() -> int
        """

    def GetContentsEnd(self) -> int:
        """
        GetContentsEnd() -> int
        """

    def SetTitle(self, title: str) -> None:
        """
        SetTitle(title) -> None
        """

    def SetBasePath(self, path: str) -> None:
        """
        SetBasePath(path) -> None
        """

    def SetStart(self, start: str) -> None:
        """
        SetStart(start) -> None
        """

    def GetFullPath(self, page: str) -> str:
        """
        GetFullPath(page) -> str
        """
    @property
    def BasePath(self) -> str: ...
    @BasePath.setter
    def BasePath(self, value: str, /) -> None: ...
    @property
    def BookFile(self) -> str: ...
    @property
    def ContentsEnd(self) -> int: ...
    @property
    def ContentsStart(self) -> int: ...
    @property
    def Start(self) -> str: ...
    @Start.setter
    def Start(self, value: str, /) -> None: ...
    @property
    def Title(self) -> str: ...
    @Title.setter
    def Title(self, value: str, /) -> None: ...
# end of class HtmlBookRecord


class HtmlHelpDataItem:
    """
    HtmlHelpDataItem() -> None
    
    Helper class for wxHtmlHelpData.
    """

    def __init__(self) -> None:
        """
        HtmlHelpDataItem() -> None
        
        Helper class for wxHtmlHelpData.
        """
    level: int
    parent: HtmlHelpDataItem
    id: int
    name: str
    page: str
    book: HtmlBookRecord

    def GetFullPath(self) -> str:
        """
        GetFullPath() -> str
        """

    def GetIndentedName(self) -> str:
        """
        GetIndentedName() -> str
        """
    @property
    def FullPath(self) -> str: ...
    @property
    def IndentedName(self) -> str: ...
# end of class HtmlHelpDataItem


class HtmlHelpData(wx.Object):
    """
    HtmlHelpData() -> None
    
    This class is used by wxHtmlHelpController and wxHtmlHelpFrame to
    access HTML help items.
    """

    def __init__(self) -> None:
        """
        HtmlHelpData() -> None
        
        This class is used by wxHtmlHelpController and wxHtmlHelpFrame to
        access HTML help items.
        """

    def AddBook(self, book_url: str) -> bool:
        """
        AddBook(book_url) -> bool
        
        Adds new book.
        """

    def FindPageById(self, id: int) -> str:
        """
        FindPageById(id) -> str
        
        Returns page's URL based on integer ID stored in project.
        """

    def FindPageByName(self, page: str) -> str:
        """
        FindPageByName(page) -> str
        
        Returns page's URL based on its (file)name.
        """

    def GetBookRecArray(self) -> HtmlBookRecArray:
        """
        GetBookRecArray() -> HtmlBookRecArray
        
        Returns array with help books info.
        """

    def GetContentsArray(self) -> HtmlHelpDataItems:
        """
        GetContentsArray() -> HtmlHelpDataItems
        
        Returns reference to array with contents entries.
        """

    def GetIndexArray(self) -> HtmlHelpDataItems:
        """
        GetIndexArray() -> HtmlHelpDataItems
        
        Returns reference to array with index entries.
        """

    def SetTempDir(self, path: str) -> None:
        """
        SetTempDir(path) -> None
        
        Sets the temporary directory where binary cached versions of MS HTML
        Workshop files will be stored.
        """
    @property
    def BookRecArray(self) -> HtmlBookRecArray: ...
    @property
    def ContentsArray(self) -> HtmlHelpDataItems: ...
    @property
    def IndexArray(self) -> HtmlHelpDataItems: ...
# end of class HtmlHelpData

#-- end-helpdata --#
#-- begin-helpfrm --#
HF_TOOLBAR: int
HF_CONTENTS: int
HF_INDEX: int
HF_SEARCH: int
HF_BOOKMARKS: int
HF_OPEN_FILES: int
HF_PRINT: int
HF_FLAT_TOOLBAR: int
HF_MERGE_BOOKS: int
HF_ICONS_BOOK: int
HF_ICONS_BOOK_CHAPTER: int
HF_ICONS_FOLDER: int
HF_DEFAULT_STYLE: int

class HtmlHelpFrame(wx.Frame):
    """
    HtmlHelpFrame(data=None) -> None
    HtmlHelpFrame(parent, id=wx.ID_ANY, title='', style=HF_DEFAULT_STYLE, data=None, config=None, rootpath='') -> None
    
    This class is used by wxHtmlHelpController to display help.
    """

    @overload
    def __init__(self, parent: Optional[wx.Window], id: int=wx.ID_ANY, title: str='', style: int=HF_DEFAULT_STYLE, data: Optional[HtmlHelpData]=None, config: Optional[wx.ConfigBase]=None, rootpath: str='') -> None:
        ...

    @overload
    def __init__(self, data: Optional[HtmlHelpData]=None) -> None:
        """
        HtmlHelpFrame(data=None) -> None
        HtmlHelpFrame(parent, id=wx.ID_ANY, title='', style=HF_DEFAULT_STYLE, data=None, config=None, rootpath='') -> None
        
        This class is used by wxHtmlHelpController to display help.
        """

    def AddToolbarButtons(self, toolBar: wx.ToolBar, style: int) -> None:
        """
        AddToolbarButtons(toolBar, style) -> None
        
        You may override this virtual method to add more buttons to the help
        window's toolbar.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, title: str='', style: int=HF_DEFAULT_STYLE, config: Optional[wx.ConfigBase]=None, rootpath: str='') -> bool:
        """
        Create(parent, id=wx.ID_ANY, title='', style=HF_DEFAULT_STYLE, config=None, rootpath='') -> bool
        
        Creates the frame.
        """

    def GetController(self) -> HtmlHelpController:
        """
        GetController() -> HtmlHelpController
        
        Returns the help controller associated with the frame.
        """

    def SetController(self, controller: HtmlHelpController) -> None:
        """
        SetController(controller) -> None
        
        Sets the help controller associated with the frame.
        """

    def SetTitleFormat(self, format: str) -> None:
        """
        SetTitleFormat(format) -> None
        
        Sets the frame's title format.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Controller(self) -> HtmlHelpController: ...
    @Controller.setter
    def Controller(self, value: HtmlHelpController, /) -> None: ...
# end of class HtmlHelpFrame

#-- end-helpfrm --#
#-- begin-helpdlg --#

class HtmlHelpDialog(wx.Dialog):
    """
    HtmlHelpDialog(data=None) -> None
    HtmlHelpDialog(parent, id=wx.ID_ANY, title='', style=HF_DEFAULT_STYLE, data=None) -> None
    
    This class is used by wxHtmlHelpController to display help.
    """

    @overload
    def __init__(self, parent: Optional[wx.Window], id: int=wx.ID_ANY, title: str='', style: int=HF_DEFAULT_STYLE, data: Optional[HtmlHelpData]=None) -> None:
        ...

    @overload
    def __init__(self, data: Optional[HtmlHelpData]=None) -> None:
        """
        HtmlHelpDialog(data=None) -> None
        HtmlHelpDialog(parent, id=wx.ID_ANY, title='', style=HF_DEFAULT_STYLE, data=None) -> None
        
        This class is used by wxHtmlHelpController to display help.
        """

    def AddToolbarButtons(self, toolBar: wx.ToolBar, style: int) -> None:
        """
        AddToolbarButtons(toolBar, style) -> None
        
        You may override this virtual method to add more buttons to the help
        window's toolbar.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, title: str='', style: int=HF_DEFAULT_STYLE) -> bool:
        """
        Create(parent, id=wx.ID_ANY, title='', style=HF_DEFAULT_STYLE) -> bool
        
        Creates the dialog.
        """

    def GetController(self) -> HtmlHelpController:
        """
        GetController() -> HtmlHelpController
        
        Returns the help controller associated with the dialog.
        """

    def SetController(self, controller: HtmlHelpController) -> None:
        """
        SetController(controller) -> None
        
        Sets the help controller associated with the dialog.
        """

    def SetTitleFormat(self, format: str) -> None:
        """
        SetTitleFormat(format) -> None
        
        Sets the dialog's title format.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Controller(self) -> HtmlHelpController: ...
    @Controller.setter
    def Controller(self, value: HtmlHelpController, /) -> None: ...
# end of class HtmlHelpDialog

#-- end-helpdlg --#
#-- begin-helpwnd --#

class _enum_26(IntEnum):
    ID_HTML_PANEL = auto()
    ID_HTML_BACK = auto()
    ID_HTML_FORWARD = auto()
    ID_HTML_UPNODE = auto()
    ID_HTML_UP = auto()
    ID_HTML_DOWN = auto()
    ID_HTML_PRINT = auto()
    ID_HTML_OPENFILE = auto()
    ID_HTML_OPTIONS = auto()
    ID_HTML_BOOKMARKSLIST = auto()
    ID_HTML_BOOKMARKSADD = auto()
    ID_HTML_BOOKMARKSREMOVE = auto()
    ID_HTML_TREECTRL = auto()
    ID_HTML_INDEXPAGE = auto()
    ID_HTML_INDEXLIST = auto()
    ID_HTML_INDEXTEXT = auto()
    ID_HTML_INDEXBUTTON = auto()
    ID_HTML_INDEXBUTTONALL = auto()
    ID_HTML_NOTEBOOK = auto()
    ID_HTML_SEARCHPAGE = auto()
    ID_HTML_SEARCHTEXT = auto()
    ID_HTML_SEARCHLIST = auto()
    ID_HTML_SEARCHBUTTON = auto()
    ID_HTML_SEARCHCHOICE = auto()
    ID_HTML_COUNTINFO = auto()
ID_HTML_PANEL = _enum_26.ID_HTML_PANEL
ID_HTML_BACK = _enum_26.ID_HTML_BACK
ID_HTML_FORWARD = _enum_26.ID_HTML_FORWARD
ID_HTML_UPNODE = _enum_26.ID_HTML_UPNODE
ID_HTML_UP = _enum_26.ID_HTML_UP
ID_HTML_DOWN = _enum_26.ID_HTML_DOWN
ID_HTML_PRINT = _enum_26.ID_HTML_PRINT
ID_HTML_OPENFILE = _enum_26.ID_HTML_OPENFILE
ID_HTML_OPTIONS = _enum_26.ID_HTML_OPTIONS
ID_HTML_BOOKMARKSLIST = _enum_26.ID_HTML_BOOKMARKSLIST
ID_HTML_BOOKMARKSADD = _enum_26.ID_HTML_BOOKMARKSADD
ID_HTML_BOOKMARKSREMOVE = _enum_26.ID_HTML_BOOKMARKSREMOVE
ID_HTML_TREECTRL = _enum_26.ID_HTML_TREECTRL
ID_HTML_INDEXPAGE = _enum_26.ID_HTML_INDEXPAGE
ID_HTML_INDEXLIST = _enum_26.ID_HTML_INDEXLIST
ID_HTML_INDEXTEXT = _enum_26.ID_HTML_INDEXTEXT
ID_HTML_INDEXBUTTON = _enum_26.ID_HTML_INDEXBUTTON
ID_HTML_INDEXBUTTONALL = _enum_26.ID_HTML_INDEXBUTTONALL
ID_HTML_NOTEBOOK = _enum_26.ID_HTML_NOTEBOOK
ID_HTML_SEARCHPAGE = _enum_26.ID_HTML_SEARCHPAGE
ID_HTML_SEARCHTEXT = _enum_26.ID_HTML_SEARCHTEXT
ID_HTML_SEARCHLIST = _enum_26.ID_HTML_SEARCHLIST
ID_HTML_SEARCHBUTTON = _enum_26.ID_HTML_SEARCHBUTTON
ID_HTML_SEARCHCHOICE = _enum_26.ID_HTML_SEARCHCHOICE
ID_HTML_COUNTINFO = _enum_26.ID_HTML_COUNTINFO

class HtmlHelpWindow(wx.Window):
    """
    HtmlHelpWindow(data=None) -> None
    HtmlHelpWindow(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.TAB_TRAVERSAL|wx.BORDER_NONE, helpStyle=HF_DEFAULT_STYLE, data=None) -> None
    
    This class is used by wxHtmlHelpController to display help within a
    frame or dialog, but you can use it yourself to create an embedded
    HTML help window.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.TAB_TRAVERSAL|wx.BORDER_NONE, helpStyle: int=HF_DEFAULT_STYLE, data: Optional[HtmlHelpData]=None) -> None:
        ...

    @overload
    def __init__(self, data: Optional[HtmlHelpData]=None) -> None:
        """
        HtmlHelpWindow(data=None) -> None
        HtmlHelpWindow(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.TAB_TRAVERSAL|wx.BORDER_NONE, helpStyle=HF_DEFAULT_STYLE, data=None) -> None
        
        This class is used by wxHtmlHelpController to display help within a
        frame or dialog, but you can use it yourself to create an embedded
        HTML help window.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.TAB_TRAVERSAL|wx.BORDER_NONE, helpStyle: int=HF_DEFAULT_STYLE) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.TAB_TRAVERSAL|wx.BORDER_NONE, helpStyle=HF_DEFAULT_STYLE) -> bool
        
        Creates the help window.
        """

    @overload
    def Display(self, id: int) -> bool:
        ...

    @overload
    def Display(self, x: str) -> bool:
        """
        Display(x) -> bool
        Display(id) -> bool
        
        Displays page x.
        """

    def DisplayContents(self) -> bool:
        """
        DisplayContents() -> bool
        
        Displays contents panel.
        """

    def DisplayIndex(self) -> bool:
        """
        DisplayIndex() -> bool
        
        Displays index panel.
        """

    def GetData(self) -> HtmlHelpData:
        """
        GetData() -> HtmlHelpData
        
        Returns the wxHtmlHelpData object, which is usually a pointer to the
        controller's data.
        """

    def KeywordSearch(self, keyword: str, mode: wx.HelpSearchMode=wx.HELP_SEARCH_ALL) -> bool:
        """
        KeywordSearch(keyword, mode=wx.HELP_SEARCH_ALL) -> bool
        
        Search for given keyword.
        """

    def ReadCustomization(self, cfg: wx.ConfigBase, path: str='') -> None:
        """
        ReadCustomization(cfg, path='') -> None
        
        Reads the user's settings for this window.
        """

    def UseConfig(self, config: wx.ConfigBase, rootpath: str='') -> None:
        """
        UseConfig(config, rootpath='') -> None
        
        Associates a wxConfig object with the help window.
        """

    def WriteCustomization(self, cfg: wx.ConfigBase, path: str='') -> None:
        """
        WriteCustomization(cfg, path='') -> None
        
        Saves the user's settings for this window.
        """

    def RefreshLists(self) -> None:
        """
        RefreshLists() -> None
        
        Refresh all panels.
        """

    def GetController(self) -> HtmlHelpController:
        """
        GetController() -> HtmlHelpController
        """

    def SetController(self, controller: HtmlHelpController) -> None:
        """
        SetController(controller) -> None
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Controller(self) -> HtmlHelpController: ...
    @Controller.setter
    def Controller(self, value: HtmlHelpController, /) -> None: ...
    @property
    def Data(self) -> HtmlHelpData: ...

    def CreateSearch(self) -> None:
        """
        CreateSearch() -> None
        
        Creates search panel.
        """

    def AddToolbarButtons(self, toolBar: wx.ToolBar, style: int) -> None:
        """
        AddToolbarButtons(toolBar, style) -> None
        
        You may override this virtual method to add more buttons to the help
        window's toolbar.
        """

    def CreateContents(self) -> None:
        """
        CreateContents() -> None
        
        Creates contents panel.
        """

    def CreateIndex(self) -> None:
        """
        CreateIndex() -> None
        
        Creates index panel.
        """
# end of class HtmlHelpWindow

#-- end-helpwnd --#
#-- begin-helpctrl --#
ID_HTML_HELPFRAME: int
HF_EMBEDDED: int
HF_DIALOG: int
HF_FRAME: int
HF_MODAL: int

class HtmlHelpController(wx.HelpControllerBase):
    """
    HtmlHelpController(style=HF_DEFAULT_STYLE, parentWindow=None) -> None
    HtmlHelpController(parentWindow, style=HF_DEFAULT_STYLE) -> None
    
    This help controller provides an easy way of displaying HTML help in
    your application (see HTML Sample, test example).
    """

    @overload
    def __init__(self, parentWindow: wx.Window, style: int=HF_DEFAULT_STYLE) -> None:
        ...

    @overload
    def __init__(self, style: int=HF_DEFAULT_STYLE, parentWindow: Optional[wx.Window]=None) -> None:
        """
        HtmlHelpController(style=HF_DEFAULT_STYLE, parentWindow=None) -> None
        HtmlHelpController(parentWindow, style=HF_DEFAULT_STYLE) -> None
        
        This help controller provides an easy way of displaying HTML help in
        your application (see HTML Sample, test example).
        """

    @overload
    def AddBook(self, bookUrl: str, showWaitMsg: bool=False) -> bool:
        ...

    @overload
    def AddBook(self, bookFile: str, showWaitMsg: bool=False) -> bool:
        """
        AddBook(bookFile, showWaitMsg=False) -> bool
        AddBook(bookUrl, showWaitMsg=False) -> bool
        
        Adds a book (i.e.
        """

    @overload
    def Display(self, id: int) -> bool:
        ...

    @overload
    def Display(self, x: str) -> bool:
        """
        Display(x) -> bool
        Display(id) -> bool
        
        Displays page x.
        """

    def DisplayContents(self) -> bool:
        """
        DisplayContents() -> bool
        
        Displays help window and focuses contents panel.
        """

    def DisplayIndex(self) -> bool:
        """
        DisplayIndex() -> bool
        
        Displays help window and focuses index panel.
        """

    def KeywordSearch(self, keyword: str, mode: wx.HelpSearchMode=wx.HELP_SEARCH_ALL) -> bool:
        """
        KeywordSearch(keyword, mode=wx.HELP_SEARCH_ALL) -> bool
        
        Displays the help window, focuses search panel and starts searching.
        """

    def ReadCustomization(self, cfg: wx.ConfigBase, path: str='') -> None:
        """
        ReadCustomization(cfg, path='') -> None
        
        Reads the controller's setting (position of window, etc.)
        """

    def SetShouldPreventAppExit(self, enable: bool) -> None:
        """
        SetShouldPreventAppExit(enable) -> None
        
        Sets whether the help frame should prevent application from exiting if
        it's the only remaining top level window.
        """

    def SetTempDir(self, path: str) -> None:
        """
        SetTempDir(path) -> None
        
        Sets the path for storing temporary files - cached binary versions of
        index and contents files.
        """

    def SetTitleFormat(self, format: str) -> None:
        """
        SetTitleFormat(format) -> None
        
        Sets format of title of the frame.
        """

    def UseConfig(self, config: wx.ConfigBase, rootpath: str='') -> None:
        """
        UseConfig(config, rootpath='') -> None
        
        Associates the config object with the controller.
        """

    def WriteCustomization(self, cfg: wx.ConfigBase, path: str='') -> None:
        """
        WriteCustomization(cfg, path='') -> None
        
        Stores controllers setting (position of window etc.)
        """

    def GetHelpWindow(self) -> HtmlHelpWindow:
        """
        GetHelpWindow() -> HtmlHelpWindow
        
        Get the current help window.
        """

    def SetHelpWindow(self, helpWindow: HtmlHelpWindow) -> None:
        """
        SetHelpWindow(helpWindow) -> None
        
        Set the help window to be managed by this controller.
        """

    def GetFrame(self) -> HtmlHelpFrame:
        """
        GetFrame() -> HtmlHelpFrame
        
        Returns the current help frame.
        """

    def GetDialog(self) -> HtmlHelpDialog:
        """
        GetDialog() -> HtmlHelpDialog
        
        Returns the current help dialog.
        """
    @property
    def Dialog(self) -> HtmlHelpDialog: ...
    @property
    def Frame(self) -> HtmlHelpFrame: ...
    @property
    def HelpWindow(self) -> HtmlHelpWindow: ...
    @HelpWindow.setter
    def HelpWindow(self, value: HtmlHelpWindow, /) -> None: ...

    def CreateHelpDialog(self, data: HtmlHelpData) -> HtmlHelpDialog:
        """
        CreateHelpDialog(data) -> HtmlHelpDialog
        
        This protected virtual method may be overridden so that when
        specifying the wxHF_DIALOG style, the controller uses a different
        dialog.
        """

    def CreateHelpFrame(self, data: HtmlHelpData) -> HtmlHelpFrame:
        """
        CreateHelpFrame(data) -> HtmlHelpFrame
        
        This protected virtual method may be overridden so that the controller
        uses a different frame.
        """
# end of class HtmlHelpController


class HtmlModalHelp:
    """
    HtmlModalHelp(parent, helpFile, topic='', style=HF_DEFAULT_STYLE|HF_DIALOG|HF_MODAL) -> None
    
    This class uses wxHtmlHelpController to display help in a modal
    dialog.
    """

    def __init__(self, parent: wx.Window, helpFile: str, topic: str='', style: int=HF_DEFAULT_STYLE|HF_DIALOG|HF_MODAL) -> None:
        """
        HtmlModalHelp(parent, helpFile, topic='', style=HF_DEFAULT_STYLE|HF_DIALOG|HF_MODAL) -> None
        
        This class uses wxHtmlHelpController to display help in a modal
        dialog.
        """
# end of class HtmlModalHelp

#-- end-helpctrl --#
#-- begin-htmllbox --#
HLB_DEFAULT_STYLE: int
HLB_MULTIPLE: int
HtmlListBoxNameStr: str

class HtmlListBox(wx.VListBox):
    """
    HtmlListBox(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, name=HtmlListBoxNameStr) -> None
    HtmlListBox() -> None
    
    wxHtmlListBox is an implementation of wxVListBox which shows HTML
    content in the listbox rows.
    """

    @overload
    def __init__(self) -> None:
        ...

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, name: str=HtmlListBoxNameStr) -> None:
        """
        HtmlListBox(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, name=HtmlListBoxNameStr) -> None
        HtmlListBox() -> None
        
        wxHtmlListBox is an implementation of wxVListBox which shows HTML
        content in the listbox rows.
        """

    def GetFileSystem(self) -> wx.FileSystem:
        """
        GetFileSystem() -> wx.FileSystem
        
        Returns the wxFileSystem used by the HTML parser of this object.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, name: str=HtmlListBoxNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, name=HtmlListBoxNameStr) -> bool
        
        Creates the control and optionally sets the initial number of items in it (it may also be set or changed later with wxVListBox::SetItemCount).
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def FileSystem(self) -> wx.FileSystem: ...

    def OnLinkClicked(self, n: int, link: HtmlLinkInfo) -> None:
        """
        OnLinkClicked(n, link) -> None
        
        Called when the user clicks on hypertext link.
        """

    def GetSelectedTextBgColour(self, colBg: wx.Colour) -> wx.Colour:
        """
        GetSelectedTextBgColour(colBg) -> wx.Colour
        
        This virtual function may be overridden to change the appearance of
        the background of the selected cells in the same way as
        GetSelectedTextColour().
        """

    def GetSelectedTextColour(self, colFg: wx.Colour) -> wx.Colour:
        """
        GetSelectedTextColour(colFg) -> wx.Colour
        
        This virtual function may be overridden to customize the appearance of
        the selected cells.
        """

    def OnGetItemMarkup(self, n: int) -> str:
        """
        OnGetItemMarkup(n) -> str
        
        This function may be overridden to decorate HTML returned by
        OnGetItem().
        """

    def OnGetItem(self, n: int) -> str:
        """
        OnGetItem(n) -> str
        
        This method must be implemented in the derived class and should return
        the body (i.e. without html nor body tags) of the HTML fragment for
        the given item.
        """
# end of class HtmlListBox

SimpleHtmlListBoxNameStr: str

class SimpleHtmlListBox(HtmlListBox, wx.ItemContainer):
    """
    SimpleHtmlListBox(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, choices=[], style=HLB_DEFAULT_STYLE, validator=wx.DefaultValidator, name=SimpleHtmlListBoxNameStr) -> None
    SimpleHtmlListBox() -> None
    
    wxSimpleHtmlListBox is an implementation of wxHtmlListBox which shows
    HTML content in the listbox rows.
    """

    @overload
    def __init__(self) -> None:
        ...

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, choices: List[str]=[], style: int=HLB_DEFAULT_STYLE, validator: wx.Validator=wx.DefaultValidator, name: str=SimpleHtmlListBoxNameStr) -> None:
        """
        SimpleHtmlListBox(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, choices=[], style=HLB_DEFAULT_STYLE, validator=wx.DefaultValidator, name=SimpleHtmlListBoxNameStr) -> None
        SimpleHtmlListBox() -> None
        
        wxSimpleHtmlListBox is an implementation of wxHtmlListBox which shows
        HTML content in the listbox rows.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, choices: List[str]=[], style: int=HLB_DEFAULT_STYLE, validator: wx.Validator=wx.DefaultValidator, name: str=SimpleHtmlListBoxNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, choices=[], style=HLB_DEFAULT_STYLE, validator=wx.DefaultValidator, name=SimpleHtmlListBoxNameStr) -> bool
        
        Creates the HTML listbox for two-step construction.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
# end of class SimpleHtmlListBox

#-- end-htmllbox --#
