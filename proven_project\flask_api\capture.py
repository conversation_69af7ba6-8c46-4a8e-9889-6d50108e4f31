"""Fingerprint Capture Module

This module handles the communication with the fingerprint bridge service
for capturing new fingerprint images. It establishes a TCP socket connection
to the bridge service and processes the captured fingerprint data.
"""

import socket
import time

def capture_fingerprint_bmp(person_id, finger_index):
    """Capture a fingerprint image in BMP format.
    
    Establishes a connection to the fingerprint bridge service and requests
    a new fingerprint capture. The captured image is returned in base64 format.
    
    Args:
        person_id (str): Unique identifier for the person
        finger_index (int): Index of the finger being captured (1-10)
    
    Returns:
        dict: Contains capture operation result
            - status: 'success' or 'error'
            - message: Operation result description
            - bmp_base64: Base64 encoded fingerprint image (if successful)
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(30)  # longer timeout for full scan
            s.connect(('127.0.0.1', 8123))
            command = f"CAPTURE {person_id} {finger_index}\n"
            s.sendall(command.encode())

            start_time = time.time()
            data = b""
            while True:
                chunk = s.recv(4096)
                if not chunk:
                    break
                data += chunk

            duration = time.time() - start_time
            print(f"[Bridge] Response received in {duration:.2f} sec")

            decoded = data.decode("utf-8", errors="ignore")
            lines = decoded.splitlines()

            result_message = "❌ No valid response"
            base64_bmp = ""

            for line in lines:
                clean_line = line.strip().lstrip("\ufeff")
                print(f"[Bridge] {clean_line}")
                if clean_line.startswith("BMP:"):
                    base64_bmp = clean_line[4:]
                elif clean_line.upper().startswith("OK") or "✅" in clean_line:
                    result_message = clean_line
                elif "ERROR" in clean_line.upper():
                    result_message = clean_line

            status = "success" if "OK" in result_message.upper() or "✅" in result_message else "error"

            return {
                "status": status,
                "message": result_message,
                "bmp_base64": base64_bmp
            }

    except socket.timeout:
        return {"status": "error", "message": "Socket timeout during capture"}
    except socket.error as e:
        return {"status": "error", "message": f"Socket error: {str(e)}"}
    except Exception as e:
        return {"status": "error", "message": f"Unexpected error: {str(e)}"}
