using System;
using System.Windows.Forms;
using Microsoft.Owin.Hosting;
using Owin;
using System.Web.Http;
using System.IO;
using MultiFingerDemo.WebDemo;
using System.Drawing;
using System.Net.Sockets;
using System.Net;
using System.Threading.Tasks;
using System.Text;

namespace MultiFingerDemo
{
    static class Program
    {
        private static IDisposable webApp;
        private static WebServer webDemoServer;
        private static TrayApplicationContext trayContext;
        private static string ApiBaseAddress = "http://localhost:9000/";
        private static string WebDemoAddress = "http://localhost:8080/";

        // Public properties for access from TrayApplicationContext
        public static string CurrentApiAddress { get { return ApiBaseAddress; } }
        public static string CurrentWebDemoAddress { get { return WebDemoAddress; } }
        public static WebServer CurrentWebDemoServer { get { return webDemoServer; } }

        /// <summary>
        /// Automatically initialize and open the fingerprint device on startup
        /// </summary>
        private static void AutoInitializeDevice()
        {
            try
            {
                Console.WriteLine("[Startup] Initializing fingerprint device for bridge service...");

                var deviceManager = Api.TrustFingerDeviceManager.Instance;

                // Initialize SDK
                Aratek.TrustFinger.TrustFingerManager.GlobalInitialize();
                Console.WriteLine("[Startup] SDK initialized");

                // Check device count
                int devCount = Aratek.TrustFinger.TrustFingerManager.GetDeviceCount();
                Console.WriteLine($"[Startup] Found {devCount} device(s)");

                if (devCount > 0)
                {
                    // Try to open device
                    bool opened = deviceManager.OpenDevice(0);
                    Console.WriteLine($"[Startup] Device open result: {opened}");

                    if (opened)
                    {
                        Console.WriteLine("[Startup] ✅ Device connected successfully");

                        // Try to get device description
                        try
                        {
                            var desc = Aratek.TrustFinger.TrustFingerManager.GetDeviceDescription(0);
                            Console.WriteLine($"[Startup] Device: {desc.ProductName} (ID: {desc.DeviceId})");

                            // Check if device is supported
                            if (desc.DeviceId == 800 || desc.DeviceId == 900 || desc.DeviceId == 303)
                            {
                                Console.WriteLine("[Startup] ✅ Device is supported and bridge is ready");
                            }
                            else
                            {
                                Console.WriteLine($"[Startup] ⚠️ Device ID {desc.DeviceId} may not be fully supported");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[Startup] ⚠️ Device opened but description failed: {ex.Message}");
                            Console.WriteLine("[Startup] ✅ Device should still work for basic operations");
                        }
                    }
                    else
                    {
                        Console.WriteLine("[Startup] ❌ Failed to open device");
                        Console.WriteLine("[Startup] Bridge will run but device operations will fail until manually opened");
                    }
                }
                else
                {
                    Console.WriteLine("[Startup] ❌ No devices found");
                    Console.WriteLine("[Startup] Bridge will run but device operations will fail until device is connected");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Startup] ❌ Exception during device initialization: {ex.Message}");
                Console.WriteLine("[Startup] Bridge will run but device operations may fail");
            }
        }

        /// <summary>
        /// ??????????
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Load configuration from config.sys
            ConfigManager.LoadConfig();

            // Auto-initialize device on startup for bridge functionality
            AutoInitializeDevice();

            StartWebApi();
            StartWebDemo();
            StartTcpBridgeServer();

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Create and run the tray application context
            trayContext = new TrayApplicationContext();
            Application.Run(trayContext);

            StopServers();
        }

        private static void StartWebApi()
        {
            // Get preferred port from config, with fallback ports
            string preferredPort = ConfigManager.GetValue("api_port", "9000");
            string[] possiblePorts = { preferredPort, "9000", "9001", "9002", "8090", "8091" };

            foreach (string port in possiblePorts)
            {
                try
                {
                    string apiUrl = "http://localhost:" + port + "/";
                    webApp = WebApp.Start<Startup>(url: apiUrl);

                    // Update the API base address for other components
                    ApiBaseAddress = apiUrl;
                    return; // Success, exit the loop
                }
                catch (Exception)
                {
                    // Log to event log or file if needed
                    // For now, just continue to next port
                }
            }

            // If we get here, all ports failed
            MessageBox.Show("Failed to start Web API on any available port. Please check if ports 9000-9002, 8090-8091 are available.",
                "Web API Startup Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        private static void StartWebDemo()
        {
            try
            {
                string webRoot = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "web_demo");
                if (!Directory.Exists(webRoot))
                {
                    return; // Web demo directory not found, skip
                }

                // Get web demo port from config
                string webDemoPort = ConfigManager.GetValue("web_demo_port", "8080");
                WebDemoAddress = "http://localhost:" + webDemoPort + "/";

                webDemoServer = new WebServer(WebDemoAddress, webRoot);
                webDemoServer.Start();
            }
            catch (Exception)
            {
                // Don't show MessageBox for web demo failure, just continue
            }
        }

        private static TcpListener tcpListener;

        /// <summary>
        /// Start TCP Bridge Server for Python API communication
        /// </summary>
        private static void StartTcpBridgeServer()
        {
            try
            {
                Task.Run(() =>
                {
                    try
                    {
                        tcpListener = new TcpListener(IPAddress.Loopback, 8123);
                        tcpListener.Start();
                        Console.WriteLine("[TCP] 🔌 TCP Bridge server started on port 8123");

                        while (true)
                        {
                            try
                            {
                                using (TcpClient client = tcpListener.AcceptTcpClient())
                                {
                                    Console.WriteLine("[TCP] 📡 Connection from: " + client.Client.RemoteEndPoint);

                                    using (NetworkStream stream = client.GetStream())
                                    using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                                    using (StreamWriter writer = new StreamWriter(stream, Encoding.UTF8) { AutoFlush = true })
                                    {
                                        string line = reader.ReadLine();
                                        Console.WriteLine("[TCP] ➡️ Command: " + line);

                                        if (string.IsNullOrWhiteSpace(line))
                                        {
                                            writer.WriteLine("ERROR Empty command");
                                            continue;
                                        }

                                        // Process command using device manager
                                        string response = ProcessTcpCommand(line, writer);
                                        Console.WriteLine("[TCP] ⬅️ Response: " + response);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine("[TCP] ❌ Client error: " + ex.Message);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("[TCP] 🔥 Bridge server error: " + ex.Message);
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine("[TCP] ❌ Failed to start TCP bridge server: " + ex.Message);
            }
        }

        /// <summary>
        /// Process TCP command and return response
        /// </summary>
        private static string ProcessTcpCommand(string commandLine, StreamWriter writer)
        {
            try
            {
                var parts = commandLine.Trim().Split(' ');
                var command = parts[0].ToUpper();

                switch (command)
                {
                    case "OPEN":
                    case "OPEN_DEVICE":
                        return ProcessOpenDevice(writer);

                    case "CLOSE":
                    case "CLOSE_DEVICE":
                        return ProcessCloseDevice(writer);

                    case "STATUS":
                    case "DEVICE_INFO":
                        return ProcessDeviceInfo(writer);

                    case "CAPTURE":
                        return parts.Length >= 2
                            ? ProcessCapture(int.Parse(parts[1]), writer)
                            : ProcessError("Usage: CAPTURE <finger_index>", writer);

                    case "ENROLL":
                        return parts.Length >= 3
                            ? ProcessEnroll(parts[1], int.Parse(parts[2]), writer)
                            : ProcessError("Usage: ENROLL <person_id> <finger_index>", writer);

                    case "VERIFY":
                        return parts.Length >= 3
                            ? ProcessVerify(parts[1], int.Parse(parts[2]), writer)
                            : ProcessError("Usage: VERIFY <person_id> <finger_index>", writer);

                    case "IDENTIFY":
                        return parts.Length >= 2
                            ? ProcessIdentify(int.Parse(parts[1]), writer)
                            : ProcessError("Usage: IDENTIFY <finger_index>", writer);

                    default:
                        writer.WriteLine($"ERROR Unknown command: {command}");
                        return $"ERROR Unknown command: {command}";
                }
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        private static string ProcessOpenDevice(StreamWriter writer)
        {
            try
            {
                var deviceManager = Api.TrustFingerDeviceManager.Instance;

                if (deviceManager.IsOpen)
                {
                    writer.WriteLine("✅ Device already open");
                    writer.WriteLine("RESULT:STATUS=OPEN,DEVICE_ID=900");
                    return "OK Device already open";
                }

                bool opened = deviceManager.OpenDevice(0);
                if (opened)
                {
                    writer.WriteLine("✅ Device opened successfully");
                    writer.WriteLine("RESULT:STATUS=OPEN,DEVICE_ID=900,DEVICE_NAME=A900 Device");
                    return "OK Device opened";
                }
                else
                {
                    writer.WriteLine("❌ Failed to open device");
                    writer.WriteLine("RESULT:STATUS=ERROR,MESSAGE=Failed to open device");
                    return "ERROR Failed to open device";
                }
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        private static string ProcessCloseDevice(StreamWriter writer)
        {
            try
            {
                var deviceManager = Api.TrustFingerDeviceManager.Instance;
                deviceManager.CloseDevice();
                writer.WriteLine("✅ Device closed");
                writer.WriteLine("RESULT:STATUS=CLOSED");
                return "OK Device closed";
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        private static string ProcessDeviceInfo(StreamWriter writer)
        {
            try
            {
                var deviceManager = Api.TrustFingerDeviceManager.Instance;

                if (deviceManager.IsOpen)
                {
                    writer.WriteLine("✅ Device Status: OPEN");
                    writer.WriteLine("RESULT:STATUS=OPEN,DEVICE_ID=900,DEVICE_NAME=A900 Device,SERIAL_NUMBER=Unknown");
                    return "OK Device info - OPEN";
                }
                else
                {
                    writer.WriteLine("❌ Device Status: CLOSED");
                    writer.WriteLine("RESULT:STATUS=CLOSED,DEVICE_ID=,DEVICE_NAME=,SERIAL_NUMBER=");
                    return "OK Device info - CLOSED";
                }
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        private static string ProcessCapture(int fingerIndex, StreamWriter writer)
        {
            try
            {
                Console.WriteLine($"[ProcessCapture] Starting real capture for finger {fingerIndex}");
                writer.WriteLine($"⏳ Starting capture for finger {fingerIndex}...");

                var deviceManager = Api.TrustFingerDeviceManager.Instance;

                if (!deviceManager.IsOpen)
                {
                    writer.WriteLine("ERROR Device not open");
                    return "ERROR Device not open";
                }

                // Get the MainForm instance for real capture (same as proven project)
                var mainForm = TrayApplicationContext.CurrentMainForm;
                if (mainForm == null)
                {
                    // Create MainForm if it doesn't exist (for TCP-only usage)
                    Console.WriteLine("[ProcessCapture] Creating MainForm for TCP capture");
                    mainForm = new MainForm();

                    // Ensure window handle is created (required for Invoke operations)
                    var handle = mainForm.Handle; // This forces window handle creation
                    Console.WriteLine($"[ProcessCapture] MainForm window handle created: {handle}");

                    TrayApplicationContext.CurrentMainForm = mainForm;
                }

                Console.WriteLine($"[ProcessCapture] Calling MainForm.RunCapture for finger {fingerIndex}");

                // Use placeholder person ID since capture doesn't require it
                string placeholderPersonId = "TCP_CAPTURE";

                // Call the existing working capture method directly (no UI thread needed for capture logic)
                try
                {
                    Console.WriteLine($"[ProcessCapture] Calling RunCapture for {placeholderPersonId}, finger {fingerIndex}");
                    string result = mainForm.RunCapture(placeholderPersonId, fingerIndex, writer);
                    Console.WriteLine($"[ProcessCapture] RunCapture completed: {result}");
                    return result;
                }
                catch (Exception ex)
                {
                    string err = "ERROR RunCapture failed: " + ex.Message;
                    writer.WriteLine(err);
                    writer.Flush();
                    Console.WriteLine($"[ProcessCapture] RunCapture failed: {ex.Message}");
                    return err;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ProcessCapture] Error: {ex.Message}");
                Console.WriteLine($"[ProcessCapture] Stack trace: {ex.StackTrace}");
                writer.WriteLine("ERROR " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        private static string ProcessEnroll(string personId, int fingerIndex, StreamWriter writer)
        {
            try
            {
                Console.WriteLine($"[ProcessEnroll] Starting enroll for {personId}, finger {fingerIndex}");
                writer.WriteLine($"⏳ Starting enrollment for {personId}, finger {fingerIndex}...");

                // Get the MainForm instance for real enroll (same core function as UI)
                var mainForm = TrayApplicationContext.CurrentMainForm;
                if (mainForm == null)
                {
                    Console.WriteLine("[ProcessEnroll] Creating MainForm for TCP enroll");
                    mainForm = new MainForm();
                    var handle = mainForm.Handle; // Force window handle creation
                    TrayApplicationContext.CurrentMainForm = mainForm;
                }

                Console.WriteLine($"[ProcessEnroll] Calling MainForm.RunEnroll for {personId}, finger {fingerIndex}");

                // Call the existing working enroll method
                try
                {
                    string result = mainForm.RunEnroll(personId, fingerIndex, writer);
                    Console.WriteLine($"[ProcessEnroll] RunEnroll completed: {result}");
                    return result;
                }
                catch (Exception ex)
                {
                    string err = "ERROR RunEnroll failed: " + ex.Message;
                    writer.WriteLine(err);
                    writer.Flush();
                    Console.WriteLine($"[ProcessEnroll] RunEnroll failed: {ex.Message}");
                    return err;
                }
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        private static string ProcessVerify(string personId, int fingerIndex, StreamWriter writer)
        {
            try
            {
                Console.WriteLine($"[ProcessVerify] Starting verify for {personId}, finger {fingerIndex}");
                writer.WriteLine($"⏳ Starting verification for {personId}, finger {fingerIndex}...");

                // Get the MainForm instance for real verify (same core function as UI)
                var mainForm = TrayApplicationContext.CurrentMainForm;
                if (mainForm == null)
                {
                    Console.WriteLine("[ProcessVerify] Creating MainForm for TCP verify");
                    mainForm = new MainForm();
                    var handle = mainForm.Handle; // Force window handle creation
                    TrayApplicationContext.CurrentMainForm = mainForm;
                }

                Console.WriteLine($"[ProcessVerify] Calling MainForm.RunVerify for {personId}, finger {fingerIndex}");

                // Call the existing working verify method
                try
                {
                    string result = mainForm.RunVerify(personId, fingerIndex, writer);
                    Console.WriteLine($"[ProcessVerify] RunVerify completed: {result}");
                    return result;
                }
                catch (Exception ex)
                {
                    string err = "ERROR RunVerify failed: " + ex.Message;
                    writer.WriteLine(err);
                    writer.Flush();
                    Console.WriteLine($"[ProcessVerify] RunVerify failed: {ex.Message}");
                    return err;
                }
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        private static string ProcessIdentify(int fingerIndex, StreamWriter writer)
        {
            try
            {
                Console.WriteLine($"[ProcessIdentify] Starting identification for finger {fingerIndex}");
                writer.WriteLine($"⏳ Starting identification for finger {fingerIndex}...");

                // Get the MainForm instance for real identify (same core function as UI)
                var mainForm = TrayApplicationContext.CurrentMainForm;
                if (mainForm == null)
                {
                    Console.WriteLine("[ProcessIdentify] Creating MainForm for TCP identify");
                    mainForm = new MainForm();
                    var handle = mainForm.Handle; // Force window handle creation
                    TrayApplicationContext.CurrentMainForm = mainForm;
                }

                Console.WriteLine($"[ProcessIdentify] Calling MainForm.RunIdentify for finger {fingerIndex}");

                // Call the existing working identify method
                try
                {
                    string result = mainForm.RunIdentify(fingerIndex, writer);
                    Console.WriteLine($"[ProcessIdentify] RunIdentify completed: {result}");
                    return result;
                }
                catch (Exception ex)
                {
                    string err = "ERROR RunIdentify failed: " + ex.Message;
                    writer.WriteLine(err);
                    writer.Flush();
                    Console.WriteLine($"[ProcessIdentify] RunIdentify failed: {ex.Message}");
                    return err;
                }
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        private static string ProcessError(string message, StreamWriter writer)
        {
            writer.WriteLine("ERROR " + message);
            return "ERROR " + message;
        }

        /// <summary>
        /// Get finger name from index (including slaps)
        /// </summary>
        private static string GetFingerName(int fingerIndex)
        {
            string[] fingerNames = {
                "", "Right Thumb", "Right Index", "Right Middle", "Right Ring", "Right Little",
                "Left Thumb", "Left Index", "Left Middle", "Left Ring", "Left Little",
                "Two Thumbs", "Left Four Fingers", "Right Four Fingers"
            };

            return fingerIndex >= 1 && fingerIndex <= 13 ? fingerNames[fingerIndex] : "Unknown";
        }

        /// <summary>
        /// Create placeholder BMP image data
        /// </summary>
        private static byte[] CreatePlaceholderBMP()
        {
            // Create a simple 100x100 gray BMP image
            int width = 100;
            int height = 100;
            int stride = width * 3; // 3 bytes per pixel (RGB)
            int imageSize = stride * height;
            int fileSize = 54 + imageSize; // BMP header is 54 bytes

            byte[] bmpData = new byte[fileSize];

            // BMP Header
            bmpData[0] = 0x42; // 'B'
            bmpData[1] = 0x4D; // 'M'
            BitConverter.GetBytes(fileSize).CopyTo(bmpData, 2);
            BitConverter.GetBytes(54).CopyTo(bmpData, 10); // Offset to pixel data
            BitConverter.GetBytes(40).CopyTo(bmpData, 14); // DIB header size
            BitConverter.GetBytes(width).CopyTo(bmpData, 18);
            BitConverter.GetBytes(height).CopyTo(bmpData, 22);
            BitConverter.GetBytes((short)1).CopyTo(bmpData, 26); // Planes
            BitConverter.GetBytes((short)24).CopyTo(bmpData, 28); // Bits per pixel
            BitConverter.GetBytes(imageSize).CopyTo(bmpData, 34);

            // Fill with gray pixels (RGB: 211, 211, 211)
            for (int i = 54; i < fileSize; i += 3)
            {
                bmpData[i] = 211;     // Blue
                bmpData[i + 1] = 211; // Green
                bmpData[i + 2] = 211; // Red
            }

            return bmpData;
        }

        private static void StopServers()
        {
            if (webApp != null)
            {
                webApp.Dispose();
            }

            if (webDemoServer != null)
            {
                webDemoServer.Stop();
            }

            if (tcpListener != null)
            {
                tcpListener.Stop();
                Console.WriteLine("[TCP] TCP Bridge server stopped");
            }
        }

        public static void ExitApplication()
        {
            StopServers();
            if (trayContext != null)
            {
                trayContext.ExitThread();
            }
        }
    }

    public class TrayApplicationContext : ApplicationContext
    {
        private NotifyIcon trayIcon;
        private MainForm mainForm;

        // Static reference for TCP bridge access
        public static MainForm CurrentMainForm { get; set; }

        public TrayApplicationContext()
        {
            try
            {
                InitializeTrayIcon();
                // Show a startup notification
                ShowStartupNotification();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Failed to initialize tray application: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeTrayIcon()
        {
            // Try to load custom fingerprint icon, fallback to system icon
            Icon appIcon = SystemIcons.Application;
            try
            {
                string iconPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "fingerprint.ico");
                if (File.Exists(iconPath))
                {
                    appIcon = new Icon(iconPath);
                }
            }
            catch
            {
                // Use default icon if custom icon fails to load
            }

            // Create the tray icon
            trayIcon = new NotifyIcon()
            {
                Icon = appIcon,
                ContextMenuStrip = CreateContextMenu(),
                Visible = true,
                Text = "Fingerprint Bridge Service - REST API & Console"
            };

            // Handle double-click to open main form
            trayIcon.DoubleClick += TrayIcon_DoubleClick;
        }

        private void ShowStartupNotification()
        {
            if (trayIcon != null)
            {
                string message = "Service started successfully!\nAPI: " + Program.CurrentApiAddress;
                if (Program.CurrentWebDemoServer != null)
                {
                    message += "\nWeb Demo: " + Program.CurrentWebDemoAddress;
                }

                trayIcon.ShowBalloonTip(3000,
                    "Fingerprint Bridge Service",
                    message,
                    ToolTipIcon.Info);
            }
        }

        private ContextMenuStrip CreateContextMenu()
        {
            var contextMenu = new ContextMenuStrip();

            // Main functionality
            var openConsoleItem = new ToolStripMenuItem("Open");
            openConsoleItem.Click += OpenConsole_Click;

            var exitItem = new ToolStripMenuItem("Exit");
            exitItem.Click += Exit_Click;

            var separator = new ToolStripSeparator();

            var aboutItem = new ToolStripMenuItem("About");
            aboutItem.Click += ShowAbout_Click;

            // Add items to context menu
            contextMenu.Items.Add(openConsoleItem);
            contextMenu.Items.Add(exitItem);
            contextMenu.Items.Add(separator);
            contextMenu.Items.Add(aboutItem);

            return contextMenu;
        }

        private void TrayIcon_DoubleClick(object sender, EventArgs e)
        {
            ShowMainForm();
        }

        private void OpenConsole_Click(object sender, EventArgs e)
        {
            ShowMainForm();
        }

        private void ShowMainForm()
        {
            if (mainForm == null || mainForm.IsDisposed)
            {
                mainForm = new MainForm();
                mainForm.FormClosed += MainForm_FormClosed;
                CurrentMainForm = mainForm; // Set static reference for TCP bridge
            }

            if (mainForm.WindowState == FormWindowState.Minimized)
            {
                mainForm.WindowState = FormWindowState.Normal;
            }

            mainForm.Show();
            mainForm.BringToFront();
            mainForm.Activate();
        }

        private void MainForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            // Don't exit the application when main form is closed, just hide it
            mainForm = null;
            CurrentMainForm = null; // Clear static reference
        }

        private void ShowAbout_Click(object sender, EventArgs e)
        {
            ShowCustomAboutDialog();
        }

        private void ShowCustomAboutDialog()
        {
            // Create custom About dialog with better proportions
            Form aboutForm = new Form();
            aboutForm.Text = "About";
            aboutForm.Size = new Size(300, 130);  // Reduced height for better proportions
            aboutForm.StartPosition = FormStartPosition.CenterScreen;
            aboutForm.FormBorderStyle = FormBorderStyle.FixedDialog;
            aboutForm.MaximizeBox = false;
            aboutForm.MinimizeBox = false;
            aboutForm.ShowIcon = false;
            aboutForm.BackColor = Color.White;

            // Create the APIS label with custom colors - larger font
            Label apisLabel = new Label();
            apisLabel.Text = "APIS";
            apisLabel.Font = new Font("Arial", 20, FontStyle.Bold);  // Size 2.5 equivalent (20pt)
            apisLabel.Size = new Size(120, 35);
            apisLabel.Location = new Point(90, 15);  // Reduced top margin
            apisLabel.BackColor = Color.Transparent;

            // Create company label with larger font
            Label companyLabel = new Label();
            companyLabel.Text = "APIS Co. Ltd, All rights reserved";
            companyLabel.Font = new Font("Arial", 9, FontStyle.Regular);  // Size 1.2 equivalent (9pt)
            companyLabel.Size = new Size(220, 25);
            companyLabel.Location = new Point(40, 55);  // Adjusted position
            companyLabel.BackColor = Color.Transparent;
            companyLabel.ForeColor = Color.Black;
            companyLabel.TextAlign = ContentAlignment.MiddleCenter;

            // Add labels to form
            aboutForm.Controls.Add(apisLabel);
            aboutForm.Controls.Add(companyLabel);

            // Custom paint event for colored APIS text with larger spacing
            apisLabel.Paint += (sender, e) =>
            {
                e.Graphics.Clear(Color.White);

                // Draw "A" in bright blue
                using (Brush blueBrush = new SolidBrush(Color.FromArgb(0, 123, 255)))
                {
                    e.Graphics.DrawString("A", apisLabel.Font, blueBrush, new PointF(0, 0));
                }

                // Draw "P" in bright blue (adjusted spacing for larger font)
                using (Brush blueBrush = new SolidBrush(Color.FromArgb(0, 123, 255)))
                {
                    e.Graphics.DrawString("P", apisLabel.Font, blueBrush, new PointF(22, 0));
                }

                // Draw "I" in bright red (adjusted spacing for larger font)
                using (Brush redBrush = new SolidBrush(Color.FromArgb(255, 0, 0)))
                {
                    e.Graphics.DrawString("I", apisLabel.Font, redBrush, new PointF(44, 0));
                }

                // Draw "S" in bright blue (adjusted spacing for larger font)
                using (Brush blueBrush = new SolidBrush(Color.FromArgb(0, 123, 255)))
                {
                    e.Graphics.DrawString("S", apisLabel.Font, blueBrush, new PointF(56, 0));
                }
            };

            // Show the dialog
            aboutForm.ShowDialog();
        }

        private void Exit_Click(object sender, EventArgs e)
        {
            Program.ExitApplication();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (trayIcon != null)
                    trayIcon.Dispose();
                if (mainForm != null)
                    mainForm.Dispose();
            }
            base.Dispose(disposing);
        }
    }

    public class Startup
    {
        public void Configuration(IAppBuilder app)
        {
            var config = new HttpConfiguration();
            Api.WebApiConfig.Register(config);
            app.UseWebApi(config);
        }
    }
}
