<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 5px; padding: 8px 16px; }
        #results { margin-top: 20px; border: 1px solid #ccc; padding: 10px; min-height: 200px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Fingerprint API Test</h1>
    
    <div>
        <button id="checkHealth">Check Health</button>
        <button id="openDevice">Open Device</button>
        <button id="captureFingerprint">Capture Fingerprint</button>
        <button id="deviceInfo">Device Info</button>
    </div>
    
    <div id="results">
        <p>Results will appear here...</p>
    </div>

    <script>
        const apiBaseUrl = 'http://localhost:5001';
        const resultsDiv = document.getElementById('results');
        
        // Add log function
        function log(message, isError = false) {
            const p = document.createElement('p');
            p.textContent = message;
            p.className = isError ? 'error' : 'success';
            resultsDiv.appendChild(p);
            console.log(isError ? '❌' : '✅', message);
        }
        
        // Clear results
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        // API call function
        async function callApi(endpoint, body = null, method = 'GET') {
            clearResults();
            log(`Calling ${method} ${endpoint}...`);
            
            try {
                const response = await fetch(apiBaseUrl + endpoint, {
                    method: method,
                    headers: { 'Content-Type': 'application/json' },
                    body: body ? JSON.stringify(body) : null
                });
                
                const data = await response.json();
                log(`Response status: ${response.status}`);
                log(`Response data: ${JSON.stringify(data, null, 2)}`);
                
                return data;
            } catch (error) {
                log(`Error: ${error.message}`, true);
                throw error;
            }
        }
        
        // Event listeners
        document.getElementById('checkHealth').addEventListener('click', async () => {
            try {
                await callApi('/api/health');
            } catch (error) {
                log(`Health check failed: ${error.message}`, true);
            }
        });
        
        document.getElementById('openDevice').addEventListener('click', async () => {
            try {
                await callApi('/api/device/open', {}, 'POST');
            } catch (error) {
                log(`Open device failed: ${error.message}`, true);
            }
        });
        
        document.getElementById('captureFingerprint').addEventListener('click', async () => {
            try {
                const body = {
                    finger_index: 1,  // Right thumb
                    user_id: 'test_user'
                };
                await callApi('/api/fingerprint/capture-direct', body, 'POST');
            } catch (error) {
                log(`Capture failed: ${error.message}`, true);
            }
        });
        
        document.getElementById('deviceInfo').addEventListener('click', async () => {
            try {
                await callApi('/api/device/info');
            } catch (error) {
                log(`Device info failed: ${error.message}`, true);
            }
        });
    </script>
</body>
</html>