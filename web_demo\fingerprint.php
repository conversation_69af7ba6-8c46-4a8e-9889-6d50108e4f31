<?php
session_start();

// Initialize session variables (mirroring MainForm state exactly)
if (!isset($_SESSION['deviceOpen'])) {
    $_SESSION['deviceOpen'] = false;
}

if (!isset($_SESSION['capturedTemplates'])) {
    $_SESSION['capturedTemplates'] = [];
}

if (!isset($_SESSION['selectedFingers'])) {
    $_SESSION['selectedFingers'] = [];
}

if (!isset($_SESSION['currentTab'])) {
    $_SESSION['currentTab'] = 0; // 0=slaps, 1=rolled, 2=flat
}

if (!isset($_SESSION['deviceInfo'])) {
    $_SESSION['deviceInfo'] = [
        'manufacturer' => 'APIS Co.,LTD.',
        'deviceId' => 'N/A',
        'imageWidth' => 'N/A',
        'imageHeight' => 'N/A',
        'firmwareVersion' => 'N/A',
        'resolution' => 'N/A',
        'serialNumber' => 'N/A'
    ];
}

// API Configuration - ONLY calling existing REST API endpoints (port 9000 from documentation)
$API_BASE_URL = "http://localhost:9000/api/fingerprint";

// Helper function to call existing REST API (no changes to bridge app)
function callAPI($endpoint, $method = 'GET', $data = null) {
    global $API_BASE_URL;

    $url = $API_BASE_URL . '/' . $endpoint;
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            $jsonData = json_encode($data);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            error_log("API Call: $method $url with data: $jsonData");
        }
    } else {
        error_log("API Call: $method $url");
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    error_log("API Response: HTTP $httpCode, Response: $response" . ($curlError ? ", CURL Error: $curlError" : ""));

    if ($httpCode !== 200) {
        return ['success' => false, 'message' => "API call failed: HTTP $httpCode" . ($curlError ? " - $curlError" : "")];
    }

    $decoded = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("JSON decode error: " . json_last_error_msg());
        return ['success' => false, 'message' => 'Invalid JSON response'];
    }

    return $decoded;
}

// Finger mapping (exactly matching MainForm checkboxes and CapFingerPosition enum)
$fingerMapping = [
    'L1' => ['id' => 1, 'label' => 'L1', 'name' => 'Left Thumb', 'capPos' => 1],      // LeftThumb
    'L2' => ['id' => 2, 'label' => 'L2', 'name' => 'Left Index', 'capPos' => 2],     // LeftIndex
    'L3' => ['id' => 3, 'label' => 'L3', 'name' => 'Left Middle', 'capPos' => 3],    // LeftMiddle
    'L4' => ['id' => 4, 'label' => 'L4', 'name' => 'Left Ring', 'capPos' => 4],      // LeftRing
    'L5' => ['id' => 5, 'label' => 'L5', 'name' => 'Left Little', 'capPos' => 5],    // LeftLittle
    'R1' => ['id' => 6, 'label' => 'R1', 'name' => 'Right Thumb', 'capPos' => 6],    // RightThumb
    'R2' => ['id' => 7, 'label' => 'R2', 'name' => 'Right Index', 'capPos' => 7],    // RightIndex
    'R3' => ['id' => 8, 'label' => 'R3', 'name' => 'Right Middle', 'capPos' => 8],   // RightMiddle
    'R4' => ['id' => 9, 'label' => 'R4', 'name' => 'Right Ring', 'capPos' => 9],     // RightRing
    'R5' => ['id' => 10, 'label' => 'R5', 'name' => 'Right Little', 'capPos' => 10], // RightLittle
];

// Slaps mapping for group captures (matching CapFingerPosition enum)
$slapsMapping = [
    'leftfour' => ['id' => 12, 'label' => 'Left 4', 'name' => 'Left Four Fingers', 'fingers' => ['L2', 'L3', 'L4', 'L5']],
    'rightfour' => ['id' => 13, 'label' => 'Right 4', 'name' => 'Right Four Fingers', 'fingers' => ['R2', 'R3', 'R4', 'R5']],
    'twothumbs' => ['id' => 11, 'label' => '2 Thumbs', 'name' => 'Two Thumbs', 'fingers' => ['L1', 'R1']]
];

// Tab mapping (exactly matching MainForm tabControlMain)
$tabMapping = [
    0 => ['name' => 'slaps', 'title' => 'Slaps Fingerprints'],
    1 => ['name' => 'rolled', 'title' => 'Rolled Fingerprints'],
    2 => ['name' => 'flat', 'title' => 'Flat Fingerprints']
];

$message = '';
$messageType = 'info';

// Handle form submissions (calling existing REST API endpoints only)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'openDevice':
            // First check if bridge application is running
            $statusResult = callAPI('status', 'GET');
            if (!$statusResult || isset($statusResult['curl_error'])) {
                $message = 'Bridge application is not running. Please start the MultiFingerDemo.exe application first.';
                $messageType = 'error';
                break;
            }

            // Call existing device/open endpoint
            $result = callAPI('device/open', 'POST');

            // Debug: Show the actual API response
            $debugResponse = json_encode($result, JSON_PRETTY_PRINT);

            if ($result && (isset($result['Success']) && $result['Success']) || (isset($result['success']) && $result['success'])) {
                $_SESSION['deviceOpen'] = true;
                $message = 'Device opened successfully';
                $messageType = 'success';
            } else {
                // Show the full API response for debugging
                $message = 'Failed to open device. API Response: ' . $debugResponse;
                $messageType = 'error';
            }
            break;
            
        case 'closeDevice':
            // Call existing device/close endpoint
            $result = callAPI('device/close', 'POST');
            if ($result && (isset($result['Success']) && $result['Success']) || (isset($result['success']) && $result['success'])) {
                $_SESSION['deviceOpen'] = false;
                $_SESSION['capturedTemplates'] = [];
                $_SESSION['selectedFingers'] = [];
                $message = 'Device closed successfully';
                $messageType = 'success';
            } else {
                $errorMsg = $result['Message'] ?? $result['message'] ?? 'Unknown error';
                $message = 'Failed to close device: ' . $errorMsg;
                $messageType = 'error';
            }
            break;
            
        case 'deviceInfo':
            // Call existing device/info endpoint
            $result = callAPI('device/info', 'GET');
            if ($result && isset($result['success']) && $result['success']) {
                $deviceInfo = $result['data'] ?? $result;
                // Update session device info (mirroring MainForm device info display)
                $_SESSION['deviceInfo'] = [
                    'manufacturer' => $deviceInfo['Manufacturer'] ?? 'APIS Co.,LTD.',
                    'deviceId' => $deviceInfo['DeviceId'] ?? 'N/A',
                    'imageWidth' => $deviceInfo['ImagePixelWidth'] ?? 'N/A',
                    'imageHeight' => $deviceInfo['ImagePixelHeight'] ?? 'N/A',
                    'firmwareVersion' => $deviceInfo['FirmwareVersion'] ?? 'N/A',
                    'resolution' => $deviceInfo['Resolution'] ?? 'N/A',
                    'serialNumber' => $deviceInfo['SerialNumber'] ?? 'N/A'
                ];
                $message = 'Device Info Retrieved';
                $messageType = 'success';
            } else {
                $message = 'Failed to get device info';
                $messageType = 'error';
            }
            break;

        case 'switchTab':
            // Handle tab switching (mirroring MainForm tabControlMain_SelectedIndexChanged)
            $currentTab = (int)($_POST['currentTab'] ?? 0);
            $_SESSION['currentTab'] = $currentTab;
            break;
            
        case 'capture':
            // Call existing capture endpoint (mirroring MainForm capture logic)
            if (!$_SESSION['deviceOpen']) {
                $message = 'Please open device first';
                $messageType = 'error';
                break;
            }

            $selectedFingers = $_POST['selectedFingers'] ?? [];
            $currentTab = $_SESSION['currentTab'] ?? 0;

            if (empty($selectedFingers)) {
                $message = 'Please select at least one finger';
                $messageType = 'error';
                break;
            }

            $_SESSION['selectedFingers'] = $selectedFingers;

            // Note: OperationType will be determined per finger based on tab and finger type
            // Using string operation types as expected by current API

            // Debug: Log capture attempt
            error_log("Capture attempt - Selected fingers: " . implode(', ', $selectedFingers) . ", Tab: $currentTab");

            // Use direct capture endpoint for ALL operations (including slaps)
            $captureResults = [];
            foreach ($selectedFingers as $fingerId) {
                $fingerPos = null;
                $operationType = null;
                $fingerLabel = '';

                // Determine FingerPosition and OperationType based on current tab and finger selection
                if ($currentTab == 0) {
                    // Slaps tab - use slaps mapping
                    if (isset($slapsMapping[$fingerId])) {
                        $fingerPos = $slapsMapping[$fingerId]['id'];
                        $fingerLabel = $slapsMapping[$fingerId]['label'];
                        $operationType = 'slaps'; // Current API expects string
                        error_log("Slaps finger: $fingerId mapped to position $fingerPos, operation $operationType");
                    }
                } else {
                    // Individual finger operations (rolled or flat)
                    if (isset($fingerMapping[$fingerId])) {
                        $fingerPos = $fingerMapping[$fingerId]['id'];
                        $fingerLabel = $fingerMapping[$fingerId]['label'];

                        if ($currentTab == 1) {
                            $operationType = 'rolled';
                        } else {
                            $operationType = 'flat';
                        }
                        error_log("Individual finger: $fingerId mapped to position $fingerPos, operation $operationType");
                    }
                }

                if ($fingerPos === null || $operationType === null) {
                    error_log("Unknown finger ID or invalid operation: $fingerId");
                    continue;
                }

                $captureData = [
                    'FingerPosition' => $fingerPos,
                    'OperationType' => $operationType,
                    'Timeout' => 30,
                    'SaveImage' => true
                ];

                error_log("Calling capture API for finger $fingerId (pos $fingerPos) with data: " . json_encode($captureData));

                $result = callAPI('capture', 'POST', $captureData);

                error_log("Capture API result for $fingerId: " . json_encode($result));
                error_log("Result success check: " . ($result ? 'result exists' : 'no result') .
                         ", success field: " . (isset($result['success']) ? ($result['success'] ? 'true' : 'false') : 'not set'));

                if ($result && isset($result['success']) && $result['success']) {
                    $_SESSION['capturedTemplates'][$fingerId] = [
                        'template' => $result['data']['TemplateData'] ?? '',
                        'image' => $result['data']['ImageData'] ?? '',
                        'quality' => $result['data']['Quality'] ?? 'N/A',
                        'fingerPosition' => $fingerPos,
                        'operationType' => $operationType
                    ];
                    $captureResults[] = $fingerLabel . ' captured successfully';
                } else {
                    $errorMsg = $result['message'] ?? 'Unknown error';
                    $captureResults[] = $fingerLabel . ' failed: ' . $errorMsg;
                    error_log("Capture failed for $fingerId: $errorMsg");
                }
            }

            $message = implode(', ', $captureResults);
            $messageType = empty($captureResults) ? 'error' : 'success';

            error_log("Capture results array: " . json_encode($captureResults));
            error_log("Final capture message: '$message', type: $messageType");
            break;
            
        case 'enroll':
            // Call existing enroll endpoint
            if (!$_SESSION['deviceOpen']) {
                $message = 'Please open device first';
                $messageType = 'error';
                break;
            }
            
            $userId = trim($_POST['userId'] ?? '');
            if (empty($userId)) {
                $message = 'Please input Prisoner ID';
                $messageType = 'error';
                break;
            }
            
            $enrollResults = [];
            foreach ($_SESSION['capturedTemplates'] as $fingerId => $templateData) {
                if (empty($templateData['template'])) continue;
                
                $enrollData = [
                    'UserId' => $userId, // Fixed: capital U
                    'FingerPosition' => $templateData['fingerPosition'], // Fixed: capital F
                    'TemplateData' => $templateData['template'], // Fixed: capital T
                    'ImageData' => $templateData['image'], // Fixed: capital I
                    'ImageQuality' => (int)$templateData['quality'] // Fixed: capital I and Q
                ];
                
                $result = callAPI('enroll', 'POST', $enrollData);
                if ($result && isset($result['success']) && $result['success']) {
                    $enrollResults[] = $fingerMapping[$fingerId]['label'] . ' enrolled';
                } else {
                    $enrollResults[] = $fingerMapping[$fingerId]['label'] . ' enroll failed: ' . ($result['message'] ?? 'Unknown error');
                }
            }
            
            $message = implode(', ', $enrollResults);
            $messageType = count($enrollResults) > 0 ? 'success' : 'error';
            break;
            
        case 'identify':
            // Call existing identify endpoint
            if (!$_SESSION['deviceOpen']) {
                $message = 'Please open device first';
                $messageType = 'error';
                break;
            }
            
            $identifyResults = [];
            foreach ($_SESSION['capturedTemplates'] as $fingerId => $templateData) {
                if (empty($templateData['template'])) continue;

                $identifyData = [
                    'TemplateData' => $templateData['template'] // Fixed: capital T
                ];

                $result = callAPI('identify', 'POST', $identifyData);
                if ($result && isset($result['success']) && $result['success']) {
                    $userId = $result['data']['UserId'] ?? 'Unknown';
                    $score = $result['data']['Score'] ?? 'N/A';
                    $identifyResults[] = $fingerMapping[$fingerId]['label'] . ': ' . $userId . ' (Score: ' . $score . ')';
                } else {
                    $identifyResults[] = $fingerMapping[$fingerId]['label'] . ': No match';
                }
            }
            
            $message = implode(', ', $identifyResults);
            $messageType = 'info';
            break;

        case 'switchTab':
            // Handle tab switching
            $currentTab = intval($_POST['currentTab'] ?? 0);
            $_SESSION['currentTab'] = $currentTab;
            break;
    }
}

$deviceOpen = $_SESSION['deviceOpen'];
$capturedTemplates = $_SESSION['capturedTemplates'];
$currentTab = $_SESSION['currentTab'];
$deviceInfo = $_SESSION['deviceInfo'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APIS TrustFingers</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f0f0; }
        .main-container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; }
        h1 { color: #333; text-align: center; margin-bottom: 20px; font-size: 24px; }

        /* Top Controls Section (mirroring MainForm top area) */
        .top-section { display: flex; gap: 20px; margin-bottom: 20px; }
        .user-info-box { border: 2px solid #ccc; padding: 15px; flex: 1; }
        .user-info-box h3 { margin-top: 0; font-size: 14px; }
        .finger-select-box { border: 2px solid #ccc; padding: 15px; flex: 1; }
        .finger-select-box h3 { margin-top: 0; font-size: 14px; }

        /* Device Controls */
        .device-controls { margin-bottom: 15px; }
        .btn { padding: 8px 16px; margin: 3px; border: 1px solid #ccc; background: #f8f9fa; cursor: pointer; font-size: 12px; }
        .btn:hover { background: #e9ecef; }
        .btn:disabled { background: #6c757d; color: white; cursor: not-allowed; }
        .btn-primary { background: #007bff; color: white; border-color: #007bff; }
        .btn-danger { background: #dc3545; color: white; border-color: #dc3545; }
        .btn-success { background: #28a745; color: white; border-color: #28a745; }
        .btn-info { background: #17a2b8; color: white; border-color: #17a2b8; }

        /* User Input Fields */
        .input-row { margin: 10px 0; }
        .input-row label { display: inline-block; width: 80px; font-size: 12px; }
        .input-row input, .input-row select { padding: 4px; font-size: 12px; width: 200px; }

        /* Finger Selection Grid (exactly like MainForm) */
        .finger-grid { display: grid; grid-template-columns: repeat(5, 1fr); gap: 10px; margin: 15px 0; }
        .finger-item { text-align: center; }
        .finger-item input[type="checkbox"], .finger-item input[type="radio"] { margin-bottom: 5px; }
        .finger-item label { display: block; font-size: 11px; }

        /* Device Info Display */
        .device-info { font-size: 11px; margin: 10px 0; }
        .device-info-row { margin: 3px 0; }
        .device-info-row label { display: inline-block; width: 100px; }

        /* Message Display */
        .message { padding: 8px; margin: 10px 0; border-radius: 3px; font-size: 12px; }
        .message-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .message-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }

        /* Tab Control (exactly like MainForm tabControlMain) */
        .tab-control { margin-top: 20px; border: 1px solid #ccc; }
        .tab-headers { display: flex; background: #f8f9fa; border-bottom: 1px solid #ccc; }
        .tab-header { padding: 8px 16px; border-right: 1px solid #ccc; cursor: pointer; font-size: 12px; background: #f8f9fa; }
        .tab-header.active { background: white; border-bottom: 1px solid white; margin-bottom: -1px; }
        .tab-content { padding: 20px; min-height: 400px; background: white; }

        /* Image Display Areas (like MainForm picture boxes) */
        .image-display { display: flex; gap: 20px; margin: 20px 0; }
        .image-box { text-align: center; flex: 1; }
        .image-box h4 { font-size: 14px; margin-bottom: 10px; }
        .image-preview { width: 300px; height: 250px; border: 2px solid #000; background: white; margin: 0 auto; display: flex; align-items: center; justify-content: center; }
        .image-preview img { max-width: 100%; max-height: 100%; }
        .nfiq-label { font-size: 11px; margin-top: 5px; color: #666; }

        /* Individual Finger Images (for flat/rolled tabs) */
        .finger-images { display: grid; grid-template-columns: repeat(5, 1fr); gap: 15px; margin: 20px 0; }
        .finger-image-box { text-align: center; }
        .finger-image-box h5 { font-size: 11px; margin-bottom: 5px; }
        .finger-image-preview { width: 120px; height: 150px; border: 1px solid #ccc; background: white; margin: 0 auto; display: flex; align-items: center; justify-content: center; }
        .finger-image-preview img { max-width: 100%; max-height: 100%; }
    </style>
</head>
<body>
    <div class="main-container">
        <h1>APIS TrustFingers</h1>

        <!-- Top Section (mirroring MainForm layout) -->
        <div class="top-section">
            <!-- User Info Box (left side) -->
            <div class="user-info-box">
                <h3>User Information</h3>

                <!-- Device Controls -->
                <div class="device-controls">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="<?= $deviceOpen ? 'closeDevice' : 'openDevice' ?>">
                        <button type="submit" class="btn <?= $deviceOpen ? 'btn-primary' : 'btn-primary' ?>">
                            <?= $deviceOpen ? 'Close Device' : 'Open Device' ?>
                        </button>
                    </form>

                    <button type="button" class="btn" onclick="startCapture()" <?= !$deviceOpen ? 'disabled' : '' ?>>
                        Capture
                    </button>

                    <button type="button" class="btn">Clear</button>
                </div>

                <!-- User Input Fields -->
                <div class="input-row">
                    <label>User ID:</label>
                    <input type="text" id="userID_textBox" placeholder="Prisoner ID"
                           value="<?= htmlspecialchars($_POST['userId'] ?? '') ?>">
                </div>

                <div class="input-row">
                    <label>User Name:</label>
                    <input type="text" id="username_textBox" placeholder="User Name">
                </div>

                <!-- Action Buttons -->
                <div style="margin: 15px 0;">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="enroll">
                        <input type="hidden" name="userId" id="enrollUserId">
                        <button type="submit" class="btn btn-success" <?= !$deviceOpen ? 'disabled' : '' ?> onclick="setEnrollUserId()">
                            Enroll
                        </button>
                    </form>

                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="identify">
                        <button type="submit" class="btn btn-info" <?= !$deviceOpen ? 'disabled' : '' ?>>
                            Identify
                        </button>
                    </form>

                    <button type="button" class="btn" style="display: none;">Clear DB</button>
                </div>

                <!-- Device Info Display -->
                <div class="device-info">
                    <div class="device-info-row">
                        <label>Manufacturer:</label>
                        <span><?= htmlspecialchars($deviceInfo['manufacturer']) ?></span>
                    </div>
                    <div class="device-info-row">
                        <label>Device ID:</label>
                        <span><?= htmlspecialchars($deviceInfo['deviceId']) ?></span>
                    </div>
                    <div class="device-info-row">
                        <label>Image Width:</label>
                        <span><?= htmlspecialchars($deviceInfo['imageWidth']) ?></span>
                    </div>
                    <div class="device-info-row">
                        <label>Image Height:</label>
                        <span><?= htmlspecialchars($deviceInfo['imageHeight']) ?></span>
                    </div>
                    <div class="device-info-row">
                        <label>FW Version:</label>
                        <span><?= htmlspecialchars($deviceInfo['firmwareVersion']) ?></span>
                    </div>
                    <div class="device-info-row">
                        <label>Image DPI:</label>
                        <span><?= htmlspecialchars($deviceInfo['resolution']) ?></span>
                    </div>
                    <div class="device-info-row">
                        <label>S/N:</label>
                        <span><?= htmlspecialchars($deviceInfo['serialNumber']) ?></span>
                    </div>
                </div>
            </div>

            <!-- Finger Select Box (right side) -->
            <div class="finger-select-box">
                <h3>Finger Select</h3>

                <!-- Slaps Selection (only visible when Slaps tab is active) -->
                <div id="slaps-selection" style="margin-bottom: 20px; <?= $currentTab !== 0 ? 'display: none;' : '' ?>">
                    <div style="display: flex; gap: 15px; flex-wrap: wrap; justify-content: center;">
                        <div class="finger-item">
                            <input type="checkbox" id="leftfour_checkBox" name="selectedFingers[]" value="leftfour"
                                   <?= !$deviceOpen ? 'disabled' : '' ?>
                                   onchange="handleFingerSelection('leftfour', this.checked)">
                            <label for="leftfour_checkBox">Left 4</label>
                        </div>
                        <div class="finger-item">
                            <input type="checkbox" id="rightfour_checkBox" name="selectedFingers[]" value="rightfour"
                                   <?= !$deviceOpen ? 'disabled' : '' ?>
                                   onchange="handleFingerSelection('rightfour', this.checked)">
                            <label for="rightfour_checkBox">Right 4</label>
                        </div>
                        <div class="finger-item">
                            <input type="checkbox" id="twothumbs_checkBox" name="selectedFingers[]" value="twothumbs"
                                   <?= !$deviceOpen ? 'disabled' : '' ?>
                                   onchange="handleFingerSelection('twothumbs', this.checked)">
                            <label for="twothumbs_checkBox">2 Thumbs</label>
                        </div>
                    </div>
                </div>

                <!-- Individual Finger Selection Grid (visible for rolled/flat tabs) -->
                <div id="individual-selection" style="margin-bottom: 20px; <?= $currentTab === 0 ? 'display: none;' : '' ?>">
                    <div class="finger-grid">
                        <?php foreach ($fingerMapping as $fingerId => $fingerInfo): ?>
                            <div class="finger-item">
                                <input type="checkbox" id="<?= $fingerId ?>_checkBox" name="selectedFingers[]" value="<?= $fingerId ?>"
                                       <?= !$deviceOpen ? 'disabled' : '' ?>
                                       onchange="handleFingerSelection('<?= $fingerId ?>', this.checked)">
                                <label for="<?= $fingerId ?>_checkBox"><?= $fingerInfo['label'] ?></label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Hand Images (like MainForm pictureBox4 and pictureBox5) -->
                <div style="display: flex; justify-content: space-between; margin-top: 20px;">
                    <img src="left_hand.png" alt="Left Hand" style="width: 150px;">
                    <img src="right_hand.png" alt="Right Hand" style="width: 150px;">
                </div>
            </div>
        </div>

        <!-- Message Display -->
        <?php if (!empty($message)): ?>
            <div class="message message-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Tab Control (exactly like MainForm tabControlMain) -->
        <div class="tab-control">
            <div class="tab-headers">
                <div class="tab-header <?= $currentTab === 0 ? 'active' : '' ?>" onclick="switchTab(0)">
                    Slaps Fingerprints
                </div>
                <div class="tab-header <?= $currentTab === 1 ? 'active' : '' ?>" onclick="switchTab(1)">
                    Rolled Fingerprints
                </div>
                <div class="tab-header <?= $currentTab === 2 ? 'active' : '' ?>" onclick="switchTab(2)">
                    Flat Fingerprints
                </div>
            </div>

            <!-- Slaps Tab Content (like MainForm slaps_tabPage) -->
            <div class="tab-content" id="tab-content-0" style="<?= $currentTab !== 0 ? 'display: none;' : '' ?>">
                <!-- Image Display Areas (like MainForm picture boxes) -->
                <div class="image-display">
                    <div class="image-box">
                        <h4>Left Four Fingers</h4>
                        <div class="image-preview" id="leftFourPreview">
                            <?php if (isset($capturedTemplates['leftfour']) && !empty($capturedTemplates['leftfour']['image'])): ?>
                                <img src="data:image/bmp;base64,<?= $capturedTemplates['leftfour']['image'] ?>" alt="Left Four Fingers">
                            <?php else: ?>
                                <span style="color: #ccc;">No Image</span>
                            <?php endif; ?>
                        </div>
                        <div class="nfiq-label">NFIQ=N/A</div>
                    </div>

                    <div class="image-box">
                        <h4>Two Thumbs</h4>
                        <div class="image-preview" id="twoThumbsPreview">
                            <?php if (isset($capturedTemplates['twothumbs']) && !empty($capturedTemplates['twothumbs']['image'])): ?>
                                <img src="data:image/bmp;base64,<?= $capturedTemplates['twothumbs']['image'] ?>" alt="Two Thumbs">
                            <?php else: ?>
                                <span style="color: #ccc;">No Image</span>
                            <?php endif; ?>
                        </div>
                        <div class="nfiq-label">NFIQ=N/A</div>
                    </div>

                    <div class="image-box">
                        <h4>Right Four Fingers</h4>
                        <div class="image-preview" id="rightFourPreview">
                            <?php if (isset($capturedTemplates['rightfour']) && !empty($capturedTemplates['rightfour']['image'])): ?>
                                <img src="data:image/bmp;base64,<?= $capturedTemplates['rightfour']['image'] ?>" alt="Right Four Fingers">
                            <?php else: ?>
                                <span style="color: #ccc;">No Image</span>
                            <?php endif; ?>
                        </div>
                        <div class="nfiq-label">NFIQ=N/A</div>
                    </div>
                </div>
            </div>

            <!-- Rolled Tab Content (like MainForm rolled_tabPage) -->
            <div class="tab-content" id="tab-content-1" style="<?= $currentTab !== 1 ? 'display: none;' : '' ?>">
                <!-- Individual Finger Images (like MainForm rolled finger picture boxes) -->
                <div class="finger-images">
                    <?php foreach ($fingerMapping as $fingerId => $fingerInfo): ?>
                        <div class="finger-image-box">
                            <h5><?= $fingerInfo['name'] ?></h5>
                            <div class="finger-image-preview" id="rolled_<?= $fingerId ?>_preview">
                                <?php if (isset($capturedTemplates[$fingerId]) && !empty($capturedTemplates[$fingerId]['image'])): ?>
                                    <img src="data:image/bmp;base64,<?= $capturedTemplates[$fingerId]['image'] ?>" alt="<?= $fingerInfo['name'] ?>">
                                <?php else: ?>
                                    <span style="color: #ccc; font-size: 10px;">No Image</span>
                                <?php endif; ?>
                            </div>
                            <div class="nfiq-label">NFIQ=N/A</div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Flat Tab Content (like MainForm flat_tabPage) -->
            <div class="tab-content" id="tab-content-2" style="<?= $currentTab !== 2 ? 'display: none;' : '' ?>">
                <!-- Individual Finger Images (like MainForm flat finger picture boxes) -->
                <div class="finger-images">
                    <?php foreach ($fingerMapping as $fingerId => $fingerInfo): ?>
                        <div class="finger-image-box">
                            <h5><?= $fingerInfo['name'] ?></h5>
                            <div class="finger-image-preview" id="flat_<?= $fingerId ?>_preview">
                                <?php if (isset($capturedTemplates[$fingerId]) && !empty($capturedTemplates[$fingerId]['image'])): ?>
                                    <img src="data:image/bmp;base64,<?= $capturedTemplates[$fingerId]['image'] ?>" alt="<?= $fingerInfo['name'] ?>">
                                <?php else: ?>
                                    <span style="color: #ccc; font-size: 10px;">No Image</span>
                                <?php endif; ?>
                            </div>
                            <div class="nfiq-label">NFIQ=N/A</div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        </div>
    </div>

    <script>
        // Tab switching (mirroring MainForm tabControlMain_SelectedIndexChanged)
        function switchTab(tabIndex) {
            // Hide all tab contents
            for (let i = 0; i < 3; i++) {
                const content = document.getElementById('tab-content-' + i);
                if (content) content.style.display = 'none';
            }

            // Remove active class from all tab headers
            document.querySelectorAll('.tab-header').forEach(header => header.classList.remove('active'));

            // Show selected tab content
            const selectedContent = document.getElementById('tab-content-' + tabIndex);
            if (selectedContent) selectedContent.style.display = 'block';

            // Add active class to selected tab header
            event.target.classList.add('active');

            // Update finger selection based on active tab
            const slapsSelection = document.getElementById('slaps-selection');
            const individualSelection = document.getElementById('individual-selection');

            if (tabIndex === 0) {
                // Slaps tab - show only Left 4, Right 4, 2 Thumbs
                slapsSelection.style.display = 'block';
                individualSelection.style.display = 'none';
                // Clear individual finger selections
                clearIndividualFingerSelections();
            } else {
                // Rolled or Flat tab - show individual fingers
                slapsSelection.style.display = 'none';
                individualSelection.style.display = 'block';
                // Clear slaps selections
                clearSlapsSelections();
            }

            // Update session via AJAX
            fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=switchTab&currentTab=' + tabIndex
            });
        }

        // Helper function to clear individual finger selections
        function clearIndividualFingerSelections() {
            <?php foreach ($fingerMapping as $fingerId => $fingerInfo): ?>
                const checkbox_<?= $fingerId ?> = document.getElementById('<?= $fingerId ?>_checkBox');
                if (checkbox_<?= $fingerId ?>) checkbox_<?= $fingerId ?>.checked = false;
            <?php endforeach; ?>
        }

        // Helper function to clear slaps selections
        function clearSlapsSelections() {
            const leftfour = document.getElementById('leftfour_checkBox');
            const rightfour = document.getElementById('rightfour_checkBox');
            const twothumbs = document.getElementById('twothumbs_checkBox');
            if (leftfour) leftfour.checked = false;
            if (rightfour) rightfour.checked = false;
            if (twothumbs) twothumbs.checked = false;
        }

        // Finger selection handling (mirroring MainForm checkbox logic)
        function handleFingerSelection(fingerId, isChecked) {
            const currentTab = <?= $currentTab ?>;

            if (currentTab === 1) { // Rolled tab - only one finger at a time (like MainForm)
                if (isChecked) {
                    // Uncheck all other fingers
                    <?php foreach ($fingerMapping as $fId => $fInfo): ?>
                        if ('<?= $fId ?>' !== fingerId) {
                            const checkbox = document.getElementById('<?= $fId ?>_checkBox');
                            if (checkbox) checkbox.checked = false;
                        }
                    <?php endforeach; ?>
                }
            }
        }

        // Start capture (mirroring MainForm start_button_Click)
        function startCapture() {
            const currentTab = <?= $currentTab ?>;
            const selectedFingers = [];

            if (currentTab === 0) {
                // Slaps tab - check slaps selections
                const leftfour = document.getElementById('leftfour_checkBox');
                const rightfour = document.getElementById('rightfour_checkBox');
                const twothumbs = document.getElementById('twothumbs_checkBox');

                if (leftfour && leftfour.checked) selectedFingers.push('leftfour');
                if (rightfour && rightfour.checked) selectedFingers.push('rightfour');
                if (twothumbs && twothumbs.checked) selectedFingers.push('twothumbs');
            } else {
                // Rolled/Flat tabs - check individual finger selections
                <?php foreach ($fingerMapping as $fingerId => $fingerInfo): ?>
                    const checkbox_<?= $fingerId ?> = document.getElementById('<?= $fingerId ?>_checkBox');
                    if (checkbox_<?= $fingerId ?> && checkbox_<?= $fingerId ?>.checked) {
                        selectedFingers.push('<?= $fingerId ?>');
                    }
                <?php endforeach; ?>
            }

            if (selectedFingers.length === 0) {
                if (currentTab === 0) {
                    alert('Please select at least one option: Left 4, Right 4, or 2 Thumbs');
                } else {
                    alert('Please select at least one finger');
                }
                return;
            }

            // Create form and submit (operation type determined server-side per finger)
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="capture">
                ${selectedFingers.map(f => `<input type="hidden" name="selectedFingers[]" value="${f}">`).join('')}
            `;
            document.body.appendChild(form);
            form.submit();
        }

        // Set enroll user ID (helper function)
        function setEnrollUserId() {
            const userId = document.getElementById('userID_textBox').value;
            document.getElementById('enrollUserId').value = userId;
        }
    </script>
</body>
</html>
