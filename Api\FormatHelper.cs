using Aratek.TrustFinger;
using System;

namespace MultiFingerDemo.Api
{
    public class FormatHelper
    {
        public static FingerPosition ConvertToFingerPosition(string name)
        {
            FingerPosition position = FingerPosition.UnKnow;
            Enum.TryParse<FingerPosition>(name, out position);
            return position;
        }

        public static string FormatFingerprintPosition(FingerPosition position)
        {
            string desc = string.Empty;

            switch (position)
            {
                case FingerPosition.UnKnow:
                    break;
                case FingerPosition.LeftIndexFinger:
                    desc = "Left Index Finger";
                    break;
                case FingerPosition.LeftLittleFinger:
                    desc = "Left Little Finger";
                    break;
                case FingerPosition.LeftMiddleFinger:
                    desc = "Left Middle Finger";
                    break;
                case FingerPosition.LeftRingFinger:
                    desc = "Left Ring Finger";
                    break;
                case FingerPosition.LeftThumb:
                    desc = "Left Thumb";
                    break;
                case FingerPosition.RightIndexFinger:
                    desc = "Right Index Finger";
                    break;
                case FingerPosition.RightLittleFinger:
                    desc = "Right Little Finger";
                    break;
                case FingerPosition.RightMiddleFinger:
                    desc = "Right Middle Finger";
                    break;
                case FingerPosition.RightRingFinger:
                    desc = "Right Ring Finger";
                    break;
                case FingerPosition.RightThumb:
                    desc = "Right Thumb";
                    break;
            }

            return desc;
        }
    }
}