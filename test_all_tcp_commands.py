#!/usr/bin/env python3
"""
Complete TCP Commands Test
Tests all TCP socket commands including the new device management commands
"""

import sys
import time
import os

# Add python_client to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'python_client'))

from tcp_client import Tcp<PERSON>lient, TcpClientError

def test_connection():
    """Test basic TCP connection"""
    print("\n🔌 Testing TCP Connection...")
    try:
        client = TcpClient()
        if client.test_connection():
            print("   ✅ TCP connection successful")
            return True
        else:
            print("   ❌ TCP connection failed")
            return False
    except Exception as e:
        print(f"   ❌ Connection error: {e}")
        return False

def test_device_management():
    """Test all device management commands"""
    print("\n📱 Testing Device Management Commands...")
    
    try:
        client = TcpClient()
        
        # Test device info (initial state)
        print("   📊 Getting initial device status...")
        info = client.get_device_info()
        print(f"      Status: {info.get('status', 'Unknown')}")
        
        # Test open device
        print("   🔓 Opening device...")
        result = client.open_device()
        print(f"      Result: {result.get('status', 'Unknown')}")
        if 'device_name' in result:
            print(f"      Device: {result['device_name']} (ID: {result.get('device_id', 'Unknown')})")
        
        # Test device info (after open)
        print("   📊 Getting device status after open...")
        info = client.get_device_info()
        print(f"      Status: {info.get('status', 'Unknown')}")
        
        # Test close device
        print("   🔒 Closing device...")
        result = client.close_device()
        print(f"      Result: {result.get('status', 'Unknown')}")
        
        # Test device info (after close)
        print("   📊 Getting device status after close...")
        info = client.get_device_info()
        print(f"      Status: {info.get('status', 'Unknown')}")
        
        return True
        
    except TcpClientError as e:
        print(f"   ❌ Device management error: {e}")
        return False

def test_fingerprint_operations():
    """Test fingerprint operations (requires device to be open)"""
    print("\n👆 Testing Fingerprint Operations...")
    
    try:
        client = TcpClient()
        
        # Ensure device is open
        print("   🔓 Ensuring device is open...")
        client.open_device()
        
        # Test capture
        print("   📸 Testing capture (will timeout - no actual finger)...")
        try:
            result = client.capture_fingerprint("TEST_USER", 2)  # Right Index
            print(f"      Capture result: {result.get('status', 'Unknown')}")
        except TcpClientError as e:
            if "timeout" in str(e).lower() or "no finger" in str(e).lower():
                print("      Expected timeout (no finger placed)")
            else:
                print(f"      Capture error: {e}")
        
        # Test enroll (will fail - no captured data)
        print("   💾 Testing enroll (will fail - no captured data)...")
        try:
            result = client.enroll_fingerprint("TEST_USER")
            print(f"      Enroll result: {result}")
        except TcpClientError as e:
            print(f"      Expected enroll error: {e}")
        
        # Test verify (will fail - no captured data)
        print("   🔍 Testing verify (will fail - no captured data)...")
        try:
            result = client.verify_fingerprint("TEST_USER", 2)
            print(f"      Verify result: {result}")
        except TcpClientError as e:
            print(f"      Expected verify error: {e}")
        
        # Test identify (will fail - no captured data)
        print("   🔎 Testing identify (will fail - no captured data)...")
        try:
            result = client.identify_fingerprint()
            print(f"      Identify result: {result}")
        except TcpClientError as e:
            print(f"      Expected identify error: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Fingerprint operations error: {e}")
        return False

def main():
    """Run comprehensive TCP commands test"""
    print("🧪 Complete TCP Commands Test")
    print("=" * 60)
    print("Testing all TCP socket commands including new device management")
    print("=" * 60)
    
    # Test sequence
    tests = [
        ("TCP Connection", test_connection),
        ("Device Management", test_device_management),
        ("Fingerprint Operations", test_fingerprint_operations),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
            if success:
                print(f"   ✅ {test_name} - PASSED")
            else:
                print(f"   ❌ {test_name} - FAILED")
        except Exception as e:
            print(f"   💥 {test_name} - EXCEPTION: {e}")
            results.append((test_name, False))
        
        # Small delay between tests
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} - {test_name}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All TCP commands working!")
        print("\n💡 Available TCP Commands:")
        print("   • OPEN / OPEN_DEVICE - Open fingerprint device")
        print("   • CLOSE / CLOSE_DEVICE - Close fingerprint device") 
        print("   • STATUS / DEVICE_INFO - Get device information")
        print("   • CAPTURE <person_id> <finger_index> - Capture fingerprint")
        print("   • ENROLL <person_id> - Enroll captured fingerprints")
        print("   • VERIFY <person_id> <finger_index> - 1:1 verification")
        print("   • IDENTIFY - 1:N identification")
        print("   • MATCH - Alias for IDENTIFY")
    else:
        print("⚠️ Some tests failed. Check the C# bridge application.")
        print("\n🔧 Troubleshooting:")
        print("   1. Make sure C# bridge application is running")
        print("   2. Check that TCP server is listening on port 8123")
        print("   3. Verify A900 device is connected via USB")

if __name__ == "__main__":
    main()
