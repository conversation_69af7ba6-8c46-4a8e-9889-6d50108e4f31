using System;
using System.IO;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Linq;
using System.Collections.Generic;

namespace MultiFingerDemo.WebDemo
{
    public class WebServer
    {
        private readonly HttpListener listener;
        private readonly string webRoot;
        private bool isRunning;

        public WebServer(string prefix, string webRoot)
        {
            this.webRoot = webRoot;
            listener = new HttpListener();
            listener.Prefixes.Add(prefix);
        }

        public void Start()
        {
            if (!isRunning)
            {
                listener.Start();
                isRunning = true;
                Task.Factory.StartNew(() => HandleRequests(), TaskCreationOptions.LongRunning);
                var prefixes = new List<string>();
                foreach (var prefix in listener.Prefixes)
                {
                    prefixes.Add(prefix);
                }
                Console.WriteLine("Web demo server running at " + prefixes.First());
            }
        }

        public void Stop()
        {
            if (isRunning)
            {
                listener.Stop();
                isRunning = false;
            }
        }

        private async Task HandleRequests()
        {
            while (isRunning)
            {
                try
                {
                    var context = await listener.GetContextAsync();
                    ProcessRequest(context);
                }
                catch (HttpListenerException)
                {
                    // Listener was stopped
                    break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Error handling request: " + ex.Message);
                }
            }
        }

        private void ProcessRequest(HttpListenerContext context)
        {
            try
            {
                string filename = context.Request.Url.AbsolutePath;
                if (filename == "/")
                    filename = "/index.html";

                filename = filename.TrimStart('/');
                
                // Security check - prevent directory traversal
                if (filename.Contains("..") || filename.Contains("\\") || filename.Contains(":"))
                {
                    context.Response.StatusCode = 400;
                    WriteErrorResponse(context, "Invalid file path");
                    return;
                }

                string path = Path.Combine(webRoot, filename);

                if (!File.Exists(path))
                {
                    context.Response.StatusCode = 404;
                    WriteErrorResponse(context, "File not found");
                    return;
                }

                string contentType = GetContentType(Path.GetExtension(path));
                byte[] buffer = File.ReadAllBytes(path);

                context.Response.ContentType = contentType;
                context.Response.ContentLength64 = buffer.Length;
                context.Response.OutputStream.Write(buffer, 0, buffer.Length);
            }
            catch (Exception ex)
            {
                context.Response.StatusCode = 500;
                WriteErrorResponse(context, "Internal server error: " + ex.Message);
            }
            finally
            {
                try
                {
                    context.Response.Close();
                }
                catch
                {
                    // Ignore errors during response close
                }
            }
        }

        private void WriteErrorResponse(HttpListenerContext context, string message)
        {
            try
            {
                byte[] buffer = Encoding.UTF8.GetBytes(message);
                context.Response.ContentType = "text/plain";
                context.Response.ContentLength64 = buffer.Length;
                context.Response.OutputStream.Write(buffer, 0, buffer.Length);
            }
            catch
            {
                // Ignore errors during error response
            }
        }

        private string GetContentType(string extension)
        {
            switch (extension.ToLower())
            {
                case ".html":
                    return "text/html";
                case ".js":
                    return "application/javascript";
                case ".css":
                    return "text/css";
                case ".png":
                    return "image/png";
                case ".jpg":
                case ".jpeg":
                    return "image/jpeg";
                default:
                    return "application/octet-stream";
            }
        }
    }
} 