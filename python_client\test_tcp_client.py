#!/usr/bin/env python3
"""
Simple TCP client to test the C# TCP Socket Server
This will be the foundation for the Python REST API
"""

import socket
import json
import time
import sys

class TcpClient:
    def __init__(self, host='localhost', port=8123):
        self.host = host
        self.port = port
        
    def send_message_text(self, command_line):
        """Send a text command to the TCP server and return the response"""
        try:
            # Create socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)  # 10 second timeout

            # Connect to server
            sock.connect((self.host, self.port))

            # Send command (text-based protocol)
            sock.send((command_line + '\n').encode('utf-8'))

            # Receive response (may be multiple lines)
            response_lines = []
            sock_file = sock.makefile('r')

            # Read all response lines until connection closes or timeout
            try:
                while True:
                    line = sock_file.readline()
                    if not line:
                        break
                    response_lines.append(line.strip())
            except socket.timeout:
                pass  # Expected when server closes connection

            sock_file.close()
            sock.close()

            return '\n'.join(response_lines)

        except Exception as e:
            print(f"Error sending message: {e}")
            return None

def test_status():
    """Test connection (MainForm.cs doesn't have STATUS command)"""
    print("Testing connection to MainForm.cs TCP bridge...")
    client = TcpClient()

    # Test with a simple command to see if server responds
    response = client.send_message_text("IDENTIFY")

    if response:
        print(f"Response: {response}")
        return "OK" in response or "✅" in response or "ERROR" in response  # Any response means connection works
    else:
        print("No response received")
        return False

def test_capture():
    """Test the capture command"""
    print("\nTesting CAPTURE command...")
    client = TcpClient()

    # MainForm.cs expects: CAPTURE <person_id> <finger_index>
    command = "CAPTURE USER001 1"

    response = client.send_message_text(command)

    if response:
        print(f"Response: {response}")
        return "OK" in response or "✅" in response
    else:
        print("No response received")
        return False

def test_identify():
    """Test the identify command"""
    print("\nTesting IDENTIFY command...")
    client = TcpClient()

    # MainForm.cs expects: IDENTIFY (no parameters)
    response = client.send_message_text("IDENTIFY")

    if response:
        print(f"Response: {response}")
        return "OK" in response or "✅" in response
    else:
        print("No response received")
        return False

def test_enroll():
    """Test the enroll command"""
    print("\nTesting ENROLL command...")
    client = TcpClient()

    # MainForm.cs expects: ENROLL <person_id>
    response = client.send_message_text("ENROLL TEST001")

    if response:
        print(f"Response: {response}")
        return "OK" in response or "✅" in response
    else:
        print("No response received")
        return False

def test_verify():
    """Test the verify command"""
    print("\nTesting VERIFY command...")
    client = TcpClient()

    # MainForm.cs expects: VERIFY <person_id> <finger_index>
    response = client.send_message_text("VERIFY TEST001 1")

    if response:
        print(f"Response: {response}")
        return "OK" in response or "✅" in response or "ERROR" in response  # Any response is good
    else:
        print("No response received")
        return False

def test_invalid_command():
    """Test an invalid command"""
    print("\nTesting INVALID command...")
    client = TcpClient()
    response = client.send_message_text("INVALID_COMMAND")

    if response:
        print(f"Response: {response}")
        # Should return error for invalid command
        return "ERROR" in response
    else:
        print("No response received")
        return False

def main():
    """Run all tests"""
    print("TCP Socket Server Test Client")
    print("=" * 40)
    
    tests = [
        ("Connection Test", test_status),
        ("Capture", test_capture),
        ("Identify", test_identify),
        ("Enroll", test_enroll),
        ("Verify", test_verify),
        ("Invalid Command", test_invalid_command)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✓ {test_name}: {'PASS' if result else 'FAIL'}")
        except Exception as e:
            print(f"✗ {test_name}: ERROR - {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # Small delay between tests
    
    print("\n" + "=" * 40)
    print("Test Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("All tests passed! TCP server is working correctly.")
        return 0
    else:
        print("Some tests failed. Check the server and try again.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
