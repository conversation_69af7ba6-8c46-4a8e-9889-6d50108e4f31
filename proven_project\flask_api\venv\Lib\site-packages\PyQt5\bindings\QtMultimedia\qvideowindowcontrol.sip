// qvideowindowcontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QVideoWindowControl : public QMediaControl
{
%TypeHeaderCode
#include <qvideowindowcontrol.h>
%End

public:
    virtual ~QVideoWindowControl();
    virtual WId winId() const = 0;
    virtual void setWinId(WId id) = 0;
    virtual QRect displayRect() const = 0;
    virtual void setDisplayRect(const QRect &rect) = 0;
    virtual bool isFullScreen() const = 0;
    virtual void setFullScreen(bool fullScreen) = 0;
    virtual void repaint() = 0;
    virtual QSize nativeSize() const = 0;
    virtual Qt::AspectRatioMode aspectRatioMode() const = 0;
    virtual void setAspectRatioMode(Qt::AspectRatioMode mode) = 0;
    virtual int brightness() const = 0;
    virtual void setBrightness(int brightness) = 0;
    virtual int contrast() const = 0;
    virtual void setContrast(int contrast) = 0;
    virtual int hue() const = 0;
    virtual void setHue(int hue) = 0;
    virtual int saturation() const = 0;
    virtual void setSaturation(int saturation) = 0;

signals:
    void fullScreenChanged(bool fullScreen);
    void brightnessChanged(int brightness);
    void contrastChanged(int contrast);
    void hueChanged(int hue);
    void saturationChanged(int saturation);
    void nativeSizeChanged();

protected:
    explicit QVideoWindowControl(QObject *parent /TransferThis/ = 0);
};
