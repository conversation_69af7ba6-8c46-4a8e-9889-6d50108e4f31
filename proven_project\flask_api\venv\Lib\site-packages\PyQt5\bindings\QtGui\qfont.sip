// qfont.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFont
{
%TypeHeaderCode
#include <qfont.h>
%End

public:
    enum StyleHint
    {
        Helvetica,
        SansSerif,
        Times,
        Serif,
        Courier,
        TypeWriter,
        OldEnglish,
        Decorative,
        System,
        AnyStyle,
        Cursive,
        Monospace,
        Fantasy,
    };

    enum StyleStrategy
    {
        PreferDefault,
        PreferBitmap,
        PreferDevice,
        PreferOutline,
        ForceOutline,
        PreferMatch,
        PreferQuality,
        PreferAntialias,
        NoAntialias,
%If (Qt_5_4_0 -)
        NoSubpixelAntialias,
%End
        OpenGLCompatible,
        NoFontMerging,
        ForceIntegerMetrics,
%If (Qt_5_10_0 -)
        PreferNoShaping,
%End
    };

    enum Weight
    {
%If (Qt_5_5_0 -)
        Thin,
%End
%If (Qt_5_5_0 -)
        ExtraLight,
%End
        Light,
        Normal,
%If (Qt_5_5_0 -)
        Medium,
%End
        DemiBold,
        Bold,
%If (Qt_5_5_0 -)
        ExtraBold,
%End
        Black,
    };

    enum Style
    {
        StyleNormal,
        StyleItalic,
        StyleOblique,
    };

    enum Stretch
    {
%If (Qt_5_8_0 -)
        AnyStretch,
%End
        UltraCondensed,
        ExtraCondensed,
        Condensed,
        SemiCondensed,
        Unstretched,
        SemiExpanded,
        Expanded,
        ExtraExpanded,
        UltraExpanded,
    };

    QFont();
    QFont(const QString &family, int pointSize = -1, int weight = -1, bool italic = false);
    QFont(const QFont &, QPaintDevice *pd);
    QFont(const QFont &);
    QFont(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QFont>())
            sipCpp = new QFont(a0->value<QFont>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    ~QFont();
    QString family() const;
    void setFamily(const QString &);
    int pointSize() const;
    void setPointSize(int);
    qreal pointSizeF() const;
    void setPointSizeF(qreal);
    int pixelSize() const;
    void setPixelSize(int);
    int weight() const;
    void setWeight(int);
    void setStyle(QFont::Style style);
    QFont::Style style() const;
    bool underline() const;
    void setUnderline(bool);
    bool overline() const;
    void setOverline(bool);
    bool strikeOut() const;
    void setStrikeOut(bool);
    bool fixedPitch() const;
    void setFixedPitch(bool);
    bool kerning() const;
    void setKerning(bool);
    QFont::StyleHint styleHint() const;
    QFont::StyleStrategy styleStrategy() const;
    void setStyleHint(QFont::StyleHint hint, QFont::StyleStrategy strategy = QFont::PreferDefault);
    void setStyleStrategy(QFont::StyleStrategy s);
    int stretch() const;
    void setStretch(int);
    bool rawMode() const;
    void setRawMode(bool);
    bool exactMatch() const;
    bool operator==(const QFont &) const;
    bool operator!=(const QFont &) const;
    bool operator<(const QFont &) const;
    bool isCopyOf(const QFont &) const;
    void setRawName(const QString &);
    QString rawName() const;
    QString key() const;
    QString toString() const;
    bool fromString(const QString &);
    static QString substitute(const QString &);
    static QStringList substitutes(const QString &);
    static QStringList substitutions();
    static void insertSubstitution(const QString &, const QString &);
    static void insertSubstitutions(const QString &, const QStringList &);
    static void removeSubstitutions(const QString &);
    static void initialize();
    static void cleanup();
    static void cacheStatistics();
    QString defaultFamily() const;
    QString lastResortFamily() const;
    QString lastResortFont() const;
    QFont resolve(const QFont &) const;
    bool bold() const;
    void setBold(bool enable);
    bool italic() const;
    void setItalic(bool b);

    enum Capitalization
    {
        MixedCase,
        AllUppercase,
        AllLowercase,
        SmallCaps,
        Capitalize,
    };

    enum SpacingType
    {
        PercentageSpacing,
        AbsoluteSpacing,
    };

    qreal letterSpacing() const;
    QFont::SpacingType letterSpacingType() const;
    void setLetterSpacing(QFont::SpacingType type, qreal spacing);
    qreal wordSpacing() const;
    void setWordSpacing(qreal spacing);
    void setCapitalization(QFont::Capitalization);
    QFont::Capitalization capitalization() const;

    enum HintingPreference
    {
        PreferDefaultHinting,
        PreferNoHinting,
        PreferVerticalHinting,
        PreferFullHinting,
    };

    QString styleName() const;
    void setStyleName(const QString &styleName);
    void setHintingPreference(QFont::HintingPreference hintingPreference);
    QFont::HintingPreference hintingPreference() const;
    void swap(QFont &other /Constrained/);
%If (Qt_5_3_0 -)
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

%End
%If (Qt_5_13_0 -)
    QStringList families() const;
%End
%If (Qt_5_13_0 -)
    void setFamilies(const QStringList &);
%End
};

QDataStream &operator<<(QDataStream &, const QFont & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QFont & /Constrained/) /ReleaseGIL/;
