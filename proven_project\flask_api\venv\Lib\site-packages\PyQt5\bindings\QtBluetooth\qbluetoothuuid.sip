// qbluetoothuuid.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QBluetoothUuid : public QUuid
{
%TypeHeaderCode
#include <qbluetoothuuid.h>
%End

public:
    enum ProtocolUuid
    {
        Sdp,
        Udp,
        Rfcomm,
        Tcp,
        TcsBin,
        TcsAt,
        Att,
        Obex,
        Ip,
        Ftp,
        Http,
        Wsp,
        Bnep,
        Upnp,
        Hidp,
        HardcopyControlChannel,
        HardcopyDataChannel,
        HardcopyNotification,
        Avctp,
        Avdtp,
        Cmtp,
        UdiCPlain,
        McapControlChannel,
        McapDataChannel,
        L2cap,
    };

    enum ServiceClassUuid
    {
        ServiceDiscoveryServer,
        BrowseGroupDescriptor,
        PublicBrowseGroup,
        SerialPort,
        LANAccessUsingPPP,
        DialupNetworking,
        IrMCSync,
        ObexObjectPush,
        OBEXFileTransfer,
        IrMCSyncCommand,
        Headset,
        AudioSource,
        AudioSink,
        AV_RemoteControlTarget,
        AdvancedAudioDistribution,
        AV_RemoteControl,
        AV_RemoteControlController,
        HeadsetAG,
        PANU,
        NAP,
        GN,
        DirectPrinting,
        ReferencePrinting,
        ImagingResponder,
        ImagingAutomaticArchive,
        ImagingReferenceObjects,
        Handsfree,
        HandsfreeAudioGateway,
        DirectPrintingReferenceObjectsService,
        ReflectedUI,
        BasicPrinting,
        PrintingStatus,
        HumanInterfaceDeviceService,
        HardcopyCableReplacement,
        HCRPrint,
        HCRScan,
        SIMAccess,
        PhonebookAccessPCE,
        PhonebookAccessPSE,
        PhonebookAccess,
        HeadsetHS,
        MessageAccessServer,
        MessageNotificationServer,
        MessageAccessProfile,
        PnPInformation,
        GenericNetworking,
        GenericFileTransfer,
        GenericAudio,
        GenericTelephony,
        VideoSource,
        VideoSink,
        VideoDistribution,
        HDP,
        HDPSource,
        HDPSink,
%If (Qt_5_3_0 -)
        BasicImage,
%End
%If (Qt_5_3_0 -)
        GNSS,
%End
%If (Qt_5_3_0 -)
        GNSSServer,
%End
%If (Qt_5_3_0 -)
        Display3D,
%End
%If (Qt_5_3_0 -)
        Glasses3D,
%End
%If (Qt_5_3_0 -)
        Synchronization3D,
%End
%If (Qt_5_3_0 -)
        MPSProfile,
%End
%If (Qt_5_3_0 -)
        MPSService,
%End
%If (Qt_5_4_0 -)
        GenericAccess,
%End
%If (Qt_5_4_0 -)
        GenericAttribute,
%End
%If (Qt_5_4_0 -)
        ImmediateAlert,
%End
%If (Qt_5_4_0 -)
        LinkLoss,
%End
%If (Qt_5_4_0 -)
        TxPower,
%End
%If (Qt_5_4_0 -)
        CurrentTimeService,
%End
%If (Qt_5_4_0 -)
        ReferenceTimeUpdateService,
%End
%If (Qt_5_4_0 -)
        NextDSTChangeService,
%End
%If (Qt_5_4_0 -)
        Glucose,
%End
%If (Qt_5_4_0 -)
        HealthThermometer,
%End
%If (Qt_5_4_0 -)
        DeviceInformation,
%End
%If (Qt_5_4_0 -)
        HeartRate,
%End
%If (Qt_5_4_0 -)
        PhoneAlertStatusService,
%End
%If (Qt_5_4_0 -)
        BatteryService,
%End
%If (Qt_5_4_0 -)
        BloodPressure,
%End
%If (Qt_5_4_0 -)
        AlertNotificationService,
%End
%If (Qt_5_4_0 -)
        HumanInterfaceDevice,
%End
%If (Qt_5_4_0 -)
        ScanParameters,
%End
%If (Qt_5_4_0 -)
        RunningSpeedAndCadence,
%End
%If (Qt_5_4_0 -)
        CyclingSpeedAndCadence,
%End
%If (Qt_5_4_0 -)
        CyclingPower,
%End
%If (Qt_5_4_0 -)
        LocationAndNavigation,
%End
%If (Qt_5_5_0 -)
        EnvironmentalSensing,
%End
%If (Qt_5_5_0 -)
        BodyComposition,
%End
%If (Qt_5_5_0 -)
        UserData,
%End
%If (Qt_5_5_0 -)
        WeightScale,
%End
%If (Qt_5_5_0 -)
        BondManagement,
%End
%If (Qt_5_5_0 -)
        ContinuousGlucoseMonitoring,
%End
    };

%If (Qt_5_4_0 -)

    enum CharacteristicType
    {
        DeviceName,
        Appearance,
        PeripheralPrivacyFlag,
        ReconnectionAddress,
        PeripheralPreferredConnectionParameters,
        ServiceChanged,
        AlertLevel,
        TxPowerLevel,
        DateTime,
        DayOfWeek,
        DayDateTime,
        ExactTime256,
        DSTOffset,
        TimeZone,
        LocalTimeInformation,
        TimeWithDST,
        TimeAccuracy,
        TimeSource,
        ReferenceTimeInformation,
        TimeUpdateControlPoint,
        TimeUpdateState,
        GlucoseMeasurement,
        BatteryLevel,
        TemperatureMeasurement,
        TemperatureType,
        IntermediateTemperature,
        MeasurementInterval,
        BootKeyboardInputReport,
        SystemID,
        ModelNumberString,
        SerialNumberString,
        FirmwareRevisionString,
        HardwareRevisionString,
        SoftwareRevisionString,
        ManufacturerNameString,
        IEEE1107320601RegulatoryCertificationDataList,
        CurrentTime,
        ScanRefresh,
        BootKeyboardOutputReport,
        BootMouseInputReport,
        GlucoseMeasurementContext,
        BloodPressureMeasurement,
        IntermediateCuffPressure,
        HeartRateMeasurement,
        BodySensorLocation,
        HeartRateControlPoint,
        AlertStatus,
        RingerControlPoint,
        RingerSetting,
        AlertCategoryIDBitMask,
        AlertCategoryID,
        AlertNotificationControlPoint,
        UnreadAlertStatus,
        NewAlert,
        SupportedNewAlertCategory,
        SupportedUnreadAlertCategory,
        BloodPressureFeature,
        HIDInformation,
        ReportMap,
        HIDControlPoint,
        Report,
        ProtocolMode,
        ScanIntervalWindow,
        PnPID,
        GlucoseFeature,
        RecordAccessControlPoint,
        RSCMeasurement,
        RSCFeature,
        SCControlPoint,
        CSCMeasurement,
        CSCFeature,
        SensorLocation,
        CyclingPowerMeasurement,
        CyclingPowerVector,
        CyclingPowerFeature,
        CyclingPowerControlPoint,
        LocationAndSpeed,
        Navigation,
        PositionQuality,
        LNFeature,
        LNControlPoint,
%If (Qt_5_5_0 -)
        MagneticDeclination,
%End
%If (Qt_5_5_0 -)
        Elevation,
%End
%If (Qt_5_5_0 -)
        Pressure,
%End
%If (Qt_5_5_0 -)
        Temperature,
%End
%If (Qt_5_5_0 -)
        Humidity,
%End
%If (Qt_5_5_0 -)
        TrueWindSpeed,
%End
%If (Qt_5_5_0 -)
        TrueWindDirection,
%End
%If (Qt_5_5_0 -)
        ApparentWindSpeed,
%End
%If (Qt_5_5_0 -)
        ApparentWindDirection,
%End
%If (Qt_5_5_0 -)
        GustFactor,
%End
%If (Qt_5_5_0 -)
        PollenConcentration,
%End
%If (Qt_5_5_0 -)
        UVIndex,
%End
%If (Qt_5_5_0 -)
        Irradiance,
%End
%If (Qt_5_5_0 -)
        Rainfall,
%End
%If (Qt_5_5_0 -)
        WindChill,
%End
%If (Qt_5_5_0 -)
        HeatIndex,
%End
%If (Qt_5_5_0 -)
        DewPoint,
%End
%If (Qt_5_5_0 -)
        DescriptorValueChanged,
%End
%If (Qt_5_5_0 -)
        AerobicHeartRateLowerLimit,
%End
%If (Qt_5_5_0 -)
        AerobicThreshold,
%End
%If (Qt_5_5_0 -)
        Age,
%End
%If (Qt_5_5_0 -)
        AnaerobicHeartRateLowerLimit,
%End
%If (Qt_5_5_0 -)
        AnaerobicHeartRateUpperLimit,
%End
%If (Qt_5_5_0 -)
        AnaerobicThreshold,
%End
%If (Qt_5_5_0 -)
        AerobicHeartRateUpperLimit,
%End
%If (Qt_5_5_0 -)
        DateOfBirth,
%End
%If (Qt_5_5_0 -)
        DateOfThresholdAssessment,
%End
%If (Qt_5_5_0 -)
        EmailAddress,
%End
%If (Qt_5_5_0 -)
        FatBurnHeartRateLowerLimit,
%End
%If (Qt_5_5_0 -)
        FatBurnHeartRateUpperLimit,
%End
%If (Qt_5_5_0 -)
        FirstName,
%End
%If (Qt_5_5_0 -)
        FiveZoneHeartRateLimits,
%End
%If (Qt_5_5_0 -)
        Gender,
%End
%If (Qt_5_5_0 -)
        HeartRateMax,
%End
%If (Qt_5_5_0 -)
        Height,
%End
%If (Qt_5_5_0 -)
        HipCircumference,
%End
%If (Qt_5_5_0 -)
        LastName,
%End
%If (Qt_5_5_0 -)
        MaximumRecommendedHeartRate,
%End
%If (Qt_5_5_0 -)
        RestingHeartRate,
%End
%If (Qt_5_5_0 -)
        SportTypeForAerobicAnaerobicThresholds,
%End
%If (Qt_5_5_0 -)
        ThreeZoneHeartRateLimits,
%End
%If (Qt_5_5_0 -)
        TwoZoneHeartRateLimits,
%End
%If (Qt_5_5_0 -)
        VO2Max,
%End
%If (Qt_5_5_0 -)
        WaistCircumference,
%End
%If (Qt_5_5_0 -)
        Weight,
%End
%If (Qt_5_5_0 -)
        DatabaseChangeIncrement,
%End
%If (Qt_5_5_0 -)
        UserIndex,
%End
%If (Qt_5_5_0 -)
        BodyCompositionFeature,
%End
%If (Qt_5_5_0 -)
        BodyCompositionMeasurement,
%End
%If (Qt_5_5_0 -)
        WeightMeasurement,
%End
%If (Qt_5_5_0 -)
        WeightScaleFeature,
%End
%If (Qt_5_5_0 -)
        UserControlPoint,
%End
%If (Qt_5_5_0 -)
        MagneticFluxDensity2D,
%End
%If (Qt_5_5_0 -)
        MagneticFluxDensity3D,
%End
%If (Qt_5_5_0 -)
        Language,
%End
%If (Qt_5_5_0 -)
        BarometricPressureTrend,
%End
    };

%End
%If (Qt_5_4_0 -)

    enum DescriptorType
    {
        UnknownDescriptorType,
        CharacteristicExtendedProperties,
        CharacteristicUserDescription,
        ClientCharacteristicConfiguration,
        ServerCharacteristicConfiguration,
        CharacteristicPresentationFormat,
        CharacteristicAggregateFormat,
        ValidRange,
        ExternalReportReference,
        ReportReference,
%If (Qt_5_5_0 -)
        EnvironmentalSensingConfiguration,
%End
%If (Qt_5_5_0 -)
        EnvironmentalSensingMeasurement,
%End
%If (Qt_5_5_0 -)
        EnvironmentalSensingTriggerSetting,
%End
    };

%End
    QBluetoothUuid();
    QBluetoothUuid(QBluetoothUuid::ProtocolUuid uuid /Constrained/);
    QBluetoothUuid(QBluetoothUuid::ServiceClassUuid uuid /Constrained/);
%If (Qt_5_4_0 -)
    QBluetoothUuid(QBluetoothUuid::CharacteristicType uuid /Constrained/);
%End
%If (Qt_5_4_0 -)
    QBluetoothUuid(QBluetoothUuid::DescriptorType uuid /Constrained/);
%End
    explicit QBluetoothUuid(quint32 uuid);
    explicit QBluetoothUuid(quint128 uuid);
    explicit QBluetoothUuid(const QString &uuid);
    QBluetoothUuid(const QBluetoothUuid &uuid);
    QBluetoothUuid(const QUuid &uuid);
    ~QBluetoothUuid();
    bool operator==(const QBluetoothUuid &other) const;
%If (Qt_5_7_0 -)
    bool operator!=(const QBluetoothUuid &other) const;
%End
    int minimumSize() const;
    quint16 toUInt16(bool *ok = 0) const;
    quint32 toUInt32(bool *ok = 0) const;
    quint128 toUInt128() const;
%If (Qt_5_4_0 -)
    static QString serviceClassToString(QBluetoothUuid::ServiceClassUuid uuid);
%End
%If (Qt_5_4_0 -)
    static QString protocolToString(QBluetoothUuid::ProtocolUuid uuid);
%End
%If (Qt_5_4_0 -)
    static QString characteristicToString(QBluetoothUuid::CharacteristicType uuid);
%End
%If (Qt_5_4_0 -)
    static QString descriptorToString(QBluetoothUuid::DescriptorType uuid);
%End
};

%End
%If (Qt_5_12_0 -)
QDataStream &operator<<(QDataStream &s, const QBluetoothUuid &uuid /Constrained/) /ReleaseGIL/;
%End
%If (Qt_5_12_0 -)
QDataStream &operator>>(QDataStream &s, QBluetoothUuid &uuid /Constrained/) /ReleaseGIL/;
%End
