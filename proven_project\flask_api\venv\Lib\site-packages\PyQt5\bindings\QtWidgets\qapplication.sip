// qapplication.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>ENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


// QApplication *qApp
QApplication *qApp {
%AccessCode
    // Qt implements this has a #define to a function call so we have to handle
    // it like this.
    return qApp;
%End
};
typedef QList<QWidget *> QWidgetList;

class QApplication : public QGuiApplication
{
%TypeHeaderCode
#include <qapplication.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QKeyEventTransition, &sipType_QKeyEventTransition, -1, 1},
        {sipName_QUndoStack, &sipType_QUndoStack, -1, 2},
        {sipName_QAbstractItemDelegate, &sipType_QAbstractItemDelegate, 26, 3},
        {sipName_QGraphicsTransform, &sipType_QGraphicsTransform, 28, 4},
        {sipName_QCompleter, &sipType_QCompleter, -1, 5},
        {sipName_QActionGroup, &sipType_QActionGroup, -1, 6},
        {sipName_QShortcut, &sipType_QShortcut, -1, 7},
        {sipName_QButtonGroup, &sipType_QButtonGroup, -1, 8},
        {sipName_QPlainTextDocumentLayout, &sipType_QPlainTextDocumentLayout, -1, 9},
        {sipName_QGraphicsAnchor, &sipType_QGraphicsAnchor, -1, 10},
        {sipName_QFileSystemModel, &sipType_QFileSystemModel, -1, 11},
        {sipName_QGesture, &sipType_QGesture, 30, 12},
        {sipName_QApplication, &sipType_QApplication, -1, 13},
        {sipName_QLayout, &sipType_QLayout, 35, 14},
        {sipName_QDirModel, &sipType_QDirModel, -1, 15},
        {sipName_QDataWidgetMapper, &sipType_QDataWidgetMapper, -1, 16},
        {sipName_QGraphicsObject, &sipType_QGraphicsObject, 41, 17},
        {sipName_QScroller, &sipType_QScroller, -1, 18},
        {sipName_QGraphicsScene, &sipType_QGraphicsScene, -1, 19},
        {sipName_QUndoGroup, &sipType_QUndoGroup, -1, 20},
        {sipName_QMouseEventTransition, &sipType_QMouseEventTransition, -1, 21},
        {sipName_QGraphicsEffect, &sipType_QGraphicsEffect, 44, 22},
        {sipName_QWidget, &sipType_QWidget, 48, 23},
        {sipName_QStyle, &sipType_QStyle, 122, 24},
        {sipName_QAction, &sipType_QAction, 124, 25},
        {sipName_QSystemTrayIcon, &sipType_QSystemTrayIcon, -1, -1},
        {sipName_QStyledItemDelegate, &sipType_QStyledItemDelegate, -1, 27},
        {sipName_QItemDelegate, &sipType_QItemDelegate, -1, -1},
        {sipName_QGraphicsRotation, &sipType_QGraphicsRotation, -1, 29},
        {sipName_QGraphicsScale, &sipType_QGraphicsScale, -1, -1},
        {sipName_QSwipeGesture, &sipType_QSwipeGesture, -1, 31},
        {sipName_QTapGesture, &sipType_QTapGesture, -1, 32},
        {sipName_QPanGesture, &sipType_QPanGesture, -1, 33},
        {sipName_QPinchGesture, &sipType_QPinchGesture, -1, 34},
        {sipName_QTapAndHoldGesture, &sipType_QTapAndHoldGesture, -1, -1},
        {sipName_QStackedLayout, &sipType_QStackedLayout, -1, 36},
        {sipName_QFormLayout, &sipType_QFormLayout, -1, 37},
        {sipName_QBoxLayout, &sipType_QBoxLayout, 39, 38},
        {sipName_QGridLayout, &sipType_QGridLayout, -1, -1},
        {sipName_QVBoxLayout, &sipType_QVBoxLayout, -1, 40},
        {sipName_QHBoxLayout, &sipType_QHBoxLayout, -1, -1},
        {sipName_QGraphicsWidget, &sipType_QGraphicsWidget, 43, 42},
        {sipName_QGraphicsTextItem, &sipType_QGraphicsTextItem, -1, -1},
        {sipName_QGraphicsProxyWidget, &sipType_QGraphicsProxyWidget, -1, -1},
        {sipName_QGraphicsBlurEffect, &sipType_QGraphicsBlurEffect, -1, 45},
        {sipName_QGraphicsColorizeEffect, &sipType_QGraphicsColorizeEffect, -1, 46},
        {sipName_QGraphicsDropShadowEffect, &sipType_QGraphicsDropShadowEffect, -1, 47},
        {sipName_QGraphicsOpacityEffect, &sipType_QGraphicsOpacityEffect, -1, -1},
        {sipName_QDockWidget, &sipType_QDockWidget, -1, 49},
        {sipName_QProgressBar, &sipType_QProgressBar, -1, 50},
    #if QT_VERSION >= 0x050400 && defined(SIP_FEATURE_PyQt_OpenGL)
        {sipName_QOpenGLWidget, &sipType_QOpenGLWidget, -1, 51},
    #else
        {0, 0, -1, 51},
    #endif
        {sipName_QAbstractButton, &sipType_QAbstractButton, 78, 52},
        {sipName_QSplashScreen, &sipType_QSplashScreen, -1, 53},
        {sipName_QMenuBar, &sipType_QMenuBar, -1, 54},
        {sipName_QStatusBar, &sipType_QStatusBar, -1, 55},
        {sipName_QMenu, &sipType_QMenu, -1, 56},
        {sipName_QMdiSubWindow, &sipType_QMdiSubWindow, -1, 57},
        {sipName_QTabWidget, &sipType_QTabWidget, -1, 58},
    #if defined(Q_OS_MAC) && defined(SIP_FEATURE_PyQt_MacOSXOnly)
        {sipName_QMacCocoaViewContainer, &sipType_QMacCocoaViewContainer, -1, 59},
    #else
        {0, 0, -1, 59},
    #endif
        {sipName_QLineEdit, &sipType_QLineEdit, -1, 60},
        {sipName_QFrame, &sipType_QFrame, 83, 61},
        {sipName_QComboBox, &sipType_QComboBox, 105, 62},
        {sipName_QRubberBand, &sipType_QRubberBand, -1, 63},
        {sipName_QAbstractSpinBox, &sipType_QAbstractSpinBox, 106, 64},
        {sipName_QDialog, &sipType_QDialog, 111, 65},
    #if QT_VERSION >= 0x050200
        {sipName_QKeySequenceEdit, &sipType_QKeySequenceEdit, -1, 66},
    #else
        {0, 0, -1, 66},
    #endif
        {sipName_QAbstractSlider, &sipType_QAbstractSlider, 119, 67},
        {sipName_QWizardPage, &sipType_QWizardPage, -1, 68},
        {sipName_QDesktopWidget, &sipType_QDesktopWidget, -1, 69},
        {sipName_QDialogButtonBox, &sipType_QDialogButtonBox, -1, 70},
        {sipName_QToolBar, &sipType_QToolBar, -1, 71},
        {sipName_QSizeGrip, &sipType_QSizeGrip, -1, 72},
        {sipName_QSplitterHandle, &sipType_QSplitterHandle, -1, 73},
        {sipName_QTabBar, &sipType_QTabBar, -1, 74},
        {sipName_QGroupBox, &sipType_QGroupBox, -1, 75},
        {sipName_QMainWindow, &sipType_QMainWindow, -1, 76},
        {sipName_QCalendarWidget, &sipType_QCalendarWidget, -1, 77},
        {sipName_QFocusFrame, &sipType_QFocusFrame, -1, -1},
        {sipName_QCheckBox, &sipType_QCheckBox, -1, 79},
        {sipName_QRadioButton, &sipType_QRadioButton, -1, 80},
        {sipName_QPushButton, &sipType_QPushButton, 82, 81},
        {sipName_QToolButton, &sipType_QToolButton, -1, -1},
        {sipName_QCommandLinkButton, &sipType_QCommandLinkButton, -1, -1},
        {sipName_QAbstractScrollArea, &sipType_QAbstractScrollArea, 89, 84},
        {sipName_QToolBox, &sipType_QToolBox, -1, 85},
        {sipName_QSplitter, &sipType_QSplitter, -1, 86},
        {sipName_QStackedWidget, &sipType_QStackedWidget, -1, 87},
        {sipName_QLabel, &sipType_QLabel, -1, 88},
        {sipName_QLCDNumber, &sipType_QLCDNumber, -1, -1},
        {sipName_QScrollArea, &sipType_QScrollArea, -1, 90},
        {sipName_QPlainTextEdit, &sipType_QPlainTextEdit, -1, 91},
        {sipName_QGraphicsView, &sipType_QGraphicsView, -1, 92},
        {sipName_QAbstractItemView, &sipType_QAbstractItemView, 95, 93},
        {sipName_QTextEdit, &sipType_QTextEdit, 104, 94},
        {sipName_QMdiArea, &sipType_QMdiArea, -1, -1},
        {sipName_QHeaderView, &sipType_QHeaderView, -1, 96},
        {sipName_QTableView, &sipType_QTableView, 100, 97},
        {sipName_QListView, &sipType_QListView, 101, 98},
        {sipName_QTreeView, &sipType_QTreeView, 103, 99},
        {sipName_QColumnView, &sipType_QColumnView, -1, -1},
        {sipName_QTableWidget, &sipType_QTableWidget, -1, -1},
        {sipName_QListWidget, &sipType_QListWidget, -1, 102},
        {sipName_QUndoView, &sipType_QUndoView, -1, -1},
        {sipName_QTreeWidget, &sipType_QTreeWidget, -1, -1},
        {sipName_QTextBrowser, &sipType_QTextBrowser, -1, -1},
        {sipName_QFontComboBox, &sipType_QFontComboBox, -1, -1},
        {sipName_QDateTimeEdit, &sipType_QDateTimeEdit, 109, 107},
        {sipName_QSpinBox, &sipType_QSpinBox, -1, 108},
        {sipName_QDoubleSpinBox, &sipType_QDoubleSpinBox, -1, -1},
        {sipName_QDateEdit, &sipType_QDateEdit, -1, 110},
        {sipName_QTimeEdit, &sipType_QTimeEdit, -1, -1},
        {sipName_QFontDialog, &sipType_QFontDialog, -1, 112},
        {sipName_QErrorMessage, &sipType_QErrorMessage, -1, 113},
        {sipName_QMessageBox, &sipType_QMessageBox, -1, 114},
        {sipName_QProgressDialog, &sipType_QProgressDialog, -1, 115},
        {sipName_QColorDialog, &sipType_QColorDialog, -1, 116},
        {sipName_QFileDialog, &sipType_QFileDialog, -1, 117},
        {sipName_QInputDialog, &sipType_QInputDialog, -1, 118},
        {sipName_QWizard, &sipType_QWizard, -1, -1},
        {sipName_QDial, &sipType_QDial, -1, 120},
        {sipName_QScrollBar, &sipType_QScrollBar, -1, 121},
        {sipName_QSlider, &sipType_QSlider, -1, -1},
        {sipName_QCommonStyle, &sipType_QCommonStyle, 123, -1},
        {sipName_QProxyStyle, &sipType_QProxyStyle, -1, -1},
        {sipName_QWidgetAction, &sipType_QWidgetAction, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QApplication(SIP_PYLIST argv /TypeHint="List[str]"/) /PostHook=__pyQtQAppHook__/ [(int &argc, char **argv, int = ApplicationFlags)];
%MethodCode
        // The Python interface is a list of argument strings that is modified.
        
        int argc;
        char **argv;
        
        // Convert the list.
        if ((argv = pyqt5_qtwidgets_from_argv_list(a0, argc)) == NULL)
            sipIsErr = 1;
        else
        {
            // Create it now the arguments are right.
            static int nargc;
            nargc = argc;
        
            Py_BEGIN_ALLOW_THREADS
            sipCpp = new sipQApplication(nargc, argv, QCoreApplication::ApplicationFlags);
            Py_END_ALLOW_THREADS
        
            // Now modify the original list.
            pyqt5_qtwidgets_update_argv_list(a0, argc, argv);
        }
%End

    virtual ~QApplication() /ReleaseGIL/;
%MethodCode
        pyqt5_qtwidgets_cleanup_qobjects();
%End

    static QStyle *style();
    static void setStyle(QStyle * /Transfer/);
    static QStyle *setStyle(const QString &);

    enum ColorSpec
    {
        NormalColor,
        CustomColor,
        ManyColor,
    };

    static int colorSpec();
    static void setColorSpec(int);
    static QPalette palette();
    static QPalette palette(const QWidget *);
    static QPalette palette(const char *className);
    static void setPalette(const QPalette &, const char *className = 0);
    static QFont font();
    static QFont font(const QWidget *);
    static QFont font(const char *className);
    static void setFont(const QFont &, const char *className = 0);
    static QFontMetrics fontMetrics();
    static void setWindowIcon(const QIcon &icon);
    static QIcon windowIcon();
    static QWidgetList allWidgets();
    static QWidgetList topLevelWidgets();
    static QDesktopWidget *desktop();
    static QWidget *activePopupWidget();
    static QWidget *activeModalWidget();
    static QWidget *focusWidget();
    static QWidget *activeWindow();
    static void setActiveWindow(QWidget *act);
    static QWidget *widgetAt(const QPoint &p);
    static QWidget *widgetAt(int x, int y);
    static QWidget *topLevelAt(const QPoint &p);
    static QWidget *topLevelAt(int x, int y);
    static void beep();
    static void alert(QWidget *widget, int msecs = 0) /ReleaseGIL/;
    static void setCursorFlashTime(int);
    static int cursorFlashTime();
    static void setDoubleClickInterval(int);
    static int doubleClickInterval();
    static void setKeyboardInputInterval(int);
    static int keyboardInputInterval();
    static void setWheelScrollLines(int);
    static int wheelScrollLines();
    static void setGlobalStrut(const QSize &);
    static QSize globalStrut();
    static void setStartDragTime(int ms);
    static int startDragTime();
    static void setStartDragDistance(int l);
    static int startDragDistance();
    static bool isEffectEnabled(Qt::UIEffect);
    static void setEffectEnabled(Qt::UIEffect, bool enabled = true);
    static int exec() /PostHook=__pyQtPostEventLoopHook__,PreHook=__pyQtPreEventLoopHook__,PyName=exec_,ReleaseGIL/;
%If (Py_v3)
    static int exec() /PostHook=__pyQtPostEventLoopHook__,PreHook=__pyQtPreEventLoopHook__,ReleaseGIL/;
%End
    virtual bool notify(QObject *, QEvent *) /ReleaseGIL/;
    bool autoSipEnabled() const;
    QString styleSheet() const;

signals:
    void focusChanged(QWidget *old, QWidget *now);

public slots:
    static void aboutQt();
    static void closeAllWindows();
    void setAutoSipEnabled(const bool enabled);
    void setStyleSheet(const QString &sheet);

protected:
    virtual bool event(QEvent *);
};

%ModuleHeaderCode
// Imports from QtCore.
typedef void (*pyqt5_qtwidgets_cleanup_qobjects_t)();
extern pyqt5_qtwidgets_cleanup_qobjects_t pyqt5_qtwidgets_cleanup_qobjects;

typedef char **(*pyqt5_qtwidgets_from_argv_list_t)(PyObject *, int &);
extern pyqt5_qtwidgets_from_argv_list_t pyqt5_qtwidgets_from_argv_list;

typedef sipErrorState (*pyqt5_qtwidgets_get_connection_parts_t)(PyObject *, QObject *, const char *, bool, QObject **, QByteArray &);
extern pyqt5_qtwidgets_get_connection_parts_t pyqt5_qtwidgets_get_connection_parts;

typedef sipErrorState (*pyqt5_qtwidgets_get_signal_signature_t)(PyObject *, QObject *, QByteArray &);
extern pyqt5_qtwidgets_get_signal_signature_t pyqt5_qtwidgets_get_signal_signature;

typedef void (*pyqt5_qtwidgets_update_argv_list_t)(PyObject *, int, char **);
extern pyqt5_qtwidgets_update_argv_list_t pyqt5_qtwidgets_update_argv_list;

// This is needed for Qt v5.0.0.
#if defined(B0)
#undef B0
#endif
%End

%ModuleCode
#include "qpywidgets_api.h"

// Imports from QtCore.
pyqt5_qtwidgets_cleanup_qobjects_t pyqt5_qtwidgets_cleanup_qobjects;
pyqt5_qtwidgets_from_argv_list_t pyqt5_qtwidgets_from_argv_list;
pyqt5_qtwidgets_get_connection_parts_t pyqt5_qtwidgets_get_connection_parts;
pyqt5_qtwidgets_get_signal_signature_t pyqt5_qtwidgets_get_signal_signature;
pyqt5_qtwidgets_update_argv_list_t pyqt5_qtwidgets_update_argv_list;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt5_qtwidgets_cleanup_qobjects = (pyqt5_qtwidgets_cleanup_qobjects_t)sipImportSymbol("pyqt5_cleanup_qobjects");
Q_ASSERT(pyqt5_qtwidgets_cleanup_qobjects);

pyqt5_qtwidgets_from_argv_list = (pyqt5_qtwidgets_from_argv_list_t)sipImportSymbol("pyqt5_from_argv_list");
Q_ASSERT(pyqt5_qtwidgets_from_argv_list);

pyqt5_qtwidgets_get_connection_parts = (pyqt5_qtwidgets_get_connection_parts_t)sipImportSymbol("pyqt5_get_connection_parts");
Q_ASSERT(pyqt5_qtwidgets_get_connection_parts);

pyqt5_qtwidgets_get_signal_signature = (pyqt5_qtwidgets_get_signal_signature_t)sipImportSymbol("pyqt5_get_signal_signature");
Q_ASSERT(pyqt5_qtwidgets_get_signal_signature);

pyqt5_qtwidgets_update_argv_list = (pyqt5_qtwidgets_update_argv_list_t)sipImportSymbol("pyqt5_update_argv_list");
Q_ASSERT(pyqt5_qtwidgets_update_argv_list);

qpywidgets_post_init();
%End
