# this file gets copied to wx/lib/pubsub folder when release to wxPython

For wxPython users:

The code in this wx/lib/pubsub folder is taken verbatim from the PyPubSub
project on SourceForge.net. Pubsub originated as a wxPython lib, but it is now
a standalone project on SourceForge. It is included as part of wxPython
distribution for convenience to wxPython users, but pubsub can also be installed
standalone (see installation notes at http://pypubsub.sourceforge.net), or you
can also overwrite the version in this folder with the standalone version or
put an SVN checkout of pubsub in this folder, etc.

Note that the source distribution on SF.net tends to be updated more often than
the copy in wx/lib/pubsub. If you wish to install pubsub standalone, there are
instructions on the Install page of http://pypubsub.sourceforge.net.

There is extensive documentation for pubsub at http://pypubsub.sourceforge.net,
and some examples are in wx/lib/pubsub/examples. The WxPython wiki also discusses
usage of pubsub in wxPython.

<PERSON>
December 2013
