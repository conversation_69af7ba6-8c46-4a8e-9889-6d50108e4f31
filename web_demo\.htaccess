# Disable strict Content Security Policy for MultiFinger web interface
# This allows modern JavaScript features needed for the fingerprint application

# Remove any existing CSP headers
Header unset Content-Security-Policy
Header unset X-Content-Security-Policy
Header unset X-WebKit-CSP

# Set permissive CSP that allows all JavaScript features
<PERSON><PERSON> always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' http: https:; style-src 'self' 'unsafe-inline' http: https:; img-src 'self' data: blob: http: https:; connect-src 'self' http: https: ws: wss:; font-src 'self' data: http: https:;"

# Alternative: Completely disable CSP (uncomment the line below if the above doesn't work)
# Header unset Content-Security-Policy
