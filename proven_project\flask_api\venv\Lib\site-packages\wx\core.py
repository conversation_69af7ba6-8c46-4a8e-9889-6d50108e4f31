# This file is generated by wxPython's SIP generator.  Do not edit by hand.
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License

"""
The classes in this module are the most commonly used classes for wxPython,
which is why they have been made visible in the core `wx` namespace.
Everything you need for building typical GUI applications is here.
"""

from ._core import *

#-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=
# This code block was included from src/core_ex.py
import sys as _sys

# Load version numbers from __version__ and some other initialization tasks...
if 'wxEVT_NULL' in dir():
    from wx.__version__ import *
    import wx._core
    __version__ = VERSION_STRING

    # Add the build type to PlatformInfo
    PlatformInfo = PlatformInfo + ('build-type: ' + BUILD_TYPE, )

    # Register a function to be called when Python terminates that will clean
    # up and release all system resources that wxWidgets allocated.
    import atexit
    atexit.register(wx._core._wxPyCleanup)
    del atexit

else:
    Port = ''
    Platform = ''
    PlatformInfo = []

# A little trick to make 'wx' be a reference to this module so wx.Names can
# be used in the python code here.
wx = _sys.modules[__name__]


import warnings
class wxPyDeprecationWarning(DeprecationWarning):
    pass

warnings.simplefilter('default', wxPyDeprecationWarning)
del warnings


def deprecated(item, msg='', useName=False):
    """
    Create a delegating wrapper that raises a deprecation warning.  Can be
    used with callable objects (functions, methods, classes) or with
    properties.
    """
    import warnings

    name = ''
    if useName:
        try:
            name = ' ' + item.__name__
        except AttributeError:
            pass

    if isinstance(item, type):
        # It is a class.  Make a subclass that raises a warning.
        class DeprecatedClassProxy(item):
            def __init__(*args, **kw):
                warnings.warn("Using deprecated class%s. %s" % (name, msg),
                          wxPyDeprecationWarning, stacklevel=2)
                item.__init__(*args, **kw)
        DeprecatedClassProxy.__name__ = item.__name__
        return DeprecatedClassProxy

    elif callable(item):
        # wrap a new function around the callable
        def deprecated_func(*args, **kw):
            warnings.warn("Call to deprecated item%s. %s" % (name, msg),
                          wxPyDeprecationWarning, stacklevel=2)
            if not kw:
                return item(*args)
            return item(*args, **kw)
        deprecated_func.__name__ = item.__name__
        deprecated_func.__doc__ = item.__doc__
        if hasattr(item, '__dict__'):
            deprecated_func.__dict__.update(item.__dict__)
        return deprecated_func

    elif hasattr(item, '__get__'):
        # it should be a property if there is a getter
        class DepGetProp(object):
            def __init__(self, item, msg):
                self.item = item
                self.msg = msg
            def __get__(self, inst, klass):
                warnings.warn("Accessing deprecated property. %s" % msg,
                              wxPyDeprecationWarning, stacklevel=2)
                return self.item.__get__(inst, klass)
        class DepGetSetProp(DepGetProp):
            def __set__(self, inst, val):
                warnings.warn("Accessing deprecated property. %s" % msg,
                              wxPyDeprecationWarning, stacklevel=2)
                return self.item.__set__(inst, val)
        class DepGetSetDelProp(DepGetSetProp):
            def __delete__(self, inst):
                warnings.warn("Accessing deprecated property. %s" % msg,
                              wxPyDeprecationWarning, stacklevel=2)
                return self.item.__delete__(inst)

        if hasattr(item, '__set__') and hasattr(item, '__delete__'):
            return DepGetSetDelProp(item, msg)
        elif hasattr(item, '__set__'):
            return DepGetSetProp(item, msg)
        else:
            return DepGetProp(item, msg)
    else:
        raise TypeError("unsupported type %s" % type(item))


def deprecatedMsg(msg):
    """
    A wrapper for the deprecated decorator that makes it easier to attach a
    custom message to the warning that is raised if the item is used. This
    can also be used in the @decorator role since it returns the real
    decorator when called.
    """
    import functools
    return functools.partial(deprecated, msg=msg, useName=True)

#----------------------------------------------------------------------------

EmptyString = ""

#----------------------------------------------------------------------------

# End of included code block
#-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=

import typing

BG_STYLE_CUSTOM = BG_STYLE_PAINT

ADJUST_MINSIZE = 0

WS_EX_VALIDATE_RECURSIVELY = 0

def _ClientDataContainer_GetClientObject(self):
    """
    Alias for :meth:`GetClientData`
    """
    return self.GetClientData()
ClientDataContainer.GetClientObject = _ClientDataContainer_GetClientObject
del _ClientDataContainer_GetClientObject
def _ClientDataContainer_SetClientObject(self, data):
    """
    Alias for :meth:`SetClientData`
    """
    self.SetClientData(data)
ClientDataContainer.SetClientObject = _ClientDataContainer_SetClientObject
del _ClientDataContainer_SetClientObject
ClientDataContainer.ClientData = property(ClientDataContainer.GetClientData, ClientDataContainer.SetClientData)
def _initStockObjects():
    import wx
    wx.NORMAL_FONT._copyFrom(       StockGDI.instance().GetFont(StockGDI.FONT_NORMAL))
    wx.SMALL_FONT._copyFrom(        StockGDI.instance().GetFont(StockGDI.FONT_SMALL))
    wx.SWISS_FONT._copyFrom(        StockGDI.instance().GetFont(StockGDI.FONT_SWISS))
    wx.ITALIC_FONT._copyFrom(       StockGDI.instance().GetFont(StockGDI.FONT_ITALIC))

    wx.BLACK_DASHED_PEN._copyFrom(  StockGDI.GetPen(StockGDI.PEN_BLACKDASHED))
    wx.BLACK_PEN._copyFrom(         StockGDI.GetPen(StockGDI.PEN_BLACK))
    wx.BLUE_PEN._copyFrom(          StockGDI.GetPen(StockGDI.PEN_BLUE))
    wx.CYAN_PEN._copyFrom(          StockGDI.GetPen(StockGDI.PEN_CYAN))
    wx.GREEN_PEN._copyFrom(         StockGDI.GetPen(StockGDI.PEN_GREEN))
    wx.YELLOW_PEN._copyFrom(        StockGDI.GetPen(StockGDI.PEN_YELLOW))
    wx.GREY_PEN._copyFrom(          StockGDI.GetPen(StockGDI.PEN_GREY))
    wx.LIGHT_GREY_PEN._copyFrom(    StockGDI.GetPen(StockGDI.PEN_LIGHTGREY))
    wx.MEDIUM_GREY_PEN._copyFrom(   StockGDI.GetPen(StockGDI.PEN_MEDIUMGREY))
    wx.RED_PEN._copyFrom(           StockGDI.GetPen(StockGDI.PEN_RED))
    wx.TRANSPARENT_PEN._copyFrom(   StockGDI.GetPen(StockGDI.PEN_TRANSPARENT))
    wx.WHITE_PEN._copyFrom(         StockGDI.GetPen(StockGDI.PEN_WHITE))

    wx.BLACK_BRUSH._copyFrom(       StockGDI.GetBrush(StockGDI.BRUSH_BLACK))
    wx.BLUE_BRUSH._copyFrom(        StockGDI.GetBrush(StockGDI.BRUSH_BLUE))
    wx.CYAN_BRUSH._copyFrom(        StockGDI.GetBrush(StockGDI.BRUSH_CYAN))
    wx.GREEN_BRUSH._copyFrom(       StockGDI.GetBrush(StockGDI.BRUSH_GREEN))
    wx.YELLOW_BRUSH._copyFrom(      StockGDI.GetBrush(StockGDI.BRUSH_YELLOW))
    wx.GREY_BRUSH._copyFrom(        StockGDI.GetBrush(StockGDI.BRUSH_GREY))
    wx.LIGHT_GREY_BRUSH._copyFrom(  StockGDI.GetBrush(StockGDI.BRUSH_LIGHTGREY))
    wx.MEDIUM_GREY_BRUSH._copyFrom( StockGDI.GetBrush(StockGDI.BRUSH_MEDIUMGREY))
    wx.RED_BRUSH._copyFrom(         StockGDI.GetBrush(StockGDI.BRUSH_RED))
    wx.TRANSPARENT_BRUSH._copyFrom( StockGDI.GetBrush(StockGDI.BRUSH_TRANSPARENT))
    wx.WHITE_BRUSH._copyFrom(       StockGDI.GetBrush(StockGDI.BRUSH_WHITE))

    wx.BLACK._copyFrom(             StockGDI.GetColour(StockGDI.COLOUR_BLACK))
    wx.BLUE._copyFrom(              StockGDI.GetColour(StockGDI.COLOUR_BLUE))
    wx.CYAN._copyFrom(              StockGDI.GetColour(StockGDI.COLOUR_CYAN))
    wx.GREEN._copyFrom(             StockGDI.GetColour(StockGDI.COLOUR_GREEN))
    wx.YELLOW._copyFrom(            StockGDI.GetColour(StockGDI.COLOUR_YELLOW))
    wx.LIGHT_GREY._copyFrom(        StockGDI.GetColour(StockGDI.COLOUR_LIGHTGREY))
    wx.RED._copyFrom(               StockGDI.GetColour(StockGDI.COLOUR_RED))
    wx.WHITE._copyFrom(             StockGDI.GetColour(StockGDI.COLOUR_WHITE))

    wx.CROSS_CURSOR._copyFrom(      StockGDI.GetCursor(StockGDI.CURSOR_CROSS))
    wx.HOURGLASS_CURSOR._copyFrom(  StockGDI.GetCursor(StockGDI.CURSOR_HOURGLASS))
    wx.STANDARD_CURSOR._copyFrom(   StockGDI.GetCursor(StockGDI.CURSOR_STANDARD))

    wx.TheFontList       = StockGDI._getTheFontList()
    wx.ThePenList        = StockGDI._getThePenList()
    wx.TheBrushList      = StockGDI._getTheBrushList()
    wx.TheColourDatabase = StockGDI._getTheColourDatabase()


StockGDI._initStockObjects = staticmethod(_initStockObjects)
def _DateTime___repr__(self):
    if self.IsValid():
        f = self.Format()
        return '<wx.DateTime: "%s">' % f
    else:
        return '<wx.DateTime: "INVALID">'
DateTime.__repr__ = _DateTime___repr__
del _DateTime___repr__
def _DateTime___str__(self):
    if self.IsValid():
        f = self.Format()
        return f
    else:
        return "INVALID DateTime"
DateTime.__str__ = _DateTime___str__
del _DateTime___str__
InvalidDateTime = DefaultDateTime

@wx.deprecatedMsg("Use :meth:`DateTime.FromTimeT` instead.")
def DateTimeFromTimeT(timet):
    """
    Compatibility wrapper for :meth:`DateTime.FromTimeT`
    """
    return DateTime.FromTimeT(timet)

@wx.deprecatedMsg("Use :meth:`DateTime.FromJDN` instead.")
def DateTimeFromJDN(jdn):
    """
    Compatibility wrapper for :meth:`DateTime.FromJDN`
    """
    return DateTime.FromJDN(jdn)

@wx.deprecatedMsg("Use :meth:`DateTime.FromHMS` instead.")
def DateTimeFromHMS(hour, minute=0, second=0, millisecond=0):
    """
    Compatibility wrapper for :meth:`DateTime.FromHMS`
    """
    return DateTime.FromHMS(hour, minute, second, millisecond)

@wx.deprecatedMsg("Use :meth:`DateTime.FromDMY` instead.")
def DateTimeFromDMY(day, month, year=DateTime.Inv_Year, hour=0, minute=0, second=0, millisecond=0):
    """
    Compatibility wrapper for :meth:`DateTime.FromDMY`
    """
    return DateTime.FromDMY(day, month, year, hour, minute, second, millisecond)

def pydate2wxdate(date):
    """
    Convert a Python date or datetime to a :class:`DateTime` object
    """
    import datetime
    assert isinstance(date, (datetime.datetime, datetime.date))
    return DateTime(date)  # the built-in typemap will convert it for us

def wxdate2pydate(date):
    """
    Convert a :class:`DateTime` object to a Python datetime.
    """
    import datetime
    assert isinstance(date, DateTime)
    if date.IsValid():
        return datetime.datetime(date.year, date.month+1, date.day,
                                 date.hour, date.minute, date.second, date.millisecond*1000)
    else:
        return None

def _WindowIDRef___repr__(self):
    return "WindowIDRef: {}".format(self.GetId())
WindowIDRef.__repr__ = _WindowIDRef___repr__
del _WindowIDRef___repr__
def _WindowIDRef___hash__(self):
    return hash(self.GetValue())
WindowIDRef.__hash__ = _WindowIDRef___hash__
del _WindowIDRef___hash__
def NewIdRef(count=1):
    """
    Reserves a new Window ID (or range of WindowIDs) and returns a
    :class:`wx.WindowIDRef` object (or list of them) that will help
    manage the reservation of that ID.
    
    This function is intended to be a drop-in replacement of the old
    and deprecated :func:`wx.NewId` function, with the added benefit
    that the ID should never conflict with an in-use ID or other IDs
    generated by this function.
    """
    if count == 1:
        return WindowIDRef(IdManager.ReserveId())
    else:
        start = IdManager.ReserveId(count)
        IDRefs = []
        for id in range(start, start+count):
            IDRefs.append(WindowIDRef(id))
        return IDRefs

def _ArrayVideoModes___repr__(self):
    return "ArrayVideoModes: " + repr(list(self))
ArrayVideoModes.__repr__ = _ArrayVideoModes___repr__
del _ArrayVideoModes___repr__
#----------------------------------------------------------------------------
# Add the directory where the wxWidgets catalogs were installed
# to the default catalog path, if they were put in the package dir.
import os
_localedir = os.path.join(os.path.dirname(__file__), "locale")
if os.path.exists(_localedir):
    if isinstance(_localedir, (bytes, bytearray)):
        _localedir = _localedir.decode(_sys.getfilesystemencoding())
    Locale.AddCatalogLookupPathPrefix(_localedir)
del os
#----------------------------------------------------------------------------

def _Point_GetIM(self):
    """
    Returns an immutable representation of the ``wx.Point`` object, based on ``namedtuple``.
    
    This new object is hashable and can be used as a dictionary key,
    be added to sets, etc.  It can be converted back into a real ``wx.Point``
    with a simple statement like this: ``obj = wx.Point(imObj)``.
    """
    return _im_Point(*self.Get())
Point.GetIM = _Point_GetIM
del _Point_GetIM
def _Point___str__(self):
    return str(self.Get())
Point.__str__ = _Point___str__
del _Point___str__
def _Point___repr__(self):
    return "wx.Point"+str(self.Get())
Point.__repr__ = _Point___repr__
del _Point___repr__
def _Point___len__(self):
    return len(self.Get())
Point.__len__ = _Point___len__
del _Point___len__
def _Point___reduce__(self):
    return (Point, self.Get())
Point.__reduce__ = _Point___reduce__
del _Point___reduce__
def _Point___getitem__(self, idx):
    return self.Get()[idx]
Point.__getitem__ = _Point___getitem__
del _Point___getitem__
def _Point___setitem__(self, idx, val):
    if idx == 0: self.x = val
    elif idx == 1: self.y = val
    else: raise IndexError
Point.__setitem__ = _Point___setitem__
del _Point___setitem__
Point.__safe_for_unpickling__ = True

Point.IM = property(Point.GetIM)
def _Size_GetIM(self):
    """
    Returns an immutable representation of the ``wx.Size`` object, based on ``namedtuple``.
    
    This new object is hashable and can be used as a dictionary key,
    be added to sets, etc.  It can be converted back into a real ``wx.Size``
    with a simple statement like this: ``obj = wx.Size(imObj)``.
    """
    return _im_Size(*self.Get())
Size.GetIM = _Size_GetIM
del _Size_GetIM
def _Size___str__(self):
    return str(self.Get())
Size.__str__ = _Size___str__
del _Size___str__
def _Size___repr__(self):
    return "wx.Size"+str(self.Get())
Size.__repr__ = _Size___repr__
del _Size___repr__
def _Size___len__(self):
    return len(self.Get())
Size.__len__ = _Size___len__
del _Size___len__
def _Size___nonzero__(self):
    return self.Get() != (0,0)
Size.__nonzero__ = _Size___nonzero__
del _Size___nonzero__
def _Size___bool__(self):
    return self.Get() != (0,0)
Size.__bool__ = _Size___bool__
del _Size___bool__
def _Size___reduce__(self):
    return (Size, self.Get())
Size.__reduce__ = _Size___reduce__
del _Size___reduce__
def _Size___getitem__(self, idx):
    return self.Get()[idx]
Size.__getitem__ = _Size___getitem__
del _Size___getitem__
def _Size___setitem__(self, idx, val):
    if idx == 0: self.width = val
    elif idx == 1: self.height = val
    else: raise IndexError
Size.__setitem__ = _Size___setitem__
del _Size___setitem__
Size.__safe_for_unpickling__ = True

def _Rect_GetIM(self):
    """
    Returns an immutable representation of the ``wx.Rect`` object, based on ``namedtuple``.
    
    This new object is hashable and can be used as a dictionary key,
    be added to sets, etc.  It can be converted back into a real ``wx.Rect``
    with a simple statement like this: ``obj = wx.Rect(imObj)``.
    """
    return _im_Rect(*self.Get())
Rect.GetIM = _Rect_GetIM
del _Rect_GetIM
def _Rect___str__(self):
    return str(self.Get())
Rect.__str__ = _Rect___str__
del _Rect___str__
def _Rect___repr__(self):
    return "wx.Rect"+str(self.Get())
Rect.__repr__ = _Rect___repr__
del _Rect___repr__
def _Rect___len__(self):
    return len(self.Get())
Rect.__len__ = _Rect___len__
del _Rect___len__
def _Rect___nonzero__(self):
    return self.Get() != (0,0,0,0)
Rect.__nonzero__ = _Rect___nonzero__
del _Rect___nonzero__
def _Rect___bool__(self):
    return self.Get() != (0,0,0,0)
Rect.__bool__ = _Rect___bool__
del _Rect___bool__
def _Rect___reduce__(self):
    return (Rect, self.Get())
Rect.__reduce__ = _Rect___reduce__
del _Rect___reduce__
def _Rect___getitem__(self, idx):
    return self.Get()[idx]
Rect.__getitem__ = _Rect___getitem__
del _Rect___getitem__
def _Rect___setitem__(self, idx, val):
    if idx == 0: self.x = val
    elif idx == 1: self.y = val
    elif idx == 2: self.width = val
    elif idx == 3: self.height = val
    else: raise IndexError
Rect.__setitem__ = _Rect___setitem__
del _Rect___setitem__
Rect.__safe_for_unpickling__ = True

def _RealPoint_GetIM(self):
    """
    Returns an immutable representation of the ``wx.RealPoint`` object, based on ``namedtuple``.
    
    This new object is hashable and can be used as a dictionary key,
    be added to sets, etc.  It can be converted back into a real ``wx.RealPoint``
    with a simple statement like this: ``obj = wx.RealPoint(imObj)``.
    """
    return _im_RealPoint(*self.Get())
RealPoint.GetIM = _RealPoint_GetIM
del _RealPoint_GetIM
def _RealPoint___str__(self):
    return str(self.Get())
RealPoint.__str__ = _RealPoint___str__
del _RealPoint___str__
def _RealPoint___repr__(self):
    return "wx.RealPoint"+str(self.Get())
RealPoint.__repr__ = _RealPoint___repr__
del _RealPoint___repr__
def _RealPoint___len__(self):
    return len(self.Get())
RealPoint.__len__ = _RealPoint___len__
del _RealPoint___len__
def _RealPoint___nonzero__(self):
    return self.Get() != (0,0)
RealPoint.__nonzero__ = _RealPoint___nonzero__
del _RealPoint___nonzero__
def _RealPoint___bool__(self):
    return self.Get() != (0,0)
RealPoint.__bool__ = _RealPoint___bool__
del _RealPoint___bool__
def _RealPoint___reduce__(self):
    return (RealPoint, self.Get())
RealPoint.__reduce__ = _RealPoint___reduce__
del _RealPoint___reduce__
def _RealPoint___getitem__(self, idx):
    return self.Get()[idx]
RealPoint.__getitem__ = _RealPoint___getitem__
del _RealPoint___getitem__
def _RealPoint___setitem__(self, idx, val):
    if idx == 0: self.x = val
    elif idx == 1: self.y = val
    else: raise IndexError
RealPoint.__setitem__ = _RealPoint___setitem__
del _RealPoint___setitem__
RealPoint.__safe_for_unpickling__ = True

RealPoint.IM = property(RealPoint.GetIM)
def _ColourDatabase_FindColour(self, colour):
    return self.Find(colour)
ColourDatabase.FindColour = _ColourDatabase_FindColour
del _ColourDatabase_FindColour
from collections import namedtuple
_im_Point = namedtuple('_im_Point', ['x', 'y'])
del namedtuple

def _PointList___repr__(self):
    return "PointList: " + repr(list(self))
PointList.__repr__ = _PointList___repr__
del _PointList___repr__
from collections import namedtuple
_im_Size = namedtuple('_im_Size', ['width', 'height'])
del namedtuple

from collections import namedtuple
_im_Rect = namedtuple('_im_Rect', ['x', 'y', 'width', 'height'])
del namedtuple

from collections import namedtuple
_im_RealPoint = namedtuple('_im_RealPoint', ['x', 'y'])
del namedtuple

def _Point2D_GetIM(self):
    """
    Returns an immutable representation of the ``wx.Point2D`` object, based on ``namedtuple``.
    
    This new object is hashable and can be used as a dictionary key,
    be added to sets, etc.  It can be converted back into a real ``wx.Point2D``
    with a simple statement like this: ``obj = wx.Point2D(imObj)``.
    """
    return _im_Point2D(*self.Get())
Point2D.GetIM = _Point2D_GetIM
del _Point2D_GetIM
def _Point2D___str__(self):
    return str(self.Get())
Point2D.__str__ = _Point2D___str__
del _Point2D___str__
def _Point2D___repr__(self):
    return "wx.Point2D"+str(self.Get())
Point2D.__repr__ = _Point2D___repr__
del _Point2D___repr__
def _Point2D___len__(self):
    return len(self.Get())
Point2D.__len__ = _Point2D___len__
del _Point2D___len__
def _Point2D___nonzero__(self):
    return self.Get() != (0,0)
Point2D.__nonzero__ = _Point2D___nonzero__
del _Point2D___nonzero__
def _Point2D___bool__(self):
    return self.Get() != (0,0)
Point2D.__bool__ = _Point2D___bool__
del _Point2D___bool__
def _Point2D___reduce__(self):
    return (Point2D, self.Get())
Point2D.__reduce__ = _Point2D___reduce__
del _Point2D___reduce__
def _Point2D___getitem__(self, idx):
    return self.Get()[idx]
Point2D.__getitem__ = _Point2D___getitem__
del _Point2D___getitem__
def _Point2D___setitem__(self, idx, val):
    if idx == 0: self.x = val
    elif idx == 1: self.y = val
    else: raise IndexError
Point2D.__setitem__ = _Point2D___setitem__
del _Point2D___setitem__
Point2D.__safe_for_unpickling__ = True

Point2D.IM = property(Point2D.GetIM)
def _Rect2D_GetIM(self):
    """
    Returns an immutable representation of the ``wx.Rect2D`` object, based on ``namedtuple``.
    
    This new object is hashable and can be used as a dictionary key,
    be added to sets, etc.  It can be converted back into a real ``wx.Rect2D``
    with a simple statement like this: ``obj = wx.Rect2D(imObj)``.
    """
    return _im_Rect2D(*self.Get())
Rect2D.GetIM = _Rect2D_GetIM
del _Rect2D_GetIM
def _Rect2D___str__(self):
    return str(self.Get())
Rect2D.__str__ = _Rect2D___str__
del _Rect2D___str__
def _Rect2D___repr__(self):
    return "wx.Rect2D"+str(self.Get())
Rect2D.__repr__ = _Rect2D___repr__
del _Rect2D___repr__
def _Rect2D___len__(self):
    return len(self.Get())
Rect2D.__len__ = _Rect2D___len__
del _Rect2D___len__
def _Rect2D___nonzero__(self):
    return self.Get() != (0,0,0,0)
Rect2D.__nonzero__ = _Rect2D___nonzero__
del _Rect2D___nonzero__
def _Rect2D___bool__(self):
    return self.Get() != (0,0,0,0)
Rect2D.__bool__ = _Rect2D___bool__
del _Rect2D___bool__
def _Rect2D___reduce__(self):
    return (Rect2D, self.Get())
Rect2D.__reduce__ = _Rect2D___reduce__
del _Rect2D___reduce__
def _Rect2D___getitem__(self, idx):
    return self.Get()[idx]
Rect2D.__getitem__ = _Rect2D___getitem__
del _Rect2D___getitem__
def _Rect2D___setitem__(self, idx, val):
    if idx == 0: self.x = val
    elif idx == 1: self.y = val
    elif idx == 2: self.width = val
    elif idx == 3: self.height = val
    else: raise IndexError
Rect2D.__setitem__ = _Rect2D___setitem__
del _Rect2D___setitem__
Rect2D.__safe_for_unpickling__ = True

Rect2D.IM = property(Rect2D.GetIM)
from collections import namedtuple
_im_Point2D = namedtuple('_im_Point2D', ['x', 'y'])
del namedtuple

from collections import namedtuple
_im_Rect2D = namedtuple('_im_Rect2D', ['x', 'y', 'width', 'height'])
del namedtuple

def _Position_GetIM(self):
    """
    Returns an immutable representation of the ``wx.Position`` object, based on ``namedtuple``.
    
    This new object is hashable and can be used as a dictionary key,
    be added to sets, etc.  It can be converted back into a real ``wx.Position``
    with a simple statement like this: ``obj = wx.Position(imObj)``.
    """
    return _im_Position(*self.Get())
Position.GetIM = _Position_GetIM
del _Position_GetIM
def _Position___str__(self):
    return str(self.Get())
Position.__str__ = _Position___str__
del _Position___str__
def _Position___repr__(self):
    return "wx.Position"+str(self.Get())
Position.__repr__ = _Position___repr__
del _Position___repr__
def _Position___len__(self):
    return len(self.Get())
Position.__len__ = _Position___len__
del _Position___len__
def _Position___nonzero__(self):
    return self.Get() != (0,0)
Position.__nonzero__ = _Position___nonzero__
del _Position___nonzero__
def _Position___bool__(self):
    return self.Get() != (0,0)
Position.__bool__ = _Position___bool__
del _Position___bool__
def _Position___reduce__(self):
    return (Position, self.Get())
Position.__reduce__ = _Position___reduce__
del _Position___reduce__
def _Position___getitem__(self, idx):
    return self.Get()[idx]
Position.__getitem__ = _Position___getitem__
del _Position___getitem__
def _Position___setitem__(self, idx, val):
    if idx == 0: self.Row = val
    elif idx == 1: self.Col = val
    else: raise IndexError
Position.__setitem__ = _Position___setitem__
del _Position___setitem__
Position.__safe_for_unpickling__ = True

Position.IM = property(Position.GetIM)
from collections import namedtuple
_im_Position = namedtuple('_im_Position', ['Row', 'Col'])
del namedtuple

def _Colour_GetIM(self):
    """
    Returns an immutable representation of the ``wx.Colour`` object, based on ``namedtuple``.
    
    This new object is hashable and can be used as a dictionary key,
    be added to sets, etc.  It can be converted back into a real ``wx.Colour``
    with a simple statement like this: ``obj = wx.Colour(imObj)``.
    """
    return _im_Colour(*self.Get())
Colour.GetIM = _Colour_GetIM
del _Colour_GetIM
def _Colour___str__(self):
    return str(self.Get())
Colour.__str__ = _Colour___str__
del _Colour___str__
def _Colour___repr__(self):
    return "wx.Colour"+str(self.Get())
Colour.__repr__ = _Colour___repr__
del _Colour___repr__
def _Colour___len__(self):
    return len(self.Get())
Colour.__len__ = _Colour___len__
del _Colour___len__
def _Colour___reduce__(self):
    return (Colour, self.Get())
Colour.__reduce__ = _Colour___reduce__
del _Colour___reduce__
def _Colour___getitem__(self, idx):
    return self.Get()[idx]
Colour.__getitem__ = _Colour___getitem__
del _Colour___getitem__
def _Colour___setitem__(self, idx, val):
    if idx == 0:   self.red = val
    elif idx == 1: self.green = val
    elif idx == 2: self.blue = val
    elif idx == 3: self.alpha = val
    else: raise IndexError
Colour.__setitem__ = _Colour___setitem__
del _Colour___setitem__
Colour.__safe_for_unpickling__ = True

# These stock colours will be initialized when the wx.App object is created.
BLACK = Colour()
BLUE = Colour()
CYAN = Colour()
GREEN = Colour()
YELLOW = Colour()
LIGHT_GREY = Colour()
RED = Colour()
WHITE = Colour()

from collections import namedtuple
_im_Colour = namedtuple('_im_Colour', ['red', 'green', 'blue', 'alpha'])
del namedtuple

NamedColour = wx.deprecated(Colour, "Use Colour instead.")

ZipFSHandler = wx.deprecated(ArchiveFSHandler, "Use ArchiveFSHandler instead.")

def _Image_ConvertToBitmap(self, depth=-1):
    """
    ConvertToBitmap(depth=-1) -> Bitmap
    
    Convert the image to a :class:`wx.Bitmap`.
    """
    bmp = wx.Bitmap(self, depth)
    return bmp
Image.ConvertToBitmap = _Image_ConvertToBitmap
del _Image_ConvertToBitmap
def _Image_ConvertToMonoBitmap(self, red, green, blue):
    """
    ConvertToMonoBitmap(red, green, blue) -> Bitmap
    
    Creates a monochrome version of the image and returns it as a :class:`wx.Bitmap`.
    """
    mono = self.ConvertToMono( red, green, blue )
    bmp = wx.Bitmap( mono, 1 )
    return bmp
Image.ConvertToMonoBitmap = _Image_ConvertToMonoBitmap
del _Image_ConvertToMonoBitmap
@wx.deprecatedMsg("Use :class:`Image` instead.")
def EmptyImage(width=0, height=0, clear=True):
    """
    A compatibility wrapper for the wx.Image(width, height) constructor
    """
    return Image(width, height, clear)

@wx.deprecatedMsg("Use bitmap.ConvertToImage instead.")
def ImageFromBitmap(bitmap):
    """
    Create a :class:`Image` from a :class:`wx.Bitmap`
    """
    return bitmap.ConvertToImage()

@wx.deprecatedMsg("Use :class:`Image` instead.")
def ImageFromStream(stream, type=BITMAP_TYPE_ANY, index=-1):
    """
    Load an image from a stream (file-like object)
    """
    return wx.Image(stream, type, index)

@wx.deprecatedMsg("Use :class:`Image` instead.")
def ImageFromData(width, height, data):
    """
    Compatibility wrapper for creating an image from RGB data
    """
    return Image(width, height, data)

@wx.deprecatedMsg("Use :class:`Image` instead.")
def ImageFromDataWithAlpha(width, height, data, alpha):
    """
    Compatibility wrapper for creating an image from RGB and Alpha data
    """
    return Image(width, height, data, alpha)

def ImageFromBuffer(width, height, dataBuffer, alphaBuffer=None):
    """
    Creates a :class:`Image` from the data in `dataBuffer`.  The `dataBuffer`
    parameter must be a Python object that implements the buffer interface,
    such as a string, array, etc.  The `dataBuffer` object is expected to
    contain a series of RGB bytes and be width*height*3 bytes long.  A buffer
    object can optionally be supplied for the image's alpha channel data, and
    it is expected to be width*height bytes long.
    
    The :class:`Image` will be created with its data and alpha pointers initialized
    to the memory address pointed to by the buffer objects, thus saving the
    time needed to copy the image data from the buffer object to the :class:`Image`.
    While this has advantages, it also has the shoot-yourself-in-the-foot
    risks associated with sharing a C pointer between two objects.
    
    To help alleviate the risk a reference to the data and alpha buffer
    objects are kept with the :class:`Image`, so that they won't get deleted until
    after the wx.Image is deleted.  However please be aware that it is not
    guaranteed that an object won't move its memory buffer to a new location
    when it needs to resize its contents.  If that happens then the :class:`Image`
    will end up referring to an invalid memory location and could cause the
    application to crash.  Therefore care should be taken to not manipulate
    the objects used for the data and alpha buffers in a way that would cause
    them to change size.
    """
    img = Image(width, height)
    img.SetDataBuffer(dataBuffer)
    if alphaBuffer:
        img.SetAlphaBuffer(alphaBuffer)
    img._buffer = dataBuffer
    img._alpha = alphaBuffer
    return img

def _ImageArray___repr__(self):
    return "ImageArray: " + repr(list(self))
ImageArray.__repr__ = _ImageArray___repr__
del _ImageArray___repr__
IMAGE_OPTION_QUALITY = "quality"
IMAGE_OPTION_FILENAME = "FileName"
IMAGE_OPTION_RESOLUTION = "Resolution"
IMAGE_OPTION_RESOLUTIONX = "ResolutionX"
IMAGE_OPTION_RESOLUTIONY = "ResolutionY"
IMAGE_OPTION_RESOLUTIONUNIT = "ResolutionUnit"
IMAGE_OPTION_MAX_WIDTH = "MaxWidth"
IMAGE_OPTION_MAX_HEIGHT = "MaxHeight"
IMAGE_OPTION_ORIGINAL_WIDTH = "OriginalWidth"
IMAGE_OPTION_ORIGINAL_HEIGHT = "OriginalHeight"
IMAGE_OPTION_BMP_FORMAT = "wxBMP_FORMAT"
IMAGE_OPTION_CUR_HOTSPOT_X = "HotSpotX"
IMAGE_OPTION_CUR_HOTSPOT_Y = "HotSpotY"
IMAGE_OPTION_GIF_COMMENT = "GifComment"
IMAGE_OPTION_GIF_TRANSPARENCY = "Transparency"
IMAGE_OPTION_GIF_TRANSPARENCY_HIGHLIGHT = "Highlight"
IMAGE_OPTION_GIF_TRANSPARENCY_UNCHANGED = "Unchanged"
IMAGE_OPTION_PNG_FORMAT = "PngFormat"
IMAGE_OPTION_PNG_BITDEPTH = "PngBitDepth"
IMAGE_OPTION_PNG_FILTER = "PngF"
IMAGE_OPTION_PNG_COMPRESSION_LEVEL = "PngZL"
IMAGE_OPTION_PNG_COMPRESSION_MEM_LEVEL = "PngZM"
IMAGE_OPTION_PNG_COMPRESSION_STRATEGY = "PngZS"
IMAGE_OPTION_PNG_COMPRESSION_BUFFER_SIZE = "PngZB"
IMAGE_OPTION_TIFF_BITSPERSAMPLE = "BitsPerSample"
IMAGE_OPTION_TIFF_SAMPLESPERPIXEL = "SamplesPerPixel"
IMAGE_OPTION_TIFF_COMPRESSION = "Compression"
IMAGE_OPTION_TIFF_PHOTOMETRIC = "Photometric"
IMAGE_OPTION_TIFF_IMAGEDESCRIPTOR = "ImageDescriptor"
IMAGE_OPTION_TIFF_BITSPERSAMPLE = "BitsPerSample"
IMAGE_OPTION_TIFF_SAMPLESPERPIXEL = "SamplesPerPixel"
IMAGE_OPTION_TIFF_COMPRESSION = "Compression"
IMAGE_OPTION_TIFF_PHOTOMETRIC = "Photometric"
IMAGE_OPTION_TIFF_IMAGEDESCRIPTOR = "ImageDescriptor"
IMAGE_OPTION_GIF_COMMENT = "GifComment"
IMAGE_OPTION_PNG_FORMAT = "PngFormat"
IMAGE_OPTION_PNG_BITDEPTH = "PngBitDepth"
IMAGE_OPTION_PNG_FILTER = "PngF"
IMAGE_OPTION_PNG_COMPRESSION_LEVEL = "PngZL"
IMAGE_OPTION_PNG_COMPRESSION_MEM_LEVEL = "PngZM"
IMAGE_OPTION_PNG_COMPRESSION_STRATEGY = "PngZS"
IMAGE_OPTION_PNG_COMPRESSION_BUFFER_SIZE = "PngZB"

@wx.deprecatedMsg("Use :meth:`wx.Bitmap.FromBuffer` or :meth:`wx.Bitmap.FromBufferAndAlpha` instead.")
def BitmapFromBuffer(width, height, dataBuffer, alphaBuffer=None):
    """
    A compatibility wrapper for :meth:`wx.Bitmap.FromBuffer` and :meth:`wx.Bitmap.FromBufferAndAlpha`
    """
    if alphaBuffer is not None:
        return Bitmap.FromBufferAndAlpha(width, height, dataBuffer, alphaBuffer)
    else:
        return Bitmap.FromBuffer(width, height, dataBuffer)

@wx.deprecatedMsg("Use :meth:`wx.Bitmap.FromBufferRGBA` instead.")
def BitmapFromBufferRGBA(width, height, dataBuffer):
    """
    A compatibility wrapper for :meth:`wx.Bitmap.FromBufferRGBA`
    """
    return Bitmap.FromBufferRGBA(width, height, dataBuffer)

@wx.deprecatedMsg("Use :meth:`wx.Bitmap.FromRGBA` instead.")
def EmptyBitmapRGBA(width, height, red=0, green=0, blue=0, alpha=0):
    """
    A compatibility wrapper for :meth:`wx.Bitmap.FromRGBA`
    """
    return Bitmap.FromRGBA(width, height, red, green, blue, alpha)

@wx.deprecatedMsg("Use :class:`wx.Bitmap` instead")
def EmptyBitmap(width, height, depth=BITMAP_SCREEN_DEPTH):
    """
    A compatibility wrapper for the wx.Bitmap(width, height, depth) constructor
    """
    return Bitmap(width, height, depth)

@wx.deprecatedMsg("Use :class:`wx.Bitmap` instead")
def BitmapFromImage(image):
    """
    A compatibility wrapper for the wx.Bitmap(wx.Image) constructor
    """
    return Bitmap(image)

@wx.deprecatedMsg("Use :class:`Icon` instead")
def EmptyIcon():
    """
    A compatibility wrapper for the :class:`Icon` constructor
    """
    return Icon()

def _Font_SetNoAntiAliasing(self, no=True):
    pass
Font.SetNoAntiAliasing = wx.deprecated(_Font_SetNoAntiAliasing)
del _Font_SetNoAntiAliasing
def _Font_GetNoAntiAliasing(self):
    pass
Font.GetNoAntiAliasing = wx.deprecated(_Font_GetNoAntiAliasing)
del _Font_GetNoAntiAliasing
# These stock fonts will be initialized when the wx.App object is created.
NORMAL_FONT = Font()
SMALL_FONT = Font()
ITALIC_FONT = Font()
SWISS_FONT = Font()

wx.DEFAULT    = int(wx.FONTFAMILY_DEFAULT)
wx.DECORATIVE = int(wx.FONTFAMILY_DECORATIVE)
wx.ROMAN      = int(wx.FONTFAMILY_ROMAN)
wx.SCRIPT     = int(wx.FONTFAMILY_SCRIPT)
wx.SWISS      = int(wx.FONTFAMILY_SWISS)
wx.MODERN     = int(wx.FONTFAMILY_MODERN)
wx.TELETYPE   = int(wx.FONTFAMILY_TELETYPE)

wx.NORMAL = int(wx.FONTWEIGHT_NORMAL)
wx.LIGHT  = int(wx.FONTWEIGHT_LIGHT)
wx.BOLD   = int(wx.FONTWEIGHT_BOLD)

wx.NORMAL = int(wx.FONTSTYLE_NORMAL)
wx.ITALIC = int(wx.FONTSTYLE_ITALIC)
wx.SLANT  = int(wx.FONTSTYLE_SLANT)

# These stock pens will be initialized when the wx.App object is created.
RED_PEN = Pen()
BLUE_PEN = Pen()
CYAN_PEN = Pen()
GREEN_PEN = Pen()
YELLOW_PEN = Pen()
BLACK_PEN = Pen()
WHITE_PEN = Pen()
TRANSPARENT_PEN = Pen()
BLACK_DASHED_PEN = Pen()
GREY_PEN = Pen()
MEDIUM_GREY_PEN = Pen()
LIGHT_GREY_PEN = Pen()

wx.SOLID       = int(wx.PENSTYLE_SOLID)
wx.DOT         = int(wx.PENSTYLE_DOT)
wx.LONG_DASH   = int(wx.PENSTYLE_LONG_DASH)
wx.SHORT_DASH  = int(wx.PENSTYLE_SHORT_DASH)
wx.DOT_DASH    = int(wx.PENSTYLE_DOT_DASH)
wx.USER_DASH   = int(wx.PENSTYLE_USER_DASH)
wx.TRANSPARENT = int(wx.PENSTYLE_TRANSPARENT)

# These stock brushes will be initialized when the wx.App object is created.
BLUE_BRUSH = Brush()
GREEN_BRUSH = Brush()
YELLOW_BRUSH = Brush()
WHITE_BRUSH = Brush()
BLACK_BRUSH = Brush()
GREY_BRUSH = Brush()
MEDIUM_GREY_BRUSH = Brush()
LIGHT_GREY_BRUSH = Brush()
TRANSPARENT_BRUSH = Brush()
CYAN_BRUSH = Brush()
RED_BRUSH = Brush()

wx.STIPPLE_MASK_OPAQUE = int(wx.BRUSHSTYLE_STIPPLE_MASK_OPAQUE)
wx.STIPPLE_MASK        = int(wx.BRUSHSTYLE_STIPPLE_MASK)
wx.STIPPLE             = int(wx.BRUSHSTYLE_STIPPLE)
wx.BDIAGONAL_HATCH     = int(wx.BRUSHSTYLE_BDIAGONAL_HATCH)
wx.CROSSDIAG_HATCH     = int(wx.BRUSHSTYLE_CROSSDIAG_HATCH)
wx.FDIAGONAL_HATCH     = int(wx.BRUSHSTYLE_FDIAGONAL_HATCH)
wx.CROSS_HATCH         = int(wx.BRUSHSTYLE_CROSS_HATCH)
wx.HORIZONTAL_HATCH    = int(wx.BRUSHSTYLE_HORIZONTAL_HATCH)
wx.VERTICAL_HATCH      = int(wx.BRUSHSTYLE_VERTICAL_HATCH)

# These stock cursors will be initialized when the wx.App object is created.
STANDARD_CURSOR = Cursor()
HOURGLASS_CURSOR = Cursor()
CROSS_CURSOR = Cursor()

StockCursor = wx.deprecated(Cursor, "Use Cursor instead.")

CursorFromImage = wx.deprecated(Cursor, "Use Cursor instead.")

def _Region___iter__(self):
    """
    Returns a rectangle iterator conforming to the Python iterator
    protocol.
    """
    return PyRegionIterator(self)
Region.__iter__ = _Region___iter__
del _Region___iter__
class PyRegionIterator(object):
    "A Python iterator for wx.Region objects"
    def __init__(self, region):
        self._region = region
        self._iterator = wx.RegionIterator(region)
    def next(self):
        if not self._iterator:
            raise StopIteration
        rect = self._iterator.GetRect()
        if self._iterator.HaveRects():
            self._iterator.Next()
        return rect
    __next__ = next  # for Python 3

def _DC_GetClippingRect(self):
    """
    Returns the rectangle surrounding the current clipping region as a wx.Rect.
    """
    rv, x, y, w, h = self.GetClippingBox()
    return wx.Rect(x,y,w,h)
DC.GetClippingRect = _DC_GetClippingRect
del _DC_GetClippingRect
DC.DrawImageLabel = wx.deprecated(DC.DrawLabel, "Use DrawLabel instead.")

def _DC_GetBoundingBox(self):
    """
    GetBoundingBox() -> (x1,y1, x2,y2)
    
    Returns the min and max points used in drawing commands so far.
    """
    return (self.MinX(), self.MinY(), self.MaxX(), self.MaxY())
DC.GetBoundingBox = _DC_GetBoundingBox
del _DC_GetBoundingBox
DC.GetHDC = wx.deprecated(DC.GetHDC, "Use GetHandle instead.")

DC.GetCGContext = wx.deprecated(DC.GetCGContext, "Use GetHandle instead.")

DC.GetGdkDrawable = wx.deprecated(DC.GetGdkDrawable, "Use GetHandle instead.")

def _DC___enter__(self):
    return self
DC.__enter__ = _DC___enter__
del _DC___enter__
def _DC___exit__(self, exc_type, exc_val, exc_tb):
    self.Destroy()
DC.__exit__ = _DC___exit__
del _DC___exit__
def _DC_DrawPointList(self, points, pens=None):
    """
    Draw a list of points as quickly as possible.
    
    :param points: A sequence of 2-element sequences representing
                   each point to draw, (x,y).
    :param pens:   If None, then the current pen is used.  If a single
                   pen then it will be used for all points.  If a list of
                   pens then there should be one for each point in points.
    """
    if pens is None:
        pens = []
    elif isinstance(pens, wx.Pen):
        pens = [pens]
    elif len(pens) != len(points):
        raise ValueError('points and pens must have same length')
    return self._DrawPointList(points, pens, [])
DC.DrawPointList = _DC_DrawPointList
del _DC_DrawPointList
def _DC_DrawLineList(self, lines, pens=None):
    """
    Draw a list of lines as quickly as possible.
    
    :param lines: A sequence of 4-element sequences representing
                  each line to draw, (x1,y1, x2,y2).
    :param pens:  If None, then the current pen is used.  If a
                  single pen then it will be used for all lines.  If
                  a list of pens then there should be one for each line
                  in lines.
    """
    if pens is None:
        pens = []
    elif isinstance(pens, wx.Pen):
        pens = [pens]
    elif len(pens) != len(lines):
        raise ValueError('lines and pens must have same length')
    return self._DrawLineList(lines, pens, [])
DC.DrawLineList = _DC_DrawLineList
del _DC_DrawLineList
def _DC_DrawRectangleList(self, rectangles, pens=None, brushes=None):
    """
    Draw a list of rectangles as quickly as possible.
    
    :param rectangles: A sequence of 4-element sequences representing
                       each rectangle to draw, (x,y, w,h).
    :param pens:       If None, then the current pen is used.  If a
                       single pen then it will be used for all rectangles.
                       If a list of pens then there should be one for each
                       rectangle in rectangles.
    :param brushes:    A brush or brushes to be used to fill the rectagles,
                       with similar semantics as the pens parameter.
    """
    if pens is None:
        pens = []
    elif isinstance(pens, wx.Pen):
        pens = [pens]
    elif len(pens) != len(rectangles):
        raise ValueError('rectangles and pens must have same length')
    if brushes is None:
        brushes = []
    elif isinstance(brushes, wx.Brush):
        brushes = [brushes]
    elif len(brushes) != len(rectangles):
        raise ValueError('rectangles and brushes must have same length')
    return self._DrawRectangleList(rectangles, pens, brushes)
DC.DrawRectangleList = _DC_DrawRectangleList
del _DC_DrawRectangleList
def _DC_DrawEllipseList(self, ellipses, pens=None, brushes=None):
    """
    Draw a list of ellipses as quickly as possible.
    
    :param ellipses: A sequence of 4-element sequences representing
                     each ellipse to draw, (x,y, w,h).
    :param pens:     If None, then the current pen is used.  If a
                     single pen then it will be used for all ellipses.
                     If a list of pens then there should be one for each
                     ellipse in ellipses.
    :param brushes:  A brush or brushes to be used to fill the ellipses,
                     with similar semantics as the pens parameter.
    """
    if pens is None:
        pens = []
    elif isinstance(pens, wx.Pen):
        pens = [pens]
    elif len(pens) != len(ellipses):
        raise ValueError('ellipses and pens must have same length')
    if brushes is None:
        brushes = []
    elif isinstance(brushes, wx.Brush):
        brushes = [brushes]
    elif len(brushes) != len(ellipses):
        raise ValueError('ellipses and brushes must have same length')
    return self._DrawEllipseList(ellipses, pens, brushes)
DC.DrawEllipseList = _DC_DrawEllipseList
del _DC_DrawEllipseList
def _DC_DrawPolygonList(self, polygons, pens=None, brushes=None):
    """
    Draw a list of polygons, each of which is a list of points.
    
    :param polygons: A sequence of sequences of sequences.
                     [[(x1,y1),(x2,y2),(x3,y3)...], [(x1,y1),(x2,y2),(x3,y3)...]]
    
    :param pens:     If None, then the current pen is used.  If a
                     single pen then it will be used for all polygons.
                     If a list of pens then there should be one for each
                     polygon.
    :param brushes:  A brush or brushes to be used to fill the polygons,
                     with similar semantics as the pens parameter.
    """
    if pens is None:
        pens = []
    elif isinstance(pens, wx.Pen):
        pens = [pens]
    elif len(pens) != len(polygons):
        raise ValueError('polygons and pens must have same length')
    if brushes is None:
        brushes = []
    elif isinstance(brushes, wx.Brush):
        brushes = [brushes]
    elif len(brushes) != len(polygons):
        raise ValueError('polygons and brushes must have same length')
    return self._DrawPolygonList(polygons, pens, brushes)
DC.DrawPolygonList = _DC_DrawPolygonList
del _DC_DrawPolygonList
def _DC_DrawTextList(self, textList, coords, foregrounds=None, backgrounds=None):
    """
    Draw a list of strings using a list of coordinants for positioning each string.
    
    :param textList:    A list of strings
    :param coords:      A list of (x,y) positions
    :param foregrounds: A list of `wx.Colour` objects to use for the
                        foregrounds of the strings.
    :param backgrounds: A list of `wx.Colour` objects to use for the
                        backgrounds of the strings.
    
    NOTE: Make sure you set background mode to wx.Solid (DC.SetBackgroundMode)
          If you want backgrounds to do anything.
    """
    if type(textList) == type(''):
        textList = [textList]
    elif len(textList) != len(coords):
        raise ValueError('textlist and coords must have same length')
    if foregrounds is None:
        foregrounds = []
    elif isinstance(foregrounds, wx.Colour):
        foregrounds = [foregrounds]
    elif len(foregrounds) != len(coords):
        raise ValueError('foregrounds and coords must have same length')
    if backgrounds is None:
        backgrounds = []
    elif isinstance(backgrounds, wx.Colour):
        backgrounds = [backgrounds]
    elif len(backgrounds) != len(coords):
        raise ValueError('backgrounds and coords must have same length')
    return  self._DrawTextList(textList, coords, foregrounds, backgrounds)
DC.DrawTextList = _DC_DrawTextList
del _DC_DrawTextList
def _DC_DrawLinesFromBuffer(self, pyBuff):
    """
    Implementation of DrawLines that can use numpy arrays, or anything else that uses the
    python buffer protocol directly without any element conversion.  This provides a 
    significant performance increase over the standard DrawLines function.
    
    The pyBuff argument needs to provide an array of C integers organized as 
    x, y point pairs.  The size of a C integer is platform dependent.
    With numpy, the intc data type will provide the appropriate element size.
    
    If called with an object that doesn't support
    the python buffer protocol, or if the underlying element size does not
    match the size of a C integer, a TypeError exception is raised.  If 
    the buffer provided has float data with the same element size as a 
    C integer, no error will be raised, but the lines will not be drawn
    in the appropriate places.
    
    :param pyBuff:    A python buffer containing integer pairs
    """
    return  self._DrawLinesFromBuffer(pyBuff)
DC.DrawLinesFromBuffer = _DC_DrawLinesFromBuffer
del _DC_DrawLinesFromBuffer
DC.BoundingBox = property(DC.GetBoundingBox)
DC.ClippingRect = property(DC.GetClippingRect)
def _DCClipper___enter__(self):
    return self
DCClipper.__enter__ = _DCClipper___enter__
del _DCClipper___enter__
def _DCClipper___exit__(self, exc_type, exc_val, exc_tb):
    return False
DCClipper.__exit__ = _DCClipper___exit__
del _DCClipper___exit__
def _DCBrushChanger___enter__(self):
    return self
DCBrushChanger.__enter__ = _DCBrushChanger___enter__
del _DCBrushChanger___enter__
def _DCBrushChanger___exit__(self, exc_type, exc_val, exc_tb):
    return False
DCBrushChanger.__exit__ = _DCBrushChanger___exit__
del _DCBrushChanger___exit__
def _DCPenChanger___enter__(self):
    return self
DCPenChanger.__enter__ = _DCPenChanger___enter__
del _DCPenChanger___enter__
def _DCPenChanger___exit__(self, exc_type, exc_val, exc_tb):
    return False
DCPenChanger.__exit__ = _DCPenChanger___exit__
del _DCPenChanger___exit__
def _DCTextColourChanger___enter__(self):
    return self
DCTextColourChanger.__enter__ = _DCTextColourChanger___enter__
del _DCTextColourChanger___enter__
def _DCTextColourChanger___exit__(self, exc_type, exc_val, exc_tb):
    return False
DCTextColourChanger.__exit__ = _DCTextColourChanger___exit__
del _DCTextColourChanger___exit__
def _DCFontChanger___enter__(self):
    return self
DCFontChanger.__enter__ = _DCFontChanger___enter__
del _DCFontChanger___enter__
def _DCFontChanger___exit__(self, exc_type, exc_val, exc_tb):
    return False
DCFontChanger.__exit__ = _DCFontChanger___exit__
del _DCFontChanger___exit__
def _DCTextBgColourChanger___enter__(self):
    return self
DCTextBgColourChanger.__enter__ = _DCTextBgColourChanger___enter__
del _DCTextBgColourChanger___enter__
def _DCTextBgColourChanger___exit__(self, exc_type, exc_val, exc_tb):
    return False
DCTextBgColourChanger.__exit__ = _DCTextBgColourChanger___exit__
del _DCTextBgColourChanger___exit__
def _DCTextBgModeChanger___enter__(self):
    return self
DCTextBgModeChanger.__enter__ = _DCTextBgModeChanger___enter__
del _DCTextBgModeChanger___enter__
def _DCTextBgModeChanger___exit__(self, exc_type, exc_val, exc_tb):
    return False
DCTextBgModeChanger.__exit__ = _DCTextBgModeChanger___exit__
del _DCTextBgModeChanger___exit__
GraphicsContext.DrawRotatedText = wx.deprecated(GraphicsContext.DrawText, 'Use DrawText instead.')

def _ctx_hold_ref(f):
    from functools import wraps
    @wraps(f)
    def wrapper(self, obj):
        ctx = f(self, obj)
        if ctx is not None:
            ctx._obj = obj
        return ctx
    return wrapper
GraphicsRenderer.CreateContext = _ctx_hold_ref(GraphicsRenderer.CreateContext)
GraphicsRenderer.CreateContextFromImage = _ctx_hold_ref(GraphicsRenderer.CreateContextFromImage)
GraphicsRenderer.CreateContextFromUnknownDC = _ctx_hold_ref(GraphicsRenderer.CreateContextFromUnknownDC)

def _GraphicsRenderer_GetType(self):
    """
    Returns the name of the GraphicsRenderer class.
    """
    return self.GetClassInfo().GetClassName()
GraphicsRenderer.GetType = _GraphicsRenderer_GetType
del _GraphicsRenderer_GetType
GraphicsRenderer.Type = property(GraphicsRenderer.GetType)
def _PixelDataBase___iter__(self):
    """
    Create and return an iterator/generator object for traversing
    this pixel data object.
    """
    width  = self.GetWidth()
    height = self.GetHeight()
    pixels = self.GetPixels() # this is the C++ iterator
    
    # This class is a facade over the pixels object (using the one
    # in the enclosing scope) that only allows Get() and Set() to
    # be called.
    class PixelFacade(object):
        def Get(self):
            return pixels.Get()
        def Set(self, *args, **kw):
            return pixels.Set(*args, **kw)
        def __str__(self):
            return str(self.Get())
        def __repr__(self):
            return 'pixel(%d,%d): %s' % (x,y,self.Get())
        X = property(lambda self: x)
        Y = property(lambda self: y)
    
    pf = PixelFacade()
    for y in range(height):
        pixels.MoveTo(self, 0, y)
        for x in range(width):
            # We always generate the same pf instance, but it
            # accesses the pixels object which we use to iterate
            # over the pixel buffer.
            yield pf
            pixels.nextPixel()
PixelDataBase.__iter__ = _PixelDataBase___iter__
del _PixelDataBase___iter__
@wx.deprecated
def GetAccelFromString(label):
    accel = wx.AcceleratorEntry()
    accel.FromString(label)
    return accel

def _LogNull___enter__(self):
    return self
LogNull.__enter__ = _LogNull___enter__
del _LogNull___enter__
def _LogNull___exit__(self, exc_type, exc_val, exc_tb):
    return False
LogNull.__exit__ = _LogNull___exit__
del _LogNull___exit__
def CustomDataFormat(format):
    return wx.DataFormat(format)
CustomDataFormat = wx.deprecated(CustomDataFormat, "Use wx.DataFormat instead.")

PyDataObjectSimple = wx.deprecated(DataObjectSimple), 'Use DataObjectSimple instead.'

PyTextDataObject = wx.deprecated(TextDataObject, 'Use TextDataObject instead.')

PyBitmapDataObject = wx.deprecated(BitmapDataObject, 'Use TextDataObject instead.')

PyDropTarget = wx.deprecated(DropTarget, 'Use DropTarget instead.')

# Since wxTheClipboard is not really a global variable (it is a macro
# that calls the Get static method) we can't declare it as a global
# variable for the wrapper generator, otherwise it will try to run the
# function at module import and the wxApp object won't exist yet.  So
# we'll use a class that will allow us to delay calling the Get until
# wx.TheClipboard is actually being used for the first time.
class _wxPyDelayedInitWrapper(object):
    def __init__(self, initfunc, *args, **kwargs):
        self._initfunc = initfunc
        self._args = args
        self._kwargs = kwargs
        self._instance = None
    def _checkInstance(self):
        if self._instance is None:
            if wx.GetApp():
                self._instance = self._initfunc(*self._args, **self._kwargs)
    def __getattr__(self, name):
        self._checkInstance()
        return getattr(self._instance, name)
    def __repr__(self):
        self._checkInstance()
        return repr(self._instance)

    # context manager methods
    def __enter__(self):
        self._checkInstance()
        if not self.Open():
            raise RuntimeError('Unable to open clipboard.')
        return self
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.Close()

TheClipboard = _wxPyDelayedInitWrapper(Clipboard.Get)

def _ConfigBase_ReadInt(self, key, defaultVal=0):
    rv = self._cpp_ReadInt(key, defaultVal)
    return rv
ConfigBase.ReadInt = _ConfigBase_ReadInt
del _ConfigBase_ReadInt
def _ConfigPathChanger___enter__(self):
    return self
ConfigPathChanger.__enter__ = _ConfigPathChanger___enter__
del _ConfigPathChanger___enter__
def _ConfigPathChanger___exit__(self, exc_type, exc_val, exc_tb):
    return False
ConfigPathChanger.__exit__ = _ConfigPathChanger___exit__
del _ConfigPathChanger___exit__
# For 2.8 compatibility
KeyboardState.m_controlDown = wx.deprecated(KeyboardState.controlDown, "Use controlDown instead.")
KeyboardState.m_shiftDown   = wx.deprecated(KeyboardState.shiftDown, "Use shiftDown instead.")
KeyboardState.m_altDown     = wx.deprecated(KeyboardState.altDown, "Use altDown instead.")
KeyboardState.m_metaDown    = wx.deprecated(KeyboardState.metaDown, "Use metaDown instead.")

def _EvtHandler_Bind(self, event, handler, source=None, id=wx.ID_ANY, id2=wx.ID_ANY):
    """
    Bind an event to an event handler.
    
    :param event: One of the ``EVT_*`` event binder objects that
                  specifies the type of event to bind.
    
    :param handler: A callable object to be invoked when the
                    event is delivered to self.  Pass ``None`` to
                    disconnect an event handler.
    
    :param source: Sometimes the event originates from a
                   different window than self, but you still
                   want to catch it in self.  (For example, a
                   button event delivered to a frame.)  By
                   passing the source of the event, the event
                   handling system is able to differentiate
                   between the same event type from different
                   controls.
    
    :param id: Used to spcify the event source by ID instead
               of instance.
    
    :param id2: Used when it is desirable to bind a handler
                to a range of IDs, such as with EVT_MENU_RANGE.
    """
    assert isinstance(event, wx.PyEventBinder)
    assert callable(handler) or handler is None
    assert source is None or hasattr(source, 'GetId')
    if source is not None:
        id  = source.GetId()
    event.Bind(self, id, id2, handler)
EvtHandler.Bind = _EvtHandler_Bind
del _EvtHandler_Bind
def _EvtHandler_Unbind(self, event, source=None, id=wx.ID_ANY, id2=wx.ID_ANY, handler=None):
    """
    Disconnects the event handler binding for event from `self`.
    Returns ``True`` if successful.
    """
    if source is not None:
        id  = source.GetId()
    return event.Unbind(self, id, id2, handler)
EvtHandler.Unbind = _EvtHandler_Unbind
del _EvtHandler_Unbind
def _EventBlocker___enter__(self):
    return self
EventBlocker.__enter__ = _EventBlocker___enter__
del _EventBlocker___enter__
def _EventBlocker___exit__(self, exc_type, exc_val, exc_tb):
    return False
EventBlocker.__exit__ = _EventBlocker___exit__
del _EventBlocker___exit__
def _PropagationDisabler___enter__(self):
    return self
PropagationDisabler.__enter__ = _PropagationDisabler___enter__
del _PropagationDisabler___enter__
def _PropagationDisabler___exit__(self, exc_type, exc_val, exc_tb):
    return False
PropagationDisabler.__exit__ = _PropagationDisabler___exit__
del _PropagationDisabler___exit__
def _PropagateOnce___enter__(self):
    return self
PropagateOnce.__enter__ = _PropagateOnce___enter__
del _PropagateOnce___enter__
def _PropagateOnce___exit__(self, exc_type, exc_val, exc_tb):
    return False
PropagateOnce.__exit__ = _PropagateOnce___exit__
del _PropagateOnce___exit__
def _CommandEvent_GetClientObject(self):
    """
    Alias for :meth:`GetClientData`
    """
    return self.GetClientData()
CommandEvent.GetClientObject = _CommandEvent_GetClientObject
del _CommandEvent_GetClientObject
def _CommandEvent_SetClientObject(self, data):
    """
    Alias for :meth:`SetClientData`
    """
    self.SetClientData(data)
CommandEvent.SetClientObject = _CommandEvent_SetClientObject
del _CommandEvent_SetClientObject
CommandEvent.ClientData = property(CommandEvent.GetClientData, CommandEvent.SetClientData)
class PyEventBinder(object):
    """
    Instances of this class are used to bind specific events to event handlers.
    """
    def __init__(self, evtType, expectedIDs=0):
        if expectedIDs not in [0, 1, 2]:
            raise ValueError("Invalid number of expectedIDs")
        self.expectedIDs = expectedIDs
        
        if isinstance(evtType, (list, tuple)):
            self.evtType = list(evtType)
        else:
            self.evtType = [evtType]

    def Bind(self, target, id1, id2, function):
        """
        Bind this set of event types to target using its Connect() method.
        """
        for et in self.evtType:
            target.Connect(id1, id2, et, function)

    def Unbind(self, target, id1, id2, handler=None):
        """
        Remove an event binding.
        """
        success = 0
        for et in self.evtType:
            success += int(target.Disconnect(id1, id2, et, handler))
        return success != 0

    def _getEvtType(self):
        """
        Make it easy to get to the default wxEventType typeID for this
        event binder.
        """
        return self.evtType[0]

    typeId = property(_getEvtType)
    @wx.deprecatedMsg("Use :meth:`EvtHandler.Bind` instead.")
    def __call__(self, *args):
        """
        For backwards compatibility with the old ``EVT_*`` functions.
        Should be called with either (window, func), (window, ID,
        func) or (window, ID1, ID2, func) parameters depending on the
        type of the event.
        """
        assert len(args) == 2 + self.expectedIDs
        id1 = ID_ANY
        id2 = ID_ANY
        target = args[0]
        if self.expectedIDs == 0:
            func = args[1]
        elif self.expectedIDs == 1:
            id1 = args[1]
            func = args[2]
        elif self.expectedIDs == 2:
            id1 = args[1]
            id2 = args[2]
            func = args[3]
        else:
            raise ValueError("Unexpected number of IDs")
        
        self.Bind(target, id1, id2, func)


#-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=
# This code block was included from src/event_ex.py
# Create some event binders
EVT_SIZE = wx.PyEventBinder( wxEVT_SIZE )
EVT_SIZING = wx.PyEventBinder( wxEVT_SIZING )
EVT_MOVE = wx.PyEventBinder( wxEVT_MOVE )
EVT_MOVING = wx.PyEventBinder( wxEVT_MOVING )
EVT_MOVE_START = wx.PyEventBinder( wxEVT_MOVE_START )
EVT_MOVE_END = wx.PyEventBinder( wxEVT_MOVE_END )
EVT_CLOSE = wx.PyEventBinder( wxEVT_CLOSE_WINDOW )
EVT_END_SESSION = wx.PyEventBinder( wxEVT_END_SESSION )
EVT_QUERY_END_SESSION = wx.PyEventBinder( wxEVT_QUERY_END_SESSION )
EVT_PAINT = wx.PyEventBinder( wxEVT_PAINT )
EVT_NC_PAINT = wx.PyEventBinder( wxEVT_NC_PAINT )
EVT_ERASE_BACKGROUND = wx.PyEventBinder( wxEVT_ERASE_BACKGROUND )
EVT_CHAR = wx.PyEventBinder( wxEVT_CHAR )
EVT_KEY_DOWN = wx.PyEventBinder( wxEVT_KEY_DOWN )
EVT_KEY_UP = wx.PyEventBinder( wxEVT_KEY_UP )
EVT_HOTKEY = wx.PyEventBinder( wxEVT_HOTKEY, 1)
EVT_CHAR_HOOK = wx.PyEventBinder( wxEVT_CHAR_HOOK )
EVT_MENU_OPEN = wx.PyEventBinder( wxEVT_MENU_OPEN )
EVT_MENU_CLOSE = wx.PyEventBinder( wxEVT_MENU_CLOSE )
EVT_MENU_HIGHLIGHT = wx.PyEventBinder( wxEVT_MENU_HIGHLIGHT, 1)
EVT_MENU_HIGHLIGHT_ALL = wx.PyEventBinder( wxEVT_MENU_HIGHLIGHT )
EVT_SET_FOCUS = wx.PyEventBinder( wxEVT_SET_FOCUS )
EVT_KILL_FOCUS = wx.PyEventBinder( wxEVT_KILL_FOCUS )
EVT_CHILD_FOCUS = wx.PyEventBinder( wxEVT_CHILD_FOCUS )
EVT_ACTIVATE = wx.PyEventBinder( wxEVT_ACTIVATE )
EVT_ACTIVATE_APP = wx.PyEventBinder( wxEVT_ACTIVATE_APP )
EVT_HIBERNATE = wx.PyEventBinder( wxEVT_HIBERNATE )
EVT_DROP_FILES = wx.PyEventBinder( wxEVT_DROP_FILES )
EVT_INIT_DIALOG = wx.PyEventBinder( wxEVT_INIT_DIALOG )
EVT_SYS_COLOUR_CHANGED = wx.PyEventBinder( wxEVT_SYS_COLOUR_CHANGED )
EVT_DISPLAY_CHANGED = wx.PyEventBinder( wxEVT_DISPLAY_CHANGED )
EVT_DPI_CHANGED = wx.PyEventBinder( wxEVT_DPI_CHANGED )
EVT_SHOW = wx.PyEventBinder( wxEVT_SHOW )
EVT_MAXIMIZE = wx.PyEventBinder( wxEVT_MAXIMIZE )
EVT_ICONIZE = wx.PyEventBinder( wxEVT_ICONIZE )
EVT_NAVIGATION_KEY = wx.PyEventBinder( wxEVT_NAVIGATION_KEY )
EVT_PALETTE_CHANGED = wx.PyEventBinder( wxEVT_PALETTE_CHANGED )
EVT_QUERY_NEW_PALETTE = wx.PyEventBinder( wxEVT_QUERY_NEW_PALETTE )
EVT_WINDOW_CREATE = wx.PyEventBinder( wxEVT_CREATE )
EVT_WINDOW_DESTROY = wx.PyEventBinder( wxEVT_DESTROY )
EVT_SET_CURSOR = wx.PyEventBinder( wxEVT_SET_CURSOR )
EVT_MOUSE_CAPTURE_CHANGED = wx.PyEventBinder( wxEVT_MOUSE_CAPTURE_CHANGED )
EVT_MOUSE_CAPTURE_LOST = wx.PyEventBinder( wxEVT_MOUSE_CAPTURE_LOST )

EVT_LEFT_DOWN = wx.PyEventBinder( wxEVT_LEFT_DOWN )
EVT_LEFT_UP = wx.PyEventBinder( wxEVT_LEFT_UP )
EVT_MIDDLE_DOWN = wx.PyEventBinder( wxEVT_MIDDLE_DOWN )
EVT_MIDDLE_UP = wx.PyEventBinder( wxEVT_MIDDLE_UP )
EVT_RIGHT_DOWN = wx.PyEventBinder( wxEVT_RIGHT_DOWN )
EVT_RIGHT_UP = wx.PyEventBinder( wxEVT_RIGHT_UP )
EVT_MOTION = wx.PyEventBinder( wxEVT_MOTION )
EVT_LEFT_DCLICK = wx.PyEventBinder( wxEVT_LEFT_DCLICK )
EVT_MIDDLE_DCLICK = wx.PyEventBinder( wxEVT_MIDDLE_DCLICK )
EVT_RIGHT_DCLICK = wx.PyEventBinder( wxEVT_RIGHT_DCLICK )
EVT_LEAVE_WINDOW = wx.PyEventBinder( wxEVT_LEAVE_WINDOW )
EVT_ENTER_WINDOW = wx.PyEventBinder( wxEVT_ENTER_WINDOW )
EVT_MOUSEWHEEL = wx.PyEventBinder( wxEVT_MOUSEWHEEL )
EVT_MOUSE_AUX1_DOWN = wx.PyEventBinder( wxEVT_AUX1_DOWN )
EVT_MOUSE_AUX1_UP = wx.PyEventBinder( wxEVT_AUX1_UP )
EVT_MOUSE_AUX1_DCLICK = wx.PyEventBinder( wxEVT_AUX1_DCLICK )
EVT_MOUSE_AUX2_DOWN = wx.PyEventBinder( wxEVT_AUX2_DOWN )
EVT_MOUSE_AUX2_UP = wx.PyEventBinder( wxEVT_AUX2_UP )
EVT_MOUSE_AUX2_DCLICK = wx.PyEventBinder( wxEVT_AUX2_DCLICK )

EVT_MOUSE_EVENTS = wx.PyEventBinder([ wxEVT_LEFT_DOWN,
                                      wxEVT_LEFT_UP,
                                      wxEVT_MIDDLE_DOWN,
                                      wxEVT_MIDDLE_UP,
                                      wxEVT_RIGHT_DOWN,
                                      wxEVT_RIGHT_UP,
                                      wxEVT_MOTION,
                                      wxEVT_LEFT_DCLICK,
                                      wxEVT_MIDDLE_DCLICK,
                                      wxEVT_RIGHT_DCLICK,
                                      wxEVT_ENTER_WINDOW,
                                      wxEVT_LEAVE_WINDOW,
                                      wxEVT_MOUSEWHEEL,
                                      wxEVT_AUX1_DOWN,
                                      wxEVT_AUX1_UP,
                                      wxEVT_AUX1_DCLICK,
                                      wxEVT_AUX2_DOWN,
                                      wxEVT_AUX2_UP,
                                      wxEVT_AUX2_DCLICK,
                                     ])
EVT_MAGNIFY = wx.PyEventBinder( wxEVT_MAGNIFY )


# Scrolling from wxWindow (sent to wxScrolledWindow)
EVT_SCROLLWIN = wx.PyEventBinder([ wxEVT_SCROLLWIN_TOP,
                                  wxEVT_SCROLLWIN_BOTTOM,
                                  wxEVT_SCROLLWIN_LINEUP,
                                  wxEVT_SCROLLWIN_LINEDOWN,
                                  wxEVT_SCROLLWIN_PAGEUP,
                                  wxEVT_SCROLLWIN_PAGEDOWN,
                                  wxEVT_SCROLLWIN_THUMBTRACK,
                                  wxEVT_SCROLLWIN_THUMBRELEASE,
                                  ])

EVT_SCROLLWIN_TOP = wx.PyEventBinder( wxEVT_SCROLLWIN_TOP )
EVT_SCROLLWIN_BOTTOM = wx.PyEventBinder( wxEVT_SCROLLWIN_BOTTOM )
EVT_SCROLLWIN_LINEUP = wx.PyEventBinder( wxEVT_SCROLLWIN_LINEUP )
EVT_SCROLLWIN_LINEDOWN = wx.PyEventBinder( wxEVT_SCROLLWIN_LINEDOWN )
EVT_SCROLLWIN_PAGEUP = wx.PyEventBinder( wxEVT_SCROLLWIN_PAGEUP )
EVT_SCROLLWIN_PAGEDOWN = wx.PyEventBinder( wxEVT_SCROLLWIN_PAGEDOWN )
EVT_SCROLLWIN_THUMBTRACK = wx.PyEventBinder( wxEVT_SCROLLWIN_THUMBTRACK )
EVT_SCROLLWIN_THUMBRELEASE = wx.PyEventBinder( wxEVT_SCROLLWIN_THUMBRELEASE )

# Scrolling from wx.Slider and wx.ScrollBar
EVT_SCROLL = wx.PyEventBinder([ wxEVT_SCROLL_TOP,
                               wxEVT_SCROLL_BOTTOM,
                               wxEVT_SCROLL_LINEUP,
                               wxEVT_SCROLL_LINEDOWN,
                               wxEVT_SCROLL_PAGEUP,
                               wxEVT_SCROLL_PAGEDOWN,
                               wxEVT_SCROLL_THUMBTRACK,
                               wxEVT_SCROLL_THUMBRELEASE,
                               wxEVT_SCROLL_CHANGED,
                               ])

EVT_SCROLL_TOP = wx.PyEventBinder( wxEVT_SCROLL_TOP )
EVT_SCROLL_BOTTOM = wx.PyEventBinder( wxEVT_SCROLL_BOTTOM )
EVT_SCROLL_LINEUP = wx.PyEventBinder( wxEVT_SCROLL_LINEUP )
EVT_SCROLL_LINEDOWN = wx.PyEventBinder( wxEVT_SCROLL_LINEDOWN )
EVT_SCROLL_PAGEUP = wx.PyEventBinder( wxEVT_SCROLL_PAGEUP )
EVT_SCROLL_PAGEDOWN = wx.PyEventBinder( wxEVT_SCROLL_PAGEDOWN )
EVT_SCROLL_THUMBTRACK = wx.PyEventBinder( wxEVT_SCROLL_THUMBTRACK )
EVT_SCROLL_THUMBRELEASE = wx.PyEventBinder( wxEVT_SCROLL_THUMBRELEASE )
EVT_SCROLL_CHANGED = wx.PyEventBinder( wxEVT_SCROLL_CHANGED )
EVT_SCROLL_ENDSCROLL = EVT_SCROLL_CHANGED

# Scrolling from wx.Slider and wx.ScrollBar, with an id
EVT_COMMAND_SCROLL = wx.PyEventBinder([ wxEVT_SCROLL_TOP,
                                       wxEVT_SCROLL_BOTTOM,
                                       wxEVT_SCROLL_LINEUP,
                                       wxEVT_SCROLL_LINEDOWN,
                                       wxEVT_SCROLL_PAGEUP,
                                       wxEVT_SCROLL_PAGEDOWN,
                                       wxEVT_SCROLL_THUMBTRACK,
                                       wxEVT_SCROLL_THUMBRELEASE,
                                       wxEVT_SCROLL_CHANGED,
                                       ], 1)

EVT_COMMAND_SCROLL_TOP = wx.PyEventBinder( wxEVT_SCROLL_TOP, 1)
EVT_COMMAND_SCROLL_BOTTOM = wx.PyEventBinder( wxEVT_SCROLL_BOTTOM, 1)
EVT_COMMAND_SCROLL_LINEUP = wx.PyEventBinder( wxEVT_SCROLL_LINEUP, 1)
EVT_COMMAND_SCROLL_LINEDOWN = wx.PyEventBinder( wxEVT_SCROLL_LINEDOWN, 1)
EVT_COMMAND_SCROLL_PAGEUP = wx.PyEventBinder( wxEVT_SCROLL_PAGEUP, 1)
EVT_COMMAND_SCROLL_PAGEDOWN = wx.PyEventBinder( wxEVT_SCROLL_PAGEDOWN, 1)
EVT_COMMAND_SCROLL_THUMBTRACK = wx.PyEventBinder( wxEVT_SCROLL_THUMBTRACK, 1)
EVT_COMMAND_SCROLL_THUMBRELEASE = wx.PyEventBinder( wxEVT_SCROLL_THUMBRELEASE, 1)
EVT_COMMAND_SCROLL_CHANGED = wx.PyEventBinder( wxEVT_SCROLL_CHANGED, 1)
EVT_COMMAND_SCROLL_ENDSCROLL = EVT_COMMAND_SCROLL_CHANGED

EVT_BUTTON = wx.PyEventBinder( wxEVT_BUTTON, 1)
EVT_CHECKBOX = wx.PyEventBinder( wxEVT_CHECKBOX, 1)
EVT_CHOICE = wx.PyEventBinder( wxEVT_CHOICE, 1)
EVT_LISTBOX = wx.PyEventBinder( wxEVT_LISTBOX, 1)
EVT_LISTBOX_DCLICK = wx.PyEventBinder( wxEVT_LISTBOX_DCLICK, 1)
EVT_MENU = wx.PyEventBinder( wxEVT_MENU, 1)
EVT_MENU_RANGE = wx.PyEventBinder( wxEVT_MENU, 2)
EVT_SLIDER = wx.PyEventBinder( wxEVT_SLIDER, 1)
EVT_RADIOBOX = wx.PyEventBinder( wxEVT_RADIOBOX, 1)
EVT_RADIOBUTTON = wx.PyEventBinder( wxEVT_RADIOBUTTON, 1)

EVT_SCROLLBAR = wx.PyEventBinder( wxEVT_SCROLLBAR, 1)
EVT_VLBOX = wx.PyEventBinder( wxEVT_VLBOX, 1)
EVT_COMBOBOX = wx.PyEventBinder( wxEVT_COMBOBOX, 1)
EVT_TOOL = wx.PyEventBinder( wxEVT_TOOL, 1)
EVT_TOOL_RANGE = wx.PyEventBinder( wxEVT_TOOL, 2)
EVT_TOOL_RCLICKED = wx.PyEventBinder( wxEVT_TOOL_RCLICKED, 1)
EVT_TOOL_RCLICKED_RANGE = wx.PyEventBinder( wxEVT_TOOL_RCLICKED, 2)
EVT_TOOL_ENTER = wx.PyEventBinder( wxEVT_TOOL_ENTER, 1)
EVT_TOOL_DROPDOWN = wx.PyEventBinder( wxEVT_TOOL_DROPDOWN, 1)
EVT_CHECKLISTBOX = wx.PyEventBinder( wxEVT_CHECKLISTBOX, 1)
EVT_COMBOBOX_DROPDOWN = wx.PyEventBinder( wxEVT_COMBOBOX_DROPDOWN , 1)
EVT_COMBOBOX_CLOSEUP  = wx.PyEventBinder( wxEVT_COMBOBOX_CLOSEUP , 1)

EVT_COMMAND_LEFT_CLICK = wx.PyEventBinder( wxEVT_COMMAND_LEFT_CLICK, 1)
EVT_COMMAND_LEFT_DCLICK = wx.PyEventBinder( wxEVT_COMMAND_LEFT_DCLICK, 1)
EVT_COMMAND_RIGHT_CLICK = wx.PyEventBinder( wxEVT_COMMAND_RIGHT_CLICK, 1)
EVT_COMMAND_RIGHT_DCLICK = wx.PyEventBinder( wxEVT_COMMAND_RIGHT_DCLICK, 1)
EVT_COMMAND_SET_FOCUS = wx.PyEventBinder( wxEVT_COMMAND_SET_FOCUS, 1)
EVT_COMMAND_KILL_FOCUS = wx.PyEventBinder( wxEVT_COMMAND_KILL_FOCUS, 1)
EVT_COMMAND_ENTER = wx.PyEventBinder( wxEVT_COMMAND_ENTER, 1)

EVT_HELP = wx.PyEventBinder( wxEVT_HELP, 1)
EVT_HELP_RANGE = wx.PyEventBinder(  wxEVT_HELP, 2)
EVT_DETAILED_HELP = wx.PyEventBinder( wxEVT_DETAILED_HELP, 1)
EVT_DETAILED_HELP_RANGE = wx.PyEventBinder( wxEVT_DETAILED_HELP, 2)

EVT_IDLE = wx.PyEventBinder( wxEVT_IDLE )

EVT_UPDATE_UI = wx.PyEventBinder( wxEVT_UPDATE_UI, 1)
EVT_UPDATE_UI_RANGE = wx.PyEventBinder( wxEVT_UPDATE_UI, 2)

EVT_CONTEXT_MENU = wx.PyEventBinder( wxEVT_CONTEXT_MENU )

EVT_THREAD = wx.PyEventBinder( wxEVT_THREAD )

EVT_WINDOW_MODAL_DIALOG_CLOSED = wx.PyEventBinder( wxEVT_WINDOW_MODAL_DIALOG_CLOSED )

EVT_JOY_BUTTON_DOWN = wx.PyEventBinder( wxEVT_JOY_BUTTON_DOWN )
EVT_JOY_BUTTON_UP = wx.PyEventBinder( wxEVT_JOY_BUTTON_UP )
EVT_JOY_MOVE = wx.PyEventBinder( wxEVT_JOY_MOVE )
EVT_JOY_ZMOVE = wx.PyEventBinder( wxEVT_JOY_ZMOVE )
EVT_JOYSTICK_EVENTS = wx.PyEventBinder([ wxEVT_JOY_BUTTON_DOWN,
                                        wxEVT_JOY_BUTTON_UP,
                                        wxEVT_JOY_MOVE,
                                        wxEVT_JOY_ZMOVE,
                                        ])

EVT_GESTURE_PAN = wx.PyEventBinder( wxEVT_GESTURE_PAN )
EVT_GESTURE_ZOOM = wx.PyEventBinder( wxEVT_GESTURE_ZOOM )
EVT_GESTURE_ROTATE = wx.PyEventBinder( wxEVT_GESTURE_ROTATE )
EVT_TWO_FINGER_TAP = wx.PyEventBinder( wxEVT_TWO_FINGER_TAP )
EVT_LONG_PRESS = wx.PyEventBinder( wxEVT_LONG_PRESS )
EVT_PRESS_AND_TAP = wx.PyEventBinder( wxEVT_PRESS_AND_TAP )

EVT_CLIPBOARD_CHANGED = wx.PyEventBinder(wxEVT_CLIPBOARD_CHANGED, 1)

EVT_FULLSCREEN = wx.PyEventBinder(wxEVT_FULLSCREEN, 1)

# deprecated wxEVT aliases
wxEVT_COMMAND_BUTTON_CLICKED         = wxEVT_BUTTON
wxEVT_COMMAND_CHECKBOX_CLICKED       = wxEVT_CHECKBOX
wxEVT_COMMAND_CHOICE_SELECTED        = wxEVT_CHOICE
wxEVT_COMMAND_LISTBOX_SELECTED       = wxEVT_LISTBOX
wxEVT_COMMAND_LISTBOX_DOUBLECLICKED  = wxEVT_LISTBOX_DCLICK
wxEVT_COMMAND_CHECKLISTBOX_TOGGLED   = wxEVT_CHECKLISTBOX
wxEVT_COMMAND_MENU_SELECTED          = wxEVT_MENU
wxEVT_COMMAND_TOOL_CLICKED           = wxEVT_TOOL
wxEVT_COMMAND_SLIDER_UPDATED         = wxEVT_SLIDER
wxEVT_COMMAND_RADIOBOX_SELECTED      = wxEVT_RADIOBOX
wxEVT_COMMAND_RADIOBUTTON_SELECTED   = wxEVT_RADIOBUTTON
wxEVT_COMMAND_SCROLLBAR_UPDATED      = wxEVT_SCROLLBAR
wxEVT_COMMAND_VLBOX_SELECTED         = wxEVT_VLBOX
wxEVT_COMMAND_COMBOBOX_SELECTED      = wxEVT_COMBOBOX
wxEVT_COMMAND_TOOL_RCLICKED          = wxEVT_TOOL_RCLICKED
wxEVT_COMMAND_TOOL_DROPDOWN_CLICKED  = wxEVT_TOOL_DROPDOWN
wxEVT_COMMAND_TOOL_ENTER             = wxEVT_TOOL_ENTER
wxEVT_COMMAND_COMBOBOX_DROPDOWN      = wxEVT_COMBOBOX_DROPDOWN
wxEVT_COMMAND_COMBOBOX_CLOSEUP       = wxEVT_COMBOBOX_CLOSEUP

# End of included code block
#-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=

PyEvtHandler = wx.deprecated(EvtHandler, "Use :class:`EvtHandler` instead.")

def _PyEvent_Clone(self):
    """
    Make a new instance of the event that is a copy of self.
    
    Through the magic of Python this implementation should work for
    this and all derived classes.
    """
    # Create a new instance of the same type as this instance and
    # then invoke the C++ copy constructor to copy the C++ parts and
    # any custom attributes.
    clone = wx.PyEvent.__new__(self.__class__)
    wx.PyEvent.__init__(clone, self)
    return clone
PyEvent.Clone = _PyEvent_Clone
del _PyEvent_Clone
def _PyCommandEvent_Clone(self):
    """
    Make a new instance of the event that is a copy of self.
    
    Through the magic of Python this implementation should work for
    this and all derived classes.
    """
    # Create a new instance of the same type as this instance and
    # then invoke the C++ copy constructor to copy the C++ parts and
    # any custom attributes.
    clone = wx.PyCommandEvent.__new__(self.__class__)
    wx.PyCommandEvent.__init__(clone, self)
    return clone
PyCommandEvent.Clone = _PyCommandEvent_Clone
del _PyCommandEvent_Clone
def _Sizer_AddMany(self, items):
    """
    :meth:`AddMany` is a convenience method for adding several items to a sizer
    at one time. Simply pass it a list of tuples, where each tuple
    consists of the parameters that you would normally pass to the :meth:`Add`
    method.
    """
    for item in items:
        if not isinstance(item, (tuple, list)):
            item = (item, )
        self.Add(*item)
Sizer.AddMany = _Sizer_AddMany
del _Sizer_AddMany
def _Sizer___nonzero__(self):
    """
    Can be used to test if the C++ part of the sizer still exists, with 
    code like this::
    
        if theSizer:
            doSomething()
    """
    import wx.siplib
    return not wx.siplib.isdeleted(self)
Sizer.__nonzero__ = _Sizer___nonzero__
del _Sizer___nonzero__
def _Sizer___iter__(self):
    """
    A Python convenience method that allows Sizers to act as iterables that will yield their wx.SizerItems.
    """
    for item in self.GetChildren(): yield item
Sizer.__iter__ = _Sizer___iter__
del _Sizer___iter__
Sizer.__bool__ = Sizer.__nonzero__

def _GridSizer_CalcRowsCols(self):
    """
    CalcRowsCols() -> (rows, cols)
    
    Calculates how many rows and columns will be in the sizer based
    on the current number of items and also the rows, cols specified
    in the constructor.
    """
    nitems = len(self.GetChildren())
    rows = self.GetRows()
    cols = self.GetCols()
    assert rows != 0 or cols != 0, "Grid sizer must have either rows or columns fixed"
    if cols != 0:
        rows = (nitems + cols - 1) / cols
    elif rows != 0:
        cols = (nitems + rows - 1) / rows
    return (rows, cols)
GridSizer.CalcRowsCols = _GridSizer_CalcRowsCols
del _GridSizer_CalcRowsCols
PySizer = wx.deprecated(Sizer, 'Use Sizer instead.')

def _SizerItemList___repr__(self):
    return "SizerItemList: " + repr(list(self))
SizerItemList.__repr__ = _SizerItemList___repr__
del _SizerItemList___repr__
def _GBPosition_GetIM(self):
    """
    Returns an immutable representation of the ``wx.GBPosition`` object, based on ``namedtuple``.
    
    This new object is hashable and can be used as a dictionary key,
    be added to sets, etc.  It can be converted back into a real ``wx.GBPosition``
    with a simple statement like this: ``obj = wx.GBPosition(imObj)``.
    """
    return _im_GBPosition(*self.Get())
GBPosition.GetIM = _GBPosition_GetIM
del _GBPosition_GetIM
def _GBPosition___str__(self):
    return str(self.Get())
GBPosition.__str__ = _GBPosition___str__
del _GBPosition___str__
def _GBPosition___repr__(self):
    return "wx.GBPosition"+str(self.Get())
GBPosition.__repr__ = _GBPosition___repr__
del _GBPosition___repr__
def _GBPosition___len__(self):
    return len(self.Get())
GBPosition.__len__ = _GBPosition___len__
del _GBPosition___len__
def _GBPosition___nonzero__(self):
    return self.Get() != (0,0)
GBPosition.__nonzero__ = _GBPosition___nonzero__
del _GBPosition___nonzero__
def _GBPosition___bool__(self):
    return self.Get() != (0,0)
GBPosition.__bool__ = _GBPosition___bool__
del _GBPosition___bool__
def _GBPosition___reduce__(self):
    return (GBPosition, self.Get())
GBPosition.__reduce__ = _GBPosition___reduce__
del _GBPosition___reduce__
def _GBPosition___getitem__(self, idx):
    return self.Get()[idx]
GBPosition.__getitem__ = _GBPosition___getitem__
del _GBPosition___getitem__
def _GBPosition___setitem__(self, idx, val):
    if idx == 0: self.Row = val
    elif idx == 1: self.Col = val
    else: raise IndexError
GBPosition.__setitem__ = _GBPosition___setitem__
del _GBPosition___setitem__
GBPosition.__safe_for_unpickling__ = True

def _GBSpan_GetIM(self):
    """
    Returns an immutable representation of the ``wx.GBSpan`` object, based on ``namedtuple``.
    
    This new object is hashable and can be used as a dictionary key,
    be added to sets, etc.  It can be converted back into a real ``wx.GBSpan``
    with a simple statement like this: ``obj = wx.GBSpan(imObj)``.
    """
    return _im_GBSpan(*self.Get())
GBSpan.GetIM = _GBSpan_GetIM
del _GBSpan_GetIM
def _GBSpan___str__(self):
    return str(self.Get())
GBSpan.__str__ = _GBSpan___str__
del _GBSpan___str__
def _GBSpan___repr__(self):
    return "wx.GBSpan"+str(self.Get())
GBSpan.__repr__ = _GBSpan___repr__
del _GBSpan___repr__
def _GBSpan___len__(self):
    return len(self.Get())
GBSpan.__len__ = _GBSpan___len__
del _GBSpan___len__
def _GBSpan___nonzero__(self):
    return self.Get() != (0,0)
GBSpan.__nonzero__ = _GBSpan___nonzero__
del _GBSpan___nonzero__
def _GBSpan___bool__(self):
    return self.Get() != (0,0)
GBSpan.__bool__ = _GBSpan___bool__
del _GBSpan___bool__
def _GBSpan___reduce__(self):
    return (GBSpan, self.Get())
GBSpan.__reduce__ = _GBSpan___reduce__
del _GBSpan___reduce__
def _GBSpan___getitem__(self, idx):
    return self.Get()[idx]
GBSpan.__getitem__ = _GBSpan___getitem__
del _GBSpan___getitem__
def _GBSpan___setitem__(self, idx, val):
    if idx == 0: self.Rowspan = val
    elif idx == 1: self.Colspan = val
    else: raise IndexError
GBSpan.__setitem__ = _GBSpan___setitem__
del _GBSpan___setitem__
GBSpan.__safe_for_unpickling__ = True

GridBagSizer.CheckForIntersectionPos = wx.deprecated(GridBagSizer.CheckForIntersection, 'Use CheckForIntersection instead.')

from collections import namedtuple
_im_GBPosition = namedtuple('_im_GBPosition', ['row', 'col'])
del namedtuple

from collections import namedtuple
_im_GBSpan = namedtuple('_im_GBSpan', ['rowspan', 'colspan'])
del namedtuple

def _EventLoopActivator___enter__(self):
    return self
EventLoopActivator.__enter__ = _EventLoopActivator___enter__
del _EventLoopActivator___enter__
def _EventLoopActivator___exit__(self, exc_type, exc_val, exc_tb):
    return False
EventLoopActivator.__exit__ = _EventLoopActivator___exit__
del _EventLoopActivator___exit__
@wx.deprecatedMsg('Use GUIEventLoop instead.')
class EventLoop(GUIEventLoop):
    '''A class using the old name for compatibility.'''
    def __init__(self):
        GUIEventLoop.__init__(self)

def YieldIfNeeded():
    """
    Convenience function for wx.GetApp().Yield(True)
    """
    return wx.GetApp().Yield(True)

class PyOnDemandOutputWindow(object):
    """
    A class that can be used for redirecting Python's stdout and
    stderr streams.  It will do nothing until something is wrriten to
    the stream at which point it will create a Frame with a text area
    and write the text there.
    """
    def __init__(self, title="wxPython: stdout/stderr"):
        self.frame  = None
        self.title  = title
        self.pos    = wx.DefaultPosition
        self.size   = (450, 300)
        self.parent = None

    def SetParent(self, parent):
        """
        Set the window to be used as the popup Frame's parent.
        """
        self.parent = parent

    def CreateOutputWindow(self, txt):
        self.frame = wx.Frame(self.parent, -1, self.title, self.pos, self.size,
                              style=wx.DEFAULT_FRAME_STYLE)
        self.text  = wx.TextCtrl(self.frame, -1, "",
                                 style=wx.TE_MULTILINE|wx.TE_READONLY)
        self.text.AppendText(txt)
        self.frame.Show(True)
        self.frame.Bind(wx.EVT_CLOSE, self.OnCloseWindow)

    def OnCloseWindow(self, event):
        if self.frame is not None:
            self.frame.Destroy()
        self.frame = None
        self.text  = None
        self.parent = None

    def write(self, text):
        """
        Create the output window if needed and write the string to it.
        If not called in the context of the gui thread then CallAfter is
        used to do the work there.
        """
        if self.frame is None:
            if not wx.IsMainThread():
                wx.CallAfter(self.CreateOutputWindow, text)
            else:
                self.CreateOutputWindow(text)
        else:
            if not wx.IsMainThread():
                wx.CallAfter(self.text.AppendText, text)
            else:
                self.text.AppendText(text)

    def close(self):
        if self.frame is not None:
            wx.CallAfter(self.frame.Close)

    def flush(self):
        pass


class App(PyApp):
    """
    The ``wx.App`` class represents the application and is used to:
    
      * bootstrap the wxPython system and initialize the underlying
        gui toolkit
      * set and get application-wide properties
      * implement the native windowing system main message or event loop,
        and to dispatch events to window instances
      * etc.
    
    Every wx application must have a single ``wx.App`` instance, and all
    creation of UI objects should be delayed until after the ``wx.App`` object
    has been created in order to ensure that the gui platform and wxWidgets
    have been fully initialized.
    
    Normally you would derive from this class and implement an ``OnInit``
    method that creates a frame and then calls ``self.SetTopWindow(frame)``,
    however ``wx.App`` is also usable on its own without derivation.
    
    :note: In Python the wrapper for the C++ class ``wxApp`` has been renamed tp
        :class:`wx.PyApp`. This ``wx.App`` class derives from ``wx.PyApp``, and is
        responsible for handling the Python-specific needs for bootstrapping the
        wxWidgets library and other Python integration related requirements.
    """
    outputWindowClass = PyOnDemandOutputWindow
    def __init__(self, redirect=False, filename=None, useBestVisual=False, clearSigInt=True):
        """
        Construct a ``wx.App`` object.
        
        :param redirect: Should ``sys.stdout`` and ``sys.stderr`` be
            redirected?  Defaults to False. If ``filename`` is None
            then output will be redirected to a window that pops up
            as needed.  (You can control what kind of window is created
            for the output by resetting the class variable
            ``outputWindowClass`` to a class of your choosing.)
        
        :param filename: The name of a file to redirect output to, if
            redirect is True.
        
        :param useBestVisual: Should the app try to use the best
            available visual provided by the system (only relevant on
            systems that have more than one visual.)  This parameter
            must be used instead of calling `SetUseBestVisual` later
            on because it must be set before the underlying GUI
            toolkit is initialized.
        
        :param clearSigInt: Should SIGINT be cleared?  This allows the
            app to terminate upon a Ctrl-C in the console like other
            GUI apps will.
        
        :note: You should override OnInit to do application
            initialization to ensure that the system, toolkit and
            wxWidgets are fully initialized.
        """
        PyApp.__init__(self)
        
        # make sure we can create a GUI
        if not self.IsDisplayAvailable():
        
            if wx.Port == "__WXMAC__":
                msg = "This program needs access to the screen. Please run with a\n" \
                      "Framework build of python, and only when you are logged in\n" \
                      "on the main display of your Mac."
        
            elif wx.Port == "__WXGTK__":
                msg ="Unable to access the X Display, is $DISPLAY set properly?"
        
            else:
                msg = "Unable to create GUI"
                # TODO: more description is needed for wxMSW...
        
            raise SystemExit(msg)
        
        # This has to be done before OnInit
        self.SetUseBestVisual(useBestVisual)
        
        # Set the default handler for SIGINT.  This fixes a problem
        # where if Ctrl-C is pressed in the console that started this
        # app then it will not appear to do anything, (not even send
        # KeyboardInterrupt???)  but will later segfault on exit.  By
        # setting the default handler then the app will exit, as
        # expected (depending on platform.)
        if clearSigInt:
            try:
                import signal
                signal.signal(signal.SIGINT, signal.SIG_DFL)
            except:
                pass
        
        # Save and redirect the stdio to a window?
        self.stdioWin = None
        self.saveStdio = (_sys.stdout, _sys.stderr)
        if redirect:
            self.RedirectStdio(filename)
        
        # Use Python's install prefix as the default
        prefix = _sys.prefix
        if isinstance(prefix, (bytes, bytearray)):
            prefix = prefix.decode(_sys.getfilesystemencoding())
        wx.StandardPaths.Get().SetInstallPrefix(prefix)
        
        # Until the new native control for wxMac is up to par, still use the generic one.
        wx.SystemOptions.SetOption("mac.listctrl.always_use_generic", 1)
        
        # This finishes the initialization of wxWindows and then calls
        # the OnInit that should be present in the derived class
        self._BootstrapApp()

    def OnPreInit(self):
        """
        Things that must be done after _BootstrapApp has done its thing, but
        would be nice if they were already done by the time that OnInit is
        called.  This can be overridden in derived classes, but be sure to call
        this method from there.
        """
        wx.StockGDI._initStockObjects()
        self.InitLocale()

    def __del__(self):
        # Just in case the MainLoop was overridden without calling RestoreStio
        self.RestoreStdio()

    def SetTopWindow(self, frame):
        """
        Set the "main" top level window, which will be used for the parent of
        the on-demand output window as well as for dialogs that do not have
        an explicit parent set.
        """
        if self.stdioWin:
            self.stdioWin.SetParent(frame)
        wx.PyApp.SetTopWindow(self, frame)

    def MainLoop(self):
        """
        Execute the main GUI event loop
        """
        rv = wx.PyApp.MainLoop(self)
        self.RestoreStdio()
        return rv

    def RedirectStdio(self, filename=None):
        """
        Redirect sys.stdout and sys.stderr to a file or a popup window.
        """
        if filename:
            _sys.stdout = _sys.stderr = open(filename, 'a')
        else:
            self.stdioWin = self.outputWindowClass()
            _sys.stdout = _sys.stderr = self.stdioWin

    def RestoreStdio(self):
        try:
            _sys.stdout, _sys.stderr = self.saveStdio
        except:
            pass

    def SetOutputWindowAttributes(self, title=None, pos=None, size=None):
        """
        Set the title, position and/or size of the output window if the stdio
        has been redirected. This should be called before any output would
        cause the output window to be created.
        """
        if self.stdioWin:
            if title is not None:
                self.stdioWin.title = title
            if pos is not None:
                self.stdioWin.pos = pos
            if size is not None:
                self.stdioWin.size = size

    def InitLocale(self):
        """
        Starting with version 3.8 on Windows, Python is now setting the locale
        to what is defined by the system as the default locale. This causes
        problems with wxWidgets which expects to be able to manage the locale
        via the wx.Locale class, so the locale will be reset here to be the
        default "C" locale settings.
        
        If you have troubles from the default behavior of this method you can
        override it in a derived class to behave differently. Please report
        the problem you encountered.
        """
        if _sys.platform.startswith('win') and _sys.version_info > (3,8):
            import locale
            locale.setlocale(locale.LC_ALL, "C")

    def ResetLocale(self):
        """
        This method is now a NOP and will be deprecated.
        """
        pass

    @staticmethod
    def Get():
        """
        A staticmethod returning the currently active application object.
        Essentially just a more pythonic version of :meth:`GetApp`.
        """
        return GetApp()


@wx.deprecatedMsg("Use :class:`App` instead.")
class PySimpleApp(App):
    """
    This class is deprecated.  Please use :class:`App` instead.
    """
    def __init__(self, *args, **kw):
        App.__init__(self, *args, **kw)


EVT_TIMER = wx.PyEventBinder( wxEVT_TIMER )

class PyTimer(Timer):
    '''This timer class is passed the callable object to be called when the timer expires.'''
    def __init__(self, notify):
        Timer.__init__(self)
        self.notify = notify

    def Notify(self):
        if self.notify:
            self.notify()

def _Window_SetRect(self, rect):
    return self.SetSize(rect)
Window.SetRect = _Window_SetRect
del _Window_SetRect
Window.Rect = property(Window.GetRect, Window.SetRect)
def _Window_SetClientRect(self, rect):
    return self.SetClientSize(rect)
Window.SetClientRect = _Window_SetClientRect
del _Window_SetClientRect
Window.ClientRect = property(Window.GetClientRect, Window.SetClientRect)
Window.SetDimensions = wx.deprecated(Window.SetDimensions, 'Use SetSize instead.')

def _Window___nonzero__(self):
    """
    Can be used to test if the C++ part of the window still exists, with 
    code like this::
    
        if theWindow:
            doSomething()
    """
    import wx.siplib
    return not wx.siplib.isdeleted(self)
Window.__nonzero__ = _Window___nonzero__
del _Window___nonzero__
Window.__bool__ = Window.__nonzero__

def _Window_DestroyLater(self):
    """
    Schedules the window to be destroyed in the near future.
    
    This should be used whenever Destroy could happen too soon, such
    as when there may still be events for this window or its children
    waiting in the event queue.
    """
    self.Hide()
    wx.GetApp().ScheduleForDestruction(self)
Window.DestroyLater = _Window_DestroyLater
del _Window_DestroyLater
def _Window_DLG_UNIT(self, dlg_unit):
    """
    A convenience wrapper for :meth:`ConvertDialogToPixels`.
    """
    is_wxType = isinstance(dlg_unit, (wx.Size, wx.Point))
    pix = self.ConvertDialogToPixels(dlg_unit)
    if not is_wxType:
        pix = tuple(pix)
    return pix
Window.DLG_UNIT = _Window_DLG_UNIT
del _Window_DLG_UNIT
def _Window_PostCreate(self, pre):
    pass
Window.PostCreate = wx.deprecated(_Window_PostCreate, "PostCreate is no longer necessary.")
del _Window_PostCreate
def _Window_GetPositionTuple(self):
    return self.GetPosition()
Window.GetPositionTuple = wx.deprecated(_Window_GetPositionTuple, "Use GetPosition instead")
del _Window_GetPositionTuple
def _Window_GetSizeTuple(self):
    return self.GetSize()
Window.GetSizeTuple = wx.deprecated(_Window_GetSizeTuple, "Use GetSize instead")
del _Window_GetSizeTuple
def _Window_MoveXY(self, x, y):
    return self.Move(x, y)
Window.MoveXY = wx.deprecated(_Window_MoveXY, "Use Move instead.")
del _Window_MoveXY
def _Window_SetSizeWH(self, w, h):
    return self.SetSize(w,h)
Window.SetSizeWH = wx.deprecated(_Window_SetSizeWH, "Use SetSize instead.")
del _Window_SetSizeWH
def _Window_SetVirtualSizeWH(self, w, h):
    return self.SetVirtualSize(w,h)
Window.SetVirtualSizeWH = wx.deprecated(_Window_SetVirtualSizeWH, "Use SetVirtualSize instead.")
del _Window_SetVirtualSizeWH
def _Window_GetVirtualSizeTuple(self):
    return self.GetVirtualSize()
Window.GetVirtualSizeTuple = wx.deprecated(_Window_GetVirtualSizeTuple, "Use GetVirtualSize instead.")
del _Window_GetVirtualSizeTuple
def _Window_SetToolTipString(self, string):
    return self.SetToolTip(string)
Window.SetToolTipString = wx.deprecated(_Window_SetToolTipString, "Use SetToolTip instead.")
del _Window_SetToolTipString
def _Window_ConvertDialogPointToPixels(self, point):
    return self.ConvertDialogToPixels(point)
Window.ConvertDialogPointToPixels = wx.deprecated(_Window_ConvertDialogPointToPixels, "Use ConvertDialogToPixels instead.")
del _Window_ConvertDialogPointToPixels
def _Window_ConvertDialogSizeToPixels(self, size):
    return self.ConvertDialogToPixels(point)
Window.ConvertDialogSizeToPixels = wx.deprecated(_Window_ConvertDialogSizeToPixels, "Use ConvertDialogToPixels instead.")
del _Window_ConvertDialogSizeToPixels
def _Window_SetSizeHintsSz(self, minSize, maxSize=wx.DefaultSize, incSize=wx.DefaultSize):
    return self.SetSizeHints(minSize, maxSize, incSize)
Window.SetSizeHintsSz = wx.deprecated(_Window_SetSizeHintsSz, "Use SetSizeHints instead.")
del _Window_SetSizeHintsSz
class FrozenWindow(object):
    """
    A context manager to be used with Python 'with' statements
    that will freeze the given window for the duration of the
    with block.
    """
    def __init__(self, window):
        self._win = window
    def __enter__(self):
        self._win.Freeze()
        return self
    def __exit__(self, exc_type, exc_val, exc_tb):
        self._win.Thaw()

def DLG_UNIT(win, dlg_unit, val2=None):
    """
    Convenience function for converting a wx.Point, wx.Size or
    (x,y) in dialog units to pixels, using the given window as a
    reference.
    """
    if val2 is not None:
        dlg_unit = (dlg_unit, val2)
    is_wxType = isinstance(dlg_unit, (wx.Size, wx.Point))
    pix = win.ConvertDialogToPixels(dlg_unit)
    if not is_wxType:
        pix = tuple(pix)
    return pix

DLG_PNT = wx.deprecated(DLG_UNIT, "Use DLG_UNIT instead.")
DLG_SZE = wx.deprecated(DLG_UNIT, "Use DLG_UNIT instead.")

def _WindowList___repr__(self):
    return "WindowList: " + repr(list(self))
WindowList.__repr__ = _WindowList___repr__
del _WindowList___repr__
PyWindow = wx.deprecated(Window, 'Use Window instead.')

PyValidator = wx.deprecated(Validator, 'Use Validator instead.')

PyPanel = wx.deprecated(Panel, 'Use Panel instead.')

def _MenuItemList___repr__(self):
    return "MenuItemList: " + repr(list(self))
MenuItemList.__repr__ = _MenuItemList___repr__
del _MenuItemList___repr__
def _Menu_AppendMenu(self, id, item, subMenu, help=""):
    return self.Append(id, item, subMenu, help)
Menu.AppendMenu = wx.deprecated(_Menu_AppendMenu, "Use Append instead.")
del _Menu_AppendMenu
def _Menu_AppendItem(self, menuItem):
    return self.Append(menuItem)
Menu.AppendItem = wx.deprecated(_Menu_AppendItem, "Use Append instead.")
del _Menu_AppendItem
def _Menu_InsertMenu(self, pos, id, item, subMenu, help=""):
    return self.Insert(pos, id, item, subMenu, help)
Menu.InsertMenu = wx.deprecated(_Menu_InsertMenu, "Use Insert instead.")
del _Menu_InsertMenu
def _Menu_InsertItem(self, pos, menuItem):
    return self.Insert(pos, menuItem)
Menu.InsertItem = wx.deprecated(_Menu_InsertItem, "Use Insert instead.")
del _Menu_InsertItem
def _Menu_PrependMenu(self, id, item, subMenu, help=""):
    return self.Prepend(id, item, subMenu, help)
Menu.PrependMenu = wx.deprecated(_Menu_PrependMenu, "Use Prepend instead.")
del _Menu_PrependMenu
def _Menu_PrependItem(self, menuItem):
    return self.Prepend(menuItem)
Menu.PrependItem = wx.deprecated(_Menu_PrependItem, "Use Prepend instead.")
del _Menu_PrependItem
def _Menu_RemoveMenu(self, id, item, subMenu, help=""):
    return self.Remove(id, item, subMenu, help)
Menu.RemoveMenu = wx.deprecated(_Menu_RemoveMenu, "Use Remove instead.")
del _Menu_RemoveMenu
def _Menu_RemoveItem(self, menuItem):
    return self.Remove(menuItem)
Menu.RemoveItem = wx.deprecated(_Menu_RemoveItem, "Use Remove instead.")
del _Menu_RemoveItem
def _MenuBar_GetMenus(self):
    """
    GetMenus() -> (menu, label)
    
    Return a list of (menu, label) items for the menus in the :class:`MenuBar`.
    """
    return [(self.GetMenu(i), self.GetMenuLabel(i)) for i in range(self.GetMenuCount())]
MenuBar.GetMenus = _MenuBar_GetMenus
del _MenuBar_GetMenus
def _MenuBar_SetMenus(self, items):
    """
    SetMenus()
    
    Clear and add new menus to the :class:`MenuBar` from a list of (menu, label) items.
    """
    for i in range(self.GetMenuCount()-1, -1, -1):
        self.Remove(i)
    for m, l in items:
        self.Append(m, l)
MenuBar.SetMenus = _MenuBar_SetMenus
del _MenuBar_SetMenus
MenuBar.Menus = property(MenuBar.GetMenus, MenuBar.SetMenus)
def _MenuList___repr__(self):
    return "MenuList: " + repr(list(self))
MenuList.__repr__ = _MenuList___repr__
del _MenuList___repr__
PyScrolledWindow = wx.deprecated(ScrolledWindow, 'Use ScrolledWindow instead.')

def _VScrolledWindow_HitTest(self, *args):
    """
    Deprecated compatibility helper.
    """
    if len(args) == 2:
        x, y = args
        return self.VirtualHitTest(y)
    else:
        pt = args[0]
        return self.VirtualHitTest(pt[1])
VScrolledWindow.HitTest = wx.deprecated(_VScrolledWindow_HitTest, "Use VirtualHitTest instead.")
del _VScrolledWindow_HitTest
PyControl = wx.deprecated(Control, 'Use Control instead.')

def _ItemContainer_GetClientObject(self, n):
    """
    Alias for :meth:`GetClientData`
    """
    return self.GetClientData(n)
ItemContainer.GetClientObject = _ItemContainer_GetClientObject
del _ItemContainer_GetClientObject
def _ItemContainer_SetClientObject(self, n, data):
    """
    Alias for :meth:`SetClientData`
    """
    self.SetClientData(n, data)
ItemContainer.SetClientObject = _ItemContainer_SetClientObject
del _ItemContainer_SetClientObject
def _ItemContainer_AppendItems(self, items):
    """
    Alias for :meth:`Append`
    """
    self.Append(items)
ItemContainer.AppendItems = _ItemContainer_AppendItems
del _ItemContainer_AppendItems
def _ItemContainer_GetItems(self):
    """
    Alias for :meth:`GetStrings`
    """
    return self.GetStrings()
ItemContainer.GetItems = _ItemContainer_GetItems
del _ItemContainer_GetItems
def _ItemContainer_SetItems(self, items):
    """
    Alias for :meth:`Set`
    """
    self.Set(items)
ItemContainer.SetItems = _ItemContainer_SetItems
del _ItemContainer_SetItems
ItemContainer.Items = property(ItemContainer.GetItems, ItemContainer.SetItems)
EVT_NOTEBOOK_PAGE_CHANGED  = wx.PyEventBinder( wxEVT_NOTEBOOK_PAGE_CHANGED, 1 )
EVT_NOTEBOOK_PAGE_CHANGING = wx.PyEventBinder( wxEVT_NOTEBOOK_PAGE_CHANGING, 1 )

# Aliases for the "best book" control as described in the overview
BookCtrl =                       Notebook
wxEVT_BOOKCTRL_PAGE_CHANGED =    wxEVT_NOTEBOOK_PAGE_CHANGED
wxEVT_BOOKCTRL_PAGE_CHANGING =   wxEVT_NOTEBOOK_PAGE_CHANGING
EVT_BOOKCTRL_PAGE_CHANGED =      EVT_NOTEBOOK_PAGE_CHANGED
EVT_BOOKCTRL_PAGE_CHANGING =     EVT_NOTEBOOK_PAGE_CHANGING

# deprecated wxEVT aliases
wxEVT_COMMAND_BOOKCTRL_PAGE_CHANGED   = wxEVT_BOOKCTRL_PAGE_CHANGED
wxEVT_COMMAND_BOOKCTRL_PAGE_CHANGING  = wxEVT_BOOKCTRL_PAGE_CHANGING
wxEVT_COMMAND_NOTEBOOK_PAGE_CHANGED   = wxEVT_NOTEBOOK_PAGE_CHANGED
wxEVT_COMMAND_NOTEBOOK_PAGE_CHANGING  = wxEVT_NOTEBOOK_PAGE_CHANGING

# Add wx.NotebookPage alias, as seen in the documentation
NotebookPage = Window

EVT_SPLITTER_SASH_POS_CHANGED = wx.PyEventBinder( wxEVT_SPLITTER_SASH_POS_CHANGED, 1 )
EVT_SPLITTER_SASH_POS_CHANGING = wx.PyEventBinder( wxEVT_SPLITTER_SASH_POS_CHANGING, 1 )
EVT_SPLITTER_DOUBLECLICKED = wx.PyEventBinder( wxEVT_SPLITTER_DOUBLECLICKED, 1 )
EVT_SPLITTER_UNSPLIT = wx.PyEventBinder( wxEVT_SPLITTER_UNSPLIT, 1 )
EVT_SPLITTER_DCLICK = EVT_SPLITTER_DOUBLECLICKED

# deprecated wxEVT aliases
wxEVT_COMMAND_SPLITTER_SASH_POS_CHANGED   = wxEVT_SPLITTER_SASH_POS_CHANGED
wxEVT_COMMAND_SPLITTER_SASH_POS_CHANGING  = wxEVT_SPLITTER_SASH_POS_CHANGING
wxEVT_COMMAND_SPLITTER_DOUBLECLICKED      = wxEVT_SPLITTER_DOUBLECLICKED
wxEVT_COMMAND_SPLITTER_UNSPLIT            = wxEVT_SPLITTER_UNSPLIT

EVT_COLLAPSIBLEPANE_CHANGED = wx.PyEventBinder( wxEVT_COLLAPSIBLEPANE_CHANGED )

# deprecated wxEVT alias
wxEVT_COMMAND_COLLPANE_CHANGED  = wxEVT_COLLAPSIBLEPANE_CHANGED

EVT_TEXT        = wx.PyEventBinder( wxEVT_TEXT, 1)
EVT_TEXT_ENTER  = wx.PyEventBinder( wxEVT_TEXT_ENTER, 1)
EVT_TEXT_URL    = wx.PyEventBinder( wxEVT_TEXT_URL, 1)
EVT_TEXT_MAXLEN = wx.PyEventBinder( wxEVT_TEXT_MAXLEN, 1)
EVT_TEXT_CUT    = wx.PyEventBinder( wxEVT_TEXT_CUT )
EVT_TEXT_COPY   = wx.PyEventBinder( wxEVT_TEXT_COPY )
EVT_TEXT_PASTE  = wx.PyEventBinder( wxEVT_TEXT_PASTE )

# deprecated wxEVT aliases
wxEVT_COMMAND_TEXT_UPDATED   = wxEVT_TEXT
wxEVT_COMMAND_TEXT_ENTER     = wxEVT_TEXT_ENTER
wxEVT_COMMAND_TEXT_URL       = wxEVT_TEXT_URL
wxEVT_COMMAND_TEXT_MAXLEN    = wxEVT_TEXT_MAXLEN
wxEVT_COMMAND_TEXT_CUT       = wxEVT_TEXT_CUT
wxEVT_COMMAND_TEXT_COPY      = wxEVT_TEXT_COPY
wxEVT_COMMAND_TEXT_PASTE     = wxEVT_TEXT_PASTE

ComboBox.SetMark = wx.deprecated(ComboBox.SetTextSelection, 'Use SetTextSelection instead.')

ComboBox.GetMark = wx.deprecated(ComboBox.GetTextSelection, 'Use GetTextSelection instead.')

def _CheckListBox_GetCheckedItems(self):
    """
    GetCheckedItems()
    
    Return a sequence of integers corresponding to the checked items in
    the control, based on :meth:`IsChecked`.
    """
    return tuple([i for i in range(self.Count) if self.IsChecked(i)])
CheckListBox.GetCheckedItems = _CheckListBox_GetCheckedItems
del _CheckListBox_GetCheckedItems
def _CheckListBox_GetCheckedStrings(self):
    """
    GetCheckedStrings()
    
    Return a tuple of strings corresponding to the checked
    items of the control, based on :meth:`GetChecked`.
    """
    return tuple([self.GetString(i) for i in self.GetCheckedItems()])
CheckListBox.GetCheckedStrings = _CheckListBox_GetCheckedStrings
del _CheckListBox_GetCheckedStrings
def _CheckListBox_SetCheckedItems(self, indexes):
    """
    SetCheckedItems(indexes)
    
    Sets the checked state of items if the index of the item is
    found in the indexes sequence.
    """
    for i in indexes:
        assert 0 <= i < self.Count, "Index (%s) out of range" % i
    for i in range(self.Count):
        self.Check(i, i in indexes)
CheckListBox.SetCheckedItems = _CheckListBox_SetCheckedItems
del _CheckListBox_SetCheckedItems
def _CheckListBox_SetCheckedStrings(self, strings):
    """
    SetCheckedStrings(strings)
    
    Sets the checked state of items if the item's string is found
    in the strings sequence.
    """
    for s in strings:
        assert s in self.GetStrings(), "String ('%s') not found" % s
    for i in range(self.Count):
        self.Check(i, self.GetString(i) in strings)
CheckListBox.SetCheckedStrings = _CheckListBox_SetCheckedStrings
del _CheckListBox_SetCheckedStrings
def _CheckListBox_GetChecked(self):
    return self.GetCheckedItems()
CheckListBox.GetChecked = wx.deprecated(_CheckListBox_GetChecked, "Use GetCheckedItems instead.")
del _CheckListBox_GetChecked
def _CheckListBox_SetChecked(self, indexes):
    return self.SetCheckedItems(indexes)
CheckListBox.SetChecked = wx.deprecated(_CheckListBox_SetChecked, "Use SetCheckedItems instead.")
del _CheckListBox_SetChecked
CheckListBox.Checked = property(CheckListBox.GetChecked, CheckListBox.SetChecked)
CheckListBox.CheckedItems = property(CheckListBox.GetCheckedItems, CheckListBox.SetCheckedItems)
CheckListBox.CheckedStrings = property(CheckListBox.GetCheckedStrings, CheckListBox.SetCheckedStrings)
SettableHeaderColumn.Title = property(HeaderColumn.GetTitle, SettableHeaderColumn.SetTitle)
SettableHeaderColumn.Bitmap = property(HeaderColumn.GetBitmap, SettableHeaderColumn.SetBitmap)
SettableHeaderColumn.Width = property(HeaderColumn.GetWidth, SettableHeaderColumn.SetWidth)
SettableHeaderColumn.MinWidth = property(HeaderColumn.GetMinWidth, SettableHeaderColumn.SetMinWidth)
SettableHeaderColumn.Alignment = property(HeaderColumn.GetAlignment, SettableHeaderColumn.SetAlignment)
SettableHeaderColumn.Flags = property(HeaderColumn.GetFlags, SettableHeaderColumn.SetFlags)
SettableHeaderColumn.Resizeable = property(HeaderColumn.IsResizeable, SettableHeaderColumn.SetResizeable)
SettableHeaderColumn.Sortable = property(HeaderColumn.IsSortable, SettableHeaderColumn.SetSortable)
SettableHeaderColumn.Reorderable = property(HeaderColumn.IsReorderable, SettableHeaderColumn.SetReorderable)
SettableHeaderColumn.Hidden = property(HeaderColumn.IsHidden, SettableHeaderColumn.SetHidden)
EVT_HEADER_CLICK =              wx.PyEventBinder( wxEVT_HEADER_CLICK )
EVT_HEADER_RIGHT_CLICK =        wx.PyEventBinder( wxEVT_HEADER_RIGHT_CLICK )
EVT_HEADER_MIDDLE_CLICK =       wx.PyEventBinder( wxEVT_HEADER_MIDDLE_CLICK )
EVT_HEADER_DCLICK =             wx.PyEventBinder( wxEVT_HEADER_DCLICK )
EVT_HEADER_RIGHT_DCLICK =       wx.PyEventBinder( wxEVT_HEADER_RIGHT_DCLICK )
EVT_HEADER_MIDDLE_DCLICK =      wx.PyEventBinder( wxEVT_HEADER_MIDDLE_DCLICK )
EVT_HEADER_SEPARATOR_DCLICK =   wx.PyEventBinder( wxEVT_HEADER_SEPARATOR_DCLICK )
EVT_HEADER_BEGIN_RESIZE =       wx.PyEventBinder( wxEVT_HEADER_BEGIN_RESIZE )
EVT_HEADER_RESIZING =           wx.PyEventBinder( wxEVT_HEADER_RESIZING )
EVT_HEADER_END_RESIZE =         wx.PyEventBinder( wxEVT_HEADER_END_RESIZE )
EVT_HEADER_BEGIN_REORDER =      wx.PyEventBinder( wxEVT_HEADER_BEGIN_REORDER )
EVT_HEADER_END_REORDER =        wx.PyEventBinder( wxEVT_HEADER_END_REORDER )
EVT_HEADER_DRAGGING_CANCELLED = wx.PyEventBinder( wxEVT_HEADER_DRAGGING_CANCELLED )

# deprecated wxEVT aliases
wxEVT_COMMAND_HEADER_CLICK               = wxEVT_HEADER_CLICK
wxEVT_COMMAND_HEADER_RIGHT_CLICK         = wxEVT_HEADER_RIGHT_CLICK
wxEVT_COMMAND_HEADER_MIDDLE_CLICK        = wxEVT_HEADER_MIDDLE_CLICK
wxEVT_COMMAND_HEADER_DCLICK              = wxEVT_HEADER_DCLICK
wxEVT_COMMAND_HEADER_RIGHT_DCLICK        = wxEVT_HEADER_RIGHT_DCLICK
wxEVT_COMMAND_HEADER_MIDDLE_DCLICK       = wxEVT_HEADER_MIDDLE_DCLICK
wxEVT_COMMAND_HEADER_SEPARATOR_DCLICK    = wxEVT_HEADER_SEPARATOR_DCLICK
wxEVT_COMMAND_HEADER_BEGIN_RESIZE        = wxEVT_HEADER_BEGIN_RESIZE
wxEVT_COMMAND_HEADER_RESIZING            = wxEVT_HEADER_RESIZING
wxEVT_COMMAND_HEADER_END_RESIZE          = wxEVT_HEADER_END_RESIZE
wxEVT_COMMAND_HEADER_BEGIN_REORDER       = wxEVT_HEADER_BEGIN_REORDER
wxEVT_COMMAND_HEADER_END_REORDER         = wxEVT_HEADER_END_REORDER
wxEVT_COMMAND_HEADER_DRAGGING_CANCELLED  = wxEVT_HEADER_DRAGGING_CANCELLED

EVT_SEARCH_CANCEL = wx.PyEventBinder( wxEVT_SEARCH_CANCEL, 1)
EVT_SEARCH = wx.PyEventBinder( wxEVT_SEARCH, 1)

# deprecated wxEVT aliases
wxEVT_SEARCHCTRL_CANCEL_BTN = wxEVT_SEARCH_CANCEL
wxEVT_SEARCHCTRL_SEARCH_BTN = wxEVT_SEARCH
wxEVT_COMMAND_SEARCHCTRL_CANCEL_BTN  = wxEVT_SEARCHCTRL_CANCEL_BTN
wxEVT_COMMAND_SEARCHCTRL_SEARCH_BTN  = wxEVT_SEARCHCTRL_SEARCH_BTN
EVT_SEARCHCTRL_CANCEL_BTN = wx.PyEventBinder( wxEVT_SEARCHCTRL_CANCEL_BTN, 1)
EVT_SEARCHCTRL_SEARCH_BTN = wx.PyEventBinder( wxEVT_SEARCHCTRL_SEARCH_BTN, 1)

def _RadioBox_GetItemLabel(self, n):
    """
    GetItemLabel(self, n) -> string
    
    Return the text of the n'th item in the radio box.
    """
    return self.GetString(n)
RadioBox.GetItemLabel = _RadioBox_GetItemLabel
del _RadioBox_GetItemLabel
def _RadioBox_SetItemLabel(self, n, text):
    """
    SetItemLabel(self, n, text)
    
    Set the text of the n'th item in the radio box.
    """
    self.SetString(n, text)
RadioBox.SetItemLabel = _RadioBox_SetItemLabel
del _RadioBox_SetItemLabel
def _Slider_GetRange(self):
    return (self.GetMin(), self.GetMax())
Slider.GetRange = _Slider_GetRange
del _Slider_GetRange
Slider.Range = property(Slider.GetRange)
def _SpinButton_GetRange(self):
    return (self.GetMin(), self.GetMax())
SpinButton.GetRange = _SpinButton_GetRange
del _SpinButton_GetRange
def _SpinButton_SetMin(self, minVal):
    self.SetRange(minVal, self.GetMax())
SpinButton.SetMin = _SpinButton_SetMin
del _SpinButton_SetMin
def _SpinButton_SetMax(self, maxVal):
    self.SetRange(self.GetMin(), maxVal)
SpinButton.SetMax = _SpinButton_SetMax
del _SpinButton_SetMax
SpinButton.Max = property(SpinButton.GetMax, SpinButton.SetMax)
SpinButton.Min = property(SpinButton.GetMin, SpinButton.SetMin)
SpinButton.Range = property(SpinButton.GetRange)
EVT_SPIN_UP   = wx.PyEventBinder( wxEVT_SPIN_UP, 1)
EVT_SPIN_DOWN = wx.PyEventBinder( wxEVT_SPIN_DOWN, 1)
EVT_SPIN      = wx.PyEventBinder( wxEVT_SPIN, 1)

def _SpinCtrl_GetRange(self):
    return (self.GetMin(), self.GetMax())
SpinCtrl.GetRange = _SpinCtrl_GetRange
del _SpinCtrl_GetRange
def _SpinCtrl_SetMin(self, minVal):
    self.SetRange(minVal, self.GetMax())
SpinCtrl.SetMin = _SpinCtrl_SetMin
del _SpinCtrl_SetMin
def _SpinCtrl_SetMax(self, maxVal):
    self.SetRange(self.GetMin(), maxVal)
SpinCtrl.SetMax = _SpinCtrl_SetMax
del _SpinCtrl_SetMax
SpinCtrl.Max = property(SpinCtrl.GetMax, SpinCtrl.SetMax)
SpinCtrl.Min = property(SpinCtrl.GetMin, SpinCtrl.SetMin)
SpinCtrl.Range = property(SpinCtrl.GetRange)
def _SpinCtrlDouble_GetRange(self):
    return (self.GetMin(), self.GetMax())
SpinCtrlDouble.GetRange = _SpinCtrlDouble_GetRange
del _SpinCtrlDouble_GetRange
def _SpinCtrlDouble_SetMin(self, minVal):
    self.SetRange(minVal, self.GetMax())
SpinCtrlDouble.SetMin = _SpinCtrlDouble_SetMin
del _SpinCtrlDouble_SetMin
def _SpinCtrlDouble_SetMax(self, maxVal):
    self.SetRange(self.GetMin(), maxVal)
SpinCtrlDouble.SetMax = _SpinCtrlDouble_SetMax
del _SpinCtrlDouble_SetMax
SpinCtrlDouble.Max = property(SpinCtrlDouble.GetMax, SpinCtrlDouble.SetMax)
SpinCtrlDouble.Min = property(SpinCtrlDouble.GetMin, SpinCtrlDouble.SetMin)
SpinCtrlDouble.Range = property(SpinCtrlDouble.GetRange)
EVT_SPINCTRL = wx.PyEventBinder( wxEVT_SPINCTRL, 1)
EVT_SPINCTRLDOUBLE = wx.PyEventBinder( wxEVT_SPINCTRLDOUBLE, 1)

# deprecated wxEVT aliases
wxEVT_COMMAND_SPINCTRL_UPDATED        = wxEVT_SPINCTRL
wxEVT_COMMAND_SPINCTRLDOUBLE_UPDATED  = wxEVT_SPINCTRLDOUBLE

EVT_TOGGLEBUTTON = PyEventBinder(wxEVT_TOGGLEBUTTON, 1)

# deprecated wxEVT alias
wxEVT_COMMAND_TOGGLEBUTTON_CLICKED   = wxEVT_TOGGLEBUTTON

def _ToolBar_AddSimpleTool(self, toolId, bitmap, shortHelpString="", longHelpString="", isToggle=0):
    """
    Old style method to add a tool to the toolbar.
    """
    kind = wx.ITEM_NORMAL
    if isToggle: kind = wx.ITEM_CHECK
    return self.AddTool(toolId, '', bitmap, wx.NullBitmap, kind,
                        shortHelpString, longHelpString)
ToolBar.AddSimpleTool = wx.deprecated(_ToolBar_AddSimpleTool, "Use :meth:`AddTool` instead.")
del _ToolBar_AddSimpleTool
def _ToolBar_AddLabelTool(self, id, label, bitmap, bmpDisabled=wx.NullBitmap, kind=wx.ITEM_NORMAL, shortHelp="", longHelp="", clientData=None):
    """
    Old style method to add a tool in the toolbar.
    """
    return self.AddTool(id, label, bitmap, bmpDisabled, kind,
                        shortHelp, longHelp, clientData)
ToolBar.AddLabelTool = wx.deprecated(_ToolBar_AddLabelTool, "Use :meth:`AddTool` instead.")
del _ToolBar_AddLabelTool
def _ToolBar_InsertSimpleTool(self, pos, toolId, bitmap, shortHelpString="", longHelpString="", isToggle=0):
    """
    Old style method to insert a tool in the toolbar.
    """
    kind = wx.ITEM_NORMAL
    if isToggle: kind = wx.ITEM_CHECK
    return self.InsertTool(pos, toolId, '', bitmap, wx.NullBitmap, kind,
                           shortHelpString, longHelpString)
ToolBar.InsertSimpleTool = wx.deprecated(_ToolBar_InsertSimpleTool, "Use :meth:`InsertTool` instead.")
del _ToolBar_InsertSimpleTool
def _ToolBar_InsertLabelTool(self, pos, id, label, bitmap, bmpDisabled=wx.NullBitmap, kind=wx.ITEM_NORMAL, shortHelp="", longHelp="", clientData=None):
    """
    Old style method to insert a tool in the toolbar.
    """
    return self.InsertTool(pos, id, label, bitmap, bmpDisabled, kind,
                           shortHelp, longHelp, clientData)
ToolBar.InsertLabelTool = wx.deprecated(_ToolBar_InsertLabelTool, "Use :meth:`InsertTool` instead.")
del _ToolBar_InsertLabelTool
ListCtrl.FindItemData = wx.deprecated(ListCtrl.FindItem, "Use FindItem instead.")

ListCtrl.FindItemAtPos = wx.deprecated(ListCtrl.FindItem, "Use FindItem instead.")

ListCtrl.InsertStringItem = wx.deprecated(ListCtrl.InsertItem, "Use InsertItem instead.")

ListCtrl.InsertImageItem = wx.deprecated(ListCtrl.InsertItem, "Use InsertItem instead.")

ListCtrl.InsertImageStringItem = wx.deprecated(ListCtrl.InsertItem, "Use InsertItem instead.")

ListCtrl.SetStringItem = wx.deprecated(ListCtrl.SetItem, "Use SetItem instead.")

def _ListCtrl_Select(self, idx, on=1):
    """
    Selects/deselects an item.
    """
    if on: state = wx.LIST_STATE_SELECTED
    else: state = 0
    self.SetItemState(idx, state, wx.LIST_STATE_SELECTED)
ListCtrl.Select = _ListCtrl_Select
del _ListCtrl_Select
def _ListCtrl_Focus(self, idx):
    """
    Focus and show the given item.
    """
    self.SetItemState(idx, wx.LIST_STATE_FOCUSED, wx.LIST_STATE_FOCUSED)
    self.EnsureVisible(idx)
ListCtrl.Focus = _ListCtrl_Focus
del _ListCtrl_Focus
def _ListCtrl_GetFocusedItem(self):
    """
    Gets the currently focused item or -1 if none is focused.
    """
    return self.GetNextItem(-1, wx.LIST_NEXT_ALL, wx.LIST_STATE_FOCUSED)
ListCtrl.GetFocusedItem = _ListCtrl_GetFocusedItem
del _ListCtrl_GetFocusedItem
def _ListCtrl_GetFirstSelected(self, *args):
    """
    Returns the first selected item, or -1 when none is selected.
    """
    return self.GetNextSelected(-1)
ListCtrl.GetFirstSelected = _ListCtrl_GetFirstSelected
del _ListCtrl_GetFirstSelected
def _ListCtrl_GetNextSelected(self, item):
    """
    Returns subsequent selected items, or -1 when no more are selected.
    """
    return self.GetNextItem(item, wx.LIST_NEXT_ALL, wx.LIST_STATE_SELECTED)
ListCtrl.GetNextSelected = _ListCtrl_GetNextSelected
del _ListCtrl_GetNextSelected
def _ListCtrl_IsSelected(self, idx):
    """
    Returns ``True`` if the item is selected.
    """
    return (self.GetItemState(idx, wx.LIST_STATE_SELECTED) & wx.LIST_STATE_SELECTED) != 0
ListCtrl.IsSelected = _ListCtrl_IsSelected
del _ListCtrl_IsSelected
def _ListCtrl_SetColumnImage(self, col, image):
    item = self.GetColumn(col)
    # preserve all other attributes too
    item.SetMask( wx.LIST_MASK_STATE |
                  wx.LIST_MASK_TEXT  |
                  wx.LIST_MASK_IMAGE |
                  wx.LIST_MASK_DATA  |
                  wx.LIST_SET_ITEM   |
                  wx.LIST_MASK_WIDTH |
                  wx.LIST_MASK_FORMAT )
    item.SetImage(image)
    self.SetColumn(col, item)
ListCtrl.SetColumnImage = _ListCtrl_SetColumnImage
del _ListCtrl_SetColumnImage
def _ListCtrl_ClearColumnImage(self, col):
    self.SetColumnImage(col, -1)
ListCtrl.ClearColumnImage = _ListCtrl_ClearColumnImage
del _ListCtrl_ClearColumnImage
def _ListCtrl_Append(self, entry):
    """
    Append an item to the list control.  The `entry` parameter should be a
    sequence with an item for each column
    """
    if len(entry):
        pos = self.InsertItem(self.GetItemCount(), str(entry[0]))
        for i in range(1, len(entry)):
            self.SetItem(pos, i, str(entry[i]))
        return pos
ListCtrl.Append = _ListCtrl_Append
del _ListCtrl_Append
ListCtrl.FocusedItem = property(ListCtrl.GetFocusedItem)
ListItemAttr = wx.deprecated(ItemAttr, 'Use ItemAttr instead')

EVT_LIST_BEGIN_DRAG        = PyEventBinder(wxEVT_LIST_BEGIN_DRAG       , 1)
EVT_LIST_BEGIN_RDRAG       = PyEventBinder(wxEVT_LIST_BEGIN_RDRAG      , 1)
EVT_LIST_BEGIN_LABEL_EDIT  = PyEventBinder(wxEVT_LIST_BEGIN_LABEL_EDIT , 1)
EVT_LIST_END_LABEL_EDIT    = PyEventBinder(wxEVT_LIST_END_LABEL_EDIT   , 1)
EVT_LIST_DELETE_ITEM       = PyEventBinder(wxEVT_LIST_DELETE_ITEM      , 1)
EVT_LIST_DELETE_ALL_ITEMS  = PyEventBinder(wxEVT_LIST_DELETE_ALL_ITEMS , 1)
EVT_LIST_ITEM_SELECTED     = PyEventBinder(wxEVT_LIST_ITEM_SELECTED    , 1)
EVT_LIST_ITEM_DESELECTED   = PyEventBinder(wxEVT_LIST_ITEM_DESELECTED  , 1)
EVT_LIST_KEY_DOWN          = PyEventBinder(wxEVT_LIST_KEY_DOWN         , 1)
EVT_LIST_INSERT_ITEM       = PyEventBinder(wxEVT_LIST_INSERT_ITEM      , 1)
EVT_LIST_COL_CLICK         = PyEventBinder(wxEVT_LIST_COL_CLICK        , 1)
EVT_LIST_ITEM_RIGHT_CLICK  = PyEventBinder(wxEVT_LIST_ITEM_RIGHT_CLICK , 1)
EVT_LIST_ITEM_MIDDLE_CLICK = PyEventBinder(wxEVT_LIST_ITEM_MIDDLE_CLICK, 1)
EVT_LIST_ITEM_ACTIVATED    = PyEventBinder(wxEVT_LIST_ITEM_ACTIVATED   , 1)
EVT_LIST_CACHE_HINT        = PyEventBinder(wxEVT_LIST_CACHE_HINT       , 1)
EVT_LIST_COL_RIGHT_CLICK   = PyEventBinder(wxEVT_LIST_COL_RIGHT_CLICK  , 1)
EVT_LIST_COL_BEGIN_DRAG    = PyEventBinder(wxEVT_LIST_COL_BEGIN_DRAG   , 1)
EVT_LIST_COL_DRAGGING      = PyEventBinder(wxEVT_LIST_COL_DRAGGING     , 1)
EVT_LIST_COL_END_DRAG      = PyEventBinder(wxEVT_LIST_COL_END_DRAG     , 1)
EVT_LIST_ITEM_FOCUSED      = PyEventBinder(wxEVT_LIST_ITEM_FOCUSED     , 1)
EVT_LIST_ITEM_CHECKED      = PyEventBinder(wxEVT_LIST_ITEM_CHECKED     , 1)
EVT_LIST_ITEM_UNCHECKED    = PyEventBinder(wxEVT_LIST_ITEM_UNCHECKED   , 1)

# deprecated wxEVT aliases
wxEVT_COMMAND_LIST_BEGIN_DRAG         = wxEVT_LIST_BEGIN_DRAG
wxEVT_COMMAND_LIST_BEGIN_RDRAG        = wxEVT_LIST_BEGIN_RDRAG
wxEVT_COMMAND_LIST_BEGIN_LABEL_EDIT   = wxEVT_LIST_BEGIN_LABEL_EDIT
wxEVT_COMMAND_LIST_END_LABEL_EDIT     = wxEVT_LIST_END_LABEL_EDIT
wxEVT_COMMAND_LIST_DELETE_ITEM        = wxEVT_LIST_DELETE_ITEM
wxEVT_COMMAND_LIST_DELETE_ALL_ITEMS   = wxEVT_LIST_DELETE_ALL_ITEMS
wxEVT_COMMAND_LIST_ITEM_SELECTED      = wxEVT_LIST_ITEM_SELECTED
wxEVT_COMMAND_LIST_ITEM_DESELECTED    = wxEVT_LIST_ITEM_DESELECTED
wxEVT_COMMAND_LIST_KEY_DOWN           = wxEVT_LIST_KEY_DOWN
wxEVT_COMMAND_LIST_INSERT_ITEM        = wxEVT_LIST_INSERT_ITEM
wxEVT_COMMAND_LIST_COL_CLICK          = wxEVT_LIST_COL_CLICK
wxEVT_COMMAND_LIST_ITEM_RIGHT_CLICK   = wxEVT_LIST_ITEM_RIGHT_CLICK
wxEVT_COMMAND_LIST_ITEM_MIDDLE_CLICK  = wxEVT_LIST_ITEM_MIDDLE_CLICK
wxEVT_COMMAND_LIST_ITEM_ACTIVATED     = wxEVT_LIST_ITEM_ACTIVATED
wxEVT_COMMAND_LIST_CACHE_HINT         = wxEVT_LIST_CACHE_HINT
wxEVT_COMMAND_LIST_COL_RIGHT_CLICK    = wxEVT_LIST_COL_RIGHT_CLICK
wxEVT_COMMAND_LIST_COL_BEGIN_DRAG     = wxEVT_LIST_COL_BEGIN_DRAG
wxEVT_COMMAND_LIST_COL_DRAGGING       = wxEVT_LIST_COL_DRAGGING
wxEVT_COMMAND_LIST_COL_END_DRAG       = wxEVT_LIST_COL_END_DRAG
wxEVT_COMMAND_LIST_ITEM_FOCUSED       = wxEVT_LIST_ITEM_FOCUSED

def _TreeItemId___hash__(self):
    return hash(int(self.GetID()))
TreeItemId.__hash__ = _TreeItemId___hash__
del _TreeItemId___hash__
TreeCtrl.GetItemPyData = wx.deprecated(TreeCtrl.GetItemData, 'Use GetItemData instead.')
TreeCtrl.SetItemPyData = wx.deprecated(TreeCtrl.SetItemData, 'Use SetItemData instead.')
TreeCtrl.GetPyData = wx.deprecated(TreeCtrl.GetItemData, 'Use GetItemData instead.')
TreeCtrl.SetPyData = wx.deprecated(TreeCtrl.SetItemData, 'Use SetItemData instead.')

def TreeItemData(data):
    return data
TreeItemData = deprecated(TreeItemData, "The TreeItemData class no longer exists, just pass your object directly to the tree instead.")

EVT_TREE_BEGIN_DRAG        = PyEventBinder(wxEVT_TREE_BEGIN_DRAG       , 1)
EVT_TREE_BEGIN_RDRAG       = PyEventBinder(wxEVT_TREE_BEGIN_RDRAG      , 1)
EVT_TREE_BEGIN_LABEL_EDIT  = PyEventBinder(wxEVT_TREE_BEGIN_LABEL_EDIT , 1)
EVT_TREE_END_LABEL_EDIT    = PyEventBinder(wxEVT_TREE_END_LABEL_EDIT   , 1)
EVT_TREE_DELETE_ITEM       = PyEventBinder(wxEVT_TREE_DELETE_ITEM      , 1)
EVT_TREE_GET_INFO          = PyEventBinder(wxEVT_TREE_GET_INFO         , 1)
EVT_TREE_SET_INFO          = PyEventBinder(wxEVT_TREE_SET_INFO         , 1)
EVT_TREE_ITEM_EXPANDED     = PyEventBinder(wxEVT_TREE_ITEM_EXPANDED    , 1)
EVT_TREE_ITEM_EXPANDING    = PyEventBinder(wxEVT_TREE_ITEM_EXPANDING   , 1)
EVT_TREE_ITEM_COLLAPSED    = PyEventBinder(wxEVT_TREE_ITEM_COLLAPSED   , 1)
EVT_TREE_ITEM_COLLAPSING   = PyEventBinder(wxEVT_TREE_ITEM_COLLAPSING  , 1)
EVT_TREE_SEL_CHANGED       = PyEventBinder(wxEVT_TREE_SEL_CHANGED      , 1)
EVT_TREE_SEL_CHANGING      = PyEventBinder(wxEVT_TREE_SEL_CHANGING     , 1)
EVT_TREE_KEY_DOWN          = PyEventBinder(wxEVT_TREE_KEY_DOWN         , 1)
EVT_TREE_ITEM_ACTIVATED    = PyEventBinder(wxEVT_TREE_ITEM_ACTIVATED   , 1)
EVT_TREE_ITEM_RIGHT_CLICK  = PyEventBinder(wxEVT_TREE_ITEM_RIGHT_CLICK , 1)
EVT_TREE_ITEM_MIDDLE_CLICK = PyEventBinder(wxEVT_TREE_ITEM_MIDDLE_CLICK, 1)
EVT_TREE_END_DRAG          = PyEventBinder(wxEVT_TREE_END_DRAG         , 1)
EVT_TREE_STATE_IMAGE_CLICK = PyEventBinder(wxEVT_TREE_STATE_IMAGE_CLICK, 1)
EVT_TREE_ITEM_GETTOOLTIP   = PyEventBinder(wxEVT_TREE_ITEM_GETTOOLTIP,   1)
EVT_TREE_ITEM_MENU         = PyEventBinder(wxEVT_TREE_ITEM_MENU,         1)

# deprecated wxEVT aliases
wxEVT_COMMAND_TREE_BEGIN_DRAG         = wxEVT_TREE_BEGIN_DRAG
wxEVT_COMMAND_TREE_BEGIN_RDRAG        = wxEVT_TREE_BEGIN_RDRAG
wxEVT_COMMAND_TREE_BEGIN_LABEL_EDIT   = wxEVT_TREE_BEGIN_LABEL_EDIT
wxEVT_COMMAND_TREE_END_LABEL_EDIT     = wxEVT_TREE_END_LABEL_EDIT
wxEVT_COMMAND_TREE_DELETE_ITEM        = wxEVT_TREE_DELETE_ITEM
wxEVT_COMMAND_TREE_GET_INFO           = wxEVT_TREE_GET_INFO
wxEVT_COMMAND_TREE_SET_INFO           = wxEVT_TREE_SET_INFO
wxEVT_COMMAND_TREE_ITEM_EXPANDED      = wxEVT_TREE_ITEM_EXPANDED
wxEVT_COMMAND_TREE_ITEM_EXPANDING     = wxEVT_TREE_ITEM_EXPANDING
wxEVT_COMMAND_TREE_ITEM_COLLAPSED     = wxEVT_TREE_ITEM_COLLAPSED
wxEVT_COMMAND_TREE_ITEM_COLLAPSING    = wxEVT_TREE_ITEM_COLLAPSING
wxEVT_COMMAND_TREE_SEL_CHANGED        = wxEVT_TREE_SEL_CHANGED
wxEVT_COMMAND_TREE_SEL_CHANGING       = wxEVT_TREE_SEL_CHANGING
wxEVT_COMMAND_TREE_KEY_DOWN           = wxEVT_TREE_KEY_DOWN
wxEVT_COMMAND_TREE_ITEM_ACTIVATED     = wxEVT_TREE_ITEM_ACTIVATED
wxEVT_COMMAND_TREE_ITEM_RIGHT_CLICK   = wxEVT_TREE_ITEM_RIGHT_CLICK
wxEVT_COMMAND_TREE_ITEM_MIDDLE_CLICK  = wxEVT_TREE_ITEM_MIDDLE_CLICK
wxEVT_COMMAND_TREE_END_DRAG           = wxEVT_TREE_END_DRAG
wxEVT_COMMAND_TREE_STATE_IMAGE_CLICK  = wxEVT_TREE_STATE_IMAGE_CLICK
wxEVT_COMMAND_TREE_ITEM_GETTOOLTIP    = wxEVT_TREE_ITEM_GETTOOLTIP
wxEVT_COMMAND_TREE_ITEM_MENU          = wxEVT_TREE_ITEM_MENU

EVT_COLOURPICKER_CHANGED = wx.PyEventBinder( wxEVT_COLOURPICKER_CHANGED, 1 )
EVT_COLOURPICKER_CURRENT_CHANGED = wx.PyEventBinder( wxEVT_COLOURPICKER_CURRENT_CHANGED, 1 )
EVT_COLOURPICKER_DIALOG_CANCELLED = wx.PyEventBinder( wxEVT_COLOURPICKER_DIALOG_CANCELLED, 1 )

# deprecated wxEVT alias
wxEVT_COMMAND_COLOURPICKER_CHANGED  = wxEVT_COLOURPICKER_CHANGED

EVT_FILEPICKER_CHANGED = wx.PyEventBinder( wxEVT_FILEPICKER_CHANGED, 1 )
EVT_DIRPICKER_CHANGED = wx.PyEventBinder( wxEVT_DIRPICKER_CHANGED, 1 )

# deprecated wxEVT aliases
wxEVT_COMMAND_FILEPICKER_CHANGED   = wxEVT_FILEPICKER_CHANGED
wxEVT_COMMAND_DIRPICKER_CHANGED    = wxEVT_DIRPICKER_CHANGED

EVT_FONTPICKER_CHANGED = wx.PyEventBinder( wxEVT_FONTPICKER_CHANGED, 1 )

# deprecated wxEVT alias
wxEVT_COMMAND_FONTPICKER_CHANGED  = wxEVT_FONTPICKER_CHANGED

if 'wxMac' in wx.PlatformInfo:
    class ColourPickerCtrl(PickerBase):
        '''
        This control allows the user to select a colour. The
        implementation varies by platform but is usually a button which
        brings up a `wx.ColourDialog` when clicked.


        Window Styles
        -------------

            ======================  ============================================
            wx.CLRP_DEFAULT         Default style.
            wx.CLRP_USE_TEXTCTRL    Creates a text control to the left of the
                                    picker button which is completely managed
                                    by the `wx.ColourPickerCtrl` and which can
                                    be used by the user to specify a colour.
                                    The text control is automatically synchronized
                                    with the button's value. Use functions defined in
                                    `wx.PickerBase` to modify the text control.
            wx.CLRP_SHOW_LABEL      Shows the colour in HTML form (AABBCC) as the
                                    colour button label (instead of no label at all).
            ======================  ============================================

        Events
        ------

            ========================  ==========================================
            EVT_COLOURPICKER_CHANGED  The user changed the colour selected in the
                                      control either using the button or using the
                                      text control (see wx.CLRP_USE_TEXTCTRL; note
                                      that in this case the event is fired only if
                                      the user's input is valid, i.e. recognizable).
            ========================  ==========================================
        '''

        # ColourData object to be shared by all colour pickers, so they can
        # share the custom colours
        _colourData = None

        #--------------------------------------------------
        class ColourPickerButton(BitmapButton):
            def __init__(self, parent, id=-1, colour=wx.BLACK,
                         pos=wx.DefaultPosition, size=wx.DefaultSize,
                         style = CLRP_DEFAULT_STYLE,
                         validator = wx.DefaultValidator,
                         name = "colourpickerwidget"):

                wx.BitmapButton.__init__(self, parent, id, wx.Bitmap(1,1),
                                         pos, size, style, validator, name)
                self.SetColour(colour)
                self.InvalidateBestSize()
                self.SetInitialSize(size)
                self.Bind(wx.EVT_BUTTON, self.OnButtonClick)

                if ColourPickerCtrl._colourData is None:
                    ColourPickerCtrl._colourData = wx.ColourData()
                    ColourPickerCtrl._colourData.SetChooseFull(True)
                    grey = 0
                    for i in range(16):
                        c = wx.Colour(grey, grey, grey)
                        ColourPickerCtrl._colourData.SetCustomColour(i, c)
                        grey += 16

            def SetColour(self, colour):
                # force a copy, in case the _colorData is shared
                self.colour = wx.Colour(colour)
                bmp = self._makeBitmap()
                self.SetBitmapLabel(wx.BitmapBundle(bmp))

            def GetColour(self):
                return self.colour

            def OnButtonClick(self, evt):
                ColourPickerCtrl._colourData.SetColour(self.colour)
                dlg = wx.ColourDialog(self, ColourPickerCtrl._colourData)
                if dlg.ShowModal() == wx.ID_OK:
                    ColourPickerCtrl._colourData = dlg.GetColourData()
                    self.SetColour(ColourPickerCtrl._colourData.GetColour())
                    evt = wx.ColourPickerEvent(self, self.GetId(), self.GetColour())
                    self.GetEventHandler().ProcessEvent(evt)

            def _makeBitmap(self):
                width = height = 24
                bg = self.GetColour()
                if self.HasFlag(CLRP_SHOW_LABEL):
                    w, h = self.GetTextExtent(bg.GetAsString(wx.C2S_HTML_SYNTAX))
                    width += w
                bmp = wx.Bitmap(width, height)
                dc = wx.MemoryDC(bmp)
                dc.SetBackground(wx.Brush(self.colour))
                dc.Clear()
                if self.HasFlag(CLRP_SHOW_LABEL):
                    from wx.lib.colourutils import BestLabelColour
                    fg = BestLabelColour(bg)
                    dc.SetTextForeground(fg)
                    dc.DrawText(bg.GetAsString(wx.C2S_HTML_SYNTAX),
                                (width - w)/2, (height - h)/2)
                return bmp

        #--------------------------------------------------

        def __init__(self, parent, id=-1, colour=wx.BLACK,
                     pos=wx.DefaultPosition, size=wx.DefaultSize,
                     style = CLRP_DEFAULT_STYLE,
                     validator = wx.DefaultValidator,
                     name = "colourpicker"):
            if type(colour) != wx.Colour:
                colour = wx.Colour(colour)
            wx.PickerBase.__init__(self)
            self.CreateBase(parent, id, colour.GetAsString(),
                            pos, size, style, validator, name)
            widget = ColourPickerCtrl.ColourPickerButton(
                self, -1, colour, style=self.GetPickerStyle(style))
            self.SetPickerCtrl(widget)
            widget.Bind(wx.EVT_COLOURPICKER_CHANGED, self.OnColourChange)
            self.PostCreation()


        def GetColour(self):
            '''Set the displayed colour.'''
            return self.GetPickerCtrl().GetColour()

        def SetColour(self, colour):
            '''Returns the currently selected colour.'''
            self.GetPickerCtrl().SetColour(colour)
            self.UpdateTextCtrlFromPicker()
        Colour = property(GetColour, SetColour)


        def UpdatePickerFromTextCtrl(self):
            col = wx.Colour(self.GetTextCtrl().GetValue())
            if not col.IsOk():
                return
            if self.GetColour() != col:
                self.GetPickerCtrl().SetColour(col)
                evt = wx.ColourPickerEvent(self, self.GetId(), self.GetColour())
                self.GetEventHandler().ProcessEvent(evt)

        def UpdateTextCtrlFromPicker(self):
            if not self.GetTextCtrl():
                return
            self.GetTextCtrl().SetValue(self.GetColour().GetAsString())

        def GetPickerStyle(self, style):
            return style & CLRP_SHOW_LABEL

        def OnColourChange(self, evt):
            self.UpdateTextCtrlFromPicker()
            evt = wx.ColourPickerEvent(self, self.GetId(), self.GetColour())
            self.GetEventHandler().ProcessEvent(evt)

EVT_FILECTRL_SELECTIONCHANGED = wx.PyEventBinder( wxEVT_FILECTRL_SELECTIONCHANGED, 1)
EVT_FILECTRL_FILEACTIVATED = wx.PyEventBinder( wxEVT_FILECTRL_FILEACTIVATED, 1)
EVT_FILECTRL_FOLDERCHANGED = wx.PyEventBinder( wxEVT_FILECTRL_FOLDERCHANGED, 1)
EVT_FILECTRL_FILTERCHANGED = wx.PyEventBinder( wxEVT_FILECTRL_FILTERCHANGED, 1)

EVT_CHOICEBOOK_PAGE_CHANGED  = wx.PyEventBinder( wxEVT_CHOICEBOOK_PAGE_CHANGED, 1 )
EVT_CHOICEBOOK_PAGE_CHANGING = wx.PyEventBinder( wxEVT_CHOICEBOOK_PAGE_CHANGING, 1 )

# deprecated wxEVT aliases
wxEVT_COMMAND_CHOICEBOOK_PAGE_CHANGED   = wxEVT_CHOICEBOOK_PAGE_CHANGED
wxEVT_COMMAND_CHOICEBOOK_PAGE_CHANGING  = wxEVT_CHOICEBOOK_PAGE_CHANGING

EVT_LISTBOOK_PAGE_CHANGED  = wx.PyEventBinder( wxEVT_LISTBOOK_PAGE_CHANGED, 1 )
EVT_LISTBOOK_PAGE_CHANGING = wx.PyEventBinder( wxEVT_LISTBOOK_PAGE_CHANGING, 1 )

# deprecated wxEVT aliases
wxEVT_COMMAND_LISTBOOK_PAGE_CHANGED   = wxEVT_LISTBOOK_PAGE_CHANGED
wxEVT_COMMAND_LISTBOOK_PAGE_CHANGING  = wxEVT_LISTBOOK_PAGE_CHANGING

EVT_TOOLBOOK_PAGE_CHANGED  = wx.PyEventBinder( wxEVT_TOOLBOOK_PAGE_CHANGED, 1 )
EVT_TOOLBOOK_PAGE_CHANGING = wx.PyEventBinder( wxEVT_TOOLBOOK_PAGE_CHANGING, 1 )

# deprecated wxEVT aliases
wxEVT_COMMAND_TOOLBOOK_PAGE_CHANGED   = wxEVT_TOOLBOOK_PAGE_CHANGED
wxEVT_COMMAND_TOOLBOOK_PAGE_CHANGING  = wxEVT_TOOLBOOK_PAGE_CHANGING

EVT_TREEBOOK_PAGE_CHANGED = wx.PyEventBinder( wxEVT_TREEBOOK_PAGE_CHANGED, 1 )
EVT_TREEBOOK_PAGE_CHANGING = wx.PyEventBinder( wxEVT_TREEBOOK_PAGE_CHANGING, 1)
EVT_TREEBOOK_NODE_COLLAPSED = wx.PyEventBinder( wxEVT_TREEBOOK_NODE_COLLAPSED, 1 )
EVT_TREEBOOK_NODE_EXPANDED = wx.PyEventBinder( wxEVT_TREEBOOK_NODE_EXPANDED, 1 )

# deprecated wxEVT aliases
wxEVT_COMMAND_TREEBOOK_PAGE_CHANGED    = wxEVT_TREEBOOK_PAGE_CHANGED
wxEVT_COMMAND_TREEBOOK_PAGE_CHANGING   = wxEVT_TREEBOOK_PAGE_CHANGING
wxEVT_COMMAND_TREEBOOK_NODE_COLLAPSED  = wxEVT_TREEBOOK_NODE_COLLAPSED
wxEVT_COMMAND_TREEBOOK_NODE_EXPANDED   = wxEVT_TREEBOOK_NODE_EXPANDED

EVT_COLLAPSIBLEHEADER_CHANGED = PyEventBinder(wxEVT_COLLAPSIBLEHEADER_CHANGED, 1)

def _Dialog___enter__(self):
    return self
Dialog.__enter__ = _Dialog___enter__
del _Dialog___enter__
def _Dialog___exit__(self, exc_type, exc_val, exc_tb):
    self.Destroy()
Dialog.__exit__ = _Dialog___exit__
del _Dialog___exit__
EVT_DIRCTRL_SELECTIONCHANGED = wx.PyEventBinder( wxEVT_DIRCTRL_SELECTIONCHANGED, 1 )
EVT_DIRCTRL_FILEACTIVATED = wx.PyEventBinder( wxEVT_DIRCTRL_FILEACTIVATED, 1 )

EVT_COLOUR_CHANGED = PyEventBinder(wxEVT_COLOUR_CHANGED, 1)

EVT_FIND = wx.PyEventBinder( wxEVT_FIND, 1 )
EVT_FIND_NEXT = wx.PyEventBinder( wxEVT_FIND_NEXT, 1 )
EVT_FIND_REPLACE = wx.PyEventBinder( wxEVT_FIND_REPLACE, 1 )
EVT_FIND_REPLACE_ALL = wx.PyEventBinder( wxEVT_FIND_REPLACE_ALL, 1 )
EVT_FIND_CLOSE = wx.PyEventBinder( wxEVT_FIND_CLOSE, 1 )

# deprecated wxEVT aliases
wxEVT_COMMAND_FIND              = wxEVT_FIND
wxEVT_COMMAND_FIND_NEXT         = wxEVT_FIND_NEXT
wxEVT_COMMAND_FIND_REPLACE      = wxEVT_FIND_REPLACE
wxEVT_COMMAND_FIND_REPLACE_ALL  = wxEVT_FIND_REPLACE_ALL
wxEVT_COMMAND_FIND_CLOSE        = wxEVT_FIND_CLOSE

def _PowerResourceBlocker___enter__(self):
    return self
PowerResourceBlocker.__enter__ = _PowerResourceBlocker___enter__
del _PowerResourceBlocker___enter__
def _PowerResourceBlocker___exit__(self, exc_type, exc_val, exc_tb):
    pass
PowerResourceBlocker.__exit__ = _PowerResourceBlocker___exit__
del _PowerResourceBlocker___exit__
EVT_POWER_SUSPENDING       = wx.PyEventBinder( wxEVT_POWER_SUSPENDING , 1 )
EVT_POWER_SUSPENDED        = wx.PyEventBinder( wxEVT_POWER_SUSPENDED , 1 )
EVT_POWER_SUSPEND_CANCEL   = wx.PyEventBinder( wxEVT_POWER_SUSPEND_CANCEL , 1 )
EVT_POWER_RESUME           = wx.PyEventBinder( wxEVT_POWER_RESUME , 1 )

def _WindowDisabler___enter__(self):
    return self
WindowDisabler.__enter__ = _WindowDisabler___enter__
del _WindowDisabler___enter__
def _WindowDisabler___exit__(self, exc_type, exc_val, exc_tb):
    pass
WindowDisabler.__exit__ = _WindowDisabler___exit__
del _WindowDisabler___exit__
def _BusyCursor___enter__(self):
    return self
BusyCursor.__enter__ = _BusyCursor___enter__
del _BusyCursor___enter__
def _BusyCursor___exit__(self, exc_type, exc_val, exc_tb):
    pass
BusyCursor.__exit__ = _BusyCursor___exit__
del _BusyCursor___exit__
EVT_END_PROCESS = wx.PyEventBinder( wxEVT_END_PROCESS )

def HelpController(parentWindow=None):
    """
    Rather than being an alias for some class, the Python version of
    ``HelpController`` is a factory function that creates and returns an
    instance of the best Help Controller for the platform.
    """
    try:
        if 'wxMSW' in wx.PlatformInfo:
            from .msw import CHMHelpController as ControllerClass
        else:
            from .html import HtmlHelpController as ControllerClass
    except ImportError:
        from .adv import ExtHelpController as ControllerClass
    
    return ControllerClass(parentWindow)

PrintPreview.Ok = wx.deprecated(PrintPreview.IsOk, 'Use IsOk instead.')

PyPrintPreview = wx.deprecated(PrintPreview, 'Use PrintPreview instead.')

PyPreviewFrame = wx.deprecated(PreviewFrame, 'Use PreviewFrame instead.')

PyPreviewControlBar = wx.deprecated(PreviewControlBar, 'Use PreviewControlBar instead.')

PyPrintout = wx.deprecated(Printout, 'Use Printout instead.')

def _BusyInfo___enter__(self):
    return self
BusyInfo.__enter__ = _BusyInfo___enter__
del _BusyInfo___enter__
def _BusyInfo___exit__(self, exc_type, exc_val, exc_tb):
    return False
BusyInfo.__exit__ = _BusyInfo___exit__
del _BusyInfo___exit__
def _FileHistoryMenuList___repr__(self):
    return "FileHistoryMenuList: " + repr(list(self))
FileHistoryMenuList.__repr__ = _FileHistoryMenuList___repr__
del _FileHistoryMenuList___repr__
def _CommandList___repr__(self):
    return "CommandList: " + repr(list(self))
CommandList.__repr__ = _CommandList___repr__
del _CommandList___repr__
EVT_FSWATCHER = wx.PyEventBinder(wxEVT_FSWATCHER)

def version():
    """
    Returns a string containing version and port info
    """
    if wx.Port == '__WXMSW__':
        port = 'msw'
    elif wx.Port == '__WXMAC__':
        if 'wxOSX-carbon' in wx.PlatformInfo:
            port = 'osx-carbon'
        else:
            port = 'osx-cocoa'
    elif wx.Port == '__WXGTK__':
        port = 'gtk'
        if 'gtk2' in wx.PlatformInfo:
            port = 'gtk2'
        elif 'gtk3' in wx.PlatformInfo:
            port = 'gtk3'
    else:
        port = '???'
    return "%s %s (phoenix) %s" % (wx.VERSION_STRING, port, wx.wxWidgets_version)

_T = typing.TypeVar('_T')
try:
    _P = typing.ParamSpec('_P')
except AttributeError:
    import typing_extensions
    _P = typing_extensions.ParamSpec('_P')

def CallAfter(callableObj: typing.Callable[_P, _T], *args: _P.args, **kw: _P.kwargs) -> None:
    """
    Call the specified function after the current and pending event
    handlers have been completed.  This is also good for making GUI
    method calls from non-GUI threads.  Any extra positional or
    keyword args are passed on to the callable when it is called.
    
    :param PyObject callableObj: the callable object
    :param args: arguments to be passed to the callable object
    :param kw: keywords to be passed to the callable object
    
    .. seealso::
        :ref:`wx.CallLater`
    """
    assert callable(callableObj), "callableObj is not callable"
    app = wx.GetApp()
    assert app is not None, 'No wx.App created yet'
    
    if not hasattr(app, "_CallAfterId"):
        app._CallAfterId = wx.NewEventType()
        app.Connect(-1, -1, app._CallAfterId,
                    lambda event: event.callable(*event.args, **event.kw) )
    evt = wx.PyEvent()
    evt.SetEventType(app._CallAfterId)
    evt.callable = callableObj
    evt.args = args
    evt.kw = kw
    wx.PostEvent(app, evt)

class CallLater(typing.Generic[_P, _T]):
    """
    A convenience class for :class:`wx.Timer`, that calls the given callable
    object once after the given amount of milliseconds, passing any
    positional or keyword args.  The return value of the callable is
    available after it has been run with the :meth:`~wx.CallLater.GetResult`
    method.
    
    If you don't need to get the return value or restart the timer
    then there is no need to hold a reference to this object. CallLater
    maintains references to its instances while they are running. When they
    finish, the internal reference is deleted and the GC is free to collect
    naturally.
    
    .. seealso::
        :func:`wx.CallAfter`
    """
    __instances = {}
    def __init__(self, millis, callableObj: typing.Callable[_P, _T], *args: _P.args, **kwargs: _P.kwargs) -> None:
        """
        Constructs a new :class:`wx.CallLater` object.
        
        :param int millis: number of milliseconds to delay until calling the callable object
        :param PyObject callableObj: the callable object
        :param args: arguments to be passed to the callable object
        :param kw: keyword arguments to be passed to the callable object
        """
        assert callable(callableObj), "callableObj is not callable"
        self.millis = millis
        self.callable = callableObj
        self.SetArgs(*args, **kwargs)
        self.runCount = 0
        self.running = False
        self.hasRun = False
        self.result = None
        self.timer = None
        self.Start()

    def __del__(self):
        self.Stop()

    def Start(self, millis: typing.Optional[int]=None, *args: _P.args, **kwargs: _P.kwargs) -> None:
        """
        (Re)start the timer
        
        :param int millis: number of milli seconds
        :param args: arguments to be passed to the callable object
        :param kw: keywords to be passed to the callable object
        """
        self.hasRun = False
        if millis is not None:
            self.millis = millis
        if args or kwargs:
            self.SetArgs(*args, **kwargs)
        self.Stop()
        CallLater.__instances[self] = "value irrelevant"  # Maintain a reference to avoid GC
        self.timer = wx.PyTimer(self.Notify)
        self.timer.Start(self.millis, wx.TIMER_ONE_SHOT)
        self.running = True

    Restart = Start
    def Stop(self) -> None:
        """
        Stop and destroy the timer.
        """
        if self in CallLater.__instances:
            del CallLater.__instances[self]
        if self.timer is not None:
            self.timer.Stop()
            self.timer = None

    def GetInterval(self) -> int:
        if self.timer is not None:
            return self.timer.GetInterval()
        else:
            return 0

    def IsRunning(self) -> bool:
        return self.timer is not None and self.timer.IsRunning()

    def SetArgs(self, *args: _P.args, **kwargs: _P.kwargs) -> None:
        """
        (Re)set the args passed to the callable object.  This is
        useful in conjunction with :meth:`Start` if
        you want to schedule a new call to the same callable
        object but with different parameters.
        
        :param args: arguments to be passed to the callable object
        :param kw: keywords to be passed to the callable object
        """
        self.args = args
        self.kwargs = kwargs

    def HasRun(self) -> bool:
        """
        Returns whether or not the callable has run.
        
        :rtype: bool
        """
        return self.hasRun

    def GetResult(self) -> _T:
        """
        Returns the value of the callable.
        
        :rtype: a Python object
        :return: result from callable
        """
        return self.result

    def Notify(self) -> None:
        """
        The timer has expired so call the callable.
        """
        if self.callable and getattr(self.callable, 'im_self', True):
            self.runCount += 1
            self.running = False
            self.result = self.callable(*self.args, **self.kwargs)
        self.hasRun = True
        if not self.running:
            # if it wasn't restarted, then cleanup
            wx.CallAfter(self.Stop)

    Interval = property(GetInterval)
    Result = property(GetResult)

FutureCall = deprecated(CallLater, 'Use CallLater instead.')

def GetDefaultPyEncoding() -> str:
    return "utf-8"
GetDefaultPyEncoding = deprecated(GetDefaultPyEncoding, msg="wxPython now always uses utf-8")

