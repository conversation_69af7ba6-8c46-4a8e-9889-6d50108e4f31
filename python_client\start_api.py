#!/usr/bin/env python3
"""
Startup script for MultipleFinger Bridge REST API
Handles environment setup and graceful startup
"""

import os
import sys
import time
import logging
from dotenv import load_dotenv
from tcp_client import TcpClient, TcpClientError

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_tcp_connection(host: str, port: int, max_retries: int = 5) -> bool:
    """Check if TCP server is available"""
    client = TcpClient(host, port, timeout=5)
    
    for attempt in range(max_retries):
        try:
            logger.info(f"Checking TCP connection to {host}:{port} (attempt {attempt + 1}/{max_retries})")
            if client.test_connection():
                logger.info("✅ TCP connection successful")
                return True
        except TcpClientError as e:
            logger.warning(f"TCP connection failed: {e}")
        
        if attempt < max_retries - 1:
            time.sleep(2)
    
    return False


def main():
    """Main startup function"""
    logger.info("Starting MultipleFinger Bridge REST API")
    
    # Get configuration
    tcp_host = os.getenv('TCP_HOST', 'localhost')
    tcp_port = int(os.getenv('TCP_PORT', '9999'))
    api_port = int(os.getenv('API_PORT', '5000'))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    logger.info(f"Configuration:")
    logger.info(f"  TCP Server: {tcp_host}:{tcp_port}")
    logger.info(f"  API Port: {api_port}")
    logger.info(f"  Debug Mode: {debug}")
    
    # Check TCP connection
    if not check_tcp_connection(tcp_host, tcp_port):
        logger.error("❌ Cannot connect to TCP server")
        logger.error("Please ensure MultipleFinger.exe is running with TCP server enabled")
        return 1
    
    # Import and start Flask app
    try:
        from api import app
        logger.info(f"🚀 Starting REST API server on port {api_port}")
        app.run(
            host='0.0.0.0',
            port=api_port,
            debug=debug
        )
    except ImportError as e:
        logger.error(f"Failed to import Flask app: {e}")
        logger.error("Please install required dependencies: pip install -r requirements.txt")
        return 1
    except Exception as e:
        logger.error(f"Failed to start API server: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
