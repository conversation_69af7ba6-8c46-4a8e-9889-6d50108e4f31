// qtexttable.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTextTableCell
{
%TypeHeaderCode
#include <qtexttable.h>
%End

public:
    QTextTableCell();
    ~QTextTableCell();
    QTextTableCell(const QTextTableCell &o);
    QTextCharFormat format() const;
    void setFormat(const QTextCharFormat &format);
    int row() const;
    int column() const;
    int rowSpan() const;
    int columnSpan() const;
    bool isValid() const;
    QTextCursor firstCursorPosition() const;
    QTextCursor lastCursorPosition() const;
    int tableCellFormatIndex() const;
    bool operator==(const QTextTableCell &other) const;
    bool operator!=(const QTextTableCell &other) const;
};

class QTextTable : public QTextFrame
{
%TypeHeaderCode
#include <qtexttable.h>
%End

public:
    explicit QTextTable(QTextDocument *doc);
    virtual ~QTextTable();
    void resize(int rows, int cols);
    void insertRows(int pos, int num);
    void insertColumns(int pos, int num);
    void removeRows(int pos, int num);
    void removeColumns(int pos, int num);
    void mergeCells(int row, int col, int numRows, int numCols);
    void mergeCells(const QTextCursor &cursor);
    void splitCell(int row, int col, int numRows, int numCols);
    int rows() const;
    int columns() const;
    QTextTableCell cellAt(int row, int col) const;
    QTextTableCell cellAt(int position) const;
    QTextTableCell cellAt(const QTextCursor &c) const;
    QTextCursor rowStart(const QTextCursor &c) const;
    QTextCursor rowEnd(const QTextCursor &c) const;
    QTextTableFormat format() const;
    void setFormat(const QTextTableFormat &aformat);
    void appendRows(int count);
    void appendColumns(int count);
};
