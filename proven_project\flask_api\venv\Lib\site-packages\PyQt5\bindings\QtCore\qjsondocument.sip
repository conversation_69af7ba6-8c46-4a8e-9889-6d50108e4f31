// qjsondocument.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


struct QJsonParseError
{
%TypeHeaderCode
#include <qjsondocument.h>
%End

    enum ParseError
    {
        NoError,
        UnterminatedObject,
        MissingNameSeparator,
        UnterminatedArray,
        MissingValueSeparator,
        IllegalValue,
        TerminationByNumber,
        IllegalNumber,
        IllegalEscapeSequence,
        IllegalUTF8String,
        UnterminatedString,
        MissingObject,
        DeepNesting,
        DocumentTooLarge,
%If (Qt_5_4_0 -)
        GarbageAtEnd,
%End
    };

    QString errorString() const;
    int offset;
    QJsonParseError::ParseError error;
};

class QJsonDocument
{
%TypeHeaderCode
#include <qjsondocument.h>
%End

public:
    QJsonDocument();
    explicit QJsonDocument(const QJsonObject &object);
    explicit QJsonDocument(const QJsonArray &array);
    QJsonDocument(const QJsonDocument &other);
    ~QJsonDocument();

    enum DataValidation
    {
        Validate,
        BypassValidation,
    };

    static QJsonDocument fromRawData(const char *data /Encoding="None"/, int size, QJsonDocument::DataValidation validation = QJsonDocument::Validate);
    const char *rawData(int *size /Out/) const /Encoding="None"/;
    static QJsonDocument fromBinaryData(const QByteArray &data, QJsonDocument::DataValidation validation = QJsonDocument::Validate);
    QByteArray toBinaryData() const;
    static QJsonDocument fromVariant(const QVariant &variant);
    QVariant toVariant() const;

    enum JsonFormat
    {
        Indented,
        Compact,
    };

    static QJsonDocument fromJson(const QByteArray &json, QJsonParseError *error = 0);
    QByteArray toJson() const;
    QByteArray toJson(QJsonDocument::JsonFormat format) const;
    bool isEmpty() const;
    bool isArray() const;
    bool isObject() const;
    QJsonObject object() const;
    QJsonArray array() const;
    void setObject(const QJsonObject &object);
    void setArray(const QJsonArray &array);
    bool operator==(const QJsonDocument &other) const;
    bool operator!=(const QJsonDocument &other) const;
    bool isNull() const;
%If (Qt_5_10_0 -)
    void swap(QJsonDocument &other /Constrained/);
%End
%If (Qt_5_10_0 -)
    const QJsonValue operator[](const QString &key) const;
%End
%If (Qt_5_10_0 -)
    const QJsonValue operator[](int i) const;
%End
};

%If (Qt_5_13_0 -)
QDataStream &operator<<(QDataStream &, const QJsonDocument & /Constrained/) /ReleaseGIL/;
%End
%If (Qt_5_13_0 -)
QDataStream &operator>>(QDataStream &, QJsonDocument & /Constrained/) /ReleaseGIL/;
%End
