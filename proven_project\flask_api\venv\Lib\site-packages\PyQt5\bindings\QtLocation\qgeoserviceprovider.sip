// qgeoserviceprovider.sip generated by MetaSIP
//
// This file is part of the QtLocation Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_11_0 -)
class QNavigationManager;
%End
%If (Qt_5_5_0 -)

class QGeoServiceProvider : public QObject
{
%TypeHeaderCode
#include <qgeoserviceprovider.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QGeoCodingManager, &sipType_QGeoCodingManager, -1, 1},
        {sipName_QGeoRoutingManagerEngine, &sipType_QGeoRoutingManagerEngine, -1, 2},
        {sipName_QGeoCodeReply, &sipType_QGeoCodeReply, -1, 3},
        {sipName_QPlaceReply, &sipType_QPlaceReply, 10, 4},
        {sipName_QPlaceManagerEngine, &sipType_QPlaceManagerEngine, -1, 5},
        {sipName_QGeoRoutingManager, &sipType_QGeoRoutingManager, -1, 6},
        {sipName_QGeoCodingManagerEngine, &sipType_QGeoCodingManagerEngine, -1, 7},
        {sipName_QGeoServiceProvider, &sipType_QGeoServiceProvider, -1, 8},
        {sipName_QGeoRouteReply, &sipType_QGeoRouteReply, -1, 9},
        {sipName_QPlaceManager, &sipType_QPlaceManager, -1, -1},
        {sipName_QPlaceSearchSuggestionReply, &sipType_QPlaceSearchSuggestionReply, -1, 11},
        {sipName_QPlaceMatchReply, &sipType_QPlaceMatchReply, -1, 12},
        {sipName_QPlaceDetailsReply, &sipType_QPlaceDetailsReply, -1, 13},
        {sipName_QPlaceContentReply, &sipType_QPlaceContentReply, -1, 14},
        {sipName_QPlaceSearchReply, &sipType_QPlaceSearchReply, -1, 15},
        {sipName_QPlaceIdReply, &sipType_QPlaceIdReply, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum Error
    {
        NoError,
        NotSupportedError,
        UnknownParameterError,
        MissingRequiredParameterError,
        ConnectionError,
%If (Qt_5_12_1 -)
        LoaderError,
%End
    };

    enum RoutingFeature
    {
        NoRoutingFeatures,
        OnlineRoutingFeature,
        OfflineRoutingFeature,
        LocalizedRoutingFeature,
        RouteUpdatesFeature,
        AlternativeRoutesFeature,
        ExcludeAreasRoutingFeature,
        AnyRoutingFeatures,
    };

    enum GeocodingFeature
    {
        NoGeocodingFeatures,
        OnlineGeocodingFeature,
        OfflineGeocodingFeature,
        ReverseGeocodingFeature,
        LocalizedGeocodingFeature,
        AnyGeocodingFeatures,
    };

    enum MappingFeature
    {
        NoMappingFeatures,
        OnlineMappingFeature,
        OfflineMappingFeature,
        LocalizedMappingFeature,
        AnyMappingFeatures,
    };

    enum PlacesFeature
    {
        NoPlacesFeatures,
        OnlinePlacesFeature,
        OfflinePlacesFeature,
        SavePlaceFeature,
        RemovePlaceFeature,
        SaveCategoryFeature,
        RemoveCategoryFeature,
        PlaceRecommendationsFeature,
        SearchSuggestionsFeature,
        LocalizedPlacesFeature,
        NotificationsFeature,
        PlaceMatchingFeature,
        AnyPlacesFeatures,
    };

    typedef QFlags<QGeoServiceProvider::RoutingFeature> RoutingFeatures;
    typedef QFlags<QGeoServiceProvider::GeocodingFeature> GeocodingFeatures;
    typedef QFlags<QGeoServiceProvider::MappingFeature> MappingFeatures;
    typedef QFlags<QGeoServiceProvider::PlacesFeature> PlacesFeatures;
    static QStringList availableServiceProviders();
    QGeoServiceProvider(const QString &providerName, const QVariantMap &parameters = QVariantMap(), bool allowExperimental = false);
    virtual ~QGeoServiceProvider();
    QGeoServiceProvider::RoutingFeatures routingFeatures() const;
    QGeoServiceProvider::GeocodingFeatures geocodingFeatures() const;
    QGeoServiceProvider::MappingFeatures mappingFeatures() const;
    QGeoServiceProvider::PlacesFeatures placesFeatures() const;
    QGeoCodingManager *geocodingManager() const;
    QGeoRoutingManager *routingManager() const;
    QPlaceManager *placeManager() const;
    QGeoServiceProvider::Error error() const;
    QString errorString() const;
    void setParameters(const QVariantMap &parameters);
    void setLocale(const QLocale &locale);
    void setAllowExperimental(bool allow);
%If (Qt_5_11_0 -)

    enum NavigationFeature
    {
        NoNavigationFeatures,
        OnlineNavigationFeature,
        OfflineNavigationFeature,
        AnyNavigationFeatures,
    };

%End
%If (Qt_5_11_0 -)
    typedef QFlags<QGeoServiceProvider::NavigationFeature> NavigationFeatures;
%End
%If (Qt_5_11_0 -)
    QGeoServiceProvider::NavigationFeatures navigationFeatures() const;
%End
%If (Qt_5_11_0 -)
    QNavigationManager *navigationManager() const;
%End
%If (Qt_5_13_0 -)
    QGeoServiceProvider::Error mappingError() const;
%End
%If (Qt_5_13_0 -)
    QString mappingErrorString() const;
%End
%If (Qt_5_13_0 -)
    QGeoServiceProvider::Error geocodingError() const;
%End
%If (Qt_5_13_0 -)
    QString geocodingErrorString() const;
%End
%If (Qt_5_13_0 -)
    QGeoServiceProvider::Error routingError() const;
%End
%If (Qt_5_13_0 -)
    QString routingErrorString() const;
%End
%If (Qt_5_13_0 -)
    QGeoServiceProvider::Error placesError() const;
%End
%If (Qt_5_13_0 -)
    QString placesErrorString() const;
%End
%If (Qt_5_13_0 -)
    QGeoServiceProvider::Error navigationError() const;
%End
%If (Qt_5_13_0 -)
    QString navigationErrorString() const;
%End
};

%End
%If (Qt_5_5_0 -)
QFlags<QGeoServiceProvider::RoutingFeature> operator|(QGeoServiceProvider::RoutingFeature f1, QFlags<QGeoServiceProvider::RoutingFeature> f2);
%End
%If (Qt_5_5_0 -)
QFlags<QGeoServiceProvider::GeocodingFeature> operator|(QGeoServiceProvider::GeocodingFeature f1, QFlags<QGeoServiceProvider::GeocodingFeature> f2);
%End
%If (Qt_5_5_0 -)
QFlags<QGeoServiceProvider::MappingFeature> operator|(QGeoServiceProvider::MappingFeature f1, QFlags<QGeoServiceProvider::MappingFeature> f2);
%End
%If (Qt_5_5_0 -)
QFlags<QGeoServiceProvider::PlacesFeature> operator|(QGeoServiceProvider::PlacesFeature f1, QFlags<QGeoServiceProvider::PlacesFeature> f2);
%End
%If (Qt_5_11_0 -)
QFlags<QGeoServiceProvider::NavigationFeature> operator|(QGeoServiceProvider::NavigationFeature f1, QFlags<QGeoServiceProvider::NavigationFeature> f2);
%End
