# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
This module contains a few classes that are only available on Windows.
"""
#-- begin-_msw --#

import wx

Metafile = wx.deprecated(wx.Metafile, 'Metafile has moved to the core wx module.')
MetafileDC = wx.deprecated(wx.MetafileDC, 'MetafileDC has moved to the core wx module.')
#-- end-_msw --#
#-- begin-axbase --#

class PyAxBaseWindow(wx.Window):
    """
    PyAxBaseWindow(parent, id=-1, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, name=wx.PanelNameStr) -> None
    PyAxBaseWindow() -> None
    
    A Window class for use with ActiveX controls.
    
    This Window class exposes some low-level Microsoft Windows
    specific methods which can be overridden in Python.  Intended for
    use as an ActiveX container, but could also be useful
    elsewhere.
    """

    @overload
    def __init__(self) -> None:
        ...

    @overload
    def __init__(self, parent: wx.Window, id: int=-1, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, name: str=wx.PanelNameStr) -> None:
        """
        PyAxBaseWindow(parent, id=-1, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, name=wx.PanelNameStr) -> None
        PyAxBaseWindow() -> None
        
        A Window class for use with ActiveX controls.
        
        This Window class exposes some low-level Microsoft Windows
        specific methods which can be overridden in Python.  Intended for
        use as an ActiveX container, but could also be useful
        elsewhere.
        """

    def MSWTranslateMessage(self, msg: WXMSG) -> bool:
        """
        MSWTranslateMessage(msg) -> bool
        """
# end of class PyAxBaseWindow

#-- end-axbase --#
