// qpixmap.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPixmap : public QPaintDevice
{
%TypeHeaderCode
#include <qpixmap.h>
%End

public:
    QPixmap();
    QPixmap(int w, int h);
    explicit QPixmap(const QSize &);
    QPixmap(const QString &fileName, const char *format = 0, Qt::ImageConversionFlags flags = Qt::ImageConversionFlag::AutoColor);
    QPixmap(SIP_PYLIST xpm /TypeHint="List[str]"/) [(const char **xpm)];
%MethodCode
        // The Python interface is a list of strings that make up the image.
        
        const char **str = QtGui_ListToArray(a0);
        
        if (str)
        {
            sipCpp = new sipQPixmap(str);
            QtGui_DeleteArray(str);
        }
        else
            sipIsErr = 1;
%End

    QPixmap(const QPixmap &);
    QPixmap(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QPixmap>())
            sipCpp = new sipQPixmap(a0->value<QPixmap>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    virtual ~QPixmap();
    bool isNull() const;
    virtual int devType() const;
    int width() const;
    int height() const;
    QSize size() const;
    QRect rect() const;
    int depth() const;
    static int defaultDepth();
    void fill(const QColor &color = Qt::GlobalColor::white);
    QBitmap mask() const;
    void setMask(const QBitmap &);
    bool hasAlpha() const;
    bool hasAlphaChannel() const;
    QBitmap createHeuristicMask(bool clipTight = true) const;
    QBitmap createMaskFromColor(const QColor &maskColor, Qt::MaskMode mode = Qt::MaskInColor) const;
    QPixmap scaled(int width, int height, Qt::AspectRatioMode aspectRatioMode = Qt::IgnoreAspectRatio, Qt::TransformationMode transformMode = Qt::FastTransformation) const;
    QPixmap scaled(const QSize &size, Qt::AspectRatioMode aspectRatioMode = Qt::IgnoreAspectRatio, Qt::TransformationMode transformMode = Qt::FastTransformation) const;
    QPixmap scaledToWidth(int width, Qt::TransformationMode mode = Qt::FastTransformation) const;
    QPixmap scaledToHeight(int height, Qt::TransformationMode mode = Qt::FastTransformation) const;
    QImage toImage() const;
    static QPixmap fromImage(const QImage &image, Qt::ImageConversionFlags flags = Qt::AutoColor);
    static QPixmap fromImageReader(QImageReader *imageReader, Qt::ImageConversionFlags flags = Qt::AutoColor);
    bool convertFromImage(const QImage &img, Qt::ImageConversionFlags flags = Qt::AutoColor);
    bool load(const QString &fileName, const char *format = 0, Qt::ImageConversionFlags flags = Qt::AutoColor);
    bool loadFromData(const uchar *buf /Array/, uint len /ArraySize/, const char *format = 0, Qt::ImageConversionFlags flags = Qt::AutoColor);
    bool loadFromData(const QByteArray &buf, const char *format = 0, Qt::ImageConversionFlags flags = Qt::AutoColor);
    bool save(const QString &fileName, const char *format = 0, int quality = -1) const;
    bool save(QIODevice *device, const char *format = 0, int quality = -1) const;
    QPixmap copy(const QRect &rect = QRect()) const;
    void detach();
    bool isQBitmap() const;
    virtual QPaintEngine *paintEngine() const;

protected:
    virtual int metric(QPaintDevice::PaintDeviceMetric) const;

public:
    QPixmap copy(int ax, int ay, int awidth, int aheight) const;
    QPixmap transformed(const QTransform &transform, Qt::TransformationMode mode = Qt::FastTransformation) const;
    static QTransform trueMatrix(const QTransform &m, int w, int h);
    qint64 cacheKey() const;
    void scroll(int dx, int dy, const QRect &rect, QRegion *exposed /Out/ = 0);
    void scroll(int dx, int dy, int x, int y, int width, int height, QRegion *exposed /Out/ = 0);
    void swap(QPixmap &other /Constrained/);
    qreal devicePixelRatio() const;
    void setDevicePixelRatio(qreal scaleFactor);
};

QDataStream &operator<<(QDataStream &, const QPixmap & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QPixmap & /Constrained/) /ReleaseGIL/;
