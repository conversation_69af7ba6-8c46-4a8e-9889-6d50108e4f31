#!/usr/bin/env python3
"""
Test script for the MultipleFinger Bridge REST API
Tests the complete workflow from HTTP requests to C# responses
"""

import requests
import json
import time
import base64
import sys
from typing import Dict, Any


class RestApiTester:
    """Test class for REST API endpoints"""
    
    def __init__(self, base_url: str = "http://localhost:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
    
    def test_health_check(self) -> bool:
        """Test health check endpoint"""
        print("Testing Health Check...")
        try:
            response = self.session.get(f"{self.base_url}/api/health")
            data = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(data, indent=2)}")
            
            return response.status_code == 200 and data.get('success', False)
        except Exception as e:
            print(f"Error: {e}")
            return False
    
    def test_status(self) -> bool:
        """Test status endpoint"""
        print("\nTesting Status...")
        try:
            response = self.session.get(f"{self.base_url}/api/status")
            data = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(data, indent=2)}")
            
            return response.status_code == 200 and data.get('success', False)
        except Exception as e:
            print(f"Error: {e}")
            return False
    
    def test_finger_positions(self) -> bool:
        """Test finger positions endpoint"""
        print("\nTesting Finger Positions...")
        try:
            response = self.session.get(f"{self.base_url}/api/fingerprint/positions")
            data = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(data, indent=2)}")
            
            return response.status_code == 200 and data.get('success', False)
        except Exception as e:
            print(f"Error: {e}")
            return False
    
    def test_capture(self) -> bool:
        """Test fingerprint capture endpoint"""
        print("\nTesting Fingerprint Capture...")
        try:
            payload = {
                "finger_position": 1,
                "user_id": "TEST_USER",
                "operation_type": "flat",
                "timeout": 30,
                "save_image": True
            }
            
            response = self.session.post(
                f"{self.base_url}/api/fingerprint/capture",
                json=payload
            )
            data = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Request: {json.dumps(payload, indent=2)}")
            print(f"Response: {json.dumps(data, indent=2)}")
            
            return response.status_code == 200 and data.get('success', False)
        except Exception as e:
            print(f"Error: {e}")
            return False
    
    def test_identify(self) -> bool:
        """Test fingerprint identification endpoint"""
        print("\nTesting Fingerprint Identification...")
        try:
            # MainForm.cs IDENTIFY doesn't require template_data
            payload = {
                "threshold": 70
            }

            response = self.session.post(
                f"{self.base_url}/api/fingerprint/identify",
                json=payload
            )
            data = response.json()

            print(f"Status Code: {response.status_code}")
            print(f"Request: {json.dumps(payload, indent=2)}")
            print(f"Response: {json.dumps(data, indent=2)}")

            return response.status_code == 200 and data.get('success', False)
        except Exception as e:
            print(f"Error: {e}")
            return False

    def test_verify(self) -> bool:
        """Test fingerprint verification endpoint (1:1 matching)"""
        print("\nTesting Fingerprint Verification...")
        try:
            payload = {
                "user_id": "TEST001",
                "finger_position": 1
            }

            response = self.session.post(
                f"{self.base_url}/api/fingerprint/verify",
                json=payload
            )
            data = response.json()

            print(f"Status Code: {response.status_code}")
            print(f"Request: {json.dumps(payload, indent=2)}")
            print(f"Response: {json.dumps(data, indent=2)}")

            return response.status_code == 200 and data.get('success', False)
        except Exception as e:
            print(f"Error: {e}")
            return False
    
    def test_enroll(self) -> bool:
        """Test fingerprint enrollment endpoint"""
        print("\nTesting Fingerprint Enrollment...")
        try:
            # Mock template and image data
            mock_template = base64.b64encode(b"Mock template data for enrollment").decode('utf-8')
            mock_image = base64.b64encode(b"Mock image data for enrollment").decode('utf-8')
            
            payload = {
                "user_id": f"TEST_{int(time.time())}",
                "finger_position": 1,
                "template_data": mock_template,
                "image_data": mock_image,
                "image_quality": 85
            }
            
            response = self.session.post(
                f"{self.base_url}/api/fingerprint/enroll",
                json=payload
            )
            data = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Request: {json.dumps(payload, indent=2)}")
            print(f"Response: {json.dumps(data, indent=2)}")
            
            return response.status_code == 200 and data.get('success', False)
        except Exception as e:
            print(f"Error: {e}")
            return False
    
    def test_captured_data(self) -> bool:
        """Test get captured data endpoint"""
        print("\nTesting Get Captured Data...")
        try:
            response = self.session.get(f"{self.base_url}/api/fingerprint/captured-data")
            data = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(data, indent=2)}")
            
            # This might fail if no GUI is running, but that's expected
            return response.status_code in [200, 503]  # Accept both success and service unavailable
        except Exception as e:
            print(f"Error: {e}")
            return False
    
    def test_validation_errors(self) -> bool:
        """Test validation error handling"""
        print("\nTesting Validation Errors...")
        try:
            # Test invalid finger position
            payload = {
                "finger_position": 15,  # Invalid position
                "operation_type": "flat"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/fingerprint/capture",
                json=payload
            )
            data = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Request: {json.dumps(payload, indent=2)}")
            print(f"Response: {json.dumps(data, indent=2)}")
            
            # Should return 400 for validation error
            return response.status_code == 400 and not data.get('success', True)
        except Exception as e:
            print(f"Error: {e}")
            return False
    
    def test_api_info(self) -> bool:
        """Test API information endpoint"""
        print("\nTesting API Information...")
        try:
            response = self.session.get(f"{self.base_url}/")
            data = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(data, indent=2)}")
            
            return response.status_code == 200 and 'name' in data
        except Exception as e:
            print(f"Error: {e}")
            return False


def main():
    """Run all API tests"""
    print("MultipleFinger Bridge REST API Test Suite")
    print("=" * 50)
    
    # Check if API server is running
    try:
        response = requests.get("http://localhost:5001/api/health", timeout=5)
        if response.status_code != 200:
            print("❌ REST API server is not responding correctly")
            print("Please start the API server with: python api.py")
            return 1
    except requests.exceptions.RequestException:
        print("❌ REST API server is not running")
        print("Please start the API server with: python api.py")
        return 1
    
    tester = RestApiTester()
    
    tests = [
        ("API Information", tester.test_api_info),
        ("Health Check", tester.test_health_check),
        ("Status", tester.test_status),
        ("Finger Positions", tester.test_finger_positions),
        ("Capture", tester.test_capture),
        ("Identify", tester.test_identify),
        ("Verify", tester.test_verify),
        ("Enroll", tester.test_enroll),
        ("Captured Data", tester.test_captured_data),
        ("Validation Errors", tester.test_validation_errors)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
        except Exception as e:
            print(f"❌ ERROR {test_name}: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # Small delay between tests
    
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! REST API is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
