// qsgengine.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_4_0 -)

class QSGEngine : public QObject
{
%TypeHeaderCode
#include <qsgengine.h>
%End

public:
    enum CreateTextureOption
    {
        TextureHasAlphaChannel,
        TextureOwnsGLTexture,
        TextureCanUseAtlas,
%If (Qt_5_6_0 -)
        TextureIsOpaque,
%End
    };

    typedef QFlags<QSGEngine::CreateTextureOption> CreateTextureOptions;
%If (Qt_5_6_1 -)
    explicit QSGEngine(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QSGEngine(QObject *parent /TransferThis/ = 0);
%End
    virtual ~QSGEngine();
%If (PyQt_OpenGL)
    void initialize(QOpenGLContext *context);
%End
    void invalidate();
    QSGAbstractRenderer *createRenderer() const;
    QSGTexture *createTextureFromImage(const QImage &image, QSGEngine::CreateTextureOptions options = QSGEngine::CreateTextureOption()) const;
    QSGTexture *createTextureFromId(uint id, const QSize &size, QSGEngine::CreateTextureOptions options = QSGEngine::CreateTextureOption()) const;
%If (Qt_5_8_0 -)
    QSGRendererInterface *rendererInterface() const;
%End
%If (Qt_5_8_0 -)
    QSGRectangleNode *createRectangleNode() const /Factory/;
%End
%If (Qt_5_8_0 -)
    QSGImageNode *createImageNode() const /Factory/;
%End
};

%End
