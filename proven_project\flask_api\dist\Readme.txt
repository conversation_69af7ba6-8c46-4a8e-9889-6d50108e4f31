1. Copy FingerprintAPI.exe in folder, for example: c:\FingerPrint

2. Add a shortcut to the Startup folder in Windows to run the executable automatically on system startup.

Instructions:

Press Win + R, type "shell:startup", and press Enter. This opens the Startup folder.
In the Startup folder, right-click and select New > Shortcut.
Browse to the location of your FingerprintAPI.exe file in the dist folder and select it.
Click Next, give the shortcut a name (e.g., FingerprintAPI), and click Finish.
The executable will now run automatically when Windows starts.
This method ensures your Flask API server starts automatically on Windows startup.

