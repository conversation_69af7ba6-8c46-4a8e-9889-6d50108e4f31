<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>APIS TrustFinger - MultiFinger Bridge Interface</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f0f0f0;
    }
    
    .main-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 20px;
    }

    .control-section {
      margin: 20px 0;
      padding: 15px;
      border: 2px solid #ccc;
      border-radius: 5px;
    }
    
    .control-section h3 {
      margin-top: 0;
      color: #333;
    }

    label {
      display: block;
      margin-top: 10px;
      font-weight: bold;
    }
    
    input, select, button {
      margin-top: 5px;
      padding: 8px;
      font-size: 1em;
    }
    
    button {
      margin-right: 10px;
      cursor: pointer;
      border: 1px solid #ccc;
      background: #f8f9fa;
      border-radius: 3px;
    }
    
    button:hover {
      background: #e9ecef;
    }
    
    button:disabled {
      background: #6c757d;
      color: white;
      cursor: not-allowed;
    }
    
    .btn-primary {
      background: #007bff;
      color: white;
      border-color: #007bff;
    }
    
    .btn-success {
      background: #28a745;
      color: white;
      border-color: #28a745;
    }

    .finger-selection {
      display: flex;
      gap: 20px;
      margin: 15px 0;
      flex-wrap: wrap;
    }
    
    .finger-item {
      display: flex;
      align-items: center;
      gap: 5px;
    }

    #status {
      margin-top: 15px;
      font-weight: bold;
      color: #0b5394;
    }
    
    #response {
      margin-top: 10px;
      white-space: pre-wrap;
      background-color: #f4f4f4;
      padding: 10px;
      border: 1px solid #ccc;
      max-height: 300px;
      overflow-y: auto;
    }
    
    #bmpImage {
      margin-top: 20px;
      max-width: 300px;
      max-height: 300px;
      border: 1px solid #ccc;
    }
    
    .spinner {
      display: inline-block;
      width: 24px;
      height: 24px;
      border: 4px solid #ccc;
      border-top: 4px solid #4caf50;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      vertical-align: middle;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .device-info {
      font-size: 12px;
      margin: 10px 0;
    }
    
    .device-info-row {
      margin: 3px 0;
    }
    
    .device-info-row label {
      display: inline-block;
      width: 100px;
      font-weight: normal;
    }
  </style>
</head>
<body>
  <div class="main-container">
    <h1>APIS TrustFinger</h1>

    <!-- Device Status Section -->
    <div class="control-section">
      <h3>Device Status</h3>
      <button id="openDeviceBtn" class="btn-primary">Open Device</button>
      <div id="status">Checking device status...</div>
      
      <!-- Device Info Display -->
      <div class="device-info">
        <div class="device-info-row">
          <label>Status:</label>
          <span id="deviceStatus">Checking...</span>
        </div>
        <div class="device-info-row">
          <label>Connection:</label>
          <span id="connectionStatus">Unknown</span>
        </div>
      </div>
    </div>

    <!-- User Information Section -->
    <div class="control-section">
      <h3>User Information</h3>
      
      <label for="personId">User ID:</label>
      <input type="text" id="personId" placeholder="Enter User ID" />
      
      <label for="userName">User Name:</label>
      <input type="text" id="userName" placeholder="Enter User Name" />
    </div>

    <!-- Finger Selection Section -->
    <div class="control-section">
      <h3>Finger Selection (Slaps)</h3>
      
      <div class="finger-selection">
        <div class="finger-item">
          <input type="checkbox" id="leftFourCheck" disabled>
          <label for="leftFourCheck">Left Four Fingers</label>
        </div>
        <div class="finger-item">
          <input type="checkbox" id="rightFourCheck" disabled>
          <label for="rightFourCheck">Right Four Fingers</label>
        </div>
        <div class="finger-item">
          <input type="checkbox" id="twoThumbsCheck" disabled>
          <label for="twoThumbsCheck">Two Thumbs</label>
        </div>
      </div>
    </div>

    <!-- Action Buttons Section -->
    <div class="control-section">
      <h3>Actions</h3>
      
      <button id="captureBtn" disabled>Capture</button>
      <button id="enrollBtn" class="btn-success" disabled>Enroll</button>
      <button id="identifyBtn" disabled>Identify</button>
      <button id="clearBtn">Clear</button>
    </div>

    <!-- Results Section -->
    <div class="control-section">
      <h3>Results</h3>
      <div id="response"></div>
      <img id="bmpImage" alt="Fingerprint BMP Image" style="display:none;" />
    </div>
  </div>

  <script>
    const apiBaseUrl = 'http://localhost:5001/api';

    const personIdInput = document.getElementById('personId');
    const userNameInput = document.getElementById('userName');
    const responseDiv = document.getElementById('response');
    const bmpImage = document.getElementById('bmpImage');
    const statusDiv = document.getElementById('status');
    const deviceStatusSpan = document.getElementById('deviceStatus');
    const connectionStatusSpan = document.getElementById('connectionStatus');
    
    const openDeviceBtn = document.getElementById('openDeviceBtn');
    const captureBtn = document.getElementById('captureBtn');
    const enrollBtn = document.getElementById('enrollBtn');
    const identifyBtn = document.getElementById('identifyBtn');
    const clearBtn = document.getElementById('clearBtn');
    
    const leftFourCheck = document.getElementById('leftFourCheck');
    const rightFourCheck = document.getElementById('rightFourCheck');
    const twoThumbsCheck = document.getElementById('twoThumbsCheck');

    let deviceOpen = false;
    let capturedTemplates = {};
    let selectedFingers = [];

    // Finger mapping for slaps
    const slapsMapping = {
      'leftfour': { id: 12, name: 'Left Four Fingers' },
      'rightfour': { id: 13, name: 'Right Four Fingers' },
      'twothumbs': { id: 11, name: 'Two Thumbs' }
    };

    function setInputsDisabled(disabled) {
      captureBtn.disabled = disabled || !deviceOpen;
      enrollBtn.disabled = disabled || !deviceOpen;
      identifyBtn.disabled = disabled || !deviceOpen;
      leftFourCheck.disabled = disabled || !deviceOpen;
      rightFourCheck.disabled = disabled || !deviceOpen;
      twoThumbsCheck.disabled = disabled || !deviceOpen;
    }

    function displayStatus(text) {
      statusDiv.textContent = text;
    }

    function displayResponse(data) {
      if (data.error) {
        responseDiv.innerHTML = `<span style="color: red;">❌ Error:</span> ${data.error}`;
      } else if (data.status === "success") {
        responseDiv.innerHTML = `<span style="color: green;">✅ Operation completed successfully.</span>`;
      } else if (data.success) {
        responseDiv.innerHTML = `<span style="color: green;">✅ Operation completed successfully.</span>`;
      } else {
        responseDiv.textContent = JSON.stringify(data, null, 2);
      }
    }

    function displayBmpImage(base64Bmp) {
      if (base64Bmp) {
        bmpImage.src = 'data:image/bmp;base64,' + base64Bmp;
        bmpImage.style.display = 'block';
      } else {
        bmpImage.style.display = 'none';
        bmpImage.src = '';
      }
    }

    async function callApi(endpoint, body, label) {
      displayStatus(`⏳ Processing: ${label}...`);
      responseDiv.innerHTML = `<div><span class="spinner"></span>Processing <b>${label}</b>...</div>`;

      displayBmpImage(null);
      setInputsDisabled(true);

      try {
        const response = await fetch(apiBaseUrl + endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: body ? JSON.stringify(body) : null
        });

        const data = await response.json();
        displayResponse(data);
        displayStatus(`✅ Completed: ${label}`);

        if (data.bmp_base64 || (data.data && data.data.image_data)) {
          displayBmpImage(data.bmp_base64 || data.data.image_data);
        }

        return data;
      } catch (error) {
        displayStatus(`❌ Error: ${error.message}`);
        displayResponse({ error: error.message });
        displayBmpImage(null);
        throw error;
      } finally {
        setInputsDisabled(false);
      }
    }

    async function checkDeviceStatus() {
      try {
        displayStatus('Checking device status...');

        // First check if bridge application is running
        const healthResponse = await fetch(apiBaseUrl + '/health');
        const healthData = await healthResponse.json();

        console.log('Health check result:', healthData);

        if (healthData.success && healthData.data && healthData.data.tcp_connection === 'connected') {
          connectionStatusSpan.textContent = 'Connected';

          // Then check device status
          const statusResponse = await fetch(apiBaseUrl + '/status');
          const statusData = await statusResponse.json();

          console.log('Device status result:', statusData);

          if (statusData.success && statusData.data) {
            const isDeviceConnected = statusData.data.DeviceConnected || statusData.data.device_connected || false;

            if (isDeviceConnected) {
              deviceOpen = true;
              deviceStatusSpan.textContent = 'Open and Ready';
              openDeviceBtn.textContent = 'Refresh Status';
              openDeviceBtn.className = 'btn-success';
              displayStatus('✅ Device is open and ready for fingerprint operations');
              displayResponse({ status: 'success', message: 'Device connected and ready. You can now capture fingerprints.' });
            } else {
              deviceOpen = false;
              deviceStatusSpan.textContent = 'Not Connected';
              openDeviceBtn.textContent = 'Refresh Status';
              openDeviceBtn.className = 'btn-primary';
              displayStatus('❌ Device not connected - Please ensure device is plugged in and opened in MainForm');
              displayResponse({ error: 'Device is not connected. Please ensure the device is plugged in and the C# bridge application has opened it.' });
            }
          } else {
            throw new Error('Invalid status response');
          }
        } else {
          connectionStatusSpan.textContent = 'Disconnected';
          deviceStatusSpan.textContent = 'Unknown';
          deviceOpen = false;
          openDeviceBtn.textContent = 'Refresh Status';
          openDeviceBtn.className = 'btn-primary';
          displayStatus('❌ Bridge application not running');
          displayResponse({ error: 'Bridge application is not running. Please start the MultiFingerDemo.exe application first.' });
        }
      } catch (error) {
        console.error('Device status check failed:', error);
        connectionStatusSpan.textContent = 'Error';
        deviceStatusSpan.textContent = 'Unknown';
        deviceOpen = false;
        openDeviceBtn.textContent = 'Refresh Status';
        openDeviceBtn.className = 'btn-primary';
        displayStatus('❌ Failed to check device status: ' + error.message);
        displayResponse({ error: 'Failed to check device status. Please ensure the bridge application is running.' });
      }

      setInputsDisabled(false);
    }

    function handleFingerSelection(fingerId, isChecked) {
      if (isChecked) {
        if (!selectedFingers.includes(fingerId)) {
          selectedFingers.push(fingerId);
        }
      } else {
        selectedFingers = selectedFingers.filter(f => f !== fingerId);
      }
    }

    // Event handlers
    openDeviceBtn.addEventListener('click', () => {
      // Just refresh the device status instead of showing error
      displayStatus('Refreshing device status...');
      checkDeviceStatus();
    });

    captureBtn.addEventListener('click', async () => {
      if (selectedFingers.length === 0) {
        alert('Please select at least one finger option.');
        return;
      }

      const captureResults = [];

      for (const fingerId of selectedFingers) {
        const fingerMapping = slapsMapping[fingerId];
        if (!fingerMapping) continue;

        const captureData = {
          finger_position: fingerMapping.id,
          operation_type: 'slaps',
          timeout: 30,
          save_image: true
        };

        try {
          console.log(`Calling capture API for ${fingerId}:`, captureData);
          const result = await callApi('/fingerprint/capture', captureData, `Capture ${fingerMapping.name}`);

          if (result.success && result.data) {
            capturedTemplates[fingerId] = {
              template: result.data.template_data || '',
              image: result.data.image_data || '',
              quality: result.data.quality || 'N/A'
            };
            captureResults.push(`${fingerMapping.name} captured successfully`);
          } else {
            captureResults.push(`${fingerMapping.name} failed: ${result.error || result.message || 'Unknown error'}`);
          }
        } catch (error) {
          captureResults.push(`${fingerMapping.name} failed: ${error.message}`);
        }
      }

      const message = captureResults.join(', ');
      displayResponse({ status: captureResults.some(r => r.includes('failed')) ? 'error' : 'success', message });
    });

    enrollBtn.addEventListener('click', async () => {
      const userId = personIdInput.value.trim();
      if (!userId) {
        alert('Please enter a User ID.');
        return;
      }

      const enrollResults = [];

      for (const [fingerId, templateData] of Object.entries(capturedTemplates)) {
        if (!templateData.template) continue;

        const fingerMapping = slapsMapping[fingerId];
        const enrollData = {
          user_id: userId,
          finger_position: fingerMapping.id,
          template_data: templateData.template,
          image_data: templateData.image || '',
          image_quality: parseInt(templateData.quality) || 0
        };

        try {
          const result = await callApi('/fingerprint/enroll', enrollData, `Enroll ${fingerMapping.name}`);

          if (result.success) {
            enrollResults.push(`${fingerMapping.name} enrolled`);
          } else {
            enrollResults.push(`${fingerMapping.name} enroll failed: ${result.error || result.message || 'Unknown error'}`);
          }
        } catch (error) {
          enrollResults.push(`${fingerMapping.name} enroll failed: ${error.message}`);
        }
      }

      const message = enrollResults.join(', ');
      displayResponse({ status: enrollResults.some(r => r.includes('failed')) ? 'error' : 'success', message });

      if (!enrollResults.some(r => r.includes('failed'))) {
        // Clear captured templates after successful enrollment
        capturedTemplates = {};
        displayBmpImage(null);
      }
    });

    identifyBtn.addEventListener('click', async () => {
      const identifyResults = [];

      for (const [fingerId, templateData] of Object.entries(capturedTemplates)) {
        if (!templateData.template) continue;

        const fingerMapping = slapsMapping[fingerId];
        const identifyData = {
          template_data: templateData.template,
          threshold: 70
        };

        try {
          const result = await callApi('/fingerprint/identify', identifyData, `Identify ${fingerMapping.name}`);

          if (result.success && result.data) {
            const userId = result.data.user_id || 'Unknown';
            const score = result.data.score || 'N/A';
            identifyResults.push(`${fingerMapping.name}: ${userId} (Score: ${score})`);
          } else {
            identifyResults.push(`${fingerMapping.name}: No match`);
          }
        } catch (error) {
          identifyResults.push(`${fingerMapping.name}: Error - ${error.message}`);
        }
      }

      const message = identifyResults.join(', ');
      displayResponse({ status: 'success', message });
    });

    clearBtn.addEventListener('click', () => {
      capturedTemplates = {};
      displayBmpImage(null);
      responseDiv.innerHTML = '';
      displayStatus('Images and data cleared');
    });

    leftFourCheck.addEventListener('change', () => {
      handleFingerSelection('leftfour', leftFourCheck.checked);
    });

    rightFourCheck.addEventListener('change', () => {
      handleFingerSelection('rightfour', rightFourCheck.checked);
    });

    twoThumbsCheck.addEventListener('change', () => {
      handleFingerSelection('twothumbs', twoThumbsCheck.checked);
    });

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', () => {
      checkDeviceStatus();
      setInterval(checkDeviceStatus, 5000); // Check every 5 seconds
    });
  </script>
</body>
</html>
