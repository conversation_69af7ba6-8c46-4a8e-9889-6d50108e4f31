<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MultipleFinger Bridge - API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        
        .status-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .section h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .finger-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .finger-btn {
            padding: 15px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            background: white;
            transition: background-color 0.2s;
        }
        
        .finger-btn:hover {
            background-color: #f8f9fa;
        }
        
        .finger-btn.captured {
            background-color: #d4edda;
            border-color: #28a745;
        }
        
        .results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result-item, .captured-item {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            border-left: 3px solid #007bff;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected {
            background-color: #28a745;
        }
        
        .status-disconnected {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MultipleFinger Bridge - API Test</h1>
            <p>Direct JavaScript Interface to Python REST API</p>
        </div>

        <!-- Message Area -->
        <div id="messageArea"></div>

        <!-- Status Panel -->
        <div class="status-panel">
            <h3>System Status</h3>
            <p>
                <strong>API Status:</strong> <span id="apiStatus">Checking...</span>
            </p>
            <p>
                <strong>Device Status:</strong> <span id="deviceStatus">Checking...</span>
            </p>
            <button id="healthCheckBtn" class="btn btn-primary">Refresh Status</button>
        </div>

        <!-- Fingerprint Capture Section -->
        <div class="section">
            <h3>Fingerprint Capture</h3>
            <div class="form-group">
                <label for="operationType">Operation Type:</label>
                <select id="operationType">
                    <option value="flat">Flat</option>
                    <option value="rolled">Rolled</option>
                    <option value="slaps">Slaps</option>
                </select>
            </div>
            
            <div class="finger-grid">
                <div class="finger-btn" data-capture-position="1" data-operation-type="flat">
                    Right Thumb (1)
                </div>
                <div class="finger-btn" data-capture-position="2" data-operation-type="flat">
                    Right Index (2)
                </div>
                <div class="finger-btn" data-capture-position="3" data-operation-type="flat">
                    Right Middle (3)
                </div>
                <div class="finger-btn" data-capture-position="4" data-operation-type="flat">
                    Right Ring (4)
                </div>
                <div class="finger-btn" data-capture-position="5" data-operation-type="flat">
                    Right Little (5)
                </div>
                <div class="finger-btn" data-capture-position="6" data-operation-type="flat">
                    Left Thumb (6)
                </div>
                <div class="finger-btn" data-capture-position="7" data-operation-type="flat">
                    Left Index (7)
                </div>
                <div class="finger-btn" data-capture-position="8" data-operation-type="flat">
                    Left Middle (8)
                </div>
                <div class="finger-btn" data-capture-position="9" data-operation-type="flat">
                    Left Ring (9)
                </div>
                <div class="finger-btn" data-capture-position="10" data-operation-type="flat">
                    Left Little (10)
                </div>
            </div>
            
            <button id="getCapturedBtn" class="btn btn-warning">Get Captured Data</button>
        </div>

        <!-- Enrollment Section -->
        <div class="section">
            <h3>Fingerprint Enrollment</h3>
            <form id="enrollForm">
                <div class="form-group">
                    <label for="enrollUserId">User ID:</label>
                    <input type="text" id="enrollUserId" name="userId" required placeholder="Enter user ID">
                </div>
                <div class="form-group">
                    <label for="enrollFingerPosition">Finger Position:</label>
                    <select id="enrollFingerPosition" name="fingerPosition" required>
                        <option value="">Select finger position</option>
                        <option value="1">Right Thumb (1)</option>
                        <option value="2">Right Index (2)</option>
                        <option value="3">Right Middle (3)</option>
                        <option value="4">Right Ring (4)</option>
                        <option value="5">Right Little (5)</option>
                        <option value="6">Left Thumb (6)</option>
                        <option value="7">Left Index (7)</option>
                        <option value="8">Left Middle (8)</option>
                        <option value="9">Left Ring (9)</option>
                        <option value="10">Left Little (10)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="enrollTemplateData">Template Data (Base64):</label>
                    <textarea id="enrollTemplateData" name="templateData" required placeholder="Paste base64 encoded template data here"></textarea>
                </div>
                <button type="submit" class="btn btn-success">Enroll Fingerprint</button>
            </form>
        </div>

        <!-- Identification Section -->
        <div class="section">
            <h3>Fingerprint Identification</h3>
            <form id="identifyForm">
                <div class="form-group">
                    <label for="identifyTemplateData">Template Data (Base64):</label>
                    <textarea id="identifyTemplateData" name="templateData" required placeholder="Paste base64 encoded template data here"></textarea>
                </div>
                <div class="form-group">
                    <label for="identifyThreshold">Matching Threshold (0-100):</label>
                    <input type="number" id="identifyThreshold" name="threshold" min="0" max="100" value="70">
                </div>
                <button type="submit" class="btn btn-warning">Identify Fingerprint</button>
            </form>
        </div>

        <!-- Results Display -->
        <div id="identificationResults" class="results" style="display: none;"></div>
        <div id="capturedDataDisplay" class="results" style="display: none;"></div>
    </div>

    <script src="app.js"></script>
    
    <script>
        // Update operation type for capture buttons
        document.getElementById('operationType').addEventListener('change', function() {
            const operationType = this.value;
            const captureButtons = document.querySelectorAll('[data-capture-position]');
            captureButtons.forEach(button => {
                button.dataset.operationType = operationType;
            });
        });
        
        // Show/hide results sections
        function showResults(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'block';
            }
        }
        
        // Override display functions to show results
        const originalDisplayIdentificationResults = window.MultiFinger ? window.MultiFinger.displayIdentificationResults : null;
        const originalDisplayCapturedData = window.MultiFinger ? window.MultiFinger.displayCapturedData : null;
        
        // Wait for MultiFinger to be available
        setTimeout(() => {
            if (window.MultiFinger) {
                const originalIdentify = window.MultiFinger.identifyFingerprint;
                window.MultiFinger.identifyFingerprint = async function(...args) {
                    const result = await originalIdentify.apply(this, args);
                    showResults('identificationResults');
                    return result;
                };
                
                const originalGetCaptured = window.MultiFinger.getCapturedData;
                window.MultiFinger.getCapturedData = async function(...args) {
                    const result = await originalGetCaptured.apply(this, args);
                    showResults('capturedDataDisplay');
                    return result;
                };
            }
        }, 1000);
    </script>
</body>
</html>
