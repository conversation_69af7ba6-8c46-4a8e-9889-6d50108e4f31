<?php
session_start();

// Set CSP header to allow our JavaScript
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' http://localhost:5001; img-src 'self' data:;");

// Initialize session variables
if (!isset($_SESSION['deviceOpen'])) {
    $_SESSION['deviceOpen'] = false;
}

if (!isset($_SESSION['capturedTemplates'])) {
    $_SESSION['capturedTemplates'] = [];
}

if (!isset($_SESSION['selectedFingers'])) {
    $_SESSION['selectedFingers'] = [];
}

if (!isset($_SESSION['currentTab'])) {
    $_SESSION['currentTab'] = 0; // 0=slaps, 1=rolled, 2=flat
}

if (!isset($_SESSION['deviceInfo'])) {
    $_SESSION['deviceInfo'] = [
        'status' => 'TCP API Running',
        'device_connected' => false,
        'api_version' => '1.0.0',
        'tcp_port' => 9999,
        'uptime' => '00:00:00'
    ];
}

// Updated API Configuration - Using new Python REST API (port 5001)
$API_BASE_URL = "http://localhost:5001/api";

// Helper function to call new Python REST API
function callAPI($endpoint, $method = 'GET', $data = null) {
    global $API_BASE_URL;

    $url = $API_BASE_URL . '/' . $endpoint;
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            $jsonData = json_encode($data);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            error_log("API Call: $method $url with data: $jsonData");
        }
    } else {
        error_log("API Call: $method $url");
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    error_log("API Response: HTTP $httpCode, Response: $response" . ($curlError ? ", CURL Error: $curlError" : ""));

    if ($curlError) {
        return ['success' => false, 'error' => "Connection error: $curlError"];
    }

    if ($httpCode !== 200) {
        return ['success' => false, 'error' => "API call failed: HTTP $httpCode"];
    }

    $decoded = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("JSON decode error: " . json_last_error_msg());
        return ['success' => false, 'error' => 'Invalid JSON response'];
    }

    return $decoded;
}

// Updated finger mapping to match new API (1-10 positions)
$fingerMapping = [
    'R1' => ['id' => 1, 'label' => 'R1', 'name' => 'Right Thumb'],
    'R2' => ['id' => 2, 'label' => 'R2', 'name' => 'Right Index'],
    'R3' => ['id' => 3, 'label' => 'R3', 'name' => 'Right Middle'],
    'R4' => ['id' => 4, 'label' => 'R4', 'name' => 'Right Ring'],
    'R5' => ['id' => 5, 'label' => 'R5', 'name' => 'Right Little'],
    'L1' => ['id' => 6, 'label' => 'L1', 'name' => 'Left Thumb'],
    'L2' => ['id' => 7, 'label' => 'L2', 'name' => 'Left Index'],
    'L3' => ['id' => 8, 'label' => 'L3', 'name' => 'Left Middle'],
    'L4' => ['id' => 9, 'label' => 'L4', 'name' => 'Left Ring'],
    'L5' => ['id' => 10, 'label' => 'L5', 'name' => 'Left Little'],
];

// Tab mapping
$tabMapping = [
    0 => ['name' => 'slaps', 'title' => 'Slaps Fingerprints'],
    1 => ['name' => 'rolled', 'title' => 'Rolled Fingerprints'],
    2 => ['name' => 'flat', 'title' => 'Flat Fingerprints']
];

$message = '';
$messageType = 'info';

// Handle form submissions using new Python REST API
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'checkStatus':
            // Check API health and status
            $healthResult = callAPI('health', 'GET');
            if ($healthResult && $healthResult['success']) {
                $statusResult = callAPI('status', 'GET');
                if ($statusResult && $statusResult['success']) {
                    $_SESSION['deviceInfo'] = $statusResult['data'];
                    $_SESSION['deviceOpen'] = $statusResult['data']['device_connected'] ?? false;
                    $message = 'Status updated successfully';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to get status: ' . ($statusResult['error'] ?? 'Unknown error');
                    $messageType = 'error';
                }
            } else {
                $message = 'Python REST API is not running. Please start it with: python start_api.py';
                $messageType = 'error';
            }
            break;

        case 'switchTab':
            $currentTab = (int)($_POST['currentTab'] ?? 0);
            $_SESSION['currentTab'] = $currentTab;
            break;
            
        case 'capture':
            // Capture fingerprint using new API
            $selectedFingers = $_POST['selectedFingers'] ?? [];
            $currentTab = $_SESSION['currentTab'] ?? 0;

            if (empty($selectedFingers)) {
                $message = 'Please select at least one finger';
                $messageType = 'error';
                break;
            }

            $operationType = 'flat'; // Default
            if ($currentTab == 0) $operationType = 'slaps';
            elseif ($currentTab == 1) $operationType = 'rolled';
            elseif ($currentTab == 2) $operationType = 'flat';

            $captureResults = [];
            $successCount = 0;

            foreach ($selectedFingers as $fingerId) {
                if (isset($fingerMapping[$fingerId])) {
                    $fingerPos = $fingerMapping[$fingerId]['id'];
                    $fingerName = $fingerMapping[$fingerId]['name'];

                    $captureData = [
                        'finger_position' => $fingerPos,
                        'operation_type' => $operationType,
                        'timeout' => 30,
                        'save_image' => true
                    ];

                    $result = callAPI('fingerprint/capture', 'POST', $captureData);
                    
                    if ($result && $result['success']) {
                        $captureResults[] = [
                            'finger_id' => $fingerId,
                            'finger_name' => $fingerName,
                            'data' => $result['data']
                        ];
                        
                        // Store in session for later use
                        $_SESSION['capturedTemplates'][$fingerId] = $result['data'];
                        $successCount++;
                    } else {
                        $error = $result['error'] ?? 'Unknown error';
                        $captureResults[] = [
                            'finger_id' => $fingerId,
                            'finger_name' => $fingerName,
                            'error' => $error
                        ];
                    }
                }
            }

            if ($successCount > 0) {
                $message = "Successfully captured $successCount fingerprint(s)";
                $messageType = 'success';
            } else {
                $message = 'Failed to capture any fingerprints';
                $messageType = 'error';
            }
            break;

        case 'enroll':
            // Enroll fingerprints using new API
            $userId = $_POST['userId'] ?? '';
            if (empty($userId)) {
                $message = 'Please enter a User ID';
                $messageType = 'error';
                break;
            }

            if (empty($_SESSION['capturedTemplates'])) {
                $message = 'No captured templates available. Please capture fingerprints first.';
                $messageType = 'error';
                break;
            }

            $enrollResults = [];
            $successCount = 0;

            foreach ($_SESSION['capturedTemplates'] as $fingerId => $templateData) {
                if (isset($fingerMapping[$fingerId])) {
                    $fingerPos = $fingerMapping[$fingerId]['id'];
                    
                    $enrollData = [
                        'user_id' => $userId,
                        'finger_position' => $fingerPos,
                        'template_data' => $templateData['template_data'] ?? '',
                        'image_data' => $templateData['image_data'] ?? '',
                        'image_quality' => $templateData['quality'] ?? 0
                    ];

                    $result = callAPI('fingerprint/enroll', 'POST', $enrollData);
                    
                    if ($result && $result['success']) {
                        $enrollResults[] = [
                            'finger_id' => $fingerId,
                            'success' => true
                        ];
                        $successCount++;
                    } else {
                        $error = $result['error'] ?? 'Unknown error';
                        $enrollResults[] = [
                            'finger_id' => $fingerId,
                            'success' => false,
                            'error' => $error
                        ];
                    }
                }
            }

            if ($successCount > 0) {
                $message = "Successfully enrolled $successCount fingerprint(s) for user: $userId";
                $messageType = 'success';
                // Clear captured templates after successful enrollment
                $_SESSION['capturedTemplates'] = [];
            } else {
                $message = 'Failed to enroll any fingerprints';
                $messageType = 'error';
            }
            break;

        case 'identify':
            // Identify fingerprints using new API
            if (empty($_SESSION['capturedTemplates'])) {
                $message = 'No captured templates available. Please capture fingerprints first.';
                $messageType = 'error';
                break;
            }

            $identifyResults = [];
            $matchCount = 0;

            foreach ($_SESSION['capturedTemplates'] as $fingerId => $templateData) {
                $identifyData = [
                    'template_data' => $templateData['template_data'] ?? '',
                    'threshold' => 70
                ];

                $result = callAPI('fingerprint/identify', 'POST', $identifyData);
                
                if ($result && $result['success']) {
                    $matches = $result['data']['matches'] ?? [];
                    if (!empty($matches)) {
                        $identifyResults[] = [
                            'finger_id' => $fingerId,
                            'matches' => $matches
                        ];
                        $matchCount += count($matches);
                    }
                }
            }

            if ($matchCount > 0) {
                $message = "Found $matchCount match(es)";
                $messageType = 'success';
            } else {
                $message = 'No matches found';
                $messageType = 'info';
            }
            break;

        case 'getCapturedData':
            // Get captured data from bridge application
            $result = callAPI('fingerprint/captured-data', 'GET');
            if ($result && $result['success']) {
                $capturedData = $result['data']['captured_data'] ?? [];
                if (!empty($capturedData)) {
                    foreach ($capturedData as $data) {
                        // Map back to our finger IDs
                        $fingerPos = $data['finger_position'];
                        $fingerId = array_search($fingerPos, array_column($fingerMapping, 'id'));
                        if ($fingerId) {
                            $_SESSION['capturedTemplates'][$fingerId] = $data;
                        }
                    }
                    $message = 'Retrieved ' . count($capturedData) . ' captured fingerprint(s)';
                    $messageType = 'success';
                } else {
                    $message = 'No captured data available';
                    $messageType = 'info';
                }
            } else {
                $error = $result['error'] ?? 'Unknown error';
                $message = 'Failed to get captured data: ' . $error;
                $messageType = 'error';
            }
            break;

        default:
            $message = 'Unknown action';
            $messageType = 'error';
            break;
    }
}

// Get current device status on page load
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $healthResult = callAPI('health', 'GET');
    if ($healthResult && $healthResult['success']) {
        $statusResult = callAPI('status', 'GET');
        if ($statusResult && $statusResult['success']) {
            $_SESSION['deviceInfo'] = $statusResult['data'];
            $_SESSION['deviceOpen'] = $statusResult['data']['device_connected'] ?? false;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MultipleFinger Bridge - Web Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }

        .status-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }

        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }

        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: none;
            border-bottom: 3px solid transparent;
            font-size: 16px;
        }

        .tab.active {
            border-bottom-color: #007bff;
            color: #007bff;
            font-weight: bold;
        }

        .tab-content {
            display: none;
            padding: 20px 0;
        }

        .tab-content.active {
            display: block;
        }

        .finger-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .finger-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
            background: white;
        }

        .finger-item.captured {
            background-color: #d4edda;
            border-color: #28a745;
        }

        .finger-checkbox {
            margin-right: 10px;
        }

        .button-group {
            margin: 20px 0;
            text-align: center;
        }

        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .form-group {
            margin: 15px 0;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .device-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .device-info strong {
            color: #495057;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected {
            background-color: #28a745;
        }

        .status-disconnected {
            background-color: #dc3545;
        }

        .captured-templates {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }

        .template-item {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            border-left: 3px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MultipleFinger Bridge - Web Demo</h1>
            <p>Python REST API Interface</p>
        </div>

        <?php if ($message): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <!-- Status Panel -->
        <div class="status-panel">
            <h3>System Status</h3>
            <div class="device-info">
                <p>
                    <span class="status-indicator <?php echo $_SESSION['deviceOpen'] ? 'status-connected' : 'status-disconnected'; ?>"></span>
                    <strong>Device Status:</strong> <?php echo $_SESSION['deviceOpen'] ? 'Connected' : 'Disconnected'; ?>
                </p>
                <p><strong>API Status:</strong> <?php echo $_SESSION['deviceInfo']['status'] ?? 'Unknown'; ?></p>
                <p><strong>API Version:</strong> <?php echo $_SESSION['deviceInfo']['api_version'] ?? 'N/A'; ?></p>
                <p><strong>TCP Port:</strong> <?php echo $_SESSION['deviceInfo']['tcp_port'] ?? 'N/A'; ?></p>
                <p><strong>Uptime:</strong> <?php echo $_SESSION['deviceInfo']['uptime'] ?? 'N/A'; ?></p>
            </div>

            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="checkStatus">
                <button type="submit" class="btn btn-primary">Refresh Status</button>
            </form>
        </div>

        <!-- Tabs -->
        <div class="tabs">
            <button class="tab <?php echo $_SESSION['currentTab'] == 0 ? 'active' : ''; ?>" data-tab="0">
                Slaps Fingerprints
            </button>
            <button class="tab <?php echo $_SESSION['currentTab'] == 1 ? 'active' : ''; ?>" data-tab="1">
                Rolled Fingerprints
            </button>
            <button class="tab <?php echo $_SESSION['currentTab'] == 2 ? 'active' : ''; ?>" data-tab="2">
                Flat Fingerprints
            </button>
        </div>

        <!-- Tab Contents -->
        <?php foreach ($tabMapping as $tabIndex => $tabInfo): ?>
            <div class="tab-content <?php echo $_SESSION['currentTab'] == $tabIndex ? 'active' : ''; ?>" id="tab-<?php echo $tabIndex; ?>">
                <h3><?php echo $tabInfo['title']; ?></h3>

                <!-- Finger Selection -->
                <form method="post" id="captureForm<?php echo $tabIndex; ?>">
                    <input type="hidden" name="action" value="capture">
                    <input type="hidden" name="currentTab" value="<?php echo $tabIndex; ?>">

                    <div class="finger-grid">
                        <?php foreach ($fingerMapping as $fingerId => $fingerInfo): ?>
                            <div class="finger-item <?php echo isset($_SESSION['capturedTemplates'][$fingerId]) ? 'captured' : ''; ?>">
                                <label>
                                    <input type="checkbox" name="selectedFingers[]" value="<?php echo $fingerId; ?>" class="finger-checkbox">
                                    <?php echo $fingerInfo['name']; ?> (<?php echo $fingerInfo['label']; ?>)
                                </label>
                                <?php if (isset($_SESSION['capturedTemplates'][$fingerId])): ?>
                                    <div style="color: #28a745; font-size: 12px; margin-top: 5px;">
                                        ✓ Captured
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="button-group">
                        <button type="submit" class="btn btn-primary">Capture Selected</button>
                    </div>
                </form>
            </div>
        <?php endforeach; ?>

        <!-- Captured Templates Display -->
        <?php if (!empty($_SESSION['capturedTemplates'])): ?>
            <div class="captured-templates">
                <h3>Captured Templates</h3>
                <?php foreach ($_SESSION['capturedTemplates'] as $fingerId => $templateData): ?>
                    <div class="template-item">
                        <strong><?php echo $fingerMapping[$fingerId]['name']; ?></strong>
                        <br>
                        Quality: <?php echo $templateData['quality'] ?? 'N/A'; ?>
                        <br>
                        Capture Time: <?php echo $templateData['capture_time'] ?? 'N/A'; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="button-group">
            <!-- Get Captured Data -->
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="getCapturedData">
                <button type="submit" class="btn btn-warning">Get Captured Data</button>
            </form>

            <!-- Enrollment -->
            <?php if (!empty($_SESSION['capturedTemplates'])): ?>
                <form method="post" style="display: inline;">
                    <input type="hidden" name="action" value="enroll">
                    <div class="form-group" style="display: inline-block; margin: 0 10px;">
                        <input type="text" name="userId" placeholder="Enter User ID" required style="width: 200px;">
                    </div>
                    <button type="submit" class="btn btn-success">Enroll User</button>
                </form>

                <!-- Identification -->
                <form method="post" style="display: inline;">
                    <input type="hidden" name="action" value="identify">
                    <button type="submit" class="btn btn-warning">Identify</button>
                </form>
            <?php endif; ?>
        </div>

        <!-- Results Display -->
        <?php if (isset($captureResults)): ?>
            <div class="captured-templates">
                <h3>Capture Results</h3>
                <?php foreach ($captureResults as $result): ?>
                    <div class="template-item">
                        <strong><?php echo $result['finger_name']; ?></strong>
                        <?php if (isset($result['error'])): ?>
                            <span style="color: #dc3545;">Error: <?php echo htmlspecialchars($result['error']); ?></span>
                        <?php else: ?>
                            <span style="color: #28a745;">✓ Success</span>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php if (isset($identifyResults) && !empty($identifyResults)): ?>
            <div class="captured-templates">
                <h3>Identification Results</h3>
                <?php foreach ($identifyResults as $result): ?>
                    <div class="template-item">
                        <strong>Finger: <?php echo $result['finger_id']; ?></strong>
                        <?php foreach ($result['matches'] as $match): ?>
                            <div style="margin: 5px 0; padding: 5px; background: #e9ecef; border-radius: 3px;">
                                User ID: <?php echo htmlspecialchars($match['user_id']); ?><br>
                                Score: <?php echo $match['score']; ?><br>
                                Position: <?php echo $match['finger_position_name']; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            // Setup tab click handlers
            const tabButtons = document.querySelectorAll('.tab[data-tab]');
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const tabIndex = parseInt(this.getAttribute('data-tab'));
                    switchTab(tabIndex);
                });
            });
        });

        function switchTab(tabIndex) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Show selected tab content
            document.getElementById('tab-' + tabIndex).classList.add('active');

            // Add active class to selected tab
            document.querySelectorAll('.tab')[tabIndex].classList.add('active');

            // Use fetch instead of form submission to avoid CSP issues
            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=switchTab&currentTab=' + encodeURIComponent(tabIndex)
            }).catch(error => {
                console.log('Tab switch request failed:', error);
                // Continue anyway - the UI has already switched
            });
        }
    </script>
</body>
</html>
