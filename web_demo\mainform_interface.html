<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>APIS TrustFinger - MultiFinger Bridge Interface</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f0f0;
    }
    
    .main-container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 20px;
      font-size: 24px;
    }

    /* Top Controls Section */
    .top-section {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }
    
    .control-box {
      border: 2px solid #ccc;
      padding: 15px;
      flex: 1;
      border-radius: 5px;
    }
    
    .control-box h3 {
      margin-top: 0;
      font-size: 14px;
      color: #333;
    }

    /* Device Controls */
    .device-controls {
      margin-bottom: 15px;
    }
    
    .btn {
      padding: 8px 16px;
      margin: 3px;
      border: 1px solid #ccc;
      background: #f8f9fa;
      cursor: pointer;
      font-size: 12px;
      border-radius: 3px;
    }
    
    .btn:hover {
      background: #e9ecef;
    }
    
    .btn:disabled {
      background: #6c757d;
      color: white;
      cursor: not-allowed;
    }
    
    .btn-primary {
      background: #007bff;
      color: white;
      border-color: #007bff;
    }
    
    .btn-success {
      background: #28a745;
      color: white;
      border-color: #28a745;
    }
    
    .btn-info {
      background: #17a2b8;
      color: white;
      border-color: #17a2b8;
    }

    /* Input Fields */
    .input-row {
      margin: 10px 0;
    }
    
    .input-row label {
      display: inline-block;
      width: 80px;
      font-size: 12px;
    }
    
    .input-row input, .input-row select {
      padding: 4px;
      font-size: 12px;
      width: 200px;
    }

    /* Finger Selection Grid */
    .finger-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 10px;
      margin: 15px 0;
    }
    
    .finger-item {
      text-align: center;
    }
    
    .finger-item input[type="checkbox"] {
      margin-bottom: 5px;
    }
    
    .finger-item label {
      display: block;
      font-size: 11px;
    }

    /* Device Info Display */
    .device-info {
      font-size: 11px;
      margin: 10px 0;
    }
    
    .device-info-row {
      margin: 3px 0;
    }
    
    .device-info-row label {
      display: inline-block;
      width: 100px;
    }

    /* Message Display */
    .message {
      padding: 8px;
      margin: 10px 0;
      border-radius: 3px;
      font-size: 12px;
    }
    
    .message-success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .message-error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .message-info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    /* Tab Control */
    .tab-control {
      margin-top: 20px;
      border: 1px solid #ccc;
      border-radius: 5px;
    }
    
    .tab-headers {
      display: flex;
      background: #f8f9fa;
      border-bottom: 1px solid #ccc;
      border-radius: 5px 5px 0 0;
    }
    
    .tab-header {
      padding: 8px 16px;
      border-right: 1px solid #ccc;
      cursor: pointer;
      font-size: 12px;
      background: #f8f9fa;
    }
    
    .tab-header:last-child {
      border-right: none;
    }
    
    .tab-header.active {
      background: white;
      border-bottom: 1px solid white;
      margin-bottom: -1px;
    }
    
    .tab-content {
      padding: 20px;
      min-height: 400px;
      background: white;
    }

    /* Image Display Areas */
    .image-display {
      display: flex;
      gap: 20px;
      margin: 20px 0;
      justify-content: center;
    }
    
    .image-box {
      text-align: center;
      flex: 1;
      max-width: 300px;
    }
    
    .image-box h4 {
      font-size: 14px;
      margin-bottom: 10px;
    }
    
    .image-preview {
      width: 280px;
      height: 220px;
      border: 2px solid #000;
      background: white;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .image-preview img {
      max-width: 100%;
      max-height: 100%;
    }
    
    .nfiq-label {
      font-size: 11px;
      margin-top: 5px;
      color: #666;
    }

    /* Individual Finger Images */
    .finger-images {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 15px;
      margin: 20px 0;
    }
    
    .finger-image-box {
      text-align: center;
    }
    
    .finger-image-box h5 {
      font-size: 11px;
      margin-bottom: 5px;
    }
    
    .finger-image-preview {
      width: 120px;
      height: 150px;
      border: 1px solid #ccc;
      background: white;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .finger-image-preview img {
      max-width: 100%;
      max-height: 100%;
    }

    /* Spinner */
    .spinner {
      display: inline-block;
      width: 24px;
      height: 24px;
      border: 4px solid #ccc;
      border-top: 4px solid #4caf50;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      vertical-align: middle;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Hand Images */
    .hand-images {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
    }
    
    .hand-images img {
      width: 150px;
    }

    /* Status Display */
    #status {
      margin-top: 15px;
      font-weight: bold;
      color: #0b5394;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="main-container">
    <h1>APIS TrustFinger</h1>

    <!-- Top Section (mirroring MainForm layout) -->
    <div class="top-section">
      <!-- User Info Box (left side) -->
      <div class="control-box">
        <h3>User Information</h3>

        <!-- Device Controls -->
        <div class="device-controls">
          <button id="openDeviceBtn" class="btn btn-primary">Open Device</button>
          <button id="captureBtn" class="btn" disabled>Capture</button>
          <button id="clearBtn" class="btn">Clear</button>
        </div>

        <!-- User Input Fields -->
        <div class="input-row">
          <label>User ID:</label>
          <input type="text" id="userIdInput" placeholder="Prisoner ID">
        </div>

        <div class="input-row">
          <label>User Name:</label>
          <input type="text" id="userNameInput" placeholder="User Name">
        </div>

        <!-- Action Buttons -->
        <div style="margin: 15px 0;">
          <button id="enrollBtn" class="btn btn-success" disabled>Enroll</button>
          <button id="identifyBtn" class="btn btn-info" disabled>Identify</button>
          <button id="clearDbBtn" class="btn" style="display: none;">Clear DB</button>
        </div>

        <!-- Device Info Display -->
        <div class="device-info">
          <div class="device-info-row">
            <label>Manufacturer:</label>
            <span id="manufacturerInfo">APIS Co.,LTD.</span>
          </div>
          <div class="device-info-row">
            <label>Device ID:</label>
            <span id="deviceIdInfo">N/A</span>
          </div>
          <div class="device-info-row">
            <label>Image Width:</label>
            <span id="imageWidthInfo">N/A</span>
          </div>
          <div class="device-info-row">
            <label>Image Height:</label>
            <span id="imageHeightInfo">N/A</span>
          </div>
          <div class="device-info-row">
            <label>FW Version:</label>
            <span id="firmwareInfo">N/A</span>
          </div>
          <div class="device-info-row">
            <label>Image DPI:</label>
            <span id="resolutionInfo">N/A</span>
          </div>
          <div class="device-info-row">
            <label>S/N:</label>
            <span id="serialNumberInfo">N/A</span>
          </div>
        </div>
      </div>

      <!-- Finger Select Box (right side) -->
      <div class="control-box">
        <h3>Finger Select</h3>

        <!-- Slaps Selection (only visible when Slaps tab is active) -->
        <div id="slapsSelection" style="margin-bottom: 20px;">
          <div style="display: flex; gap: 15px; flex-wrap: wrap; justify-content: center;">
            <div class="finger-item">
              <input type="checkbox" id="leftFourCheck" disabled>
              <label for="leftFourCheck">Left 4</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="rightFourCheck" disabled>
              <label for="rightFourCheck">Right 4</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="twoThumbsCheck" disabled>
              <label for="twoThumbsCheck">2 Thumbs</label>
            </div>
          </div>
        </div>

        <!-- Individual Finger Selection Grid (visible for rolled/flat tabs) -->
        <div id="individualSelection" style="margin-bottom: 20px; display: none;">
          <div class="finger-grid">
            <div class="finger-item">
              <input type="checkbox" id="L1Check" disabled>
              <label for="L1Check">L1</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="L2Check" disabled>
              <label for="L2Check">L2</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="L3Check" disabled>
              <label for="L3Check">L3</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="L4Check" disabled>
              <label for="L4Check">L4</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="L5Check" disabled>
              <label for="L5Check">L5</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="R1Check" disabled>
              <label for="R1Check">R1</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="R2Check" disabled>
              <label for="R2Check">R2</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="R3Check" disabled>
              <label for="R3Check">R3</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="R4Check" disabled>
              <label for="R4Check">R4</label>
            </div>
            <div class="finger-item">
              <input type="checkbox" id="R5Check" disabled>
              <label for="R5Check">R5</label>
            </div>
          </div>
        </div>

        <!-- Hand Images -->
        <div class="hand-images">
          <img src="left_hand.png" alt="Left Hand">
          <img src="right_hand.png" alt="Right Hand">
        </div>
      </div>
    </div>

    <!-- Message Display -->
    <div id="messageDisplay" style="display: none;"></div>

    <!-- Status Display -->
    <div id="status">Please Open Device</div>

    <!-- Tab Control (exactly like MainForm tabControlMain) -->
    <div class="tab-control">
      <div class="tab-headers">
        <div class="tab-header active" data-tab="0">Slaps Fingerprints</div>
        <div class="tab-header" data-tab="1">Rolled Fingerprints</div>
        <div class="tab-header" data-tab="2">Flat Fingerprints</div>
      </div>

      <!-- Slaps Tab Content -->
      <div class="tab-content" id="tabContent0">
        <div class="image-display">
          <div class="image-box">
            <h4>Left Four Fingers</h4>
            <div class="image-preview" id="leftFourPreview">
              <span style="color: #ccc;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>

          <div class="image-box">
            <h4>Two Thumbs</h4>
            <div class="image-preview" id="twoThumbsPreview">
              <span style="color: #ccc;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>

          <div class="image-box">
            <h4>Right Four Fingers</h4>
            <div class="image-preview" id="rightFourPreview">
              <span style="color: #ccc;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
        </div>
      </div>

      <!-- Rolled Tab Content -->
      <div class="tab-content" id="tabContent1" style="display: none;">
        <div class="finger-images">
          <div class="finger-image-box">
            <h5>Left Thumb</h5>
            <div class="finger-image-preview" id="rolledL1Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Left Index</h5>
            <div class="finger-image-preview" id="rolledL2Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Left Middle</h5>
            <div class="finger-image-preview" id="rolledL3Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Left Ring</h5>
            <div class="finger-image-preview" id="rolledL4Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Left Little</h5>
            <div class="finger-image-preview" id="rolledL5Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Right Thumb</h5>
            <div class="finger-image-preview" id="rolledR1Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Right Index</h5>
            <div class="finger-image-preview" id="rolledR2Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Right Middle</h5>
            <div class="finger-image-preview" id="rolledR3Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Right Ring</h5>
            <div class="finger-image-preview" id="rolledR4Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Right Little</h5>
            <div class="finger-image-preview" id="rolledR5Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
        </div>
      </div>

      <!-- Flat Tab Content -->
      <div class="tab-content" id="tabContent2" style="display: none;">
        <div class="finger-images">
          <div class="finger-image-box">
            <h5>Left Thumb</h5>
            <div class="finger-image-preview" id="flatL1Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Left Index</h5>
            <div class="finger-image-preview" id="flatL2Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Left Middle</h5>
            <div class="finger-image-preview" id="flatL3Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Left Ring</h5>
            <div class="finger-image-preview" id="flatL4Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Left Little</h5>
            <div class="finger-image-preview" id="flatL5Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Right Thumb</h5>
            <div class="finger-image-preview" id="flatR1Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Right Index</h5>
            <div class="finger-image-preview" id="flatR2Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Right Middle</h5>
            <div class="finger-image-preview" id="flatR3Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Right Ring</h5>
            <div class="finger-image-preview" id="flatR4Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
          <div class="finger-image-box">
            <h5>Right Little</h5>
            <div class="finger-image-preview" id="flatR5Preview">
              <span style="color: #ccc; font-size: 10px;">No Image</span>
            </div>
            <div class="nfiq-label">NFIQ=N/A</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Configuration - using the Python Flask API on port 5001
    const API_BASE_URL = 'http://localhost:5001/api';

    // Application state (mirroring MainForm state)
    let deviceOpen = false;
    let currentTab = 0; // 0=slaps, 1=rolled, 2=flat
    let capturedTemplates = {};
    let selectedFingers = [];

    // Finger mapping (matching MainForm and CapFingerPosition enum)
    const fingerMapping = {
      'L1': { id: 6, name: 'Left Thumb', capPos: 6 },      // LeftThumb
      'L2': { id: 7, name: 'Left Index', capPos: 7 },      // LeftIndex
      'L3': { id: 8, name: 'Left Middle', capPos: 8 },     // LeftMiddle
      'L4': { id: 9, name: 'Left Ring', capPos: 9 },       // LeftRing
      'L5': { id: 10, name: 'Left Little', capPos: 10 },   // LeftLittle
      'R1': { id: 1, name: 'Right Thumb', capPos: 1 },     // RightThumb
      'R2': { id: 2, name: 'Right Index', capPos: 2 },     // RightIndex
      'R3': { id: 3, name: 'Right Middle', capPos: 3 },    // RightMiddle
      'R4': { id: 4, name: 'Right Ring', capPos: 4 },      // RightRing
      'R5': { id: 5, name: 'Right Little', capPos: 5 }     // RightLittle
    };

    // Slaps mapping for group captures
    const slapsMapping = {
      'leftfour': { id: 12, name: 'Left Four Fingers', fingers: ['L2', 'L3', 'L4', 'L5'] },
      'rightfour': { id: 13, name: 'Right Four Fingers', fingers: ['R2', 'R3', 'R4', 'R5'] },
      'twothumbs': { id: 11, name: 'Two Thumbs', fingers: ['L1', 'R1'] }
    };

    // DOM elements
    const openDeviceBtn = document.getElementById('openDeviceBtn');
    const captureBtn = document.getElementById('captureBtn');
    const clearBtn = document.getElementById('clearBtn');
    const enrollBtn = document.getElementById('enrollBtn');
    const identifyBtn = document.getElementById('identifyBtn');
    const userIdInput = document.getElementById('userIdInput');
    const userNameInput = document.getElementById('userNameInput');
    const statusDiv = document.getElementById('status');
    const messageDisplay = document.getElementById('messageDisplay');

    // Tab elements
    const tabHeaders = document.querySelectorAll('.tab-header');
    const slapsSelection = document.getElementById('slapsSelection');
    const individualSelection = document.getElementById('individualSelection');

    // Checkbox elements
    const leftFourCheck = document.getElementById('leftFourCheck');
    const rightFourCheck = document.getElementById('rightFourCheck');
    const twoThumbsCheck = document.getElementById('twoThumbsCheck');

    // Individual finger checkboxes
    const fingerChecks = {};
    ['L1', 'L2', 'L3', 'L4', 'L5', 'R1', 'R2', 'R3', 'R4', 'R5'].forEach(finger => {
      fingerChecks[finger] = document.getElementById(finger + 'Check');
    });

    // Initialize the interface
    function initializeInterface() {
      updateDeviceControls();
      switchTab(0); // Start with Slaps tab

      // Add event listeners
      openDeviceBtn.addEventListener('click', toggleDevice);
      captureBtn.addEventListener('click', startCapture);
      clearBtn.addEventListener('click', clearImages);
      enrollBtn.addEventListener('click', enrollFingerprints);
      identifyBtn.addEventListener('click', identifyFingerprints);

      // Tab switching
      tabHeaders.forEach(header => {
        header.addEventListener('click', (e) => {
          const tabIndex = parseInt(e.target.dataset.tab);
          switchTab(tabIndex);
        });
      });

      // Finger selection handlers
      leftFourCheck.addEventListener('change', () => handleFingerSelection('leftfour', leftFourCheck.checked));
      rightFourCheck.addEventListener('change', () => handleFingerSelection('rightfour', rightFourCheck.checked));
      twoThumbsCheck.addEventListener('change', () => handleFingerSelection('twothumbs', twoThumbsCheck.checked));

      Object.keys(fingerChecks).forEach(finger => {
        fingerChecks[finger].addEventListener('change', () => {
          handleFingerSelection(finger, fingerChecks[finger].checked);
        });
      });

      // Check device status on page load
      checkDeviceStatus();

      // Set up periodic status checking (every 5 seconds)
      setInterval(checkDeviceStatus, 5000);
    }

    // Check device status on page load (like MainForm auto-detection)
    async function checkDeviceStatus() {
      try {
        displayStatus('Checking device status...', 'info');

        // First check if bridge application is running
        const healthCheck = await callAPI('health');
        if (!healthCheck.success || healthCheck.data.tcp_connection !== 'connected') {
          displayStatus('Bridge application not running', 'error');
          displayMessage('Bridge application is not running. Please start the MultiFingerDemo.exe application first.', 'error');
          deviceOpen = false;
          updateDeviceControls();
          return;
        }

        // Check current device status via TCP API
        const statusResult = await callAPI('status');
        if (statusResult.success && statusResult.data) {
          // The status API returns DeviceConnected field
          const isDeviceConnected = statusResult.data.DeviceConnected || statusResult.data.device_connected || false;

          if (isDeviceConnected) {
            // Device is already open
            deviceOpen = true;
            displayStatus('Device is open and ready', 'success');
            displayMessage('Device is connected and ready for fingerprint operations.', 'success');

            // Try to get additional device info if available
            try {
              // Note: The current API doesn't have device info endpoint,
              // but we can show basic status
              updateDeviceInfo({
                manufacturer: 'APIS Co.,LTD.',
                device_id: 'Connected',
                status: 'Ready'
              });
            } catch (infoError) {
              console.warn('Failed to get device info:', infoError);
            }
          } else {
            // Device is not open
            deviceOpen = false;
            displayStatus('Device not connected', 'info');
            displayMessage('Device is not connected. Please ensure the device is plugged in and the C# bridge application has opened it.', 'info');
            resetDeviceInfo();
          }
        } else {
          displayStatus('Unable to check device status', 'error');
          displayMessage('Failed to get device status from bridge application.', 'error');
          deviceOpen = false;
          resetDeviceInfo();
        }

        updateDeviceControls();

      } catch (error) {
        console.error('Device status check failed:', error);
        displayStatus('Device status check failed', 'error');
        displayMessage('Device status check failed: ' + error.message, 'error');
        deviceOpen = false;
        updateDeviceControls();
        resetDeviceInfo();
      }
    }

    // Update device control states (mirroring MainForm initUIControlEnable)
    function updateDeviceControls() {
      captureBtn.disabled = !deviceOpen;
      enrollBtn.disabled = !deviceOpen;
      identifyBtn.disabled = !deviceOpen;

      // Update finger selection controls
      leftFourCheck.disabled = !deviceOpen;
      rightFourCheck.disabled = !deviceOpen;
      twoThumbsCheck.disabled = !deviceOpen;

      Object.values(fingerChecks).forEach(checkbox => {
        checkbox.disabled = !deviceOpen;
      });

      // Update button text
      openDeviceBtn.textContent = deviceOpen ? 'Close Device' : 'Open Device';
      openDeviceBtn.className = deviceOpen ? 'btn btn-primary' : 'btn btn-primary';
    }

    // Display status message (mirroring MainForm maintips_label)
    function displayStatus(message, type = 'info') {
      statusDiv.textContent = message;
      statusDiv.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#0b5394';
    }

    // Display message with styling
    function displayMessage(message, type = 'info') {
      messageDisplay.innerHTML = message;
      messageDisplay.className = 'message message-' + type;
      messageDisplay.style.display = 'block';

      // Auto-hide after 5 seconds
      setTimeout(function() {
        messageDisplay.style.display = 'none';
      }, 5000);
    }

    // API call helper function
    async function callAPI(endpoint, method = 'GET', data = null) {
      try {
        const options = {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          }
        };

        if (data && method === 'POST') {
          options.body = JSON.stringify(data);
        }

        const response = await fetch(API_BASE_URL + '/' + endpoint, options);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'HTTP ' + response.status);
        }

        return result;
      } catch (error) {
        console.error('API call failed:', error);
        throw error;
      }
    }

    // Toggle device open/close (mirroring MainForm opencloseDevice_button_Click)
    async function toggleDevice() {
      try {
        // Check if bridge application is running first
        const healthCheck = await callAPI('health');
        if (!healthCheck.success || healthCheck.data.tcp_connection !== 'connected') {
          throw new Error('Bridge application is not running. Please start the MultiFingerDemo.exe application first.');
        }

        if (!deviceOpen) {
          displayStatus('Opening device...', 'info');
          displayMessage('Device opening is handled by the C# bridge application. Please use the MainForm application to open/close the device.', 'info');

          // Check current device status after a short delay
          setTimeout(function() {
            checkDeviceStatus();
          }, 1000);

        } else {
          displayStatus('Closing device...', 'info');
          displayMessage('Device closing is handled by the C# bridge application. Please use the MainForm application to open/close the device.', 'info');

          // Check current device status after a short delay
          setTimeout(function() {
            checkDeviceStatus();
          }, 1000);
        }

      } catch (error) {
        displayStatus('Device operation failed', 'error');
        displayMessage('Error: ' + error.message, 'error');

        // Reset device state on error
        deviceOpen = false;
        updateDeviceControls();
        resetDeviceInfo();
      }
    }

    // Tab switching (mirroring MainForm tabControlMain_SelectedIndexChanged)
    function switchTab(tabIndex) {
      currentTab = tabIndex;

      // Update tab headers
      tabHeaders.forEach((header, index) => {
        header.classList.toggle('active', index === tabIndex);
      });

      // Update tab content visibility
      for (let i = 0; i < 3; i++) {
        const content = document.getElementById('tabContent' + i);
        content.style.display = i === tabIndex ? 'block' : 'none';
      }

      // Update finger selection based on active tab
      if (tabIndex === 0) {
        // Slaps tab - show only Left 4, Right 4, 2 Thumbs
        slapsSelection.style.display = 'block';
        individualSelection.style.display = 'none';
        clearIndividualFingerSelections();
      } else {
        // Rolled or Flat tab - show individual fingers
        slapsSelection.style.display = 'none';
        individualSelection.style.display = 'block';
        clearSlapsSelections();
      }

      // Clear selected fingers array
      selectedFingers = [];
    }

    // Clear individual finger selections
    function clearIndividualFingerSelections() {
      Object.values(fingerChecks).forEach(checkbox => {
        checkbox.checked = false;
      });
    }

    // Clear slaps selections
    function clearSlapsSelections() {
      leftFourCheck.checked = false;
      rightFourCheck.checked = false;
      twoThumbsCheck.checked = false;
    }

    // Handle finger selection (mirroring MainForm checkbox logic)
    function handleFingerSelection(fingerId, isChecked) {
      if (isChecked) {
        if (!selectedFingers.includes(fingerId)) {
          selectedFingers.push(fingerId);
        }

        // For rolled tab, only allow one finger at a time (like MainForm)
        if (currentTab === 1) {
          // Uncheck all other fingers
          if (currentTab === 0) {
            // Slaps - allow multiple
          } else {
            // Individual fingers - for rolled, only one at a time
            selectedFingers = [fingerId];
            Object.keys(fingerChecks).forEach(finger => {
              if (finger !== fingerId) {
                fingerChecks[finger].checked = false;
              }
            });
          }
        }
      } else {
        selectedFingers = selectedFingers.filter(f => f !== fingerId);
      }
    }

    // Start capture (mirroring MainForm start_button_Click)
    async function startCapture() {
      if (!deviceOpen) {
        displayMessage('Please open device first', 'error');
        return;
      }

      if (selectedFingers.length === 0) {
        const message = currentTab === 0
          ? 'Please select at least one option: Left 4, Right 4, or 2 Thumbs'
          : 'Please select at least one finger';
        displayMessage(message, 'error');
        return;
      }

      try {
        displayStatus('Capturing fingerprints...', 'info');

        const captureResults = [];

        for (const fingerId of selectedFingers) {
          let fingerPos = null;
          let operationType = null;

          // Determine FingerPosition and OperationType based on current tab and finger selection
          if (currentTab === 0) {
            // Slaps tab
            if (slapsMapping[fingerId]) {
              fingerPos = slapsMapping[fingerId].id;
              operationType = 'slaps';
            }
          } else {
            // Individual finger operations (rolled or flat)
            if (fingerMapping[fingerId]) {
              fingerPos = fingerMapping[fingerId].capPos;
              operationType = currentTab === 1 ? 'rolled' : 'flat';
            }
          }

          if (fingerPos === null || operationType === null) {
            console.error('Unknown finger ID or invalid operation:', fingerId);
            continue;
          }

          const captureData = {
            finger_position: fingerPos,
            operation_type: operationType,
            timeout: 30,
            save_image: true
          };

          try {
            console.log('Calling capture API for ' + fingerId + ' with data:', captureData);
            displayStatus('Capturing ' + fingerId + '...', 'info');

            const result = await callAPI('fingerprint/capture', 'POST', captureData);
            console.log('Capture API result for ' + fingerId + ':', result);

            if (result.success && result.data) {
              capturedTemplates[fingerId] = {
                template: result.data.template_data || '',
                image: result.data.image_data || '',
                quality: result.data.quality || 'N/A',
                fingerPosition: fingerPos,
                operationType: operationType
              };

              // Update image display
              updateImageDisplay(fingerId, result.data.image_data);

              const fingerName = currentTab === 0 ? slapsMapping[fingerId]?.name : fingerMapping[fingerId]?.name;
              captureResults.push(fingerName + ' captured successfully');
            } else {
              const errorMsg = result.error || result.message || 'Unknown error';
              console.error('Capture failed for ' + fingerId + ':', result);
              captureResults.push(fingerId + ' failed: ' + errorMsg);
            }
          } catch (error) {
            console.error('Capture error for ' + fingerId + ':', error);
            captureResults.push(fingerId + ' failed: ' + error.message);
          }
        }

        const message = captureResults.join(', ');
        const messageType = captureResults.some(r => r.includes('failed')) ? 'error' : 'success';
        displayMessage(message, messageType);
        displayStatus('Capture completed', 'success');

      } catch (error) {
        displayStatus('Capture failed', 'error');
        displayMessage('Capture error: ' + error.message, 'error');
      }
    }

    // Update image display
    function updateImageDisplay(fingerId, imageData) {
      if (!imageData) return;

      let previewElement = null;

      if (currentTab === 0) {
        // Slaps tab
        if (fingerId === 'leftfour') previewElement = document.getElementById('leftFourPreview');
        else if (fingerId === 'rightfour') previewElement = document.getElementById('rightFourPreview');
        else if (fingerId === 'twothumbs') previewElement = document.getElementById('twoThumbsPreview');
      } else if (currentTab === 1) {
        // Rolled tab
        previewElement = document.getElementById('rolled' + fingerId + 'Preview');
      } else if (currentTab === 2) {
        // Flat tab
        previewElement = document.getElementById('flat' + fingerId + 'Preview');
      }

      if (previewElement) {
        previewElement.innerHTML = '<img src="data:image/bmp;base64,' + imageData + '" alt="' + fingerId + '">';
      }
    }

    // Enroll fingerprints (mirroring MainForm enroll_button_Click)
    async function enrollFingerprints() {
      if (!deviceOpen) {
        displayMessage('Please open device first', 'error');
        return;
      }

      const userId = userIdInput.value.trim();
      if (!userId) {
        displayMessage('Please input Prisoner ID', 'error');
        return;
      }

      const enrollResults = [];

      try {
        for (const [fingerId, templateData] of Object.entries(capturedTemplates)) {
          if (!templateData.template) continue;

          const enrollData = {
            user_id: userId,
            finger_position: templateData.fingerPosition,
            template_data: templateData.template,
            image_data: templateData.image || '',
            image_quality: parseInt(templateData.quality) || 0
          };

          try {
            const result = await callAPI('fingerprint/enroll', 'POST', enrollData);

            if (result.success) {
              const fingerName = fingerMapping[fingerId]?.name || fingerId;
              enrollResults.push(fingerName + ' enrolled');
            } else {
              const errorMsg = result.error || result.message || 'Unknown error';
              enrollResults.push(fingerId + ' enroll failed: ' + errorMsg);
            }
          } catch (error) {
            enrollResults.push(fingerId + ' enroll failed: ' + error.message);
          }
        }

        if (enrollResults.length > 0) {
          const message = enrollResults.join(', ');
          const messageType = enrollResults.some(r => r.includes('failed')) ? 'error' : 'success';
          displayMessage(message, messageType);
          displayStatus('Enroll completed', 'success');

          if (messageType === 'success') {
            clearImages(); // Clear after successful enrollment
          }
        } else {
          displayMessage('No fingerprints to enroll', 'error');
        }

      } catch (error) {
        displayStatus('Enroll failed', 'error');
        displayMessage('Enroll error: ' + error.message, 'error');
      }
    }

    // Identify fingerprints (mirroring MainForm Identify_button_Click)
    async function identifyFingerprints() {
      if (!deviceOpen) {
        displayMessage('Please open device first', 'error');
        return;
      }

      const identifyResults = [];

      try {
        for (const [fingerId, templateData] of Object.entries(capturedTemplates)) {
          if (!templateData.template) continue;

          const identifyData = {
            template_data: templateData.template,
            threshold: 70
          };

          try {
            const result = await callAPI('fingerprint/identify', 'POST', identifyData);

            if (result.success && result.data) {
              const userId = result.data.user_id || 'Unknown';
              const score = result.data.score || 'N/A';
              const fingerName = fingerMapping[fingerId]?.name || fingerId;
              identifyResults.push(fingerName + ': ' + userId + ' (Score: ' + score + ')');
            } else {
              const fingerName = fingerMapping[fingerId]?.name || fingerId;
              identifyResults.push(fingerName + ': No match');
            }
          } catch (error) {
            const fingerName = fingerMapping[fingerId]?.name || fingerId;
            identifyResults.push(fingerName + ': Error - ' + error.message);
          }
        }

        if (identifyResults.length > 0) {
          const message = identifyResults.join(', ');
          displayMessage(message, 'info');
          displayStatus('Identify completed', 'success');
        } else {
          displayMessage('No fingerprints to identify', 'error');
        }

      } catch (error) {
        displayStatus('Identify failed', 'error');
        displayMessage('Identify error: ' + error.message, 'error');
      }
    }

    // Clear images (mirroring MainForm clear functionality)
    function clearImages() {
      capturedTemplates = {};

      // Clear all image previews
      const imageElements = [
        'leftFourPreview', 'rightFourPreview', 'twoThumbsPreview'
      ];

      imageElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
          element.innerHTML = '<span style="color: #ccc;">No Image</span>';
        }
      });

      // Clear individual finger images
      ['L1', 'L2', 'L3', 'L4', 'L5', 'R1', 'R2', 'R3', 'R4', 'R5'].forEach(finger => {
        const rolledElement = document.getElementById('rolled' + finger + 'Preview');
        const flatElement = document.getElementById('flat' + finger + 'Preview');

        if (rolledElement) {
          rolledElement.innerHTML = '<span style="color: #ccc; font-size: 10px;">No Image</span>';
        }
        if (flatElement) {
          flatElement.innerHTML = '<span style="color: #ccc; font-size: 10px;">No Image</span>';
        }
      });

      displayStatus('Images cleared', 'info');
    }

    // Clear all data (when device is closed)
    function clearAllData() {
      clearImages();
      selectedFingers = [];
      clearSlapsSelections();
      clearIndividualFingerSelections();
      userIdInput.value = '';
      userNameInput.value = '';
    }

    // Update device info display
    function updateDeviceInfo(deviceData) {
      document.getElementById('manufacturerInfo').textContent = deviceData.manufacturer || deviceData.Manufacturer || 'APIS Co.,LTD.';
      document.getElementById('deviceIdInfo').textContent = deviceData.device_id || deviceData.DeviceId || deviceData.status || 'Connected';
      document.getElementById('imageWidthInfo').textContent = deviceData.image_width || deviceData.ImageWidth || deviceData.ImagePixelWidth || 'N/A';
      document.getElementById('imageHeightInfo').textContent = deviceData.image_height || deviceData.ImageHeight || deviceData.ImagePixelHeight || 'N/A';
      document.getElementById('firmwareInfo').textContent = deviceData.firmware_version || deviceData.FirmwareVersion || 'N/A';
      document.getElementById('resolutionInfo').textContent = deviceData.resolution || deviceData.Resolution || 'N/A';
      document.getElementById('serialNumberInfo').textContent = deviceData.serial_number || deviceData.SerialNumber || 'N/A';
    }

    // Reset device info display
    function resetDeviceInfo() {
      document.getElementById('manufacturerInfo').textContent = 'APIS Co.,LTD.';
      document.getElementById('deviceIdInfo').textContent = 'N/A';
      document.getElementById('imageWidthInfo').textContent = 'N/A';
      document.getElementById('imageHeightInfo').textContent = 'N/A';
      document.getElementById('firmwareInfo').textContent = 'N/A';
      document.getElementById('resolutionInfo').textContent = 'N/A';
      document.getElementById('serialNumberInfo').textContent = 'N/A';
    }

    // Initialize the interface when page loads
    document.addEventListener('DOMContentLoaded', initializeInterface);
  </script>
</body>
</html>
