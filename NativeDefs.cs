using System.Runtime.InteropServices;
using System;

namespace MultipleFinger.Native
{
    public enum FingerPosition
    {
        Unknown = 0,
        RightThumb = 1,
        RightIndexFinger = 2,
        RightMiddleFinger = 3,
        RightRingFinger = 4,
        RightLittleFinger = 5,
        LeftThumb = 6,
        LeftIndexFinger = 7,
        LeftMiddleFinger = 8,
        LeftRingFinger = 9,
        LeftLittle<PERSON>inger = 10,
    }

    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
    public struct DeviceDescription
    {
        public int DeviceId;
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
        public string ProductName;
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
        public string Manufacturer;
        public int ImagePixelWidth;
        public int ImagePixelHeight;
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
        public string FirmwareVersion;
        public int Resolution;
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
        public string SerialNumber;
    }

    public enum EnumOperationType
    {
        FLAT_RIGHT_THUMB_FINGER = 1,
        FLAT_RIGHT_INDEX_FINGER = 2,
        FLAT_RIGHT_MIDDLE_FINGER = 3,
        FLAT_RIGHT_RING_FINGER = 4,
        FLAT_RIGHT_LITTLE_FINGER = 5,
        FLAT_LEFT_THUMB_FINGER = 6,
        FLAT_LEFT_INDEX_FINGER = 7,
        FLAT_LEFT_MIDDLE_FINGER = 8,
        FLAT_LEFT_RING_FINGER = 9,
        FLAT_LEFT_LITTLE_FINGER = 10,
        ROLL_RIGHT_THUMB_FINGER = 11,
        ROLL_RIGHT_INDEX_FINGER = 12,
        ROLL_RIGHT_MIDDLE_FINGER = 13,
        ROLL_RIGHT_RING_FINGER = 14,
        ROLL_RIGHT_LITTLE_FINGER = 15,
        ROLL_LEFT_THUMB_FINGER = 16,
        ROLL_LEFT_INDEX_FINGER = 17,
        ROLL_LEFT_MIDDLE_FINGER = 18,
        ROLL_LEFT_RING_FINGER = 19,
        ROLL_LEFT_LITTLE_FINGER = 20,
        SLAP_4_LEFT_FINGERS = 21,
        SLAP_4_RIGHT_FINGERS = 22,
        SLAP_2_THUMBS = 23,
        SLAP_ANY_TWO_FINGERS = 24
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct NativeSegmentImagDesc
    {
        public int finger_position;
        public int image_quality;
        public int nfiq_quality;
        public int impression_type;
        public int image_width;
        public int image_height;
        public IntPtr image_data;
        public IntPtr feature_data;
    }

    public enum AcquisitionEventCode
    {
        Start = 0,
        Progress = 1,
        End = 2,
        Error = 3
    }

    public class TrustFingerDevice { }
}