# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
The ``wx.html2`` module includes a widget class and supporting classes that
wraps native browser components on the system, therefore providing a fully
featured HTML rendering component including the latest HTML, Javascript and
CSS standards. Since platform-specific back-ends are used (Microsoft Trident,
WebKit webView, etc.) there will be some difference in ability and behaviors,
but these classes will minimize those differences as much as possible.
"""
#-- begin-_html2 --#

import wx
#-- end-_html2 --#
#-- begin-webview --#

# On Windows we need to ensure that the wx package folder is on on the
# PATH, so the MS Edge Loader DLLs can be found when they are dynamically
# loaded.
import os
if os.name == 'nt':
    _path = os.environ.get('PATH')
    _pkg_path = os.path.abspath(os.path.dirname(wx.__file__))
    if _pkg_path.lower() not in _path.lower():
        os.environ['PATH'] = _path + os.pathsep + _pkg_path
WebViewDefaultURLStr: str
WebViewNameStr: str
WebViewBackendWebKit: str
WebViewBackendEdge: str
WebViewBackendIE: str
WebViewBackendDefault: str

class _WebViewZoom(IntEnum):
    WEBVIEW_ZOOM_TINY = auto()
    WEBVIEW_ZOOM_SMALL = auto()
    WEBVIEW_ZOOM_MEDIUM = auto()
    WEBVIEW_ZOOM_LARGE = auto()
    WEBVIEW_ZOOM_LARGEST = auto()
WebViewZoom: TypeAlias = Union[_WebViewZoom, int]
WEBVIEW_ZOOM_TINY = _WebViewZoom.WEBVIEW_ZOOM_TINY
WEBVIEW_ZOOM_SMALL = _WebViewZoom.WEBVIEW_ZOOM_SMALL
WEBVIEW_ZOOM_MEDIUM = _WebViewZoom.WEBVIEW_ZOOM_MEDIUM
WEBVIEW_ZOOM_LARGE = _WebViewZoom.WEBVIEW_ZOOM_LARGE
WEBVIEW_ZOOM_LARGEST = _WebViewZoom.WEBVIEW_ZOOM_LARGEST

class _WebViewZoomType(IntEnum):
    WEBVIEW_ZOOM_TYPE_LAYOUT = auto()
    WEBVIEW_ZOOM_TYPE_TEXT = auto()
WebViewZoomType: TypeAlias = Union[_WebViewZoomType, int]
WEBVIEW_ZOOM_TYPE_LAYOUT = _WebViewZoomType.WEBVIEW_ZOOM_TYPE_LAYOUT
WEBVIEW_ZOOM_TYPE_TEXT = _WebViewZoomType.WEBVIEW_ZOOM_TYPE_TEXT

class _WebViewNavigationError(IntEnum):
    WEBVIEW_NAV_ERR_CONNECTION = auto()
    WEBVIEW_NAV_ERR_CERTIFICATE = auto()
    WEBVIEW_NAV_ERR_AUTH = auto()
    WEBVIEW_NAV_ERR_SECURITY = auto()
    WEBVIEW_NAV_ERR_NOT_FOUND = auto()
    WEBVIEW_NAV_ERR_REQUEST = auto()
    WEBVIEW_NAV_ERR_USER_CANCELLED = auto()
    WEBVIEW_NAV_ERR_OTHER = auto()
WebViewNavigationError: TypeAlias = Union[_WebViewNavigationError, int]
WEBVIEW_NAV_ERR_CONNECTION = _WebViewNavigationError.WEBVIEW_NAV_ERR_CONNECTION
WEBVIEW_NAV_ERR_CERTIFICATE = _WebViewNavigationError.WEBVIEW_NAV_ERR_CERTIFICATE
WEBVIEW_NAV_ERR_AUTH = _WebViewNavigationError.WEBVIEW_NAV_ERR_AUTH
WEBVIEW_NAV_ERR_SECURITY = _WebViewNavigationError.WEBVIEW_NAV_ERR_SECURITY
WEBVIEW_NAV_ERR_NOT_FOUND = _WebViewNavigationError.WEBVIEW_NAV_ERR_NOT_FOUND
WEBVIEW_NAV_ERR_REQUEST = _WebViewNavigationError.WEBVIEW_NAV_ERR_REQUEST
WEBVIEW_NAV_ERR_USER_CANCELLED = _WebViewNavigationError.WEBVIEW_NAV_ERR_USER_CANCELLED
WEBVIEW_NAV_ERR_OTHER = _WebViewNavigationError.WEBVIEW_NAV_ERR_OTHER

class _WebViewReloadFlags(IntFlag):
    WEBVIEW_RELOAD_DEFAULT = auto()
    WEBVIEW_RELOAD_NO_CACHE = auto()
WebViewReloadFlags: TypeAlias = Union[_WebViewReloadFlags, int]
WEBVIEW_RELOAD_DEFAULT = _WebViewReloadFlags.WEBVIEW_RELOAD_DEFAULT
WEBVIEW_RELOAD_NO_CACHE = _WebViewReloadFlags.WEBVIEW_RELOAD_NO_CACHE

class _WebViewFindFlags(IntFlag):
    WEBVIEW_FIND_WRAP = auto()
    WEBVIEW_FIND_ENTIRE_WORD = auto()
    WEBVIEW_FIND_MATCH_CASE = auto()
    WEBVIEW_FIND_HIGHLIGHT_RESULT = auto()
    WEBVIEW_FIND_BACKWARDS = auto()
    WEBVIEW_FIND_DEFAULT = auto()
WebViewFindFlags: TypeAlias = Union[_WebViewFindFlags, int]
WEBVIEW_FIND_WRAP = _WebViewFindFlags.WEBVIEW_FIND_WRAP
WEBVIEW_FIND_ENTIRE_WORD = _WebViewFindFlags.WEBVIEW_FIND_ENTIRE_WORD
WEBVIEW_FIND_MATCH_CASE = _WebViewFindFlags.WEBVIEW_FIND_MATCH_CASE
WEBVIEW_FIND_HIGHLIGHT_RESULT = _WebViewFindFlags.WEBVIEW_FIND_HIGHLIGHT_RESULT
WEBVIEW_FIND_BACKWARDS = _WebViewFindFlags.WEBVIEW_FIND_BACKWARDS
WEBVIEW_FIND_DEFAULT = _WebViewFindFlags.WEBVIEW_FIND_DEFAULT

class _WebViewNavigationActionFlags(IntFlag):
    WEBVIEW_NAV_ACTION_NONE = auto()
    WEBVIEW_NAV_ACTION_USER = auto()
    WEBVIEW_NAV_ACTION_OTHER = auto()
WebViewNavigationActionFlags: TypeAlias = Union[_WebViewNavigationActionFlags, int]
WEBVIEW_NAV_ACTION_NONE = _WebViewNavigationActionFlags.WEBVIEW_NAV_ACTION_NONE
WEBVIEW_NAV_ACTION_USER = _WebViewNavigationActionFlags.WEBVIEW_NAV_ACTION_USER
WEBVIEW_NAV_ACTION_OTHER = _WebViewNavigationActionFlags.WEBVIEW_NAV_ACTION_OTHER

class _WebViewUserScriptInjectionTime(IntEnum):
    WEBVIEW_INJECT_AT_DOCUMENT_START = auto()
    WEBVIEW_INJECT_AT_DOCUMENT_END = auto()
WebViewUserScriptInjectionTime: TypeAlias = Union[_WebViewUserScriptInjectionTime, int]
WEBVIEW_INJECT_AT_DOCUMENT_START = _WebViewUserScriptInjectionTime.WEBVIEW_INJECT_AT_DOCUMENT_START
WEBVIEW_INJECT_AT_DOCUMENT_END = _WebViewUserScriptInjectionTime.WEBVIEW_INJECT_AT_DOCUMENT_END

class _WebViewIE_EmulationLevel(IntEnum):
    WEBVIEWIE_EMU_DEFAULT = auto()
    WEBVIEWIE_EMU_IE7 = auto()
    WEBVIEWIE_EMU_IE8 = auto()
    WEBVIEWIE_EMU_IE8_FORCE = auto()
    WEBVIEWIE_EMU_IE9 = auto()
    WEBVIEWIE_EMU_IE9_FORCE = auto()
    WEBVIEWIE_EMU_IE10 = auto()
    WEBVIEWIE_EMU_IE10_FORCE = auto()
    WEBVIEWIE_EMU_IE11 = auto()
    WEBVIEWIE_EMU_IE11_FORCE = auto()
WebViewIE_EmulationLevel: TypeAlias = Union[_WebViewIE_EmulationLevel, int]
WEBVIEWIE_EMU_DEFAULT = _WebViewIE_EmulationLevel.WEBVIEWIE_EMU_DEFAULT
WEBVIEWIE_EMU_IE7 = _WebViewIE_EmulationLevel.WEBVIEWIE_EMU_IE7
WEBVIEWIE_EMU_IE8 = _WebViewIE_EmulationLevel.WEBVIEWIE_EMU_IE8
WEBVIEWIE_EMU_IE8_FORCE = _WebViewIE_EmulationLevel.WEBVIEWIE_EMU_IE8_FORCE
WEBVIEWIE_EMU_IE9 = _WebViewIE_EmulationLevel.WEBVIEWIE_EMU_IE9
WEBVIEWIE_EMU_IE9_FORCE = _WebViewIE_EmulationLevel.WEBVIEWIE_EMU_IE9_FORCE
WEBVIEWIE_EMU_IE10 = _WebViewIE_EmulationLevel.WEBVIEWIE_EMU_IE10
WEBVIEWIE_EMU_IE10_FORCE = _WebViewIE_EmulationLevel.WEBVIEWIE_EMU_IE10_FORCE
WEBVIEWIE_EMU_IE11 = _WebViewIE_EmulationLevel.WEBVIEWIE_EMU_IE11
WEBVIEWIE_EMU_IE11_FORCE = _WebViewIE_EmulationLevel.WEBVIEWIE_EMU_IE11_FORCE
wxEVT_WEBVIEW_NAVIGATING: int
wxEVT_WEBVIEW_NAVIGATED: int
wxEVT_WEBVIEW_LOADED: int
wxEVT_WEBVIEW_ERROR: int
wxEVT_WEBVIEW_NEWWINDOW: int
wxEVT_WEBVIEW_TITLE_CHANGED: int
wxEVT_WEBVIEW_FULLSCREEN_CHANGED: int
wxEVT_WEBVIEW_SCRIPT_MESSAGE_RECEIVED: int
wxEVT_WEBVIEW_SCRIPT_RESULT: int

class WebViewHistoryItem:
    """
    WebViewHistoryItem(url, title) -> None
    
    A simple class that contains the URL and title of an element of the
    history of a wxWebView.
    """

    def __init__(self, url: str, title: str) -> None:
        """
        WebViewHistoryItem(url, title) -> None
        
        A simple class that contains the URL and title of an element of the
        history of a wxWebView.
        """

    def GetUrl(self) -> str:
        """
        GetUrl() -> str
        """

    def GetTitle(self) -> str:
        """
        GetTitle() -> str
        """
    @property
    def Title(self) -> str: ...
    @property
    def Url(self) -> str: ...
# end of class WebViewHistoryItem


class WebViewHandler:
    """
    WebViewHandler(scheme) -> None
    
    The base class for handling custom schemes in wxWebView, for example
    to allow virtual file system support.
    """

    def __init__(self, scheme: str) -> None:
        """
        WebViewHandler(scheme) -> None
        
        The base class for handling custom schemes in wxWebView, for example
        to allow virtual file system support.
        """

    def GetFile(self, uri: str) -> wx.FSFile:
        """
        GetFile(uri) -> wx.FSFile
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        """

    def SetSecurityURL(self, url: str) -> None:
        """
        SetSecurityURL(url) -> None
        
        Sets a custom security URL.
        """

    def GetSecurityURL(self) -> str:
        """
        GetSecurityURL() -> str
        """
    @property
    def Name(self) -> str: ...
    @property
    def SecurityURL(self) -> str: ...
    @SecurityURL.setter
    def SecurityURL(self, value: str, /) -> None: ...
# end of class WebViewHandler


class WebViewArchiveHandler(WebViewHandler):
    """
    WebViewArchiveHandler(scheme) -> None
    
    A custom handler for the file scheme which also supports loading from
    archives.
    """

    def __init__(self, scheme: str) -> None:
        """
        WebViewArchiveHandler(scheme) -> None
        
        A custom handler for the file scheme which also supports loading from
        archives.
        """

    def GetFile(self, uri: str) -> wx.FSFile:
        """
        GetFile(uri) -> wx.FSFile
        """
# end of class WebViewArchiveHandler


class WebViewFSHandler(WebViewHandler):
    """
    WebViewFSHandler(scheme) -> None
    
    A wxWebView file system handler to support standard wxFileSystem
    protocols of the form  example:page.htm  The handler allows wxWebView
    to use wxFileSystem in a similar fashion to its use with wxHtml.
    """

    def __init__(self, scheme: str) -> None:
        """
        WebViewFSHandler(scheme) -> None
        
        A wxWebView file system handler to support standard wxFileSystem
        protocols of the form  example:page.htm  The handler allows wxWebView
        to use wxFileSystem in a similar fashion to its use with wxHtml.
        """

    def GetFile(self, uri: str) -> wx.FSFile:
        """
        GetFile(uri) -> wx.FSFile
        """
# end of class WebViewFSHandler


class WebView(wx.Control):
    """
    This control may be used to render web (HTML / CSS / javascript)
    documents.
    """

    def RunScript(self, javascript: str) -> Tuple[bool, str]:
        """
        RunScript(javascript) -> Tuple[bool, str]
        
        Runs the given JavaScript code.
        """

    def RunScriptAsync(self, javascript: str, clientData: Optional[Any]=None) -> None:
        """
        RunScriptAsync(javascript, clientData=None) -> None
        
        Runs the given JavaScript code asynchronously and returns the result
        via a wxEVT_WEBVIEW_SCRIPT_RESULT.
        """

    def AddScriptMessageHandler(self, name: str) -> bool:
        """
        AddScriptMessageHandler(name) -> bool
        
        Add a script message handler with the given name.
        """

    def RemoveScriptMessageHandler(self, name: str) -> bool:
        """
        RemoveScriptMessageHandler(name) -> bool
        
        Remove a script message handler with the given name that was
        previously added via AddScriptMessageHandler().
        """

    def AddUserScript(self, javascript: str, injectionTime: WebViewUserScriptInjectionTime=WEBVIEW_INJECT_AT_DOCUMENT_START) -> bool:
        """
        AddUserScript(javascript, injectionTime=WEBVIEW_INJECT_AT_DOCUMENT_START) -> bool
        
        Injects the specified script into the webpage's content.
        """

    def RemoveAllUserScripts(self) -> None:
        """
        RemoveAllUserScripts() -> None
        
        Removes all user scripts from the web view.
        """

    def CanCopy(self) -> bool:
        """
        CanCopy() -> bool
        
        Returns true if the current selection can be copied.
        """

    def CanCut(self) -> bool:
        """
        CanCut() -> bool
        
        Returns true if the current selection can be cut.
        """

    def CanPaste(self) -> bool:
        """
        CanPaste() -> bool
        
        Returns true if data can be pasted.
        """

    def Copy(self) -> None:
        """
        Copy() -> None
        
        Copies the current selection.
        """

    def Cut(self) -> None:
        """
        Cut() -> None
        
        Cuts the current selection.
        """

    def Paste(self) -> None:
        """
        Paste() -> None
        
        Pastes the current data.
        """

    def EnableContextMenu(self, enable: bool=True) -> None:
        """
        EnableContextMenu(enable=True) -> None
        
        Enable or disable the right click context menu.
        """

    def IsContextMenuEnabled(self) -> bool:
        """
        IsContextMenuEnabled() -> bool
        
        Returns true if a context menu will be shown on right click.
        """

    def EnableAccessToDevTools(self, enable: bool=True) -> None:
        """
        EnableAccessToDevTools(enable=True) -> None
        
        Enable or disable access to dev tools for the user.
        """

    def IsAccessToDevToolsEnabled(self) -> bool:
        """
        IsAccessToDevToolsEnabled() -> bool
        
        Returns true if dev tools are available to the user.
        """

    def SetUserAgent(self, userAgent: str) -> bool:
        """
        SetUserAgent(userAgent) -> bool
        
        Specify a custom user agent string for the web view.
        """

    def GetUserAgent(self) -> str:
        """
        GetUserAgent() -> str
        
        Returns the current user agent string for the web view.
        """

    def CanGoBack(self) -> bool:
        """
        CanGoBack() -> bool
        
        Returns true if it is possible to navigate backward in the history of
        visited pages.
        """

    def CanGoForward(self) -> bool:
        """
        CanGoForward() -> bool
        
        Returns true if it is possible to navigate forward in the history of
        visited pages.
        """

    def ClearHistory(self) -> None:
        """
        ClearHistory() -> None
        
        Clear the history, this will also remove the visible page.
        """

    def EnableHistory(self, enable: bool=True) -> None:
        """
        EnableHistory(enable=True) -> None
        
        Enable or disable the history.
        """

    def GetBackwardHistory(self) -> Any:
        """
        GetBackwardHistory() -> Any
        
        Returns a list of items in the back history.
        """

    def GetForwardHistory(self) -> Any:
        """
        GetForwardHistory() -> Any
        
        Returns a list of items in the forward history.
        """

    def GoBack(self) -> None:
        """
        GoBack() -> None
        
        Navigate back in the history of visited pages.
        """

    def GoForward(self) -> None:
        """
        GoForward() -> None
        
        Navigate forward in the history of visited pages.
        """

    def ClearSelection(self) -> None:
        """
        ClearSelection() -> None
        
        Clears the current selection.
        """

    def DeleteSelection(self) -> None:
        """
        DeleteSelection() -> None
        
        Deletes the current selection.
        """

    def GetSelectedSource(self) -> str:
        """
        GetSelectedSource() -> str
        
        Returns the currently selected source, if any.
        """

    def GetSelectedText(self) -> str:
        """
        GetSelectedText() -> str
        
        Returns the currently selected text, if any.
        """

    def HasSelection(self) -> bool:
        """
        HasSelection() -> bool
        
        Returns true if there is a current selection.
        """

    def SelectAll(self) -> None:
        """
        SelectAll() -> None
        
        Selects the entire page.
        """

    def CanRedo(self) -> bool:
        """
        CanRedo() -> bool
        
        Returns true if there is an action to redo.
        """

    def CanUndo(self) -> bool:
        """
        CanUndo() -> bool
        
        Returns true if there is an action to undo.
        """

    def Redo(self) -> None:
        """
        Redo() -> None
        
        Redos the last action.
        """

    def Undo(self) -> None:
        """
        Undo() -> None
        
        Undos the last action.
        """

    def Find(self, text: str, flags: WebViewFindFlags=WEBVIEW_FIND_DEFAULT) -> int:
        """
        Find(text, flags=WEBVIEW_FIND_DEFAULT) -> int
        
        Finds a phrase on the current page and if found, the control will scroll the phrase into view and select it.
        """

    def CanSetZoomType(self, type: WebViewZoomType) -> bool:
        """
        CanSetZoomType(type) -> bool
        
        Retrieve whether the current HTML engine supports a zoom type.
        """

    def GetZoom(self) -> WebViewZoom:
        """
        GetZoom() -> WebViewZoom
        
        Get the zoom level of the page.
        """

    def GetZoomFactor(self) -> float:
        """
        GetZoomFactor() -> float
        
        Get the zoom factor of the page.
        """

    def GetZoomType(self) -> WebViewZoomType:
        """
        GetZoomType() -> WebViewZoomType
        
        Get how the zoom factor is currently interpreted.
        """

    def SetZoom(self, zoom: WebViewZoom) -> None:
        """
        SetZoom(zoom) -> None
        
        Set the zoom level of the page.
        """

    def SetZoomFactor(self, zoom: float) -> None:
        """
        SetZoomFactor(zoom) -> None
        
        Set the zoom factor of the page.
        """

    def SetZoomType(self, zoomType: WebViewZoomType) -> None:
        """
        SetZoomType(zoomType) -> None
        
        Set how to interpret the zoom factor.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, url: str=WebViewDefaultURLStr, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, name: str=WebViewNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, url=WebViewDefaultURLStr, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, name=WebViewNameStr) -> bool
        
        Creation function for two-step creation.
        """

    def GetCurrentTitle(self) -> str:
        """
        GetCurrentTitle() -> str
        
        Get the title of the current web page, or its URL/path if title is not
        available.
        """

    def GetCurrentURL(self) -> str:
        """
        GetCurrentURL() -> str
        
        Get the URL of the currently displayed document.
        """

    def GetNativeBackend(self) -> Any:
        """
        GetNativeBackend() -> Any
        
        Return the pointer to the native backend used by this control.
        """

    def GetPageSource(self) -> str:
        """
        GetPageSource() -> str
        
        Get the HTML source code of the currently displayed document.
        """

    def GetPageText(self) -> str:
        """
        GetPageText() -> str
        
        Get the text of the current page.
        """

    def IsBusy(self) -> bool:
        """
        IsBusy() -> bool
        
        Returns whether the web control is currently busy (e.g. loading a
        page).
        """

    def IsEditable(self) -> bool:
        """
        IsEditable() -> bool
        
        Returns whether the web control is currently editable.
        """

    def LoadURL(self, url: str) -> None:
        """
        LoadURL(url) -> None
        
        Load a web page from a URL.
        """

    def Print(self) -> None:
        """
        Print() -> None
        
        Opens a print dialog so that the user may print the currently
        displayed page.
        """

    def RegisterHandler(self, handler: WebViewHandler) -> None:
        """
        RegisterHandler(handler) -> None
        
        Registers a custom scheme handler.
        """

    def Reload(self, flags: WebViewReloadFlags=WEBVIEW_RELOAD_DEFAULT) -> None:
        """
        Reload(flags=WEBVIEW_RELOAD_DEFAULT) -> None
        
        Reload the currently displayed URL.
        """

    def SetEditable(self, enable: bool=True) -> None:
        """
        SetEditable(enable=True) -> None
        
        Set the editable property of the web control.
        """

    @overload
    def SetPage(self, html: wx.InputStream, baseUrl: str) -> None:
        ...

    @overload
    def SetPage(self, html: str, baseUrl: str) -> None:
        """
        SetPage(html, baseUrl) -> None
        SetPage(html, baseUrl) -> None
        
        Set the displayed page source to the contents of the given string.
        """

    def Stop(self) -> None:
        """
        Stop() -> None
        
        Stop the current page loading process, if any.
        """

    @overload
    @staticmethod
    def New(parent: wx.Window, id: int=wx.ID_ANY, url: str=WebViewDefaultURLStr, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, backend: str=WebViewBackendDefault, style: int=0, name: str=WebViewNameStr) -> WebView:
        ...

    @overload
    @staticmethod
    def New(backend: str=WebViewBackendDefault) -> WebView:
        """
        New(backend=WebViewBackendDefault) -> WebView
        New(parent, id=wx.ID_ANY, url=WebViewDefaultURLStr, pos=wx.DefaultPosition, size=wx.DefaultSize, backend=WebViewBackendDefault, style=0, name=WebViewNameStr) -> WebView
        
        Factory function to create a new wxWebView with two-step creation,
        wxWebView::Create should be called on the returned object.
        """

    @staticmethod
    def RegisterFactory(backend: str, factory: WebViewFactory) -> None:
        """
        RegisterFactory(backend, factory) -> None
        
        Allows the registering of new backend for wxWebView.
        """

    @staticmethod
    def IsBackendAvailable(backend: str) -> bool:
        """
        IsBackendAvailable(backend) -> bool
        
        Allows to check if a specific backend is currently available.
        """

    @staticmethod
    def GetBackendVersionInfo(backend: str=WebViewBackendDefault) -> wx.VersionInfo:
        """
        GetBackendVersionInfo(backend=WebViewBackendDefault) -> wx.VersionInfo
        
        Retrieve the version information about the backend implementation.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    @staticmethod
    def MSWSetEmulationLevel(level: WebViewIE_EmulationLevel=WEBVIEWIE_EMU_IE11) -> bool:
        """
        MSWSetEmulationLevel(level=WEBVIEWIE_EMU_IE11) -> bool
        
        Sets emulation level.
        """

    @staticmethod
    def MSWSetModernEmulationLevel(modernLevel: bool=True) -> bool:
        """
        MSWSetModernEmulationLevel(modernLevel=True) -> bool
        """
    @property
    def BackwardHistory(self) -> Any: ...
    @property
    def CurrentTitle(self) -> str: ...
    @property
    def CurrentURL(self) -> str: ...
    @property
    def ForwardHistory(self) -> Any: ...
    @property
    def NativeBackend(self) -> Any: ...
    @property
    def PageSource(self) -> str: ...
    @property
    def PageText(self) -> str: ...
    @property
    def SelectedSource(self) -> str: ...
    @property
    def SelectedText(self) -> str: ...
    @property
    def UserAgent(self) -> str: ...
    @UserAgent.setter
    def UserAgent(self, value: str, /) -> None: ...
    @property
    def Zoom(self) -> WebViewZoom: ...
    @Zoom.setter
    def Zoom(self, value: WebViewZoom, /) -> None: ...
    @property
    def ZoomFactor(self) -> float: ...
    @ZoomFactor.setter
    def ZoomFactor(self, value: float, /) -> None: ...
    @property
    def ZoomType(self) -> WebViewZoomType: ...
    @ZoomType.setter
    def ZoomType(self, value: WebViewZoomType, /) -> None: ...
# end of class WebView


class WebViewEvent(wx.NotifyEvent):
    """
    WebViewEvent() -> None
    WebViewEvent(type, id, href, target, flags=WEBVIEW_NAV_ACTION_NONE, messageHandler="") -> None
    
    A navigation event holds information about events associated with
    wxWebView objects.
    """

    @overload
    def __init__(self, type: EventType, id: int, href: str, target: str, flags: WebViewNavigationActionFlags=WEBVIEW_NAV_ACTION_NONE, messageHandler: str="") -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        WebViewEvent() -> None
        WebViewEvent(type, id, href, target, flags=WEBVIEW_NAV_ACTION_NONE, messageHandler="") -> None
        
        A navigation event holds information about events associated with
        wxWebView objects.
        """

    def GetTarget(self) -> str:
        """
        GetTarget() -> str
        
        Get the name of the target frame which the url of this event has been
        or will be loaded into.
        """

    def GetURL(self) -> str:
        """
        GetURL() -> str
        
        Get the URL being visited.
        """

    def GetNavigationAction(self) -> WebViewNavigationActionFlags:
        """
        GetNavigationAction() -> WebViewNavigationActionFlags
        
        Get the type of navigation action.
        """

    def GetMessageHandler(self) -> str:
        """
        GetMessageHandler() -> str
        
        Get the name of the script handler.
        """

    def IsError(self) -> bool:
        """
        IsError() -> bool
        
        Returns true the script execution failed.
        """
    @property
    def MessageHandler(self) -> str: ...
    @property
    def NavigationAction(self) -> WebViewNavigationActionFlags: ...
    @property
    def Target(self) -> str: ...
    @property
    def URL(self) -> str: ...
# end of class WebViewEvent


class WebViewFactory(wx.Object):
    """
    An abstract factory class for creating wxWebView backends.
    """

    @overload
    def Create(self, parent: wx.Window, id: int, url: str=WebViewDefaultURLStr, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, name: str=WebViewNameStr) -> WebView:
        ...

    @overload
    def Create(self) -> WebView:
        """
        Create() -> WebView
        Create(parent, id, url=WebViewDefaultURLStr, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, name=WebViewNameStr) -> WebView
        
        Function to create a new wxWebView with two-step creation,
        wxWebView::Create should be called on the returned object.
        """

    def IsAvailable(self) -> bool:
        """
        IsAvailable() -> bool
        
        Function to check if the backend is available at runtime.
        """

    def GetVersionInfo(self) -> wx.VersionInfo:
        """
        GetVersionInfo() -> wx.VersionInfo
        
        Retrieve the version information about this backend implementation.
        """
    @property
    def VersionInfo(self) -> wx.VersionInfo: ...
# end of class WebViewFactory

USE_WEBVIEW: int

EVT_WEBVIEW_NAVIGATING = wx.PyEventBinder( wxEVT_WEBVIEW_NAVIGATING, 1 )
EVT_WEBVIEW_NAVIGATED = wx.PyEventBinder( wxEVT_WEBVIEW_NAVIGATED, 1 )
EVT_WEBVIEW_LOADED = wx.PyEventBinder( wxEVT_WEBVIEW_LOADED, 1 )
EVT_WEBVIEW_ERROR = wx.PyEventBinder( wxEVT_WEBVIEW_ERROR, 1 )
EVT_WEBVIEW_NEWWINDOW = wx.PyEventBinder( wxEVT_WEBVIEW_NEWWINDOW, 1 )
EVT_WEBVIEW_TITLE_CHANGED = wx.PyEventBinder( wxEVT_WEBVIEW_TITLE_CHANGED, 1 )
EVT_WEBVIEW_FULLSCREEN_CHANGED = wx.PyEventBinder( wxEVT_WEBVIEW_FULLSCREEN_CHANGED, 1)
EVT_WEBVIEW_SCRIPT_MESSAGE_RECEIVED = wx.PyEventBinder( wxEVT_WEBVIEW_SCRIPT_MESSAGE_RECEIVED, 1)
EVT_WEBVIEW_SCRIPT_RESULT = wx.PyEventBinder( wxEVT_WEBVIEW_SCRIPT_RESULT, 1)

# deprecated wxEVT aliases
wxEVT_COMMAND_WEBVIEW_NAVIGATING     = wxEVT_WEBVIEW_NAVIGATING
wxEVT_COMMAND_WEBVIEW_NAVIGATED      = wxEVT_WEBVIEW_NAVIGATED
wxEVT_COMMAND_WEBVIEW_LOADED         = wxEVT_WEBVIEW_LOADED
wxEVT_COMMAND_WEBVIEW_ERROR          = wxEVT_WEBVIEW_ERROR
wxEVT_COMMAND_WEBVIEW_NEWWINDOW      = wxEVT_WEBVIEW_NEWWINDOW
wxEVT_COMMAND_WEBVIEW_TITLE_CHANGED  = wxEVT_WEBVIEW_TITLE_CHANGED
#-- end-webview --#
