# This file is generated by wxPython's SIP generator.  Do not edit by hand.
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License

"""
The classes in this module enable loading widgets and layout from XML.
"""

from ._xrc import *

import wx
ID_NONE = wx.ID_NONE  # Needed for some parameter defaults in this module

XmlResource.LoadFromString = wx.deprecated(XmlResource.LoadFromBuffer, 'Use LoadFromBuffer instead')

@wx.deprecatedMsg("Use :class:`xrc.XmlResource` instead")
def EmptyXmlResource(flags=XRC_USE_LOCALE, domain=""):
    """
    A compatibility wrapper for the XmlResource(flags, domain) constructor
    """
    return XmlResource(flags, domain)

def XRCID(str_id, value_if_not_found=wx.ID_NONE):
    """
    Returns a numeric ID that is equivalent to the string ID used in an XML resource.
    """
    return XmlResource.GetXRCID(str_id, value_if_not_found)

def XRCCTRL(window, str_id, *ignoreargs):
    """
    Returns the child window associated with the string ID in an XML resource.
    """
    return window.FindWindow(XRCID(str_id))

# Create a factory for handling the subclass property of XRC's
# object tag.  This factory will search for the specified
# package.module.class and will try to instantiate it for XRC's
# use.  The class must support instantiation with no parameters and
# delayed creation of the UI widget (aka 2-phase create).

def _my_import(name):
    try:
        mod = __import__(name)
    except ImportError:
        import traceback
        print(traceback.format_exc())
        raise
    components = name.split('.')
    for comp in components[1:]:
        mod = getattr(mod, comp)
    return mod

class XmlSubclassFactory_Python(XmlSubclassFactory):
    def __init__(self):
        XmlSubclassFactory.__init__(self)

    def Create(self, className):
        assert className.find('.') != -1, "Module name must be specified!"
        mname = className[:className.rfind('.')]
        cname = className[className.rfind('.')+1:]
        module = _my_import(mname)
        klass = getattr(module, cname)
        inst = klass()
        return inst

XmlResource.AddSubclassFactory(XmlSubclassFactory_Python())

