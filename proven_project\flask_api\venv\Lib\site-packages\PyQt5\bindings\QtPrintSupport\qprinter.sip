// qprinter.sip generated by MetaSIP
//
// This file is part of the QtPrintSupport Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_Printer)

class QPrinter : public QPagedPaintDevice
{
%TypeHeaderCode
#include <qprinter.h>
%End

public:
    enum PrinterMode
    {
        ScreenResolution,
        PrinterResolution,
        HighResolution,
    };

    explicit QPrinter(QPrinter::PrinterMode mode = QPrinter::ScreenResolution);
    QPrinter(const QPrinterInfo &printer, QPrinter::PrinterMode mode = QPrinter::ScreenResolution);
    virtual ~QPrinter();

    enum Orientation
    {
        Portrait,
        Landscape,
    };

    typedef QPagedPaintDevice::PageSize PaperSize;

    enum PageOrder
    {
        FirstPageFirst,
        LastPageFirst,
    };

    enum ColorMode
    {
        GrayScale,
        Color,
    };

    enum PaperSource
    {
        OnlyOne,
        Lower,
        Middle,
        Manual,
        Envelope,
        EnvelopeManual,
        Auto,
        Tractor,
        SmallFormat,
        LargeFormat,
        LargeCapacity,
        Cassette,
        FormSource,
        MaxPageSource,
%If (Qt_5_3_0 -)
        Upper,
%End
%If (Qt_5_3_0 -)
        CustomSource,
%End
%If (Qt_5_3_0 -)
        LastPaperSource,
%End
    };

    enum PrinterState
    {
        Idle,
        Active,
        Aborted,
        Error,
    };

    enum OutputFormat
    {
        NativeFormat,
        PdfFormat,
    };

    enum PrintRange
    {
        AllPages,
        Selection,
        PageRange,
        CurrentPage,
    };

    enum Unit
    {
        Millimeter,
        Point,
        Inch,
        Pica,
        Didot,
        Cicero,
        DevicePixel,
    };

    enum DuplexMode
    {
        DuplexNone,
        DuplexAuto,
        DuplexLongSide,
        DuplexShortSide,
    };

    void setOutputFormat(QPrinter::OutputFormat format);
    QPrinter::OutputFormat outputFormat() const;
    void setPrinterName(const QString &);
    QString printerName() const;
    bool isValid() const;
    void setOutputFileName(const QString &);
    QString outputFileName() const;
    void setPrintProgram(const QString &);
    QString printProgram() const;
    void setDocName(const QString &);
    QString docName() const;
    void setCreator(const QString &);
    QString creator() const;
    void setOrientation(QPrinter::Orientation);
    QPrinter::Orientation orientation() const;
    virtual void setPageSizeMM(const QSizeF &size);
    void setPaperSize(QPrinter::PaperSize);
    QPrinter::PaperSize paperSize() const;
    void setPaperSize(const QSizeF &paperSize, QPrinter::Unit unit);
    QSizeF paperSize(QPrinter::Unit unit) const;
    void setPageOrder(QPrinter::PageOrder);
    QPrinter::PageOrder pageOrder() const;
    void setResolution(int);
    int resolution() const;
    void setColorMode(QPrinter::ColorMode);
    QPrinter::ColorMode colorMode() const;
    void setCollateCopies(bool collate);
    bool collateCopies() const;
    void setFullPage(bool);
    bool fullPage() const;
    void setCopyCount(int);
    int copyCount() const;
    bool supportsMultipleCopies() const;
    void setPaperSource(QPrinter::PaperSource);
    QPrinter::PaperSource paperSource() const;
    void setDuplex(QPrinter::DuplexMode duplex);
    QPrinter::DuplexMode duplex() const;
    QList<int> supportedResolutions() const;
    void setFontEmbeddingEnabled(bool enable);
    bool fontEmbeddingEnabled() const;
    void setDoubleSidedPrinting(bool enable);
    bool doubleSidedPrinting() const;
    QRect paperRect() const;
    QRect pageRect() const;
    QRectF paperRect(QPrinter::Unit) const;
    QRectF pageRect(QPrinter::Unit) const;
%If (WS_X11 || WS_MACX)
    QString printerSelectionOption() const;
%End
%If (WS_X11 || WS_MACX)
    void setPrinterSelectionOption(const QString &);
%End
    virtual bool newPage();
    bool abort();
    QPrinter::PrinterState printerState() const;
    virtual QPaintEngine *paintEngine() const;
    QPrintEngine *printEngine() const;
    void setFromTo(int fromPage, int toPage);
    int fromPage() const;
    int toPage() const;
    void setPrintRange(QPrinter::PrintRange range);
    QPrinter::PrintRange printRange() const;
    virtual void setMargins(const QPagedPaintDevice::Margins &m);
    void setPageMargins(qreal left, qreal top, qreal right, qreal bottom, QPrinter::Unit unit);
    void getPageMargins(qreal *left, qreal *top, qreal *right, qreal *bottom, QPrinter::Unit unit) const;

protected:
    virtual int metric(QPaintDevice::PaintDeviceMetric) const;
    void setEngines(QPrintEngine *printEngine, QPaintEngine *paintEngine);

public:
%If (Qt_5_1_0 -)
    void setPaperName(const QString &paperName);
%End
%If (Qt_5_1_0 -)
    QString paperName() const;
%End
%If (Qt_5_10_0 -)
    void setPdfVersion(QPagedPaintDevice::PdfVersion version);
%End
%If (Qt_5_10_0 -)
    QPagedPaintDevice::PdfVersion pdfVersion() const;
%End
};

%End
