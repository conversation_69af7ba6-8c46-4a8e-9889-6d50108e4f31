<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none'; connect-src 'self' http://localhost:5001;" />
    <title>APIS TrustFinger</title>
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 10px;
        background-color: #f0f0f0;
        font-size: 12px;
      }
      .main-container {
        max-width: 1200px;
        margin: 0 auto;
        background-color: #f0f0f0;
        padding: 10px;
      }
      .top-section {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
      }
      .user-info-box {
        flex: 0 0 300px;
        background-color: white;
        border: 2px solid #ccc;
        border-radius: 5px;
        padding: 15px;
      }
      .user-info-box h3 {
        margin: 0 0 15px 0;
        font-size: 14px;
        color: #333;
        border-bottom: 1px solid #ddd;
        padding-bottom: 5px;
      }

      .form-row {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }
      .form-row label {
        width: 80px;
        font-size: 12px;
        margin-right: 10px;
      }
      .form-row input, .form-row select {
        flex: 1;
        padding: 4px;
        border: 1px solid #ccc;
        border-radius: 3px;
        font-size: 12px;
      }
      .button-row {
        display: flex;
        gap: 8px;
        margin-bottom: 15px;
        flex-wrap: wrap;
      }
      .btn {
        padding: 8px 16px;
        font-size: 12px;
        font-weight: bold;
        border: 1px solid #ccc;
        border-radius: 3px;
        background: #f8f9fa;
        color: #333;
        cursor: pointer;
        min-width: 90px;
        transition: all 0.2s ease;
      }
      .btn:hover:not(:disabled) {
        background: #e9ecef;
        border-color: #adb5bd;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .btn:disabled {
        background: #e9ecef;
        color: #6c757d;
        cursor: not-allowed;
        opacity: 0.6;
      }
      .btn-primary {
        background: #007bff;
        color: white;
        border-color: #007bff;
      }
      .btn-primary:hover:not(:disabled) {
        background: #0056b3;
        border-color: #0056b3;
      }
      .btn-success {
        background: #28a745;
        color: white;
        border-color: #28a745;
      }
      .btn-success:hover:not(:disabled) {
        background: #1e7e34;
        border-color: #1e7e34;
      }

      .tab-control {
        background-color: white;
        border: 2px solid #ccc;
        border-radius: 5px;
        margin-bottom: 20px;
      }
      .tab-headers {
        display: flex;
        background-color: #f8f9fa;
        border-bottom: 1px solid #ccc;
      }
      .tab-header {
        padding: 10px 20px;
        cursor: pointer;
        border-right: 1px solid #ccc;
        font-size: 12px;
        background-color: #e9ecef;
        transition: background-color 0.2s;
      }
      .tab-header:last-child {
        border-right: none;
      }
      .tab-header.active {
        background-color: white;
        border-bottom: 1px solid white;
        margin-bottom: -1px;
        font-weight: bold;
      }
      .tab-header:hover:not(.active) {
        background-color: #f8f9fa;
      }
      .tab-content {
        display: none;
        padding: 20px;
        min-height: 400px;
      }
      .tab-content.active {
        display: block;
      }
      .fingerprint-preview {
        display: flex;
        gap: 20px;
        margin-top: 20px;
      }
      .preview-item {
        text-align: center;
        flex: 1;
      }
      .preview-image {
        width: 150px;
        height: 200px;
        border: 2px solid #ccc;
        border-radius: 5px;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        font-size: 12px;
        margin: 0 auto 10px;
      }
      .nfiq-label {
        font-size: 11px;
        color: #666;
        font-weight: bold;
      }
      .device-info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-top: 15px;
      }
      .info-group {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        border: 1px solid #dee2e6;
      }
      .info-group h4 {
        margin: 0 0 10px 0;
        font-size: 13px;
        color: #495057;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 5px;
      }
      .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 12px;
      }
      .info-label {
        font-weight: bold;
        color: #495057;
      }
      .info-value {
        color: #6c757d;
      }
      .status-bar {
        background-color: white;
        border: 2px solid #ccc;
        border-radius: 5px;
        padding: 10px;
        margin-top: 10px;
        font-size: 12px;
        color: #495057;
      }

      .preview-row {
        display: flex;
        gap: 20px;
        margin: 10px 0;
      }
      .preview-col {
        flex: 1;
        text-align: center;
      }
      .nfiq-label {
        font-size: 12px;
        color: #555;
        margin-top: 4px;
      }
      .controls {
        display: flex;
        gap: 10px;
        margin: 20px 0;
      }
      button {
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
      }
      button:disabled {
        background-color: #cccccc;
        color: #888;
        cursor: not-allowed;
      }
      .status {
        margin: 10px 0;
        padding: 10px;
        border-radius: 4px;
      }
      .success {
        background-color: #d4edda;
        color: #155724;
      }
      .error {
        background-color: #f8d7da;
        color: #721c24;
      }
      .form-group {
        margin: 10px 0;
      }
      label {
        display: block;
        margin-bottom: 5px;
      }
      input[type="text"] {
        padding: 8px;
        width: 100%;
        max-width: 300px;
        border: 1px solid #ccc;
        border-radius: 4px;
      }
      .device-info {
        margin-bottom: 10px;
        font-size: 14px;
        color: #333;
      }
      .tip-label {
        font-size: 14px;
        color: #007bff;
        margin-top: 10px;
      }



    </style>
  </head>
  <body>
    <div class="main-container">
      <h1 style="margin: 0 0 15px 0; font-size: 18px; color: #333;">APIS TrustFinger</h1>

      <!-- Top Section - matches MainForm layout -->
      <div class="top-section">
        <!-- User Information Box (left side) -->
        <div class="user-info-box">
          <h3>User Information</h3>

          <!-- Device Controls -->
          <div class="button-row">
            <button id="openDeviceBtn" class="btn btn-primary">Open Device</button>
            <button id="startCaptureBtn" class="btn" disabled onclick="console.log('Button clicked directly!')">Start Capture</button>
            <button id="deviceInfoBtn" class="btn">Device Info</button>
          </div>

          <!-- User Input -->
          <div class="form-row">
            <label>Prisoner ID:</label>
            <input type="text" id="prisonerIdInput" placeholder="e.g. PRISONER_001" />
          </div>

          <!-- Image Format -->
          <div class="form-row">
            <label>Image Format:</label>
            <select id="imageFormatSelect">
              <option value="0">BMP</option>
              <option value="1">WSQ</option>
              <option value="2">JPEG</option>
            </select>
          </div>

          <!-- Action Buttons -->
          <div class="button-row">
            <button id="enrollBtn" class="btn btn-success" disabled>Enroll</button>
            <button id="verifyBtn" class="btn" disabled>Verify</button>
            <button id="identifyBtn" class="btn" disabled>Identify</button>
          </div>

          <!-- Additional Controls -->
          <div class="button-row">
            <button id="clearBtn" class="btn">Clear All</button>
          </div>
        </div>



        <!-- Finger Selection Box (top-right, like MainForm.cs) -->
        <div style="flex: 0 0 500px; background-color: white; border: 2px solid #ccc; border-radius: 5px; padding: 15px; height: fit-content;">
          <h3 id="fingerSelectionTitle" style="margin: 0 0 15px 0; font-size: 14px; color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px;">Select Finger Group to Capture</h3>

          <!-- Horizontal layout for finger selection and missing fingers -->
          <div style="display: flex; gap: 20px;">
            <!-- Left side: Finger Selection -->
            <div style="flex: 1;">
              <!-- Slaps Selection (visible on Slaps tab) -->
              <div id="slapsSelection" style="display: flex; flex-direction: column; gap: 8px;">
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="slapsFingerGroup" value="12" checked style="margin-right: 8px;" /> Left Four Fingers
                </label>
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="slapsFingerGroup" value="11" style="margin-right: 8px;" /> Two Thumbs
                </label>
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="slapsFingerGroup" value="13" style="margin-right: 8px;" /> Right Four Fingers
                </label>
              </div>

              <!-- Individual Finger Selection (visible on Rolled/Flat tabs) -->
              <div id="individualSelection" style="display: none; flex-direction: column; gap: 8px;">
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="individualFinger" value="1" checked style="margin-right: 8px;" /> R1 (Right Thumb)
                </label>
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="individualFinger" value="2" style="margin-right: 8px;" /> R2 (Right Index)
                </label>
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="individualFinger" value="3" style="margin-right: 8px;" /> R3 (Right Middle)
                </label>
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="individualFinger" value="4" style="margin-right: 8px;" /> R4 (Right Ring)
                </label>
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="individualFinger" value="5" style="margin-right: 8px;" /> R5 (Right Little)
                </label>
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="individualFinger" value="6" style="margin-right: 8px;" /> L1 (Left Thumb)
                </label>
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="individualFinger" value="7" style="margin-right: 8px;" /> L2 (Left Index)
                </label>
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="individualFinger" value="8" style="margin-right: 8px;" /> L3 (Left Middle)
                </label>
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="individualFinger" value="9" style="margin-right: 8px;" /> L4 (Left Ring)
                </label>
                <label style="display: flex; align-items: center; font-size: 12px; font-weight: bold; cursor: pointer; padding: 4px 8px; border-radius: 3px;">
                  <input type="radio" name="individualFinger" value="10" style="margin-right: 8px;" /> L5 (Left Little)
                </label>
              </div>
            </div>

            <!-- Right side: Missing Fingers -->
            <div style="flex: 1; border-left: 1px solid #ddd; padding-left: 20px;">
              <h4 style="margin: 0 0 10px 0; font-size: 11px; color: #666;">Missing Fingers (Check if unavailable)</h4>
              <div style="display: flex; flex-direction: column; gap: 6px;">
                <label style="display: flex; align-items: center; font-size: 11px; cursor: pointer;">
                  <input type="checkbox" id="L5_checkbox" style="margin-right: 8px;" /> L5 (Left Little)
                </label>
                <label style="display: flex; align-items: center; font-size: 11px; cursor: pointer;">
                  <input type="checkbox" id="L4_checkbox" style="margin-right: 8px;" /> L4 (Left Ring)
                </label>
                <label style="display: flex; align-items: center; font-size: 11px; cursor: pointer;">
                  <input type="checkbox" id="L3_checkbox" style="margin-right: 8px;" /> L3 (Left Middle)
                </label>
                <label style="display: flex; align-items: center; font-size: 11px; cursor: pointer;">
                  <input type="checkbox" id="L2_checkbox" style="margin-right: 8px;" /> L2 (Left Index)
                </label>
                <label style="display: flex; align-items: center; font-size: 11px; cursor: pointer;">
                  <input type="checkbox" id="L1_checkbox" style="margin-right: 8px;" /> L1 (Left Thumb)
                </label>
                <label style="display: flex; align-items: center; font-size: 11px; cursor: pointer;">
                  <input type="checkbox" id="R1_checkbox" style="margin-right: 8px;" /> R1 (Right Thumb)
                </label>
                <label style="display: flex; align-items: center; font-size: 11px; cursor: pointer;">
                  <input type="checkbox" id="R2_checkbox" style="margin-right: 8px;" /> R2 (Right Index)
                </label>
                <label style="display: flex; align-items: center; font-size: 11px; cursor: pointer;">
                  <input type="checkbox" id="R3_checkbox" style="margin-right: 8px;" /> R3 (Right Middle)
                </label>
                <label style="display: flex; align-items: center; font-size: 11px; cursor: pointer;">
                  <input type="checkbox" id="R4_checkbox" style="margin-right: 8px;" /> R4 (Right Ring)
                </label>
                <label style="display: flex; align-items: center; font-size: 11px; cursor: pointer;">
                  <input type="checkbox" id="R5_checkbox" style="margin-right: 8px;" /> R5 (Right Little)
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Tab Control (matches MainForm tabControlMain) -->
      <div class="tab-control">
        <div class="tab-headers">
          <div class="tab-header active" data-tab="slaps" onclick="switchTab('slaps')">Slaps</div>
          <div class="tab-header" data-tab="rolled" onclick="switchTab('rolled')">Rolled Fingerprints</div>
          <div class="tab-header" data-tab="flat" onclick="switchTab('flat')">Flat Fingerprints</div>
          <div class="tab-header" data-tab="device" onclick="switchTab('device')">Device Info</div>
        </div>
      </div>
      <!-- Tab Content Sections -->
      <div id="tab-content-slaps" class="tab-content active">
        <h3 style="margin-top: 0;">Slaps Fingerprints</h3>
        <div class="fingerprint-preview">
          <div class="preview-item">
            <h4>Left Four Fingers</h4>
            <div class="preview-image" id="leftFourPreview">No Image</div>
            <div class="nfiq-label" id="nfiqLeftFour">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Two Thumbs</h4>
            <div class="preview-image" id="twoThumbsPreview">No Image</div>
            <div class="nfiq-label" id="nfiqTwoThumbs">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Right Four Fingers</h4>
            <div class="preview-image" id="rightFourPreview">No Image</div>
            <div class="nfiq-label" id="nfiqRightFour">NFIQ=N/A</div>
          </div>
        </div>
      </div>
      <div id="tab-content-rolled" class="tab-content">
        <h3 style="margin-top: 0;">Rolled Fingerprints</h3>
        <div class="fingerprint-preview">
          <div class="preview-item">
            <h4>Left Thumb</h4>
            <div class="preview-image" id="leftThumbRolled">No Image</div>
            <div class="nfiq-label" id="nfiqLT_ROLL">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Left Index</h4>
            <div class="preview-image" id="leftIndexRolled">No Image</div>
            <div class="nfiq-label" id="nfiqLI_ROLL">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Left Middle</h4>
            <div class="preview-image" id="leftMiddleRolled">No Image</div>
            <div class="nfiq-label" id="nfiqLM_ROLL">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Left Ring</h4>
            <div class="preview-image" id="leftRingRolled">No Image</div>
            <div class="nfiq-label" id="nfiqLR_ROLL">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Left Little</h4>
            <div class="preview-image" id="leftLittleRolled">No Image</div>
            <div class="nfiq-label" id="nfiqLL_ROLL">NFIQ=N/A</div>
          </div>
        </div>
        <div class="fingerprint-preview">
          <div class="preview-item">
            <h4>Right Thumb</h4>
            <div class="preview-image" id="rightThumbRolled">No Image</div>
            <div class="nfiq-label" id="nfiqRT_ROLL">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Right Index</h4>
            <div class="preview-image" id="rightIndexRolled">No Image</div>
            <div class="nfiq-label" id="nfiqRI_ROLL">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Right Middle</h4>
            <div class="preview-image" id="rightMiddleRolled">No Image</div>
            <div class="nfiq-label" id="nfiqRM_ROLL">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Right Ring</h4>
            <div class="preview-image" id="rightRingRolled">No Image</div>
            <div class="nfiq-label" id="nfiqRR_ROLL">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Right Little</h4>
            <div class="preview-image" id="rightLittleRolled">No Image</div>
            <div class="nfiq-label" id="nfiqRL_ROLL">NFIQ=N/A</div>
          </div>
        </div>
      </div>

      <div id="tab-content-flat" class="tab-content">
        <h3 style="margin-top: 0;">Flat Fingerprints</h3>
        <div class="fingerprint-preview">
          <div class="preview-item">
            <h4>Left Thumb</h4>
            <div class="preview-image" id="leftThumbFlat">No Image</div>
            <div class="nfiq-label" id="nfiqLT_FLAT">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Left Index</h4>
            <div class="preview-image" id="leftIndexFlat">No Image</div>
            <div class="nfiq-label" id="nfiqLI_FLAT">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Left Middle</h4>
            <div class="preview-image" id="leftMiddleFlat">No Image</div>
            <div class="nfiq-label" id="nfiqLM_FLAT">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Left Ring</h4>
            <div class="preview-image" id="leftRingFlat">No Image</div>
            <div class="nfiq-label" id="nfiqLR_FLAT">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Left Little</h4>
            <div class="preview-image" id="leftLittleFlat">No Image</div>
            <div class="nfiq-label" id="nfiqLL_FLAT">NFIQ=N/A</div>
          </div>
        </div>
        <div class="fingerprint-preview">
          <div class="preview-item">
            <h4>Right Thumb</h4>
            <div class="preview-image" id="rightThumbFlat">No Image</div>
            <div class="nfiq-label" id="nfiqRT_FLAT">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Right Index</h4>
            <div class="preview-image" id="rightIndexFlat">No Image</div>
            <div class="nfiq-label" id="nfiqRI_FLAT">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Right Middle</h4>
            <div class="preview-image" id="rightMiddleFlat">No Image</div>
            <div class="nfiq-label" id="nfiqRM_FLAT">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Right Ring</h4>
            <div class="preview-image" id="rightRingFlat">No Image</div>
            <div class="nfiq-label" id="nfiqRR_FLAT">NFIQ=N/A</div>
          </div>
          <div class="preview-item">
            <h4>Right Little</h4>
            <div class="preview-image" id="rightLittleFlat">No Image</div>
            <div class="nfiq-label" id="nfiqRL_FLAT">NFIQ=N/A</div>
          </div>
        </div>
      </div>

      <div id="tab-content-device" class="tab-content">
        <h3 style="margin-top: 0;">Device Information</h3>
        <div class="device-info-grid">
          <div class="info-group">
            <h4>Device Details</h4>
            <div class="info-row">
              <span class="info-label">Manufacturer:</span>
              <span class="info-value" id="manufacturerInfo">APIS Co.,LTD.</span>
            </div>
            <div class="info-row">
              <span class="info-label">Device ID:</span>
              <span class="info-value" id="deviceIdInfo">N/A</span>
            </div>
            <div class="info-row">
              <span class="info-label">Serial Number:</span>
              <span class="info-value" id="serialNumberInfo">N/A</span>
            </div>
            <div class="info-row">
              <span class="info-label">Firmware Version:</span>
              <span class="info-value" id="firmwareInfo">N/A</span>
            </div>
          </div>
          <div class="info-group">
            <h4>Image Specifications</h4>
            <div class="info-row">
              <span class="info-label">Image Width:</span>
              <span class="info-value" id="imageWidthInfo">N/A</span>
            </div>
            <div class="info-row">
              <span class="info-label">Image Height:</span>
              <span class="info-value" id="imageHeightInfo">N/A</span>
            </div>
            <div class="info-row">
              <span class="info-label">Image DPI:</span>
              <span class="info-value" id="imageDpiInfo">N/A</span>
            </div>
            <div class="info-row">
              <span class="info-label">Tool Version:</span>
              <span class="info-value" id="toolVersionInfo">v1.0.2</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Status Bar -->
      <div class="status-bar" id="statusBar">Please Open Device</div>

      <!-- Simple Test Section (Proven Project Style) -->
      <div style="margin-top: 20px; padding: 15px; border: 2px solid #007bff; border-radius: 5px; background: #f8f9fa;">
        <h3 style="color: #007bff; margin-top: 0;">Quick Test Functions</h3>
        <p style="margin: 5px 0; font-size: 0.9em; color: #666;">Simple test buttons using proven project approach:</p>
        <button onclick="simpleCapture()" style="margin: 5px; padding: 8px 15px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">Test Capture</button>
        <button onclick="testCaptureForce()" style="margin: 5px; padding: 8px 15px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">Force Test Capture</button>
        <button onclick="simpleEnroll()" style="margin: 5px; padding: 8px 15px; background: #17a2b8; color: white; border: none; border-radius: 3px; cursor: pointer;">Test Enroll</button>
        <button onclick="simpleIdentify()" style="margin: 5px; padding: 8px 15px; background: #ffc107; color: black; border: none; border-radius: 3px; cursor: pointer;">Test Identify</button>
        <button onclick="checkDeviceStatus()" style="margin: 5px; padding: 8px 15px; background: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer;">Check Status</button>
      </div>

      <!-- Response Display -->
      <div id="response" style="margin-top: 10px; padding: 10px; background: #f4f4f4; border: 1px solid #ccc; max-height: 300px; overflow-y: auto; white-space: pre-wrap; font-family: monospace; font-size: 0.9em;"></div>
    </div>



    <script>
      // API configuration - matches your Python Flask API
      const apiBaseUrl = 'http://localhost:5001';

      // UI elements
      const statusBar = document.getElementById('statusBar');
      let currentTab = 'slaps';

      // Tab switching function
      function switchTab(tabName) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.remove('active');
        });

        // Remove active class from all tab headers
        document.querySelectorAll('.tab-header').forEach(header => {
          header.classList.remove('active');
        });

        // Show selected tab content
        const tabContent = document.getElementById(`tab-content-${tabName}`);
        if (tabContent) {
          tabContent.classList.add('active');
        }

        // Add active class to selected tab header
        const tabHeader = document.querySelector(`[data-tab="${tabName}"]`);
        if (tabHeader) {
          tabHeader.classList.add('active');
        }

        currentTab = tabName;

        // Update finger selection interface based on tab
        updateFingerSelectionInterface(tabName);

        // Update button states based on tab
        updateButtonStates();
      }

      // Update finger selection interface based on current tab
      function updateFingerSelectionInterface(tabName) {
        const slapsSelection = document.getElementById('slapsSelection');
        const individualSelection = document.getElementById('individualSelection');
        const fingerSelectionTitle = document.getElementById('fingerSelectionTitle');

        if (tabName === 'slaps') {
          slapsSelection.style.display = 'flex';
          individualSelection.style.display = 'none';
          fingerSelectionTitle.textContent = 'Select Finger Group to Capture';
        } else if (tabName === 'rolled' || tabName === 'flat') {
          slapsSelection.style.display = 'none';
          individualSelection.style.display = 'flex';
          fingerSelectionTitle.textContent = 'Select Individual Finger to Capture';
        } else {
          slapsSelection.style.display = 'none';
          individualSelection.style.display = 'none';
          fingerSelectionTitle.textContent = 'Device Information';
        }
      }

      // Utility functions
      function setStatus(message, isError = false) {
        if (statusBar) {
          statusBar.textContent = message;
          statusBar.style.color = isError ? '#dc3545' : '#28a745';
        }
        console.log(isError ? '❌' : '✅', message);
      }

      function updateButtonStates() {
        const enrollBtn = document.getElementById('enrollBtn');
        const verifyBtn = document.getElementById('verifyBtn');
        const identifyBtn = document.getElementById('identifyBtn');
        const startCaptureBtn = document.getElementById('startCaptureBtn');

        // Enable/disable buttons based on current tab and device state
        if (currentTab === 'slaps') {
          // Slaps tab - disable individual finger operations
          if (enrollBtn) enrollBtn.disabled = true;
          if (verifyBtn) verifyBtn.disabled = true;
          if (identifyBtn) identifyBtn.disabled = false; // Identify works for slaps
        } else if (currentTab === 'rolled' || currentTab === 'flat') {
          // Individual finger tabs - enable all operations
          if (enrollBtn) enrollBtn.disabled = false;
          if (verifyBtn) verifyBtn.disabled = false;
          if (identifyBtn) identifyBtn.disabled = false;
        } else {
          // Device info tab - disable all fingerprint operations
          if (enrollBtn) enrollBtn.disabled = true;
          if (verifyBtn) verifyBtn.disabled = true;
          if (identifyBtn) identifyBtn.disabled = true;
        }
      }

      // API call function
      async function callApi(endpoint, body, label, method = 'POST') {
        setStatus(`⏳ Processing: ${label}...`);

        try {
          const options = {
            method: method,
            headers: { 'Content-Type': 'application/json' }
          };

          if (body && method !== 'GET') {
            options.body = JSON.stringify(body);
          }

          const response = await fetch(apiBaseUrl + endpoint, options);
          const data = await response.json();

          if (data.success) {
            setStatus(`✅ ${label} completed successfully`);
          } else {
            setStatus(`❌ ${label} failed: ${data.error || 'Unknown error'}`, true);
          }

          return data;
        } catch (error) {
          setStatus(`❌ ${label} error: ${error.message}`, true);
          throw error;
        }
      }

      // Device management functions
      async function openDevice() {
        try {
          // First check if device is already open
          const statusData = await callApi('/api/device/info', null, 'Check Device Status', 'GET');
          if (statusData.success && statusData.data && statusData.data.status === 'OPEN') {
            setStatus('✅ Device is already open', false);
            // Update UI to reflect open state
            document.getElementById('openDeviceBtn').textContent = 'Close Device';
            document.getElementById('openDeviceBtn').onclick = closeDevice;
            document.getElementById('startCaptureBtn').disabled = false;
            updateDeviceInfo();
            return true;
          }

          // Device is closed, try to open it
          const data = await callApi('/api/device/open', null, 'Open Device', 'POST');
          if (data.success) {
            document.getElementById('openDeviceBtn').textContent = 'Close Device';
            document.getElementById('openDeviceBtn').onclick = closeDevice;
            document.getElementById('startCaptureBtn').disabled = false;
            updateDeviceInfo();
          }
          return data.success;
        } catch (error) {
          return false;
        }
      }

      async function closeDevice() {
        try {
          const data = await callApi('/api/device/close', null, 'Close Device', 'POST');
          if (data.success) {
            document.getElementById('openDeviceBtn').textContent = 'Open Device';
            document.getElementById('openDeviceBtn').onclick = openDevice;
            document.getElementById('startCaptureBtn').disabled = true;
            clearDeviceInfo();
          }
          return data.success;
        } catch (error) {
          return false;
        }
      }

      async function showDeviceInfo() {
        try {
          const data = await callApi('/api/device/info', null, 'Get Device Info', 'GET');
          if (data.success && data.data) {
            const info = data.data;

            // Switch to Device Info tab and update info
            switchTab('device');
            updateDeviceInfoDisplay(info);
            setStatus('Device information retrieved successfully');
          } else {
            setStatus('Failed to get device information', true);
          }
        } catch (error) {
          setStatus('Error getting device info: ' + error.message, true);
        }
      }

      async function updateDeviceInfo() {
        try {
          const data = await callApi('/api/device/info', null, 'Get Device Info', 'GET');
          if (data.success && data.data) {
            updateDeviceInfoDisplay(data.data);
          }
        } catch (error) {
          console.error('Failed to get device info:', error);
        }
      }

      function updateDeviceInfoDisplay(info) {
        document.getElementById('deviceIdInfo').textContent = info.device_id || 'N/A';
        document.getElementById('serialNumberInfo').textContent = info.serial_number || 'N/A';
        document.getElementById('imageWidthInfo').textContent = info.image_width || 'N/A';
        document.getElementById('imageHeightInfo').textContent = info.image_height || 'N/A';
        document.getElementById('imageDpiInfo').textContent = info.image_dpi || 'N/A';
        document.getElementById('firmwareInfo').textContent = info.firmware_version || 'N/A';
      }

      function clearDeviceInfo() {
        document.getElementById('deviceIdInfo').textContent = 'N/A';
        document.getElementById('serialNumberInfo').textContent = 'N/A';
        document.getElementById('imageWidthInfo').textContent = 'N/A';
        document.getElementById('imageHeightInfo').textContent = 'N/A';
        document.getElementById('imageDpiInfo').textContent = 'N/A';
        document.getElementById('firmwareInfo').textContent = 'N/A';
      }

      // Get selected finger index based on current tab
      function getSelectedFingerIndex() {
        if (currentTab === 'slaps') {
          const selected = document.querySelector('input[name="slapsFingerGroup"]:checked');
          return selected ? parseInt(selected.value) : 12; // Default to Left Four Fingers
        } else if (currentTab === 'rolled' || currentTab === 'flat') {
          const selected = document.querySelector('input[name="individualFinger"]:checked');
          return selected ? parseInt(selected.value) : 1; // Default to Right Thumb
        }
        return 1;
      }

      // Get finger name for display
      function getFingerName(fingerIndex) {
        const fingerNames = {
          1: 'Right Thumb', 2: 'Right Index', 3: 'Right Middle', 4: 'Right Ring', 5: 'Right Little',
          6: 'Left Thumb', 7: 'Left Index', 8: 'Left Middle', 9: 'Left Ring', 10: 'Left Little',
          11: 'Two Thumbs', 12: 'Left Four Fingers', 13: 'Right Four Fingers'
        };
        return fingerNames[fingerIndex] || `Finger ${fingerIndex}`;
      }

      // Fingerprint operation functions
      async function startCapture() {
        console.log('=== START CAPTURE CLICKED ===');
        console.log('Current tab:', currentTab);

        const prisonerId = document.getElementById('prisonerIdInput').value || 'test_user';
        const fingerIndex = getSelectedFingerIndex();
        const fingerName = getFingerName(fingerIndex);

        console.log('Prisoner ID:', prisonerId);
        console.log('Finger Index:', fingerIndex);
        console.log('Finger Name:', fingerName);
        
        // Map tab to operation type
        let operationType = 'flat';
        if (currentTab === 'slaps') operationType = 'slaps';
        else if (currentTab === 'rolled') operationType = 'rolled';

        try {
          const endpoint = '/api/fingerprint/capture';
          const body = {
            finger_position: fingerIndex,
            user_id: prisonerId,
            operation_type: operationType,
            timeout: 15,  // 15 seconds timeout
            save_image: true
          };

          setStatus(`⏳ Capturing ${fingerName} - Place finger on device...`);

          const data = await callApi(endpoint, body, `Capture ${fingerName}`, 'POST');

          // Debug: Log the complete response structure
          console.log('=== CAPTURE API RESPONSE DEBUG ===');
          console.log('Complete API response:', data);
          if (data.data) {
            console.log('data.data.bmp_base64:', data.data.bmp_base64 ? `${data.data.bmp_base64.length} chars` : 'not found');
          }
          console.log('=== END DEBUG ===');

          // Display status message from bridge
          const bridgeMessage = (data.data && data.data.message) || data.message || 'Unknown response';
          if (data.success) {
            setStatus(`✅ ${bridgeMessage}`);
          } else {
            setStatus(`❌ ${bridgeMessage}`, true);
          }

          // Display BMP if available
          if (data.data && data.data.bmp_base64) {
            console.log(`✅ Received BMP data for finger ${fingerIndex}, length: ${data.data.bmp_base64.length}`);
            displayBmpImage(data.data.bmp_base64, fingerIndex);
          } else {
            console.log('❌ No BMP data received in response');
          }

          // Enable other operations
          updateButtonStates();
        } catch (error) {
          setStatus('❌ Capture failed: ' + error.message, true);
        }
      }

      async function enrollFingerprint() {
        const prisonerId = document.getElementById('prisonerIdInput').value;
        if (!prisonerId) {
          setStatus('❌ Please enter Prisoner ID for enrollment', true);
          return;
        }

        try {
          const data = await callApi('/api/fingerprint/enroll',
            { user_id: prisonerId }, 'Enroll Fingerprint', 'POST');
          if (data.success) {
            setStatus(`✅ Fingerprint enrolled successfully for ${prisonerId}`);
          }
        } catch (error) {
          setStatus('❌ Enroll failed', true);
        }
      }

      async function verifyFingerprint() {
        const prisonerId = document.getElementById('prisonerIdInput').value;
        if (!prisonerId) {
          setStatus('❌ Please enter Prisoner ID for verification', true);
          return;
        }

        const fingerIndex = getSelectedFingerIndex();

        try {
          const data = await callApi('/api/fingerprint/verify',
            { user_id: prisonerId, finger_position: fingerIndex }, 'Verify Fingerprint', 'POST');
          if (data.success) {
            setStatus(`✅ Fingerprint verification completed for ${prisonerId}`);
          }
        } catch (error) {
          setStatus('❌ Verify failed', true);
        }
      }

      async function identifyFingerprint() {
        try {
          const data = await callApi('/api/fingerprint/identify',
            { threshold: 70 }, 'Identify Fingerprint', 'POST');
          if (data.success) {
            setStatus('✅ Fingerprint identification completed');
          }
        } catch (error) {
          setStatus('❌ Identify failed', true);
        }
      }

      function clearAll() {
        // Clear all fingerprint previews
        document.querySelectorAll('.preview-image').forEach(img => {
          img.textContent = 'No Image';
        });

        // Reset NFIQ labels
        document.querySelectorAll('.nfiq-label').forEach(label => {
          label.textContent = 'NFIQ=N/A';
        });

        setStatus('All data cleared');
      }

      // Initialize when page loads
      document.addEventListener('DOMContentLoaded', function() {
        // Set up event handlers
        const openDeviceBtn = document.getElementById('openDeviceBtn');
        const startCaptureBtn = document.getElementById('startCaptureBtn');
        const deviceInfoBtn = document.getElementById('deviceInfoBtn');
        const clearBtn = document.getElementById('clearBtn');
        const enrollBtn = document.getElementById('enrollBtn');
        const verifyBtn = document.getElementById('verifyBtn');
        const identifyBtn = document.getElementById('identifyBtn');

        // Device control handlers
        if (openDeviceBtn) {
          openDeviceBtn.onclick = openDevice;
        }

        if (startCaptureBtn) {
          startCaptureBtn.onclick = function() {
            console.log('Start Capture button clicked!');
            console.log('Button disabled state:', startCaptureBtn.disabled);
            if (startCaptureBtn.disabled) {
              console.log('Button is disabled - not executing capture');
              setStatus('❌ Please open device first before capturing', true);
              return;
            }
            startCapture();
          };
        }

        if (deviceInfoBtn) {
          deviceInfoBtn.onclick = showDeviceInfo;
        }

        if (clearBtn) {
          clearBtn.onclick = clearAll;
        }

        // Fingerprint operation handlers
        if (enrollBtn) {
          enrollBtn.onclick = enrollFingerprint;
        }

        if (verifyBtn) {
          verifyBtn.onclick = verifyFingerprint;
        }

        if (identifyBtn) {
          identifyBtn.onclick = identifyFingerprint;
        }

        // Initialize UI state
        updateFingerSelectionInterface('slaps'); // Start with slaps tab
        updateButtonStates();
        setStatus('Please Open Device');

        // Check initial device status and simulate Open Device if needed
        checkInitialDeviceStatus().then(() => {
          // After checking status, simulate Open Device click if needed
          if (document.getElementById('openDeviceBtn').textContent === 'Close Device') {
            simulateOpenDeviceAction();
          }
        });
      });

      // Simulate Open Device button click action
      function simulateOpenDeviceAction() {
        const openBtn = document.getElementById('openDeviceBtn');
        if (openBtn) {
          // Trigger click animation
          openBtn.classList.add('active');
          setTimeout(() => {
            openBtn.classList.remove('active');
          }, 300);
          
          // Update status to show simulated action
          setStatus('Device connection established automatically');
        }
      }

      async function checkInitialDeviceStatus() {
        try {
          const data = await callApi('/api/device/info', null, 'Check Device Status', 'GET');
          
          // Debug: Log the complete device info response
          console.log('Device info response:', data);
          
          // Show debug info in status bar
          const debugStatus = `API response: success=${data.success}, status=${data.data?.status || 'undefined'}`;
          setStatus(debugStatus, !data.success);
          
          // Check if we got a valid response with device status
          if (data.success && data.data && data.data.status) {
            if (data.data.status.toUpperCase() === 'OPEN') {
              // Device is open - update UI accordingly
              document.getElementById('openDeviceBtn').textContent = 'Close Device';
              document.getElementById('openDeviceBtn').onclick = closeDevice;
              document.getElementById('startCaptureBtn').disabled = false;
              updateDeviceInfoDisplay(data.data);
              setStatus('✅ Device is ready - Connected successfully');
              
              // Simulate visual feedback for auto-connection
              simulateOpenDeviceAction();
            } else {
              // Device status is not OPEN (could be CLOSED or ERROR)
              document.getElementById('openDeviceBtn').textContent = 'Open Device';
              document.getElementById('openDeviceBtn').onclick = openDevice;
              document.getElementById('startCaptureBtn').disabled = true;
              setStatus(`❌ Device status: ${data.data.status || 'CLOSED'} - Please Open Device`);
              clearDeviceInfo();
            }
          } else {
            // Invalid response structure - default to closed state
            document.getElementById('openDeviceBtn').textContent = 'Open Device';
            document.getElementById('openDeviceBtn').onclick = openDevice;
            document.getElementById('startCaptureBtn').disabled = true;
            setStatus('❌ Unable to determine device status - Please Open Device', true);
            clearDeviceInfo();
          }
        } catch (error) {
          setStatus('❌ Error checking device status: ' + error.message, true);
          // Default to closed state
          document.getElementById('openDeviceBtn').textContent = 'Open Device';
          document.getElementById('openDeviceBtn').onclick = openDevice;
          document.getElementById('startCaptureBtn').disabled = true;
        }
        
        // Always update button states after device status check
        updateButtonStates();
      }

      // Simulate the Open Device action for already connected devices
      async function simulateOpenDeviceAction(deviceInfo) {
        // Update UI to show device is open
        document.getElementById('openDeviceBtn').textContent = 'Close Device';
        document.getElementById('openDeviceBtn').onclick = closeDevice;
        document.getElementById('startCaptureBtn').disabled = false;
        
        // Update device information display
        updateDeviceInfoDisplay(deviceInfo);
        
        // Set status message
        setStatus('Device is ready - Connected successfully');
        
        // Add a slight delay to simulate the "click" effect
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // Add visual feedback to simulate button click
        const openBtn = document.getElementById('openDeviceBtn');
        openBtn.style.backgroundColor = '#28a745';
        openBtn.style.color = 'white';
        await new Promise(resolve => setTimeout(resolve, 300));
        openBtn.style.backgroundColor = '';
        openBtn.style.color = '';
      }

      // Function to display captured BMP image (same as proven project)
      function displayBmpImage(base64Bmp, fingerIndex) {
        console.log('displayBmpImage called with:', {
          base64Bmp: base64Bmp ? `${base64Bmp.length} characters` : 'null/undefined',
          fingerIndex: fingerIndex
        });

        if (!base64Bmp) {
          console.log('No BMP data to display');
          return;
        }

        console.log(`Displaying BMP image for finger ${fingerIndex}, data length: ${base64Bmp.length}`);
        console.log('First 100 chars of BMP data:', base64Bmp.substring(0, 100));

        // Create image data URL
        const imageDataUrl = 'data:image/bmp;base64,' + base64Bmp;
        console.log('Created image data URL, length:', imageDataUrl.length);

        // Determine which preview element to update based on finger index
        let previewElementId = '';

        if (fingerIndex == 11) {
          previewElementId = 'twoThumbsPreview';
        } else if (fingerIndex == 12) {
          previewElementId = 'leftFourPreview';
        } else if (fingerIndex == 13) {
          previewElementId = 'rightFourPreview';
        } else if (fingerIndex >= 1 && fingerIndex <= 10) {
          // Individual fingers - determine based on current tab and finger index
          const currentTab = document.querySelector('.tab-header.active').dataset.tab;
          const fingerNames = ['', 'rightThumb', 'rightIndex', 'rightMiddle', 'rightRing', 'rightLittle',
                              'leftThumb', 'leftIndex', 'leftMiddle', 'leftRing', 'leftLittle'];

          if (currentTab === 'rolled') {
            previewElementId = fingerNames[fingerIndex] + 'Rolled';
          } else if (currentTab === 'flat') {
            previewElementId = fingerNames[fingerIndex] + 'Flat';
          }
        }

        // Update the preview element
        console.log('Determined preview element ID:', previewElementId);

        if (previewElementId) {
          const previewElement = document.getElementById(previewElementId);
          console.log('Found preview element:', previewElement);

          if (previewElement) {
            previewElement.innerHTML = `<img src="${imageDataUrl}" style="max-width: 100%; max-height: 100%; object-fit: contain;" alt="Captured Fingerprint">`;
            console.log(`✅ Updated preview element: ${previewElementId}`);
            console.log('Preview element after update:', previewElement.innerHTML.substring(0, 100));
          } else {
            console.log(`❌ Preview element not found: ${previewElementId}`);
            // List all available elements with similar IDs
            const allElements = document.querySelectorAll('[id*="Preview"]');
            console.log('Available preview elements:', Array.from(allElements).map(el => el.id));
          }
        } else {
          console.log(`❌ Could not determine preview element for finger ${fingerIndex}`);
        }
      }

      // Simple test functions (proven project style)
      async function simpleCapture() {
        try {
          const data = await callApi('/api/fingerprint/capture-direct',
            { finger_index: 12 }, 'Simple Capture Test', 'POST');
          console.log('Simple capture result:', data);
        } catch (error) {
          console.error('Simple capture error:', error);
        }
      }

      // Force test capture (ignores device status)
      async function testCaptureForce() {
        console.log('=== FORCE TEST CAPTURE ===');
        console.log('Testing direct API call...');

        try {
          setStatus('⏳ Testing direct capture API call...');

          const testData = {
            finger_index: 12,
            user_id: 'test_user'
          };

          console.log('Sending request to:', apiBaseUrl + '/api/fingerprint/capture-direct');
          console.log('Request data:', testData);

          const response = await fetch(apiBaseUrl + '/api/fingerprint/capture-direct', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(testData)
          });

          console.log('Response status:', response.status);
          console.log('Response headers:', response.headers);

          const data = await response.json();
          console.log('Response data:', data);

          if (data.success) {
            setStatus('✅ Force test capture successful!');
          } else {
            setStatus('❌ Force test capture failed: ' + (data.error || 'Unknown error'), true);
          }

        } catch (error) {
          console.error('Force test error:', error);
          setStatus('❌ Force test error: ' + error.message, true);
        }
      }

      async function simpleEnroll() {
        try {
          const data = await callApi('/api/fingerprint/enroll',
            { user_id: 'test_user' }, 'Simple Enroll Test', 'POST');
          console.log('Simple enroll result:', data);
        } catch (error) {
          console.error('Simple enroll error:', error);
        }
      }

      async function simpleIdentify() {
        try {
          const data = await callApi('/api/fingerprint/identify',
            { threshold: 70 }, 'Simple Identify Test', 'POST');
          console.log('Simple identify result:', data);
        } catch (error) {
          console.error('Simple identify error:', error);
        }
      }

      async function checkDeviceStatus() {
        try {
          const data = await callApi('/api/device/info', null, 'Check Device Status', 'GET');
          console.log('Device status result:', data);
        } catch (error) {
          console.error('Device status error:', error);
        }
      }



    </script>
  </body>
</html>
