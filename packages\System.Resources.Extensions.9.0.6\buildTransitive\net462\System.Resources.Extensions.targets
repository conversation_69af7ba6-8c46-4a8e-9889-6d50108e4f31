<Project>
  <!-- ResolveAssemblyReferences will never see the assembly reference embedded in the resources type,
       force a binding redirect ourselves so that we'll always unify to the System.Resources.Extensions
       version provided by this package -->
  <ItemGroup>
    <SuggestedBindingRedirects Include="System.Resources.Extensions, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51" MaxVersion="9.0.0.6" />
  </ItemGroup>
</Project>
