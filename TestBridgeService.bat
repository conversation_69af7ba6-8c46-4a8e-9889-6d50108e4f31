@echo off
echo ========================================
echo   Fingerprint Bridge Service Test
echo ========================================
echo.

echo 1. Checking if application is running...
tasklist /FI "IMAGENAME eq MultipleFinger.exe" | find "MultipleFinger.exe"
if %ERRORLEVEL% == 0 (
    echo ✓ Application is running
) else (
    echo ✗ Application is not running
    echo Starting application...
    start "" "bin\Debug\MultipleFinger.exe"
    timeout /t 3 /nobreak >nul
)

echo.
echo 2. Testing REST API endpoints...
echo Testing API connection...
curl -s -o nul -w "API Status: %%{http_code}\n" http://localhost:9000/api/fingerprint/enroll -X POST -H "Content-Type: application/json" -d "{\"test\":\"ping\"}" 2>nul
if %ERRORLEVEL% == 0 (
    echo ✓ REST API is responding
) else (
    echo ✗ REST API is not responding
)

echo.
echo 3. Testing Web Demo...
curl -s -o nul -w "Web Demo Status: %%{http_code}\n" http://localhost:8080/ 2>nul
if %ERRORLEVEL% == 0 (
    echo ✓ Web Demo is responding
) else (
    echo ✗ Web Demo is not responding
)

echo.
echo ========================================
echo   Service Information
echo ========================================
echo REST API: http://localhost:9000/
echo Web Demo: http://localhost:8080/
echo.
echo Look for the system tray icon to access the console!
echo Right-click the tray icon for menu options.
echo.
pause
