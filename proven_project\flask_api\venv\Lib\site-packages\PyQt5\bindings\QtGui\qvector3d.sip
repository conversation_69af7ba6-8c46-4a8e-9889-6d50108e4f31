// qvector3d.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qvector3d.h>
%End

class QVector3D
{
%TypeHeaderCode
#include <qvector3d.h>
%End

%PickleCode
    sipRes = Py_BuildValue((char *)"ddd", (double)sipCpp->x(), (double)sipCpp->y(),
            (double)sipCpp->z());
%End

public:
    QVector3D();
    QVector3D(float xpos, float ypos, float zpos);
    explicit QVector3D(const QPoint &point);
    explicit QVector3D(const QPointF &point);
    QVector3D(const QVector2D &vector);
    QVector3D(const QVector2D &vector, float zpos);
    explicit QVector3D(const QVector4D &vector);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *x = PyFloat_FromDouble(sipCpp->x());
        PyObject *y = PyFloat_FromDouble(sipCpp->y());
        PyObject *z = PyFloat_FromDouble(sipCpp->z());
        
        if (x && y && z)
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromFormat("PyQt5.QtGui.QVector3D(%R, %R, %R)", x, y,
                    z);
        #else
            sipRes = PyString_FromString("PyQt5.QtGui.QVector3D(");
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(x));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(y));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(z));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
        }
        
        Py_XDECREF(x);
        Py_XDECREF(y);
        Py_XDECREF(z);
%End

    float length() const;
    float lengthSquared() const;
    QVector3D normalized() const;
    void normalize();
    static float dotProduct(const QVector3D &v1, const QVector3D &v2);
    static QVector3D crossProduct(const QVector3D &v1, const QVector3D &v2);
    static QVector3D normal(const QVector3D &v1, const QVector3D &v2);
    static QVector3D normal(const QVector3D &v1, const QVector3D &v2, const QVector3D &v3);
    float distanceToPlane(const QVector3D &plane, const QVector3D &normal) const;
    float distanceToPlane(const QVector3D &plane1, const QVector3D &plane2, const QVector3D &plane3) const;
    float distanceToLine(const QVector3D &point, const QVector3D &direction) const;
    QVector2D toVector2D() const;
    QVector4D toVector4D() const;
    bool isNull() const;
    float x() const;
    float y() const;
    float z() const;
    void setX(float aX);
    void setY(float aY);
    void setZ(float aZ);
    QVector3D &operator+=(const QVector3D &vector);
    QVector3D &operator-=(const QVector3D &vector);
    QVector3D &operator*=(float factor);
    QVector3D &operator*=(const QVector3D &vector);
    QVector3D &operator/=(float divisor);
%If (Qt_5_5_0 -)
    QVector3D &operator/=(const QVector3D &vector);
%End
    QPoint toPoint() const;
    QPointF toPointF() const;
%If (Qt_5_1_0 -)
    float distanceToPoint(const QVector3D &point) const;
%End
%If (Qt_5_2_0 -)
    float operator[](int i) const;
%End
%If (Qt_5_5_0 -)
    QVector3D project(const QMatrix4x4 &modelView, const QMatrix4x4 &projection, const QRect &viewport) const;
%End
%If (Qt_5_5_0 -)
    QVector3D unproject(const QMatrix4x4 &modelView, const QMatrix4x4 &projection, const QRect &viewport) const;
%End
};

bool operator==(const QVector3D &v1, const QVector3D &v2);
bool operator!=(const QVector3D &v1, const QVector3D &v2);
const QVector3D operator+(const QVector3D &v1, const QVector3D &v2);
const QVector3D operator-(const QVector3D &v1, const QVector3D &v2);
const QVector3D operator*(float factor, const QVector3D &vector);
const QVector3D operator*(const QVector3D &vector, float factor);
const QVector3D operator*(const QVector3D &v1, const QVector3D &v2);
const QVector3D operator-(const QVector3D &vector);
const QVector3D operator/(const QVector3D &vector, float divisor);
%If (Qt_5_5_0 -)
const QVector3D operator/(const QVector3D &vector, const QVector3D &divisor);
%End
bool qFuzzyCompare(const QVector3D &v1, const QVector3D &v2);
QDataStream &operator<<(QDataStream &, const QVector3D & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QVector3D & /Constrained/) /ReleaseGIL/;
