// qtimezone.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QTimeZone
{
%TypeHeaderCode
#include <qtimezone.h>
%End

public:
    enum TimeType
    {
        StandardTime,
        DaylightTime,
        GenericTime,
    };

    enum NameType
    {
        DefaultName,
        LongName,
        ShortName,
        OffsetName,
    };

    struct OffsetData
    {
%TypeHeaderCode
#include <qtimezone.h>
%End

        QString abbreviation;
        QDateTime atUtc;
        int offsetFromUtc;
        int standardTimeOffset;
        int daylightTimeOffset;
    };

    typedef QVector<QTimeZone::OffsetData> OffsetDataList;
    QTimeZone();
    explicit QTimeZone(const QByteArray &ianaId);
    explicit QTimeZone(int offsetSeconds);
    QTimeZone(const QByteArray &zoneId, int offsetSeconds, const QString &name, const QString &abbreviation, QLocale::Country country = QLocale::AnyCountry, const QString &comment = QString());
    QTimeZone(const QTimeZone &other);
    ~QTimeZone();
    void swap(QTimeZone &other /Constrained/);
    bool operator==(const QTimeZone &other) const;
    bool operator!=(const QTimeZone &other) const;
    bool isValid() const;
    QByteArray id() const;
    QLocale::Country country() const;
    QString comment() const;
    QString displayName(const QDateTime &atDateTime, QTimeZone::NameType nameType = QTimeZone::DefaultName, const QLocale &locale = QLocale()) const;
    QString displayName(QTimeZone::TimeType timeType, QTimeZone::NameType nameType = QTimeZone::DefaultName, const QLocale &locale = QLocale()) const;
    QString abbreviation(const QDateTime &atDateTime) const;
    int offsetFromUtc(const QDateTime &atDateTime) const;
    int standardTimeOffset(const QDateTime &atDateTime) const;
    int daylightTimeOffset(const QDateTime &atDateTime) const;
    bool hasDaylightTime() const;
    bool isDaylightTime(const QDateTime &atDateTime) const;
    QTimeZone::OffsetData offsetData(const QDateTime &forDateTime) const;
    bool hasTransitions() const;
    QTimeZone::OffsetData nextTransition(const QDateTime &afterDateTime) const;
    QTimeZone::OffsetData previousTransition(const QDateTime &beforeDateTime) const;
    QTimeZone::OffsetDataList transitions(const QDateTime &fromDateTime, const QDateTime &toDateTime) const;
    static QByteArray systemTimeZoneId();
    static bool isTimeZoneIdAvailable(const QByteArray &ianaId);
    static QList<QByteArray> availableTimeZoneIds();
    static QList<QByteArray> availableTimeZoneIds(QLocale::Country country /Constrained/);
    static QList<QByteArray> availableTimeZoneIds(int offsetSeconds);
    static QByteArray ianaIdToWindowsId(const QByteArray &ianaId);
    static QByteArray windowsIdToDefaultIanaId(const QByteArray &windowsId);
    static QByteArray windowsIdToDefaultIanaId(const QByteArray &windowsId, QLocale::Country country);
    static QList<QByteArray> windowsIdToIanaIds(const QByteArray &windowsId);
    static QList<QByteArray> windowsIdToIanaIds(const QByteArray &windowsId, QLocale::Country country);
%If (Qt_5_5_0 -)
    static QTimeZone systemTimeZone();
%End
%If (Qt_5_5_0 -)
    static QTimeZone utc();
%End
};

%End
%If (Qt_5_2_0 -)
QDataStream &operator<<(QDataStream &ds, const QTimeZone &tz /Constrained/) /ReleaseGIL/;
%End
%If (Qt_5_2_0 -)
QDataStream &operator>>(QDataStream &ds, QTimeZone &tz /Constrained/) /ReleaseGIL/;
%End
