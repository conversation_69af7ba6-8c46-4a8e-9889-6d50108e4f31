"""
TCP Client module for communicating with C# MultipleFinger Bridge (MainForm.cs)
Uses text-based command protocol on port 8123
"""

import socket
import time
import logging
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TcpClientError(Exception):
    """Custom exception for TCP client errors"""
    pass


class TcpClient:
    """TCP client for communicating with C# MultipleFinger Bridge (MainForm.cs)"""

    def __init__(self, host: str = 'localhost', port: int = 8123, timeout: int = 30):
        """
        Initialize TCP client

        Args:
            host: Server hostname or IP address
            port: Server port number (default 8123 for MainForm.cs bridge)
            timeout: Socket timeout in seconds
        """
        self.host = host
        self.port = port
        self.timeout = timeout

    def send_command(self, command_line: str) -> Dict[str, Any]:
        """
        Send a text command to the TCP server and return the response

        Args:
            command_line: Complete command line (e.g., "CAPTURE USER001 1")

        Returns:
            Response dictionary with parsed response

        Raises:
            TcpClientError: If communication fails or server returns error
        """
        try:
            # Create socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)

            try:
                # Connect to server
                sock.connect((self.host, self.port))

                # Send command (text-based protocol)
                logger.debug(f"Sending: {command_line}")
                sock.send((command_line + '\n').encode('utf-8'))

                # Receive response (may be multiple lines)
                response_lines = []
                sock_file = sock.makefile('r', encoding='utf-8')

                # Read all response lines until connection closes or timeout
                try:
                    while True:
                        line = sock_file.readline()
                        if not line:
                            break
                        response_lines.append(line.strip())
                except socket.timeout:
                    pass  # Expected when server closes connection

                sock_file.close()

                logger.debug(f"Received: {response_lines}")

                # Parse response
                if not response_lines:
                    raise TcpClientError("No response received from server")

                # Check for error in first line
                first_line = response_lines[0]
                if first_line.startswith("ERROR"):
                    raise TcpClientError(f"Server error: {first_line}")

                return self._parse_response(response_lines)

            finally:
                sock.close()

        except socket.timeout:
            raise TcpClientError(f"Connection timeout after {self.timeout} seconds")
        except socket.error as e:
            raise TcpClientError(f"Socket error: {e}")
        except Exception as e:
            raise TcpClientError(f"Unexpected error: {e}")

    def _parse_response(self, response_lines: list) -> Dict[str, Any]:
        """
        Parse text response lines into a structured dictionary

        Args:
            response_lines: List of response lines from server

        Returns:
            Parsed response dictionary
        """
        result = {
            'success': True,
            'data': {},
            'raw_response': response_lines
        }

        for line in response_lines:
            if line.startswith('✅'):
                result['data']['message'] = line
            elif line.startswith('BMP:'):
                result['data']['image_data'] = line[4:]  # Remove 'BMP:' prefix
            elif line.startswith('RESULT:'):
                # Parse result data (e.g., "RESULT:VERIFIED=true,PERSON_ID=USER001,FINGER=Right_Index,SCORE=92")
                result_data = line[7:]  # Remove 'RESULT:' prefix
                result['data']['result'] = self._parse_result_data(result_data)
            elif line.startswith('ERROR'):
                result['success'] = False
                result['error'] = line

        return result

    def _parse_result_data(self, result_data: str) -> Dict[str, Any]:
        """
        Parse result data string into dictionary

        Args:
            result_data: Result data string (e.g., "VERIFIED=true,PERSON_ID=USER001,SCORE=92")

        Returns:
            Parsed result dictionary
        """
        result = {}
        pairs = result_data.split(',')

        for pair in pairs:
            if '=' in pair:
                key, value = pair.split('=', 1)
                # Try to convert to appropriate type
                if value.lower() == 'true':
                    result[key.lower()] = True
                elif value.lower() == 'false':
                    result[key.lower()] = False
                elif value.isdigit():
                    result[key.lower()] = int(value)
                else:
                    result[key.lower()] = value

        return result

    def get_status(self) -> Dict[str, Any]:
        """Get server status - simulated since MainForm.cs doesn't have STATUS command"""
        try:
            # Test connection by sending a simple command
            self.test_connection()
            return {
                'status': 'TCP Bridge Running',
                'device_connected': True,  # Assume connected if TCP works
                'tcp_port': self.port,
                'host': self.host
            }
        except TcpClientError:
            return {
                'status': 'TCP Bridge Disconnected',
                'device_connected': False,
                'tcp_port': self.port,
                'host': self.host
            }

    def capture_fingerprint(self, finger_position: int, user_id: str = "TEMP_USER",
                          operation_type: str = "flat", timeout: int = 30,
                          save_image: bool = True) -> Dict[str, Any]:
        """
        Initiate fingerprint capture using MainForm.cs CAPTURE command

        Args:
            finger_position: Finger position (1-14, where 11-14 are slaps)
            user_id: User identifier for capture
            operation_type: "flat", "rolled", or "slaps" (informational only)
            timeout: Capture timeout in seconds (not used by MainForm.cs)
            save_image: Whether to save image data (not used by MainForm.cs)

        Returns:
            Capture response data
        """
        # MainForm.cs expects: CAPTURE <person_id> <finger_index>
        command = f"CAPTURE {user_id} {finger_position}"
        response = self.send_command(command)

        # Add additional metadata
        response['data']['finger_position'] = finger_position
        response['data']['finger_position_name'] = self._get_finger_name(finger_position)
        response['data']['operation_type'] = operation_type
        response['data']['user_id'] = user_id

        return response.get('data', {})

    def identify_fingerprint(self, finger_index: int, template_data: str = None, threshold: int = 70) -> Dict[str, Any]:
        """
        Identify a fingerprint template using MainForm.cs IDENTIFY command

        Args:
            finger_index: Finger position (1-13, where 11=Two Thumbs, 12=Left Four, 13=Right Four)
            template_data: Base64-encoded template data (not used by MainForm.cs)
            threshold: Matching threshold (not used by MainForm.cs)

        Returns:
            Identification results
        """
        # MainForm.cs expects: IDENTIFY <finger_index>
        command = f"IDENTIFY {finger_index}"
        response = self.send_command(command)

        # Add additional metadata
        response['data']['finger_index'] = finger_index
        response['data']['finger_name'] = self._get_finger_name(finger_index)

        return response.get('data', {})

    def verify_fingerprint(self, user_id: str, finger_position: int) -> Dict[str, Any]:
        """
        Verify a fingerprint against specific user template (1:1 matching)

        Args:
            user_id: User identifier to verify against
            finger_position: Finger position (1-10)

        Returns:
            Verification result
        """
        # MainForm.cs expects: VERIFY <person_id> <finger_index>
        command = f"VERIFY {user_id} {finger_position}"
        response = self.send_command(command)

        # Add additional metadata
        response['data']['user_id'] = user_id
        response['data']['finger_position'] = finger_position
        response['data']['finger_position_name'] = self._get_finger_name(finger_position)

        return response.get('data', {})

    def enroll_fingerprint(self, user_id: str, finger_position: int,
                          template_data: str = "", image_data: str = "",
                          image_quality: int = 0) -> Dict[str, Any]:
        """
        Enroll fingerprint templates using MainForm.cs ENROLL command

        Args:
            user_id: User identifier
            finger_position: Finger position (1-13, where 11=Two Thumbs, 12=Left Four, 13=Right Four)
            template_data: Base64-encoded template data (not used by MainForm.cs)
            image_data: Base64-encoded image data (not used by MainForm.cs)
            image_quality: Image quality score (not used by MainForm.cs)

        Returns:
            Enrollment result
        """
        # MainForm.cs expects: ENROLL <person_id> <finger_index>
        command = f"ENROLL {user_id} {finger_position}"
        response = self.send_command(command)

        # Add additional metadata
        response['data']['user_id'] = user_id
        response['data']['finger_position'] = finger_position
        response['data']['finger_name'] = self._get_finger_name(finger_position)

        return response.get('data', {})

    def get_captured_data(self) -> Dict[str, Any]:
        """Get captured fingerprint data - not directly supported by MainForm.cs"""
        # MainForm.cs doesn't have a specific command for this
        # Return simulated response
        return {
            'message': 'MainForm.cs bridge does not support get_captured_data command',
            'captured_data': [],
            'note': 'Use CAPTURE command to capture fingerprints directly'
        }

    def open_device(self) -> Dict[str, Any]:
        """
        Open the fingerprint device

        Returns:
            Device open result with status and device information
        """
        command = "OPEN"
        response = self.send_command(command)
        return response.get('data', {})

    def close_device(self) -> Dict[str, Any]:
        """
        Close the fingerprint device

        Returns:
            Device close result with status
        """
        command = "CLOSE"
        response = self.send_command(command)
        return response.get('data', {})

    def get_device_info(self) -> Dict[str, Any]:
        """
        Get device information and status

        Returns:
            Device information including status, device ID, name, and serial number
        """
        command = "STATUS"
        response = self.send_command(command)
        return response.get('data', {})

    def test_connection(self) -> bool:
        """
        Test if connection to server is working

        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Try a simple connection test
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((self.host, self.port))
            sock.close()
            return result == 0
        except Exception:
            return False

    def _get_finger_name(self, finger_position: int) -> str:
        """
        Get finger name from position index

        Args:
            finger_position: Finger position (1-14)

        Returns:
            Human-readable finger name
        """
        finger_names = {
            1: "Right Thumb",
            2: "Right Index",
            3: "Right Middle",
            4: "Right Ring",
            5: "Right Little",
            6: "Left Thumb",
            7: "Left Index",
            8: "Left Middle",
            9: "Left Ring",
            10: "Left Little",
            11: "Two Thumbs",
            12: "Left Four Fingers",
            13: "Right Four Fingers",
            14: "Two Fingers"
        }
        return finger_names.get(finger_position, f"Unknown Position {finger_position}")


# Convenience function for quick testing
def create_client(host: str = 'localhost', port: int = 8123) -> TcpClient:
    """Create a TCP client instance with default settings"""
    return TcpClient(host, port)


if __name__ == "__main__":
    # Simple test when run directly
    client = create_client()
    try:
        status = client.get_status()
        print(f"Server status: {status}")
    except TcpClientError as e:
        print(f"Error: {e}")
