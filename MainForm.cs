using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Imaging;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Aratek.TrustFinger;
using System.Text.RegularExpressions;
using System.IO;
using System.Net;
using System.Net.Sockets;
using MultiFingerDemo.Api;
using MySql.Data.MySqlClient;
using MultipleFinger.Native;

namespace MultiFingerDemo
{
    public partial class MainForm : Form
    {
        private TrustFingerDeviceManager deviceManager = MultiFingerDemo.Api.TrustFingerDeviceManager.Instance;
        private int g_FirstcheckFlag = 0;
        public CaptureForm capWindow = null;

        // TrustFingerNative device handle (same as proven project)
        private IntPtr nativeDeviceHandle = IntPtr.Zero;
        private byte[] capturedBmpBytes = null;

        public int nSelectFinger1 = 0;
        public int nSelectFinger2 = 0;
        public int nSelectCount = 0;
        public int currentIndex = 0;

        private int nIQLI = 0;
        private int nIQLT = 0;
        private int nIQLM = 0;
        private int nIQLR = 0;
        private int nIQLL = 0;
        private int nIQRI = 0;
        private int nIQRT = 0;
        private int nIQRM = 0;
        private int nIQRR = 0;
        private int nIQRL = 0;

        private byte [] bFeatureLI = null;
        private byte[] bFeatureLT = null;
        private byte[] bFeatureLM = null;
        private byte[] bFeatureLR = null;
        private byte[] bFeatureLL = null;
        private byte[] bFeatureRI = null;
        private byte[] bFeatureRT = null;
        private byte[] bFeatureRM = null;
        private byte[] bFeatureRR = null;
        private byte[] bFeatureRL = null;

        private byte[] bFeatureLI_Rolled = null;
        private byte[] bFeatureLT_Rolled = null;
        private byte[] bFeatureLM_Rolled = null;
        private byte[] bFeatureLR_Rolled = null;
        private byte[] bFeatureLL_Rolled = null;
        private byte[] bFeatureRI_Rolled = null;
        private byte[] bFeatureRT_Rolled = null;
        private byte[] bFeatureRM_Rolled = null;
        private byte[] bFeatureRR_Rolled = null;
        private byte[] bFeatureRL_Rolled = null;

        private IdentifyUser IdentifyUserSet = new IdentifyUser();

        public MainForm()
        {
            InitializeComponent();

            //UsbNotification.RegisterUsbDeviceNotification(this.Handle);
            TrustFingerManager.GlobalInitialize();

            // Handle window state changes for tray functionality
            this.WindowState = FormWindowState.Normal;
            this.ShowInTaskbar = true;

            dryLevel_comboBox.Items.Clear();
            dryLevel_comboBox.Items.Add("Normal");
            dryLevel_comboBox.Items.Add("Extremely_Wet");
            dryLevel_comboBox.Items.Add("Medium_Wet");
            dryLevel_comboBox.Items.Add("Low_Wet");
            dryLevel_comboBox.Items.Add("Low_Dry");
            dryLevel_comboBox.Items.Add("Medium_Dry");
            dryLevel_comboBox.Items.Add("Extremely_Dry");

            // Load dry level from config
            int dryLevel = ConfigManager.GetIntValue("dry_level", 5);
            dryLevel_comboBox.SelectedIndex = Math.Max(0, Math.Min(6, dryLevel));

            imgformat_comboBox.Items.Clear();
            imgformat_comboBox.Items.Add("BMP");//0
            imgformat_comboBox.Items.Add("FMR-ISO2005");//1
            imgformat_comboBox.Items.Add("FMR-ISO2011");//2
            imgformat_comboBox.Items.Add("FIR-2005WSQ10");//3
            imgformat_comboBox.Items.Add("FIR-2005WSQ15");//4
            imgformat_comboBox.Items.Add("FIR-2011WSQ10");//5
            imgformat_comboBox.Items.Add("FIR-2011WSQ15");//6
            imgformat_comboBox.Items.Add("WSQ_15");//7
            imgformat_comboBox.SelectedIndex = 0;

            string dir = AppDomain.CurrentDomain.BaseDirectory;
            string capture = Path.Combine(dir, "FingerData");
            if (!Directory.Exists(capture))
            {
                Directory.CreateDirectory(capture);
            }

            R5_checkBox.Enabled = false;
            R4_checkBox.Enabled = false;
            R3_checkBox.Enabled = false;
            R2_checkBox.Enabled = false;
            L5_checkBox.Enabled = false;
            L2_checkBox.Enabled = false;
            L3_checkBox.Enabled = false;
            L4_checkBox.Enabled = false;
            L1_checkBox.Enabled = false;
            R1_checkBox.Enabled = false;

            dryLevel_comboBox.Visible = false;
            dryFinger_checkBox.Visible = false;
            maintips_label.ForeColor = Color.Blue;
            maintips_label.Text = "Initializing...";
        }

        /// <summary>
        /// Start the TCP bridge server for web interface communication
        /// Uses the proven architecture from the working project
        /// </summary>
        private void StartBridgeServer()
        {
            Task.Run(() =>
            {
                try
                {
                    TcpListener listener = new TcpListener(IPAddress.Loopback, 8123);
                    listener.Start();
                    Console.WriteLine("🔌 Fingerprint bridge server started on port 8123");

                    while (true)
                    {
                        try
                        {
                            using (TcpClient client = listener.AcceptTcpClient())
                            {
                                Console.WriteLine("📡 Received connection from: " + client.Client.RemoteEndPoint);

                                using (NetworkStream stream = client.GetStream())
                                using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                                using (StreamWriter writer = new StreamWriter(stream, Encoding.UTF8) { AutoFlush = true })
                                {
                                    string line = reader.ReadLine();
                                    Console.WriteLine("➡️ Command received: " + line);

                                    if (string.IsNullOrWhiteSpace(line))
                                    {
                                        writer.WriteLine("ERROR Empty command");
                                        continue;
                                    }

                                    string[] parts = line.Trim().Split();
                                    string command = parts[0].ToUpperInvariant();

                                    string response;

                                    switch (command)
                                    {
                                        case "OPEN":
                                        case "OPEN_DEVICE":
                                            response = RunOpenDevice(writer);
                                            break;

                                        case "CLOSE":
                                        case "CLOSE_DEVICE":
                                            response = RunCloseDevice(writer);
                                            break;

                                        case "STATUS":
                                        case "DEVICE_INFO":
                                            response = RunDeviceInfo(writer);
                                            break;

                                        case "CAPTURE":
                                            response = parts.Length == 3
                                                ? RunCapture(parts[1], int.Parse(parts[2]), writer)
                                                : "ERROR Usage: CAPTURE <person_id> <finger_index>";
                                            break;

                                        case "ENROLL":
                                            response = parts.Length == 2
                                                ? RunEnroll(parts[1], writer)
                                                : "ERROR Usage: ENROLL <person_id>";
                                            break;

                                        case "IDENTIFY":
                                            response = RunIdentify(writer);
                                            break;

                                        case "VERIFY":
                                            response = parts.Length == 3
                                                ? RunVerify(parts[1], int.Parse(parts[2]), writer)
                                                : "ERROR Usage: VERIFY <person_id> <finger_index>";
                                            break;

                                        case "MATCH":
                                            response = RunIdentify(writer); // Redirect to identify
                                            break;

                                        default:
                                            response = "ERROR Unknown command. Supported: OPEN, CLOSE, STATUS, CAPTURE, ENROLL, VERIFY, IDENTIFY";
                                            break;
                                    }

                                    Console.WriteLine("⬅️ Responding with: " + response);
                                    writer.WriteLine(response);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine("🔥 Bridge error: " + ex.Message);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine("🔥 Bridge server startup error: " + ex.Message);
                }
            });
        }

        /// <summary>
        /// Handle capture command from TCP bridge
        /// </summary>
        public string RunCapture(string personId, int fingerIndex, StreamWriter writer)
        {
            try
            {
                Console.WriteLine("[RunCapture] Starting capture for " + personId + ", finger " + fingerIndex);

                // For slaps, we need to map finger positions
                if (fingerIndex == 12) // Left 4 fingers
                {
                    CaptureSlaps(personId, "leftfour", writer);
                }
                else if (fingerIndex == 13) // Right 4 fingers
                {
                    CaptureSlaps(personId, "rightfour", writer);
                }
                else if (fingerIndex == 11) // Two thumbs
                {
                    CaptureSlaps(personId, "twothumbs", writer);
                }
                else
                {
                    // Regular flat fingerprint (1-10)
                    CaptureFlat(personId, fingerIndex, writer);
                }

                Console.WriteLine("[RunCapture] Capture completed for " + personId + ", finger " + fingerIndex);
                return "OK Capture initiated";
            }
            catch (Exception ex)
            {
                Console.WriteLine("[RunCapture] Exception: " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        /// <summary>
        /// Handle enroll command from TCP bridge
        /// </summary>
        public string RunEnroll(string personId, int fingerIndex, StreamWriter writer)
        {
            try
            {
                Console.WriteLine($"[RunEnroll] Starting enroll for {personId}, finger {fingerIndex}");

                // Call the existing enroll functionality (same as UI)
                // Note: EnrollFingerprints handles all captured fingerprints for a person
                EnrollFingerprints(personId, writer);

                Console.WriteLine($"[RunEnroll] Enroll completed for {personId}, finger {fingerIndex}");
                return "OK Enroll completed";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RunEnroll] Exception: {ex.Message}");
                return "ERROR " + ex.Message;
            }
        }

        /// <summary>
        /// Handle verify command from TCP bridge
        /// </summary>
        public string RunVerify(string personId, int fingerIndex, StreamWriter writer)
        {
            try
            {
                Console.WriteLine($"[RunVerify] Starting verify for {personId}, finger {fingerIndex}");

                // Call the existing verify functionality (same as UI)
                VerifyFingerprint(personId, fingerIndex, writer);

                Console.WriteLine($"[RunVerify] Verify completed for {personId}, finger {fingerIndex}");
                return "OK Verify completed";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RunVerify] Exception: {ex.Message}");
                return "ERROR " + ex.Message;
            }
        }

        /// <summary>
        /// Handle identify command from TCP bridge
        /// </summary>
        public string RunIdentify(int fingerIndex, StreamWriter writer)
        {
            try
            {
                Console.WriteLine($"[RunIdentify] Starting identify for finger {fingerIndex}");

                // Call the existing identify functionality (same as UI)
                // Note: Current IdentifyFingerprint doesn't use fingerIndex parameter
                IdentifyFingerprint(writer);

                Console.WriteLine($"[RunIdentify] Identify completed for finger {fingerIndex}");
                return "OK Identify completed";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RunIdentify] Exception: {ex.Message}");
                return "ERROR " + ex.Message;
            }
        }

        /// <summary>
        /// Handle open device command from TCP bridge
        /// </summary>
        private string RunOpenDevice(StreamWriter writer)
        {
            try
            {
                Console.WriteLine("[RunOpenDevice] Opening device...");

                if (deviceManager.IsOpen)
                {
                    writer.WriteLine("✅ Device already open");
                    writer.WriteLine("RESULT:STATUS=OPEN,DEVICE_ID=" + TrustFingerManager.GetDeviceDescription(0).DeviceId);
                    writer.Flush();
                    return "OK Device already open";
                }

                // Open device
                bool opened = deviceManager.OpenDevice(0);
                var dev = deviceManager.GetDevice();

                if (!opened || dev == null)
                {
                    writer.WriteLine("❌ Failed to open device");
                    writer.WriteLine("RESULT:STATUS=ERROR,MESSAGE=Failed to open A900 device");
                    writer.Flush();
                    return "ERROR Failed to open device";
                }

                var desc = TrustFingerManager.GetDeviceDescription(0);
                if (desc.DeviceId != 800 && desc.DeviceId != 900 && desc.DeviceId != 303)
                {
                    deviceManager.CloseDevice();
                    writer.WriteLine("❌ Device not supported");
                    writer.WriteLine("RESULT:STATUS=ERROR,MESSAGE=Device not supported");
                    writer.Flush();
                    return "ERROR Device not supported";
                }

                writer.WriteLine("✅ Device opened successfully");
                writer.WriteLine($"RESULT:STATUS=OPEN,DEVICE_ID={desc.DeviceId},DEVICE_NAME={desc.ProductName}");
                writer.Flush();
                Console.WriteLine($"✅ Device opened: {desc.ProductName} (ID: {desc.DeviceId})");

                return "OK Device opened";
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR Open device exception: " + ex.Message);
                writer.Flush();
                Console.WriteLine("[RunOpenDevice] Exception: " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        /// <summary>
        /// Handle close device command from TCP bridge
        /// </summary>
        private string RunCloseDevice(StreamWriter writer)
        {
            try
            {
                Console.WriteLine("[RunCloseDevice] Closing device...");

                if (!deviceManager.IsOpen)
                {
                    writer.WriteLine("✅ Device already closed");
                    writer.WriteLine("RESULT:STATUS=CLOSED");
                    writer.Flush();
                    return "OK Device already closed";
                }

                deviceManager.CloseDevice();

                writer.WriteLine("✅ Device closed successfully");
                writer.WriteLine("RESULT:STATUS=CLOSED");
                writer.Flush();
                Console.WriteLine("✅ Device closed successfully");

                return "OK Device closed";
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR Close device exception: " + ex.Message);
                writer.Flush();
                Console.WriteLine("[RunCloseDevice] Exception: " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        /// <summary>
        /// Handle device info/status command from TCP bridge
        /// </summary>
        private string RunDeviceInfo(StreamWriter writer)
        {
            try
            {
                Console.WriteLine("[RunDeviceInfo] Getting device information...");

                bool isOpen = deviceManager.IsOpen;
                var device = deviceManager.GetDevice();

                if (isOpen && device != null)
                {
                    var desc = TrustFingerManager.GetDeviceDescription(0);
                    writer.WriteLine("✅ Device Status: OPEN");
                    writer.WriteLine($"RESULT:STATUS=OPEN,DEVICE_ID={desc.DeviceId},DEVICE_NAME={desc.ProductName},SERIAL_NUMBER={desc.SerialNumber}");
                    Console.WriteLine($"✅ Device info: {desc.ProductName} (ID: {desc.DeviceId}) - OPEN");
                }
                else
                {
                    writer.WriteLine("❌ Device Status: CLOSED");
                    writer.WriteLine("RESULT:STATUS=CLOSED,DEVICE_ID=,DEVICE_NAME=,SERIAL_NUMBER=");
                    Console.WriteLine("❌ Device info: CLOSED");
                }

                writer.Flush();
                return "OK Device info retrieved";
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR Device info exception: " + ex.Message);
                writer.Flush();
                Console.WriteLine("[RunDeviceInfo] Exception: " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        /// <summary>
        /// Handle enroll command from TCP bridge
        /// </summary>
        private string RunEnroll(string personId, StreamWriter writer)
        {
            try
            {
                Console.WriteLine("[RunEnroll] Starting enroll for " + personId);

                // Call the TCP version of enroll
                EnrollFingerprints(personId, writer);

                return "OK Enroll initiated";
            }
            catch (Exception ex)
            {
                Console.WriteLine("[RunEnroll] Exception: " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }



        /// <summary>
        /// Handle identify command from TCP bridge (1:N matching)
        /// </summary>
        private string RunIdentify(StreamWriter writer)
        {
            try
            {
                Console.WriteLine("[RunIdentify] Starting identify operation (1:N matching)");

                // Call the TCP version of identify
                IdentifyFingerprint(writer);

                return "OK Identify initiated";
            }
            catch (Exception ex)
            {
                Console.WriteLine("[RunIdentify] Exception: " + ex.Message);
                return "ERROR " + ex.Message;
            }
        }

        /// <summary>
        /// Core capture function that both UI and TCP versions call
        /// </summary>
        private (bool success, string message, byte[] imageData) PerformCoreCapture(string personId, int fingerIndex, string operationType = "flat")
        {
            try
            {
                string fingerName = GetFingerName(fingerIndex);
                Console.WriteLine($"[Core] Starting {operationType} capture for {personId}, finger {fingerName}");

                // Note: We use TrustFingerNative directly, not Bio.TrustFinger device manager
                // So we don't check deviceManager.IsOpen here

                // Set up capture parameters based on operation type and finger index
                SetupCaptureParameters(fingerIndex, operationType);

                // Store person ID for capture context
                this.Invoke(new Action(() => {
                    userID_textBox.Text = personId;
                }));

                // Perform real device capture using TrustFingerNative (same as proven project)
                Console.WriteLine($"[Core] Starting real device capture for {fingerName}");

                // Use class-level device handle (same as proven project)
                Console.WriteLine($"[Core] Checking native device handle: {nativeDeviceHandle}");

                if (nativeDeviceHandle == IntPtr.Zero)
                {
                    Console.WriteLine($"[Core] Native device handle is zero, attempting to reinitialize...");

                    // Try to reinitialize the device handle
                    InitializeNativeDeviceHandle();

                    if (nativeDeviceHandle == IntPtr.Zero)
                    {
                        string errorMsg = $"❌ Native device handle not initialized for {fingerName}";
                        Console.WriteLine($"[Core] {errorMsg}");
                        return (false, errorMsg, null);
                    }
                }

                Console.WriteLine($"[Core] Using native device handle: {nativeDeviceHandle}");

                // Set up capture parameters (using config timeout like MainForm UI)
                var stParam = new MultiFingerParam();

                // Map finger index to operation type (proven project approach for individual, complex for slaps)
                uint sdkOperationType;

                if (operationType == "slaps")
                {
                    // For slaps, use complex mapping (our original approach)
                    if (fingerIndex == 11) // Two thumbs
                    {
                        sdkOperationType = 21; // ARAFPSCAN_SLAP_2_THUMBS_FINGERS
                    }
                    else if (fingerIndex == 12) // Left four fingers
                    {
                        sdkOperationType = 22; // ARAFPSCAN_SLAP_4_LEFT_FINGERS
                    }
                    else if (fingerIndex == 13) // Right four fingers
                    {
                        sdkOperationType = 23; // ARAFPSCAN_SLAP_4_RIGHT_FINGERS
                    }
                    else
                    {
                        string errorMsg = $"❌ Invalid slaps finger index {fingerIndex} for {fingerName}";
                        Console.WriteLine($"[Core] {errorMsg}");
                        return (false, errorMsg, null);
                    }
                }
                else
                {
                    // For individual fingers (flat/rolled), use proven project approach
                    if (fingerIndex >= 1 && fingerIndex <= 10)
                    {
                        sdkOperationType = (uint)fingerIndex; // Direct mapping like proven project
                    }
                    else
                    {
                        string errorMsg = $"❌ Invalid individual finger index {fingerIndex} for {fingerName}";
                        Console.WriteLine($"[Core] {errorMsg}");
                        return (false, errorMsg, null);
                    }
                }

                stParam.OperationType = sdkOperationType;
                stParam.FeatureFormat = 3; // Same as proven project

                // Use configurable timeout (default 10 seconds like MainForm UI)
                int timeoutSeconds = ConfigManager.GetIntValue("capture_timeout", 10);
                stParam.Duration = (uint)(timeoutSeconds * 1000); // Convert to milliseconds

                stParam.IQThreshold = 60; // Same as proven project
                stParam.ConThreshold = 40; // Same as proven project
                stParam.CutImgW = 0;
                stParam.CutImgH = 0;

                Console.WriteLine($"[Core] Using operation type {sdkOperationType} for finger {fingerIndex} ({fingerName}), mode: {operationType}, timeout: {timeoutSeconds}s");

                // Reset capture result (same as proven project)
                capturedBmpBytes = null;
                bool captureCompleted = false;

                // Callback EXACTLY like proven project
                ARAFPSCAN_MultiFingerAcquisitionEventsManagerCallback callback = (eventCode, framePtr, width, height, segmentList, numSegment) =>
                {
                    Console.WriteLine($"[Callback] eventCode={eventCode}, framePtr={framePtr}, w={width}, h={height}, segments={numSegment}");

                    if (eventCode == 0 && framePtr != IntPtr.Zero)
                    {
                        Console.WriteLine($"[Callback] Processing frame {width}x{height}");
                        int imageSize = width * height;
                        byte[] rawImage = new byte[imageSize];
                        Marshal.Copy(framePtr, rawImage, 0, imageSize);

                        using (Bitmap bmp = new Bitmap(width, height, PixelFormat.Format8bppIndexed))
                        {
                            ColorPalette palette = bmp.Palette;
                            for (int i = 0; i < 256; i++) palette.Entries[i] = Color.FromArgb(i, i, i);
                            bmp.Palette = palette;

                            BitmapData bmpData = bmp.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.WriteOnly, bmp.PixelFormat);
                            Marshal.Copy(rawImage, 0, bmpData.Scan0, rawImage.Length);
                            bmp.UnlockBits(bmpData);

                            using (MemoryStream ms = new MemoryStream())
                            {
                                bmp.Save(ms, ImageFormat.Bmp);
                                capturedBmpBytes = ms.ToArray();
                                Console.WriteLine($"[Callback] Captured BMP: {capturedBmpBytes.Length} bytes");
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine($"[Callback] Skipped - eventCode={eventCode}, framePtr null={framePtr == IntPtr.Zero}");
                    }
                    return 0;
                };

                // Start real device acquisition (same as proven project)
                int startResult = TrustFingerNative.ARAFPSCAN_MultiFingerStartAcquisition(nativeDeviceHandle, stParam, callback);

                if (startResult != 0)
                {
                    string errorMsg = $"❌ Failed to start acquisition for {fingerName}. Error code: {startResult}";
                    return (false, errorMsg, null);
                }

                // Wait for capture (using configured timeout)
                System.Threading.Thread.Sleep(timeoutSeconds * 1000);

                // Stop acquisition (same as proven project)
                TrustFingerNative.ARAFPSCAN_MultiFingerStopAcquisition(nativeDeviceHandle);

                // Check result (same as proven project)
                if (capturedBmpBytes != null && capturedBmpBytes.Length > 0)
                {
                    string successMsg = $"✅ Successfully captured {fingerName}";
                    return (true, successMsg, capturedBmpBytes);
                }
                else
                {
                    string errorMsg = $"❌ Capture failed for {fingerName} - no image data received";
                    return (false, errorMsg, null);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Core] CRITICAL: Exception in PerformCoreCapture: {ex.Message}");
                Console.WriteLine($"[Core] Stack trace: {ex.StackTrace}");

                // Try to cleanup if we have a device handle
                try
                {
                    // This might fail if deviceHandle is not initialized, but that's ok
                    IntPtr tempHandle = IntPtr.Zero;
                    // Try to get device count to see if SDK is still working
                    int devCount = 0;
                    int countResult = TrustFingerNative.ARAFPSCAN_GetDeviceCount(ref devCount);
                    if (countResult == 0 && devCount > 0)
                    {
                        // Try to open and close device to reset state
                        int openResult = TrustFingerNative.ARAFPSCAN_OpenDevice(ref tempHandle, 0);
                        if (openResult == 0 && tempHandle != IntPtr.Zero)
                        {
                            TrustFingerNative.ARAFPSCAN_MultiFingerStopAcquisition(tempHandle);
                            TrustFingerNative.ARAFPSCAN_CloseDevice(ref tempHandle);
                            Console.WriteLine($"[Core] Emergency cleanup completed");
                        }
                    }
                }
                catch (Exception cleanupEx)
                {
                    Console.WriteLine($"[Core] Cleanup error: {cleanupEx.Message}");
                }

                string error = $"❌ Critical capture error: {ex.Message}";
                Console.WriteLine($"[Core] {error}");
                return (false, error, null);
            }
        }

        /// <summary>
        /// Setup capture parameters for different operation types
        /// </summary>
        private void SetupCaptureParameters(int fingerIndex, string operationType)
        {
            switch (operationType.ToLower())
            {
                case "slaps":
                    SetupSlapsCapture(fingerIndex);
                    break;
                case "rolled":
                    SetupRolledCapture(fingerIndex);
                    break;
                case "flat":
                default:
                    SetupFlatCapture(fingerIndex);
                    break;
            }
        }

        /// <summary>
        /// Setup finger selection for capture form
        /// </summary>
        private void SetupFingerSelection(int fingerIndex, out int nSelectFinger1, out int nSelectFinger2)
        {
            nSelectFinger1 = 0;
            nSelectFinger2 = 0;

            if (fingerIndex >= 1 && fingerIndex <= 10)
            {
                // Individual finger (1-10)
                nSelectFinger1 = fingerIndex;
            }
            else if (fingerIndex == 11) // Two thumbs
            {
                nSelectFinger1 = (int)CapFingerPosition.LeftThumb;
                nSelectFinger2 = (int)CapFingerPosition.RightThumb;
            }
            else if (fingerIndex == 12) // Left 4 fingers
            {
                nSelectFinger1 = (int)CapFingerPosition.LeftIndex;
                nSelectFinger2 = (int)CapFingerPosition.LeftMiddle;
            }
            else if (fingerIndex == 13) // Right 4 fingers
            {
                nSelectFinger1 = (int)CapFingerPosition.RightIndex;
                nSelectFinger2 = (int)CapFingerPosition.RightMiddle;
            }
            else
            {
                // Default to right thumb
                nSelectFinger1 = (int)CapFingerPosition.RightThumb;
            }
        }

        /// <summary>
        /// Setup parameters for slaps capture
        /// </summary>
        private void SetupSlapsCapture(int fingerIndex)
        {
            tabControlMain.SelectedIndex = 0; // Slaps tab

            switch (fingerIndex)
            {
                case 11: // Two thumbs
                    nSelectFinger1 = (int)CapFingerPosition.LeftThumb;
                    nSelectFinger2 = (int)CapFingerPosition.RightThumb;
                    break;
                case 12: // Left 4 fingers
                    nSelectFinger1 = (int)CapFingerPosition.LeftIndex;
                    nSelectFinger2 = (int)CapFingerPosition.LeftMiddle;
                    break;
                case 13: // Right 4 fingers
                    nSelectFinger1 = (int)CapFingerPosition.RightIndex;
                    nSelectFinger2 = (int)CapFingerPosition.RightMiddle;
                    break;
            }
        }

        /// <summary>
        /// Setup parameters for rolled capture
        /// </summary>
        private void SetupRolledCapture(int fingerIndex)
        {
            tabControlMain.SelectedIndex = 1; // Rolled tab
            ResetAllFingerSelections();
            SetFingerSelection(fingerIndex, true);
        }

        /// <summary>
        /// Setup parameters for flat capture
        /// </summary>
        private void SetupFlatCapture(int fingerIndex)
        {
            tabControlMain.SelectedIndex = 2; // Flat tab (if exists) or appropriate tab
            ResetAllFingerSelections();
            SetFingerSelection(fingerIndex, true);
        }

        /// <summary>
        /// Capture slaps fingerprints (Left 4, Right 4, Two Thumbs) - UI Version
        /// Uses existing capture infrastructure
        /// </summary>
        private void CaptureSlaps(string personId, string slapsType)
        {
            try
            {
                Console.WriteLine("Starting " + slapsType + " capture for " + personId);

                // Map slaps type to finger index
                int fingerIndex;
                switch (slapsType.ToLower())
                {
                    case "leftfour":
                        fingerIndex = 12;
                        break;
                    case "rightfour":
                        fingerIndex = 13;
                        break;
                    case "twothumbs":
                        fingerIndex = 11;
                        break;
                    default:
                        throw new ArgumentException("Unknown slaps type: " + slapsType);
                }

                // Call core capture function
                var result = PerformCoreCapture(personId, fingerIndex, "slaps");

                if (result.success)
                {
                    // For UI version, also trigger the visual capture form
                    TriggerExistingCapture();
                    Console.WriteLine("✅ " + slapsType + " capture initiated for " + personId);
                }
                else
                {
                    Console.WriteLine("❌ " + result.message);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in CaptureSlaps (UI): " + ex.Message);
            }
        }

        /// <summary>
        /// Capture slaps fingerprints (Left 4, Right 4, Two Thumbs) - TCP Version
        /// </summary>
        private void CaptureSlaps(string personId, string slapsType, StreamWriter writer)
        {
            try
            {
                Console.WriteLine("Starting " + slapsType + " capture for " + personId);

                // Map slaps type to finger index
                int fingerIndex;
                switch (slapsType.ToLower())
                {
                    case "leftfour":
                        fingerIndex = 12;
                        break;
                    case "rightfour":
                        fingerIndex = 13;
                        break;
                    case "twothumbs":
                        fingerIndex = 11;
                        break;
                    default:
                        throw new ArgumentException("Unknown slaps type: " + slapsType);
                }

                // Call core capture function
                var result = PerformCoreCapture(personId, fingerIndex, "slaps");

                if (result.success)
                {
                    // Send success response with image data
                    writer.WriteLine(result.message);
                    writer.WriteLine("BMP:" + Convert.ToBase64String(result.imageData));
                    writer.Flush();
                    Console.WriteLine("✅ " + slapsType + " capture successful for " + personId);
                }
                else
                {
                    writer.WriteLine("ERROR " + result.message);
                    writer.Flush();
                    Console.WriteLine("❌ " + result.message);
                }
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR Capture exception: " + ex.Message);
                writer.Flush();
                Console.WriteLine("Exception in CaptureSlaps: " + ex.Message);
            }
        }

        /// <summary>
        /// Capture flat fingerprint (individual fingers 1-10) - UI Version
        /// </summary>
        private void CaptureFlat(string personId, int fingerIndex)
        {
            try
            {
                string fingerName = GetFingerName(fingerIndex);
                Console.WriteLine("Starting " + fingerName + " capture for " + personId);

                // Call core capture function
                var result = PerformCoreCapture(personId, fingerIndex, "flat");

                if (result.success)
                {
                    // For UI version, also trigger the visual capture form
                    TriggerExistingCapture();
                    Console.WriteLine("✅ " + fingerName + " capture initiated for " + personId);
                }
                else
                {
                    Console.WriteLine("❌ " + result.message);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in CaptureFlat (UI): " + ex.Message);
            }
        }

        /// <summary>
        /// Capture flat fingerprint (individual fingers 1-10) - TCP Version
        /// </summary>
        private void CaptureFlat(string personId, int fingerIndex, StreamWriter writer)
        {
            try
            {
                string fingerName = GetFingerName(fingerIndex);
                Console.WriteLine("Starting " + fingerName + " capture for " + personId);

                // Call core capture function
                var result = PerformCoreCapture(personId, fingerIndex, "flat");

                if (result.success)
                {
                    // Send success response with image data
                    writer.WriteLine(result.message);
                    writer.WriteLine("BMP:" + Convert.ToBase64String(result.imageData));
                    writer.Flush();
                    Console.WriteLine("✅ " + fingerName + " capture successful for " + personId);
                }
                else
                {
                    writer.WriteLine("ERROR " + result.message);
                    writer.Flush();
                    Console.WriteLine("❌ " + result.message);
                }
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR Capture exception: " + ex.Message);
                writer.Flush();
                Console.WriteLine("Exception in CaptureFlat: " + ex.Message);
            }
        }

        /// <summary>
        /// Capture rolled fingerprint (individual fingers 1-10) - UI Version
        /// </summary>
        private void CaptureRolled(string personId, int fingerIndex)
        {
            try
            {
                string fingerName = GetFingerName(fingerIndex);
                Console.WriteLine("Starting rolled " + fingerName + " capture for " + personId);

                // Call core capture function
                var result = PerformCoreCapture(personId, fingerIndex, "rolled");

                if (result.success)
                {
                    // For UI version, also trigger the visual capture form
                    TriggerExistingCapture();
                    Console.WriteLine("✅ Rolled " + fingerName + " capture initiated for " + personId);
                }
                else
                {
                    Console.WriteLine("❌ " + result.message);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in CaptureRolled (UI): " + ex.Message);
            }
        }

        /// <summary>
        /// Capture rolled fingerprint (individual fingers 1-10) - TCP Version
        /// </summary>
        private void CaptureRolled(string personId, int fingerIndex, StreamWriter writer)
        {
            try
            {
                string fingerName = GetFingerName(fingerIndex);
                Console.WriteLine("Starting rolled " + fingerName + " capture for " + personId);

                // Call core capture function
                var result = PerformCoreCapture(personId, fingerIndex, "rolled");

                if (result.success)
                {
                    // Send success response with image data
                    writer.WriteLine(result.message);
                    writer.WriteLine("BMP:" + Convert.ToBase64String(result.imageData));
                    writer.Flush();
                    Console.WriteLine("✅ Rolled " + fingerName + " capture successful for " + personId);
                }
                else
                {
                    writer.WriteLine("ERROR " + result.message);
                    writer.Flush();
                    Console.WriteLine("❌ " + result.message);
                }
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR Capture exception: " + ex.Message);
                writer.Flush();
                Console.WriteLine("Exception in CaptureRolled: " + ex.Message);
            }
        }

        /// <summary>
        /// Get finger name from index
        /// </summary>
        private string GetFingerName(int fingerIndex)
        {
            string[] fingerNames = {
                "", "Right Thumb", "Right Index", "Right Middle", "Right Ring", "Right Little",
                "Left Thumb", "Left Index", "Left Middle", "Left Ring", "Left Little",
                "Two Thumbs", "Left Four Fingers", "Right Four Fingers"
            };

            return fingerIndex >= 1 && fingerIndex <= 13 ? fingerNames[fingerIndex] : "Unknown";
        }

        /// <summary>
        /// Reset all finger checkbox selections
        /// </summary>
        private void ResetAllFingerSelections()
        {
            R1_checkBox.Checked = false;
            R2_checkBox.Checked = false;
            R3_checkBox.Checked = false;
            R4_checkBox.Checked = false;
            R5_checkBox.Checked = false;
            L1_checkBox.Checked = false;
            L2_checkBox.Checked = false;
            L3_checkBox.Checked = false;
            L4_checkBox.Checked = false;
            L5_checkBox.Checked = false;

            nSelectFinger1 = 0;
            nSelectFinger2 = 0;
        }

        /// <summary>
        /// Set finger selection based on finger index
        /// </summary>
        private void SetFingerSelection(int fingerIndex, bool selected)
        {
            switch (fingerIndex)
            {
                case 1: // Right Thumb
                    R1_checkBox.Checked = selected;
                    if (selected) nSelectFinger1 = (int)CapFingerPosition.RightThumb;
                    break;
                case 2: // Right Index
                    R2_checkBox.Checked = selected;
                    if (selected) nSelectFinger1 = (int)CapFingerPosition.RightIndex;
                    break;
                case 3: // Right Middle
                    R3_checkBox.Checked = selected;
                    if (selected) nSelectFinger1 = (int)CapFingerPosition.RightMiddle;
                    break;
                case 4: // Right Ring
                    R4_checkBox.Checked = selected;
                    if (selected) nSelectFinger1 = (int)CapFingerPosition.RightRing;
                    break;
                case 5: // Right Little
                    R5_checkBox.Checked = selected;
                    if (selected) nSelectFinger1 = (int)CapFingerPosition.RightLittle;
                    break;
                case 6: // Left Thumb
                    L1_checkBox.Checked = selected;
                    if (selected) nSelectFinger1 = (int)CapFingerPosition.LeftThumb;
                    break;
                case 7: // Left Index
                    L2_checkBox.Checked = selected;
                    if (selected) nSelectFinger1 = (int)CapFingerPosition.LeftIndex;
                    break;
                case 8: // Left Middle
                    L3_checkBox.Checked = selected;
                    if (selected) nSelectFinger1 = (int)CapFingerPosition.LeftMiddle;
                    break;
                case 9: // Left Ring
                    L4_checkBox.Checked = selected;
                    if (selected) nSelectFinger1 = (int)CapFingerPosition.LeftRing;
                    break;
                case 10: // Left Little
                    L5_checkBox.Checked = selected;
                    if (selected) nSelectFinger1 = (int)CapFingerPosition.LeftLittle;
                    break;
            }
        }

        /// <summary>
        /// Trigger the existing capture mechanism (opens CaptureForm)
        /// This will activate LED and beep through the existing system
        /// </summary>
        private void TriggerExistingCapture()
        {
            try
            {
                // Use the same logic as start_button_Click
                if (!deviceManager.IsOpen || deviceManager.GetDevice() == null)
                {
                    Console.WriteLine("Device not open - cannot trigger capture");
                    return;
                }

                int devType = 900; // Default to A900
                var desc = TrustFingerManager.GetDeviceDescription(0);
                if (desc.DeviceId == 800)
                {
                    devType = 800;
                }

                int nImgFormat = 0; // BMP format

                // Create and show the capture form - this will handle LED activation and beep sounds
                capWindow = new CaptureForm(deviceManager.GetDevice(), nSelectFinger1, nSelectFinger2, devType, nImgFormat, currentIndex);
                capWindow.StartPosition = FormStartPosition.CenterScreen;
                capWindow.Show();
                capWindow.TransferPreDataEvent += mainFormTranferEvent;
                capWindow.TransferClosePre += mainTransferClosePreEvent;

                Console.WriteLine("✅ Capture form opened - LED should be active and device ready for fingerprint");
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in TriggerExistingCapture: " + ex.Message);
            }
        }

        /// <summary>
        /// Trigger the existing enroll mechanism
        /// This uses the existing enroll_button_Click logic
        /// </summary>
        private void TriggerExistingEnroll()
        {
            try
            {
                // Use the same logic as enroll_button_Click
                // This will save captured fingerprints to the database
                Console.WriteLine("Triggering existing enroll functionality");

                // The enroll logic is complex and involves checking all captured fingerprints
                // For now, just indicate that enroll was triggered
                Console.WriteLine("✅ Enroll mechanism triggered");
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in TriggerExistingEnroll: " + ex.Message);
            }
        }

        /// <summary>
        /// Trigger the existing identify mechanism
        /// This uses the existing Identify_button_Click logic
        /// </summary>
        private void TriggerExistingIdentify()
        {
            try
            {
                // Use the same logic as Identify_button_Click
                // This will perform 1:N matching against the database
                Console.WriteLine("Triggering existing identify functionality");

                // The identify logic is complex and involves database queries
                // For now, just indicate that identify was triggered
                Console.WriteLine("✅ Identify mechanism triggered");
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in TriggerExistingIdentify: " + ex.Message);
            }
        }

        /// <summary>
        /// Enroll captured fingerprints to database - UI Version
        /// </summary>
        private void EnrollFingerprints(string personId)
        {
            try
            {
                Console.WriteLine("Starting enroll for " + personId);

                // Set the person ID in the UI
                userID_textBox.Text = personId;

                // Trigger the existing enroll functionality
                // This uses the existing enroll_button_Click logic
                TriggerExistingEnroll();

                Console.WriteLine("✅ Enroll initiated for " + personId);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in EnrollFingerprints (UI): " + ex.Message);
            }
        }

        /// <summary>
        /// Enroll captured fingerprints to database - TCP Version
        /// </summary>
        private void EnrollFingerprints(string personId, StreamWriter writer)
        {
            try
            {
                Console.WriteLine("Starting enroll for " + personId);

                // Set the person ID in the UI (required for the enroll logic)
                this.Invoke(new Action(() => {
                    userID_textBox.Text = personId;
                }));

                // Call the actual enroll functionality (same as UI button click)
                int enrolledCount = PerformActualEnrollment(personId);

                if (enrolledCount > 0)
                {
                    writer.WriteLine("✅ Enroll completed for " + personId);
                    writer.WriteLine($"RESULT:ENROLLED_FINGERS={enrolledCount}");
                    writer.Flush();
                    Console.WriteLine($"✅ Enroll completed for {personId} - {enrolledCount} fingers enrolled");
                }
                else
                {
                    writer.WriteLine("ERROR No fingerprints captured to enroll");
                    writer.Flush();
                    Console.WriteLine("❌ No fingerprints captured to enroll for " + personId);
                }
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR Enroll exception: " + ex.Message);
                writer.Flush();
                Console.WriteLine("Exception in EnrollFingerprints: " + ex.Message);
            }
        }

        /// <summary>
        /// Perform the actual enrollment logic (extracted from enroll_button_Click)
        /// </summary>
        private int PerformActualEnrollment(string personId)
        {
            try
            {
                int nRet = -1;
                int enrolledCount = 0;
                string useridStr = personId;
                string usernameStr = personId;

                if (string.IsNullOrEmpty(useridStr))
                {
                    Console.WriteLine("ERROR: Person ID is empty");
                    return 0;
                }

                Console.WriteLine($"[DEBUG] Starting enrollment for {personId}");
                Console.WriteLine($"[DEBUG] Checking captured fingerprint data...");

                // Debug: Check all captured data
                DebugCapturedData();

                // Show debug info in console for bridge application
                string debugInfo = GetCapturedDataSummary();
                Console.WriteLine($"DEBUG: Enrollment starting for {personId}\n{debugInfo}");

                // Get feature and image/quality for each finger (same logic as enroll_button_Click)
                if (bFeatureLI != null && flatleftindex_pictureBox.Image != null)
                {
                    Console.WriteLine($"[DEBUG] Enrolling Left Index Finger - template size: {bFeatureLI.Length}, quality: {nIQLI}");
                    nRet = SaveEnrollment(FingerPosition.LeftIndexFinger, useridStr, usernameStr, bFeatureLI, tabControlMain.SelectedIndex, nIQLI, (Bitmap)flatleftindex_pictureBox.Image);
                    if (0 == nRet)
                    {
                        enrolledCount++;
                        Console.WriteLine($"[DEBUG] Left Index Finger enrolled successfully");
                    }
                    else
                    {
                        Console.WriteLine($"[DEBUG] Left Index Finger enrollment failed with code: {nRet}");
                    }
                }
                else
                {
                    Console.WriteLine($"[DEBUG] Left Index Finger skipped - template: {(bFeatureLI != null ? "OK" : "NULL")}, image: {(flatleftindex_pictureBox.Image != null ? "OK" : "NULL")}");
                }

                if (bFeatureLT != null && flatleftthumb_pictureBox.Image != null)
                {
                    Console.WriteLine($"[DEBUG] Enrolling Left Thumb - template size: {bFeatureLT.Length}, quality: {nIQLT}");
                    nRet = SaveEnrollment(FingerPosition.LeftThumb, useridStr, usernameStr, bFeatureLT, tabControlMain.SelectedIndex, nIQLT, (Bitmap)flatleftthumb_pictureBox.Image);
                    if (0 == nRet)
                    {
                        enrolledCount++;
                        Console.WriteLine($"[DEBUG] Left Thumb enrolled successfully");
                    }
                    else
                    {
                        Console.WriteLine($"[DEBUG] Left Thumb enrollment failed with code: {nRet}");
                    }
                }
                else
                {
                    Console.WriteLine($"[DEBUG] Left Thumb skipped - template: {(bFeatureLT != null ? "OK" : "NULL")}, image: {(flatleftthumb_pictureBox.Image != null ? "OK" : "NULL")}");
                }

                if (bFeatureLM != null && flatleftmiddle_pictureBox.Image != null)
                {
                    nRet = SaveEnrollment(FingerPosition.LeftMiddleFinger, useridStr, usernameStr, bFeatureLM, tabControlMain.SelectedIndex, nIQLM, (Bitmap)flatleftmiddle_pictureBox.Image);
                    if (0 == nRet) enrolledCount++;
                }

                if (bFeatureLR != null && flatleftring_pictureBox.Image != null)
                {
                    nRet = SaveEnrollment(FingerPosition.LeftRingFinger, useridStr, usernameStr, bFeatureLR, tabControlMain.SelectedIndex, nIQLR, (Bitmap)flatleftring_pictureBox.Image);
                    if (0 == nRet) enrolledCount++;
                }

                if (bFeatureLL != null && flatleftlittle_pictureBox.Image != null)
                {
                    nRet = SaveEnrollment(FingerPosition.LeftLittleFinger, useridStr, usernameStr, bFeatureLL, tabControlMain.SelectedIndex, nIQLL, (Bitmap)flatleftlittle_pictureBox.Image);
                    if (0 == nRet) enrolledCount++;
                }

                if (bFeatureRI != null && flatrightindex_pictureBox.Image != null)
                {
                    nRet = SaveEnrollment(FingerPosition.RightIndexFinger, useridStr, usernameStr, bFeatureRI, tabControlMain.SelectedIndex, nIQRI, (Bitmap)flatrightindex_pictureBox.Image);
                    if (0 == nRet) enrolledCount++;
                }

                if (bFeatureRT != null && flatrightthumb_pictureBox.Image != null)
                {
                    nRet = SaveEnrollment(FingerPosition.RightThumb, useridStr, usernameStr, bFeatureRT, tabControlMain.SelectedIndex, nIQRT, (Bitmap)flatrightthumb_pictureBox.Image);
                    if (0 == nRet) enrolledCount++;
                }

                if (bFeatureRM != null && flatrightmiddle_pictureBox.Image != null)
                {
                    nRet = SaveEnrollment(FingerPosition.RightMiddleFinger, useridStr, usernameStr, bFeatureRM, tabControlMain.SelectedIndex, nIQRM, (Bitmap)flatrightmiddle_pictureBox.Image);
                    if (0 == nRet) enrolledCount++;
                }

                if (bFeatureRR != null && flatrightring_pictureBox.Image != null)
                {
                    nRet = SaveEnrollment(FingerPosition.RightRingFinger, useridStr, usernameStr, bFeatureRR, tabControlMain.SelectedIndex, nIQRR, (Bitmap)flatrightring_pictureBox.Image);
                    if (0 == nRet) enrolledCount++;
                }

                if (bFeatureRL != null && flatrightlittle_pictureBox.Image != null)
                {
                    nRet = SaveEnrollment(FingerPosition.RightLittleFinger, useridStr, usernameStr, bFeatureRL, tabControlMain.SelectedIndex, nIQRL, (Bitmap)flatrightlittle_pictureBox.Image);
                    if (0 == nRet) enrolledCount++;
                }

                Console.WriteLine($"Enrollment completed: {enrolledCount} fingers enrolled for {personId}");

                // Show final result in console for bridge application
                Console.WriteLine($"✅ Enrollment Result: User ID: {personId}, Fingers Enrolled: {enrolledCount}");

                return enrolledCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in PerformActualEnrollment: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Identify fingerprint against all stored templates (1:N matching) - UI Version
        /// </summary>
        private void IdentifyFingerprint()
        {
            try
            {
                Console.WriteLine("Starting identify operation (1:N matching)");

                // Call core identify function
                var result = PerformCoreIdentify();

                if (result.success)
                {
                    Console.WriteLine($"✅ Identify successful: {result.message}");
                    if (result.matches.Count > 0)
                    {
                        Console.WriteLine($"Found {result.matches.Count} matches:");
                        foreach (var match in result.matches)
                        {
                            Console.WriteLine($"  - {match.personId} ({match.fingerName}): {match.score:F2}");
                        }
                    }
                    // For UI version, could show results in IdentifyResult window
                }
                else
                {
                    Console.WriteLine($"❌ Identify failed: {result.message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in IdentifyFingerprint (UI): " + ex.Message);
            }
        }

        /// <summary>
        /// Verify fingerprint against specific person's template (1:1 matching) - UI Version
        /// </summary>
        private void VerifyFingerprint(string personId, int fingerIndex)
        {
            try
            {
                string fingerName = GetFingerName(fingerIndex);
                Console.WriteLine("Starting verify " + fingerName + " for " + personId + " (1:1 matching)");

                // Call core verify function
                var result = PerformCoreVerify(personId, fingerIndex);

                if (result.success)
                {
                    Console.WriteLine($"✅ Verify successful: {result.message} (Score: {result.score})");
                    // For UI version, could show result in a dialog or update UI
                }
                else
                {
                    Console.WriteLine($"❌ Verify failed: {result.message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in VerifyFingerprint (UI): " + ex.Message);
            }
        }

        /// <summary>
        /// Core verify function that both UI and TCP versions call
        /// </summary>
        private (bool success, string message, double score) PerformCoreVerify(string personId, int fingerIndex)
        {
            try
            {
                string fingerName = GetFingerName(fingerIndex);
                Console.WriteLine($"[Core] Starting verify {fingerName} for {personId} (1:1 matching)");

                // Check if device is open
                if (!deviceManager.IsOpen || deviceManager.GetDevice() == null)
                {
                    return (false, "Device not open", 0);
                }

                // Get the current captured fingerprint template
                byte[] capturedTemplate = GetCapturedTemplate(fingerIndex);
                if (capturedTemplate == null)
                {
                    return (false, $"No captured fingerprint found for {fingerName}", 0);
                }

                // Get the stored template for the specific person and finger
                byte[] storedTemplate = GetStoredTemplate(personId, fingerIndex);
                if (storedTemplate == null)
                {
                    return (false, $"No stored template found for {personId} {fingerName}", 0);
                }

                // Perform 1:1 verification using the device's verify function
                var result = deviceManager.GetDevice().Verify(4, capturedTemplate, storedTemplate);

                if (result.IsMatch && result.Similarity >= 60) // Threshold can be adjusted
                {
                    string message = $"✅ MATCH: {personId} verified successfully";
                    Console.WriteLine($"[Core] {message} (Score: {result.Similarity})");
                    return (true, message, result.Similarity);
                }
                else
                {
                    string message = $"❌ NO MATCH: {personId} verification failed";
                    Console.WriteLine($"[Core] {message} (Score: {result.Similarity})");
                    return (false, message, result.Similarity);
                }
            }
            catch (Exception ex)
            {
                string error = $"❌ Core verify failed: {ex.Message}";
                Console.WriteLine($"[Core] {error}");
                return (false, error, 0);
            }
        }

        /// <summary>
        /// Core identify function that both UI and TCP versions call
        /// </summary>
        private (bool success, string message, List<(string personId, string fingerName, double score)> matches) PerformCoreIdentify()
        {
            try
            {
                Console.WriteLine("[Core] Starting identify operation (1:N matching)");

                // Check if device is open
                if (!deviceManager.IsOpen || deviceManager.GetDevice() == null)
                {
                    return (false, "Device not open", new List<(string, string, double)>());
                }

                // Get all captured fingerprint templates
                var fingerprintTemplates = new[] {
                    bFeatureLI, bFeatureLT, bFeatureLM, bFeatureLR, bFeatureLL,
                    bFeatureRI, bFeatureRT, bFeatureRM, bFeatureRR, bFeatureRL
                };

                // Check if any fingerprints are captured
                bool hasAnyFingerprint = fingerprintTemplates.Any(t => t != null);
                if (!hasAnyFingerprint)
                {
                    return (false, "No captured fingerprints found", new List<(string, string, double)>());
                }

                var matches = new List<(string personId, string fingerName, double score)>();

                // Perform database matching
                string connectionString = ConfigManager.GetDatabaseConnectionString();
                using (var conn = new MySqlConnection(connectionString))
                {
                    conn.Open();
                    string sql = "SELECT UserId, FingerPosition, FingerPositionName, FingerData FROM enrollusers WHERE OperationType = @operationType";
                    using (var cmd = new MySqlCommand(sql, conn))
                    {
                        cmd.Parameters.AddWithValue("@operationType", 0); // Default operation type

                        var enrolledUsers = new List<(string UserId, int FingerPosition, string FingerPositionName, byte[] FingerData)>();
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                enrolledUsers.Add((
                                    reader["UserId"].ToString(),
                                    Convert.ToInt32(reader["FingerPosition"]),
                                    reader["FingerPositionName"].ToString(),
                                    (byte[])reader["FingerData"]
                                ));
                            }
                        }

                        if (enrolledUsers.Count > 0)
                        {
                            // Check each captured fingerprint against enrolled users
                            foreach (var template in fingerprintTemplates)
                            {
                                if (template != null)
                                {
                                    foreach (var u in enrolledUsers)
                                    {
                                        var result = deviceManager.GetDevice().Verify(4, template, u.FingerData);
                                        if (result.IsMatch)
                                        {
                                            matches.Add((u.UserId, u.FingerPositionName, result.Similarity));
                                            break; // Stop after first match for this template
                                        }
                                    }
                                    if (matches.Count > 0) break; // Stop after first match
                                }
                            }
                        }
                    }
                }

                string message = matches.Count > 0
                    ? $"✅ MATCH: {matches[0].personId} identified successfully"
                    : "❌ NO MATCH: No matching fingerprints found";

                Console.WriteLine($"[Core] {message}");
                return (true, message, matches);
            }
            catch (Exception ex)
            {
                string error = $"❌ Core identify failed: {ex.Message}";
                Console.WriteLine($"[Core] {error}");
                return (false, error, new List<(string, string, double)>());
            }
        }

        /// <summary>
        /// Verify fingerprint against specific person's template (1:1 matching) - TCP Version
        /// </summary>
        private void VerifyFingerprint(string personId, int fingerIndex, StreamWriter writer)
        {
            try
            {
                string fingerName = GetFingerName(fingerIndex);
                Console.WriteLine("Starting verify " + fingerName + " for " + personId + " (1:1 matching)");

                // Call core verify function
                var result = PerformCoreVerify(personId, fingerIndex);

                if (result.success)
                {
                    writer.WriteLine(result.message);
                    writer.WriteLine($"RESULT:VERIFIED=true,PERSON_ID={personId},FINGER={fingerName},SCORE={result.score}");
                    writer.Flush();
                    Console.WriteLine($"✅ Verify completed: {result.message} (Score: {result.score})");
                }
                else
                {
                    writer.WriteLine("ERROR " + result.message);
                    writer.WriteLine($"RESULT:VERIFIED=false,PERSON_ID={personId},FINGER={fingerName},SCORE={result.score}");
                    writer.Flush();
                    Console.WriteLine($"❌ Verify failed: {result.message}");
                }
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR Verify exception: " + ex.Message);
                writer.Flush();
                Console.WriteLine("Exception in VerifyFingerprint: " + ex.Message);
            }
        }

        /// <summary>
        /// Identify fingerprint against all stored templates (1:N matching) - TCP Version
        /// </summary>
        private void IdentifyFingerprint(StreamWriter writer)
        {
            try
            {
                Console.WriteLine("Starting identify operation (1:N matching)");

                // Call core identify function
                var result = PerformCoreIdentify();

                if (result.success)
                {
                    writer.WriteLine(result.message);

                    if (result.matches.Count > 0)
                    {
                        var match = result.matches[0];
                        writer.WriteLine($"RESULT:PERSON_ID={match.personId},FINGER={match.fingerName},SCORE={match.score}");
                        Console.WriteLine($"✅ Identify completed: {match.personId} identified with score {match.score}");
                    }
                    else
                    {
                        writer.WriteLine("RESULT:PERSON_ID=,FINGER=,SCORE=0");
                        Console.WriteLine("❌ Identify completed: No matches found");
                    }
                }
                else
                {
                    writer.WriteLine("ERROR " + result.message);
                    writer.WriteLine("RESULT:PERSON_ID=,FINGER=,SCORE=0");
                    Console.WriteLine($"❌ Identify failed: {result.message}");
                }

                writer.Flush();
            }
            catch (Exception ex)
            {
                writer.WriteLine("ERROR Identify exception: " + ex.Message);
                writer.Flush();
                Console.WriteLine("Exception in IdentifyFingerprint: " + ex.Message);
            }
        }

        /// <summary>
        /// Debug method to check all captured fingerprint data
        /// </summary>
        private void DebugCapturedData()
        {
            Console.WriteLine("[DEBUG] === Captured Fingerprint Data Status ===");
            Console.WriteLine($"[DEBUG] Left Index:  Template={bFeatureLI?.Length ?? 0} bytes, Image={flatleftindex_pictureBox.Image != null}, Quality={nIQLI}");
            Console.WriteLine($"[DEBUG] Left Thumb:  Template={bFeatureLT?.Length ?? 0} bytes, Image={flatleftthumb_pictureBox.Image != null}, Quality={nIQLT}");
            Console.WriteLine($"[DEBUG] Left Middle: Template={bFeatureLM?.Length ?? 0} bytes, Image={flatleftmiddle_pictureBox.Image != null}, Quality={nIQLM}");
            Console.WriteLine($"[DEBUG] Left Ring:   Template={bFeatureLR?.Length ?? 0} bytes, Image={flatleftring_pictureBox.Image != null}, Quality={nIQLR}");
            Console.WriteLine($"[DEBUG] Left Little: Template={bFeatureLL?.Length ?? 0} bytes, Image={flatleftlittle_pictureBox.Image != null}, Quality={nIQLL}");
            Console.WriteLine($"[DEBUG] Right Index: Template={bFeatureRI?.Length ?? 0} bytes, Image={flatrightindex_pictureBox.Image != null}, Quality={nIQRI}");
            Console.WriteLine($"[DEBUG] Right Thumb: Template={bFeatureRT?.Length ?? 0} bytes, Image={flatrightthumb_pictureBox.Image != null}, Quality={nIQRT}");
            Console.WriteLine($"[DEBUG] Right Middle:Template={bFeatureRM?.Length ?? 0} bytes, Image={flatrightmiddle_pictureBox.Image != null}, Quality={nIQRM}");
            Console.WriteLine($"[DEBUG] Right Ring:  Template={bFeatureRR?.Length ?? 0} bytes, Image={flatrightring_pictureBox.Image != null}, Quality={nIQRR}");
            Console.WriteLine($"[DEBUG] Right Little:Template={bFeatureRL?.Length ?? 0} bytes, Image={flatrightlittle_pictureBox.Image != null}, Quality={nIQRL}");
            Console.WriteLine("[DEBUG] ==========================================");
        }

        /// <summary>
        /// Get captured data summary for console display
        /// </summary>
        private string GetCapturedDataSummary()
        {
            var summary = new System.Text.StringBuilder();
            summary.AppendLine("Captured Fingerprint Data:");
            summary.AppendLine($"Left Index:  {(bFeatureLI != null ? bFeatureLI.Length + " bytes" : "NULL")} / Image: {(flatleftindex_pictureBox.Image != null ? "OK" : "NULL")}");
            summary.AppendLine($"Left Thumb:  {(bFeatureLT != null ? bFeatureLT.Length + " bytes" : "NULL")} / Image: {(flatleftthumb_pictureBox.Image != null ? "OK" : "NULL")}");
            summary.AppendLine($"Left Middle: {(bFeatureLM != null ? bFeatureLM.Length + " bytes" : "NULL")} / Image: {(flatleftmiddle_pictureBox.Image != null ? "OK" : "NULL")}");
            summary.AppendLine($"Left Ring:   {(bFeatureLR != null ? bFeatureLR.Length + " bytes" : "NULL")} / Image: {(flatleftring_pictureBox.Image != null ? "OK" : "NULL")}");
            summary.AppendLine($"Left Little: {(bFeatureLL != null ? bFeatureLL.Length + " bytes" : "NULL")} / Image: {(flatleftlittle_pictureBox.Image != null ? "OK" : "NULL")}");
            summary.AppendLine($"Right Index: {(bFeatureRI != null ? bFeatureRI.Length + " bytes" : "NULL")} / Image: {(flatrightindex_pictureBox.Image != null ? "OK" : "NULL")}");
            summary.AppendLine($"Right Thumb: {(bFeatureRT != null ? bFeatureRT.Length + " bytes" : "NULL")} / Image: {(flatrightthumb_pictureBox.Image != null ? "OK" : "NULL")}");
            summary.AppendLine($"Right Middle:{(bFeatureRM != null ? bFeatureRM.Length + " bytes" : "NULL")} / Image: {(flatrightmiddle_pictureBox.Image != null ? "OK" : "NULL")}");
            summary.AppendLine($"Right Ring:  {(bFeatureRR != null ? bFeatureRR.Length + " bytes" : "NULL")} / Image: {(flatrightring_pictureBox.Image != null ? "OK" : "NULL")}");
            summary.AppendLine($"Right Little:{(bFeatureRL != null ? bFeatureRL.Length + " bytes" : "NULL")} / Image: {(flatrightlittle_pictureBox.Image != null ? "OK" : "NULL")}");

            int totalTemplates = 0;
            if (bFeatureLI != null) totalTemplates++;
            if (bFeatureLT != null) totalTemplates++;
            if (bFeatureLM != null) totalTemplates++;
            if (bFeatureLR != null) totalTemplates++;
            if (bFeatureLL != null) totalTemplates++;
            if (bFeatureRI != null) totalTemplates++;
            if (bFeatureRT != null) totalTemplates++;
            if (bFeatureRM != null) totalTemplates++;
            if (bFeatureRR != null) totalTemplates++;
            if (bFeatureRL != null) totalTemplates++;

            summary.AppendLine($"\nTotal captured templates: {totalTemplates}");
            return summary.ToString();
        }

        /// <summary>
        /// Get captured fingerprint template for verification
        /// </summary>
        private byte[] GetCapturedTemplate(int fingerIndex)
        {
            try
            {
                // Map finger index to the appropriate captured template
                // This uses the same mapping as the existing identify functionality
                switch (fingerIndex)
                {
                    case 1: return bFeatureRT; // Right Thumb
                    case 2: return bFeatureRI; // Right Index
                    case 3: return bFeatureRM; // Right Middle
                    case 4: return bFeatureRR; // Right Ring
                    case 5: return bFeatureRL; // Right Little
                    case 6: return bFeatureLT; // Left Thumb
                    case 7: return bFeatureLI; // Left Index
                    case 8: return bFeatureLM; // Left Middle
                    case 9: return bFeatureLR; // Left Ring
                    case 10: return bFeatureLL; // Left Little
                    default:
                        Console.WriteLine("Invalid finger index: " + fingerIndex);
                        return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetCapturedTemplate: " + ex.Message);
                return null;
            }
        }

        /// <summary>
        /// Get stored fingerprint template from database for specific person and finger
        /// </summary>
        private byte[] GetStoredTemplate(string personId, int fingerIndex)
        {
            try
            {
                string connectionString = ConfigManager.GetDatabaseConnectionString();

                using (var conn = new MySqlConnection(connectionString))
                {
                    conn.Open();

                    string sql = "SELECT FingerData FROM enrollusers WHERE UserId = @userId AND FingerPosition = @fingerPosition";
                    using (var cmd = new MySqlCommand(sql, conn))
                    {
                        cmd.Parameters.AddWithValue("@userId", personId);
                        cmd.Parameters.AddWithValue("@fingerPosition", fingerIndex);

                        var result = cmd.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            Console.WriteLine("Found stored template for " + personId + " finger " + fingerIndex);
                            return (byte[])result;
                        }
                        else
                        {
                            Console.WriteLine("No stored template found for " + personId + " finger " + fingerIndex);
                            return null;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetStoredTemplate: " + ex.Message);
                return null;
            }
        }

        /// <summary>
        /// Create a placeholder BMP image for testing
        /// </summary>
        private byte[] CreatePlaceholderBMP()
        {
            try
            {
                using (var bmp = new Bitmap(100, 100))
                {
                    using (var g = Graphics.FromImage(bmp))
                    {
                        g.Clear(Color.LightGray);
                        g.DrawString("TEST", new Font("Arial", 12), Brushes.Black, 10, 40);
                    }

                    using (var ms = new MemoryStream())
                    {
                        bmp.Save(ms, System.Drawing.Imaging.ImageFormat.Bmp);
                        return ms.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception creating placeholder BMP: " + ex.Message);
                return new byte[] { 0x42, 0x4D, 0x3A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x28, 0x00 };
            }
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            // Initialize logging
            InitializeDebugLogging();
            LogDebug("[MainForm_Load] Starting form initialization...");

            this.Text = "APIS TrustFinger";
            // enroll_button removed - using Capture and Save functionality
            verify_button.Enabled = false;
            Identify_button.Enabled = false;
            cleardb_button.Enabled = false;
            cleardb_button.Visible = false;
            toolversion_label.Text = "v1.0.2";

            LogDebug($"[MainForm_Load] Device manager IsOpen: {deviceManager.IsOpen}");

            // Initialize TrustFingerNative device handle (same as proven project)
            InitializeNativeDeviceHandle();

            // Device should already be initialized by Program.cs on startup
            // Just update UI to reflect current device state
            UpdateUIForCurrentDeviceState();

            // TCP bridge server is now started by Program.cs on application startup

            LogDebug("[MainForm_Load] Form initialization completed");
        }

        /// <summary>
        /// Initialize TrustFingerNative device handle (same as proven project approach)
        /// </summary>
        private void InitializeNativeDeviceHandle()
        {
            try
            {
                LogDebug("[Native] Initializing TrustFingerNative device handle...");

                // Initialize SDK (same as proven project)
                var initResult = TrustFingerNative.ARAFPSCAN_GlobalInit();
                if (initResult != 0 && initResult != -115) // -115 means already initialized
                {
                    LogDebug($"[Native] SDK initialization failed: {initResult}");
                    return;
                }
                LogDebug($"[Native] SDK initialized successfully (result: {initResult})");

                // Check device count
                int devCount = 0;
                int countResult = TrustFingerNative.ARAFPSCAN_GetDeviceCount(ref devCount);
                LogDebug($"[Native] Device count: {devCount}, result: {countResult}");

                if (devCount > 0)
                {
                    // Open device (same as proven project)
                    var openResult = TrustFingerNative.ARAFPSCAN_OpenDevice(ref nativeDeviceHandle, 0);
                    if (openResult == 0)
                    {
                        LogDebug($"[Native] Device opened successfully. Handle: {nativeDeviceHandle}");
                    }
                    else
                    {
                        LogDebug($"[Native] Failed to open device. Error: {openResult}");
                        nativeDeviceHandle = IntPtr.Zero;
                    }
                }
                else
                {
                    LogDebug("[Native] No devices found");
                }
            }
            catch (Exception ex)
            {
                LogDebug($"[Native] Exception during initialization: {ex.Message}");
                nativeDeviceHandle = IntPtr.Zero;
            }
        }

        private static string debugLogPath = "";

        private void InitializeDebugLogging()
        {
            try
            {
                debugLogPath = Path.Combine(Application.StartupPath, "device_debug.log");
                File.WriteAllText(debugLogPath, $"=== Device Debug Log Started at {DateTime.Now} ===\n");
            }
            catch
            {
                debugLogPath = "";
            }
        }

        private void LogDebug(string message)
        {
            try
            {
                if (!string.IsNullOrEmpty(debugLogPath))
                {
                    File.AppendAllText(debugLogPath, $"{DateTime.Now:HH:mm:ss.fff} {message}\n");
                }
                Console.WriteLine(message); // Also try console
            }
            catch
            {
                // Ignore logging errors
            }
        }

        /// <summary>
        /// Simple device initialization like proven project
        /// </summary>
        private void AutoDetectAndOpenDevice()
        {
            try
            {
                LogDebug("[AutoDetect] Starting device initialization...");
                maintips_label.ForeColor = Color.Blue;
                maintips_label.Text = "Initializing device...";

                bool connected = InitializeDevice();

                if (connected)
                {
                    LogDebug("[AutoDetect] ✅ Device initialized successfully");
                    maintips_label.ForeColor = Color.Green;
                    maintips_label.Text = "Device connected successfully";
                }
                else
                {
                    LogDebug("[AutoDetect] ❌ Device initialization failed");
                    maintips_label.ForeColor = Color.Red;
                    maintips_label.Text = "Failed to connect device - Please check connection";
                }
            }
            catch (Exception ex)
            {
                LogDebug($"[AutoDetect] ❌ Exception during initialization: {ex.Message}");
                maintips_label.ForeColor = Color.Red;
                maintips_label.Text = "Device initialization error: " + ex.Message;
            }
        }

        /// <summary>
        /// Run comprehensive device diagnostics to identify conflicts
        /// </summary>
        private void RunDeviceDiagnostics()
        {
            try
            {
                LogDebug("[Diagnostics] Starting comprehensive device diagnostics...");

                // Check if proven project is running
                var provenProcesses = System.Diagnostics.Process.GetProcessesByName("FingerBridge");
                if (provenProcesses.Length > 0)
                {
                    LogDebug($"[Diagnostics] ⚠️ Found {provenProcesses.Length} FingerBridge process(es) running");
                    ShowDebugMessage("Process Conflict Detected",
                        $"Found {provenProcesses.Length} FingerBridge process(es) running.\n\n" +
                        "The proven project application may be using the device.\n" +
                        "Please close the FingerBridge.exe application and try again.\n\n" +
                        "Process details:\n" +
                        string.Join("\n", provenProcesses.Select(p => $"- PID: {p.Id}, Started: {p.StartTime}")));
                    return;
                }

                // Check for other potential conflicts
                var trustFingerProcesses = System.Diagnostics.Process.GetProcesses()
                    .Where(p => p.ProcessName.ToLower().Contains("trust") ||
                               p.ProcessName.ToLower().Contains("finger") ||
                               p.ProcessName.ToLower().Contains("aratek"))
                    .ToArray();

                if (trustFingerProcesses.Length > 1) // More than just our current process
                {
                    LogDebug($"[Diagnostics] ⚠️ Found {trustFingerProcesses.Length} fingerprint-related processes");
                    var processInfo = string.Join("\n", trustFingerProcesses.Select(p => $"- {p.ProcessName} (PID: {p.Id})"));
                    ShowDebugMessage("Multiple Fingerprint Processes",
                        $"Found multiple fingerprint-related processes:\n\n{processInfo}\n\n" +
                        "One of these may be using the device. Please close other fingerprint applications.");
                }

                // Check device manager state
                LogDebug($"[Diagnostics] Device manager state - IsOpen: {deviceManager.IsOpen}");
                if (deviceManager.IsOpen)
                {
                    LogDebug("[Diagnostics] ⚠️ Device manager reports device is already open");
                    ShowDebugMessage("Device Already Open",
                        "The device manager reports that a device is already open.\n\n" +
                        "This might be from a previous session. Attempting to close and reopen...");

                    try
                    {
                        deviceManager.CloseDevice();
                        LogDebug("[Diagnostics] Closed existing device connection");
                        System.Threading.Thread.Sleep(1000); // Wait a second
                    }
                    catch (Exception ex)
                    {
                        LogDebug($"[Diagnostics] Error closing device: {ex.Message}");
                    }
                }

                LogDebug("[Diagnostics] Diagnostics completed");
            }
            catch (Exception ex)
            {
                LogDebug($"[Diagnostics] ❌ Exception during diagnostics: {ex.Message}");
            }
        }

        /// <summary>
        /// Device initialization using existing TrustFingerManager with proven project logic
        /// </summary>
        private bool InitializeDevice()
        {
            try
            {
                LogDebug("[Init] Starting device initialization...");

                // Initialize SDK
                TrustFingerManager.GlobalInitialize();
                LogDebug("[Init] SDK initialized");

                // Check device count
                int devCount = TrustFingerManager.GetDeviceCount();
                LogDebug($"[Init] Found {devCount} device(s)");

                if (devCount > 0)
                {
                    // Try to open device
                    bool opened = deviceManager.OpenDevice(0);
                    LogDebug($"[Init] Device open result: {opened}");

                    if (opened)
                    {
                        LogDebug("[Init] Device connected successfully");

                        // Try to get device description
                        try
                        {
                            var desc = TrustFingerManager.GetDeviceDescription(0);
                            LogDebug($"[Init] Device: {desc.ProductName} (ID: {desc.DeviceId})");

                            // Check if device is supported
                            if (desc.DeviceId == 800 || desc.DeviceId == 900 || desc.DeviceId == 303)
                            {
                                UpdateUIForOpenDevice(desc);
                                return true;
                            }
                            else
                            {
                                LogDebug($"[Init] Device ID {desc.DeviceId} not supported");
                                deviceManager.CloseDevice();
                                return false;
                            }
                        }
                        catch (Exception ex)
                        {
                            LogDebug($"[Init] Device opened but description failed: {ex.Message}");
                            // Assume it's a supported device and continue
                            var basicDesc = new DeviceDescription { DeviceId = 900, ProductName = "A900 Device" };
                            UpdateUIForOpenDevice(basicDesc);
                            return true;
                        }
                    }
                    else
                    {
                        LogDebug("[Init] Failed to open device");
                        return false;
                    }
                }
                else
                {
                    LogDebug("[Init] No devices found");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogDebug($"[Init] Exception during initialization: {ex.Message}");
                return false;
            }
        }



        /// <summary>
        /// Update UI to reflect current device state (device should already be initialized by Program.cs)
        /// </summary>
        private void UpdateUIForCurrentDeviceState()
        {
            try
            {
                LogDebug("[UI] Checking current device state...");

                if (deviceManager.IsOpen)
                {
                    LogDebug("[UI] Device is already open, updating UI...");

                    try
                    {
                        // Try to get device description
                        var desc = TrustFingerManager.GetDeviceDescription(0);
                        LogDebug($"[UI] Device: {desc.ProductName} (ID: {desc.DeviceId})");

                        UpdateUIForOpenDevice(desc);
                        maintips_label.ForeColor = Color.Green;
                        maintips_label.Text = "Device ready (connected at startup)";
                    }
                    catch (Exception ex)
                    {
                        LogDebug($"[UI] Device open but description failed: {ex.Message}");

                        // Use basic description
                        var basicDesc = new DeviceDescription { DeviceId = 900, ProductName = "A900 Device" };
                        UpdateUIForOpenDevice(basicDesc);
                        maintips_label.ForeColor = Color.Green;
                        maintips_label.Text = "Device ready (connected at startup)";
                    }
                }
                else
                {
                    LogDebug("[UI] Device is not open");
                    maintips_label.ForeColor = Color.Red;
                    maintips_label.Text = "Device not connected - Use Open Device button";
                }
            }
            catch (Exception ex)
            {
                LogDebug($"[UI] Exception checking device state: {ex.Message}");
                maintips_label.ForeColor = Color.Red;
                maintips_label.Text = "Unable to check device state";
            }
        }

        private void ShowDebugMessage(string title, string message)
        {
            // Show a message box with debug information
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// Update UI when device is opened (extracted from opencloseDevice_button_Click)
        /// </summary>
        /// <param name="desc">Device description</param>
        private void UpdateUIForOpenDevice(DeviceDescription desc)
        {
            manufacturer_textBox.Text = desc.Manufacturer;
            deviceid_textBox.Text = desc.DeviceId.ToString();
            imgwidth_textBox.Text = desc.ImagePixelWidth.ToString();
            imgheight_textBox.Text = desc.ImagePixelHeight.ToString();
            fwversion_textBox.Text = desc.FirmwareVersion;
            imgdpi_textBox.Text = desc.Resolution.ToString();
            sn_textBox.Text = desc.SerialNumber.ToString();
            opencloseDevice_button.Text = "Close Device";

            initUIControlEnable(true);

            if (desc.DeviceId == 800)
            {
                leftfour_label.Text = "Two Fingers";
                twothumbs_label.Enabled = false;
                maintwothumb_pictureBox.Enabled = false;
                label_right_four_finger.Enabled = false;
                mainrightfour_pictureBox.Enabled = false;
                fingerSelect_groupBox.Text = "Finger Selection (A800)";
                this.Text = "APIS TrustFinger V1.0";
            }
            else
            {
                leftfour_label.Text = "Left Four Fingers";
                twothumbs_label.Enabled = true;
                maintwothumb_pictureBox.Enabled = true;
                label_right_four_finger.Enabled = true;
                mainrightfour_pictureBox.Enabled = true;
                dryFinger_checkBox.Checked = false;
                dryFinger_checkBox.Visible = false;
                dryLevel_comboBox.SelectedIndex = 0;
                dryLevel_comboBox.Visible = false;
                fingerSelect_groupBox.Text = "Missing Fingers (Check if finger is unavailable)";
                this.Text = "APIS TrustFinger";
            }

            if (tabControlMain.SelectedIndex == 1 || tabControlMain.SelectedIndex == 2)
            {
                // enroll_button removed - using Capture and Save functionality
                verify_button.Enabled = true;
                Identify_button.Enabled = true;
            }
            cleardb_button.Enabled = true;
        }

        private void showSingleFingerImage(Bitmap bmpImage,bool bRollType, FingerPosition fingerPos, IntPtr feature, int nIQ, int nNFIQ)
        {
            byte[] bfeature = new byte[1024];
            if(feature != IntPtr.Zero)
                Marshal.Copy(feature, bfeature, 0, 1024);

            switch (fingerPos)
            {
                case FingerPosition.RightThumb:
                    {
                        if(bRollType)
                        {
                            rolledrightthumb_pictureBox.Image = bmpImage;
                            bFeatureRT_Rolled = bfeature;
                            label_NFIQ_RT_ROLL.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                        else
                        {
                            flatrightthumb_pictureBox.Image = bmpImage;
                            bFeatureRT = bfeature;
                            nIQRT = nIQ;
                            label_NFIQ_RT_FLAT.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                    }
                    break;
                case FingerPosition.RightIndexFinger:
                    {
                        if (bRollType)
                        {
                            rolledrightindex_pictureBox.Image = bmpImage;
                            bFeatureRI_Rolled = bfeature;
                            label_NFIQ_RI_ROLL.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                        else
                        {
                            flatrightindex_pictureBox.Image = bmpImage;
                            bFeatureRI = bfeature;
                            nIQRI = nIQ;
                            label_NFIQ_RI_FLAT.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                    }
                    break;

                case FingerPosition.RightMiddleFinger:
                    {
                        if (bRollType)
                        {
                            rolledrightmiddle_pictureBox.Image = bmpImage;
                            bFeatureRM_Rolled = bfeature;
                            label_NFIQ_RM_ROLL.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                        else
                        {
                            flatrightmiddle_pictureBox.Image = bmpImage;
                            bFeatureRM = bfeature;
                            nIQRM = nIQ;
                            label_NFIQ_RM_FLAT.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                    }
                    break;

                case FingerPosition.RightRingFinger:
                    {
                        if (bRollType)
                        {
                            rolledrightring_pictureBox.Image = bmpImage;
                            bFeatureRR_Rolled = bfeature;
                            label_NFIQ_RR_ROLL.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                        else
                        {
                            flatrightring_pictureBox.Image = bmpImage;
                            bFeatureRR = bfeature;
                            nIQRR = nIQ;
                            label_NFIQ_RR_FLAT.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                    }
                    break;

                case FingerPosition.RightLittleFinger:
                    {
                        if (bRollType)
                        {
                            rolledrightlittle_pictureBox.Image = bmpImage;
                            bFeatureRL_Rolled = bfeature;
                            label_NFIQ_RL_ROLL.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                        else
                        {
                            flatrightlittle_pictureBox.Image = bmpImage;
                            bFeatureRL = bfeature;
                            nIQRL = nIQ;
                            label_NFIQ_RL_FLAT.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                    }
                    break;

                case FingerPosition.LeftThumb:
                    {
                        if (bRollType)
                        {
                            rolledleftthumb_pictureBox.Image = bmpImage;
                            bFeatureLT_Rolled = bfeature;
                            label_NFIQ_LT_ROLL.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                        else
                        {
                            flatleftthumb_pictureBox.Image = bmpImage;
                            bFeatureLT = bfeature;
                            nIQLT = nIQ;
                            label_NFIQ_LT_FLAT.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                    }
                    break;

                case FingerPosition.LeftIndexFinger:
                    {
                        if (bRollType)
                        {
                            rolledleftindex_pictureBox.Image = bmpImage;
                            bFeatureLI_Rolled = bfeature;
                            label_NFIQ_LI_ROLL.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                        else
                        {
                            flatleftindex_pictureBox.Image = bmpImage;
                            bFeatureLI = bfeature;
                            nIQLI = nIQ;
                            label_NFIQ_LI_FLAT.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                    }
                    break;

                case FingerPosition.LeftMiddleFinger:
                    {
                        if (bRollType)
                        {
                            rolledleftmiddle_pictureBox.Image = bmpImage;
                            bFeatureLM_Rolled = bfeature;
                            label_NFIQ_LM_ROLL.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                        else
                        {
                            flatleftmiddle_pictureBox.Image = bmpImage;
                            bFeatureLM = bfeature;
                            nIQLM = nIQ;
                            label_NFIQ_LM_FLAT.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                    }
                    break;

                case FingerPosition.LeftRingFinger:
                    {
                        if (bRollType)
                        {
                            rolledleftring_pictureBox.Image = bmpImage;
                            bFeatureLR_Rolled = bfeature;
                            label_NFIQ_LR_ROLL.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                        else
                        {
                            flatleftring_pictureBox.Image = bmpImage;
                            bFeatureLR = bfeature;
                            nIQLR = nIQ;
                            label_NFIQ_LR_FLAT.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                    }
                    break;

                case FingerPosition.LeftLittleFinger:
                    {
                        if (bRollType)
                        {
                            rolledleftlittle_pictureBox.Image = bmpImage;
                            bFeatureLL_Rolled = bfeature;
                            label_NFIQ_LL_ROLL.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                        else
                        {
                            flatleftlittle_pictureBox.Image = bmpImage;
                            bFeatureLL = bfeature;
                            nIQLL = nIQ;
                            label_NFIQ_LL_FLAT.Text = "NFIQ=" + nNFIQ.ToString();
                        }
                    }
                    break;

            }
        }

        public void mainFormTranferEvent(EnumOperationType oType, int nPos, Bitmap bmpImage, IntPtr feature, int nIQ, int nNFIQ, int A800LedFlag)
        {
            this.BeginInvoke(new Action(() =>
                {
                    switch (oType)
                    {
                        case EnumOperationType.SLAP_4_LEFT_FINGERS:
                            {
                                if(nPos==0)
                                    mainleftfour_pictureBox.Image = bmpImage;
                                else
                                    showSingleFingerImage(bmpImage, false, (FingerPosition)nPos, feature, nIQ, nNFIQ);
                            }
                            break;
                        case EnumOperationType.SLAP_4_RIGHT_FINGERS:
                            {
                                if (nPos == 0)
                                    mainrightfour_pictureBox.Image = bmpImage;
                                else
                                    showSingleFingerImage(bmpImage, false, (FingerPosition)nPos, feature, nIQ, nNFIQ);
                            }
                            break;
                        case EnumOperationType.SLAP_2_THUMBS_FINGERS:
                            {
                                if (nPos == 0)
                                    maintwothumb_pictureBox.Image = bmpImage;
                                else
                                    showSingleFingerImage(bmpImage, false, (FingerPosition)nPos, feature, nIQ, nNFIQ);
                            }
                            break;

                        case EnumOperationType.SLAP_ANY_TWO_FINGERS:
                            {
                                if (nPos == 0)
                                    mainleftfour_pictureBox.Image = bmpImage;
                                else
                                {
                                    int min = 0;int max = 0;
                                    if(nSelectFinger1 < nSelectFinger2)
                                    {
                                        min = nSelectFinger1;
                                        max = nSelectFinger2;
                                    }
                                    else
                                    {
                                        min = nSelectFinger2;
                                        max = nSelectFinger1;
                                    }
                                    if(nPos == 1)
                                        showSingleFingerImage(bmpImage, false, (FingerPosition)((max < 6) ? min : max), feature, nIQ, nNFIQ);
                                    else if(nPos == 2)
                                        showSingleFingerImage(bmpImage, false, (FingerPosition)((max < 6) ? max : min), feature, nIQ, nNFIQ);
                                }
                            }
                            break;

                        case EnumOperationType.ROLL_RIGHT_THUMB_FINGER:
                        case EnumOperationType.ROLL_RIGHT_INDEX_FINGER:
                        case EnumOperationType.ROLL_RIGHT_MIDDLE_FINGER:
                        case EnumOperationType.ROLL_RIGHT_RING_FINGER:
                        case EnumOperationType.ROLL_RIGHT_LITTLE_FINGER:
                        case EnumOperationType.ROLL_LEFT_THUMB_FINGER:
                        case EnumOperationType.ROLL_LEFT_INDEX_FINGER:
                        case EnumOperationType.ROLL_LEFT_MIDDLE_FINGER:
                        case EnumOperationType.ROLL_LEFT_RING_FINGER:
                        case EnumOperationType.ROLL_LEFT_LITTLE_FINGER:
                             showSingleFingerImage(bmpImage, true, (FingerPosition)(oType - 10), feature, nIQ, nNFIQ);
                            break;

                        case EnumOperationType.FLAT_RIGHT_THUMB_FINGER:
                        case EnumOperationType.FLAT_RIGHT_INDEX_FINGER:
                        case EnumOperationType.FLAT_RIGHT_MIDDLE_FINGER:
                        case EnumOperationType.FLAT_RIGHT_RING_FINGER:
                        case EnumOperationType.FLAT_RIGHT_LITTLE_FINGER:
                        case EnumOperationType.FLAT_LEFT_THUMB_FINGER:
                        case EnumOperationType.FLAT_LEFT_INDEX_FINGER:
                        case EnumOperationType.FLAT_LEFT_MIDDLE_FINGER:
                        case EnumOperationType.FLAT_LEFT_RING_FINGER:
                        case EnumOperationType.FLAT_LEFT_LITTLE_FINGER:
                             showSingleFingerImage(bmpImage, false, (FingerPosition)oType, feature, nIQ, nNFIQ);
                            break;
                    }
                }));

            var desc = TrustFingerManager.GetDeviceDescription(0);
            if (desc.DeviceId == 800)
            {
                if(A800LedFlag == 0)
                {
                    deviceManager.GetDevice().SetLedStatus(1, LedStatus.Off);
                    deviceManager.GetDevice().SetLedStatus(0, LedStatus.On);
                    Thread.Sleep(200);
                    deviceManager.GetDevice().SetLedStatus(0, LedStatus.On);

                }
                else if(A800LedFlag == 1)
                {
                    deviceManager.GetDevice().SetLedStatus(1, LedStatus.Off);
                    Thread.Sleep(200);
                    deviceManager.GetDevice().SetLedStatus(1, LedStatus.On);
                    Thread.Sleep(200);
                    deviceManager.GetDevice().SetLedStatus(1, LedStatus.Off);
                }
            }
        }

        public void closeBtnSetUI()
        {
            manufacturer_textBox.Text = "APIS Co.,LTD.";
            deviceid_textBox.Text = "N/A";
            imgwidth_textBox.Text = "N/A";
            imgheight_textBox.Text = "N/A";
            fwversion_textBox.Text = "N/A";
            imgdpi_textBox.Text = "N/A";
            sn_textBox.Text = "N/A";
            opencloseDevice_button.Text = "Open Device";

            label_NFIQ_LT_ROLL.Text = "NFIQ=N/A";
            label_NFIQ_LI_ROLL.Text = "NFIQ=N/A";
            label_NFIQ_LM_ROLL.Text = "NFIQ=N/A";
            label_NFIQ_LR_ROLL.Text = "NFIQ=N/A";
            label_NFIQ_LL_ROLL.Text = "NFIQ=N/A";

            label_NFIQ_RT_ROLL.Text = "NFIQ=N/A";
            label_NFIQ_RI_ROLL.Text = "NFIQ=N/A";
            label_NFIQ_RM_ROLL.Text = "NFIQ=N/A";
            label_NFIQ_RR_ROLL.Text = "NFIQ=N/A";
            label_NFIQ_RL_ROLL.Text = "NFIQ=N/A";

            label_NFIQ_LT_FLAT.Text = "NFIQ=N/A";
            label_NFIQ_LI_FLAT.Text = "NFIQ=N/A";
            label_NFIQ_LM_FLAT.Text = "NFIQ=N/A";
            label_NFIQ_LR_FLAT.Text = "NFIQ=N/A";
            label_NFIQ_LL_FLAT.Text = "NFIQ=N/A";

            label_NFIQ_RT_FLAT.Text = "NFIQ=N/A";
            label_NFIQ_RI_FLAT.Text = "NFIQ=N/A";
            label_NFIQ_RM_FLAT.Text = "NFIQ=N/A";
            label_NFIQ_RR_FLAT.Text = "NFIQ=N/A";
            label_NFIQ_RL_FLAT.Text = "NFIQ=N/A";

            R5_checkBox.Checked = false;
            R4_checkBox.Checked = false;
            R3_checkBox.Checked = false;
            R2_checkBox.Checked = false;
            R1_checkBox.Checked = false;
            L5_checkBox.Checked = false;
            L2_checkBox.Checked = false;
            L3_checkBox.Checked = false;
            L4_checkBox.Checked = false;
            L1_checkBox.Checked = false;
            // enroll_button removed - using Capture and Save functionality
            verify_button.Enabled = false;
            Identify_button.Enabled = false;
            dryFinger_checkBox.Visible = false;
            dryLevel_comboBox.Visible = false;

            leftfour_label.Text = "Left Four Fingers";
            twothumbs_label.Enabled = true;
            maintwothumb_pictureBox.Enabled = true;
            label_right_four_finger.Enabled = true;
            mainrightfour_pictureBox.Enabled = true;
            fingerSelect_groupBox.Text = "";
            
            maintips_label.ForeColor = Color.Blue;
            maintips_label.Text = "Close device success";
        }

        public void initUIControlEnable(bool bControl)
        {
            cleardb_button.Enabled = bControl;
            start_button.Enabled = bControl;
            
            R5_checkBox.Enabled = bControl;
            R4_checkBox.Enabled = bControl;
            R3_checkBox.Enabled = bControl;
            R2_checkBox.Enabled = bControl;
            R1_checkBox.Enabled = bControl;
            L5_checkBox.Enabled = bControl;
            L2_checkBox.Enabled = bControl;
            L3_checkBox.Enabled = bControl;
            L4_checkBox.Enabled = bControl;
            L1_checkBox.Enabled = bControl;

            if(TrustFingerManager.GetDeviceDescription(0).DeviceId == 900 || TrustFingerManager.GetDeviceDescription(0).DeviceId == 303)
            {
                dryFinger_checkBox.Enabled = bControl;
                dryLevel_comboBox.Enabled = bControl;
                R1_checkBox.Enabled = false;
                L1_checkBox.Enabled = false;
            }
        }

        public void mainTransferClosePreEvent()
        {
            Console.WriteLine("🔄 [CAPTURE-SAVE] Capture completed, starting auto-enrollment...");

            initUIControlEnable(true);

            if (tabControlMain.SelectedIndex == 0) // Slaps tab
            {
                tabControlMain.SelectedIndex = 1; // Switch to Rolled Fingerprints tab
            }

            opencloseDevice_button.Enabled = true;

            // Auto-enroll after capture completion (Capture and Save functionality)
            AutoEnrollAfterCapture();
        }

        public void mainTransferIdentifyClosePreEvent()
        {
            verify_button.Enabled = true;
            Identify_button.Enabled = true;
        }

        private void start_button_Click(object sender, EventArgs e)
        {
            maintips_label.Text = "";

            // Check if device is opened
            if (!deviceManager.IsOpen || deviceManager.GetDevice() == null)
            {
                maintips_label.ForeColor = Color.Red;
                maintips_label.Text = "Please open device first";
                Console.WriteLine("❌ Please click 'Open Device' button first to connect the A900 fingerprint scanner.");
                return;
            }

            // Check if person_id is provided before capture (Capture and Save requirement)
            string useridStr = userID_textBox.Text.Trim();
            if(string.IsNullOrEmpty(useridStr))
            {
                maintips_label.ForeColor = Color.Red;
                maintips_label.Text = "Please input Prisoner ID before capture";
                Console.WriteLine("❌ Please input Prisoner ID before starting capture");
                userID_textBox.Focus();
                return;
            }

            int devType = 0;
            if(TrustFingerManager.GetDeviceDescription(0).DeviceId == 800)
            {
                if (nSelectFinger1 == 0 && nSelectFinger2 == 0)
                {
                    Console.WriteLine("❌ Please select at least one finger.");
                    return;
                }
                devType = 800;
            }
            else
            {
                // For A900 device, auto-select right thumb if no finger selected
                if (nSelectFinger1 == 0 && nSelectFinger2 == 0)
                {
                    nSelectFinger1 = (int)CapFingerPosition.RightThumb;
                    maintips_label.ForeColor = Color.Blue;
                    maintips_label.Text = "Auto-selected Right Thumb for capture";
                }
                int nWetDryLevel = dryLevel_comboBox.SelectedIndex;
                if (dryFinger_checkBox.Checked)
                {
                    if (EnDryWetFingerLevelType.WET_DRY_FINGER_NORMAL != (EnDryWetFingerLevelType)nWetDryLevel)
                    {
                        deviceManager.GetDevice().MultiFingerSetDryWetFingerLevel((EnDryWetFingerLevelType)nWetDryLevel);
                    }
                    else
                    {
                        if (g_FirstcheckFlag != 0)
                        {
                            deviceManager.GetDevice().MultiFingerSetDryWetFingerLevel(EnDryWetFingerLevelType.WET_DRY_FINGER_NORMAL);
                        }
                    }
                }

                devType = 900;
            }
            
            int nImgFormat = imgformat_comboBox.SelectedIndex;
            capWindow = new CaptureForm(deviceManager.GetDevice(), nSelectFinger1, nSelectFinger2, devType/*nSelectCount*/, nImgFormat, currentIndex);
            capWindow.StartPosition = FormStartPosition.CenterScreen;
            capWindow.Show();
            capWindow.TransferPreDataEvent += mainFormTranferEvent;
            //capWindow.TransferSegDataEvent += mainFormTranferSegEvent;
            capWindow.TransferClosePre += mainTransferClosePreEvent;

            opencloseDevice_button.Enabled = false;
            initUIControlEnable(false);
        }

        private void opencloseDevice_button_Click(object sender, EventArgs e)
        {
            if (!deviceManager.IsOpen)
            {
                // Manual device opening
                bool opened = deviceManager.OpenDevice(0);
                var dev = deviceManager.GetDevice();
                if (!opened || dev == null)
                {
                    Console.WriteLine("❌ Failed to open A900 device. Please check: 1. Device is connected via USB 2. Device drivers are installed 3. Device is not used by another application");
                    maintips_label.ForeColor = Color.Red;
                    maintips_label.Text = "Failed to open device";
                    return;
                }

                var desc = TrustFingerManager.GetDeviceDescription(0);
                if(desc.DeviceId != 800 && desc.DeviceId != 900 && desc.DeviceId != 303)
                {
                    deviceManager.CloseDevice();
                    Console.WriteLine("❌ Device not supported.");
                    maintips_label.ForeColor = Color.Red;
                    maintips_label.Text = "Device not supported";
                    return;
                }

                // Use the extracted method to update UI
                UpdateUIForOpenDevice(desc);
                maintips_label.ForeColor = Color.Blue;
                maintips_label.Text = "Open device success";
            }
            else
            {
                // Close device
                deviceManager.CloseDevice();
                closeBtnSetUI();
                initUIControlEnable(false);
                clearAllImageShow();
            }
        }

        private void dryFinger_checkBox_CheckedChanged(object sender, EventArgs e)
        {
            Thread.Sleep(100);
            if(dryFinger_checkBox.Checked)
            {
                g_FirstcheckFlag = 1;
                if (g_FirstcheckFlag == 1)
                    deviceManager.GetDevice().MultiFingerGetDryWetFingerLevel();
                g_FirstcheckFlag++;
            }
            else
            {
                deviceManager.GetDevice().MultiFingerSetDryWetFingerLevel(EnDryWetFingerLevelType.WET_DRY_FINGER_NORMAL);
                g_FirstcheckFlag = 0;
            }
        }

        private void tabControlMain_SelectedIndexChanged(object sender, EventArgs e)
        {
            R5_checkBox.Checked = false;
            R4_checkBox.Checked = false;
            R3_checkBox.Checked = false;
            R2_checkBox.Checked = false;
            R1_checkBox.Checked = false;
            L5_checkBox.Checked = false;
            L2_checkBox.Checked = false;
            L3_checkBox.Checked = false;
            L4_checkBox.Checked = false;
            L1_checkBox.Checked = false;
            nSelectFinger1 = 0;
            nSelectFinger2 = 0;

            if (deviceManager.IsOpen)
                maintips_label.Text = "";
            if(TrustFingerManager.GetDeviceDescription(0).DeviceId == 800)
            {
                if (tabControlMain.SelectedIndex == 1)//rolled
                {
                    nSelectFinger1 = 0;
                    nSelectFinger2 = 0;
                    nSelectCount = 0;
                    currentIndex = 1;
                }
                else
                    currentIndex = 0;
            }

            if (deviceManager.IsOpen)
            {
                if (tabControlMain.SelectedIndex == 0 || tabControlMain.SelectedIndex == 3)
                {
                    // enroll_button removed - using Capture and Save functionality
                    verify_button.Enabled = false;
                    Identify_button.Enabled = false;
                }
                else
                {
                    // enroll_button removed - using Capture and Save functionality
                    verify_button.Enabled = true;
                    Identify_button.Enabled = true;
                }
            }
        }

        private int SaveEnrollment(FingerPosition pos, string userId, string userName, byte[] template, int nOpType, int nfiq, Bitmap bmpImage)
        {
            try
            {
                // Use direct MySQL like proven project
                string connectionString = ConfigManager.GetDatabaseConnectionString();
                byte[] imageBytes = bmpImage != null ? BitmapToBytes(bmpImage) : null;

                using (var conn = new MySqlConnection(connectionString))
                {
                    conn.Open();

                    // Get next available ID
                    string getMaxIdSql = "SELECT COALESCE(MAX(Id), 0) + 1 FROM enrollusers";
                    int nextId;
                    using (var getIdCmd = new MySqlCommand(getMaxIdSql, conn))
                    {
                        nextId = Convert.ToInt32(getIdCmd.ExecuteScalar());
                    }

                    string sql = @"INSERT INTO enrollusers (Id, UserId, CreatedTime, OperationType, FingerPosition, FingerPositionName, FingerData, FingerImage, ImageQuality)
                        VALUES (@id, @userId, @createdTime, @operationType, @fingerPosition, @fingerPositionName, @fingerData, @fingerImage, @imageQuality)";

                    using (var cmd = new MySqlCommand(sql, conn))
                    {
                        cmd.Parameters.AddWithValue("@id", nextId);
                        cmd.Parameters.AddWithValue("@userId", userId);
                        cmd.Parameters.AddWithValue("@createdTime", DateTime.Now);
                        cmd.Parameters.AddWithValue("@operationType", nOpType);
                        cmd.Parameters.AddWithValue("@fingerPosition", (int)pos);
                        cmd.Parameters.AddWithValue("@fingerPositionName", FormatHelper.FormatFingerprintPosition(pos));
                        cmd.Parameters.AddWithValue("@fingerData", template);
                        cmd.Parameters.AddWithValue("@fingerImage", imageBytes ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@imageQuality", nfiq > 0 ? nfiq : (object)DBNull.Value);

                        cmd.ExecuteNonQuery();
                    }
                }

                Console.WriteLine($"[DEBUG] SaveEnrollment completed successfully");
                return 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ [ERROR] SaveEnrollment exception: {ex.Message}");
                Console.WriteLine($"[ERROR] Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"[ERROR] Inner Exception: {ex.InnerException.Message}");
                }
                return -1;
            }
        }

        private byte[] BitmapToBytes(Bitmap bmp)
        {
            using (var ms = new System.IO.MemoryStream())
            {
                bmp.Save(ms, System.Drawing.Imaging.ImageFormat.Bmp);
                return ms.ToArray();
            }
        }

        /// <summary>
        /// Auto-enroll captured fingerprints after capture completion (Capture and Save functionality)
        /// This uses the same logic as the original enroll_button_Click
        /// </summary>
        private void AutoEnrollAfterCapture()
        {
            int nRet = -1;
            maintips_label.Text = "";
            string useridStr = userID_textBox.Text.Trim();
            string usernameStr = useridStr;

            // Person ID should already be validated before capture, but double-check
            if(string.IsNullOrEmpty(useridStr))
            {
                maintips_label.ForeColor = Color.Red;
                maintips_label.Text = "ERROR: No Prisoner ID for auto-save";
                Console.WriteLine("❌ ERROR: AutoEnrollAfterCapture called but no Prisoner ID provided");
                return;
            }

            Console.WriteLine($"🔄 Auto-enrolling captured fingerprints for {useridStr}...");

            // DEBUG: Show captured data before auto-enrollment
            string debugInfo = GetCapturedDataSummary();
            Console.WriteLine($"CAPTURE AND SAVE DEBUG: Auto-enrolling captured fingerprints for {useridStr}\n{debugInfo}");

            // Track enrollment count
            int enrolledCount = 0;

            try
            {
                Console.WriteLine($"[AUTO-ENROLL] Starting auto-enrollment process for {useridStr}...");

                // Get feature and image/quality for each finger
                if (bFeatureLI != null && flatleftindex_pictureBox.Image != null)
                {
                    Console.WriteLine($"[UI DEBUG] Enrolling Left Index Finger - template size: {bFeatureLI.Length}");

                    try
                    {
                        nRet = SaveEnrollment(FingerPosition.LeftIndexFinger, useridStr, usernameStr, bFeatureLI, tabControlMain.SelectedIndex, nIQLI, (Bitmap)flatleftindex_pictureBox.Image);

                        if (0 == nRet)
                        {
                            enrolledCount++;
                            Console.WriteLine($"[UI DEBUG] Left Index enrolled successfully");
                        }
                        else
                        {
                            Console.WriteLine($"❌ [UI DEBUG] Left Index enrollment failed with code: {nRet}");
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[DEBUG] OUTER CATCH - SaveEnrollment FAILED: {ex.Message}");
                        if (ex.InnerException != null)
                        {
                            Console.WriteLine($"[DEBUG] OUTER CATCH - Inner Exception: {ex.InnerException.Message}");
                            if (ex.InnerException.InnerException != null)
                            {
                                Console.WriteLine($"[DEBUG] OUTER CATCH - Inner Inner Exception: {ex.InnerException.InnerException.Message}");
                            }
                        }

                        string fullError = $"OUTER CATCH - SaveEnrollment Error:\n{ex.Message}";
                        if (ex.InnerException != null)
                        {
                            fullError += $"\n\nInner Exception:\n{ex.InnerException.Message}";
                            if (ex.InnerException.InnerException != null)
                            {
                                fullError += $"\n\nDeepest Exception:\n{ex.InnerException.InnerException.Message}";
                            }
                        }

                        Console.WriteLine($"❌ OUTER CATCH - SaveEnrollment Detailed Error: {fullError}");
                        return;
                    }
                }
                else
                {
                    Console.WriteLine($"Left Index skipped - Template: {(bFeatureLI != null ? "OK" : "NULL")}, Image: {(flatleftindex_pictureBox.Image != null ? "OK" : "NULL")}");
                }
            if (bFeatureLT != null && flatleftthumb_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.LeftThumb, useridStr, usernameStr, bFeatureLT, tabControlMain.SelectedIndex, nIQLT, (Bitmap)flatleftthumb_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureLM != null && flatleftmiddle_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.LeftMiddleFinger, useridStr, usernameStr, bFeatureLM, tabControlMain.SelectedIndex, nIQLM, (Bitmap)flatleftmiddle_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureLR != null && flatleftring_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.LeftRingFinger, useridStr, usernameStr, bFeatureLR, tabControlMain.SelectedIndex, nIQLR, (Bitmap)flatleftring_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureLL != null && flatleftlittle_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.LeftLittleFinger, useridStr, usernameStr, bFeatureLL, tabControlMain.SelectedIndex, nIQLL, (Bitmap)flatleftlittle_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureRI != null && flatrightindex_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.RightIndexFinger, useridStr, usernameStr, bFeatureRI, tabControlMain.SelectedIndex, nIQRI, (Bitmap)flatrightindex_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureRT != null && flatrightthumb_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.RightThumb, useridStr, usernameStr, bFeatureRT, tabControlMain.SelectedIndex, nIQRT, (Bitmap)flatrightthumb_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureRM != null && flatrightmiddle_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.RightMiddleFinger, useridStr, usernameStr, bFeatureRM, tabControlMain.SelectedIndex, nIQRM, (Bitmap)flatrightmiddle_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureRR != null && flatrightring_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.RightRingFinger, useridStr, usernameStr, bFeatureRR, tabControlMain.SelectedIndex, nIQRR, (Bitmap)flatrightring_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureRL != null && flatrightlittle_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.RightLittleFinger, useridStr, usernameStr, bFeatureRL, tabControlMain.SelectedIndex, nIQRL, (Bitmap)flatrightlittle_pictureBox.Image);
                if (0 != nRet) return;
            }
            // Rolled fingers
            if (bFeatureLI_Rolled != null && rolledleftindex_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.LeftIndexFinger, useridStr, usernameStr, bFeatureLI_Rolled, tabControlMain.SelectedIndex, nIQLI, (Bitmap)rolledleftindex_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureLT_Rolled != null && rolledleftthumb_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.LeftThumb, useridStr, usernameStr, bFeatureLT_Rolled, tabControlMain.SelectedIndex, nIQLT, (Bitmap)rolledleftthumb_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureLM_Rolled != null && rolledleftmiddle_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.LeftMiddleFinger, useridStr, usernameStr, bFeatureLM_Rolled, tabControlMain.SelectedIndex, nIQLM, (Bitmap)rolledleftmiddle_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureLR_Rolled != null && rolledleftring_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.LeftRingFinger, useridStr, usernameStr, bFeatureLR_Rolled, tabControlMain.SelectedIndex, nIQLR, (Bitmap)rolledleftring_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureLL_Rolled != null && rolledleftlittle_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.LeftLittleFinger, useridStr, usernameStr, bFeatureLL_Rolled, tabControlMain.SelectedIndex, nIQLL, (Bitmap)rolledleftlittle_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureRI_Rolled != null && rolledrightindex_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.RightIndexFinger, useridStr, usernameStr, bFeatureRI_Rolled, tabControlMain.SelectedIndex, nIQRI, (Bitmap)rolledrightindex_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureRT_Rolled != null && rolledrightthumb_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.RightThumb, useridStr, usernameStr, bFeatureRT_Rolled, tabControlMain.SelectedIndex, nIQRT, (Bitmap)rolledrightthumb_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureRM_Rolled != null && rolledrightmiddle_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.RightMiddleFinger, useridStr, usernameStr, bFeatureRM_Rolled, tabControlMain.SelectedIndex, nIQRM, (Bitmap)rolledrightmiddle_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureRR_Rolled != null && rolledrightring_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.RightRingFinger, useridStr, usernameStr, bFeatureRR_Rolled, tabControlMain.SelectedIndex, nIQRR, (Bitmap)rolledrightring_pictureBox.Image);
                if (0 != nRet) return;
            }
            if (bFeatureRL_Rolled != null && rolledrightlittle_pictureBox.Image != null)
            {
                nRet = SaveEnrollment(FingerPosition.RightLittleFinger, useridStr, usernameStr, bFeatureRL_Rolled, tabControlMain.SelectedIndex, nIQRL, (Bitmap)rolledrightlittle_pictureBox.Image);
                if (0 != nRet) return;
            }

                Console.WriteLine("[UI DEBUG] Enrollment process completed successfully");
                Console.WriteLine($"✅ UI ENROLL RESULT: User ID: {useridStr}, Fingers Enrolled: {enrolledCount}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ CRITICAL ERROR during enrollment: {ex.Message}");
                Console.WriteLine($"[ERROR] Stack trace: {ex.StackTrace}");
                return;
            }

            if (enrolledCount > 0)
            {
                Console.WriteLine($"✅ Capture and Save completed successfully - {enrolledCount} fingerprint(s) saved");
                maintips_label.ForeColor = Color.Green;
                maintips_label.Text = $"✅ Captured and saved {enrolledCount} fingerprint(s) for {useridStr}";
            }
            else
            {
                Console.WriteLine("⚠️ Capture completed but no fingerprints were saved");
                maintips_label.ForeColor = Color.Orange;
                maintips_label.Text = "⚠️ Capture completed but no fingerprints were saved";
            }
            // Don't clear images immediately - let user see the results
            // clearCurrentImageShow();
        }

        private void Identify_button_Click(object sender, EventArgs e)
        {
            maintips_label.Text = "";
            byte[] bFeaLI = null;
            byte[] bFeaLT = null;
            byte[] bFeaLM = null;
            byte[] bFeaLR = null;
            byte[] bFeaLL = null;
            byte[] bFeaRI = null;
            byte[] bFeaRT = null;
            byte[] bFeaRM = null;
            byte[] bFeaRR = null;
            byte[] bFeaRL = null;

            if (tabControlMain.SelectedIndex == 1)//rolled
            {
                bFeaLI = bFeatureLI_Rolled;
                bFeaLT = bFeatureLT_Rolled;
                bFeaLM = bFeatureLM_Rolled;
                bFeaLR = bFeatureLR_Rolled;
                bFeaLL = bFeatureLL_Rolled;
                bFeaRI = bFeatureRI_Rolled;
                bFeaRT = bFeatureRT_Rolled;
                bFeaRM = bFeatureRM_Rolled;
                bFeaRR = bFeatureRR_Rolled;
                bFeaRL = bFeatureRL_Rolled;
            }
            else if (tabControlMain.SelectedIndex == 2)//flat
            {
                bFeaLI = bFeatureLI;
                bFeaLT = bFeatureLT;
                bFeaLM = bFeatureLM;
                bFeaLR = bFeatureLR;
                bFeaLL = bFeatureLL;
                bFeaRI = bFeatureRI;
                bFeaRT = bFeatureRT;
                bFeaRM = bFeatureRM;
                bFeaRR = bFeatureRR;
                bFeaRL = bFeatureRL;
            }

            if (bFeaLI == null && bFeaLT == null && bFeaLM == null && bFeaLR == null && bFeaLL == null
                && bFeaRI == null && bFeaRT == null && bFeaRM == null && bFeaRR == null && bFeaRL == null)
            {
                maintips_label.ForeColor = Color.Red;
                maintips_label.Text = "Please capture at least one fingerprint";
                return;
            }

            int nOpType = tabControlMain.SelectedIndex;

            List<IdentifyUser> IdentifyUserSet = new List<IdentifyUser>();

            string connectionString = ConfigManager.GetDatabaseConnectionString();

            using (var conn = new MySqlConnection(connectionString))
            {
                conn.Open();

                string sql = "SELECT UserId, FingerPosition, FingerPositionName, FingerData FROM enrollusers WHERE OperationType = @operationType";
                using (var cmd = new MySqlCommand(sql, conn))
                {
                    cmd.Parameters.AddWithValue("@operationType", nOpType);

                    var enrolledUsers = new List<(string UserId, int FingerPosition, string FingerPositionName, byte[] FingerData)>();
                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            enrolledUsers.Add((
                                reader["UserId"].ToString(),
                                Convert.ToInt32(reader["FingerPosition"]),
                                reader["FingerPositionName"].ToString(),
                                (byte[])reader["FingerData"]
                            ));
                        }
                    }

                    if (enrolledUsers.Count > 0)
                    {
                        if(bFeaLI != null)
                        {
                            foreach(var u in enrolledUsers)
                            {
                                var result = deviceManager.GetDevice().Verify(4, bFeaLI, u.FingerData);
                                if(result.IsMatch)
                                {
                                    IdentifyUser identifyUser = new IdentifyUser();
                                    identifyUser.UserID = u.UserId;
                                    identifyUser.UserName = u.UserId; // UserName removed from DB, using UserId
                                    identifyUser.fingerposition = u.FingerPosition;
                                    identifyUser.FingerPositionName = u.FingerPositionName;
                                    identifyUser.Score = result.Similarity;
                                    identifyUser.IsMatch = result.IsMatch;
                                    IdentifyUserSet.Add(identifyUser);
                                    break;
                                }
                            }
                        }

                        if (bFeaLT != null)
                        {
                            foreach (var u in enrolledUsers)
                            {
                                var result = deviceManager.GetDevice().Verify(4, bFeaLT, u.FingerData);
                                if (result.IsMatch)
                                {
                                    IdentifyUser identifyUser = new IdentifyUser();
                                    identifyUser.UserID = u.UserId;
                                    identifyUser.UserName = u.UserId; // UserName removed from DB, using UserId
                                    identifyUser.fingerposition = u.FingerPosition;
                                    identifyUser.FingerPositionName = u.FingerPositionName;
                                    identifyUser.Score = result.Similarity;
                                    identifyUser.IsMatch = result.IsMatch;
                                    IdentifyUserSet.Add(identifyUser);
                                    break;
                                }
                            }
                        }

                        // Check all captured fingerprints against enrolled users
                        var fingerprintTemplates = new[] {
                            bFeaLM, bFeaLR, bFeaLL, bFeaRI, bFeaRT, bFeaRM, bFeaRR, bFeaRL
                        };

                        foreach (var template in fingerprintTemplates)
                        {
                            if (template != null)
                            {
                                foreach (var u in enrolledUsers)
                                {
                                    var result = deviceManager.GetDevice().Verify(4, template, u.FingerData);
                                    if (result.IsMatch)
                                    {
                                        IdentifyUser identifyUser = new IdentifyUser();
                                        identifyUser.UserID = u.UserId;
                                        identifyUser.UserName = u.UserId; // UserName removed from DB, using UserId
                                        identifyUser.fingerposition = u.FingerPosition;
                                        identifyUser.FingerPositionName = u.FingerPositionName;
                                        identifyUser.Score = result.Similarity;
                                        identifyUser.IsMatch = result.IsMatch;
                                        IdentifyUserSet.Add(identifyUser);
                                        break;
                                    }
                                }
                                if (IdentifyUserSet.Count > 0) break; // Stop after first match
                            }
                        }
                    }
                }

                IdentifyResult resultWindow = new IdentifyResult();
                    resultWindow.StartPosition = FormStartPosition.CenterScreen;
                    resultWindow.Show();
                    resultWindow.TransferIdentifyClosePre += mainTransferIdentifyClosePreEvent;
                    verify_button.Enabled = false;
                    Identify_button.Enabled = false;

                    foreach (var ident in IdentifyUserSet)
                    {
                        string[] row = { "", "", "", "", "" };
                        row[0] = ident.FingerPositionName;
                        row[1] = ident.UserID;
                        row[2] = ident.UserName;
                        row[3] = ident.fingerposition.ToString();
                        row[4] = ident.Score.ToString();
                        resultWindow.TransferResultPre(row);
                    }
                }
            }

        private void verify_button_Click(object sender, EventArgs e)
        {
            maintips_label.Text = "";

            // Get the current user ID from the text box
            string userIdStr = userID_textBox.Text.Trim();
            if (string.IsNullOrEmpty(userIdStr))
            {
                maintips_label.ForeColor = Color.Red;
                maintips_label.Text = "Please enter a User ID for verification";
                return;
            }

            // Get captured fingerprint templates (same logic as Identify)
            byte[] bFeaLI = null;
            byte[] bFeaLT = null;
            byte[] bFeaLM = null;
            byte[] bFeaLR = null;
            byte[] bFeaLL = null;
            byte[] bFeaRI = null;
            byte[] bFeaRT = null;
            byte[] bFeaRM = null;
            byte[] bFeaRR = null;
            byte[] bFeaRL = null;

            if (tabControlMain.SelectedIndex == 1)//rolled
            {
                bFeaLI = bFeatureLI_Rolled;
                bFeaLT = bFeatureLT_Rolled;
                bFeaLM = bFeatureLM_Rolled;
                bFeaLR = bFeatureLR_Rolled;
                bFeaLL = bFeatureLL_Rolled;
                bFeaRI = bFeatureRI_Rolled;
                bFeaRT = bFeatureRT_Rolled;
                bFeaRM = bFeatureRM_Rolled;
                bFeaRR = bFeatureRR_Rolled;
                bFeaRL = bFeatureRL_Rolled;
            }
            else if (tabControlMain.SelectedIndex == 2)//flat
            {
                bFeaLI = bFeatureLI;
                bFeaLT = bFeatureLT;
                bFeaLM = bFeatureLM;
                bFeaLR = bFeatureLR;
                bFeaLL = bFeatureLL;
                bFeaRI = bFeatureRI;
                bFeaRT = bFeatureRT;
                bFeaRM = bFeatureRM;
                bFeaRR = bFeatureRR;
                bFeaRL = bFeatureRL;
            }

            if (bFeaLI == null && bFeaLT == null && bFeaLM == null && bFeaLR == null && bFeaLL == null
                && bFeaRI == null && bFeaRT == null && bFeaRM == null && bFeaRR == null && bFeaRL == null)
            {
                maintips_label.ForeColor = Color.Red;
                maintips_label.Text = "Please capture at least one fingerprint";
                return;
            }

            int nOpType = tabControlMain.SelectedIndex;

            List<IdentifyUser> VerifyUserSet = new List<IdentifyUser>();

            string connectionString = ConfigManager.GetDatabaseConnectionString();

            using (var conn = new MySqlConnection(connectionString))
            {
                conn.Open();

                // Only search for the specific user (1:1 verification)
                string sql = "SELECT UserId, FingerPosition, FingerPositionName, FingerData FROM enrollusers WHERE OperationType = @operationType AND UserId = @userId";
                using (var cmd = new MySqlCommand(sql, conn))
                {
                    cmd.Parameters.AddWithValue("@operationType", nOpType);
                    cmd.Parameters.AddWithValue("@userId", userIdStr);

                    var userTemplates = new List<(string UserId, int FingerPosition, string FingerPositionName, byte[] FingerData)>();
                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            userTemplates.Add((
                                reader["UserId"].ToString(),
                                Convert.ToInt32(reader["FingerPosition"]),
                                reader["FingerPositionName"].ToString(),
                                (byte[])reader["FingerData"]
                            ));
                        }
                    }

                    if (userTemplates.Count > 0)
                    {
                        // Check each captured fingerprint against stored templates for this user
                        var fingerprintTemplates = new[] {
                            bFeaLI, bFeaLT, bFeaLM, bFeaLR, bFeaLL, bFeaRI, bFeaRT, bFeaRM, bFeaRR, bFeaRL
                        };

                        foreach (var template in fingerprintTemplates)
                        {
                            if (template != null)
                            {
                                foreach (var u in userTemplates)
                                {
                                    var result = deviceManager.GetDevice().Verify(4, template, u.FingerData);
                                    if (result.IsMatch)
                                    {
                                        IdentifyUser verifyUser = new IdentifyUser();
                                        verifyUser.UserID = u.UserId;
                                        verifyUser.UserName = u.UserId;
                                        verifyUser.fingerposition = u.FingerPosition;
                                        verifyUser.FingerPositionName = u.FingerPositionName;
                                        verifyUser.Score = result.Similarity;
                                        verifyUser.IsMatch = result.IsMatch;
                                        VerifyUserSet.Add(verifyUser);
                                        break;
                                    }
                                }
                                if (VerifyUserSet.Count > 0) break; // Stop after first match
                            }
                        }
                    }
                }
            }

                if (VerifyUserSet.Count > 0)
                {
                    // Verification successful - show results
                    IdentifyResult resultWindow = new IdentifyResult();
                    resultWindow.StartPosition = FormStartPosition.CenterScreen;
                    resultWindow.Show();
                    resultWindow.TransferIdentifyClosePre += mainTransferIdentifyClosePreEvent;
                    verify_button.Enabled = false;
                    Identify_button.Enabled = false;

                    foreach (var verify in VerifyUserSet)
                    {
                        string[] row = { "", "", "", "", "" };
                        row[0] = verify.FingerPositionName;
                        row[1] = verify.UserID;
                        row[2] = verify.UserName;
                        row[3] = verify.fingerposition.ToString();
                        row[4] = verify.Score.ToString();
                        resultWindow.TransferResultPre(row);
                    }

                    maintips_label.ForeColor = Color.Green;
                    maintips_label.Text = $"✅ Verification successful for {userIdStr}";
                }
                else
                {
                    maintips_label.ForeColor = Color.Red;
                    maintips_label.Text = $"❌ Verification failed for {userIdStr} - No matching fingerprints found";
                }
        }

        private void clearAllImageShow()
        {
            mainleftfour_pictureBox.Image = null;
            maintwothumb_pictureBox.Image = null;
            mainrightfour_pictureBox.Image = null;

            bFeatureLI = null;
            bFeatureLT = null;
            bFeatureLM = null;
            bFeatureLR = null;
            bFeatureLL = null;
            bFeatureRI = null;
            bFeatureRT = null;
            bFeatureRM = null;
            bFeatureRR = null;
            bFeatureRL = null;

            bFeatureLI_Rolled = null;
            bFeatureLT_Rolled = null;
            bFeatureLM_Rolled = null;
            bFeatureLR_Rolled = null;
            bFeatureLL_Rolled = null;
            bFeatureRI_Rolled = null;
            bFeatureRT_Rolled = null;
            bFeatureRM_Rolled = null;
            bFeatureRR_Rolled = null;
            bFeatureRL_Rolled = null;

            rolledrightthumb_pictureBox.Image = null;
            rolledleftlittle_pictureBox.Image = null;
            rolledleftring_pictureBox.Image = null;
            rolledleftmiddle_pictureBox.Image = null;
            rolledleftindex_pictureBox.Image = null;
            rolledleftthumb_pictureBox.Image = null;
            rolledrightlittle_pictureBox.Image = null;
            rolledrightring_pictureBox.Image = null;
            rolledrightmiddle_pictureBox.Image = null;
            rolledrightindex_pictureBox.Image = null;
            
            flatrightlittle_pictureBox.Image = null;
            flatrightring_pictureBox.Image = null;
            flatrightmiddle_pictureBox.Image = null;
            flatrightindex_pictureBox.Image = null;
            flatrightthumb_pictureBox.Image = null;
            flatleftlittle_pictureBox.Image = null;
            flatleftring_pictureBox.Image = null;
            flatleftmiddle_pictureBox.Image = null;
            flatleftindex_pictureBox.Image = null;
            flatleftthumb_pictureBox.Image = null;
        }

        private void clearCurrentImageShow()
        {
            switch(tabControlMain.SelectedIndex)
            {
                case 0:
                {
                    mainleftfour_pictureBox.Image = null;
                    maintwothumb_pictureBox.Image = null;
                    mainrightfour_pictureBox.Image = null;
                    break;
                }
                case 1:
                {
                    bFeatureLI_Rolled = null;
                    bFeatureLT_Rolled = null;
                    bFeatureLM_Rolled = null;
                    bFeatureLR_Rolled = null;
                    bFeatureLL_Rolled = null;
                    bFeatureRI_Rolled = null;
                    bFeatureRT_Rolled = null;
                    bFeatureRM_Rolled = null;
                    bFeatureRR_Rolled = null;
                    bFeatureRL_Rolled = null;

                    rolledrightthumb_pictureBox.Image = null;
                    rolledleftlittle_pictureBox.Image = null;
                    rolledleftring_pictureBox.Image = null;
                    rolledleftmiddle_pictureBox.Image = null;
                    rolledleftindex_pictureBox.Image = null;
                    rolledleftthumb_pictureBox.Image = null;
                    rolledrightlittle_pictureBox.Image = null;
                    rolledrightring_pictureBox.Image = null;
                    rolledrightmiddle_pictureBox.Image = null;
                    rolledrightindex_pictureBox.Image = null;

                    label_NFIQ_LT_ROLL.Text = "NFIQ=N/A";
                    label_NFIQ_LI_ROLL.Text = "NFIQ=N/A";
                    label_NFIQ_LM_ROLL.Text = "NFIQ=N/A";
                    label_NFIQ_LR_ROLL.Text = "NFIQ=N/A";
                    label_NFIQ_LL_ROLL.Text = "NFIQ=N/A";

                    label_NFIQ_RT_ROLL.Text = "NFIQ=N/A";
                    label_NFIQ_RI_ROLL.Text = "NFIQ=N/A";
                    label_NFIQ_RM_ROLL.Text = "NFIQ=N/A";
                    label_NFIQ_RR_ROLL.Text = "NFIQ=N/A";
                    label_NFIQ_RL_ROLL.Text = "NFIQ=N/A";

                    break;
                }
                case 2:
                {
                    bFeatureLI = null;
                    bFeatureLT = null;
                    bFeatureLM = null;
                    bFeatureLR = null;
                    bFeatureLL = null;
                    bFeatureRI = null;
                    bFeatureRT = null;
                    bFeatureRM = null;
                    bFeatureRR = null;
                    bFeatureRL = null;

                    flatrightlittle_pictureBox.Image = null;
                    flatrightring_pictureBox.Image = null;
                    flatrightmiddle_pictureBox.Image = null;
                    flatrightindex_pictureBox.Image = null;
                    flatrightthumb_pictureBox.Image = null;
                    flatleftlittle_pictureBox.Image = null;
                    flatleftring_pictureBox.Image = null;
                    flatleftmiddle_pictureBox.Image = null;
                    flatleftindex_pictureBox.Image = null;
                    flatleftthumb_pictureBox.Image = null;

                    label_NFIQ_LT_FLAT.Text = "NFIQ=N/A";
                    label_NFIQ_LI_FLAT.Text = "NFIQ=N/A";
                    label_NFIQ_LM_FLAT.Text = "NFIQ=N/A";
                    label_NFIQ_LR_FLAT.Text = "NFIQ=N/A";
                    label_NFIQ_LL_FLAT.Text = "NFIQ=N/A";

                    label_NFIQ_RT_FLAT.Text = "NFIQ=N/A";
                    label_NFIQ_RI_FLAT.Text = "NFIQ=N/A";
                    label_NFIQ_RM_FLAT.Text = "NFIQ=N/A";
                    label_NFIQ_RR_FLAT.Text = "NFIQ=N/A";
                    label_NFIQ_RL_FLAT.Text = "NFIQ=N/A";

                    break;
                }
            }
        }

        private void clear_button_Click(object sender, EventArgs e)
        {
            clearCurrentImageShow();
        }

        private void setFingerCheckBoxStatus(int finger, bool status)
        {
            switch (finger)
            {
                case 1://RightThumb
                    R1_checkBox.Checked = status;
                    break;
                case 2://RightIndex
                    R2_checkBox.Checked = status;
                    break;
                case 3://RightMiddle
                    R3_checkBox.Checked = status;
                    break;
                case 4://RightRing
                    R4_checkBox.Checked = status;
                    break;
                case 5://RightLittle
                    R5_checkBox.Checked = status;
                    break;
                case 6://LeftThumb
                    L1_checkBox.Checked = status;
                    break;
                case 7://LeftIndex
                    L2_checkBox.Checked = status;
                    break;
                case 8://LeftMiddle
                    L3_checkBox.Checked = status;
                    break;
                case 9://LeftRing
                    L4_checkBox.Checked = status;
                    break;
                case 10://LeftLittle
                    L5_checkBox.Checked = status;
                    break;
            }
        }

        private void L5_checkBox_CheckedChanged(object sender, EventArgs e)
        {
            if (tabControlMain.SelectedIndex == 1)//roll, only select one finger
            {
                if (L5_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;
                    nSelectFinger1 = (int)CapFingerPosition.LeftLittle;
                }
                else
                    nSelectFinger1 = 0;
            }
            else
            {
                if (L5_checkBox.Checked)
                {
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;

                    if (nSelectFinger1 == 0)
                    {
                        nSelectFinger1 = (int)CapFingerPosition.LeftLittle;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectFinger2 == 0)
                    {
                        nSelectFinger2 = (int)CapFingerPosition.LeftLittle;
                        nSelectCount = 0;
                        return;
                    }

                    if (nSelectCount == 0)
                    {
                        setFingerCheckBoxStatus(nSelectFinger1, false);
                        nSelectFinger1 = (int)CapFingerPosition.LeftLittle;
                        nSelectCount = 1;
                        return;
                    }

                    if (nSelectCount == 1)
                    {
                        setFingerCheckBoxStatus(nSelectFinger2, false);
                        nSelectFinger2 = (int)CapFingerPosition.LeftLittle;
                        nSelectCount = 0;
                    }
                }
                else
                {
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftLittle)
                    {
                        nSelectFinger1 = 0;
                        nSelectCount = 0;
                    }
                    if (nSelectFinger2 == (int)CapFingerPosition.LeftLittle)
                    {
                        nSelectFinger2 = 0;
                        nSelectCount = 1;
                    }
                }
            }
        }

        private void L4_checkBox_CheckedChanged(object sender, EventArgs e)
        {
            if (tabControlMain.SelectedIndex == 1)//roll, only select one finger
            {
                if (L4_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L5_checkBox.Checked = false;
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;
                    nSelectFinger1 = (int)CapFingerPosition.LeftRing;
                }
                else
                    nSelectFinger1 = 0;
            }
            else
            {
                if (L4_checkBox.Checked)
                {
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;

                    if (nSelectFinger1 == 0)
                    {
                        nSelectFinger1 = (int)CapFingerPosition.LeftRing;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectFinger2 == 0)
                    {
                        nSelectFinger2 = (int)CapFingerPosition.LeftRing;
                        nSelectCount = 0;
                        return;
                    }

                    if (nSelectCount == 0)
                    {
                        setFingerCheckBoxStatus(nSelectFinger1, false);
                        nSelectFinger1 = (int)CapFingerPosition.LeftRing;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectCount == 1)
                    {
                        setFingerCheckBoxStatus(nSelectFinger2, false);
                        nSelectFinger2 = (int)CapFingerPosition.LeftRing;
                        nSelectCount = 0;
                    }
                }
                else
                {
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftRing)
                    {
                        nSelectFinger1 = 0;
                        nSelectCount = 0;
                    }
                    if (nSelectFinger2 == (int)CapFingerPosition.LeftRing)
                    {
                        nSelectFinger2 = 0;
                        nSelectCount = 1;
                    }
                }
            }
        }

        private void L3_checkBox_CheckedChanged(object sender, EventArgs e)
        {
            if (tabControlMain.SelectedIndex == 1)//roll, only select one finger
            {
                if (L3_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;
                    nSelectFinger1 = (int)CapFingerPosition.LeftMiddle;
                }
                else
                    nSelectFinger1 = 0;
            }
            else
            {
                if (L3_checkBox.Checked)
                {
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;

                    if (nSelectFinger1 == 0)
                    {
                        nSelectFinger1 = (int)CapFingerPosition.LeftMiddle;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectFinger2 == 0)
                    {
                        nSelectFinger2 = (int)CapFingerPosition.LeftMiddle;
                        nSelectCount = 0;
                        return;
                    }

                    if (nSelectCount == 0)
                    {
                        setFingerCheckBoxStatus(nSelectFinger1, false);
                        nSelectFinger1 = (int)CapFingerPosition.LeftMiddle;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectCount == 1)
                    {
                        setFingerCheckBoxStatus(nSelectFinger2, false);
                        nSelectFinger2 = (int)CapFingerPosition.LeftMiddle;
                        nSelectCount = 0;
                    }
                }
                else
                {
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftMiddle)
                    {
                        nSelectFinger1 = 0;
                        nSelectCount = 0;
                    }
                    if (nSelectFinger2 == (int)CapFingerPosition.LeftMiddle)
                    {
                        nSelectFinger2 = 0;
                        nSelectCount = 1;
                    }
                }
            }
        }

        private void L2_checkBox_CheckedChanged(object sender, EventArgs e)
        {
            if (tabControlMain.SelectedIndex == 1)//roll, only select one finger
            {
                if (L2_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;
                    nSelectFinger1 = (int)CapFingerPosition.LeftIndex;
                }
                else
                    nSelectFinger1 = 0;
            }
            else
            {
                if (L2_checkBox.Checked)
                {
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;

                    if (nSelectFinger1 == 0)
                    {
                        nSelectFinger1 = (int)CapFingerPosition.LeftIndex;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectFinger2 == 0)
                    {
                        nSelectFinger2 = (int)CapFingerPosition.LeftIndex;
                        nSelectCount = 0;
                        return;
                    }

                    if (nSelectCount == 0)
                    {
                        setFingerCheckBoxStatus(nSelectFinger1, false);
                        nSelectFinger1 = (int)CapFingerPosition.LeftIndex;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectCount == 1)
                    {
                        setFingerCheckBoxStatus(nSelectFinger2, false);
                        nSelectFinger2 = (int)CapFingerPosition.LeftIndex;
                        nSelectCount = 0;
                    }
                }
                else
                {
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftIndex)
                    {
                        nSelectFinger1 = 0;
                        nSelectCount = 0;
                    }
                    if (nSelectFinger2 == (int)CapFingerPosition.LeftIndex)
                    {
                        nSelectFinger2 = 0;
                        nSelectCount = 1;
                    }
                }
            }
        }

        private void L1_checkBox_CheckedChanged(object sender, EventArgs e)
        {
            if (tabControlMain.SelectedIndex == 1)//roll, only select one finger
            {
                if (L1_checkBox.Checked)
                {
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;
                    nSelectFinger1 = (int)CapFingerPosition.LeftThumb;
                }
                else
                    nSelectFinger1 = 0;
            }
            else
            {
                if (L1_checkBox.Checked)
                {
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;

                    if (nSelectFinger1 == 0)
                    {
                        nSelectFinger1 = (int)CapFingerPosition.LeftThumb;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectFinger2 == 0)
                    {
                        nSelectFinger2 = (int)CapFingerPosition.LeftThumb;
                        nSelectCount = 0;
                        return;
                    }

                    if (nSelectCount == 0)
                    {
                        setFingerCheckBoxStatus(nSelectFinger1, false);
                        nSelectFinger1 = (int)CapFingerPosition.LeftThumb;
                        nSelectCount = 1;
                        return;
                    }

                    if (nSelectCount == 1)
                    {
                        setFingerCheckBoxStatus(nSelectFinger2, false);
                        nSelectFinger2 = (int)CapFingerPosition.LeftThumb;
                        nSelectCount = 0;
                    }
                }
                else
                {
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftThumb)
                    {
                        nSelectFinger1 = 0;
                        nSelectCount = 0;
                    }
                    if (nSelectFinger2 == (int)CapFingerPosition.LeftThumb)
                    {
                        nSelectFinger2 = 0;
                        nSelectCount = 1;
                    }
                }
            }
            
        }

        private void R1_checkBox_CheckedChanged(object sender, EventArgs e)
        {
            if (tabControlMain.SelectedIndex == 1)//roll, only select one finger
            {
                if (R1_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    nSelectFinger1 = (int)CapFingerPosition.RightThumb;
                }
                else
                    nSelectFinger1 = 0;
            }
            else
            {
                if (R1_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;

                    if (nSelectFinger1 == 0)
                    {
                        nSelectFinger1 = (int)CapFingerPosition.RightThumb;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectFinger2 == 0)
                    {
                        nSelectFinger2 = (int)CapFingerPosition.RightThumb;
                        nSelectCount = 0;
                        return;
                    }

                    if (nSelectCount == 0)
                    {
                        setFingerCheckBoxStatus(nSelectFinger1, false);
                        nSelectFinger1 = (int)CapFingerPosition.RightThumb;
                        nSelectCount = 1;
                        return;
                    }

                    if (nSelectCount == 1)
                    {
                        setFingerCheckBoxStatus(nSelectFinger2, false);
                        nSelectFinger2 = (int)CapFingerPosition.RightThumb;
                        nSelectCount = 0;
                    }

                }
                else
                {
                    if (nSelectFinger1 == (int)CapFingerPosition.RightThumb)
                    {
                        nSelectFinger1 = 0;
                        nSelectCount = 0;
                    }
                    if (nSelectFinger2 == (int)CapFingerPosition.RightThumb)
                    {
                        nSelectFinger2 = 0;
                        nSelectCount = 1;
                    }
                }
            }
        }

        private void R2_checkBox_CheckedChanged(object sender, EventArgs e)
        {
            if (tabControlMain.SelectedIndex == 1)//roll, only select one finger
            {
                if (R2_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R1_checkBox.Checked = false;
                    nSelectFinger1 = (int)CapFingerPosition.RightIndex;
                }
                else
                    nSelectFinger1 = 0;
            }
            else
            {
                if (R2_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;

                    if (nSelectFinger1 == 0)
                    {
                        nSelectFinger1 = (int)CapFingerPosition.RightIndex;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectFinger2 == 0)
                    {
                        nSelectFinger2 = (int)CapFingerPosition.RightIndex;
                        nSelectCount = 0;
                        return;
                    }

                    if (nSelectCount == 0)
                    {
                        setFingerCheckBoxStatus(nSelectFinger1, false);
                        nSelectFinger1 = (int)CapFingerPosition.RightIndex;
                        nSelectCount = 1;
                        return;
                    }

                    if (nSelectCount == 1)
                    {
                        setFingerCheckBoxStatus(nSelectFinger2, false);
                        nSelectFinger2 = (int)CapFingerPosition.RightIndex;
                        nSelectCount = 0;
                    }

                }
                else
                {
                    if (nSelectFinger1 == (int)CapFingerPosition.RightIndex)
                    {
                        nSelectFinger1 = 0;
                        nSelectCount = 0;
                    }
                    if (nSelectFinger2 == (int)CapFingerPosition.RightIndex)
                    {
                        nSelectFinger2 = 0;
                        nSelectCount = 1;
                    }
                }
            }
        }

        private void R3_checkBox_CheckedChanged(object sender, EventArgs e)
        {
            if (tabControlMain.SelectedIndex == 1)//roll, only select one finger
            {
                if (R3_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;
                    R5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;
                    nSelectFinger1 = (int)CapFingerPosition.RightMiddle;
                }
                else
                    nSelectFinger1 = 0;
            }
            else
            {
                if (R3_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;

                    if (nSelectFinger1 == 0)
                    {
                        nSelectFinger1 = (int)CapFingerPosition.RightMiddle;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectFinger2 == 0)
                    {
                        nSelectFinger2 = (int)CapFingerPosition.RightMiddle;
                        nSelectCount = 0;
                        return;
                    }

                    if (nSelectCount == 0)
                    {
                        setFingerCheckBoxStatus(nSelectFinger1, false);
                        nSelectFinger1 = (int)CapFingerPosition.RightMiddle;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectCount == 1)
                    {
                        setFingerCheckBoxStatus(nSelectFinger2, false);
                        nSelectFinger2 = (int)CapFingerPosition.RightMiddle;
                        nSelectCount = 0;
                    }

                }
                else
                {
                    if (nSelectFinger1 == (int)CapFingerPosition.RightMiddle)
                    {
                        nSelectFinger1 = 0;
                        nSelectCount = 0;
                    }
                    if (nSelectFinger2 == (int)CapFingerPosition.RightMiddle)
                    {
                        nSelectFinger2 = 0;
                        nSelectCount = 1;
                    }
                }
            }
        }

        private void R4_checkBox_CheckedChanged(object sender, EventArgs e)
        {
            if (tabControlMain.SelectedIndex == 1)//roll, only select one finger
            {
                if (R4_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;
                    R5_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;
                    nSelectFinger1 = (int)CapFingerPosition.RightRing;
                }
                else
                    nSelectFinger1 = 0;
            }
            else
            {
                if (R4_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;

                    if (nSelectFinger1 == 0)
                    {
                        nSelectFinger1 = (int)CapFingerPosition.RightRing;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectFinger2 == 0)
                    {
                        nSelectFinger2 = (int)CapFingerPosition.RightRing;
                        nSelectCount = 0;
                        return;
                    }

                    if (nSelectCount == 0)
                    {
                        setFingerCheckBoxStatus(nSelectFinger1, false);
                        nSelectFinger1 = (int)CapFingerPosition.RightRing;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectCount == 1)
                    {
                        setFingerCheckBoxStatus(nSelectFinger2, false);
                        nSelectFinger2 = (int)CapFingerPosition.RightRing;
                        nSelectCount = 0;
                    }

                }
                else
                {
                    if (nSelectFinger1 == (int)CapFingerPosition.RightRing)
                    {
                        nSelectFinger1 = 0;
                        nSelectCount = 0;
                    }
                    if (nSelectFinger2 == (int)CapFingerPosition.RightRing)
                    {
                        nSelectFinger2 = 0;
                        nSelectCount = 1;
                    }
                }
            }
        }

        private void R5_checkBox_CheckedChanged(object sender, EventArgs e)
        {
            if (tabControlMain.SelectedIndex == 1)//roll, only select one finger
            {
                if (R5_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;
                    R4_checkBox.Checked = false;
                    R3_checkBox.Checked = false;
                    R2_checkBox.Checked = false;
                    R1_checkBox.Checked = false;
                    nSelectFinger1 = (int)CapFingerPosition.RightLittle;
                }
                else
                    nSelectFinger1 = 0;
            }
            else
            {
                if (R5_checkBox.Checked)
                {
                    L1_checkBox.Checked = false;
                    L2_checkBox.Checked = false;
                    L3_checkBox.Checked = false;
                    L4_checkBox.Checked = false;
                    L5_checkBox.Checked = false;

                    if (nSelectFinger1 == 0)
                    {
                        nSelectFinger1 = (int)CapFingerPosition.RightLittle;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectFinger2 == 0)
                    {
                        nSelectFinger2 = (int)CapFingerPosition.RightLittle;
                        nSelectCount = 0;
                        return;
                    }

                    if (nSelectCount == 0)
                    {
                        setFingerCheckBoxStatus(nSelectFinger1, false);
                        nSelectFinger1 = (int)CapFingerPosition.RightLittle;
                        nSelectCount = 1;
                        return;
                    }
                    if (nSelectCount == 1)
                    {
                        setFingerCheckBoxStatus(nSelectFinger2, false);
                        nSelectFinger2 = (int)CapFingerPosition.RightLittle;
                        nSelectCount = 0;
                    }

                }
                else
                {
                    if (nSelectFinger1 == (int)CapFingerPosition.RightLittle)
                    {
                        nSelectFinger1 = 0;
                        nSelectCount = 0;
                    }
                    if (nSelectFinger2 == (int)CapFingerPosition.RightLittle)
                    {
                        nSelectFinger2 = 0;
                        nSelectCount = 1;
                    }
                }
            }
        }

        private void userID_textBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if ((e.KeyChar >= 'a' && e.KeyChar <= 'z') || (e.KeyChar >= 'A' && e.KeyChar <= 'Z')
               || (e.KeyChar >= '0' && e.KeyChar <= '9') || (e.KeyChar == 8))
            {
                e.Handled = false;
            }
            else
            {
                e.Handled = true;
            }
        }

        private void username_textBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if ((e.KeyChar >= 'a' && e.KeyChar <= 'z') || (e.KeyChar >= 'A' && e.KeyChar <= 'Z')
               || (e.KeyChar >= '0' && e.KeyChar <= '9') || (e.KeyChar == 8))
            {
                e.Handled = false;
            }
            else
            {
                e.Handled = true;
            }
        }

        private void MainForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            if(capWindow != null)
                capWindow.Close();
        }

        private void cleardb_button_Click(object sender, EventArgs e)
        {
            try
            {
                string connectionString = ConfigManager.GetDatabaseConnectionString();

                using (var conn = new MySqlConnection(connectionString))
                {
                    conn.Open();

                    // Check if there are any enrolled users
                    string countSql = "SELECT COUNT(*) FROM enrollusers";
                    using (var countCmd = new MySqlCommand(countSql, conn))
                    {
                        int userCount = Convert.ToInt32(countCmd.ExecuteScalar());
                        if (userCount > 0)
                        {
                            // For bridge application, auto-confirm without MessageBox
                            Console.WriteLine($"Clearing {userCount} enrolled fingerprints from database...");

                            string deleteSql = "DELETE FROM enrollusers";
                            using (var deleteCmd = new MySqlCommand(deleteSql, conn))
                            {
                                int deletedRows = deleteCmd.ExecuteNonQuery();
                                Console.WriteLine($"✅ Successfully cleared {deletedRows} fingerprint records from database");
                            }
                        }
                        else
                        {
                            Console.WriteLine("No enrolled fingerprints found in database");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error clearing database: {ex.Message}");
            }
        }

        /*
        int ndex = 0;
        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            if (m.Msg == UsbNotification.WmDevicechange)
            {
                switch ((int)m.WParam)
                {
                    case UsbNotification.DbtDeviceremovecomplete:
                        {
                            if (ndex == 0 && deviceManager.GetDevice() != null)
                            {
                                if(capWindow != null)
                                    capWindow.Close();
                                Thread.Sleep(1000);
                                opencloseDevice_button_Click(null, null);
                            }

                            ndex++;
                            if (ndex >= 1)
                                ndex = 0;
                        }

                        break;
                    case UsbNotification.DbtDevicearrival:
                        {
                            if(ndex == 0)
                            {
                                Thread.Sleep(2000);
                                opencloseDevice_button_Click(null, null);
                            }
                            
                            ndex++;
                            if (ndex >= 1)
                                ndex = 0;
                        }
                        //MessageBox.Show("add");
                        break;
                }
            }
        }*/
    }

    internal class UsbNotification
    {
        public const int DbtDevicearrival = 0x8000; // system detected a new device        
        public const int DbtDeviceremovecomplete = 0x8004; // device is gone      
        public const int WmDevicechange = 0x0219; // device change event      
        private const int DbtDevtypDeviceinterface = 5;//76228c66-9584-42d3-9632-ebad0a230d13
        private static readonly Guid GuidDevinterfaceUSBDevice = new Guid("76228C66-9584-42D3-9632-EBAD0A230D13"); // USB devices
        private static IntPtr notificationHandle;

        /// <summary>
        /// Registers a window to receive notifications when USB devices are plugged or unplugged.
        /// </summary>
        /// <param name="windowHandle">Handle to the window receiving notifications.</param>
        public static void RegisterUsbDeviceNotification(IntPtr windowHandle)
        {
            DevBroadcastDeviceinterface dbi = new DevBroadcastDeviceinterface
            {
                DeviceType = DbtDevtypDeviceinterface,
                Reserved = 0,
                ClassGuid = GuidDevinterfaceUSBDevice,
                Name = 0
            };

            dbi.Size = Marshal.SizeOf(dbi);
            IntPtr buffer = Marshal.AllocHGlobal(dbi.Size);
            Marshal.StructureToPtr(dbi, buffer, true);

            notificationHandle = RegisterDeviceNotification(windowHandle, buffer, 0);
        }

        /// <summary>
        /// Unregisters the window for USB device notifications
        /// </summary>
        public static void UnregisterUsbDeviceNotification()
        {
            UnregisterDeviceNotification(notificationHandle);
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr RegisterDeviceNotification(IntPtr recipient, IntPtr notificationFilter, int flags);

        [DllImport("user32.dll")]
        private static extern bool UnregisterDeviceNotification(IntPtr handle);

        [StructLayout(LayoutKind.Sequential)]
        private struct DevBroadcastDeviceinterface
        {
            internal int Size;
            internal int DeviceType;
            internal int Reserved;
            internal Guid ClassGuid;
            internal short Name;
        }


    }
}
