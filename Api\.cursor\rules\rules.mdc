---
description:
globs:
alwaysApply: true
---

### ✅ Task: Interactive Task Loop with User Feedback

1. **Check if `userinput.py` exists** in the root directory.

   - If it doesn't exist, create it with the following content:

     ```python
     # userinput.py
     user_input = input("prompt: ")
     ```

2. **Main Workflow**:

   - Perform your assigned tasks.

   - Run:

     ```bash
     python userinput.py
     ```

   - The terminal should be opened in the chat window itself.

   - Read the user's input.

   - Based on the input, perform the next set of tasks.

   - Repeat the process.

3. **Exit Condition**:

   - If the user enters `"stop"` when prompted, exit the loop and terminate the process.
