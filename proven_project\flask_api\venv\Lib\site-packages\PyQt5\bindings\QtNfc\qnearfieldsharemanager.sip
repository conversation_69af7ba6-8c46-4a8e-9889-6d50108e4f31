// qnearfieldsharemanager.sip generated by MetaSIP
//
// This file is part of the QtNfc Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QNearFieldShareManager : public QObject
{
%TypeHeaderCode
#include <qnearfieldsharemanager.h>
%End

public:
    explicit QNearFieldShareManager(QObject *parent /TransferThis/ = 0);
    virtual ~QNearFieldShareManager();

    enum ShareError
    {
        NoError,
        UnknownError,
        InvalidShareContentError,
        ShareCanceledError,
        ShareInterruptedError,
        ShareRejectedError,
        UnsupportedShareModeError,
        ShareAlreadyInProgressError,
        SharePermissionDeniedError,
    };

    enum ShareMode
    {
        NoShare,
        NdefShare,
        FileShare,
    };

    typedef QFlags<QNearFieldShareManager::ShareMode> ShareModes;
    static QNearFieldShareManager::ShareModes supportedShareModes();
    void setShareModes(QNearFieldShareManager::ShareModes modes);
    QNearFieldShareManager::ShareModes shareModes() const;
    QNearFieldShareManager::ShareError shareError() const;

signals:
    void targetDetected(QNearFieldShareTarget *shareTarget);
    void shareModesChanged(QNearFieldShareManager::ShareModes modes);
    void error(QNearFieldShareManager::ShareError error);
};

%End
%If (Qt_5_5_0 -)
QFlags<QNearFieldShareManager::ShareMode> operator|(QNearFieldShareManager::ShareMode f1, QFlags<QNearFieldShareManager::ShareMode> f2);
%End
