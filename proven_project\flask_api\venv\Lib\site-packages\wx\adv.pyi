# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
The ``wx.adv`` module contains classes which are more advanced and/or less
commonly used than those in the core namespace. They are provided in a
separate module to help reduce overhead and dependencies for those
applications which do not need any of these classes.
"""
#-- begin-_adv --#

import wx
#-- end-_adv --#
#-- begin-aboutdlg --#

class AboutDialogInfo:
    """
    AboutDialogInfo() -> None
    
    wxAboutDialogInfo contains information shown in the standard About
    dialog displayed by the wxAboutBox() function.
    """

    def __init__(self) -> None:
        """
        AboutDialogInfo() -> None
        
        wxAboutDialogInfo contains information shown in the standard About
        dialog displayed by the wxAboutBox() function.
        """

    def AddArtist(self, artist: str) -> None:
        """
        AddArtist(artist) -> None
        
        Adds an artist name to be shown in the program credits.
        """

    def AddDeveloper(self, developer: str) -> None:
        """
        AddDeveloper(developer) -> None
        
        Adds a developer name to be shown in the program credits.
        """

    def AddDocWriter(self, docwriter: str) -> None:
        """
        AddDocWriter(docwriter) -> None
        
        Adds a documentation writer name to be shown in the program credits.
        """

    def AddTranslator(self, translator: str) -> None:
        """
        AddTranslator(translator) -> None
        
        Adds a translator name to be shown in the program credits.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Get the name of the program.
        """

    def HasDescription(self) -> bool:
        """
        HasDescription() -> bool
        
        Returns true if a description string has been specified.
        """

    def GetDescription(self) -> str:
        """
        GetDescription() -> str
        
        Get the description string.
        """

    def HasCopyright(self) -> bool:
        """
        HasCopyright() -> bool
        
        Returns true if a copyright string has been specified.
        """

    def GetCopyright(self) -> str:
        """
        GetCopyright() -> str
        
        Get the copyright string.
        """

    def SetArtists(self, artists: List[str]) -> None:
        """
        SetArtists(artists) -> None
        
        Sets the list of artists to be shown in the program credits.
        """

    def SetCopyright(self, copyright: str) -> None:
        """
        SetCopyright(copyright) -> None
        
        Set the short string containing the program copyright information.
        """

    def SetDescription(self, desc: str) -> None:
        """
        SetDescription(desc) -> None
        
        Set brief, but possibly multiline, description of the program.
        """

    def SetDevelopers(self, developers: List[str]) -> None:
        """
        SetDevelopers(developers) -> None
        
        Set the list of developers of the program.
        """

    def SetDocWriters(self, docwriters: List[str]) -> None:
        """
        SetDocWriters(docwriters) -> None
        
        Set the list of documentation writers.
        """

    def HasIcon(self) -> bool:
        """
        HasIcon() -> bool
        
        Returns true if an icon has been set for the about dialog.
        """

    def GetIcon(self) -> wx.Icon:
        """
        GetIcon() -> wx.Icon
        
        Returns the icon set by SetIcon().
        """

    def SetIcon(self, icon: wx.Icon) -> None:
        """
        SetIcon(icon) -> None
        
        Set the icon to be shown in the dialog.
        """

    def HasLicence(self) -> bool:
        """
        HasLicence() -> bool
        
        Returns true if the licence string has been set.
        """

    def GetLicence(self) -> str:
        """
        GetLicence() -> str
        
        Returns the licence string.
        """

    def SetLicence(self, licence: str) -> None:
        """
        SetLicence(licence) -> None
        
        Set the long, multiline string containing the text of the program
        licence.
        """

    def SetLicense(self, licence: str) -> None:
        """
        SetLicense(licence) -> None
        
        This is the same as SetLicence().
        """

    def SetName(self, name: str) -> None:
        """
        SetName(name) -> None
        
        Set the name of the program.
        """

    def SetTranslators(self, translators: List[str]) -> None:
        """
        SetTranslators(translators) -> None
        
        Set the list of translators.
        """

    def SetVersion(self, version: str, longVersion: str="") -> None:
        """
        SetVersion(version, longVersion="") -> None
        
        Set the version of the program.
        """

    def GetVersion(self) -> str:
        """
        GetVersion() -> str
        
        Return the short version string.
        """

    def GetLongVersion(self) -> str:
        """
        GetLongVersion() -> str
        
        Return the long version string if set.
        """

    def HasWebSite(self) -> bool:
        """
        HasWebSite() -> bool
        
        Returns true if the website info has been set.
        """

    def GetWebSiteURL(self) -> str:
        """
        GetWebSiteURL() -> str
        
        Returns the website URL set for the dialog.
        """

    def GetWebSiteDescription(self) -> str:
        """
        GetWebSiteDescription() -> str
        
        Returns the description of the website URL set for the dialog.
        """

    def SetWebSite(self, url: str, desc: str='') -> None:
        """
        SetWebSite(url, desc='') -> None
        
        Set the web site for the program and its description (which defaults
        to url itself if empty).
        """

    def HasDevelopers(self) -> bool:
        """
        HasDevelopers() -> bool
        
        Returns true if developers have been set in the dialog info.
        """

    def GetDevelopers(self) -> List[str]:
        """
        GetDevelopers() -> List[str]
        
        Returns an array of the developer strings set in the dialog info.
        """

    def HasDocWriters(self) -> bool:
        """
        HasDocWriters() -> bool
        
        Returns true if writers have been set in the dialog info.
        """

    def GetDocWriters(self) -> List[str]:
        """
        GetDocWriters() -> List[str]
        
        Returns an array of the writer strings set in the dialog info.
        """

    def HasArtists(self) -> bool:
        """
        HasArtists() -> bool
        
        Returns true if artists have been set in the dialog info.
        """

    def GetArtists(self) -> List[str]:
        """
        GetArtists() -> List[str]
        
        Returns an array of the artist strings set in the dialog info.
        """

    def HasTranslators(self) -> bool:
        """
        HasTranslators() -> bool
        
        Returns true if translators have been set in the dialog info.
        """

    def GetTranslators(self) -> List[str]:
        """
        GetTranslators() -> List[str]
        
        Returns an array of the translator strings set in the dialog info.
        """
    @property
    def Artists(self) -> List[str]: ...
    @Artists.setter
    def Artists(self, value: List[str], /) -> None: ...
    @property
    def Copyright(self) -> str: ...
    @Copyright.setter
    def Copyright(self, value: str, /) -> None: ...
    @property
    def Description(self) -> str: ...
    @Description.setter
    def Description(self, value: str, /) -> None: ...
    @property
    def Developers(self) -> List[str]: ...
    @Developers.setter
    def Developers(self, value: List[str], /) -> None: ...
    @property
    def DocWriters(self) -> List[str]: ...
    @DocWriters.setter
    def DocWriters(self, value: List[str], /) -> None: ...
    @property
    def Icon(self) -> wx.Icon: ...
    @Icon.setter
    def Icon(self, value: wx.Icon, /) -> None: ...
    @property
    def Licence(self) -> str: ...
    @Licence.setter
    def Licence(self, value: str, /) -> None: ...
    @property
    def LongVersion(self) -> str: ...
    @property
    def Name(self) -> str: ...
    @Name.setter
    def Name(self, value: str, /) -> None: ...
    @property
    def Translators(self) -> List[str]: ...
    @Translators.setter
    def Translators(self, value: List[str], /) -> None: ...
    @property
    def Version(self) -> str: ...
    @Version.setter
    def Version(self, value: str, /) -> None: ...
    @property
    def WebSiteDescription(self) -> str: ...
    @property
    def WebSiteURL(self) -> str: ...

    HasLicense = HasLicence
    GetLicense = GetLicence
    License = Licence
# end of class AboutDialogInfo


def AboutBox(info: AboutDialogInfo, parent: Optional[wx.Window]=None) -> None:    """
    AboutBox(info, parent=None) -> None
    
    This function shows the standard about dialog containing the
    information specified in info.
    """

def GenericAboutBox(info: AboutDialogInfo, parent: Optional[wx.Window]=None) -> None:    """
    GenericAboutBox(info, parent=None) -> None
    
    This function does the same thing as wxAboutBox() except that it
    always uses the generic wxWidgets version of the dialog instead of the
    native one.
    """
#-- end-aboutdlg --#
#-- begin-helpext --#

class ExtHelpController(wx.HelpControllerBase):
    """
    ExtHelpController(parentWindow=None) -> None
    
    This class implements help via an external browser.
    """

    def __init__(self, parentWindow: Optional[wx.Window]=None) -> None:
        """
        ExtHelpController(parentWindow=None) -> None
        
        This class implements help via an external browser.
        """

    def SetViewer(self, viewer: str='', flags: int=wx.HELP_NETSCAPE) -> None:
        """
        SetViewer(viewer='', flags=wx.HELP_NETSCAPE) -> None
        
        Tell it which browser to use.
        """

    def Initialize(self, dir: str) -> bool:
        """
        Initialize(dir) -> bool
        
        This must be called to tell the controller where to find the
        documentation.
        """

    def LoadFile(self, file: str='') -> bool:
        """
        LoadFile(file='') -> bool
        
        If file is "", reloads file given in Initialize.
        """

    def DisplayContents(self) -> bool:
        """
        DisplayContents() -> bool
        
        Display list of all help entries.
        """

    @overload
    def DisplaySection(self, section: str) -> bool:
        ...

    @overload
    def DisplaySection(self, sectionNo: int) -> bool:
        """
        DisplaySection(sectionNo) -> bool
        DisplaySection(section) -> bool
        
        Display help for id sectionNo.
        """

    def DisplayBlock(self, blockNo: int) -> bool:
        """
        DisplayBlock(blockNo) -> bool
        
        Display help for URL (using DisplayHelp) or keyword (using
        KeywordSearch)
        """

    def KeywordSearch(self, k: str, mode: wx.HelpSearchMode=wx.HELP_SEARCH_ALL) -> bool:
        """
        KeywordSearch(k, mode=wx.HELP_SEARCH_ALL) -> bool
        
        Search comment/documentation fields in map file and present a list to
        chose from.
        """

    def Quit(self) -> bool:
        """
        Quit() -> bool
        
        Does nothing.
        """

    def OnQuit(self) -> None:
        """
        OnQuit() -> None
        
        Does nothing.
        """

    def DisplayHelp(self, relativeURL: str) -> bool:
        """
        DisplayHelp(relativeURL) -> bool
        
        Call the browser using a relative URL.
        """

    def SetFrameParameters(self, titleFormat: str, size: wx.Size, pos: wx.Point=wx.DefaultPosition, newFrameEachTime: bool=False) -> None:
        """
        SetFrameParameters(titleFormat, size, pos=wx.DefaultPosition, newFrameEachTime=False) -> None
        
        Allows one to override the default settings for the help frame.
        """

    def GetFrameParameters(self, size: Optional[wx.Size]=None, pos: Optional[wx.Point]=None, newFrameEachTime: Optional[bool]=None) -> wx.Frame:
        """
        GetFrameParameters(size=None, pos=None, newFrameEachTime=None) -> wx.Frame
        
        Obtains the latest settings used by the help frame and the help frame.
        """
    @property
    def FrameParameters(self) -> wx.Frame: ...
# end of class ExtHelpController

#-- end-helpext --#
#-- begin-commandlinkbutton --#

class CommandLinkButton(wx.Button):
    """
    CommandLinkButton() -> None
    CommandLinkButton(parent, id=wx.ID_ANY, mainLabel='', note='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, validator=wx.DefaultValidator, name=wx.ButtonNameStr) -> None
    
    Objects of this class are similar in appearance to the normal
    wxButtons but are similar to the links in a web page in functionality.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, mainLabel: str='', note: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, validator: wx.Validator=wx.DefaultValidator, name: str=wx.ButtonNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        CommandLinkButton() -> None
        CommandLinkButton(parent, id=wx.ID_ANY, mainLabel='', note='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, validator=wx.DefaultValidator, name=wx.ButtonNameStr) -> None
        
        Objects of this class are similar in appearance to the normal
        wxButtons but are similar to the links in a web page in functionality.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, mainLabel: str='', note: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, validator: wx.Validator=wx.DefaultValidator, name: str=wx.ButtonNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, mainLabel='', note='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, validator=wx.DefaultValidator, name=wx.ButtonNameStr) -> bool
        
        Button creation function for two-step creation.
        """

    def SetMainLabelAndNote(self, mainLabel: str, note: str) -> None:
        """
        SetMainLabelAndNote(mainLabel, note) -> None
        
        Sets a new main label and note for the button.
        """

    def SetLabel(self, label: str) -> None:
        """
        SetLabel(label) -> None
        
        Sets the string label and note for the button.
        """

    def GetLabel(self) -> str:
        """
        GetLabel() -> str
        
        Returns the string label for the button.
        """

    def SetMainLabel(self, mainLabel: str) -> None:
        """
        SetMainLabel(mainLabel) -> None
        
        Changes the main label.
        """

    def SetNote(self, note: str) -> None:
        """
        SetNote(note) -> None
        
        Changes the note.
        """

    def GetMainLabel(self) -> str:
        """
        GetMainLabel() -> str
        
        Returns the current main label.
        """

    def GetNote(self) -> str:
        """
        GetNote() -> str
        
        Returns the currently used note.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Label(self) -> str: ...
    @Label.setter
    def Label(self, value: str, /) -> None: ...
    @property
    def MainLabel(self) -> str: ...
    @MainLabel.setter
    def MainLabel(self, value: str, /) -> None: ...
    @property
    def Note(self) -> str: ...
    @Note.setter
    def Note(self, value: str, /) -> None: ...
# end of class CommandLinkButton

#-- end-commandlinkbutton --#
#-- begin-dateevt --#
wxEVT_DATE_CHANGED: int
wxEVT_TIME_CHANGED: int

class DateEvent(wx.CommandEvent):
    """
    DateEvent() -> None
    DateEvent(win, dt, type) -> None
    
    This event class holds information about a date change and is used
    together with wxDatePickerCtrl.
    """

    @overload
    def __init__(self, win: wx.Window, dt: wx.DateTime, type: EventType) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        DateEvent() -> None
        DateEvent(win, dt, type) -> None
        
        This event class holds information about a date change and is used
        together with wxDatePickerCtrl.
        """

    def GetDate(self) -> wx.DateTime:
        """
        GetDate() -> wx.DateTime
        
        Returns the date.
        """

    def SetDate(self, date: wx.DateTime) -> None:
        """
        SetDate(date) -> None
        
        Sets the date carried by the event, normally only used by the library
        internally.
        """

    def PyGetDate(self):
        """
        Return the date as a Python datetime.date object.
        """

    PySetDate = wx.deprecated(SetDate, 'Use SetDate instead.')
    @property
    def Date(self) -> wx.DateTime: ...
    @Date.setter
    def Date(self, value: wx.DateTime, /) -> None: ...
# end of class DateEvent


EVT_DATE_CHANGED = wx.PyEventBinder( wxEVT_DATE_CHANGED, 1 )
EVT_TIME_CHANGED = wx.PyEventBinder( wxEVT_TIME_CHANGED, 1 )
#-- end-dateevt --#
#-- begin-datectrl --#

class _enum_11(IntEnum):
    DP_DEFAULT = auto()
    DP_SPIN = auto()
    DP_DROPDOWN = auto()
    DP_SHOWCENTURY = auto()
    DP_ALLOWNONE = auto()
DP_DEFAULT = _enum_11.DP_DEFAULT
DP_SPIN = _enum_11.DP_SPIN
DP_DROPDOWN = _enum_11.DP_DROPDOWN
DP_SHOWCENTURY = _enum_11.DP_SHOWCENTURY
DP_ALLOWNONE = _enum_11.DP_ALLOWNONE

class DatePickerCtrl(wx.Control):
    """
    DatePickerCtrl() -> None
    DatePickerCtrl(parent, id=wx.ID_ANY, dt=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=DP_DEFAULT|DP_SHOWCENTURY, validator=wx.DefaultValidator, name="datectrl") -> None
    
    This control allows the user to select a date.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, dt: wx.DateTime=wx.DefaultDateTime, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=DP_DEFAULT|DP_SHOWCENTURY, validator: wx.Validator=wx.DefaultValidator, name: str="datectrl") -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        DatePickerCtrl() -> None
        DatePickerCtrl(parent, id=wx.ID_ANY, dt=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=DP_DEFAULT|DP_SHOWCENTURY, validator=wx.DefaultValidator, name="datectrl") -> None
        
        This control allows the user to select a date.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, dt: wx.DateTime=wx.DefaultDateTime, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=DP_DEFAULT|DP_SHOWCENTURY, validator: wx.Validator=wx.DefaultValidator, name: str="datectrl") -> bool:
        """
        Create(parent, id=wx.ID_ANY, dt=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=DP_DEFAULT|DP_SHOWCENTURY, validator=wx.DefaultValidator, name="datectrl") -> bool
        
        Create the control window.
        """

    def GetRange(self) -> Tuple[bool, wx.DateTime, wx.DateTime]:
        """
        GetRange() -> Tuple[bool, wx.DateTime, wx.DateTime]
        
        If the control had been previously limited to a range of dates using
        SetRange(), returns the lower and upper bounds of this range.
        """

    def GetValue(self) -> wx.DateTime:
        """
        GetValue() -> wx.DateTime
        
        Returns the currently entered date.
        """

    def SetNullText(self, text: str) -> None:
        """
        SetNullText(text) -> None
        
        Set the text to show when there is no valid value.
        """

    def SetRange(self, dt1: wx.DateTime, dt2: wx.DateTime) -> None:
        """
        SetRange(dt1, dt2) -> None
        
        Sets the valid range for the date selection.
        """

    def SetValue(self, dt: wx.DateTime) -> None:
        """
        SetValue(dt) -> None
        
        Changes the current value of the control.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Value(self) -> wx.DateTime: ...
    @Value.setter
    def Value(self, value: wx.DateTime, /) -> None: ...
# end of class DatePickerCtrl


class DatePickerCtrlGeneric(wx.Control):
    """
    DatePickerCtrlGeneric() -> None
    DatePickerCtrlGeneric(parent, id=wx.ID_ANY, dt=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=DP_DEFAULT|DP_SHOWCENTURY, validator=wx.DefaultValidator, name="datectrl") -> None
    
    This control allows the user to select a date.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, dt: wx.DateTime=wx.DefaultDateTime, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=DP_DEFAULT|DP_SHOWCENTURY, validator: wx.Validator=wx.DefaultValidator, name: str="datectrl") -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        DatePickerCtrlGeneric() -> None
        DatePickerCtrlGeneric(parent, id=wx.ID_ANY, dt=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=DP_DEFAULT|DP_SHOWCENTURY, validator=wx.DefaultValidator, name="datectrl") -> None
        
        This control allows the user to select a date.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, dt: wx.DateTime=wx.DefaultDateTime, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=DP_DEFAULT|DP_SHOWCENTURY, validator: wx.Validator=wx.DefaultValidator, name: str="datectrl") -> bool:
        """
        Create(parent, id=wx.ID_ANY, dt=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=DP_DEFAULT|DP_SHOWCENTURY, validator=wx.DefaultValidator, name="datectrl") -> bool
        
        Create the control window.
        """

    def GetRange(self) -> Tuple[bool, wx.DateTime, wx.DateTime]:
        """
        GetRange() -> Tuple[bool, wx.DateTime, wx.DateTime]
        
        If the control had been previously limited to a range of dates using
        SetRange(), returns the lower and upper bounds of this range.
        """

    def GetValue(self) -> wx.DateTime:
        """
        GetValue() -> wx.DateTime
        
        Returns the currently entered date.
        """

    def SetNullText(self, text: str) -> None:
        """
        SetNullText(text) -> None
        
        Set the text to show when there is no valid value.
        """

    def SetRange(self, dt1: wx.DateTime, dt2: wx.DateTime) -> None:
        """
        SetRange(dt1, dt2) -> None
        
        Sets the valid range for the date selection.
        """

    def SetValue(self, dt: wx.DateTime) -> None:
        """
        SetValue(dt) -> None
        
        Changes the current value of the control.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Value(self) -> wx.DateTime: ...
    @Value.setter
    def Value(self, value: wx.DateTime, /) -> None: ...
# end of class DatePickerCtrlGeneric


GenericDatePickerCtrl = DatePickerCtrlGeneric
#-- end-datectrl --#
#-- begin-calctrl --#

class _enum_4(IntEnum):
    CAL_SUNDAY_FIRST = auto()
    CAL_MONDAY_FIRST = auto()
    CAL_SHOW_HOLIDAYS = auto()
    CAL_NO_YEAR_CHANGE = auto()
    CAL_NO_MONTH_CHANGE = auto()
    CAL_SEQUENTIAL_MONTH_SELECTION = auto()
    CAL_SHOW_SURROUNDING_WEEKS = auto()
    CAL_SHOW_WEEK_NUMBERS = auto()
CAL_SUNDAY_FIRST = _enum_4.CAL_SUNDAY_FIRST
CAL_MONDAY_FIRST = _enum_4.CAL_MONDAY_FIRST
CAL_SHOW_HOLIDAYS = _enum_4.CAL_SHOW_HOLIDAYS
CAL_NO_YEAR_CHANGE = _enum_4.CAL_NO_YEAR_CHANGE
CAL_NO_MONTH_CHANGE = _enum_4.CAL_NO_MONTH_CHANGE
CAL_SEQUENTIAL_MONTH_SELECTION = _enum_4.CAL_SEQUENTIAL_MONTH_SELECTION
CAL_SHOW_SURROUNDING_WEEKS = _enum_4.CAL_SHOW_SURROUNDING_WEEKS
CAL_SHOW_WEEK_NUMBERS = _enum_4.CAL_SHOW_WEEK_NUMBERS

class _CalendarDateBorder(IntEnum):
    CAL_BORDER_NONE = auto()
    CAL_BORDER_SQUARE = auto()
    CAL_BORDER_ROUND = auto()
CalendarDateBorder: TypeAlias = Union[_CalendarDateBorder, int]
CAL_BORDER_NONE = _CalendarDateBorder.CAL_BORDER_NONE
CAL_BORDER_SQUARE = _CalendarDateBorder.CAL_BORDER_SQUARE
CAL_BORDER_ROUND = _CalendarDateBorder.CAL_BORDER_ROUND

class _CalendarHitTestResult(IntEnum):
    CAL_HITTEST_NOWHERE = auto()
    CAL_HITTEST_HEADER = auto()
    CAL_HITTEST_DAY = auto()
    CAL_HITTEST_INCMONTH = auto()
    CAL_HITTEST_DECMONTH = auto()
    CAL_HITTEST_SURROUNDING_WEEK = auto()
    CAL_HITTEST_WEEK = auto()
CalendarHitTestResult: TypeAlias = Union[_CalendarHitTestResult, int]
CAL_HITTEST_NOWHERE = _CalendarHitTestResult.CAL_HITTEST_NOWHERE
CAL_HITTEST_HEADER = _CalendarHitTestResult.CAL_HITTEST_HEADER
CAL_HITTEST_DAY = _CalendarHitTestResult.CAL_HITTEST_DAY
CAL_HITTEST_INCMONTH = _CalendarHitTestResult.CAL_HITTEST_INCMONTH
CAL_HITTEST_DECMONTH = _CalendarHitTestResult.CAL_HITTEST_DECMONTH
CAL_HITTEST_SURROUNDING_WEEK = _CalendarHitTestResult.CAL_HITTEST_SURROUNDING_WEEK
CAL_HITTEST_WEEK = _CalendarHitTestResult.CAL_HITTEST_WEEK
wxEVT_CALENDAR_SEL_CHANGED: int
wxEVT_CALENDAR_PAGE_CHANGED: int
wxEVT_CALENDAR_DOUBLECLICKED: int
wxEVT_CALENDAR_WEEKDAY_CLICKED: int
wxEVT_CALENDAR_WEEK_CLICKED: int
wxEVT_CALENDAR_YEAR_CHANGED: int
wxEVT_CALENDAR_MONTH_CHANGED: int
wxEVT_CALENDAR_DAY_CHANGED: int

class CalendarEvent(DateEvent):
    """
    CalendarEvent() -> None
    CalendarEvent(win, dt, type) -> None
    
    The wxCalendarEvent class is used together with wxCalendarCtrl.
    """

    @overload
    def __init__(self, win: wx.Window, dt: wx.DateTime, type: EventType) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        CalendarEvent() -> None
        CalendarEvent(win, dt, type) -> None
        
        The wxCalendarEvent class is used together with wxCalendarCtrl.
        """

    def GetWeekDay(self) -> wx.DateTime.WeekDay:
        """
        GetWeekDay() -> wx.DateTime.WeekDay
        
        Returns the week day on which the user clicked in
        EVT_CALENDAR_WEEKDAY_CLICKED handler.
        """

    def SetWeekDay(self, day: wx.DateTime.WeekDay) -> None:
        """
        SetWeekDay(day) -> None
        
        Sets the week day carried by the event, normally only used by the
        library internally.
        """
    @property
    def WeekDay(self) -> wx.DateTime.WeekDay: ...
    @WeekDay.setter
    def WeekDay(self, value: wx.DateTime.WeekDay, /) -> None: ...
# end of class CalendarEvent


class CalendarDateAttr:
    """
    CalendarDateAttr(colText=wx.NullColour, colBack=wx.NullColour, colBorder=wx.NullColour, font=wx.NullFont, border=CAL_BORDER_NONE) -> None
    CalendarDateAttr(border, colBorder=wx.NullColour) -> None
    
    wxCalendarDateAttr is a custom attributes for a calendar date.
    """

    @overload
    def __init__(self, border: CalendarDateBorder, colBorder: wx.Colour=wx.NullColour) -> None:
        ...

    @overload
    def __init__(self, colText: wx.Colour=wx.NullColour, colBack: wx.Colour=wx.NullColour, colBorder: wx.Colour=wx.NullColour, font: wx.Font=wx.NullFont, border: CalendarDateBorder=CAL_BORDER_NONE) -> None:
        """
        CalendarDateAttr(colText=wx.NullColour, colBack=wx.NullColour, colBorder=wx.NullColour, font=wx.NullFont, border=CAL_BORDER_NONE) -> None
        CalendarDateAttr(border, colBorder=wx.NullColour) -> None
        
        wxCalendarDateAttr is a custom attributes for a calendar date.
        """

    def GetBackgroundColour(self) -> wx.Colour:
        """
        GetBackgroundColour() -> wx.Colour
        
        Returns the background colour set for the calendar date.
        """

    def GetBorder(self) -> CalendarDateBorder:
        """
        GetBorder() -> CalendarDateBorder
        
        Returns the border set for the calendar date.
        """

    def GetBorderColour(self) -> wx.Colour:
        """
        GetBorderColour() -> wx.Colour
        
        Returns the border colour set for the calendar date.
        """

    def GetFont(self) -> wx.Font:
        """
        GetFont() -> wx.Font
        
        Returns the font set for the calendar date.
        """

    def GetTextColour(self) -> wx.Colour:
        """
        GetTextColour() -> wx.Colour
        
        Returns the text colour set for the calendar date.
        """

    def HasBackgroundColour(self) -> bool:
        """
        HasBackgroundColour() -> bool
        
        Returns true if a non-default text background colour is set.
        """

    def HasBorder(self) -> bool:
        """
        HasBorder() -> bool
        
        Returns true if a non-default (i.e. any) border is set.
        """

    def HasBorderColour(self) -> bool:
        """
        HasBorderColour() -> bool
        
        Returns true if a non-default border colour is set.
        """

    def HasFont(self) -> bool:
        """
        HasFont() -> bool
        
        Returns true if a non-default font is set.
        """

    def HasTextColour(self) -> bool:
        """
        HasTextColour() -> bool
        
        Returns true if a non-default text foreground colour is set.
        """

    def IsHoliday(self) -> bool:
        """
        IsHoliday() -> bool
        
        Returns true if this calendar day is displayed as a holiday.
        """

    def SetBackgroundColour(self, colBack: wx.Colour) -> None:
        """
        SetBackgroundColour(colBack) -> None
        
        Sets the text background colour to use.
        """

    def SetBorder(self, border: CalendarDateBorder) -> None:
        """
        SetBorder(border) -> None
        
        Sets the border to use.
        """

    def SetBorderColour(self, col: wx.Colour) -> None:
        """
        SetBorderColour(col) -> None
        
        Sets the border colour to use.
        """

    def SetFont(self, font: wx.Font) -> None:
        """
        SetFont(font) -> None
        
        Sets the font to use.
        """

    def SetHoliday(self, holiday: bool) -> None:
        """
        SetHoliday(holiday) -> None
        
        If holiday is true, this calendar day will be displayed as a holiday.
        """

    def SetTextColour(self, colText: wx.Colour) -> None:
        """
        SetTextColour(colText) -> None
        
        Sets the text (foreground) colour to use.
        """

    @staticmethod
    def GetMark() -> CalendarDateAttr:
        """
        GetMark() -> CalendarDateAttr
        
        Used (internally) by the generic wxCalendarCtrl::Mark().
        """

    @staticmethod
    def SetMark(m: CalendarDateAttr) -> None:
        """
        SetMark(m) -> None
        
        Set the attributes that will be used to Mark() days on the generic
        wxCalendarCtrl.
        """
    @property
    def BackgroundColour(self) -> wx.Colour: ...
    @BackgroundColour.setter
    def BackgroundColour(self, value: wx.Colour, /) -> None: ...
    @property
    def Border(self) -> CalendarDateBorder: ...
    @Border.setter
    def Border(self, value: CalendarDateBorder, /) -> None: ...
    @property
    def BorderColour(self) -> wx.Colour: ...
    @BorderColour.setter
    def BorderColour(self, value: wx.Colour, /) -> None: ...
    @property
    def Font(self) -> wx.Font: ...
    @Font.setter
    def Font(self, value: wx.Font, /) -> None: ...
    @property
    def TextColour(self) -> wx.Colour: ...
    @TextColour.setter
    def TextColour(self, value: wx.Colour, /) -> None: ...
# end of class CalendarDateAttr

CalendarNameStr: str

class CalendarCtrl(wx.Control):
    """
    CalendarCtrl() -> None
    CalendarCtrl(parent, id=wx.ID_ANY, date=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=CAL_SHOW_HOLIDAYS, name=CalendarNameStr) -> None
    
    The calendar control allows the user to pick a date.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, date: wx.DateTime=wx.DefaultDateTime, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=CAL_SHOW_HOLIDAYS, name: str=CalendarNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        CalendarCtrl() -> None
        CalendarCtrl(parent, id=wx.ID_ANY, date=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=CAL_SHOW_HOLIDAYS, name=CalendarNameStr) -> None
        
        The calendar control allows the user to pick a date.
        """

    def SetDateRange(self, lowerdate: wx.DateTime=wx.DefaultDateTime, upperdate: wx.DateTime=wx.DefaultDateTime) -> bool:
        """
        SetDateRange(lowerdate=wx.DefaultDateTime, upperdate=wx.DefaultDateTime) -> bool
        
        Restrict the dates that can be selected in the control to the
        specified range.
        """

    def GetDateRange(self) -> Tuple[bool, wx.DateTime, wx.DateTime]:
        """
        GetDateRange() -> Tuple[bool, wx.DateTime, wx.DateTime]
        
        Returns the limits currently being used.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, date: wx.DateTime=wx.DefaultDateTime, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=CAL_SHOW_HOLIDAYS, name: str=CalendarNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, date=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=CAL_SHOW_HOLIDAYS, name=CalendarNameStr) -> bool
        
        Creates the control.
        """

    def EnableHolidayDisplay(self, display: bool=True) -> None:
        """
        EnableHolidayDisplay(display=True) -> None
        
        This function should be used instead of changing wxCAL_SHOW_HOLIDAYS
        style bit directly.
        """

    def EnableMonthChange(self, enable: bool=True) -> bool:
        """
        EnableMonthChange(enable=True) -> bool
        
        This function should be used instead of changing wxCAL_NO_MONTH_CHANGE
        style bit.
        """

    def GetAttr(self, day: int) -> CalendarDateAttr:
        """
        GetAttr(day) -> CalendarDateAttr
        
        Returns the attribute for the given date (should be in the range
        1...31).
        """

    def GetDate(self) -> wx.DateTime:
        """
        GetDate() -> wx.DateTime
        
        Gets the currently selected date.
        """

    def GetHeaderColourBg(self) -> wx.Colour:
        """
        GetHeaderColourBg() -> wx.Colour
        
        Gets the background colour of the header part of the calendar window.
        """

    def GetHeaderColourFg(self) -> wx.Colour:
        """
        GetHeaderColourFg() -> wx.Colour
        
        Gets the foreground colour of the header part of the calendar window.
        """

    def GetHighlightColourBg(self) -> wx.Colour:
        """
        GetHighlightColourBg() -> wx.Colour
        
        Gets the background highlight colour.
        """

    def GetHighlightColourFg(self) -> wx.Colour:
        """
        GetHighlightColourFg() -> wx.Colour
        
        Gets the foreground highlight colour.
        """

    def GetHolidayColourBg(self) -> wx.Colour:
        """
        GetHolidayColourBg() -> wx.Colour
        
        Return the background colour currently used for holiday highlighting.
        """

    def GetHolidayColourFg(self) -> wx.Colour:
        """
        GetHolidayColourFg() -> wx.Colour
        
        Return the foreground colour currently used for holiday highlighting.
        """

    def HitTest(self, pos: wx.Point) -> Tuple[CalendarHitTestResult, wx.DateTime, wx.DateTime.WeekDay]:
        """
        HitTest(pos) -> Tuple[CalendarHitTestResult, wx.DateTime, wx.DateTime.WeekDay]
        
        Returns one of wxCalendarHitTestResult constants and fills either date
        or wd pointer with the corresponding value depending on the hit test
        code.
        """

    def ResetAttr(self, day: int) -> None:
        """
        ResetAttr(day) -> None
        
        Clears any attributes associated with the given day (in the range
        1...31).
        """

    def SetAttr(self, day: int, attr: CalendarDateAttr) -> None:
        """
        SetAttr(day, attr) -> None
        
        Associates the attribute with the specified date (in the range
        1...31).
        """

    def SetDate(self, date: wx.DateTime) -> bool:
        """
        SetDate(date) -> bool
        
        Sets the current date.
        """

    def SetHeaderColours(self, colFg: wx.Colour, colBg: wx.Colour) -> None:
        """
        SetHeaderColours(colFg, colBg) -> None
        
        Set the colours used for painting the weekdays at the top of the
        control.
        """

    def SetHighlightColours(self, colFg: wx.Colour, colBg: wx.Colour) -> None:
        """
        SetHighlightColours(colFg, colBg) -> None
        
        Set the colours to be used for highlighting the currently selected
        date.
        """

    def SetHoliday(self, day: int) -> None:
        """
        SetHoliday(day) -> None
        
        Marks the specified day as being a holiday in the current month.
        """

    def SetHolidayColours(self, colFg: wx.Colour, colBg: wx.Colour) -> None:
        """
        SetHolidayColours(colFg, colBg) -> None
        
        Sets the colours to be used for the holidays highlighting.
        """

    def Mark(self, day: int, mark: bool) -> None:
        """
        Mark(day, mark) -> None
        
        Mark or unmark the day.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    def PyGetDate(self):
        """
        Return the date as a Python datetime.date object.
        """

    PySetDate = wx.deprecated(SetDate, 'Use SetDate instead.')
    PySetDateRange = wx.deprecated(SetDateRange, 'Use SetDateRange instead.')
    @property
    def Date(self) -> wx.DateTime: ...
    @Date.setter
    def Date(self, value: wx.DateTime, /) -> None: ...
    @property
    def DateRange(self) -> wx.DateTime: ...
    @DateRange.setter
    def DateRange(self, value: wx.DateTime, /) -> None: ...
    @property
    def HeaderColourBg(self) -> wx.Colour: ...
    @property
    def HeaderColourFg(self) -> wx.Colour: ...
    @property
    def HighlightColourBg(self) -> wx.Colour: ...
    @property
    def HighlightColourFg(self) -> wx.Colour: ...
    @property
    def HolidayColourBg(self) -> wx.Colour: ...
    @property
    def HolidayColourFg(self) -> wx.Colour: ...
# end of class CalendarCtrl


class GenericCalendarCtrl(wx.Control):
    """
    GenericCalendarCtrl() -> None
    GenericCalendarCtrl(parent, id=wx.ID_ANY, date=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=CAL_SHOW_HOLIDAYS, name=CalendarNameStr) -> None
    
    The calendar control allows the user to pick a date.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, date: wx.DateTime=wx.DefaultDateTime, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=CAL_SHOW_HOLIDAYS, name: str=CalendarNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        GenericCalendarCtrl() -> None
        GenericCalendarCtrl(parent, id=wx.ID_ANY, date=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=CAL_SHOW_HOLIDAYS, name=CalendarNameStr) -> None
        
        The calendar control allows the user to pick a date.
        """

    def SetDateRange(self, lowerdate: wx.DateTime=wx.DefaultDateTime, upperdate: wx.DateTime=wx.DefaultDateTime) -> bool:
        """
        SetDateRange(lowerdate=wx.DefaultDateTime, upperdate=wx.DefaultDateTime) -> bool
        
        Restrict the dates that can be selected in the control to the
        specified range.
        """

    def GetDateRange(self) -> Tuple[bool, wx.DateTime, wx.DateTime]:
        """
        GetDateRange() -> Tuple[bool, wx.DateTime, wx.DateTime]
        
        Returns the limits currently being used.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, date: wx.DateTime=wx.DefaultDateTime, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=CAL_SHOW_HOLIDAYS, name: str=CalendarNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, date=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=CAL_SHOW_HOLIDAYS, name=CalendarNameStr) -> bool
        
        Creates the control.
        """

    def EnableHolidayDisplay(self, display: bool=True) -> None:
        """
        EnableHolidayDisplay(display=True) -> None
        
        This function should be used instead of changing wxCAL_SHOW_HOLIDAYS
        style bit directly.
        """

    def EnableMonthChange(self, enable: bool=True) -> bool:
        """
        EnableMonthChange(enable=True) -> bool
        
        This function should be used instead of changing wxCAL_NO_MONTH_CHANGE
        style bit.
        """

    def EnableYearChange(self, enable: bool=True) -> None:
        """
        EnableYearChange(enable=True) -> None
        """

    def GetAttr(self, day: int) -> CalendarDateAttr:
        """
        GetAttr(day) -> CalendarDateAttr
        
        Returns the attribute for the given date (should be in the range
        1...31).
        """

    def GetDate(self) -> wx.DateTime:
        """
        GetDate() -> wx.DateTime
        
        Gets the currently selected date.
        """

    def GetHeaderColourBg(self) -> wx.Colour:
        """
        GetHeaderColourBg() -> wx.Colour
        
        Gets the background colour of the header part of the calendar window.
        """

    def GetHeaderColourFg(self) -> wx.Colour:
        """
        GetHeaderColourFg() -> wx.Colour
        
        Gets the foreground colour of the header part of the calendar window.
        """

    def GetHighlightColourBg(self) -> wx.Colour:
        """
        GetHighlightColourBg() -> wx.Colour
        
        Gets the background highlight colour.
        """

    def GetHighlightColourFg(self) -> wx.Colour:
        """
        GetHighlightColourFg() -> wx.Colour
        
        Gets the foreground highlight colour.
        """

    def GetHolidayColourBg(self) -> wx.Colour:
        """
        GetHolidayColourBg() -> wx.Colour
        
        Return the background colour currently used for holiday highlighting.
        """

    def GetHolidayColourFg(self) -> wx.Colour:
        """
        GetHolidayColourFg() -> wx.Colour
        
        Return the foreground colour currently used for holiday highlighting.
        """

    def HitTest(self, pos: wx.Point) -> Tuple[CalendarHitTestResult, wx.DateTime, wx.DateTime.WeekDay]:
        """
        HitTest(pos) -> Tuple[CalendarHitTestResult, wx.DateTime, wx.DateTime.WeekDay]
        
        Returns one of wxCalendarHitTestResult constants and fills either date
        or wd pointer with the corresponding value depending on the hit test
        code.
        """

    def ResetAttr(self, day: int) -> None:
        """
        ResetAttr(day) -> None
        
        Clears any attributes associated with the given day (in the range
        1...31).
        """

    def SetAttr(self, day: int, attr: CalendarDateAttr) -> None:
        """
        SetAttr(day, attr) -> None
        
        Associates the attribute with the specified date (in the range
        1...31).
        """

    def SetDate(self, date: wx.DateTime) -> bool:
        """
        SetDate(date) -> bool
        
        Sets the current date.
        """

    def SetHeaderColours(self, colFg: wx.Colour, colBg: wx.Colour) -> None:
        """
        SetHeaderColours(colFg, colBg) -> None
        
        Set the colours used for painting the weekdays at the top of the
        control.
        """

    def SetHighlightColours(self, colFg: wx.Colour, colBg: wx.Colour) -> None:
        """
        SetHighlightColours(colFg, colBg) -> None
        
        Set the colours to be used for highlighting the currently selected
        date.
        """

    def SetHoliday(self, day: int) -> None:
        """
        SetHoliday(day) -> None
        
        Marks the specified day as being a holiday in the current month.
        """

    def SetHolidayColours(self, colFg: wx.Colour, colBg: wx.Colour) -> None:
        """
        SetHolidayColours(colFg, colBg) -> None
        
        Sets the colours to be used for the holidays highlighting.
        """

    def Mark(self, day: int, mark: bool) -> None:
        """
        Mark(day, mark) -> None
        
        Mark or unmark the day.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    def PyGetDate(self):
        """
        Return the date as a Python datetime.date object.
        """

    PySetDate = wx.deprecated(SetDate, 'Use SetDate instead.')
    PySetDateRange = wx.deprecated(SetDateRange, 'Use SetDateRange instead.')
    @property
    def Date(self) -> wx.DateTime: ...
    @Date.setter
    def Date(self, value: wx.DateTime, /) -> None: ...
    @property
    def DateRange(self) -> wx.DateTime: ...
    @DateRange.setter
    def DateRange(self, value: wx.DateTime, /) -> None: ...
    @property
    def HeaderColourBg(self) -> wx.Colour: ...
    @property
    def HeaderColourFg(self) -> wx.Colour: ...
    @property
    def HighlightColourBg(self) -> wx.Colour: ...
    @property
    def HighlightColourFg(self) -> wx.Colour: ...
    @property
    def HolidayColourBg(self) -> wx.Colour: ...
    @property
    def HolidayColourFg(self) -> wx.Colour: ...
# end of class GenericCalendarCtrl


EVT_CALENDAR =                 wx.PyEventBinder( wxEVT_CALENDAR_DOUBLECLICKED, 1)
EVT_CALENDAR_SEL_CHANGED =     wx.PyEventBinder( wxEVT_CALENDAR_SEL_CHANGED, 1)
EVT_CALENDAR_WEEKDAY_CLICKED = wx.PyEventBinder( wxEVT_CALENDAR_WEEKDAY_CLICKED, 1)
EVT_CALENDAR_PAGE_CHANGED =    wx.PyEventBinder( wxEVT_CALENDAR_PAGE_CHANGED, 1)
EVT_CALENDAR_WEEK_CLICKED =    wx.PyEventBinder( wxEVT_CALENDAR_WEEK_CLICKED, 1)

# These are deprecated, will be removed later...
EVT_CALENDAR_DAY =             wx.PyEventBinder( wxEVT_CALENDAR_DAY_CHANGED, 1)
EVT_CALENDAR_MONTH =           wx.PyEventBinder( wxEVT_CALENDAR_MONTH_CHANGED, 1)
EVT_CALENDAR_YEAR =            wx.PyEventBinder( wxEVT_CALENDAR_YEAR_CHANGED, 1)
#-- end-calctrl --#
#-- begin-hyperlink --#
HL_CONTEXTMENU: int
HL_ALIGN_LEFT: int
HL_ALIGN_RIGHT: int
HL_ALIGN_CENTRE: int
HL_DEFAULT_STYLE: int
wxEVT_HYPERLINK: int

class HyperlinkEvent(wx.CommandEvent):
    """
    HyperlinkEvent(generator, id, url) -> None
    
    This event class is used for the events generated by wxHyperlinkCtrl.
    """

    def __init__(self, generator: wx.Object, id: int, url: str) -> None:
        """
        HyperlinkEvent(generator, id, url) -> None
        
        This event class is used for the events generated by wxHyperlinkCtrl.
        """

    def GetURL(self) -> str:
        """
        GetURL() -> str
        
        Returns the URL of the hyperlink where the user has just clicked.
        """

    def SetURL(self, url: str) -> None:
        """
        SetURL(url) -> None
        
        Sets the URL associated with the event.
        """
    @property
    def URL(self) -> str: ...
    @URL.setter
    def URL(self, value: str, /) -> None: ...
# end of class HyperlinkEvent

HyperlinkCtrlNameStr: str

class HyperlinkCtrl(wx.Control):
    """
    HyperlinkCtrl() -> None
    HyperlinkCtrl(parent, id=wx.ID_ANY, label='', url='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=HL_DEFAULT_STYLE, name=HyperlinkCtrlNameStr) -> None
    
    This class shows a static text element which links to an URL.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, label: str='', url: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=HL_DEFAULT_STYLE, name: str=HyperlinkCtrlNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        HyperlinkCtrl() -> None
        HyperlinkCtrl(parent, id=wx.ID_ANY, label='', url='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=HL_DEFAULT_STYLE, name=HyperlinkCtrlNameStr) -> None
        
        This class shows a static text element which links to an URL.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, label: str='', url: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=HL_DEFAULT_STYLE, name: str=HyperlinkCtrlNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, label='', url='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=HL_DEFAULT_STYLE, name=HyperlinkCtrlNameStr) -> bool
        
        Creates the hyperlink control.
        """

    def GetHoverColour(self) -> wx.Colour:
        """
        GetHoverColour() -> wx.Colour
        
        Returns the colour used to print the label of the hyperlink when the
        mouse is over the control.
        """

    def GetNormalColour(self) -> wx.Colour:
        """
        GetNormalColour() -> wx.Colour
        
        Returns the colour used to print the label when the link has never
        been clicked before (i.e. the link has not been visited) and the mouse
        is not over the control.
        """

    def GetURL(self) -> str:
        """
        GetURL() -> str
        
        Returns the URL associated with the hyperlink.
        """

    def GetVisited(self) -> bool:
        """
        GetVisited() -> bool
        
        Returns true if the hyperlink has already been clicked by the user at
        least one time.
        """

    def GetVisitedColour(self) -> wx.Colour:
        """
        GetVisitedColour() -> wx.Colour
        
        Returns the colour used to print the label when the mouse is not over
        the control and the link has already been clicked before (i.e. the
        link has been visited).
        """

    def SetHoverColour(self, colour: wx.Colour) -> None:
        """
        SetHoverColour(colour) -> None
        
        Sets the colour used to print the label of the hyperlink when the
        mouse is over the control.
        """

    def SetNormalColour(self, colour: wx.Colour) -> None:
        """
        SetNormalColour(colour) -> None
        
        Sets the colour used to print the label when the link has never been
        clicked before (i.e. the link has not been visited) and the mouse is
        not over the control.
        """

    def SetURL(self, url: str) -> None:
        """
        SetURL(url) -> None
        
        Sets the URL associated with the hyperlink.
        """

    def SetVisited(self, visited: bool=True) -> None:
        """
        SetVisited(visited=True) -> None
        
        Marks the hyperlink as visited (see
        wxHyperlinkCtrl::SetVisitedColour).
        """

    def SetVisitedColour(self, colour: wx.Colour) -> None:
        """
        SetVisitedColour(colour) -> None
        
        Sets the colour used to print the label when the mouse is not over the
        control and the link has already been clicked before (i.e. the link
        has been visited).
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def HoverColour(self) -> wx.Colour: ...
    @HoverColour.setter
    def HoverColour(self, value: wx.Colour, /) -> None: ...
    @property
    def NormalColour(self) -> wx.Colour: ...
    @NormalColour.setter
    def NormalColour(self, value: wx.Colour, /) -> None: ...
    @property
    def URL(self) -> str: ...
    @URL.setter
    def URL(self, value: str, /) -> None: ...
    @property
    def Visited(self) -> bool: ...
    @Visited.setter
    def Visited(self, value: bool, /) -> None: ...
    @property
    def VisitedColour(self) -> wx.Colour: ...
    @VisitedColour.setter
    def VisitedColour(self, value: wx.Colour, /) -> None: ...
# end of class HyperlinkCtrl


EVT_HYPERLINK = wx.PyEventBinder( wxEVT_HYPERLINK, 1 )

# deprecated wxEVT alias
wxEVT_COMMAND_HYPERLINK  = wxEVT_HYPERLINK
#-- end-hyperlink --#
#-- begin-tipdlg --#

class TipProvider:
    """
    TipProvider(currentTip) -> None
    
    This is the class used together with wxShowTip() function.
    """

    def __init__(self, currentTip: int) -> None:
        """
        TipProvider(currentTip) -> None
        
        This is the class used together with wxShowTip() function.
        """

    def GetCurrentTip(self) -> int:
        """
        GetCurrentTip() -> int
        
        Returns the index of the current tip (i.e. the one which would be
        returned by GetTip()).
        """

    def GetTip(self) -> str:
        """
        GetTip() -> str
        
        Return the text of the current tip and pass to the next one.
        """
    @property
    def CurrentTip(self) -> int: ...
    @property
    def Tip(self) -> str: ...
# end of class TipProvider


def CreateFileTipProvider(filename: str, currentTip: int) -> TipProvider:    """
    CreateFileTipProvider(filename, currentTip) -> TipProvider
    
    This function creates a wxTipProvider which may be used with
    wxShowTip().
    """

def ShowTip(parent: wx.Window, tipProvider: TipProvider, showAtStartup: bool=True) -> bool:    """
    ShowTip(parent, tipProvider, showAtStartup=True) -> bool
    
    This function shows a "startup tip" to the user.
    """
#-- end-tipdlg --#
#-- begin-taskbar --#

class _TaskBarIconType(IntEnum):
    TBI_DOCK = auto()
    TBI_CUSTOM_STATUSITEM = auto()
    TBI_DEFAULT_TYPE = auto()
TaskBarIconType: TypeAlias = Union[_TaskBarIconType, int]
TBI_DOCK = _TaskBarIconType.TBI_DOCK
TBI_CUSTOM_STATUSITEM = _TaskBarIconType.TBI_CUSTOM_STATUSITEM
TBI_DEFAULT_TYPE = _TaskBarIconType.TBI_DEFAULT_TYPE
wxEVT_TASKBAR_MOVE: int
wxEVT_TASKBAR_LEFT_DOWN: int
wxEVT_TASKBAR_LEFT_UP: int
wxEVT_TASKBAR_RIGHT_DOWN: int
wxEVT_TASKBAR_RIGHT_UP: int
wxEVT_TASKBAR_LEFT_DCLICK: int
wxEVT_TASKBAR_RIGHT_DCLICK: int
wxEVT_TASKBAR_CLICK: int
wxEVT_TASKBAR_BALLOON_TIMEOUT: int
wxEVT_TASKBAR_BALLOON_CLICK: int

class TaskBarIconEvent(wx.Event):
    """
    TaskBarIconEvent(evtType, tbIcon) -> None
    
    The event class used by wxTaskBarIcon.
    """

    def __init__(self, evtType: EventType, tbIcon: TaskBarIcon) -> None:
        """
        TaskBarIconEvent(evtType, tbIcon) -> None
        
        The event class used by wxTaskBarIcon.
        """
# end of class TaskBarIconEvent


class TaskBarIcon(wx.EvtHandler):
    """
    TaskBarIcon(iconType=TBI_DEFAULT_TYPE) -> None
    
    This class represents a taskbar icon.
    """

    def __init__(self, iconType: TaskBarIconType=TBI_DEFAULT_TYPE) -> None:
        """
        TaskBarIcon(iconType=TBI_DEFAULT_TYPE) -> None
        
        This class represents a taskbar icon.
        """

    def Destroy(self) -> None:
        """
        Destroy() -> None
        
        This method is similar to wxWindow::Destroy and can be used to
        schedule the task bar icon object for the delayed destruction: it will
        be deleted during the next event loop iteration, which allows the task
        bar icon to process any pending events for it before being destroyed.
        """

    def IsIconInstalled(self) -> bool:
        """
        IsIconInstalled() -> bool
        
        Returns true if SetIcon() was called with no subsequent RemoveIcon().
        """

    def IsOk(self) -> bool:
        """
        IsOk() -> bool
        
        Returns true if the object initialized successfully.
        """

    def PopupMenu(self, menu: wx.Menu) -> bool:
        """
        PopupMenu(menu) -> bool
        
        Pops up a menu at the current mouse position.
        """

    def RemoveIcon(self) -> bool:
        """
        RemoveIcon() -> bool
        
        Removes the icon previously set with SetIcon().
        """

    def SetIcon(self, icon: wx.BitmapBundle, tooltip: str='') -> bool:
        """
        SetIcon(icon, tooltip='') -> bool
        
        Sets the icon, and optional tooltip text.
        """

    @staticmethod
    def IsAvailable() -> bool:
        """
        IsAvailable() -> bool
        
        Returns true if system tray is available in the desktop environment
        the app runs under.
        """

    def ShowBalloon(self, title: str, text: str, msec: int=0, flags: int=0) -> bool:
        """
        ShowBalloon(title, text, msec=0, flags=0) -> bool
        
        Show a balloon notification (the icon must have been already
        initialized using SetIcon).  Only implemented for Windows.
        
        The ``title`` and ``text`` parameters are limited to 63 and 255
        characters respectively, ``msec`` is the timeout, in milliseconds,
        before the balloon disappears (will be clamped down to the allowed
        10-30s range by Windows if it's outside it) and ``flags`` can
        include wxICON_ERROR/INFO/WARNING to show a corresponding icon.
        
        Returns ``True`` if balloon was shown, ``False`` on error (incorrect
        parameters or function unsupported by OS).
        """

    def CreatePopupMenu(self) -> wx.Menu:
        """
        CreatePopupMenu() -> wx.Menu
        
        Called by the library when the user requests popup menu if
        GetPopupMenu() is not overridden.
        """

    def GetPopupMenu(self) -> wx.Menu:
        """
        GetPopupMenu() -> wx.Menu
        
        Called by the library when the user requests popup menu.
        """
# end of class TaskBarIcon


EVT_TASKBAR_MOVE = wx.PyEventBinder (         wxEVT_TASKBAR_MOVE )
EVT_TASKBAR_LEFT_DOWN = wx.PyEventBinder (    wxEVT_TASKBAR_LEFT_DOWN )
EVT_TASKBAR_LEFT_UP = wx.PyEventBinder (      wxEVT_TASKBAR_LEFT_UP )
EVT_TASKBAR_RIGHT_DOWN = wx.PyEventBinder (   wxEVT_TASKBAR_RIGHT_DOWN )
EVT_TASKBAR_RIGHT_UP = wx.PyEventBinder (     wxEVT_TASKBAR_RIGHT_UP )
EVT_TASKBAR_LEFT_DCLICK = wx.PyEventBinder (  wxEVT_TASKBAR_LEFT_DCLICK )
EVT_TASKBAR_RIGHT_DCLICK = wx.PyEventBinder ( wxEVT_TASKBAR_RIGHT_DCLICK )
EVT_TASKBAR_CLICK =  wx.PyEventBinder (       wxEVT_TASKBAR_CLICK )
EVT_TASKBAR_BALLOON_TIMEOUT = wx.PyEventBinder ( wxEVT_TASKBAR_BALLOON_TIMEOUT )
EVT_TASKBAR_BALLOON_CLICK = wx.PyEventBinder ( wxEVT_TASKBAR_BALLOON_CLICK )
#-- end-taskbar --#
#-- begin-sound --#
SOUND_SYNC: int
SOUND_ASYNC: int
SOUND_LOOP: int

class Sound(wx.Object):
    """
    Sound() -> None
    Sound(fileName) -> None
    
    This class represents a short sound (loaded from Windows WAV file),
    that can be stored in memory and played.
    """

    @overload
    def __init__(self, fileName: str) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        Sound() -> None
        Sound(fileName) -> None
        
        This class represents a short sound (loaded from Windows WAV file),
        that can be stored in memory and played.
        """

    def Play(self, flags: int=SOUND_ASYNC) -> bool:
        """
        Play(flags=SOUND_ASYNC) -> bool
        
        Plays the sound file.
        """

    @staticmethod
    def PlaySound(filename: str, flags: int=SOUND_ASYNC) -> bool:
        """
        PlaySound(filename, flags=SOUND_ASYNC) -> bool
        
        Plays the sound file.
        """

    def Create(self, fileName: str) -> bool:
        """
        Create(fileName) -> bool
        
        Constructs a wave object from a file or resource.
        """

    def IsOk(self) -> bool:
        """
        IsOk() -> bool
        
        Returns true if the object contains a successfully loaded file or
        resource, false otherwise.
        """

    @staticmethod
    def Stop() -> None:
        """
        Stop() -> None
        
        If a sound is played, this function stops it.
        """

    def CreateFromData(self, data: PyBuffer) -> bool:
        """
        CreateFromData(data) -> bool
        
        Create a sound object from data in a memory buffer in WAV format.
        """

    def __nonzero__(self) -> bool:
        """
        __nonzero__() -> bool
        """

    def __bool__(self) -> bool:
        """
        __bool__() -> bool
        """
# end of class Sound

#-- end-sound --#
#-- begin-joystick --#

class Joystick(wx.Object):
    """
    Joystick(joystick=wx.JOYSTICK1) -> None
    
    wxJoystick allows an application to control one or more joysticks.
    """

    def __init__(self, joystick: int=wx.JOYSTICK1) -> None:
        """
        Joystick(joystick=wx.JOYSTICK1) -> None
        
        wxJoystick allows an application to control one or more joysticks.
        """

    @overload
    def GetButtonState(self, id: int) -> bool:
        ...

    @overload
    def GetButtonState(self) -> int:
        """
        GetButtonState() -> int
        GetButtonState(id) -> bool
        
        Returns the state of the joystick buttons.
        """

    def GetManufacturerId(self) -> int:
        """
        GetManufacturerId() -> int
        
        Returns the manufacturer id.
        """

    def GetMovementThreshold(self) -> int:
        """
        GetMovementThreshold() -> int
        
        Returns the movement threshold, the number of steps outside which the
        joystick is deemed to have moved.
        """

    def GetNumberAxes(self) -> int:
        """
        GetNumberAxes() -> int
        
        Returns the number of axes for this joystick.
        """

    def GetNumberButtons(self) -> int:
        """
        GetNumberButtons() -> int
        
        Returns the number of buttons for this joystick.
        """

    def GetPOVCTSPosition(self) -> int:
        """
        GetPOVCTSPosition() -> int
        
        Returns the point-of-view position, expressed in continuous, one-
        hundredth of a degree units.
        """

    def GetPOVPosition(self) -> int:
        """
        GetPOVPosition() -> int
        
        Returns the point-of-view position, expressed in continuous, one-
        hundredth of a degree units, but limited to return 0, 9000, 18000 or
        27000.
        """

    def GetPollingMax(self) -> int:
        """
        GetPollingMax() -> int
        
        Returns the maximum polling frequency.
        """

    def GetPollingMin(self) -> int:
        """
        GetPollingMin() -> int
        
        Returns the minimum polling frequency.
        """

    @overload
    def GetPosition(self, axis: int) -> int:
        ...

    @overload
    def GetPosition(self) -> wx.Point:
        """
        GetPosition() -> wx.Point
        GetPosition(axis) -> int
        
        Returns the x, y position of the joystick.
        """

    def GetProductId(self) -> int:
        """
        GetProductId() -> int
        
        Returns the product id for the joystick.
        """

    def GetProductName(self) -> str:
        """
        GetProductName() -> str
        
        Returns the product name for the joystick.
        """

    def GetRudderMax(self) -> int:
        """
        GetRudderMax() -> int
        
        Returns the maximum rudder position.
        """

    def GetRudderMin(self) -> int:
        """
        GetRudderMin() -> int
        
        Returns the minimum rudder position.
        """

    def GetRudderPosition(self) -> int:
        """
        GetRudderPosition() -> int
        
        Returns the rudder position.
        """

    def GetUMax(self) -> int:
        """
        GetUMax() -> int
        
        Returns the maximum U position.
        """

    def GetUMin(self) -> int:
        """
        GetUMin() -> int
        
        Returns the minimum U position.
        """

    def GetUPosition(self) -> int:
        """
        GetUPosition() -> int
        
        Gets the position of the fifth axis of the joystick, if it exists.
        """

    def GetVMax(self) -> int:
        """
        GetVMax() -> int
        
        Returns the maximum V position.
        """

    def GetVMin(self) -> int:
        """
        GetVMin() -> int
        
        Returns the minimum V position.
        """

    def GetVPosition(self) -> int:
        """
        GetVPosition() -> int
        
        Gets the position of the sixth axis of the joystick, if it exists.
        """

    def GetXMax(self) -> int:
        """
        GetXMax() -> int
        
        Returns the maximum x position.
        """

    def GetXMin(self) -> int:
        """
        GetXMin() -> int
        
        Returns the minimum x position.
        """

    def GetYMax(self) -> int:
        """
        GetYMax() -> int
        
        Returns the maximum y position.
        """

    def GetYMin(self) -> int:
        """
        GetYMin() -> int
        
        Returns the minimum y position.
        """

    def GetZMax(self) -> int:
        """
        GetZMax() -> int
        
        Returns the maximum z position.
        """

    def GetZMin(self) -> int:
        """
        GetZMin() -> int
        
        Returns the minimum z position.
        """

    def GetZPosition(self) -> int:
        """
        GetZPosition() -> int
        
        Returns the z position of the joystick.
        """

    def HasPOV(self) -> bool:
        """
        HasPOV() -> bool
        
        Returns true if the joystick has a point of view control.
        """

    def HasPOV4Dir(self) -> bool:
        """
        HasPOV4Dir() -> bool
        
        Returns true if the joystick point-of-view supports discrete values
        (centered, forward, backward, left, and right).
        """

    def HasPOVCTS(self) -> bool:
        """
        HasPOVCTS() -> bool
        
        Returns true if the joystick point-of-view supports continuous degree
        bearings.
        """

    def HasRudder(self) -> bool:
        """
        HasRudder() -> bool
        
        Returns true if there is a rudder attached to the computer.
        """

    def HasU(self) -> bool:
        """
        HasU() -> bool
        
        Returns true if the joystick has a U axis.
        """

    def HasV(self) -> bool:
        """
        HasV() -> bool
        
        Returns true if the joystick has a V axis.
        """

    def HasZ(self) -> bool:
        """
        HasZ() -> bool
        
        Returns true if the joystick has a Z axis.
        """

    def IsOk(self) -> bool:
        """
        IsOk() -> bool
        
        Returns true if the joystick is functioning.
        """

    def ReleaseCapture(self) -> bool:
        """
        ReleaseCapture() -> bool
        
        Releases the capture set by SetCapture.
        """

    def SetCapture(self, win: wx.Window, pollingFreq: int=0) -> bool:
        """
        SetCapture(win, pollingFreq=0) -> bool
        
        Sets the capture to direct joystick events to win.
        """

    def SetMovementThreshold(self, threshold: int) -> None:
        """
        SetMovementThreshold(threshold) -> None
        
        Sets the movement threshold, the number of steps outside which the
        joystick is deemed to have moved.
        """

    @staticmethod
    def GetNumberJoysticks() -> int:
        """
        GetNumberJoysticks() -> int
        
        Returns the number of joysticks currently attached to the computer.
        """

    def GetMaxButtons(self) -> int:
        """
        GetMaxButtons() -> int
        """

    def GetMaxAxes(self) -> int:
        """
        GetMaxAxes() -> int
        """
    @property
    def ButtonState(self) -> int: ...
    @property
    def ManufacturerId(self) -> int: ...
    @property
    def MaxAxes(self) -> int: ...
    @property
    def MaxButtons(self) -> int: ...
    @property
    def MovementThreshold(self) -> int: ...
    @MovementThreshold.setter
    def MovementThreshold(self, value: int, /) -> None: ...
    @property
    def NumberAxes(self) -> int: ...
    @property
    def NumberButtons(self) -> int: ...
    @property
    def POVCTSPosition(self) -> int: ...
    @property
    def POVPosition(self) -> int: ...
    @property
    def PollingMax(self) -> int: ...
    @property
    def PollingMin(self) -> int: ...
    @property
    def Position(self) -> wx.Point: ...
    @property
    def ProductId(self) -> int: ...
    @property
    def ProductName(self) -> str: ...
    @property
    def RudderMax(self) -> int: ...
    @property
    def RudderMin(self) -> int: ...
    @property
    def RudderPosition(self) -> int: ...
    @property
    def UMax(self) -> int: ...
    @property
    def UMin(self) -> int: ...
    @property
    def UPosition(self) -> int: ...
    @property
    def VMax(self) -> int: ...
    @property
    def VMin(self) -> int: ...
    @property
    def VPosition(self) -> int: ...
    @property
    def XMax(self) -> int: ...
    @property
    def XMin(self) -> int: ...
    @property
    def YMax(self) -> int: ...
    @property
    def YMin(self) -> int: ...
    @property
    def ZMax(self) -> int: ...
    @property
    def ZMin(self) -> int: ...
    @property
    def ZPosition(self) -> int: ...
# end of class Joystick

USE_JOYSTICK: int
#-- end-joystick --#
#-- begin-animate --#
AC_NO_AUTORESIZE: int
AC_DEFAULT_STYLE: int

class _AnimationType(IntEnum):
    ANIMATION_TYPE_INVALID = auto()
    ANIMATION_TYPE_GIF = auto()
    ANIMATION_TYPE_ANI = auto()
    ANIMATION_TYPE_ANY = auto()
AnimationType: TypeAlias = Union[_AnimationType, int]
ANIMATION_TYPE_INVALID = _AnimationType.ANIMATION_TYPE_INVALID
ANIMATION_TYPE_GIF = _AnimationType.ANIMATION_TYPE_GIF
ANIMATION_TYPE_ANI = _AnimationType.ANIMATION_TYPE_ANI
ANIMATION_TYPE_ANY = _AnimationType.ANIMATION_TYPE_ANY

class _AnimationDisposal(IntEnum):
    ANIM_UNSPECIFIED = auto()
    ANIM_DONOTREMOVE = auto()
    ANIM_TOBACKGROUND = auto()
    ANIM_TOPREVIOUS = auto()
AnimationDisposal: TypeAlias = Union[_AnimationDisposal, int]
ANIM_UNSPECIFIED = _AnimationDisposal.ANIM_UNSPECIFIED
ANIM_DONOTREMOVE = _AnimationDisposal.ANIM_DONOTREMOVE
ANIM_TOBACKGROUND = _AnimationDisposal.ANIM_TOBACKGROUND
ANIM_TOPREVIOUS = _AnimationDisposal.ANIM_TOPREVIOUS

class Animation(wx.Object):
    """
    Animation() -> None
    Animation(name, type=ANIMATION_TYPE_ANY) -> None
    Animation(other) -> None
    
    The wxAnimation class handles the interface between the animation
    control and the details of the animation image or data.
    """

    @overload
    def __init__(self, name: str, type: AnimationType=ANIMATION_TYPE_ANY) -> None:
        ...

    @overload
    def __init__(self, other: Animation) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        Animation() -> None
        Animation(name, type=ANIMATION_TYPE_ANY) -> None
        Animation(other) -> None
        
        The wxAnimation class handles the interface between the animation
        control and the details of the animation image or data.
        """

    def IsOk(self) -> bool:
        """
        IsOk() -> bool
        
        Returns true if animation data is present.
        """

    def IsCompatibleWith(self, ci: wx.ClassInfo) -> bool:
        """
        IsCompatibleWith(ci) -> bool
        
        Returns true if animation can be used with controls of the given type.
        """

    def GetDelay(self, frame: int) -> int:
        """
        GetDelay(frame) -> int
        
        Returns the delay for the i-th frame in milliseconds.
        """

    def GetFrameCount(self) -> int:
        """
        GetFrameCount() -> int
        
        Returns the number of frames for this animation.
        """

    def GetFrame(self, frame: int) -> wx.Image:
        """
        GetFrame(frame) -> wx.Image
        
        Returns the i-th frame as a wxImage.
        """

    def GetSize(self) -> wx.Size:
        """
        GetSize() -> wx.Size
        
        Returns the size of the animation.
        """

    def LoadFile(self, name: str, type: AnimationType=ANIMATION_TYPE_ANY) -> bool:
        """
        LoadFile(name, type=ANIMATION_TYPE_ANY) -> bool
        
        Loads an animation from a file.
        """

    def Load(self, stream: wx.InputStream, type: AnimationType=ANIMATION_TYPE_ANY) -> bool:
        """
        Load(stream, type=ANIMATION_TYPE_ANY) -> bool
        
        Loads an animation from the given stream.
        """

    @staticmethod
    def GetHandlers() -> AnimationDecoderList:
        """
        GetHandlers() -> AnimationDecoderList
        
        Returns the list of animation decoders used by the generic animation
        and wxGenericAnimationCtrl.
        """

    @staticmethod
    def AddHandler(handler: AnimationDecoder) -> None:
        """
        AddHandler(handler) -> None
        
        Add a new decoder to the list of animation decoders.
        """

    @staticmethod
    def InsertHandler(handler: AnimationDecoder) -> None:
        """
        InsertHandler(handler) -> None
        
        Insert a new decoder to the front of the list of animation decoders.
        """

    @staticmethod
    def FindHandler(animType: AnimationType) -> AnimationDecoder:
        """
        FindHandler(animType) -> AnimationDecoder
        
        Search for an animation decoder by type.
        """

    @staticmethod
    def InitStandardHandlers() -> None:
        """
        InitStandardHandlers() -> None
        
        Load the stock animation decoders (currently GIF and ANI) into the
        list of decoders.
        """

    @staticmethod
    def CleanUpHandlers() -> None:
        """
        CleanUpHandlers() -> None
        
        Clear out the animation decoder list.
        """
    @property
    def FrameCount(self) -> int: ...
    @property
    def Size(self) -> wx.Size: ...
# end of class Animation


class AnimationCtrl(wx.Control):
    """
    AnimationCtrl(parent, id=wx.ID_ANY, anim=NullAnimation, pos=wx.DefaultPosition, size=wx.DefaultSize, style=AC_DEFAULT_STYLE, name=AnimationCtrlNameStr) -> None
    
    This is a static control which displays an animation.
    """

    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, anim: Animation=NullAnimation, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=AC_DEFAULT_STYLE, name: str=AnimationCtrlNameStr) -> None:
        """
        AnimationCtrl(parent, id=wx.ID_ANY, anim=NullAnimation, pos=wx.DefaultPosition, size=wx.DefaultSize, style=AC_DEFAULT_STYLE, name=AnimationCtrlNameStr) -> None
        
        This is a static control which displays an animation.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, anim: Animation=NullAnimation, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=AC_DEFAULT_STYLE, name: str=AnimationCtrlNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, anim=NullAnimation, pos=wx.DefaultPosition, size=wx.DefaultSize, style=AC_DEFAULT_STYLE, name=AnimationCtrlNameStr) -> bool
        
        Creates the control with the given anim animation.
        """

    def CreateAnimation(self) -> Animation:
        """
        CreateAnimation() -> Animation
        
        Create a new animation object compatible with this control.
        """

    def GetAnimation(self) -> Animation:
        """
        GetAnimation() -> Animation
        
        Returns the animation associated with this control.
        """

    def GetInactiveBitmap(self) -> wx.Bitmap:
        """
        GetInactiveBitmap() -> wx.Bitmap
        
        Returns the inactive bitmap shown in this control when the; see
        SetInactiveBitmap() for more info.
        """

    def IsPlaying(self) -> bool:
        """
        IsPlaying() -> bool
        
        Returns true if the animation is being played.
        """

    def LoadFile(self, file: str, animType: AnimationType=ANIMATION_TYPE_ANY) -> bool:
        """
        LoadFile(file, animType=ANIMATION_TYPE_ANY) -> bool
        
        Loads the animation from the given file and calls SetAnimation().
        """

    def Load(self, file: wx.InputStream, animType: AnimationType=ANIMATION_TYPE_ANY) -> bool:
        """
        Load(file, animType=ANIMATION_TYPE_ANY) -> bool
        
        Loads the animation from the given stream and calls SetAnimation().
        """

    def Play(self) -> bool:
        """
        Play() -> bool
        
        Starts playing the animation.
        """

    def SetAnimation(self, anim: Animation) -> None:
        """
        SetAnimation(anim) -> None
        
        Sets the animation to play in this control.
        """

    def SetInactiveBitmap(self, bmp: wx.BitmapBundle) -> None:
        """
        SetInactiveBitmap(bmp) -> None
        
        Sets the bitmap to show on the control when it's not playing an
        animation.
        """

    def Stop(self) -> None:
        """
        Stop() -> None
        
        Stops playing the animation.
        """

    @staticmethod
    def CreateCompatibleAnimation() -> Animation:
        """
        CreateCompatibleAnimation() -> Animation
        
        Create a new animation object compatible with this control.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Animation(self) -> Animation: ...
    @Animation.setter
    def Animation(self, value: Animation, /) -> None: ...
    @property
    def InactiveBitmap(self) -> wx.BitmapBundle: ...
    @InactiveBitmap.setter
    def InactiveBitmap(self, value: wx.BitmapBundle, /) -> None: ...
# end of class AnimationCtrl

AnimationCtrlNameStr: str
NullAnimation: Animation

class GenericAnimationCtrl(wx.Control):
    """
    GenericAnimationCtrl(parent, id=wx.ID_ANY, anim=NullAnimation, pos=wx.DefaultPosition, size=wx.DefaultSize, style=AC_DEFAULT_STYLE, name=AnimationCtrlNameStr) -> None
    
    Generic implementation of wxAnimationCtrl interface.
    """

    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, anim: Animation=NullAnimation, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=AC_DEFAULT_STYLE, name: str=AnimationCtrlNameStr) -> None:
        """
        GenericAnimationCtrl(parent, id=wx.ID_ANY, anim=NullAnimation, pos=wx.DefaultPosition, size=wx.DefaultSize, style=AC_DEFAULT_STYLE, name=AnimationCtrlNameStr) -> None
        
        Generic implementation of wxAnimationCtrl interface.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, anim: Animation=NullAnimation, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=AC_DEFAULT_STYLE, name: str=AnimationCtrlNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, anim=NullAnimation, pos=wx.DefaultPosition, size=wx.DefaultSize, style=AC_DEFAULT_STYLE, name=AnimationCtrlNameStr) -> bool
        
        Creates the control with the given anim animation.
        """

    def DrawCurrentFrame(self, dc: wx.DC) -> None:
        """
        DrawCurrentFrame(dc) -> None
        
        Draw the current frame of the animation into given DC.
        """

    def GetBackingStore(self) -> wx.Bitmap:
        """
        GetBackingStore() -> wx.Bitmap
        
        Returns a wxBitmap with the current frame drawn in it.
        """

    @overload
    def Play(self) -> bool:
        ...

    @overload
    def Play(self, looped: bool) -> bool:
        """
        Play(looped) -> bool
        Play() -> bool
        
        This overload of Play() lets you specify if the animation must loop or
        not.
        """

    def SetUseWindowBackgroundColour(self, useWinBackground: bool=True) -> None:
        """
        SetUseWindowBackgroundColour(useWinBackground=True) -> None
        
        Specify whether the animation's background colour is to be shown (the
        default), or whether the window background should show through.
        """

    def IsUsingWindowBackgroundColour(self) -> bool:
        """
        IsUsingWindowBackgroundColour() -> bool
        
        Returns true if the window's background colour is being used.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    def Stop(self) -> None:
        """
        Stop() -> None
        
        Stops playing the animation.
        """

    def IsPlaying(self) -> bool:
        """
        IsPlaying() -> bool
        
        Returns true if the animation is being played.
        """

    def Load(self, file: wx.InputStream, animType: AnimationType=ANIMATION_TYPE_ANY) -> bool:
        """
        Load(file, animType=ANIMATION_TYPE_ANY) -> bool
        
        Loads the animation from the given stream and calls SetAnimation().
        """

    def LoadFile(self, file: str, animType: AnimationType=ANIMATION_TYPE_ANY) -> bool:
        """
        LoadFile(file, animType=ANIMATION_TYPE_ANY) -> bool
        
        Loads the animation from the given file and calls SetAnimation().
        """

    def GetAnimation(self) -> Animation:
        """
        GetAnimation() -> Animation
        
        Returns the animation associated with this control.
        """

    def SetAnimation(self, anim: Animation) -> None:
        """
        SetAnimation(anim) -> None
        
        Sets the animation to play in this control.
        """

    def SetInactiveBitmap(self, bmp: wx.BitmapBundle) -> None:
        """
        SetInactiveBitmap(bmp) -> None
        
        Sets the bitmap to show on the control when it's not playing an
        animation.
        """

    def GetInactiveBitmap(self) -> wx.Bitmap:
        """
        GetInactiveBitmap() -> wx.Bitmap
        
        Returns the inactive bitmap shown in this control when the; see
        SetInactiveBitmap() for more info.
        """

    def CreateAnimation(self) -> Animation:
        """
        CreateAnimation() -> Animation
        
        Create a new animation object compatible with this control.
        """

    @staticmethod
    def CreateCompatibleAnimation() -> Animation:
        """
        CreateCompatibleAnimation() -> Animation
        
        Create a new animation object compatible with this control.
        """
    @property
    def Animation(self) -> Animation: ...
    @Animation.setter
    def Animation(self, value: Animation, /) -> None: ...
    @property
    def BackingStore(self) -> wx.Bitmap: ...
    @property
    def InactiveBitmap(self) -> wx.BitmapBundle: ...
    @InactiveBitmap.setter
    def InactiveBitmap(self, value: wx.BitmapBundle, /) -> None: ...
# end of class GenericAnimationCtrl


class AnimationDecoder(ObjectRefData):
    """
    AnimationDecoder() -> None
    
    wxAnimationDecoder is used by wxAnimation for loading frames and other
    information for the animation from the animation image file.
    """

    def __init__(self) -> None:
        """
        AnimationDecoder() -> None
        
        wxAnimationDecoder is used by wxAnimation for loading frames and other
        information for the animation from the animation image file.
        """

    def Load(self, stream: wx.InputStream) -> bool:
        """
        Load(stream) -> bool
        
        Load the animation image frames from the given stream.
        """

    def CanRead(self, stream: wx.InputStream) -> bool:
        """
        CanRead(stream) -> bool
        
        Returns true if this decoder supports loading from the given stream.
        """

    def Clone(self) -> AnimationDecoder:
        """
        Clone() -> AnimationDecoder
        
        Create a copy of this decoder.
        """

    def GetType(self) -> AnimationType:
        """
        GetType() -> AnimationType
        
        Return the animation type this decoder implements.
        """

    def ConvertToImage(self, frame: int, image: wx.Image) -> bool:
        """
        ConvertToImage(frame, image) -> bool
        
        Convert given frame to wxImage.
        """

    def GetFrameSize(self, frame: int) -> wx.Size:
        """
        GetFrameSize(frame) -> wx.Size
        """

    def GetFramePosition(self, frame: int) -> wx.Point:
        """
        GetFramePosition(frame) -> wx.Point
        """

    def GetDisposalMethod(self, frame: int) -> AnimationDisposal:
        """
        GetDisposalMethod(frame) -> AnimationDisposal
        
        What should be done after displaying this frame.
        """

    def GetDelay(self, frame: int) -> int:
        """
        GetDelay(frame) -> int
        
        Return the number of milliseconds this frame should be displayed.
        """

    def GetTransparentColour(self, frame: int) -> wx.Colour:
        """
        GetTransparentColour(frame) -> wx.Colour
        
        The transparent colour for this frame, if any, or wxNullColour.
        """

    def GetAnimationSize(self) -> wx.Size:
        """
        GetAnimationSize() -> wx.Size
        """

    def GetBackgroundColour(self) -> wx.Colour:
        """
        GetBackgroundColour() -> wx.Colour
        """

    def GetFrameCount(self) -> int:
        """
        GetFrameCount() -> int
        """
    @property
    def AnimationSize(self) -> wx.Size: ...
    @property
    def BackgroundColour(self) -> wx.Colour: ...
    @property
    def FrameCount(self) -> int: ...
    @property
    def Type(self) -> AnimationType: ...

    def DoCanRead(self, stream: wx.InputStream) -> bool:
        """
        DoCanRead(stream) -> bool
        
        Checks the signature of the data in the given stream and returns true
        if it appears to be a valid animation format recognized by the
        animation decoder; this function should modify the stream current
        position without taking care of restoring it since CanRead() will do
        it.
        """
# end of class AnimationDecoder


class ANIDecoder(AnimationDecoder):
    """
    ANIDecoder() -> None
    
    An animation decoder supporting animated cursor (.ani) files.
    """

    def __init__(self) -> None:
        """
        ANIDecoder() -> None
        
        An animation decoder supporting animated cursor (.ani) files.
        """

    def Load(self, stream: wx.InputStream) -> bool:
        """
        Load(stream) -> bool
        
        Load the animation image frames from the given stream.
        """

    def Clone(self) -> AnimationDecoder:
        """
        Clone() -> AnimationDecoder
        
        Create a copy of this decoder.
        """

    def GetType(self) -> AnimationType:
        """
        GetType() -> AnimationType
        
        Return the animation type this decoder implements.
        """

    def ConvertToImage(self, frame: int, image: wx.Image) -> bool:
        """
        ConvertToImage(frame, image) -> bool
        
        Convert given frame to wxImage.
        """

    def GetFrameSize(self, frame: int) -> wx.Size:
        """
        GetFrameSize(frame) -> wx.Size
        """

    def GetFramePosition(self, frame: int) -> wx.Point:
        """
        GetFramePosition(frame) -> wx.Point
        """

    def GetDisposalMethod(self, frame: int) -> AnimationDisposal:
        """
        GetDisposalMethod(frame) -> AnimationDisposal
        
        What should be done after displaying this frame.
        """

    def GetDelay(self, frame: int) -> int:
        """
        GetDelay(frame) -> int
        
        Return the number of milliseconds this frame should be displayed.
        """

    def GetTransparentColour(self, frame: int) -> wx.Colour:
        """
        GetTransparentColour(frame) -> wx.Colour
        
        The transparent colour for this frame, if any, or wxNullColour.
        """
    @property
    def Type(self) -> AnimationType: ...

    def DoCanRead(self, stream: wx.InputStream) -> bool:
        """
        DoCanRead(stream) -> bool
        
        Checks the signature of the data in the given stream and returns true
        if it appears to be a valid animation format recognized by the
        animation decoder; this function should modify the stream current
        position without taking care of restoring it since CanRead() will do
        it.
        """
# end of class ANIDecoder


class GIFDecoder(AnimationDecoder):
    """
    GIFDecoder() -> None
    
    An animation decoder supporting animated GIF files.
    """

    def __init__(self) -> None:
        """
        GIFDecoder() -> None
        
        An animation decoder supporting animated GIF files.
        """

    def Load(self, stream: wx.InputStream) -> bool:
        """
        Load(stream) -> bool
        
        Load the animation image frames from the given stream.
        """

    def Clone(self) -> AnimationDecoder:
        """
        Clone() -> AnimationDecoder
        
        Create a copy of this decoder.
        """

    def GetType(self) -> AnimationType:
        """
        GetType() -> AnimationType
        
        Return the animation type this decoder implements.
        """

    def ConvertToImage(self, frame: int, image: wx.Image) -> bool:
        """
        ConvertToImage(frame, image) -> bool
        
        Convert given frame to wxImage.
        """

    def GetFrameSize(self, frame: int) -> wx.Size:
        """
        GetFrameSize(frame) -> wx.Size
        """

    def GetFramePosition(self, frame: int) -> wx.Point:
        """
        GetFramePosition(frame) -> wx.Point
        """

    def GetDisposalMethod(self, frame: int) -> AnimationDisposal:
        """
        GetDisposalMethod(frame) -> AnimationDisposal
        
        What should be done after displaying this frame.
        """

    def GetDelay(self, frame: int) -> int:
        """
        GetDelay(frame) -> int
        
        Return the number of milliseconds this frame should be displayed.
        """

    def GetTransparentColour(self, frame: int) -> wx.Colour:
        """
        GetTransparentColour(frame) -> wx.Colour
        
        The transparent colour for this frame, if any, or wxNullColour.
        """
    @property
    def Type(self) -> AnimationType: ...

    def DoCanRead(self, stream: wx.InputStream) -> bool:
        """
        DoCanRead(stream) -> bool
        
        Checks the signature of the data in the given stream and returns true
        if it appears to be a valid animation format recognized by the
        animation decoder; this function should modify the stream current
        position without taking care of restoring it since CanRead() will do
        it.
        """
# end of class GIFDecoder

#-- end-animate --#
#-- begin-bannerwindow --#
BannerWindowNameStr: str

class BannerWindow(wx.Window):
    """
    BannerWindow() -> None
    BannerWindow(parent, winid=wx.ID_ANY, dir=wx.LEFT, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, name=BannerWindowNameStr) -> None
    
    A simple banner window showing either a bitmap or text.
    """

    @overload
    def __init__(self, parent: wx.Window, winid: int=wx.ID_ANY, dir: wx.Direction=wx.LEFT, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, name: str=BannerWindowNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        BannerWindow() -> None
        BannerWindow(parent, winid=wx.ID_ANY, dir=wx.LEFT, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, name=BannerWindowNameStr) -> None
        
        A simple banner window showing either a bitmap or text.
        """

    def Create(self, parent: wx.Window, winid: int=wx.ID_ANY, dir: wx.Direction=wx.LEFT, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, name: str=BannerWindowNameStr) -> bool:
        """
        Create(parent, winid=wx.ID_ANY, dir=wx.LEFT, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, name=BannerWindowNameStr) -> bool
        
        Really create the banner window for the objects created using the
        default constructor.
        """

    def SetBitmap(self, bmp: wx.BitmapBundle) -> None:
        """
        SetBitmap(bmp) -> None
        
        Provide the bitmap to use as background.
        """

    def SetText(self, title: str, message: str) -> None:
        """
        SetText(title, message) -> None
        
        Set the text to display.
        """

    def SetGradient(self, start: wx.Colour, end: wx.Colour) -> None:
        """
        SetGradient(start, end) -> None
        
        Set the colours between which the gradient runs.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
# end of class BannerWindow

#-- end-bannerwindow --#
#-- begin-editlbox --#
EL_ALLOW_NEW: int
EL_ALLOW_EDIT: int
EL_ALLOW_DELETE: int
EL_NO_REORDER: int
EL_DEFAULT_STYLE: int
EditableListBoxNameStr: str

class EditableListBox(wx.Panel):
    """
    EditableListBox() -> None
    EditableListBox(parent, id=wx.ID_ANY, label='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=EL_DEFAULT_STYLE, name=EditableListBoxNameStr) -> None
    
    An editable listbox is composite control that lets the user easily
    enter, delete and reorder a list of strings.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, label: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=EL_DEFAULT_STYLE, name: str=EditableListBoxNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        EditableListBox() -> None
        EditableListBox(parent, id=wx.ID_ANY, label='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=EL_DEFAULT_STYLE, name=EditableListBoxNameStr) -> None
        
        An editable listbox is composite control that lets the user easily
        enter, delete and reorder a list of strings.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, label: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=EL_DEFAULT_STYLE, name: str=EditableListBoxNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, label='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=EL_DEFAULT_STYLE, name=EditableListBoxNameStr) -> bool
        
        Creates the editable listbox for two-step construction.
        """

    def SetStrings(self, strings: List[str]) -> None:
        """
        SetStrings(strings) -> None
        
        Replaces current contents with given strings.
        """

    def GetStrings(self) -> List[str]:
        """
        GetStrings() -> List[str]
        
        Returns a list of the current contents of the control.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    def GetListCtrl(self) -> wx.ListCtrl:
        """
        GetListCtrl() -> wx.ListCtrl
        
        Returns a reference to the listctrl used in the EditableListBox.
        """

    def GetDelButton(self) -> wx.BitmapButton:
        """
        GetDelButton() -> wx.BitmapButton
        
        Returns a reference to the delete button used in the EditableListBox.
        """

    def GetNewButton(self) -> wx.BitmapButton:
        """
        GetNewButton() -> wx.BitmapButton
        
        Returns a reference to the new button used in the EditableListBox.
        """

    def GetUpButton(self) -> wx.BitmapButton:
        """
        GetUpButton() -> wx.BitmapButton
        
        Returns a reference to the up button used in the EditableListBox.
        """

    def GetDownButton(self) -> wx.BitmapButton:
        """
        GetDownButton() -> wx.BitmapButton
        
        Returns a reference to the down button used in the EditableListBox.
        """

    def GetEditButton(self) -> wx.BitmapButton:
        """
        GetEditButton() -> wx.BitmapButton
        
        Returns a reference to the edit button used in the EditableListBox.
        """
    @property
    def DelButton(self) -> wx.BitmapButton: ...
    @property
    def DownButton(self) -> wx.BitmapButton: ...
    @property
    def EditButton(self) -> wx.BitmapButton: ...
    @property
    def ListCtrl(self) -> wx.ListCtrl: ...
    @property
    def NewButton(self) -> wx.BitmapButton: ...
    @property
    def Strings(self) -> List[str]: ...
    @Strings.setter
    def Strings(self, value: List[str], /) -> None: ...
    @property
    def UpButton(self) -> wx.BitmapButton: ...
# end of class EditableListBox

#-- end-editlbox --#
#-- begin-notifmsg --#
wxEVT_NOTIFICATION_MESSAGE_CLICK: int
wxEVT_NOTIFICATION_MESSAGE_DISMISSED: int
wxEVT_NOTIFICATION_MESSAGE_ACTION: int

class NotificationMessage(wx.EvtHandler):
    """
    NotificationMessage() -> None
    NotificationMessage(title, message='', parent=None, flags=wx.ICON_INFORMATION) -> None
    
    This class allows showing the user a message non intrusively.
    """

    class _enum_38(IntEnum):
        Timeout_Auto = auto()
        Timeout_Never = auto()
    Timeout_Auto = _enum_38.Timeout_Auto
    Timeout_Never = _enum_38.Timeout_Never

    @overload
    def __init__(self, title: str, message: str='', parent: Optional[wx.Window]=None, flags: int=wx.ICON_INFORMATION) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        NotificationMessage() -> None
        NotificationMessage(title, message='', parent=None, flags=wx.ICON_INFORMATION) -> None
        
        This class allows showing the user a message non intrusively.
        """

    def AddAction(self, actionid: int, label: str="") -> bool:
        """
        AddAction(actionid, label="") -> bool
        
        Add an action to the notification.
        """

    def Close(self) -> bool:
        """
        Close() -> bool
        
        Hides the notification.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        This parameter can be currently used to specify the icon to show in
        the notification.
        """

    def SetIcon(self, icon: wx.Icon) -> None:
        """
        SetIcon(icon) -> None
        
        Specify a custom icon to be displayed in the notification.
        """

    def SetMessage(self, message: str) -> None:
        """
        SetMessage(message) -> None
        
        Set the main text of the notification.
        """

    def SetParent(self, parent: wx.Window) -> None:
        """
        SetParent(parent) -> None
        
        Set the parent for this notification: the notification will be
        associated with the top level parent of this window or, if this method
        is not called, with the main application window by default.
        """

    def SetTitle(self, title: str) -> None:
        """
        SetTitle(title) -> None
        
        Set the title, it must be a concise string (not more than 64
        characters), use SetMessage() to give the user more details.
        """

    def Show(self, timeout: int=Timeout_Auto) -> bool:
        """
        Show(timeout=Timeout_Auto) -> bool
        
        Show the notification to the user and hides it after timeout seconds are elapsed.
        """

    @staticmethod
    def UseTaskBarIcon(icon: TaskBarIcon) -> TaskBarIcon:
        """
        UseTaskBarIcon(icon) -> TaskBarIcon
        
        If the application already uses a wxTaskBarIcon, it should be
        connected to notifications by using this method.
        """

    @staticmethod
    def MSWUseToasts(shortcutPath: str="", appId: str="") -> bool:
        """
        MSWUseToasts(shortcutPath="", appId="") -> bool
        
        Enables toast notifications available since Windows 8 and suppresses
        the additional icon in the notification area on Windows 10.
        """
# end of class NotificationMessage

#-- end-notifmsg --#
#-- begin-splash --#
SPLASH_CENTRE_ON_PARENT: int
SPLASH_CENTRE_ON_SCREEN: int
SPLASH_NO_CENTRE: int
SPLASH_TIMEOUT: int
SPLASH_NO_TIMEOUT: int

class SplashScreen(wx.Frame):
    """
    SplashScreen(bitmap, splashStyle, milliseconds, parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.BORDER_SIMPLE|wx.FRAME_NO_TASKBAR|wx.STAY_ON_TOP) -> None
    
    wxSplashScreen shows a window with a thin border, displaying a bitmap
    describing your application.
    """

    def __init__(self, bitmap: wx.Bitmap, splashStyle: int, milliseconds: int, parent: Optional[wx.Window], id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.BORDER_SIMPLE|wx.FRAME_NO_TASKBAR|wx.STAY_ON_TOP) -> None:
        """
        SplashScreen(bitmap, splashStyle, milliseconds, parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.BORDER_SIMPLE|wx.FRAME_NO_TASKBAR|wx.STAY_ON_TOP) -> None
        
        wxSplashScreen shows a window with a thin border, displaying a bitmap
        describing your application.
        """

    def GetSplashStyle(self) -> int:
        """
        GetSplashStyle() -> int
        
        Returns the splash style (see wxSplashScreen() for details).
        """

    def GetTimeout(self) -> int:
        """
        GetTimeout() -> int
        
        Returns the timeout in milliseconds.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    def GetBitmap(self) -> wx.Bitmap:
        """
        GetBitmap() -> wx.Bitmap
        
        Get the spash screen's bitmap
        """

    def SetBitmap(self, bitmap: wx.Bitmap) -> None:
        """
        SetBitmap(bitmap) -> None
        
        Set a new bitmap for the splash screen.
        """
    @property
    def Bitmap(self) -> wx.Bitmap: ...
    @Bitmap.setter
    def Bitmap(self, value: wx.Bitmap, /) -> None: ...
    @property
    def SplashStyle(self) -> int: ...
    @property
    def Timeout(self) -> int: ...
# end of class SplashScreen


SPLASH_CENTER_ON_PARENT = SPLASH_CENTRE_ON_PARENT
SPLASH_CENTER_ON_SCREEN = SPLASH_CENTRE_ON_SCREEN
SPLASH_NO_CENTER = SPLASH_NO_CENTRE
#-- end-splash --#
#-- begin-sashwin --#
SW_NOBORDER: int
SW_BORDER: int
SW_3DSASH: int
SW_3DBORDER: int
SW_3D: int

class _SashEdgePosition(IntEnum):
    SASH_TOP = auto()
    SASH_RIGHT = auto()
    SASH_BOTTOM = auto()
    SASH_LEFT = auto()
    SASH_NONE = auto()
SashEdgePosition: TypeAlias = Union[_SashEdgePosition, int]
SASH_TOP = _SashEdgePosition.SASH_TOP
SASH_RIGHT = _SashEdgePosition.SASH_RIGHT
SASH_BOTTOM = _SashEdgePosition.SASH_BOTTOM
SASH_LEFT = _SashEdgePosition.SASH_LEFT
SASH_NONE = _SashEdgePosition.SASH_NONE

class _SashDragStatus(IntEnum):
    SASH_STATUS_OK = auto()
    SASH_STATUS_OUT_OF_RANGE = auto()
SashDragStatus: TypeAlias = Union[_SashDragStatus, int]
SASH_STATUS_OK = _SashDragStatus.SASH_STATUS_OK
SASH_STATUS_OUT_OF_RANGE = _SashDragStatus.SASH_STATUS_OUT_OF_RANGE
wxEVT_SASH_DRAGGED: int

class SashWindow(wx.Window):
    """
    SashWindow() -> None
    SashWindow(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.CLIP_CHILDREN|SW_3D, name="sashWindow") -> None
    
    wxSashWindow allows any of its edges to have a sash which can be
    dragged to resize the window.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.CLIP_CHILDREN|SW_3D, name: str="sashWindow") -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        SashWindow() -> None
        SashWindow(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.CLIP_CHILDREN|SW_3D, name="sashWindow") -> None
        
        wxSashWindow allows any of its edges to have a sash which can be
        dragged to resize the window.
        """

    def GetMaximumSizeX(self) -> int:
        """
        GetMaximumSizeX() -> int
        
        Gets the maximum window size in the x direction.
        """

    def GetMaximumSizeY(self) -> int:
        """
        GetMaximumSizeY() -> int
        
        Gets the maximum window size in the y direction.
        """

    def GetMinimumSizeX(self) -> int:
        """
        GetMinimumSizeX() -> int
        
        Gets the minimum window size in the x direction.
        """

    def GetMinimumSizeY(self) -> int:
        """
        GetMinimumSizeY() -> int
        
        Gets the minimum window size in the y direction.
        """

    def GetSashVisible(self, edge: SashEdgePosition) -> bool:
        """
        GetSashVisible(edge) -> bool
        
        Returns true if a sash is visible on the given edge, false otherwise.
        """

    def SetMaximumSizeX(self, min: int) -> None:
        """
        SetMaximumSizeX(min) -> None
        
        Sets the maximum window size in the x direction.
        """

    def SetMaximumSizeY(self, min: int) -> None:
        """
        SetMaximumSizeY(min) -> None
        
        Sets the maximum window size in the y direction.
        """

    def SetMinimumSizeX(self, min: int) -> None:
        """
        SetMinimumSizeX(min) -> None
        
        Sets the minimum window size in the x direction.
        """

    def SetMinimumSizeY(self, min: int) -> None:
        """
        SetMinimumSizeY(min) -> None
        
        Sets the minimum window size in the y direction.
        """

    def SetSashVisible(self, edge: SashEdgePosition, visible: bool) -> None:
        """
        SetSashVisible(edge, visible) -> None
        
        Call this function to make a sash visible or invisible on a particular
        edge.
        """

    def GetEdgeMargin(self, edge: SashEdgePosition) -> int:
        """
        GetEdgeMargin(edge) -> int
        
        Get border size.
        """

    def SetDefaultBorderSize(self, width: int) -> None:
        """
        SetDefaultBorderSize(width) -> None
        
        Sets the default sash border size.
        """

    def GetDefaultBorderSize(self) -> int:
        """
        GetDefaultBorderSize() -> int
        
        Gets the default sash border size.
        """

    def SetExtraBorderSize(self, width: int) -> None:
        """
        SetExtraBorderSize(width) -> None
        
        Sets the additional border size between child and sash window.
        """

    def GetExtraBorderSize(self) -> int:
        """
        GetExtraBorderSize() -> int
        
        Gets the addition border size between child and sash window.
        """

    def SashHitTest(self, x: int, y: int, tolerance: int=2) -> SashEdgePosition:
        """
        SashHitTest(x, y, tolerance=2) -> SashEdgePosition
        
        Tests for x, y over sash.
        """

    def SizeWindows(self) -> None:
        """
        SizeWindows() -> None
        
        Resizes subwindows.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def DefaultBorderSize(self) -> int: ...
    @DefaultBorderSize.setter
    def DefaultBorderSize(self, value: int, /) -> None: ...
    @property
    def ExtraBorderSize(self) -> int: ...
    @ExtraBorderSize.setter
    def ExtraBorderSize(self, value: int, /) -> None: ...
    @property
    def MaximumSizeX(self) -> int: ...
    @MaximumSizeX.setter
    def MaximumSizeX(self, value: int, /) -> None: ...
    @property
    def MaximumSizeY(self) -> int: ...
    @MaximumSizeY.setter
    def MaximumSizeY(self, value: int, /) -> None: ...
    @property
    def MinimumSizeX(self) -> int: ...
    @MinimumSizeX.setter
    def MinimumSizeX(self, value: int, /) -> None: ...
    @property
    def MinimumSizeY(self) -> int: ...
    @MinimumSizeY.setter
    def MinimumSizeY(self, value: int, /) -> None: ...
# end of class SashWindow


class SashEvent(wx.CommandEvent):
    """
    SashEvent(id=0, edge=SASH_NONE) -> None
    
    A sash event is sent when the sash of a wxSashWindow has been dragged
    by the user.
    """

    def __init__(self, id: int=0, edge: SashEdgePosition=SASH_NONE) -> None:
        """
        SashEvent(id=0, edge=SASH_NONE) -> None
        
        A sash event is sent when the sash of a wxSashWindow has been dragged
        by the user.
        """

    def GetDragRect(self) -> wx.Rect:
        """
        GetDragRect() -> wx.Rect
        
        Returns the rectangle representing the new size the window would be if
        the resize was applied.
        """

    def GetDragStatus(self) -> SashDragStatus:
        """
        GetDragStatus() -> SashDragStatus
        
        Returns the status of the sash: one of wxSASH_STATUS_OK,
        wxSASH_STATUS_OUT_OF_RANGE.
        """

    def GetEdge(self) -> SashEdgePosition:
        """
        GetEdge() -> SashEdgePosition
        
        Returns the dragged edge.
        """

    def SetEdge(self, edge: SashEdgePosition) -> None:
        """
        SetEdge(edge) -> None
        """

    def SetDragRect(self, rect: wx.Rect) -> None:
        """
        SetDragRect(rect) -> None
        """

    def SetDragStatus(self, status: SashDragStatus) -> None:
        """
        SetDragStatus(status) -> None
        """
    @property
    def DragRect(self) -> wx.Rect: ...
    @DragRect.setter
    def DragRect(self, value: wx.Rect, /) -> None: ...
    @property
    def DragStatus(self) -> SashDragStatus: ...
    @DragStatus.setter
    def DragStatus(self, value: SashDragStatus, /) -> None: ...
    @property
    def Edge(self) -> SashEdgePosition: ...
    @Edge.setter
    def Edge(self, value: SashEdgePosition, /) -> None: ...
# end of class SashEvent


EVT_SASH_DRAGGED = wx.PyEventBinder( wxEVT_SASH_DRAGGED, 1 )
EVT_SASH_DRAGGED_RANGE = wx.PyEventBinder( wxEVT_SASH_DRAGGED, 2 )
#-- end-sashwin --#
#-- begin-laywin --#

class _LayoutOrientation(IntEnum):
    LAYOUT_HORIZONTAL = auto()
    LAYOUT_VERTICAL = auto()
LayoutOrientation: TypeAlias = Union[_LayoutOrientation, int]
LAYOUT_HORIZONTAL = _LayoutOrientation.LAYOUT_HORIZONTAL
LAYOUT_VERTICAL = _LayoutOrientation.LAYOUT_VERTICAL

class _LayoutAlignment(IntEnum):
    LAYOUT_NONE = auto()
    LAYOUT_TOP = auto()
    LAYOUT_LEFT = auto()
    LAYOUT_RIGHT = auto()
    LAYOUT_BOTTOM = auto()
LayoutAlignment: TypeAlias = Union[_LayoutAlignment, int]
LAYOUT_NONE = _LayoutAlignment.LAYOUT_NONE
LAYOUT_TOP = _LayoutAlignment.LAYOUT_TOP
LAYOUT_LEFT = _LayoutAlignment.LAYOUT_LEFT
LAYOUT_RIGHT = _LayoutAlignment.LAYOUT_RIGHT
LAYOUT_BOTTOM = _LayoutAlignment.LAYOUT_BOTTOM
wxEVT_QUERY_LAYOUT_INFO: int
wxEVT_CALCULATE_LAYOUT: int

class LayoutAlgorithm(wx.Object):
    """
    LayoutAlgorithm() -> None
    
    wxLayoutAlgorithm implements layout of subwindows in MDI or SDI
    frames.
    """

    def __init__(self) -> None:
        """
        LayoutAlgorithm() -> None
        
        wxLayoutAlgorithm implements layout of subwindows in MDI or SDI
        frames.
        """

    def LayoutFrame(self, frame: wx.Frame, mainWindow: Optional[wx.Window]=None) -> bool:
        """
        LayoutFrame(frame, mainWindow=None) -> bool
        
        Lays out the children of a normal frame.
        """

    def LayoutMDIFrame(self, frame: wx.MDIParentFrame, rect: Optional[wx.Rect]=None) -> bool:
        """
        LayoutMDIFrame(frame, rect=None) -> bool
        
        Lays out the children of an MDI parent frame.
        """

    def LayoutWindow(self, parent: wx.Window, mainWindow: Optional[wx.Window]=None) -> bool:
        """
        LayoutWindow(parent, mainWindow=None) -> bool
        
        Lays out the children of a normal frame or other window.
        """
# end of class LayoutAlgorithm


class SashLayoutWindow(SashWindow):
    """
    SashLayoutWindow() -> None
    SashLayoutWindow(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.CLIP_CHILDREN|SW_3D, name="layoutWindow") -> None
    
    wxSashLayoutWindow responds to OnCalculateLayout events generated by
    wxLayoutAlgorithm.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.CLIP_CHILDREN|SW_3D, name: str="layoutWindow") -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        SashLayoutWindow() -> None
        SashLayoutWindow(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.CLIP_CHILDREN|SW_3D, name="layoutWindow") -> None
        
        wxSashLayoutWindow responds to OnCalculateLayout events generated by
        wxLayoutAlgorithm.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.CLIP_CHILDREN|SW_3D, name: str="layoutWindow") -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.CLIP_CHILDREN|SW_3D, name="layoutWindow") -> bool
        
        Initializes a sash layout window, which can be a child of a frame,
        dialog or any other non-control window.
        """

    def GetAlignment(self) -> LayoutAlignment:
        """
        GetAlignment() -> LayoutAlignment
        
        Returns the alignment of the window: one of wxLAYOUT_TOP,
        wxLAYOUT_LEFT, wxLAYOUT_RIGHT, wxLAYOUT_BOTTOM.
        """

    def GetOrientation(self) -> LayoutOrientation:
        """
        GetOrientation() -> LayoutOrientation
        
        Returns the orientation of the window: one of wxLAYOUT_HORIZONTAL,
        wxLAYOUT_VERTICAL.
        """

    def OnCalculateLayout(self, event: CalculateLayoutEvent) -> None:
        """
        OnCalculateLayout(event) -> None
        
        The default handler for the event that is generated by
        wxLayoutAlgorithm.
        """

    def OnQueryLayoutInfo(self, event: QueryLayoutInfoEvent) -> None:
        """
        OnQueryLayoutInfo(event) -> None
        
        The default handler for the event that is generated by
        OnCalculateLayout to get size, alignment and orientation information
        for the window.
        """

    def SetAlignment(self, alignment: LayoutAlignment) -> None:
        """
        SetAlignment(alignment) -> None
        
        Sets the alignment of the window (which edge of the available parent
        client area the window is attached to).
        """

    def SetDefaultSize(self, size: wx.Size) -> None:
        """
        SetDefaultSize(size) -> None
        
        Sets the default dimensions of the window.
        """

    def SetOrientation(self, orientation: LayoutOrientation) -> None:
        """
        SetOrientation(orientation) -> None
        
        Sets the orientation of the window (the direction the window will
        stretch in, to fill the available parent client area).
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Alignment(self) -> LayoutAlignment: ...
    @Alignment.setter
    def Alignment(self, value: LayoutAlignment, /) -> None: ...
    @property
    def Orientation(self) -> LayoutOrientation: ...
    @Orientation.setter
    def Orientation(self, value: LayoutOrientation, /) -> None: ...
# end of class SashLayoutWindow


class QueryLayoutInfoEvent(wx.Event):
    """
    QueryLayoutInfoEvent(id=0) -> None
    
    This event is sent when wxLayoutAlgorithm wishes to get the size,
    orientation and alignment of a window.
    """

    def __init__(self, id: int=0) -> None:
        """
        QueryLayoutInfoEvent(id=0) -> None
        
        This event is sent when wxLayoutAlgorithm wishes to get the size,
        orientation and alignment of a window.
        """

    def GetAlignment(self) -> LayoutAlignment:
        """
        GetAlignment() -> LayoutAlignment
        
        Specifies the alignment of the window (which side of the remaining
        parent client area the window sticks to).
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        
        Returns the flags associated with this event.
        """

    def GetOrientation(self) -> LayoutOrientation:
        """
        GetOrientation() -> LayoutOrientation
        
        Returns the orientation that the event handler specified to the event
        object.
        """

    def GetRequestedLength(self) -> int:
        """
        GetRequestedLength() -> int
        
        Returns the requested length of the window in the direction of the
        window orientation.
        """

    def GetSize(self) -> wx.Size:
        """
        GetSize() -> wx.Size
        
        Returns the size that the event handler specified to the event object
        as being the requested size of the window.
        """

    def SetAlignment(self, alignment: LayoutAlignment) -> None:
        """
        SetAlignment(alignment) -> None
        
        Call this to specify the alignment of the window (which side of the
        remaining parent client area the window sticks to).
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Sets the flags associated with this event.
        """

    def SetOrientation(self, orientation: LayoutOrientation) -> None:
        """
        SetOrientation(orientation) -> None
        
        Call this to specify the orientation of the window.
        """

    def SetRequestedLength(self, length: int) -> None:
        """
        SetRequestedLength(length) -> None
        
        Sets the requested length of the window in the direction of the window
        orientation.
        """

    def SetSize(self, size: wx.Size) -> None:
        """
        SetSize(size) -> None
        
        Call this to let the calling code know what the size of the window is.
        """
    @property
    def Alignment(self) -> LayoutAlignment: ...
    @Alignment.setter
    def Alignment(self, value: LayoutAlignment, /) -> None: ...
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def Orientation(self) -> LayoutOrientation: ...
    @Orientation.setter
    def Orientation(self, value: LayoutOrientation, /) -> None: ...
    @property
    def RequestedLength(self) -> int: ...
    @RequestedLength.setter
    def RequestedLength(self, value: int, /) -> None: ...
    @property
    def Size(self) -> wx.Size: ...
    @Size.setter
    def Size(self, value: wx.Size, /) -> None: ...
# end of class QueryLayoutInfoEvent


class CalculateLayoutEvent(wx.Event):
    """
    CalculateLayoutEvent(id=0) -> None
    
    This event is sent by wxLayoutAlgorithm to calculate the amount of the
    remaining client area that the window should occupy.
    """

    def __init__(self, id: int=0) -> None:
        """
        CalculateLayoutEvent(id=0) -> None
        
        This event is sent by wxLayoutAlgorithm to calculate the amount of the
        remaining client area that the window should occupy.
        """

    def GetFlags(self) -> int:
        """
        GetFlags() -> int
        
        Returns the flags associated with this event.
        """

    def GetRect(self) -> wx.Rect:
        """
        GetRect() -> wx.Rect
        
        Before the event handler is entered, returns the remaining parent
        client area that the window could occupy.
        """

    def SetFlags(self, flags: int) -> None:
        """
        SetFlags(flags) -> None
        
        Sets the flags associated with this event.
        """

    def SetRect(self, rect: wx.Rect) -> None:
        """
        SetRect(rect) -> None
        
        Call this to specify the new remaining parent client area, after the
        space occupied by the window has been subtracted.
        """
    @property
    def Flags(self) -> int: ...
    @Flags.setter
    def Flags(self, value: int, /) -> None: ...
    @property
    def Rect(self) -> wx.Rect: ...
    @Rect.setter
    def Rect(self, value: wx.Rect, /) -> None: ...
# end of class CalculateLayoutEvent


EVT_QUERY_LAYOUT_INFO = wx.PyEventBinder( wxEVT_QUERY_LAYOUT_INFO )
EVT_CALCULATE_LAYOUT = wx.PyEventBinder( wxEVT_CALCULATE_LAYOUT )
#-- end-laywin --#
#-- begin-odcombo --#

class _OwnerDrawnComboBoxPaintingFlags(IntFlag):
    ODCB_PAINTING_CONTROL = auto()
    ODCB_PAINTING_SELECTED = auto()
OwnerDrawnComboBoxPaintingFlags: TypeAlias = Union[_OwnerDrawnComboBoxPaintingFlags, int]
ODCB_PAINTING_CONTROL = _OwnerDrawnComboBoxPaintingFlags.ODCB_PAINTING_CONTROL
ODCB_PAINTING_SELECTED = _OwnerDrawnComboBoxPaintingFlags.ODCB_PAINTING_SELECTED

class _enum_39(IntEnum):
    ODCB_DCLICK_CYCLES = auto()
    ODCB_STD_CONTROL_PAINT = auto()
ODCB_DCLICK_CYCLES = _enum_39.ODCB_DCLICK_CYCLES
ODCB_STD_CONTROL_PAINT = _enum_39.ODCB_STD_CONTROL_PAINT

class OwnerDrawnComboBox(wx.ComboCtrl, wx.ItemContainer):
    """
    OwnerDrawnComboBox() -> None
    OwnerDrawnComboBox(parent, id=wx.ID_ANY, value='', pos=wx.DefaultPosition, size=wx.DefaultSize, choices=[], style=0, validator=wx.DefaultValidator, name="comboBox") -> None
    
    wxOwnerDrawnComboBox is a combobox with owner-drawn list items.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, value: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, choices: List[str]=[], style: int=0, validator: wx.Validator=wx.DefaultValidator, name: str="comboBox") -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        OwnerDrawnComboBox() -> None
        OwnerDrawnComboBox(parent, id=wx.ID_ANY, value='', pos=wx.DefaultPosition, size=wx.DefaultSize, choices=[], style=0, validator=wx.DefaultValidator, name="comboBox") -> None
        
        wxOwnerDrawnComboBox is a combobox with owner-drawn list items.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, value: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, choices: List[str]=[], style: int=0, validator: wx.Validator=wx.DefaultValidator, name: str=wx.ComboBoxNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, value='', pos=wx.DefaultPosition, size=wx.DefaultSize, choices=[], style=0, validator=wx.DefaultValidator, name=wx.ComboBoxNameStr) -> bool
        
        Creates the combobox for two-step construction.
        """

    def IsListEmpty(self) -> bool:
        """
        IsListEmpty() -> bool
        
        Returns true if the list of combobox choices is empty.
        """

    def IsTextEmpty(self) -> bool:
        """
        IsTextEmpty() -> bool
        
        Returns true if the text of the combobox is empty.
        """

    def GetWidestItem(self) -> int:
        """
        GetWidestItem() -> int
        
        Returns index to the widest item in the list.
        """

    def GetWidestItemWidth(self) -> int:
        """
        GetWidestItemWidth() -> int
        
        Returns width of the widest item in the list.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def WidestItem(self) -> int: ...
    @property
    def WidestItemWidth(self) -> int: ...

    def OnDrawBackground(self, dc: wx.DC, rect: wx.Rect, item: int, flags: int) -> None:
        """
        OnDrawBackground(dc, rect, item, flags) -> None
        
        This method is used to draw the items background and, maybe, a border
        around it.
        """

    def OnDrawItem(self, dc: wx.DC, rect: wx.Rect, item: int, flags: int) -> None:
        """
        OnDrawItem(dc, rect, item, flags) -> None
        
        The derived class may implement this function to actually draw the
        item with the given index on the provided DC.
        """

    def OnMeasureItem(self, item: int) -> int:
        """
        OnMeasureItem(item) -> int
        
        The derived class may implement this method to return the height of
        the specified item (in pixels).
        """

    def OnMeasureItemWidth(self, item: int) -> int:
        """
        OnMeasureItemWidth(item) -> int
        
        The derived class may implement this method to return the width of the
        specified item (in pixels).
        """
# end of class OwnerDrawnComboBox

#-- end-odcombo --#
#-- begin-bmpcbox --#
BitmapComboBoxNameStr: str

class BitmapComboBox(wx.Control, wx.TextEntry, wx.ItemContainer):
    """
    BitmapComboBox() -> None
    BitmapComboBox(parent, id=wx.ID_ANY, value='', pos=wx.DefaultPosition, size=wx.DefaultSize, choices=[], style=0, validator=wx.DefaultValidator, name=BitmapComboBoxNameStr) -> None
    
    A combobox that displays bitmap in front of the list items.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, value: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, choices: List[str]=[], style: int=0, validator: wx.Validator=wx.DefaultValidator, name: str=BitmapComboBoxNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        BitmapComboBox() -> None
        BitmapComboBox(parent, id=wx.ID_ANY, value='', pos=wx.DefaultPosition, size=wx.DefaultSize, choices=[], style=0, validator=wx.DefaultValidator, name=BitmapComboBoxNameStr) -> None
        
        A combobox that displays bitmap in front of the list items.
        """

    @overload
    def Append(self, item: str, bitmap: wx.Bitmap, clientData: ClientData) -> int:
        ...

    @overload
    def Append(self, item: str, bitmap: wx.Bitmap=wx.NullBitmap) -> int:
        """
        Append(item, bitmap=wx.NullBitmap) -> int
        Append(item, bitmap, clientData) -> int
        
        Adds the item to the end of the combo box.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, value: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, choices: List[str]=[], style: int=0, validator: wx.Validator=wx.DefaultValidator, name: str=BitmapComboBoxNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, value='', pos=wx.DefaultPosition, size=wx.DefaultSize, choices=[], style=0, validator=wx.DefaultValidator, name=BitmapComboBoxNameStr) -> bool
        
        Creates the combobox for two-step construction.
        """

    def GetBitmapSize(self) -> wx.Size:
        """
        GetBitmapSize() -> wx.Size
        
        Returns the size of the bitmaps used in the combo box.
        """

    def GetItemBitmap(self, n: int) -> wx.Bitmap:
        """
        GetItemBitmap(n) -> wx.Bitmap
        
        Returns the bitmap of the item with the given index.
        """

    @overload
    def Insert(self, item: str, bitmap: wx.Bitmap, pos: int, clientData: ClientData) -> int:
        ...

    @overload
    def Insert(self, item: str, bitmap: wx.Bitmap, pos: int) -> int:
        """
        Insert(item, bitmap, pos) -> int
        Insert(item, bitmap, pos, clientData) -> int
        
        Inserts the item into the list before pos.
        """

    def SetItemBitmap(self, n: int, bitmap: wx.BitmapBundle) -> None:
        """
        SetItemBitmap(n, bitmap) -> None
        
        Sets the bitmap for the given item.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    def GetInsertionPoint(self) -> int:
        """
        GetInsertionPoint() -> int
        
        Same as wxTextEntry::GetInsertionPoint().
        """

    def IsListEmpty(self) -> bool:
        """
        IsListEmpty() -> bool
        
        Returns true if the list of combobox choices is empty.
        """

    def IsTextEmpty(self) -> bool:
        """
        IsTextEmpty() -> bool
        
        Returns true if the text of the combobox is empty.
        """

    @overload
    def SetSelection(self, n: int) -> None:
        ...

    @overload
    def SetSelection(self, from_: int, to_: int) -> None:
        """
        SetSelection(from_, to_) -> None
        SetSelection(n) -> None
        
        Same as wxTextEntry::SetSelection().
        """

    def SetTextSelection(self, from_: int, to_: int) -> None:
        """
        SetTextSelection(from_, to_) -> None
        
        Same as wxTextEntry::SetSelection().
        """

    def SetValue(self, text: str) -> None:
        """
        SetValue(text) -> None
        
        Sets the text for the combobox text field.
        """

    def Popup(self) -> None:
        """
        Popup() -> None
        
        Shows the list box portion of the combo box.
        """

    def Dismiss(self) -> None:
        """
        Dismiss() -> None
        
        Hides the list box portion of the combo box.
        """

    def GetSelection(self) -> int:
        """
        GetSelection() -> int
        
        Returns the index of the selected item or wxNOT_FOUND if no item is
        selected.
        """

    def GetTextSelection(self) -> Tuple[int, int]:
        """
        GetTextSelection() -> Tuple[int, int]
        
        Gets the current selection span.
        """

    def FindString(self, string: str, caseSensitive: bool=False) -> int:
        """
        FindString(string, caseSensitive=False) -> int
        
        Finds an item whose label matches the given string.
        """

    def GetString(self, n: int) -> str:
        """
        GetString(n) -> str
        
        Returns the label of the item with the given index.
        """

    def SetString(self, n: int, text: str) -> None:
        """
        SetString(n, text) -> None
        
        Changes the text of the specified combobox item.
        """

    def GetCount(self) -> int:
        """
        GetCount() -> int
        
        Returns the number of items in the control.
        """
    @property
    def BitmapSize(self) -> wx.Size: ...
    @property
    def Count(self) -> int: ...
    @property
    def InsertionPoint(self) -> int: ...
    @property
    def Selection(self) -> int: ...
    @Selection.setter
    def Selection(self, value: int, /) -> None: ...
# end of class BitmapComboBox

#-- end-bmpcbox --#
#-- begin-richtooltip --#

class _TipKind(IntEnum):
    TipKind_None = auto()
    TipKind_TopLeft = auto()
    TipKind_Top = auto()
    TipKind_TopRight = auto()
    TipKind_BottomLeft = auto()
    TipKind_Bottom = auto()
    TipKind_BottomRight = auto()
    TipKind_Auto = auto()
TipKind: TypeAlias = Union[_TipKind, int]
TipKind_None = _TipKind.TipKind_None
TipKind_TopLeft = _TipKind.TipKind_TopLeft
TipKind_Top = _TipKind.TipKind_Top
TipKind_TopRight = _TipKind.TipKind_TopRight
TipKind_BottomLeft = _TipKind.TipKind_BottomLeft
TipKind_Bottom = _TipKind.TipKind_Bottom
TipKind_BottomRight = _TipKind.TipKind_BottomRight
TipKind_Auto = _TipKind.TipKind_Auto

class RichToolTip:
    """
    RichToolTip(title, message) -> None
    
    Allows showing a tool tip with more customizations than wxToolTip.
    """

    def __init__(self, title: str, message: str) -> None:
        """
        RichToolTip(title, message) -> None
        
        Allows showing a tool tip with more customizations than wxToolTip.
        """

    @overload
    def SetIcon(self, icon: wx.BitmapBundle) -> None:
        ...

    @overload
    def SetIcon(self, icon: int=wx.ICON_INFORMATION) -> None:
        """
        SetIcon(icon=wx.ICON_INFORMATION) -> None
        SetIcon(icon) -> None
        
        Set the small icon to show.
        """

    def SetBackgroundColour(self, col: wx.Colour, colEnd: wx.Colour=wx.Colour()) -> None:
        """
        SetBackgroundColour(col, colEnd=wx.Colour()) -> None
        
        Set the background colour.
        """

    def SetTimeout(self, millisecondsTimeout: int, millisecondsDelay: int=0) -> None:
        """
        SetTimeout(millisecondsTimeout, millisecondsDelay=0) -> None
        
        Set timeout after which the tooltip should disappear and optionally
        set a delay before the tooltip is shown, in milliseconds.
        """

    def SetTipKind(self, tipKind: TipKind) -> None:
        """
        SetTipKind(tipKind) -> None
        
        Choose the tip kind, possibly none.
        """

    def SetTitleFont(self, font: wx.Font) -> None:
        """
        SetTitleFont(font) -> None
        
        Set the title text font.
        """

    def ShowFor(self, win: wx.Window, rect: Optional[wx.Rect]=None) -> None:
        """
        ShowFor(win, rect=None) -> None
        
        Show the tooltip for the given window and optionally specify where to
        show the tooltip.
        """
# end of class RichToolTip

#-- end-richtooltip --#
#-- begin-timectrl --#

class _enum_48(IntEnum):
    TP_DEFAULT = auto()
TP_DEFAULT = _enum_48.TP_DEFAULT
TimePickerCtrlNameStr: str

class TimePickerCtrl(wx.Control):
    """
    TimePickerCtrl() -> None
    TimePickerCtrl(parent, id=wx.ID_ANY, dt=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=TP_DEFAULT, validator=wx.DefaultValidator, name=TimePickerCtrlNameStr) -> None
    
    This control allows the user to enter time.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, dt: wx.DateTime=wx.DefaultDateTime, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=TP_DEFAULT, validator: wx.Validator=wx.DefaultValidator, name: str=TimePickerCtrlNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        TimePickerCtrl() -> None
        TimePickerCtrl(parent, id=wx.ID_ANY, dt=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=TP_DEFAULT, validator=wx.DefaultValidator, name=TimePickerCtrlNameStr) -> None
        
        This control allows the user to enter time.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, dt: wx.DateTime=wx.DefaultDateTime, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=TP_DEFAULT, validator: wx.Validator=wx.DefaultValidator, name: str=TimePickerCtrlNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, dt=wx.DefaultDateTime, pos=wx.DefaultPosition, size=wx.DefaultSize, style=TP_DEFAULT, validator=wx.DefaultValidator, name=TimePickerCtrlNameStr) -> bool
        
        Create the control window.
        """

    def GetTime(self) -> Tuple[int, int, int]:
        """
        GetTime() -> Tuple[int, int, int]
        
        Returns the currently entered time as hours, minutes and seconds.
        """

    def GetValue(self) -> wx.DateTime:
        """
        GetValue() -> wx.DateTime
        
        Returns the currently entered time.
        """

    def SetTime(self, hour: int, min: int, sec: int) -> bool:
        """
        SetTime(hour, min, sec) -> bool
        
        Changes the current time of the control.
        """

    def SetValue(self, dt: wx.DateTime) -> None:
        """
        SetValue(dt) -> None
        
        Changes the current value of the control.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Value(self) -> wx.DateTime: ...
    @Value.setter
    def Value(self, value: wx.DateTime, /) -> None: ...
# end of class TimePickerCtrl

#-- end-timectrl --#
#-- begin-wizard --#
WIZARD_EX_HELPBUTTON: int
WIZARD_VALIGN_TOP: int
WIZARD_VALIGN_CENTRE: int
WIZARD_VALIGN_BOTTOM: int
WIZARD_HALIGN_LEFT: int
WIZARD_HALIGN_CENTRE: int
WIZARD_HALIGN_RIGHT: int
WIZARD_TILE: int
wxEVT_WIZARD_PAGE_CHANGED: int
wxEVT_WIZARD_PAGE_CHANGING: int
wxEVT_WIZARD_CANCEL: int
wxEVT_WIZARD_HELP: int
wxEVT_WIZARD_FINISHED: int
wxEVT_WIZARD_PAGE_SHOWN: int
wxEVT_WIZARD_BEFORE_PAGE_CHANGED: int

class WizardPage(wx.Panel):
    """
    WizardPage() -> None
    WizardPage(parent, bitmap=wx.BitmapBundle()) -> None
    
    wxWizardPage is one of the screens in wxWizard: it must know what are
    the following and preceding pages (which may be NULL for the
    first/last page).
    """

    @overload
    def __init__(self, parent: Wizard, bitmap: wx.BitmapBundle=wx.BitmapBundle()) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        WizardPage() -> None
        WizardPage(parent, bitmap=wx.BitmapBundle()) -> None
        
        wxWizardPage is one of the screens in wxWizard: it must know what are
        the following and preceding pages (which may be NULL for the
        first/last page).
        """

    def Create(self, parent: Wizard, bitmap: wx.BitmapBundle=wx.BitmapBundle()) -> bool:
        """
        Create(parent, bitmap=wx.BitmapBundle()) -> bool
        
        Creates the wizard page.
        """

    def GetBitmap(self) -> wx.Bitmap:
        """
        GetBitmap() -> wx.Bitmap
        
        This method is called by wxWizard to get the bitmap to display
        alongside the page.
        """

    def GetNext(self) -> WizardPage:
        """
        GetNext() -> WizardPage
        
        Get the page which should be shown when the user chooses the "Next"
        button: if NULL is returned, this button will be disabled.
        """

    def GetPrev(self) -> WizardPage:
        """
        GetPrev() -> WizardPage
        
        Get the page which should be shown when the user chooses the "Back"
        button: if NULL is returned, this button will be disabled.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def Bitmap(self) -> wx.Bitmap: ...
    @property
    def Next(self) -> WizardPage: ...
    @property
    def Prev(self) -> WizardPage: ...
# end of class WizardPage


class WizardPageSimple(WizardPage):
    """
    WizardPageSimple() -> None
    WizardPageSimple(parent, prev=None, next=None, bitmap=wx.BitmapBundle()) -> None
    
    wxWizardPageSimple is the simplest possible wxWizardPage
    implementation: it just returns the pointers given to its constructor
    from wxWizardPage::GetNext() and wxWizardPage::GetPrev() functions.
    """

    @overload
    def __init__(self, parent: Wizard, prev: Optional[WizardPage]=None, next: Optional[WizardPage]=None, bitmap: wx.BitmapBundle=wx.BitmapBundle()) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        WizardPageSimple() -> None
        WizardPageSimple(parent, prev=None, next=None, bitmap=wx.BitmapBundle()) -> None
        
        wxWizardPageSimple is the simplest possible wxWizardPage
        implementation: it just returns the pointers given to its constructor
        from wxWizardPage::GetNext() and wxWizardPage::GetPrev() functions.
        """

    def Create(self, parent: Optional[Wizard]=None, prev: Optional[WizardPage]=None, next: Optional[WizardPage]=None, bitmap: wx.BitmapBundle=wx.BitmapBundle()) -> bool:
        """
        Create(parent=None, prev=None, next=None, bitmap=wx.BitmapBundle()) -> bool
        
        Creates the wizard page.
        """

    @overload
    @staticmethod
    def Chain(first: WizardPageSimple, second: WizardPageSimple) -> None:
        ...

    @overload
    def Chain(self, next: WizardPageSimple) -> WizardPageSimple:
        """
        Chain(next) -> WizardPageSimple
        Chain(first, second) -> None
        
        A helper chaining this page with the next one.
        """

    def SetNext(self, next: WizardPage) -> None:
        """
        SetNext(next) -> None
        
        Sets the next page.
        """

    def SetPrev(self, prev: WizardPage) -> None:
        """
        SetPrev(prev) -> None
        
        Sets the previous page.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
# end of class WizardPageSimple


class Wizard(wx.Dialog):
    """
    Wizard() -> None
    Wizard(parent, id=wx.ID_ANY, title='', bitmap=wx.BitmapBundle(), pos=wx.DefaultPosition, style=wx.DEFAULT_DIALOG_STYLE) -> None
    
    wxWizard is the central class for implementing 'wizard-like' dialogs.
    """

    @overload
    def __init__(self, parent: Optional[wx.Window], id: int=wx.ID_ANY, title: str='', bitmap: wx.BitmapBundle=wx.BitmapBundle(), pos: wx.Point=wx.DefaultPosition, style: int=wx.DEFAULT_DIALOG_STYLE) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        Wizard() -> None
        Wizard(parent, id=wx.ID_ANY, title='', bitmap=wx.BitmapBundle(), pos=wx.DefaultPosition, style=wx.DEFAULT_DIALOG_STYLE) -> None
        
        wxWizard is the central class for implementing 'wizard-like' dialogs.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, title: str='', bitmap: wx.BitmapBundle=wx.BitmapBundle(), pos: wx.Point=wx.DefaultPosition, style: int=wx.DEFAULT_DIALOG_STYLE) -> bool:
        """
        Create(parent, id=wx.ID_ANY, title='', bitmap=wx.BitmapBundle(), pos=wx.DefaultPosition, style=wx.DEFAULT_DIALOG_STYLE) -> bool
        
        Creates the wizard dialog.
        """

    def FitToPage(self, firstPage: WizardPage) -> None:
        """
        FitToPage(firstPage) -> None
        
        This method is obsolete, use GetPageAreaSizer() instead.
        """

    def GetBitmap(self) -> wx.Bitmap:
        """
        GetBitmap() -> wx.Bitmap
        
        Returns the bitmap used for the wizard.
        """

    def GetBitmapBackgroundColour(self) -> wx.Colour:
        """
        GetBitmapBackgroundColour() -> wx.Colour
        
        Returns the colour that should be used to fill the area not taken up
        by the wizard or page bitmap, if a non-zero bitmap placement flag has
        been set.
        """

    def GetBitmapPlacement(self) -> int:
        """
        GetBitmapPlacement() -> int
        
        Returns the flags indicating how the wizard or page bitmap should be
        expanded and positioned to fit the page height.
        """

    def GetCurrentPage(self) -> WizardPage:
        """
        GetCurrentPage() -> WizardPage
        
        Get the current page while the wizard is running.
        """

    def GetMinimumBitmapWidth(self) -> int:
        """
        GetMinimumBitmapWidth() -> int
        
        Returns the minimum width for the bitmap that will be constructed to
        contain the actual wizard or page bitmap if a non-zero bitmap
        placement flag has been set.
        """

    def GetPageAreaSizer(self) -> wx.Sizer:
        """
        GetPageAreaSizer() -> wx.Sizer
        
        Returns pointer to page area sizer.
        """

    def GetPageSize(self) -> wx.Size:
        """
        GetPageSize() -> wx.Size
        
        Returns the size available for the pages.
        """

    def HasNextPage(self, page: WizardPage) -> bool:
        """
        HasNextPage(page) -> bool
        
        Return true if this page is not the last one in the wizard.
        """

    def HasPrevPage(self, page: WizardPage) -> bool:
        """
        HasPrevPage(page) -> bool
        
        Returns true if this page is not the first one in the wizard.
        """

    def RunWizard(self, firstPage: WizardPage) -> bool:
        """
        RunWizard(firstPage) -> bool
        
        Executes the wizard starting from the given page, returning true if it
        was successfully finished or false if user cancelled it.
        """

    def SetBitmap(self, bitmap: wx.BitmapBundle) -> None:
        """
        SetBitmap(bitmap) -> None
        
        Sets the bitmap used for the wizard.
        """

    def SetBitmapBackgroundColour(self, colour: wx.Colour) -> None:
        """
        SetBitmapBackgroundColour(colour) -> None
        
        Sets the colour that should be used to fill the area not taken up by
        the wizard or page bitmap, if a non-zero bitmap placement flag has
        been set.
        """

    def SetBitmapPlacement(self, placement: int) -> None:
        """
        SetBitmapPlacement(placement) -> None
        
        Sets the flags indicating how the wizard or page bitmap should be
        expanded and positioned to fit the page height.
        """

    def SetBorder(self, border: int) -> None:
        """
        SetBorder(border) -> None
        
        Sets width of border around page area.
        """

    def SetMinimumBitmapWidth(self, width: int) -> None:
        """
        SetMinimumBitmapWidth(width) -> None
        
        Sets the minimum width for the bitmap that will be constructed to
        contain the actual wizard or page bitmap if a non-zero bitmap
        placement flag has been set.
        """

    def SetPageSize(self, sizePage: wx.Size) -> None:
        """
        SetPageSize(sizePage) -> None
        
        Sets the minimal size to be made available for the wizard pages.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    def ShowPage(self, page: WizardPage, goingForward: bool=True) -> bool:
        """
        ShowPage(page, goingForward=True) -> bool
        
        Show the given wizard page.
        """

    def IsRunning(self) -> bool:
        """
        IsRunning() -> bool
        """
    @property
    def Bitmap(self) -> wx.BitmapBundle: ...
    @Bitmap.setter
    def Bitmap(self, value: wx.BitmapBundle, /) -> None: ...
    @property
    def BitmapBackgroundColour(self) -> wx.Colour: ...
    @BitmapBackgroundColour.setter
    def BitmapBackgroundColour(self, value: wx.Colour, /) -> None: ...
    @property
    def BitmapPlacement(self) -> int: ...
    @BitmapPlacement.setter
    def BitmapPlacement(self, value: int, /) -> None: ...
    @property
    def CurrentPage(self) -> WizardPage: ...
    @property
    def MinimumBitmapWidth(self) -> int: ...
    @MinimumBitmapWidth.setter
    def MinimumBitmapWidth(self, value: int, /) -> None: ...
    @property
    def PageAreaSizer(self) -> wx.Sizer: ...
    @property
    def PageSize(self) -> wx.Size: ...
    @PageSize.setter
    def PageSize(self, value: wx.Size, /) -> None: ...
# end of class Wizard


class WizardEvent(wx.NotifyEvent):
    """
    WizardEvent(type=wx.wxEVT_NULL, id=wx.ID_ANY, direction=True, page=0) -> None
    
    wxWizardEvent class represents an event generated by the wxWizard:
    this event is first sent to the page itself and, if not processed
    there, goes up the window hierarchy as usual.
    """

    def __init__(self, type: EventType=wx.wxEVT_NULL, id: int=wx.ID_ANY, direction: bool=True, page: WizardPage=0) -> None:
        """
        WizardEvent(type=wx.wxEVT_NULL, id=wx.ID_ANY, direction=True, page=0) -> None
        
        wxWizardEvent class represents an event generated by the wxWizard:
        this event is first sent to the page itself and, if not processed
        there, goes up the window hierarchy as usual.
        """

    def GetDirection(self) -> bool:
        """
        GetDirection() -> bool
        
        Return the direction in which the page is changing: for
        EVT_WIZARD_PAGE_CHANGING, return true if we're going forward or false
        otherwise and for EVT_WIZARD_PAGE_CHANGED return true if we came from
        the previous page and false if we returned from the next one.
        """

    def GetPage(self) -> WizardPage:
        """
        GetPage() -> WizardPage
        
        Returns the wxWizardPage which was active when this event was
        generated.
        """
    @property
    def Direction(self) -> bool: ...
    @property
    def Page(self) -> WizardPage: ...
# end of class WizardEvent


PyWizardPage = wx.deprecated(WizardPage, 'Use WizardPage instead.')

EVT_WIZARD_BEFORE_PAGE_CHANGED  = wx.PyEventBinder( wxEVT_WIZARD_BEFORE_PAGE_CHANGED, 1)
EVT_WIZARD_PAGE_CHANGED  = wx.PyEventBinder( wxEVT_WIZARD_PAGE_CHANGED, 1)
EVT_WIZARD_PAGE_CHANGING = wx.PyEventBinder( wxEVT_WIZARD_PAGE_CHANGING, 1)
EVT_WIZARD_CANCEL        = wx.PyEventBinder( wxEVT_WIZARD_CANCEL, 1)
EVT_WIZARD_HELP          = wx.PyEventBinder( wxEVT_WIZARD_HELP, 1)
EVT_WIZARD_FINISHED      = wx.PyEventBinder( wxEVT_WIZARD_FINISHED, 1)
EVT_WIZARD_PAGE_SHOWN    = wx.PyEventBinder( wxEVT_WIZARD_PAGE_SHOWN, 1)
#-- end-wizard --#
#-- begin-pseudodc --#

class PseudoDC(wx.Object):
    """
    PseudoDC() -> None
    
    A PseudoDC is an object that can be used much like real
    :class:`wx.DC`, however it provides some additional features for
    object recording and manipulation beyond what a ``wx.DC`` can
    provide.
    
    All commands issued to the ``PseudoDC`` are stored in a list.  You
    can then play these commands back to a real DC object as often as
    needed, using the :meth:`DrawToDC` method or one of the similar
    methods.  Commands in the command list can be tagged by an ID. You
    can use this ID to clear the operations associated with a single
    ID, redraw the objects associated with that ID, grey them, adjust
    their position, etc.
    """

    def __init__(self) -> None:
        """
        PseudoDC() -> None
        
        A PseudoDC is an object that can be used much like real
        :class:`wx.DC`, however it provides some additional features for
        object recording and manipulation beyond what a ``wx.DC`` can
        provide.
        
        All commands issued to the ``PseudoDC`` are stored in a list.  You
        can then play these commands back to a real DC object as often as
        needed, using the :meth:`DrawToDC` method or one of the similar
        methods.  Commands in the command list can be tagged by an ID. You
        can use this ID to clear the operations associated with a single
        ID, redraw the objects associated with that ID, grey them, adjust
        their position, etc.
        """

    def RemoveAll(self) -> None:
        """
        RemoveAll() -> None
        
        Removes all objects and operations from the recorded list.
        """

    def GetLen(self) -> int:
        """
        GetLen() -> int
        
        Returns the number of operations in the recorded list.
        """

    def SetId(self, id: int) -> None:
        """
        SetId(id) -> None
        
        Sets the id to be associated with subsequent operations.
        """

    def ClearId(self, id: int) -> None:
        """
        ClearId(id) -> None
        
        Removes all operations associated with id so the object can be
        redrawn.
        """

    def RemoveId(self, id: int) -> None:
        """
        RemoveId(id) -> None
        
        Remove the object node (and all operations) associated with an id.
        """

    def TranslateId(self, id: int, dx: int, dy: int) -> None:
        """
        TranslateId(id, dx, dy) -> None
        
        Translate the position of the operations of tag `id` by (`dx`, `dy`).
        """

    def SetIdGreyedOut(self, id: int, greyout: bool) -> None:
        """
        SetIdGreyedOut(id, greyout) -> None
        
        Set whether the set of objects with tag `id` are drawn greyed out or
        not.
        """

    def GetIdGreyedOut(self, id: int) -> bool:
        """
        GetIdGreyedOut(id) -> bool
        
        Get whether the set of objects with tag `id` are drawn greyed out or
        not.
        """

    def FindObjects(self, x: int, y: int, radius: int=1, bg: wx.Colour=wx.WHITE) -> Any:
        """
        FindObjects(x, y, radius=1, bg=wx.WHITE) -> Any
        
        Returns a list of all the id's that draw a pixel with
        color not equal to bg within radius of (x,y). Returns an
        empty list if nothing is found.  The list is in reverse
        drawing order so list[0] is the top id.
        """

    def FindObjectsByBBox(self, x: int, y: int) -> Any:
        """
        FindObjectsByBBox(x, y) -> Any
        
        Returns a list of all the id's whose bounding boxes include (x,y).
        Returns an empty list if nothing is found.  The list is in
        reverse drawing order so list[0] is the top id.
        """

    def DrawIdToDC(self, id: int, dc: wx.DC) -> None:
        """
        DrawIdToDC(id, dc) -> None
        
        Draw recorded operations tagged with id to dc.
        """

    def SetIdBounds(self, id: int, rect: wx.Rect) -> None:
        """
        SetIdBounds(id, rect) -> None
        
        Set the bounding rect of a given object.
        This will create an object node if one doesn't exist.
        """

    def GetIdBounds(self, id: int) -> wx.Rect:
        """
        GetIdBounds(id) -> wx.Rect
        
        Returns the bounding rectangle previously set with `SetIdBounds`.
        If no bounds have been set, it returns wx.Rect(0,0,0,0).
        """

    def DrawToDCClipped(self, dc: wx.DC, rect: wx.Rect) -> None:
        """
        DrawToDCClipped(dc, rect) -> None
        
        Draws the recorded operations to dc,
        unless the operation is known to be outside of rect.
        """

    def DrawToDCClippedRgn(self, dc: wx.DC, region: wx.Region) -> None:
        """
        DrawToDCClippedRgn(dc, region) -> None
        
        Draws the recorded operations to dc,
        unless the operation is known to be outside the given region.
        """

    def DrawToDC(self, dc: wx.DC) -> None:
        """
        DrawToDC(dc) -> None
        
        Draws the recorded operations to dc.
        """

    @overload
    def FloodFill(self, pt: wx.Point, col: wx.Colour, style: wx.FloodFillStyle=wx.FLOOD_SURFACE) -> None:
        ...

    @overload
    def FloodFill(self, x: int, y: int, col: wx.Colour, style: wx.FloodFillStyle=wx.FLOOD_SURFACE) -> None:
        """
        FloodFill(x, y, col, style=wx.FLOOD_SURFACE) -> None
        FloodFill(pt, col, style=wx.FLOOD_SURFACE) -> None
        
        Flood fills the device context starting from the given point,
        using the current brush colour, and using a style:
        
            - ``wx.FLOOD_SURFACE``: the flooding occurs until a colour other
        than the given colour is encountered.
        
            - ``wx.FLOOD_BORDER``: the area to be flooded is bounded by the
        given colour.
        """

    @overload
    def DrawLine(self, pt1: wx.Point, pt2: wx.Point) -> None:
        ...

    @overload
    def DrawLine(self, x1: int, y1: int, x2: int, y2: int) -> None:
        """
        DrawLine(x1, y1, x2, y2) -> None
        DrawLine(pt1, pt2) -> None
        
        Draws a line from the first point to the second.
        The current pen is used for drawing the line. Note that
        the second point is *not* part of the line and is not
        drawn by this function (this is consistent with the
        behaviour of many other toolkits).
        """

    @overload
    def CrossHair(self, pt: wx.Point) -> None:
        ...

    @overload
    def CrossHair(self, x: int, y: int) -> None:
        """
        CrossHair(x, y) -> None
        CrossHair(pt) -> None
        
        Displays a cross hair using the current pen. This is a
        vertical and horizontal line the height and width of the
        window, centred on the given point.
        """

    def DrawArc(self, x1: int, y1: int, x2: int, y2: int, xc: int, yc: int) -> None:
        """
        DrawArc(x1, y1, x2, y2, xc, yc) -> None
        
        Draws an arc of a circle, centred on the *center* point
        (xc, yc), from the first point to the second. The current
        pen is used for the outline and the current brush for
        filling the shape.
        
        The arc is drawn in an anticlockwise direction from the
        start point to the end point.
        """

    @overload
    def DrawCheckMark(self, rect: wx.Rect) -> None:
        ...

    @overload
    def DrawCheckMark(self, x: int, y: int, width: int, height: int) -> None:
        """
        DrawCheckMark(x, y, width, height) -> None
        DrawCheckMark(rect) -> None
        
        Draws a check mark inside the given rectangle
        """

    @overload
    def DrawEllipticArc(self, pt: wx.Point, sz: wx.Size, start: float, end: float) -> None:
        ...

    @overload
    def DrawEllipticArc(self, x: int, y: int, w: int, h: int, start: float, end: float) -> None:
        """
        DrawEllipticArc(x, y, w, h, start, end) -> None
        DrawEllipticArc(pt, sz, start, end) -> None
        
        Draws an arc of an ellipse, with the given rectangle
        defining the bounds of the ellipse. The current pen is
        used for drawing the arc and the current brush is used for
        drawing the pie.
        
        The *start* and *end* parameters specify the start and end
        of the arc relative to the three-o'clock position from the
        center of the rectangle. Angles are specified in degrees
        (360 is a complete circle). Positive values mean
        counter-clockwise motion. If start is equal to end, a
        complete ellipse will be drawn.
        """

    @overload
    def DrawPoint(self, pt: wx.Point) -> None:
        ...

    @overload
    def DrawPoint(self, x: int, y: int) -> None:
        """
        DrawPoint(x, y) -> None
        DrawPoint(pt) -> None
        
        Draws a point using the current pen.
        """

    @overload
    def DrawRectangle(self, rect: wx.Rect) -> None:
        ...

    @overload
    def DrawRectangle(self, pt: wx.Point, sz: wx.Size) -> None:
        ...

    @overload
    def DrawRectangle(self, x: int, y: int, width: int, height: int) -> None:
        """
        DrawRectangle(x, y, width, height) -> None
        DrawRectangle(rect) -> None
        DrawRectangle(pt, sz) -> None
        
        Draws a rectangle with the given top left corner, and with
        the given size. The current pen is used for the outline
        and the current brush for filling the shape.
        """

    @overload
    def DrawRoundedRectangle(self, rect: wx.Rect, radius: float) -> None:
        ...

    @overload
    def DrawRoundedRectangle(self, pt: wx.Point, sz: wx.Size, radius: float) -> None:
        ...

    @overload
    def DrawRoundedRectangle(self, x: int, y: int, width: int, height: int, radius: float) -> None:
        """
        DrawRoundedRectangle(x, y, width, height, radius) -> None
        DrawRoundedRectangle(rect, radius) -> None
        DrawRoundedRectangle(pt, sz, radius) -> None
        
        Draws a rectangle with the given top left corner, and with
        the given size. The current pen is used for the outline
        and the current brush for filling the shape.
        """

    @overload
    def DrawCircle(self, pt: wx.Point, radius: int) -> None:
        ...

    @overload
    def DrawCircle(self, x: int, y: int, radius: int) -> None:
        """
        DrawCircle(x, y, radius) -> None
        DrawCircle(pt, radius) -> None
        
        Draws a circle with the given center point and radius.
        The current pen is used for the outline and the current
        brush for filling the shape.
        
        :see: `DrawEllipse`
        """

    @overload
    def DrawEllipse(self, rect: wx.Rect) -> None:
        ...

    @overload
    def DrawEllipse(self, pt: wx.Point, sz: wx.Size) -> None:
        ...

    @overload
    def DrawEllipse(self, x: int, y: int, width: int, height: int) -> None:
        """
        DrawEllipse(x, y, width, height) -> None
        DrawEllipse(rect) -> None
        DrawEllipse(pt, sz) -> None
        
        Draws an ellipse contained in the specified rectangle. The current pen
        is used for the outline and the current brush for filling the shape.",
        "
        
        :see: `DrawCircle`
        """

    @overload
    def DrawIcon(self, icon: wx.Icon, pt: wx.Point) -> None:
        ...

    @overload
    def DrawIcon(self, icon: wx.Icon, x: int, y: int) -> None:
        """
        DrawIcon(icon, x, y) -> None
        DrawIcon(icon, pt) -> None
        
        Draw an icon on the display at the given position.
        """

    @overload
    def DrawBitmap(self, bmp: wx.Bitmap, pt: wx.Point, useMask: bool=False) -> None:
        ...

    @overload
    def DrawBitmap(self, bmp: wx.Bitmap, x: int, y: int, useMask: bool=False) -> None:
        """
        DrawBitmap(bmp, x, y, useMask=False) -> None
        DrawBitmap(bmp, pt, useMask=False) -> None
        
        Draw a bitmap on the device context at the specified
        point. If *useMask* is true and the bitmap has a
        transparency mask, (or alpha channel on the platforms that
        support it) then the bitmap will be drawn transparently.
        
        When drawing a mono-bitmap, the current text foreground
        colour will be used to draw the foreground of the bitmap
        (all bits set to 1), and the current text background
        colour to draw the background (all bits set to 0).
        
        :see: `SetTextForeground`, `SetTextBackground` and `wx.MemoryDC`
        """

    @overload
    def DrawText(self, text: str, pt: wx.Point) -> None:
        ...

    @overload
    def DrawText(self, text: str, x: int, y: int) -> None:
        """
        DrawText(text, x, y) -> None
        DrawText(text, pt) -> None
        
        Draws a text string at the specified point, using the
        current text font, and the current text foreground and
        background colours.
        
        The coordinates refer to the top-left corner of the
        rectangle bounding the string. See `wx.DC.GetTextExtent`
        for how to get the dimensions of a text string, which can
        be used to position the text more precisely, (you will
        need to use a real DC with GetTextExtent as wx.PseudoDC
        does not implement it.)
        
        **NOTE**: under wxGTK the current logical function is used
        *by this function but it is ignored by wxMSW. Thus, you
        *should avoid using logical functions with this function
        *in portable programs.", "
        
        :see: `DrawRotatedText`
        """

    @overload
    def DrawRotatedText(self, text: str, pt: wx.Point, angle: float) -> None:
        ...

    @overload
    def DrawRotatedText(self, text: str, x: int, y: int, angle: float) -> None:
        """
        DrawRotatedText(text, x, y, angle) -> None
        DrawRotatedText(text, pt, angle) -> None
        
        Draws the text rotated by *angle* degrees, if supported by the
        platform.
        """

    @overload
    def DrawLabel(self, text: str, image: wx.Bitmap, rect: wx.Rect, alignment: int=wx.ALIGN_LEFT|wx.ALIGN_TOP, indexAccel: int=-1) -> None:
        ...

    @overload
    def DrawLabel(self, text: str, rect: wx.Rect, alignment: int=wx.ALIGN_LEFT|wx.ALIGN_TOP, indexAccel: int=-1) -> None:
        """
        DrawLabel(text, rect, alignment=wx.ALIGN_LEFT|wx.ALIGN_TOP, indexAccel=-1) -> None
        DrawLabel(text, image, rect, alignment=wx.ALIGN_LEFT|wx.ALIGN_TOP, indexAccel=-1) -> None
        
        Draw *text* within the specified rectangle, abiding by the
        alignment flags.  Will additionally emphasize the
        character at *indexAccel* if it is not -1.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Clears the device context using the current background brush.
        """

    def SetFont(self, font: wx.Font) -> None:
        """
        SetFont(font) -> None
        
        Sets the current font for the DC. It must be a valid font, in
        particular you should not pass ``wx.NullFont`` to this method.
        
        :see: `wx.Font`
        """

    def SetPen(self, pen: wx.Pen) -> None:
        """
        SetPen(pen) -> None
        
        Sets the current pen for the DC.
        
        If the argument is ``wx.NullPen``, the current pen is selected out of
        the
        device context, and the original pen restored.
        
        :see: `wx.Pen`
        """

    def SetBrush(self, brush: wx.Brush) -> None:
        """
        SetBrush(brush) -> None
        
        Sets the current brush for the DC.
        
        If the argument is ``wx.NullBrush``, the current brush is selected out
        of the device context, and the original brush restored, allowing the
        current brush to be destroyed safely.
        
        :see: `wx.Brush`
        """

    def SetBackground(self, brush: wx.Brush) -> None:
        """
        SetBackground(brush) -> None
        
        Sets the current background brush for the DC.
        """

    def SetBackgroundMode(self, mode: int) -> None:
        """
        SetBackgroundMode(mode) -> None
        
        The *mode* parameter may be one of ``wx.SOLID`` and
        ``wx.TRANSPARENT``. This setting determines whether text
        will be drawn with a background colour or not.
        """

    def SetTextForeground(self, colour: wx.Colour) -> None:
        """
        SetTextForeground(colour) -> None
        
        Sets the current text foreground colour for the DC.
        """

    def SetTextBackground(self, colour: wx.Colour) -> None:
        """
        SetTextBackground(colour) -> None
        
        Sets the current text background colour for the DC.
        """

    def SetLogicalFunction(self, function: wx.RasterOperationMode) -> None:
        """
        SetLogicalFunction(function) -> None
        
        Sets the current logical function for the device context. This
        determines how a source pixel (from a pen or brush colour, combines
        with a destination pixel in the current device context.
        
        The possible values and their meaning in terms of source and
        destination pixel values are defined in the
        :ref:`wx.RasterOperationMode`
        enumeration.
        
        The default is wx.COPY, which simply draws with the current
        colour. The others combine the current colour and the background using
        a logical operation. wx.INVERT is commonly used for drawing rubber
        bands or moving outlines, since drawing twice reverts to the original
        colour.
        """

    def DrawLines(self, points: PointList, xoffset: int=0, yoffset: int=0) -> None:
        """
        DrawLines(points, xoffset=0, yoffset=0) -> None
        
        Draws lines using a sequence of `wx.Point` objects, adding the
        optional offset coordinate. The current pen is used for drawing the
        lines.
        """

    def DrawPolygon(self, points: PointList, xoffset: int=0, yoffset: int=0, fillStyle: wx.PolygonFillMode=wx.ODDEVEN_RULE) -> None:
        """
        DrawPolygon(points, xoffset=0, yoffset=0, fillStyle=wx.ODDEVEN_RULE) -> None
        
        Draws a filled polygon using a sequence of `wx.Point` objects, adding
        the optional offset coordinate.  The last argument specifies the fill
        rule: ``wx.ODDEVEN_RULE`` (the default) or ``wx.WINDING_RULE``.
        
        The current pen is used for drawing the outline, and the current brush
        for filling the shape. Using a transparent brush suppresses
        filling. Note that wxWidgets automatically closes the first and last
        points.
        """

    def DrawSpline(self, points: PointList) -> None:
        """
        DrawSpline(points) -> None
        
        Draws a spline between all given control points, (a list of `wx.Point`
        objects) using the current pen. The spline is drawn using a series of
        lines, using an algorithm taken from the X drawing program 'XFIG'.
        """

    BeginDrawing = wx.deprecated(lambda *args: None, 'BeginDrawing has been removed.')
    EndDrawing = wx.deprecated(lambda *args: None, 'EndDrawing has been removed.')
    FloodFillPoint = wx.deprecated(FloodFill, 'Use FloodFill instead.')
    DrawLinePoint = wx.deprecated(DrawLine, 'Use DrawLine instead.')
    CrossHairPoint = wx.deprecated(CrossHair, 'Use CrossHair instead.')
    DrawArcPoint = wx.deprecated(DrawArc, 'Use DrawArc instead.')
    DrawCheckMarkRect = wx.deprecated(DrawCheckMark, 'Use DrawArc instead.')
    DrawEllipticArcPointSize = wx.deprecated(DrawEllipticArc, 'Use DrawEllipticArc instead.')
    DrawPointPoint = wx.deprecated(DrawPoint, 'Use DrawPoint instead.')
    DrawRectangleRect = wx.deprecated(DrawRectangle, 'Use DrawRectangle instead.')
    DrawRectanglePointSize = wx.deprecated(DrawRectangle, 'Use DrawRectangle instead.')
    DrawRoundedRectangleRect = wx.deprecated(DrawRoundedRectangle, 'Use DrawRectangle instead.')
    DrawRoundedRectanglePointSize = wx.deprecated(DrawRoundedRectangle, 'Use DrawRectangle instead.')
    DrawCirclePoint = wx.deprecated(DrawCircle, 'Use DrawCircle instead.')
    DrawEllipseRect = wx.deprecated(DrawEllipse, 'Use DrawEllipse instead.')
    DrawEllipsePointSize = wx.deprecated(DrawEllipse, 'Use DrawEllipse instead.')
    DrawIconPoint = wx.deprecated(DrawIcon, 'Use DrawIcon instead.')
    DrawBitmapPoint = wx.deprecated(DrawBitmap, 'Use DrawBitmap instead.')
    DrawTextPoint = wx.deprecated(DrawText, 'Use DrawText instead.')
    DrawRotatedTextPoint = wx.deprecated(DrawRotatedText, 'Use DrawRotatedText instead.')
    DrawImageLabel = wx.deprecated(DrawLabel, 'Use DrawLabel instead.')
    @property
    def Len(self) -> int: ...
# end of class PseudoDC

#-- end-pseudodc --#
#-- begin-propdlg --#

class _PropertySheetDialogFlags(IntFlag):
    PROPSHEET_DEFAULT = auto()
    PROPSHEET_NOTEBOOK = auto()
    PROPSHEET_TOOLBOOK = auto()
    PROPSHEET_CHOICEBOOK = auto()
    PROPSHEET_LISTBOOK = auto()
    PROPSHEET_BUTTONTOOLBOOK = auto()
    PROPSHEET_TREEBOOK = auto()
    PROPSHEET_SHRINKTOFIT = auto()
PropertySheetDialogFlags: TypeAlias = Union[_PropertySheetDialogFlags, int]
PROPSHEET_DEFAULT = _PropertySheetDialogFlags.PROPSHEET_DEFAULT
PROPSHEET_NOTEBOOK = _PropertySheetDialogFlags.PROPSHEET_NOTEBOOK
PROPSHEET_TOOLBOOK = _PropertySheetDialogFlags.PROPSHEET_TOOLBOOK
PROPSHEET_CHOICEBOOK = _PropertySheetDialogFlags.PROPSHEET_CHOICEBOOK
PROPSHEET_LISTBOOK = _PropertySheetDialogFlags.PROPSHEET_LISTBOOK
PROPSHEET_BUTTONTOOLBOOK = _PropertySheetDialogFlags.PROPSHEET_BUTTONTOOLBOOK
PROPSHEET_TREEBOOK = _PropertySheetDialogFlags.PROPSHEET_TREEBOOK
PROPSHEET_SHRINKTOFIT = _PropertySheetDialogFlags.PROPSHEET_SHRINKTOFIT

class PropertySheetDialog(wx.Dialog):
    """
    PropertySheetDialog() -> None
    PropertySheetDialog(parent, id=wx.ID_ANY, title='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_DIALOG_STYLE, name=wx.DialogNameStr) -> None
    
    This class represents a property sheet dialog: a tabbed dialog for
    showing settings.
    """

    @overload
    def __init__(self, parent: Optional[wx.Window], id: int=wx.ID_ANY, title: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.DEFAULT_DIALOG_STYLE, name: str=wx.DialogNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        PropertySheetDialog() -> None
        PropertySheetDialog(parent, id=wx.ID_ANY, title='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_DIALOG_STYLE, name=wx.DialogNameStr) -> None
        
        This class represents a property sheet dialog: a tabbed dialog for
        showing settings.
        """

    def AddBookCtrl(self, sizer: wx.Sizer) -> None:
        """
        AddBookCtrl(sizer) -> None
        
        Override this if you wish to add the book control in a way different
        from the standard way (for example, using different spacing).
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, title: str='', pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.DEFAULT_DIALOG_STYLE, name: str=wx.DialogNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, title='', pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.DEFAULT_DIALOG_STYLE, name=wx.DialogNameStr) -> bool
        
        Call this from your own Create function, before adding buttons and
        pages.
        """

    def CreateBookCtrl(self) -> wx.BookCtrlBase:
        """
        CreateBookCtrl() -> wx.BookCtrlBase
        
        Override this if you wish to create a different kind of book control;
        by default, the value passed to SetSheetStyle() is used to determine
        the control.
        """

    def CreateButtons(self, flags: int=wx.OK|wx.CANCEL) -> None:
        """
        CreateButtons(flags=wx.OK|wx.CANCEL) -> None
        
        Call this to create the buttons for the dialog.
        """

    def GetBookCtrl(self) -> wx.BookCtrlBase:
        """
        GetBookCtrl() -> wx.BookCtrlBase
        
        Returns the book control that will contain your settings pages.
        """

    def GetInnerSizer(self) -> wx.Sizer:
        """
        GetInnerSizer() -> wx.Sizer
        
        Returns the inner sizer that contains the book control and button
        sizer.
        """

    def SetInnerSizer(self, sizer: wx.Sizer) -> None:
        """
        SetInnerSizer(sizer) -> None
        
        Set the inner sizer that contains the book control and button sizer.
        """

    def GetSheetStyle(self) -> int:
        """
        GetSheetStyle() -> int
        
        Returns the sheet style.
        """

    def LayoutDialog(self, centreFlags: int=wx.BOTH) -> None:
        """
        LayoutDialog(centreFlags=wx.BOTH) -> None
        
        Call this to lay out the dialog.
        """

    def SetBookCtrl(self, bookCtrl: wx.BookCtrlBase) -> None:
        """
        SetBookCtrl(bookCtrl) -> None
        
        Sets the book control used for the dialog.
        """

    def SetSheetStyle(self, style: int) -> None:
        """
        SetSheetStyle(style) -> None
        
        You can customize the look and feel of the dialog by setting the sheet
        style.
        """

    def SetSheetOuterBorder(self, border: int) -> None:
        """
        SetSheetOuterBorder(border) -> None
        
        Set the border around the whole dialog.
        """

    def GetSheetOuterBorder(self) -> int:
        """
        GetSheetOuterBorder() -> int
        
        Returns the border around the whole dialog.
        """

    def SetSheetInnerBorder(self, border: int) -> None:
        """
        SetSheetInnerBorder(border) -> None
        
        Set the border around the book control only.
        """

    def GetSheetInnerBorder(self) -> int:
        """
        GetSheetInnerBorder() -> int
        
        Returns the border around the book control only.
        """

    def GetContentWindow(self) -> wx.Window:
        """
        GetContentWindow() -> wx.Window
        
        Override this to return a window containing the main content of the
        dialog.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def BookCtrl(self) -> wx.BookCtrlBase: ...
    @BookCtrl.setter
    def BookCtrl(self, value: wx.BookCtrlBase, /) -> None: ...
    @property
    def ContentWindow(self) -> wx.Window: ...
    @property
    def InnerSizer(self) -> wx.Sizer: ...
    @InnerSizer.setter
    def InnerSizer(self, value: wx.Sizer, /) -> None: ...
    @property
    def SheetInnerBorder(self) -> int: ...
    @SheetInnerBorder.setter
    def SheetInnerBorder(self, value: int, /) -> None: ...
    @property
    def SheetOuterBorder(self) -> int: ...
    @SheetOuterBorder.setter
    def SheetOuterBorder(self, value: int, /) -> None: ...
    @property
    def SheetStyle(self) -> int: ...
    @SheetStyle.setter
    def SheetStyle(self, value: int, /) -> None: ...
# end of class PropertySheetDialog

#-- end-propdlg --#
