// qnearfieldmanager.sip generated by MetaSIP
//
// This file is part of the QtNfc Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QNearFieldManager : public QObject
{
%TypeHeaderCode
#include <qnearfieldmanager.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QNearFieldManager, &sipType_QNearFieldManager, -1, 1},
        {sipName_QNearFieldTarget, &sipType_QNearFieldTarget, -1, 2},
        {sipName_QNearFieldShareManager, &sipType_QNearFieldShareManager, -1, 3},
        {sipName_QQmlNdefRecord, &sipType_QQmlNdefRecord, -1, 4},
        {sipName_QNearFieldShareTarget, &sipType_QNearFieldShareTarget, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum TargetAccessMode
    {
        NoTargetAccess,
        NdefReadTargetAccess,
        NdefWriteTargetAccess,
        TagTypeSpecificTargetAccess,
    };

    typedef QFlags<QNearFieldManager::TargetAccessMode> TargetAccessModes;
    explicit QNearFieldManager(QObject *parent /TransferThis/ = 0);
    virtual ~QNearFieldManager();
    bool isAvailable() const;
    void setTargetAccessModes(QNearFieldManager::TargetAccessModes accessModes);
    QNearFieldManager::TargetAccessModes targetAccessModes() const;
    bool startTargetDetection();
    void stopTargetDetection();
    int registerNdefMessageHandler(SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot;
        
        if ((sipError = pyqt5_qtnfc_get_pyqtslot_parts(a0, &receiver, slot)) == sipErrorNone)
        {
            sipRes = sipCpp->registerNdefMessageHandler(receiver, slot.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End

    int registerNdefMessageHandler(QNdefRecord::TypeNameFormat typeNameFormat, const QByteArray &type, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot;
        
        if ((sipError = pyqt5_qtnfc_get_pyqtslot_parts(a2, &receiver, slot)) == sipErrorNone)
        {
            sipRes = sipCpp->registerNdefMessageHandler(a0, *a1, receiver, slot.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(2, a2);
        }
%End

    int registerNdefMessageHandler(const QNdefFilter &filter, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot;
        
        if ((sipError = pyqt5_qtnfc_get_pyqtslot_parts(a1, &receiver, slot)) == sipErrorNone)
        {
            sipRes = sipCpp->registerNdefMessageHandler(*a0, receiver, slot.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(1, a1);
        }
%End

    bool unregisterNdefMessageHandler(int handlerId);

signals:
    void targetDetected(QNearFieldTarget *target);
    void targetLost(QNearFieldTarget *target);

public:
%If (Qt_5_12_0 -)

    enum class AdapterState
    {
        Offline,
        TurningOn,
        Online,
        TurningOff,
    };

%End
%If (Qt_5_12_0 -)
    bool isSupported() const;
%End

signals:
%If (Qt_5_12_0 -)
    void adapterStateChanged(QNearFieldManager::AdapterState state /ScopesStripped=1/);
%End
};

%End
%If (Qt_5_5_0 -)
QFlags<QNearFieldManager::TargetAccessMode> operator|(QNearFieldManager::TargetAccessMode f1, QFlags<QNearFieldManager::TargetAccessMode> f2);
%End

%ModuleHeaderCode
// Imports from QtCore.
typedef sipErrorState (*pyqt5_qtnfc_get_pyqtslot_parts_t)(PyObject *, QObject **, QByteArray &);
extern pyqt5_qtnfc_get_pyqtslot_parts_t pyqt5_qtnfc_get_pyqtslot_parts;
%End

%ModuleCode
// Imports from QtCore.
pyqt5_qtnfc_get_pyqtslot_parts_t pyqt5_qtnfc_get_pyqtslot_parts;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt5_qtnfc_get_pyqtslot_parts = (pyqt5_qtnfc_get_pyqtslot_parts_t)sipImportSymbol("pyqt5_get_pyqtslot_parts");
Q_ASSERT(pyqt5_qtnfc_get_pyqtslot_parts);
%End
