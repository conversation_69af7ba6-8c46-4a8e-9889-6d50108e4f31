# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
The :ref:`wx.stc.StyledTextCrtl` class provided by this module is a text widget
primarily intended for use as a syntax highlighting source code editor.  It is
based on the popular Scintilla widget.
"""
#-- begin-_stc --#

import wx
ID_ANY = wx.ID_ANY  # Needed for some parameter defaults in this module
STC_INVALID_POSITION: int
STC_START: int
STC_OPTIONAL_START: int
STC_LEXER_START: int
STC_WS_INVISIBLE: int
STC_WS_VISIBLEALWAYS: int
STC_WS_VISIBLEAFTERINDENT: int
STC_WS_VISIBLEONLYININDENT: int
STC_TD_LONGARROW: int
STC_TD_STRIKEOUT: int
STC_EOL_CRLF: int
STC_EOL_CR: int
STC_EOL_LF: int
STC_CP_UTF8: int
STC_IME_WINDOWED: int
STC_IME_INLINE: int
STC_MARKER_MAX: int
STC_MARK_CIRCLE: int
STC_MARK_ROUNDRECT: int
STC_MARK_ARROW: int
STC_MARK_SMALLRECT: int
STC_MARK_SHORTARROW: int
STC_MARK_EMPTY: int
STC_MARK_ARROWDOWN: int
STC_MARK_MINUS: int
STC_MARK_PLUS: int
STC_MARK_VLINE: int
STC_MARK_LCORNER: int
STC_MARK_TCORNER: int
STC_MARK_BOXPLUS: int
STC_MARK_BOXPLUSCONNECTED: int
STC_MARK_BOXMINUS: int
STC_MARK_BOXMINUSCONNECTED: int
STC_MARK_LCORNERCURVE: int
STC_MARK_TCORNERCURVE: int
STC_MARK_CIRCLEPLUS: int
STC_MARK_CIRCLEPLUSCONNECTED: int
STC_MARK_CIRCLEMINUS: int
STC_MARK_CIRCLEMINUSCONNECTED: int
STC_MARK_BACKGROUND: int
STC_MARK_DOTDOTDOT: int
STC_MARK_ARROWS: int
STC_MARK_PIXMAP: int
STC_MARK_FULLRECT: int
STC_MARK_LEFTRECT: int
STC_MARK_AVAILABLE: int
STC_MARK_UNDERLINE: int
STC_MARK_RGBAIMAGE: int
STC_MARK_BOOKMARK: int
STC_MARK_CHARACTER: int
STC_MARKNUM_FOLDEREND: int
STC_MARKNUM_FOLDEROPENMID: int
STC_MARKNUM_FOLDERMIDTAIL: int
STC_MARKNUM_FOLDERTAIL: int
STC_MARKNUM_FOLDERSUB: int
STC_MARKNUM_FOLDER: int
STC_MARKNUM_FOLDEROPEN: int
STC_MASK_FOLDERS: int
STC_MAX_MARGIN: int
STC_MARGIN_SYMBOL: int
STC_MARGIN_NUMBER: int
STC_MARGIN_BACK: int
STC_MARGIN_FORE: int
STC_MARGIN_TEXT: int
STC_MARGIN_RTEXT: int
STC_MARGIN_COLOUR: int
STC_STYLE_DEFAULT: int
STC_STYLE_LINENUMBER: int
STC_STYLE_BRACELIGHT: int
STC_STYLE_BRACEBAD: int
STC_STYLE_CONTROLCHAR: int
STC_STYLE_INDENTGUIDE: int
STC_STYLE_CALLTIP: int
STC_STYLE_FOLDDISPLAYTEXT: int
STC_STYLE_LASTPREDEFINED: int
STC_STYLE_MAX: int
STC_CHARSET_ANSI: int
STC_CHARSET_DEFAULT: int
STC_CHARSET_BALTIC: int
STC_CHARSET_CHINESEBIG5: int
STC_CHARSET_EASTEUROPE: int
STC_CHARSET_GB2312: int
STC_CHARSET_GREEK: int
STC_CHARSET_HANGUL: int
STC_CHARSET_MAC: int
STC_CHARSET_OEM: int
STC_CHARSET_RUSSIAN: int
STC_CHARSET_OEM866: int
STC_CHARSET_CYRILLIC: int
STC_CHARSET_SHIFTJIS: int
STC_CHARSET_SYMBOL: int
STC_CHARSET_TURKISH: int
STC_CHARSET_JOHAB: int
STC_CHARSET_HEBREW: int
STC_CHARSET_ARABIC: int
STC_CHARSET_VIETNAMESE: int
STC_CHARSET_THAI: int
STC_CHARSET_8859_15: int
STC_CASE_MIXED: int
STC_CASE_UPPER: int
STC_CASE_LOWER: int
STC_CASE_CAMEL: int
STC_FONT_SIZE_MULTIPLIER: int
STC_WEIGHT_NORMAL: int
STC_WEIGHT_SEMIBOLD: int
STC_WEIGHT_BOLD: int
STC_INDIC_PLAIN: int
STC_INDIC_SQUIGGLE: int
STC_INDIC_TT: int
STC_INDIC_DIAGONAL: int
STC_INDIC_STRIKE: int
STC_INDIC_HIDDEN: int
STC_INDIC_BOX: int
STC_INDIC_ROUNDBOX: int
STC_INDIC_STRAIGHTBOX: int
STC_INDIC_DASH: int
STC_INDIC_DOTS: int
STC_INDIC_SQUIGGLELOW: int
STC_INDIC_DOTBOX: int
STC_INDIC_SQUIGGLEPIXMAP: int
STC_INDIC_COMPOSITIONTHICK: int
STC_INDIC_COMPOSITIONTHIN: int
STC_INDIC_FULLBOX: int
STC_INDIC_TEXTFORE: int
STC_INDIC_POINT: int
STC_INDIC_POINTCHARACTER: int
STC_INDIC_IME: int
STC_INDIC_IME_MAX: int
STC_INDIC_MAX: int
STC_INDIC_CONTAINER: int
STC_INDICVALUEBIT: int
STC_INDICVALUEMASK: int
STC_INDICFLAG_VALUEFORE: int
STC_IV_NONE: int
STC_IV_REAL: int
STC_IV_LOOKFORWARD: int
STC_IV_LOOKBOTH: int
STC_PRINT_NORMAL: int
STC_PRINT_INVERTLIGHT: int
STC_PRINT_BLACKONWHITE: int
STC_PRINT_COLOURONWHITE: int
STC_PRINT_COLOURONWHITEDEFAULTBG: int
STC_FIND_WHOLEWORD: int
STC_FIND_MATCHCASE: int
STC_FIND_WORDSTART: int
STC_FIND_REGEXP: int
STC_FIND_POSIX: int
STC_FOLDLEVELBASE: int
STC_FOLDLEVELWHITEFLAG: int
STC_FOLDLEVELHEADERFLAG: int
STC_FOLDLEVELNUMBERMASK: int
STC_FOLDDISPLAYTEXT_HIDDEN: int
STC_FOLDDISPLAYTEXT_STANDARD: int
STC_FOLDDISPLAYTEXT_BOXED: int
STC_FOLDACTION_CONTRACT: int
STC_FOLDACTION_EXPAND: int
STC_FOLDACTION_TOGGLE: int
STC_AUTOMATICFOLD_SHOW: int
STC_AUTOMATICFOLD_CLICK: int
STC_AUTOMATICFOLD_CHANGE: int
STC_FOLDFLAG_LINEBEFORE_EXPANDED: int
STC_FOLDFLAG_LINEBEFORE_CONTRACTED: int
STC_FOLDFLAG_LINEAFTER_EXPANDED: int
STC_FOLDFLAG_LINEAFTER_CONTRACTED: int
STC_FOLDFLAG_LEVELNUMBERS: int
STC_FOLDFLAG_LINESTATE: int
STC_TIME_FOREVER: int
STC_IDLESTYLING_NONE: int
STC_IDLESTYLING_TOVISIBLE: int
STC_IDLESTYLING_AFTERVISIBLE: int
STC_IDLESTYLING_ALL: int
STC_WRAP_NONE: int
STC_WRAP_WORD: int
STC_WRAP_CHAR: int
STC_WRAP_WHITESPACE: int
STC_WRAPVISUALFLAG_NONE: int
STC_WRAPVISUALFLAG_END: int
STC_WRAPVISUALFLAG_START: int
STC_WRAPVISUALFLAG_MARGIN: int
STC_WRAPVISUALFLAGLOC_DEFAULT: int
STC_WRAPVISUALFLAGLOC_END_BY_TEXT: int
STC_WRAPVISUALFLAGLOC_START_BY_TEXT: int
STC_WRAPINDENT_FIXED: int
STC_WRAPINDENT_SAME: int
STC_WRAPINDENT_INDENT: int
STC_CACHE_NONE: int
STC_CACHE_CARET: int
STC_CACHE_PAGE: int
STC_CACHE_DOCUMENT: int
STC_PHASES_ONE: int
STC_PHASES_TWO: int
STC_PHASES_MULTIPLE: int
STC_EFF_QUALITY_MASK: int
STC_EFF_QUALITY_DEFAULT: int
STC_EFF_QUALITY_NON_ANTIALIASED: int
STC_EFF_QUALITY_ANTIALIASED: int
STC_EFF_QUALITY_LCD_OPTIMIZED: int
STC_MULTIPASTE_ONCE: int
STC_MULTIPASTE_EACH: int
STC_EDGE_NONE: int
STC_EDGE_LINE: int
STC_EDGE_BACKGROUND: int
STC_EDGE_MULTILINE: int
STC_POPUP_NEVER: int
STC_POPUP_ALL: int
STC_POPUP_TEXT: int
STC_STATUS_OK: int
STC_STATUS_FAILURE: int
STC_STATUS_BADALLOC: int
STC_STATUS_WARN_START: int
STC_STATUS_WARN_REGEX: int
STC_CURSORNORMAL: int
STC_CURSORARROW: int
STC_CURSORWAIT: int
STC_CURSORREVERSEARROW: int
STC_VISIBLE_SLOP: int
STC_VISIBLE_STRICT: int
STC_CARET_SLOP: int
STC_CARET_STRICT: int
STC_CARET_JUMPS: int
STC_CARET_EVEN: int
STC_SEL_STREAM: int
STC_SEL_RECTANGLE: int
STC_SEL_LINES: int
STC_SEL_THIN: int
STC_CASEINSENSITIVEBEHAVIOUR_RESPECTCASE: int
STC_CASEINSENSITIVEBEHAVIOUR_IGNORECASE: int
STC_MULTIAUTOC_ONCE: int
STC_MULTIAUTOC_EACH: int
STC_ORDER_PRESORTED: int
STC_ORDER_PERFORMSORT: int
STC_ORDER_CUSTOM: int
STC_CARETSTICKY_OFF: int
STC_CARETSTICKY_ON: int
STC_CARETSTICKY_WHITESPACE: int
STC_ALPHA_TRANSPARENT: int
STC_ALPHA_OPAQUE: int
STC_ALPHA_NOALPHA: int
STC_CARETSTYLE_INVISIBLE: int
STC_CARETSTYLE_LINE: int
STC_CARETSTYLE_BLOCK: int
STC_MARGINOPTION_NONE: int
STC_MARGINOPTION_SUBLINESELECT: int
STC_ANNOTATION_HIDDEN: int
STC_ANNOTATION_STANDARD: int
STC_ANNOTATION_BOXED: int
STC_ANNOTATION_INDENTED: int
STC_UNDO_MAY_COALESCE: int
STC_VS_NONE: int
STC_VS_RECTANGULARSELECTION: int
STC_VS_USERACCESSIBLE: int
STC_VS_NOWRAPLINESTART: int
STC_TECHNOLOGY_DEFAULT: int
STC_TECHNOLOGY_DIRECTWRITE: int
STC_LINE_END_TYPE_DEFAULT: int
STC_LINE_END_TYPE_UNICODE: int
STC_KEYWORDSET_MAX: int
STC_TYPE_BOOLEAN: int
STC_TYPE_INTEGER: int
STC_TYPE_STRING: int
STC_MOD_INSERTTEXT: int
STC_MOD_DELETETEXT: int
STC_MOD_CHANGESTYLE: int
STC_MOD_CHANGEFOLD: int
STC_PERFORMED_USER: int
STC_PERFORMED_UNDO: int
STC_PERFORMED_REDO: int
STC_MULTISTEPUNDOREDO: int
STC_LASTSTEPINUNDOREDO: int
STC_MOD_CHANGEMARKER: int
STC_MOD_BEFOREINSERT: int
STC_MOD_BEFOREDELETE: int
STC_MULTILINEUNDOREDO: int
STC_STARTACTION: int
STC_MOD_CHANGEINDICATOR: int
STC_MOD_CHANGELINESTATE: int
STC_MOD_CHANGEMARGIN: int
STC_MOD_CHANGEANNOTATION: int
STC_MOD_CONTAINER: int
STC_MOD_LEXERSTATE: int
STC_MOD_INSERTCHECK: int
STC_MOD_CHANGETABSTOPS: int
STC_MODEVENTMASKALL: int
STC_UPDATE_CONTENT: int
STC_UPDATE_SELECTION: int
STC_UPDATE_V_SCROLL: int
STC_UPDATE_H_SCROLL: int
STC_KEY_DOWN: int
STC_KEY_UP: int
STC_KEY_LEFT: int
STC_KEY_RIGHT: int
STC_KEY_HOME: int
STC_KEY_END: int
STC_KEY_PRIOR: int
STC_KEY_NEXT: int
STC_KEY_DELETE: int
STC_KEY_INSERT: int
STC_KEY_ESCAPE: int
STC_KEY_BACK: int
STC_KEY_TAB: int
STC_KEY_RETURN: int
STC_KEY_ADD: int
STC_KEY_SUBTRACT: int
STC_KEY_DIVIDE: int
STC_KEY_WIN: int
STC_KEY_RWIN: int
STC_KEY_MENU: int
STC_KEYMOD_NORM: int
STC_KEYMOD_SHIFT: int
STC_KEYMOD_CTRL: int
STC_KEYMOD_ALT: int
STC_KEYMOD_SUPER: int
STC_KEYMOD_META: int
STC_AC_FILLUP: int
STC_AC_DOUBLECLICK: int
STC_AC_TAB: int
STC_AC_NEWLINE: int
STC_AC_COMMAND: int
STC_LEX_CONTAINER: int
STC_LEX_NULL: int
STC_LEX_PYTHON: int
STC_LEX_CPP: int
STC_LEX_HTML: int
STC_LEX_XML: int
STC_LEX_PERL: int
STC_LEX_SQL: int
STC_LEX_VB: int
STC_LEX_PROPERTIES: int
STC_LEX_ERRORLIST: int
STC_LEX_MAKEFILE: int
STC_LEX_BATCH: int
STC_LEX_XCODE: int
STC_LEX_LATEX: int
STC_LEX_LUA: int
STC_LEX_DIFF: int
STC_LEX_CONF: int
STC_LEX_PASCAL: int
STC_LEX_AVE: int
STC_LEX_ADA: int
STC_LEX_LISP: int
STC_LEX_RUBY: int
STC_LEX_EIFFEL: int
STC_LEX_EIFFELKW: int
STC_LEX_TCL: int
STC_LEX_NNCRONTAB: int
STC_LEX_BULLANT: int
STC_LEX_VBSCRIPT: int
STC_LEX_BAAN: int
STC_LEX_MATLAB: int
STC_LEX_SCRIPTOL: int
STC_LEX_ASM: int
STC_LEX_CPPNOCASE: int
STC_LEX_FORTRAN: int
STC_LEX_F77: int
STC_LEX_CSS: int
STC_LEX_POV: int
STC_LEX_LOUT: int
STC_LEX_ESCRIPT: int
STC_LEX_PS: int
STC_LEX_NSIS: int
STC_LEX_MMIXAL: int
STC_LEX_CLW: int
STC_LEX_CLWNOCASE: int
STC_LEX_LOT: int
STC_LEX_YAML: int
STC_LEX_TEX: int
STC_LEX_METAPOST: int
STC_LEX_POWERBASIC: int
STC_LEX_FORTH: int
STC_LEX_ERLANG: int
STC_LEX_OCTAVE: int
STC_LEX_MSSQL: int
STC_LEX_VERILOG: int
STC_LEX_KIX: int
STC_LEX_GUI4CLI: int
STC_LEX_SPECMAN: int
STC_LEX_AU3: int
STC_LEX_APDL: int
STC_LEX_BASH: int
STC_LEX_ASN1: int
STC_LEX_VHDL: int
STC_LEX_CAML: int
STC_LEX_BLITZBASIC: int
STC_LEX_PUREBASIC: int
STC_LEX_HASKELL: int
STC_LEX_PHPSCRIPT: int
STC_LEX_TADS3: int
STC_LEX_REBOL: int
STC_LEX_SMALLTALK: int
STC_LEX_FLAGSHIP: int
STC_LEX_CSOUND: int
STC_LEX_FREEBASIC: int
STC_LEX_INNOSETUP: int
STC_LEX_OPAL: int
STC_LEX_SPICE: int
STC_LEX_D: int
STC_LEX_CMAKE: int
STC_LEX_GAP: int
STC_LEX_PLM: int
STC_LEX_PROGRESS: int
STC_LEX_ABAQUS: int
STC_LEX_ASYMPTOTE: int
STC_LEX_R: int
STC_LEX_MAGIK: int
STC_LEX_POWERSHELL: int
STC_LEX_MYSQL: int
STC_LEX_PO: int
STC_LEX_TAL: int
STC_LEX_COBOL: int
STC_LEX_TACL: int
STC_LEX_SORCUS: int
STC_LEX_POWERPRO: int
STC_LEX_NIMROD: int
STC_LEX_SML: int
STC_LEX_MARKDOWN: int
STC_LEX_TXT2TAGS: int
STC_LEX_A68K: int
STC_LEX_MODULA: int
STC_LEX_COFFEESCRIPT: int
STC_LEX_TCMD: int
STC_LEX_AVS: int
STC_LEX_ECL: int
STC_LEX_OSCRIPT: int
STC_LEX_VISUALPROLOG: int
STC_LEX_LITERATEHASKELL: int
STC_LEX_STTXT: int
STC_LEX_KVIRC: int
STC_LEX_RUST: int
STC_LEX_DMAP: int
STC_LEX_AS: int
STC_LEX_DMIS: int
STC_LEX_REGISTRY: int
STC_LEX_BIBTEX: int
STC_LEX_SREC: int
STC_LEX_IHEX: int
STC_LEX_TEHEX: int
STC_LEX_JSON: int
STC_LEX_EDIFACT: int
STC_LEX_AUTOMATIC: int
STC_P_DEFAULT: int
STC_P_COMMENTLINE: int
STC_P_NUMBER: int
STC_P_STRING: int
STC_P_CHARACTER: int
STC_P_WORD: int
STC_P_TRIPLE: int
STC_P_TRIPLEDOUBLE: int
STC_P_CLASSNAME: int
STC_P_DEFNAME: int
STC_P_OPERATOR: int
STC_P_IDENTIFIER: int
STC_P_COMMENTBLOCK: int
STC_P_STRINGEOL: int
STC_P_WORD2: int
STC_P_DECORATOR: int
STC_C_DEFAULT: int
STC_C_COMMENT: int
STC_C_COMMENTLINE: int
STC_C_COMMENTDOC: int
STC_C_NUMBER: int
STC_C_WORD: int
STC_C_STRING: int
STC_C_CHARACTER: int
STC_C_UUID: int
STC_C_PREPROCESSOR: int
STC_C_OPERATOR: int
STC_C_IDENTIFIER: int
STC_C_STRINGEOL: int
STC_C_VERBATIM: int
STC_C_REGEX: int
STC_C_COMMENTLINEDOC: int
STC_C_WORD2: int
STC_C_COMMENTDOCKEYWORD: int
STC_C_COMMENTDOCKEYWORDERROR: int
STC_C_GLOBALCLASS: int
STC_C_STRINGRAW: int
STC_C_TRIPLEVERBATIM: int
STC_C_HASHQUOTEDSTRING: int
STC_C_PREPROCESSORCOMMENT: int
STC_C_PREPROCESSORCOMMENTDOC: int
STC_C_USERLITERAL: int
STC_C_TASKMARKER: int
STC_C_ESCAPESEQUENCE: int
STC_D_DEFAULT: int
STC_D_COMMENT: int
STC_D_COMMENTLINE: int
STC_D_COMMENTDOC: int
STC_D_COMMENTNESTED: int
STC_D_NUMBER: int
STC_D_WORD: int
STC_D_WORD2: int
STC_D_WORD3: int
STC_D_TYPEDEF: int
STC_D_STRING: int
STC_D_STRINGEOL: int
STC_D_CHARACTER: int
STC_D_OPERATOR: int
STC_D_IDENTIFIER: int
STC_D_COMMENTLINEDOC: int
STC_D_COMMENTDOCKEYWORD: int
STC_D_COMMENTDOCKEYWORDERROR: int
STC_D_STRINGB: int
STC_D_STRINGR: int
STC_D_WORD5: int
STC_D_WORD6: int
STC_D_WORD7: int
STC_TCL_DEFAULT: int
STC_TCL_COMMENT: int
STC_TCL_COMMENTLINE: int
STC_TCL_NUMBER: int
STC_TCL_WORD_IN_QUOTE: int
STC_TCL_IN_QUOTE: int
STC_TCL_OPERATOR: int
STC_TCL_IDENTIFIER: int
STC_TCL_SUBSTITUTION: int
STC_TCL_SUB_BRACE: int
STC_TCL_MODIFIER: int
STC_TCL_EXPAND: int
STC_TCL_WORD: int
STC_TCL_WORD2: int
STC_TCL_WORD3: int
STC_TCL_WORD4: int
STC_TCL_WORD5: int
STC_TCL_WORD6: int
STC_TCL_WORD7: int
STC_TCL_WORD8: int
STC_TCL_COMMENT_BOX: int
STC_TCL_BLOCK_COMMENT: int
STC_H_DEFAULT: int
STC_H_TAG: int
STC_H_TAGUNKNOWN: int
STC_H_ATTRIBUTE: int
STC_H_ATTRIBUTEUNKNOWN: int
STC_H_NUMBER: int
STC_H_DOUBLESTRING: int
STC_H_SINGLESTRING: int
STC_H_OTHER: int
STC_H_COMMENT: int
STC_H_ENTITY: int
STC_H_TAGEND: int
STC_H_XMLSTART: int
STC_H_XMLEND: int
STC_H_SCRIPT: int
STC_H_ASP: int
STC_H_ASPAT: int
STC_H_CDATA: int
STC_H_QUESTION: int
STC_H_VALUE: int
STC_H_XCCOMMENT: int
STC_H_SGML_DEFAULT: int
STC_H_SGML_COMMAND: int
STC_H_SGML_1ST_PARAM: int
STC_H_SGML_DOUBLESTRING: int
STC_H_SGML_SIMPLESTRING: int
STC_H_SGML_ERROR: int
STC_H_SGML_SPECIAL: int
STC_H_SGML_ENTITY: int
STC_H_SGML_COMMENT: int
STC_H_SGML_1ST_PARAM_COMMENT: int
STC_H_SGML_BLOCK_DEFAULT: int
STC_HJ_START: int
STC_HJ_DEFAULT: int
STC_HJ_COMMENT: int
STC_HJ_COMMENTLINE: int
STC_HJ_COMMENTDOC: int
STC_HJ_NUMBER: int
STC_HJ_WORD: int
STC_HJ_KEYWORD: int
STC_HJ_DOUBLESTRING: int
STC_HJ_SINGLESTRING: int
STC_HJ_SYMBOLS: int
STC_HJ_STRINGEOL: int
STC_HJ_REGEX: int
STC_HJA_START: int
STC_HJA_DEFAULT: int
STC_HJA_COMMENT: int
STC_HJA_COMMENTLINE: int
STC_HJA_COMMENTDOC: int
STC_HJA_NUMBER: int
STC_HJA_WORD: int
STC_HJA_KEYWORD: int
STC_HJA_DOUBLESTRING: int
STC_HJA_SINGLESTRING: int
STC_HJA_SYMBOLS: int
STC_HJA_STRINGEOL: int
STC_HJA_REGEX: int
STC_HB_START: int
STC_HB_DEFAULT: int
STC_HB_COMMENTLINE: int
STC_HB_NUMBER: int
STC_HB_WORD: int
STC_HB_STRING: int
STC_HB_IDENTIFIER: int
STC_HB_STRINGEOL: int
STC_HBA_START: int
STC_HBA_DEFAULT: int
STC_HBA_COMMENTLINE: int
STC_HBA_NUMBER: int
STC_HBA_WORD: int
STC_HBA_STRING: int
STC_HBA_IDENTIFIER: int
STC_HBA_STRINGEOL: int
STC_HP_START: int
STC_HP_DEFAULT: int
STC_HP_COMMENTLINE: int
STC_HP_NUMBER: int
STC_HP_STRING: int
STC_HP_CHARACTER: int
STC_HP_WORD: int
STC_HP_TRIPLE: int
STC_HP_TRIPLEDOUBLE: int
STC_HP_CLASSNAME: int
STC_HP_DEFNAME: int
STC_HP_OPERATOR: int
STC_HP_IDENTIFIER: int
STC_HPHP_COMPLEX_VARIABLE: int
STC_HPA_START: int
STC_HPA_DEFAULT: int
STC_HPA_COMMENTLINE: int
STC_HPA_NUMBER: int
STC_HPA_STRING: int
STC_HPA_CHARACTER: int
STC_HPA_WORD: int
STC_HPA_TRIPLE: int
STC_HPA_TRIPLEDOUBLE: int
STC_HPA_CLASSNAME: int
STC_HPA_DEFNAME: int
STC_HPA_OPERATOR: int
STC_HPA_IDENTIFIER: int
STC_HPHP_DEFAULT: int
STC_HPHP_HSTRING: int
STC_HPHP_SIMPLESTRING: int
STC_HPHP_WORD: int
STC_HPHP_NUMBER: int
STC_HPHP_VARIABLE: int
STC_HPHP_COMMENT: int
STC_HPHP_COMMENTLINE: int
STC_HPHP_HSTRING_VARIABLE: int
STC_HPHP_OPERATOR: int
STC_PL_DEFAULT: int
STC_PL_ERROR: int
STC_PL_COMMENTLINE: int
STC_PL_POD: int
STC_PL_NUMBER: int
STC_PL_WORD: int
STC_PL_STRING: int
STC_PL_CHARACTER: int
STC_PL_PUNCTUATION: int
STC_PL_PREPROCESSOR: int
STC_PL_OPERATOR: int
STC_PL_IDENTIFIER: int
STC_PL_SCALAR: int
STC_PL_ARRAY: int
STC_PL_HASH: int
STC_PL_SYMBOLTABLE: int
STC_PL_VARIABLE_INDEXER: int
STC_PL_REGEX: int
STC_PL_REGSUBST: int
STC_PL_LONGQUOTE: int
STC_PL_BACKTICKS: int
STC_PL_DATASECTION: int
STC_PL_HERE_DELIM: int
STC_PL_HERE_Q: int
STC_PL_HERE_QQ: int
STC_PL_HERE_QX: int
STC_PL_STRING_Q: int
STC_PL_STRING_QQ: int
STC_PL_STRING_QX: int
STC_PL_STRING_QR: int
STC_PL_STRING_QW: int
STC_PL_POD_VERB: int
STC_PL_SUB_PROTOTYPE: int
STC_PL_FORMAT_IDENT: int
STC_PL_FORMAT: int
STC_PL_STRING_VAR: int
STC_PL_XLAT: int
STC_PL_REGEX_VAR: int
STC_PL_REGSUBST_VAR: int
STC_PL_BACKTICKS_VAR: int
STC_PL_HERE_QQ_VAR: int
STC_PL_HERE_QX_VAR: int
STC_PL_STRING_QQ_VAR: int
STC_PL_STRING_QX_VAR: int
STC_PL_STRING_QR_VAR: int
STC_RB_DEFAULT: int
STC_RB_ERROR: int
STC_RB_COMMENTLINE: int
STC_RB_POD: int
STC_RB_NUMBER: int
STC_RB_WORD: int
STC_RB_STRING: int
STC_RB_CHARACTER: int
STC_RB_CLASSNAME: int
STC_RB_DEFNAME: int
STC_RB_OPERATOR: int
STC_RB_IDENTIFIER: int
STC_RB_REGEX: int
STC_RB_GLOBAL: int
STC_RB_SYMBOL: int
STC_RB_MODULE_NAME: int
STC_RB_INSTANCE_VAR: int
STC_RB_CLASS_VAR: int
STC_RB_BACKTICKS: int
STC_RB_DATASECTION: int
STC_RB_HERE_DELIM: int
STC_RB_HERE_Q: int
STC_RB_HERE_QQ: int
STC_RB_HERE_QX: int
STC_RB_STRING_Q: int
STC_RB_STRING_QQ: int
STC_RB_STRING_QX: int
STC_RB_STRING_QR: int
STC_RB_STRING_QW: int
STC_RB_WORD_DEMOTED: int
STC_RB_STDIN: int
STC_RB_STDOUT: int
STC_RB_STDERR: int
STC_RB_UPPER_BOUND: int
STC_B_DEFAULT: int
STC_B_COMMENT: int
STC_B_NUMBER: int
STC_B_KEYWORD: int
STC_B_STRING: int
STC_B_PREPROCESSOR: int
STC_B_OPERATOR: int
STC_B_IDENTIFIER: int
STC_B_DATE: int
STC_B_STRINGEOL: int
STC_B_KEYWORD2: int
STC_B_KEYWORD3: int
STC_B_KEYWORD4: int
STC_B_CONSTANT: int
STC_B_ASM: int
STC_B_LABEL: int
STC_B_ERROR: int
STC_B_HEXNUMBER: int
STC_B_BINNUMBER: int
STC_B_COMMENTBLOCK: int
STC_B_DOCLINE: int
STC_B_DOCBLOCK: int
STC_B_DOCKEYWORD: int
STC_PROPS_DEFAULT: int
STC_PROPS_COMMENT: int
STC_PROPS_SECTION: int
STC_PROPS_ASSIGNMENT: int
STC_PROPS_DEFVAL: int
STC_PROPS_KEY: int
STC_L_DEFAULT: int
STC_L_COMMAND: int
STC_L_TAG: int
STC_L_MATH: int
STC_L_COMMENT: int
STC_L_TAG2: int
STC_L_MATH2: int
STC_L_COMMENT2: int
STC_L_VERBATIM: int
STC_L_SHORTCMD: int
STC_L_SPECIAL: int
STC_L_CMDOPT: int
STC_L_ERROR: int
STC_LUA_DEFAULT: int
STC_LUA_COMMENT: int
STC_LUA_COMMENTLINE: int
STC_LUA_COMMENTDOC: int
STC_LUA_NUMBER: int
STC_LUA_WORD: int
STC_LUA_STRING: int
STC_LUA_CHARACTER: int
STC_LUA_LITERALSTRING: int
STC_LUA_PREPROCESSOR: int
STC_LUA_OPERATOR: int
STC_LUA_IDENTIFIER: int
STC_LUA_STRINGEOL: int
STC_LUA_WORD2: int
STC_LUA_WORD3: int
STC_LUA_WORD4: int
STC_LUA_WORD5: int
STC_LUA_WORD6: int
STC_LUA_WORD7: int
STC_LUA_WORD8: int
STC_LUA_LABEL: int
STC_ERR_DEFAULT: int
STC_ERR_PYTHON: int
STC_ERR_GCC: int
STC_ERR_MS: int
STC_ERR_CMD: int
STC_ERR_BORLAND: int
STC_ERR_PERL: int
STC_ERR_NET: int
STC_ERR_LUA: int
STC_ERR_CTAG: int
STC_ERR_DIFF_CHANGED: int
STC_ERR_DIFF_ADDITION: int
STC_ERR_DIFF_DELETION: int
STC_ERR_DIFF_MESSAGE: int
STC_ERR_PHP: int
STC_ERR_ELF: int
STC_ERR_IFC: int
STC_ERR_IFORT: int
STC_ERR_ABSF: int
STC_ERR_TIDY: int
STC_ERR_JAVA_STACK: int
STC_ERR_VALUE: int
STC_ERR_GCC_INCLUDED_FROM: int
STC_ERR_ESCSEQ: int
STC_ERR_ESCSEQ_UNKNOWN: int
STC_ERR_ES_BLACK: int
STC_ERR_ES_RED: int
STC_ERR_ES_GREEN: int
STC_ERR_ES_BROWN: int
STC_ERR_ES_BLUE: int
STC_ERR_ES_MAGENTA: int
STC_ERR_ES_CYAN: int
STC_ERR_ES_GRAY: int
STC_ERR_ES_DARK_GRAY: int
STC_ERR_ES_BRIGHT_RED: int
STC_ERR_ES_BRIGHT_GREEN: int
STC_ERR_ES_YELLOW: int
STC_ERR_ES_BRIGHT_BLUE: int
STC_ERR_ES_BRIGHT_MAGENTA: int
STC_ERR_ES_BRIGHT_CYAN: int
STC_ERR_ES_WHITE: int
STC_BAT_DEFAULT: int
STC_BAT_COMMENT: int
STC_BAT_WORD: int
STC_BAT_LABEL: int
STC_BAT_HIDE: int
STC_BAT_COMMAND: int
STC_BAT_IDENTIFIER: int
STC_BAT_OPERATOR: int
STC_TCMD_DEFAULT: int
STC_TCMD_COMMENT: int
STC_TCMD_WORD: int
STC_TCMD_LABEL: int
STC_TCMD_HIDE: int
STC_TCMD_COMMAND: int
STC_TCMD_IDENTIFIER: int
STC_TCMD_OPERATOR: int
STC_TCMD_ENVIRONMENT: int
STC_TCMD_EXPANSION: int
STC_TCMD_CLABEL: int
STC_MAKE_DEFAULT: int
STC_MAKE_COMMENT: int
STC_MAKE_PREPROCESSOR: int
STC_MAKE_IDENTIFIER: int
STC_MAKE_OPERATOR: int
STC_MAKE_TARGET: int
STC_MAKE_IDEOL: int
STC_DIFF_DEFAULT: int
STC_DIFF_COMMENT: int
STC_DIFF_COMMAND: int
STC_DIFF_HEADER: int
STC_DIFF_POSITION: int
STC_DIFF_DELETED: int
STC_DIFF_ADDED: int
STC_DIFF_CHANGED: int
STC_CONF_DEFAULT: int
STC_CONF_COMMENT: int
STC_CONF_NUMBER: int
STC_CONF_IDENTIFIER: int
STC_CONF_EXTENSION: int
STC_CONF_PARAMETER: int
STC_CONF_STRING: int
STC_CONF_OPERATOR: int
STC_CONF_IP: int
STC_CONF_DIRECTIVE: int
STC_AVE_DEFAULT: int
STC_AVE_COMMENT: int
STC_AVE_NUMBER: int
STC_AVE_WORD: int
STC_AVE_STRING: int
STC_AVE_ENUM: int
STC_AVE_STRINGEOL: int
STC_AVE_IDENTIFIER: int
STC_AVE_OPERATOR: int
STC_AVE_WORD1: int
STC_AVE_WORD2: int
STC_AVE_WORD3: int
STC_AVE_WORD4: int
STC_AVE_WORD5: int
STC_AVE_WORD6: int
STC_ADA_DEFAULT: int
STC_ADA_WORD: int
STC_ADA_IDENTIFIER: int
STC_ADA_NUMBER: int
STC_ADA_DELIMITER: int
STC_ADA_CHARACTER: int
STC_ADA_CHARACTEREOL: int
STC_ADA_STRING: int
STC_ADA_STRINGEOL: int
STC_ADA_LABEL: int
STC_ADA_COMMENTLINE: int
STC_ADA_ILLEGAL: int
STC_BAAN_DEFAULT: int
STC_BAAN_COMMENT: int
STC_BAAN_COMMENTDOC: int
STC_BAAN_NUMBER: int
STC_BAAN_WORD: int
STC_BAAN_STRING: int
STC_BAAN_PREPROCESSOR: int
STC_BAAN_OPERATOR: int
STC_BAAN_IDENTIFIER: int
STC_BAAN_STRINGEOL: int
STC_BAAN_WORD2: int
STC_BAAN_WORD3: int
STC_BAAN_WORD4: int
STC_BAAN_WORD5: int
STC_BAAN_WORD6: int
STC_BAAN_WORD7: int
STC_BAAN_WORD8: int
STC_BAAN_WORD9: int
STC_BAAN_TABLEDEF: int
STC_BAAN_TABLESQL: int
STC_BAAN_FUNCTION: int
STC_BAAN_DOMDEF: int
STC_BAAN_FUNCDEF: int
STC_BAAN_OBJECTDEF: int
STC_BAAN_DEFINEDEF: int
STC_LISP_DEFAULT: int
STC_LISP_COMMENT: int
STC_LISP_NUMBER: int
STC_LISP_KEYWORD: int
STC_LISP_KEYWORD_KW: int
STC_LISP_SYMBOL: int
STC_LISP_STRING: int
STC_LISP_STRINGEOL: int
STC_LISP_IDENTIFIER: int
STC_LISP_OPERATOR: int
STC_LISP_SPECIAL: int
STC_LISP_MULTI_COMMENT: int
STC_EIFFEL_DEFAULT: int
STC_EIFFEL_COMMENTLINE: int
STC_EIFFEL_NUMBER: int
STC_EIFFEL_WORD: int
STC_EIFFEL_STRING: int
STC_EIFFEL_CHARACTER: int
STC_EIFFEL_OPERATOR: int
STC_EIFFEL_IDENTIFIER: int
STC_EIFFEL_STRINGEOL: int
STC_NNCRONTAB_DEFAULT: int
STC_NNCRONTAB_COMMENT: int
STC_NNCRONTAB_TASK: int
STC_NNCRONTAB_SECTION: int
STC_NNCRONTAB_KEYWORD: int
STC_NNCRONTAB_MODIFIER: int
STC_NNCRONTAB_ASTERISK: int
STC_NNCRONTAB_NUMBER: int
STC_NNCRONTAB_STRING: int
STC_NNCRONTAB_ENVIRONMENT: int
STC_NNCRONTAB_IDENTIFIER: int
STC_FORTH_DEFAULT: int
STC_FORTH_COMMENT: int
STC_FORTH_COMMENT_ML: int
STC_FORTH_IDENTIFIER: int
STC_FORTH_CONTROL: int
STC_FORTH_KEYWORD: int
STC_FORTH_DEFWORD: int
STC_FORTH_PREWORD1: int
STC_FORTH_PREWORD2: int
STC_FORTH_NUMBER: int
STC_FORTH_STRING: int
STC_FORTH_LOCALE: int
STC_MATLAB_DEFAULT: int
STC_MATLAB_COMMENT: int
STC_MATLAB_COMMAND: int
STC_MATLAB_NUMBER: int
STC_MATLAB_KEYWORD: int
STC_MATLAB_STRING: int
STC_MATLAB_OPERATOR: int
STC_MATLAB_IDENTIFIER: int
STC_MATLAB_DOUBLEQUOTESTRING: int
STC_SCRIPTOL_DEFAULT: int
STC_SCRIPTOL_WHITE: int
STC_SCRIPTOL_COMMENTLINE: int
STC_SCRIPTOL_PERSISTENT: int
STC_SCRIPTOL_CSTYLE: int
STC_SCRIPTOL_COMMENTBLOCK: int
STC_SCRIPTOL_NUMBER: int
STC_SCRIPTOL_STRING: int
STC_SCRIPTOL_CHARACTER: int
STC_SCRIPTOL_STRINGEOL: int
STC_SCRIPTOL_KEYWORD: int
STC_SCRIPTOL_OPERATOR: int
STC_SCRIPTOL_IDENTIFIER: int
STC_SCRIPTOL_TRIPLE: int
STC_SCRIPTOL_CLASSNAME: int
STC_SCRIPTOL_PREPROCESSOR: int
STC_ASM_DEFAULT: int
STC_ASM_COMMENT: int
STC_ASM_NUMBER: int
STC_ASM_STRING: int
STC_ASM_OPERATOR: int
STC_ASM_IDENTIFIER: int
STC_ASM_CPUINSTRUCTION: int
STC_ASM_MATHINSTRUCTION: int
STC_ASM_REGISTER: int
STC_ASM_DIRECTIVE: int
STC_ASM_DIRECTIVEOPERAND: int
STC_ASM_COMMENTBLOCK: int
STC_ASM_CHARACTER: int
STC_ASM_STRINGEOL: int
STC_ASM_EXTINSTRUCTION: int
STC_ASM_COMMENTDIRECTIVE: int
STC_F_DEFAULT: int
STC_F_COMMENT: int
STC_F_NUMBER: int
STC_F_STRING1: int
STC_F_STRING2: int
STC_F_STRINGEOL: int
STC_F_OPERATOR: int
STC_F_IDENTIFIER: int
STC_F_WORD: int
STC_F_WORD2: int
STC_F_WORD3: int
STC_F_PREPROCESSOR: int
STC_F_OPERATOR2: int
STC_F_LABEL: int
STC_F_CONTINUATION: int
STC_CSS_DEFAULT: int
STC_CSS_TAG: int
STC_CSS_CLASS: int
STC_CSS_PSEUDOCLASS: int
STC_CSS_UNKNOWN_PSEUDOCLASS: int
STC_CSS_OPERATOR: int
STC_CSS_IDENTIFIER: int
STC_CSS_UNKNOWN_IDENTIFIER: int
STC_CSS_VALUE: int
STC_CSS_COMMENT: int
STC_CSS_ID: int
STC_CSS_IMPORTANT: int
STC_CSS_DIRECTIVE: int
STC_CSS_DOUBLESTRING: int
STC_CSS_SINGLESTRING: int
STC_CSS_IDENTIFIER2: int
STC_CSS_ATTRIBUTE: int
STC_CSS_IDENTIFIER3: int
STC_CSS_PSEUDOELEMENT: int
STC_CSS_EXTENDED_IDENTIFIER: int
STC_CSS_EXTENDED_PSEUDOCLASS: int
STC_CSS_EXTENDED_PSEUDOELEMENT: int
STC_CSS_MEDIA: int
STC_CSS_VARIABLE: int
STC_POV_DEFAULT: int
STC_POV_COMMENT: int
STC_POV_COMMENTLINE: int
STC_POV_NUMBER: int
STC_POV_OPERATOR: int
STC_POV_IDENTIFIER: int
STC_POV_STRING: int
STC_POV_STRINGEOL: int
STC_POV_DIRECTIVE: int
STC_POV_BADDIRECTIVE: int
STC_POV_WORD2: int
STC_POV_WORD3: int
STC_POV_WORD4: int
STC_POV_WORD5: int
STC_POV_WORD6: int
STC_POV_WORD7: int
STC_POV_WORD8: int
STC_LOUT_DEFAULT: int
STC_LOUT_COMMENT: int
STC_LOUT_NUMBER: int
STC_LOUT_WORD: int
STC_LOUT_WORD2: int
STC_LOUT_WORD3: int
STC_LOUT_WORD4: int
STC_LOUT_STRING: int
STC_LOUT_OPERATOR: int
STC_LOUT_IDENTIFIER: int
STC_LOUT_STRINGEOL: int
STC_ESCRIPT_DEFAULT: int
STC_ESCRIPT_COMMENT: int
STC_ESCRIPT_COMMENTLINE: int
STC_ESCRIPT_COMMENTDOC: int
STC_ESCRIPT_NUMBER: int
STC_ESCRIPT_WORD: int
STC_ESCRIPT_STRING: int
STC_ESCRIPT_OPERATOR: int
STC_ESCRIPT_IDENTIFIER: int
STC_ESCRIPT_BRACE: int
STC_ESCRIPT_WORD2: int
STC_ESCRIPT_WORD3: int
STC_PS_DEFAULT: int
STC_PS_COMMENT: int
STC_PS_DSC_COMMENT: int
STC_PS_DSC_VALUE: int
STC_PS_NUMBER: int
STC_PS_NAME: int
STC_PS_KEYWORD: int
STC_PS_LITERAL: int
STC_PS_IMMEVAL: int
STC_PS_PAREN_ARRAY: int
STC_PS_PAREN_DICT: int
STC_PS_PAREN_PROC: int
STC_PS_TEXT: int
STC_PS_HEXSTRING: int
STC_PS_BASE85STRING: int
STC_PS_BADSTRINGCHAR: int
STC_NSIS_DEFAULT: int
STC_NSIS_COMMENT: int
STC_NSIS_STRINGDQ: int
STC_NSIS_STRINGLQ: int
STC_NSIS_STRINGRQ: int
STC_NSIS_FUNCTION: int
STC_NSIS_VARIABLE: int
STC_NSIS_LABEL: int
STC_NSIS_USERDEFINED: int
STC_NSIS_SECTIONDEF: int
STC_NSIS_SUBSECTIONDEF: int
STC_NSIS_IFDEFINEDEF: int
STC_NSIS_MACRODEF: int
STC_NSIS_STRINGVAR: int
STC_NSIS_NUMBER: int
STC_NSIS_SECTIONGROUP: int
STC_NSIS_PAGEEX: int
STC_NSIS_FUNCTIONDEF: int
STC_NSIS_COMMENTBOX: int
STC_MMIXAL_LEADWS: int
STC_MMIXAL_COMMENT: int
STC_MMIXAL_LABEL: int
STC_MMIXAL_OPCODE: int
STC_MMIXAL_OPCODE_PRE: int
STC_MMIXAL_OPCODE_VALID: int
STC_MMIXAL_OPCODE_UNKNOWN: int
STC_MMIXAL_OPCODE_POST: int
STC_MMIXAL_OPERANDS: int
STC_MMIXAL_NUMBER: int
STC_MMIXAL_REF: int
STC_MMIXAL_CHAR: int
STC_MMIXAL_STRING: int
STC_MMIXAL_REGISTER: int
STC_MMIXAL_HEX: int
STC_MMIXAL_OPERATOR: int
STC_MMIXAL_SYMBOL: int
STC_MMIXAL_INCLUDE: int
STC_CLW_DEFAULT: int
STC_CLW_LABEL: int
STC_CLW_COMMENT: int
STC_CLW_STRING: int
STC_CLW_USER_IDENTIFIER: int
STC_CLW_INTEGER_CONSTANT: int
STC_CLW_REAL_CONSTANT: int
STC_CLW_PICTURE_STRING: int
STC_CLW_KEYWORD: int
STC_CLW_COMPILER_DIRECTIVE: int
STC_CLW_RUNTIME_EXPRESSIONS: int
STC_CLW_BUILTIN_PROCEDURES_FUNCTION: int
STC_CLW_STRUCTURE_DATA_TYPE: int
STC_CLW_ATTRIBUTE: int
STC_CLW_STANDARD_EQUATE: int
STC_CLW_ERROR: int
STC_CLW_DEPRECATED: int
STC_LOT_DEFAULT: int
STC_LOT_HEADER: int
STC_LOT_BREAK: int
STC_LOT_SET: int
STC_LOT_PASS: int
STC_LOT_FAIL: int
STC_LOT_ABORT: int
STC_YAML_DEFAULT: int
STC_YAML_COMMENT: int
STC_YAML_IDENTIFIER: int
STC_YAML_KEYWORD: int
STC_YAML_NUMBER: int
STC_YAML_REFERENCE: int
STC_YAML_DOCUMENT: int
STC_YAML_TEXT: int
STC_YAML_ERROR: int
STC_YAML_OPERATOR: int
STC_TEX_DEFAULT: int
STC_TEX_SPECIAL: int
STC_TEX_GROUP: int
STC_TEX_SYMBOL: int
STC_TEX_COMMAND: int
STC_TEX_TEXT: int
STC_METAPOST_DEFAULT: int
STC_METAPOST_SPECIAL: int
STC_METAPOST_GROUP: int
STC_METAPOST_SYMBOL: int
STC_METAPOST_COMMAND: int
STC_METAPOST_TEXT: int
STC_METAPOST_EXTRA: int
STC_ERLANG_DEFAULT: int
STC_ERLANG_COMMENT: int
STC_ERLANG_VARIABLE: int
STC_ERLANG_NUMBER: int
STC_ERLANG_KEYWORD: int
STC_ERLANG_STRING: int
STC_ERLANG_OPERATOR: int
STC_ERLANG_ATOM: int
STC_ERLANG_FUNCTION_NAME: int
STC_ERLANG_CHARACTER: int
STC_ERLANG_MACRO: int
STC_ERLANG_RECORD: int
STC_ERLANG_PREPROC: int
STC_ERLANG_NODE_NAME: int
STC_ERLANG_COMMENT_FUNCTION: int
STC_ERLANG_COMMENT_MODULE: int
STC_ERLANG_COMMENT_DOC: int
STC_ERLANG_COMMENT_DOC_MACRO: int
STC_ERLANG_ATOM_QUOTED: int
STC_ERLANG_MACRO_QUOTED: int
STC_ERLANG_RECORD_QUOTED: int
STC_ERLANG_NODE_NAME_QUOTED: int
STC_ERLANG_BIFS: int
STC_ERLANG_MODULES: int
STC_ERLANG_MODULES_ATT: int
STC_ERLANG_UNKNOWN: int
STC_MSSQL_DEFAULT: int
STC_MSSQL_COMMENT: int
STC_MSSQL_LINE_COMMENT: int
STC_MSSQL_NUMBER: int
STC_MSSQL_STRING: int
STC_MSSQL_OPERATOR: int
STC_MSSQL_IDENTIFIER: int
STC_MSSQL_VARIABLE: int
STC_MSSQL_COLUMN_NAME: int
STC_MSSQL_STATEMENT: int
STC_MSSQL_DATATYPE: int
STC_MSSQL_SYSTABLE: int
STC_MSSQL_GLOBAL_VARIABLE: int
STC_MSSQL_FUNCTION: int
STC_MSSQL_STORED_PROCEDURE: int
STC_MSSQL_DEFAULT_PREF_DATATYPE: int
STC_MSSQL_COLUMN_NAME_2: int
STC_V_DEFAULT: int
STC_V_COMMENT: int
STC_V_COMMENTLINE: int
STC_V_COMMENTLINEBANG: int
STC_V_NUMBER: int
STC_V_WORD: int
STC_V_STRING: int
STC_V_WORD2: int
STC_V_WORD3: int
STC_V_PREPROCESSOR: int
STC_V_OPERATOR: int
STC_V_IDENTIFIER: int
STC_V_STRINGEOL: int
STC_V_USER: int
STC_V_COMMENT_WORD: int
STC_V_INPUT: int
STC_V_OUTPUT: int
STC_V_INOUT: int
STC_V_PORT_CONNECT: int
STC_KIX_DEFAULT: int
STC_KIX_COMMENT: int
STC_KIX_STRING1: int
STC_KIX_STRING2: int
STC_KIX_NUMBER: int
STC_KIX_VAR: int
STC_KIX_MACRO: int
STC_KIX_KEYWORD: int
STC_KIX_FUNCTIONS: int
STC_KIX_OPERATOR: int
STC_KIX_COMMENTSTREAM: int
STC_KIX_IDENTIFIER: int
STC_GC_DEFAULT: int
STC_GC_COMMENTLINE: int
STC_GC_COMMENTBLOCK: int
STC_GC_GLOBAL: int
STC_GC_EVENT: int
STC_GC_ATTRIBUTE: int
STC_GC_CONTROL: int
STC_GC_COMMAND: int
STC_GC_STRING: int
STC_GC_OPERATOR: int
STC_SN_DEFAULT: int
STC_SN_CODE: int
STC_SN_COMMENTLINE: int
STC_SN_COMMENTLINEBANG: int
STC_SN_NUMBER: int
STC_SN_WORD: int
STC_SN_STRING: int
STC_SN_WORD2: int
STC_SN_WORD3: int
STC_SN_PREPROCESSOR: int
STC_SN_OPERATOR: int
STC_SN_IDENTIFIER: int
STC_SN_STRINGEOL: int
STC_SN_REGEXTAG: int
STC_SN_SIGNAL: int
STC_SN_USER: int
STC_AU3_DEFAULT: int
STC_AU3_COMMENT: int
STC_AU3_COMMENTBLOCK: int
STC_AU3_NUMBER: int
STC_AU3_FUNCTION: int
STC_AU3_KEYWORD: int
STC_AU3_MACRO: int
STC_AU3_STRING: int
STC_AU3_OPERATOR: int
STC_AU3_VARIABLE: int
STC_AU3_SENT: int
STC_AU3_PREPROCESSOR: int
STC_AU3_SPECIAL: int
STC_AU3_EXPAND: int
STC_AU3_COMOBJ: int
STC_AU3_UDF: int
STC_APDL_DEFAULT: int
STC_APDL_COMMENT: int
STC_APDL_COMMENTBLOCK: int
STC_APDL_NUMBER: int
STC_APDL_STRING: int
STC_APDL_OPERATOR: int
STC_APDL_WORD: int
STC_APDL_PROCESSOR: int
STC_APDL_COMMAND: int
STC_APDL_SLASHCOMMAND: int
STC_APDL_STARCOMMAND: int
STC_APDL_ARGUMENT: int
STC_APDL_FUNCTION: int
STC_SH_DEFAULT: int
STC_SH_ERROR: int
STC_SH_COMMENTLINE: int
STC_SH_NUMBER: int
STC_SH_WORD: int
STC_SH_STRING: int
STC_SH_CHARACTER: int
STC_SH_OPERATOR: int
STC_SH_IDENTIFIER: int
STC_SH_SCALAR: int
STC_SH_PARAM: int
STC_SH_BACKTICKS: int
STC_SH_HERE_DELIM: int
STC_SH_HERE_Q: int
STC_ASN1_DEFAULT: int
STC_ASN1_COMMENT: int
STC_ASN1_IDENTIFIER: int
STC_ASN1_STRING: int
STC_ASN1_OID: int
STC_ASN1_SCALAR: int
STC_ASN1_KEYWORD: int
STC_ASN1_ATTRIBUTE: int
STC_ASN1_DESCRIPTOR: int
STC_ASN1_TYPE: int
STC_ASN1_OPERATOR: int
STC_VHDL_DEFAULT: int
STC_VHDL_COMMENT: int
STC_VHDL_COMMENTLINEBANG: int
STC_VHDL_NUMBER: int
STC_VHDL_STRING: int
STC_VHDL_OPERATOR: int
STC_VHDL_IDENTIFIER: int
STC_VHDL_STRINGEOL: int
STC_VHDL_KEYWORD: int
STC_VHDL_STDOPERATOR: int
STC_VHDL_ATTRIBUTE: int
STC_VHDL_STDFUNCTION: int
STC_VHDL_STDPACKAGE: int
STC_VHDL_STDTYPE: int
STC_VHDL_USERWORD: int
STC_VHDL_BLOCK_COMMENT: int
STC_CAML_DEFAULT: int
STC_CAML_IDENTIFIER: int
STC_CAML_TAGNAME: int
STC_CAML_KEYWORD: int
STC_CAML_KEYWORD2: int
STC_CAML_KEYWORD3: int
STC_CAML_LINENUM: int
STC_CAML_OPERATOR: int
STC_CAML_NUMBER: int
STC_CAML_CHAR: int
STC_CAML_WHITE: int
STC_CAML_STRING: int
STC_CAML_COMMENT: int
STC_CAML_COMMENT1: int
STC_CAML_COMMENT2: int
STC_CAML_COMMENT3: int
STC_HA_DEFAULT: int
STC_HA_IDENTIFIER: int
STC_HA_KEYWORD: int
STC_HA_NUMBER: int
STC_HA_STRING: int
STC_HA_CHARACTER: int
STC_HA_CLASS: int
STC_HA_MODULE: int
STC_HA_CAPITAL: int
STC_HA_DATA: int
STC_HA_IMPORT: int
STC_HA_OPERATOR: int
STC_HA_INSTANCE: int
STC_HA_COMMENTLINE: int
STC_HA_COMMENTBLOCK: int
STC_HA_COMMENTBLOCK2: int
STC_HA_COMMENTBLOCK3: int
STC_HA_PRAGMA: int
STC_HA_PREPROCESSOR: int
STC_HA_STRINGEOL: int
STC_HA_RESERVED_OPERATOR: int
STC_HA_LITERATE_COMMENT: int
STC_HA_LITERATE_CODEDELIM: int
STC_T3_DEFAULT: int
STC_T3_X_DEFAULT: int
STC_T3_PREPROCESSOR: int
STC_T3_BLOCK_COMMENT: int
STC_T3_LINE_COMMENT: int
STC_T3_OPERATOR: int
STC_T3_KEYWORD: int
STC_T3_NUMBER: int
STC_T3_IDENTIFIER: int
STC_T3_S_STRING: int
STC_T3_D_STRING: int
STC_T3_X_STRING: int
STC_T3_LIB_DIRECTIVE: int
STC_T3_MSG_PARAM: int
STC_T3_HTML_TAG: int
STC_T3_HTML_DEFAULT: int
STC_T3_HTML_STRING: int
STC_T3_USER1: int
STC_T3_USER2: int
STC_T3_USER3: int
STC_T3_BRACE: int
STC_REBOL_DEFAULT: int
STC_REBOL_COMMENTLINE: int
STC_REBOL_COMMENTBLOCK: int
STC_REBOL_PREFACE: int
STC_REBOL_OPERATOR: int
STC_REBOL_CHARACTER: int
STC_REBOL_QUOTEDSTRING: int
STC_REBOL_BRACEDSTRING: int
STC_REBOL_NUMBER: int
STC_REBOL_PAIR: int
STC_REBOL_TUPLE: int
STC_REBOL_BINARY: int
STC_REBOL_MONEY: int
STC_REBOL_ISSUE: int
STC_REBOL_TAG: int
STC_REBOL_FILE: int
STC_REBOL_EMAIL: int
STC_REBOL_URL: int
STC_REBOL_DATE: int
STC_REBOL_TIME: int
STC_REBOL_IDENTIFIER: int
STC_REBOL_WORD: int
STC_REBOL_WORD2: int
STC_REBOL_WORD3: int
STC_REBOL_WORD4: int
STC_REBOL_WORD5: int
STC_REBOL_WORD6: int
STC_REBOL_WORD7: int
STC_REBOL_WORD8: int
STC_SQL_DEFAULT: int
STC_SQL_COMMENT: int
STC_SQL_COMMENTLINE: int
STC_SQL_COMMENTDOC: int
STC_SQL_NUMBER: int
STC_SQL_WORD: int
STC_SQL_STRING: int
STC_SQL_CHARACTER: int
STC_SQL_SQLPLUS: int
STC_SQL_SQLPLUS_PROMPT: int
STC_SQL_OPERATOR: int
STC_SQL_IDENTIFIER: int
STC_SQL_SQLPLUS_COMMENT: int
STC_SQL_COMMENTLINEDOC: int
STC_SQL_WORD2: int
STC_SQL_COMMENTDOCKEYWORD: int
STC_SQL_COMMENTDOCKEYWORDERROR: int
STC_SQL_USER1: int
STC_SQL_USER2: int
STC_SQL_USER3: int
STC_SQL_USER4: int
STC_SQL_QUOTEDIDENTIFIER: int
STC_SQL_QOPERATOR: int
STC_ST_DEFAULT: int
STC_ST_STRING: int
STC_ST_NUMBER: int
STC_ST_COMMENT: int
STC_ST_SYMBOL: int
STC_ST_BINARY: int
STC_ST_BOOL: int
STC_ST_SELF: int
STC_ST_SUPER: int
STC_ST_NIL: int
STC_ST_GLOBAL: int
STC_ST_RETURN: int
STC_ST_SPECIAL: int
STC_ST_KWSEND: int
STC_ST_ASSIGN: int
STC_ST_CHARACTER: int
STC_ST_SPEC_SEL: int
STC_FS_DEFAULT: int
STC_FS_COMMENT: int
STC_FS_COMMENTLINE: int
STC_FS_COMMENTDOC: int
STC_FS_COMMENTLINEDOC: int
STC_FS_COMMENTDOCKEYWORD: int
STC_FS_COMMENTDOCKEYWORDERROR: int
STC_FS_KEYWORD: int
STC_FS_KEYWORD2: int
STC_FS_KEYWORD3: int
STC_FS_KEYWORD4: int
STC_FS_NUMBER: int
STC_FS_STRING: int
STC_FS_PREPROCESSOR: int
STC_FS_OPERATOR: int
STC_FS_IDENTIFIER: int
STC_FS_DATE: int
STC_FS_STRINGEOL: int
STC_FS_CONSTANT: int
STC_FS_WORDOPERATOR: int
STC_FS_DISABLEDCODE: int
STC_FS_DEFAULT_C: int
STC_FS_COMMENTDOC_C: int
STC_FS_COMMENTLINEDOC_C: int
STC_FS_KEYWORD_C: int
STC_FS_KEYWORD2_C: int
STC_FS_NUMBER_C: int
STC_FS_STRING_C: int
STC_FS_PREPROCESSOR_C: int
STC_FS_OPERATOR_C: int
STC_FS_IDENTIFIER_C: int
STC_FS_STRINGEOL_C: int
STC_CSOUND_DEFAULT: int
STC_CSOUND_COMMENT: int
STC_CSOUND_NUMBER: int
STC_CSOUND_OPERATOR: int
STC_CSOUND_INSTR: int
STC_CSOUND_IDENTIFIER: int
STC_CSOUND_OPCODE: int
STC_CSOUND_HEADERSTMT: int
STC_CSOUND_USERKEYWORD: int
STC_CSOUND_COMMENTBLOCK: int
STC_CSOUND_PARAM: int
STC_CSOUND_ARATE_VAR: int
STC_CSOUND_KRATE_VAR: int
STC_CSOUND_IRATE_VAR: int
STC_CSOUND_GLOBAL_VAR: int
STC_CSOUND_STRINGEOL: int
STC_INNO_DEFAULT: int
STC_INNO_COMMENT: int
STC_INNO_KEYWORD: int
STC_INNO_PARAMETER: int
STC_INNO_SECTION: int
STC_INNO_PREPROC: int
STC_INNO_INLINE_EXPANSION: int
STC_INNO_COMMENT_PASCAL: int
STC_INNO_KEYWORD_PASCAL: int
STC_INNO_KEYWORD_USER: int
STC_INNO_STRING_DOUBLE: int
STC_INNO_STRING_SINGLE: int
STC_INNO_IDENTIFIER: int
STC_OPAL_SPACE: int
STC_OPAL_COMMENT_BLOCK: int
STC_OPAL_COMMENT_LINE: int
STC_OPAL_INTEGER: int
STC_OPAL_KEYWORD: int
STC_OPAL_SORT: int
STC_OPAL_STRING: int
STC_OPAL_PAR: int
STC_OPAL_BOOL_CONST: int
STC_OPAL_DEFAULT: int
STC_SPICE_DEFAULT: int
STC_SPICE_IDENTIFIER: int
STC_SPICE_KEYWORD: int
STC_SPICE_KEYWORD2: int
STC_SPICE_KEYWORD3: int
STC_SPICE_NUMBER: int
STC_SPICE_DELIMITER: int
STC_SPICE_VALUE: int
STC_SPICE_COMMENTLINE: int
STC_CMAKE_DEFAULT: int
STC_CMAKE_COMMENT: int
STC_CMAKE_STRINGDQ: int
STC_CMAKE_STRINGLQ: int
STC_CMAKE_STRINGRQ: int
STC_CMAKE_COMMANDS: int
STC_CMAKE_PARAMETERS: int
STC_CMAKE_VARIABLE: int
STC_CMAKE_USERDEFINED: int
STC_CMAKE_WHILEDEF: int
STC_CMAKE_FOREACHDEF: int
STC_CMAKE_IFDEFINEDEF: int
STC_CMAKE_MACRODEF: int
STC_CMAKE_STRINGVAR: int
STC_CMAKE_NUMBER: int
STC_GAP_DEFAULT: int
STC_GAP_IDENTIFIER: int
STC_GAP_KEYWORD: int
STC_GAP_KEYWORD2: int
STC_GAP_KEYWORD3: int
STC_GAP_KEYWORD4: int
STC_GAP_STRING: int
STC_GAP_CHAR: int
STC_GAP_OPERATOR: int
STC_GAP_COMMENT: int
STC_GAP_NUMBER: int
STC_GAP_STRINGEOL: int
STC_PLM_DEFAULT: int
STC_PLM_COMMENT: int
STC_PLM_STRING: int
STC_PLM_NUMBER: int
STC_PLM_IDENTIFIER: int
STC_PLM_OPERATOR: int
STC_PLM_CONTROL: int
STC_PLM_KEYWORD: int
STC_ABL_DEFAULT: int
STC_ABL_NUMBER: int
STC_ABL_WORD: int
STC_ABL_STRING: int
STC_ABL_CHARACTER: int
STC_ABL_PREPROCESSOR: int
STC_ABL_OPERATOR: int
STC_ABL_IDENTIFIER: int
STC_ABL_BLOCK: int
STC_ABL_END: int
STC_ABL_COMMENT: int
STC_ABL_TASKMARKER: int
STC_ABL_LINECOMMENT: int
STC_ABAQUS_DEFAULT: int
STC_ABAQUS_COMMENT: int
STC_ABAQUS_COMMENTBLOCK: int
STC_ABAQUS_NUMBER: int
STC_ABAQUS_STRING: int
STC_ABAQUS_OPERATOR: int
STC_ABAQUS_WORD: int
STC_ABAQUS_PROCESSOR: int
STC_ABAQUS_COMMAND: int
STC_ABAQUS_SLASHCOMMAND: int
STC_ABAQUS_STARCOMMAND: int
STC_ABAQUS_ARGUMENT: int
STC_ABAQUS_FUNCTION: int
STC_ASY_DEFAULT: int
STC_ASY_COMMENT: int
STC_ASY_COMMENTLINE: int
STC_ASY_NUMBER: int
STC_ASY_WORD: int
STC_ASY_STRING: int
STC_ASY_CHARACTER: int
STC_ASY_OPERATOR: int
STC_ASY_IDENTIFIER: int
STC_ASY_STRINGEOL: int
STC_ASY_COMMENTLINEDOC: int
STC_ASY_WORD2: int
STC_R_DEFAULT: int
STC_R_COMMENT: int
STC_R_KWORD: int
STC_R_BASEKWORD: int
STC_R_OTHERKWORD: int
STC_R_NUMBER: int
STC_R_STRING: int
STC_R_STRING2: int
STC_R_OPERATOR: int
STC_R_IDENTIFIER: int
STC_R_INFIX: int
STC_R_INFIXEOL: int
STC_MAGIK_DEFAULT: int
STC_MAGIK_COMMENT: int
STC_MAGIK_HYPER_COMMENT: int
STC_MAGIK_STRING: int
STC_MAGIK_CHARACTER: int
STC_MAGIK_NUMBER: int
STC_MAGIK_IDENTIFIER: int
STC_MAGIK_OPERATOR: int
STC_MAGIK_FLOW: int
STC_MAGIK_CONTAINER: int
STC_MAGIK_BRACKET_BLOCK: int
STC_MAGIK_BRACE_BLOCK: int
STC_MAGIK_SQBRACKET_BLOCK: int
STC_MAGIK_UNKNOWN_KEYWORD: int
STC_MAGIK_KEYWORD: int
STC_MAGIK_PRAGMA: int
STC_MAGIK_SYMBOL: int
STC_POWERSHELL_DEFAULT: int
STC_POWERSHELL_COMMENT: int
STC_POWERSHELL_STRING: int
STC_POWERSHELL_CHARACTER: int
STC_POWERSHELL_NUMBER: int
STC_POWERSHELL_VARIABLE: int
STC_POWERSHELL_OPERATOR: int
STC_POWERSHELL_IDENTIFIER: int
STC_POWERSHELL_KEYWORD: int
STC_POWERSHELL_CMDLET: int
STC_POWERSHELL_ALIAS: int
STC_POWERSHELL_FUNCTION: int
STC_POWERSHELL_USER1: int
STC_POWERSHELL_COMMENTSTREAM: int
STC_POWERSHELL_HERE_STRING: int
STC_POWERSHELL_HERE_CHARACTER: int
STC_POWERSHELL_COMMENTDOCKEYWORD: int
STC_MYSQL_DEFAULT: int
STC_MYSQL_COMMENT: int
STC_MYSQL_COMMENTLINE: int
STC_MYSQL_VARIABLE: int
STC_MYSQL_SYSTEMVARIABLE: int
STC_MYSQL_KNOWNSYSTEMVARIABLE: int
STC_MYSQL_NUMBER: int
STC_MYSQL_MAJORKEYWORD: int
STC_MYSQL_KEYWORD: int
STC_MYSQL_DATABASEOBJECT: int
STC_MYSQL_PROCEDUREKEYWORD: int
STC_MYSQL_STRING: int
STC_MYSQL_SQSTRING: int
STC_MYSQL_DQSTRING: int
STC_MYSQL_OPERATOR: int
STC_MYSQL_FUNCTION: int
STC_MYSQL_IDENTIFIER: int
STC_MYSQL_QUOTEDIDENTIFIER: int
STC_MYSQL_USER1: int
STC_MYSQL_USER2: int
STC_MYSQL_USER3: int
STC_MYSQL_HIDDENCOMMAND: int
STC_MYSQL_PLACEHOLDER: int
STC_PO_DEFAULT: int
STC_PO_COMMENT: int
STC_PO_MSGID: int
STC_PO_MSGID_TEXT: int
STC_PO_MSGSTR: int
STC_PO_MSGSTR_TEXT: int
STC_PO_MSGCTXT: int
STC_PO_MSGCTXT_TEXT: int
STC_PO_FUZZY: int
STC_PO_PROGRAMMER_COMMENT: int
STC_PO_REFERENCE: int
STC_PO_FLAGS: int
STC_PO_MSGID_TEXT_EOL: int
STC_PO_MSGSTR_TEXT_EOL: int
STC_PO_MSGCTXT_TEXT_EOL: int
STC_PO_ERROR: int
STC_PAS_DEFAULT: int
STC_PAS_IDENTIFIER: int
STC_PAS_COMMENT: int
STC_PAS_COMMENT2: int
STC_PAS_COMMENTLINE: int
STC_PAS_PREPROCESSOR: int
STC_PAS_PREPROCESSOR2: int
STC_PAS_NUMBER: int
STC_PAS_HEXNUMBER: int
STC_PAS_WORD: int
STC_PAS_STRING: int
STC_PAS_STRINGEOL: int
STC_PAS_CHARACTER: int
STC_PAS_OPERATOR: int
STC_PAS_ASM: int
STC_SORCUS_DEFAULT: int
STC_SORCUS_COMMAND: int
STC_SORCUS_PARAMETER: int
STC_SORCUS_COMMENTLINE: int
STC_SORCUS_STRING: int
STC_SORCUS_STRINGEOL: int
STC_SORCUS_IDENTIFIER: int
STC_SORCUS_OPERATOR: int
STC_SORCUS_NUMBER: int
STC_SORCUS_CONSTANT: int
STC_POWERPRO_DEFAULT: int
STC_POWERPRO_COMMENTBLOCK: int
STC_POWERPRO_COMMENTLINE: int
STC_POWERPRO_NUMBER: int
STC_POWERPRO_WORD: int
STC_POWERPRO_WORD2: int
STC_POWERPRO_WORD3: int
STC_POWERPRO_WORD4: int
STC_POWERPRO_DOUBLEQUOTEDSTRING: int
STC_POWERPRO_SINGLEQUOTEDSTRING: int
STC_POWERPRO_LINECONTINUE: int
STC_POWERPRO_OPERATOR: int
STC_POWERPRO_IDENTIFIER: int
STC_POWERPRO_STRINGEOL: int
STC_POWERPRO_VERBATIM: int
STC_POWERPRO_ALTQUOTE: int
STC_POWERPRO_FUNCTION: int
STC_SML_DEFAULT: int
STC_SML_IDENTIFIER: int
STC_SML_TAGNAME: int
STC_SML_KEYWORD: int
STC_SML_KEYWORD2: int
STC_SML_KEYWORD3: int
STC_SML_LINENUM: int
STC_SML_OPERATOR: int
STC_SML_NUMBER: int
STC_SML_CHAR: int
STC_SML_STRING: int
STC_SML_COMMENT: int
STC_SML_COMMENT1: int
STC_SML_COMMENT2: int
STC_SML_COMMENT3: int
STC_MARKDOWN_DEFAULT: int
STC_MARKDOWN_LINE_BEGIN: int
STC_MARKDOWN_STRONG1: int
STC_MARKDOWN_STRONG2: int
STC_MARKDOWN_EM1: int
STC_MARKDOWN_EM2: int
STC_MARKDOWN_HEADER1: int
STC_MARKDOWN_HEADER2: int
STC_MARKDOWN_HEADER3: int
STC_MARKDOWN_HEADER4: int
STC_MARKDOWN_HEADER5: int
STC_MARKDOWN_HEADER6: int
STC_MARKDOWN_PRECHAR: int
STC_MARKDOWN_ULIST_ITEM: int
STC_MARKDOWN_OLIST_ITEM: int
STC_MARKDOWN_BLOCKQUOTE: int
STC_MARKDOWN_STRIKEOUT: int
STC_MARKDOWN_HRULE: int
STC_MARKDOWN_LINK: int
STC_MARKDOWN_CODE: int
STC_MARKDOWN_CODE2: int
STC_MARKDOWN_CODEBK: int
STC_TXT2TAGS_DEFAULT: int
STC_TXT2TAGS_LINE_BEGIN: int
STC_TXT2TAGS_STRONG1: int
STC_TXT2TAGS_STRONG2: int
STC_TXT2TAGS_EM1: int
STC_TXT2TAGS_EM2: int
STC_TXT2TAGS_HEADER1: int
STC_TXT2TAGS_HEADER2: int
STC_TXT2TAGS_HEADER3: int
STC_TXT2TAGS_HEADER4: int
STC_TXT2TAGS_HEADER5: int
STC_TXT2TAGS_HEADER6: int
STC_TXT2TAGS_PRECHAR: int
STC_TXT2TAGS_ULIST_ITEM: int
STC_TXT2TAGS_OLIST_ITEM: int
STC_TXT2TAGS_BLOCKQUOTE: int
STC_TXT2TAGS_STRIKEOUT: int
STC_TXT2TAGS_HRULE: int
STC_TXT2TAGS_LINK: int
STC_TXT2TAGS_CODE: int
STC_TXT2TAGS_CODE2: int
STC_TXT2TAGS_CODEBK: int
STC_TXT2TAGS_COMMENT: int
STC_TXT2TAGS_OPTION: int
STC_TXT2TAGS_PREPROC: int
STC_TXT2TAGS_POSTPROC: int
STC_A68K_DEFAULT: int
STC_A68K_COMMENT: int
STC_A68K_NUMBER_DEC: int
STC_A68K_NUMBER_BIN: int
STC_A68K_NUMBER_HEX: int
STC_A68K_STRING1: int
STC_A68K_OPERATOR: int
STC_A68K_CPUINSTRUCTION: int
STC_A68K_EXTINSTRUCTION: int
STC_A68K_REGISTER: int
STC_A68K_DIRECTIVE: int
STC_A68K_MACRO_ARG: int
STC_A68K_LABEL: int
STC_A68K_STRING2: int
STC_A68K_IDENTIFIER: int
STC_A68K_MACRO_DECLARATION: int
STC_A68K_COMMENT_WORD: int
STC_A68K_COMMENT_SPECIAL: int
STC_A68K_COMMENT_DOXYGEN: int
STC_MODULA_DEFAULT: int
STC_MODULA_COMMENT: int
STC_MODULA_DOXYCOMM: int
STC_MODULA_DOXYKEY: int
STC_MODULA_KEYWORD: int
STC_MODULA_RESERVED: int
STC_MODULA_NUMBER: int
STC_MODULA_BASENUM: int
STC_MODULA_FLOAT: int
STC_MODULA_STRING: int
STC_MODULA_STRSPEC: int
STC_MODULA_CHAR: int
STC_MODULA_CHARSPEC: int
STC_MODULA_PROC: int
STC_MODULA_PRAGMA: int
STC_MODULA_PRGKEY: int
STC_MODULA_OPERATOR: int
STC_MODULA_BADSTR: int
STC_COFFEESCRIPT_DEFAULT: int
STC_COFFEESCRIPT_COMMENT: int
STC_COFFEESCRIPT_COMMENTLINE: int
STC_COFFEESCRIPT_COMMENTDOC: int
STC_COFFEESCRIPT_NUMBER: int
STC_COFFEESCRIPT_WORD: int
STC_COFFEESCRIPT_STRING: int
STC_COFFEESCRIPT_CHARACTER: int
STC_COFFEESCRIPT_UUID: int
STC_COFFEESCRIPT_PREPROCESSOR: int
STC_COFFEESCRIPT_OPERATOR: int
STC_COFFEESCRIPT_IDENTIFIER: int
STC_COFFEESCRIPT_STRINGEOL: int
STC_COFFEESCRIPT_VERBATIM: int
STC_COFFEESCRIPT_REGEX: int
STC_COFFEESCRIPT_COMMENTLINEDOC: int
STC_COFFEESCRIPT_WORD2: int
STC_COFFEESCRIPT_COMMENTDOCKEYWORD: int
STC_COFFEESCRIPT_COMMENTDOCKEYWORDERROR: int
STC_COFFEESCRIPT_GLOBALCLASS: int
STC_COFFEESCRIPT_STRINGRAW: int
STC_COFFEESCRIPT_TRIPLEVERBATIM: int
STC_COFFEESCRIPT_COMMENTBLOCK: int
STC_COFFEESCRIPT_VERBOSE_REGEX: int
STC_COFFEESCRIPT_VERBOSE_REGEX_COMMENT: int
STC_COFFEESCRIPT_INSTANCEPROPERTY: int
STC_AVS_DEFAULT: int
STC_AVS_COMMENTBLOCK: int
STC_AVS_COMMENTBLOCKN: int
STC_AVS_COMMENTLINE: int
STC_AVS_NUMBER: int
STC_AVS_OPERATOR: int
STC_AVS_IDENTIFIER: int
STC_AVS_STRING: int
STC_AVS_TRIPLESTRING: int
STC_AVS_KEYWORD: int
STC_AVS_FILTER: int
STC_AVS_PLUGIN: int
STC_AVS_FUNCTION: int
STC_AVS_CLIPPROP: int
STC_AVS_USERDFN: int
STC_ECL_DEFAULT: int
STC_ECL_COMMENT: int
STC_ECL_COMMENTLINE: int
STC_ECL_NUMBER: int
STC_ECL_STRING: int
STC_ECL_WORD0: int
STC_ECL_OPERATOR: int
STC_ECL_CHARACTER: int
STC_ECL_UUID: int
STC_ECL_PREPROCESSOR: int
STC_ECL_UNKNOWN: int
STC_ECL_IDENTIFIER: int
STC_ECL_STRINGEOL: int
STC_ECL_VERBATIM: int
STC_ECL_REGEX: int
STC_ECL_COMMENTLINEDOC: int
STC_ECL_WORD1: int
STC_ECL_COMMENTDOCKEYWORD: int
STC_ECL_COMMENTDOCKEYWORDERROR: int
STC_ECL_WORD2: int
STC_ECL_WORD3: int
STC_ECL_WORD4: int
STC_ECL_WORD5: int
STC_ECL_COMMENTDOC: int
STC_ECL_ADDED: int
STC_ECL_DELETED: int
STC_ECL_CHANGED: int
STC_ECL_MOVED: int
STC_OSCRIPT_DEFAULT: int
STC_OSCRIPT_LINE_COMMENT: int
STC_OSCRIPT_BLOCK_COMMENT: int
STC_OSCRIPT_DOC_COMMENT: int
STC_OSCRIPT_PREPROCESSOR: int
STC_OSCRIPT_NUMBER: int
STC_OSCRIPT_SINGLEQUOTE_STRING: int
STC_OSCRIPT_DOUBLEQUOTE_STRING: int
STC_OSCRIPT_CONSTANT: int
STC_OSCRIPT_IDENTIFIER: int
STC_OSCRIPT_GLOBAL: int
STC_OSCRIPT_KEYWORD: int
STC_OSCRIPT_OPERATOR: int
STC_OSCRIPT_LABEL: int
STC_OSCRIPT_TYPE: int
STC_OSCRIPT_FUNCTION: int
STC_OSCRIPT_OBJECT: int
STC_OSCRIPT_PROPERTY: int
STC_OSCRIPT_METHOD: int
STC_VISUALPROLOG_DEFAULT: int
STC_VISUALPROLOG_KEY_MAJOR: int
STC_VISUALPROLOG_KEY_MINOR: int
STC_VISUALPROLOG_KEY_DIRECTIVE: int
STC_VISUALPROLOG_COMMENT_BLOCK: int
STC_VISUALPROLOG_COMMENT_LINE: int
STC_VISUALPROLOG_COMMENT_KEY: int
STC_VISUALPROLOG_COMMENT_KEY_ERROR: int
STC_VISUALPROLOG_IDENTIFIER: int
STC_VISUALPROLOG_VARIABLE: int
STC_VISUALPROLOG_ANONYMOUS: int
STC_VISUALPROLOG_NUMBER: int
STC_VISUALPROLOG_OPERATOR: int
STC_VISUALPROLOG_CHARACTER: int
STC_VISUALPROLOG_CHARACTER_TOO_MANY: int
STC_VISUALPROLOG_CHARACTER_ESCAPE_ERROR: int
STC_VISUALPROLOG_STRING: int
STC_VISUALPROLOG_STRING_ESCAPE: int
STC_VISUALPROLOG_STRING_ESCAPE_ERROR: int
STC_VISUALPROLOG_STRING_EOL_OPEN: int
STC_VISUALPROLOG_STRING_VERBATIM: int
STC_VISUALPROLOG_STRING_VERBATIM_SPECIAL: int
STC_VISUALPROLOG_STRING_VERBATIM_EOL: int
STC_STTXT_DEFAULT: int
STC_STTXT_COMMENT: int
STC_STTXT_COMMENTLINE: int
STC_STTXT_KEYWORD: int
STC_STTXT_TYPE: int
STC_STTXT_FUNCTION: int
STC_STTXT_FB: int
STC_STTXT_NUMBER: int
STC_STTXT_HEXNUMBER: int
STC_STTXT_PRAGMA: int
STC_STTXT_OPERATOR: int
STC_STTXT_CHARACTER: int
STC_STTXT_STRING1: int
STC_STTXT_STRING2: int
STC_STTXT_STRINGEOL: int
STC_STTXT_IDENTIFIER: int
STC_STTXT_DATETIME: int
STC_STTXT_VARS: int
STC_STTXT_PRAGMAS: int
STC_KVIRC_DEFAULT: int
STC_KVIRC_COMMENT: int
STC_KVIRC_COMMENTBLOCK: int
STC_KVIRC_STRING: int
STC_KVIRC_WORD: int
STC_KVIRC_KEYWORD: int
STC_KVIRC_FUNCTION_KEYWORD: int
STC_KVIRC_FUNCTION: int
STC_KVIRC_VARIABLE: int
STC_KVIRC_NUMBER: int
STC_KVIRC_OPERATOR: int
STC_KVIRC_STRING_FUNCTION: int
STC_KVIRC_STRING_VARIABLE: int
STC_RUST_DEFAULT: int
STC_RUST_COMMENTBLOCK: int
STC_RUST_COMMENTLINE: int
STC_RUST_COMMENTBLOCKDOC: int
STC_RUST_COMMENTLINEDOC: int
STC_RUST_NUMBER: int
STC_RUST_WORD: int
STC_RUST_WORD2: int
STC_RUST_WORD3: int
STC_RUST_WORD4: int
STC_RUST_WORD5: int
STC_RUST_WORD6: int
STC_RUST_WORD7: int
STC_RUST_STRING: int
STC_RUST_STRINGR: int
STC_RUST_CHARACTER: int
STC_RUST_OPERATOR: int
STC_RUST_IDENTIFIER: int
STC_RUST_LIFETIME: int
STC_RUST_MACRO: int
STC_RUST_LEXERROR: int
STC_RUST_BYTESTRING: int
STC_RUST_BYTESTRINGR: int
STC_RUST_BYTECHARACTER: int
STC_DMAP_DEFAULT: int
STC_DMAP_COMMENT: int
STC_DMAP_NUMBER: int
STC_DMAP_STRING1: int
STC_DMAP_STRING2: int
STC_DMAP_STRINGEOL: int
STC_DMAP_OPERATOR: int
STC_DMAP_IDENTIFIER: int
STC_DMAP_WORD: int
STC_DMAP_WORD2: int
STC_DMAP_WORD3: int
STC_DMIS_DEFAULT: int
STC_DMIS_COMMENT: int
STC_DMIS_STRING: int
STC_DMIS_NUMBER: int
STC_DMIS_KEYWORD: int
STC_DMIS_MAJORWORD: int
STC_DMIS_MINORWORD: int
STC_DMIS_UNSUPPORTED_MAJOR: int
STC_DMIS_UNSUPPORTED_MINOR: int
STC_DMIS_LABEL: int
STC_REG_DEFAULT: int
STC_REG_COMMENT: int
STC_REG_VALUENAME: int
STC_REG_STRING: int
STC_REG_HEXDIGIT: int
STC_REG_VALUETYPE: int
STC_REG_ADDEDKEY: int
STC_REG_DELETEDKEY: int
STC_REG_ESCAPED: int
STC_REG_KEYPATH_GUID: int
STC_REG_STRING_GUID: int
STC_REG_PARAMETER: int
STC_REG_OPERATOR: int
STC_BIBTEX_DEFAULT: int
STC_BIBTEX_ENTRY: int
STC_BIBTEX_UNKNOWN_ENTRY: int
STC_BIBTEX_KEY: int
STC_BIBTEX_PARAMETER: int
STC_BIBTEX_VALUE: int
STC_BIBTEX_COMMENT: int
STC_HEX_DEFAULT: int
STC_HEX_RECSTART: int
STC_HEX_RECTYPE: int
STC_HEX_RECTYPE_UNKNOWN: int
STC_HEX_BYTECOUNT: int
STC_HEX_BYTECOUNT_WRONG: int
STC_HEX_NOADDRESS: int
STC_HEX_DATAADDRESS: int
STC_HEX_RECCOUNT: int
STC_HEX_STARTADDRESS: int
STC_HEX_ADDRESSFIELD_UNKNOWN: int
STC_HEX_EXTENDEDADDRESS: int
STC_HEX_DATA_ODD: int
STC_HEX_DATA_EVEN: int
STC_HEX_DATA_UNKNOWN: int
STC_HEX_DATA_EMPTY: int
STC_HEX_CHECKSUM: int
STC_HEX_CHECKSUM_WRONG: int
STC_HEX_GARBAGE: int
STC_JSON_DEFAULT: int
STC_JSON_NUMBER: int
STC_JSON_STRING: int
STC_JSON_STRINGEOL: int
STC_JSON_PROPERTYNAME: int
STC_JSON_ESCAPESEQUENCE: int
STC_JSON_LINECOMMENT: int
STC_JSON_BLOCKCOMMENT: int
STC_JSON_OPERATOR: int
STC_JSON_URI: int
STC_JSON_COMPACTIRI: int
STC_JSON_KEYWORD: int
STC_JSON_LDKEYWORD: int
STC_JSON_ERROR: int
STC_EDI_DEFAULT: int
STC_EDI_SEGMENTSTART: int
STC_EDI_SEGMENTEND: int
STC_EDI_SEP_ELEMENT: int
STC_EDI_SEP_COMPOSITE: int
STC_EDI_SEP_RELEASE: int
STC_EDI_UNA: int
STC_EDI_UNH: int
STC_EDI_BADSEGMENT: int
STC_INDIC0_MASK: int
STC_INDIC1_MASK: int
STC_INDIC2_MASK: int
STC_INDICS_MASK: int
STC_CMD_REDO: int
STC_CMD_SELECTALL: int
STC_CMD_UNDO: int
STC_CMD_CUT: int
STC_CMD_COPY: int
STC_CMD_PASTE: int
STC_CMD_CLEAR: int
STC_CMD_LINEDOWN: int
STC_CMD_LINEDOWNEXTEND: int
STC_CMD_LINEUP: int
STC_CMD_LINEUPEXTEND: int
STC_CMD_CHARLEFT: int
STC_CMD_CHARLEFTEXTEND: int
STC_CMD_CHARRIGHT: int
STC_CMD_CHARRIGHTEXTEND: int
STC_CMD_WORDLEFT: int
STC_CMD_WORDLEFTEXTEND: int
STC_CMD_WORDRIGHT: int
STC_CMD_WORDRIGHTEXTEND: int
STC_CMD_HOME: int
STC_CMD_HOMEEXTEND: int
STC_CMD_LINEEND: int
STC_CMD_LINEENDEXTEND: int
STC_CMD_DOCUMENTSTART: int
STC_CMD_DOCUMENTSTARTEXTEND: int
STC_CMD_DOCUMENTEND: int
STC_CMD_DOCUMENTENDEXTEND: int
STC_CMD_PAGEUP: int
STC_CMD_PAGEUPEXTEND: int
STC_CMD_PAGEDOWN: int
STC_CMD_PAGEDOWNEXTEND: int
STC_CMD_EDITTOGGLEOVERTYPE: int
STC_CMD_CANCEL: int
STC_CMD_DELETEBACK: int
STC_CMD_TAB: int
STC_CMD_BACKTAB: int
STC_CMD_NEWLINE: int
STC_CMD_FORMFEED: int
STC_CMD_VCHOME: int
STC_CMD_VCHOMEEXTEND: int
STC_CMD_ZOOMIN: int
STC_CMD_ZOOMOUT: int
STC_CMD_DELWORDLEFT: int
STC_CMD_DELWORDRIGHT: int
STC_CMD_DELWORDRIGHTEND: int
STC_CMD_LINECUT: int
STC_CMD_LINEDELETE: int
STC_CMD_LINETRANSPOSE: int
STC_CMD_LINEDUPLICATE: int
STC_CMD_LOWERCASE: int
STC_CMD_UPPERCASE: int
STC_CMD_LINESCROLLDOWN: int
STC_CMD_LINESCROLLUP: int
STC_CMD_DELETEBACKNOTLINE: int
STC_CMD_HOMEDISPLAY: int
STC_CMD_HOMEDISPLAYEXTEND: int
STC_CMD_LINEENDDISPLAY: int
STC_CMD_LINEENDDISPLAYEXTEND: int
STC_CMD_HOMEWRAP: int
STC_CMD_HOMEWRAPEXTEND: int
STC_CMD_LINEENDWRAP: int
STC_CMD_LINEENDWRAPEXTEND: int
STC_CMD_VCHOMEWRAP: int
STC_CMD_VCHOMEWRAPEXTEND: int
STC_CMD_LINECOPY: int
STC_CMD_WORDPARTLEFT: int
STC_CMD_WORDPARTLEFTEXTEND: int
STC_CMD_WORDPARTRIGHT: int
STC_CMD_WORDPARTRIGHTEXTEND: int
STC_CMD_DELLINELEFT: int
STC_CMD_DELLINERIGHT: int
STC_CMD_PARADOWN: int
STC_CMD_PARADOWNEXTEND: int
STC_CMD_PARAUP: int
STC_CMD_PARAUPEXTEND: int
STC_CMD_LINEDOWNRECTEXTEND: int
STC_CMD_LINEUPRECTEXTEND: int
STC_CMD_CHARLEFTRECTEXTEND: int
STC_CMD_CHARRIGHTRECTEXTEND: int
STC_CMD_HOMERECTEXTEND: int
STC_CMD_VCHOMERECTEXTEND: int
STC_CMD_LINEENDRECTEXTEND: int
STC_CMD_PAGEUPRECTEXTEND: int
STC_CMD_PAGEDOWNRECTEXTEND: int
STC_CMD_STUTTEREDPAGEUP: int
STC_CMD_STUTTEREDPAGEUPEXTEND: int
STC_CMD_STUTTEREDPAGEDOWN: int
STC_CMD_STUTTEREDPAGEDOWNEXTEND: int
STC_CMD_WORDLEFTEND: int
STC_CMD_WORDLEFTENDEXTEND: int
STC_CMD_WORDRIGHTEND: int
STC_CMD_WORDRIGHTENDEXTEND: int
STC_CMD_VERTICALCENTRECARET: int
STC_CMD_MOVESELECTEDLINESUP: int
STC_CMD_MOVESELECTEDLINESDOWN: int
STC_CMD_SCROLLTOSTART: int
STC_CMD_SCROLLTOEND: int
STC_CMD_VCHOMEDISPLAY: int
STC_CMD_VCHOMEDISPLAYEXTEND: int
wxEVT_STC_CHANGE: int
wxEVT_STC_STYLENEEDED: int
wxEVT_STC_CHARADDED: int
wxEVT_STC_SAVEPOINTREACHED: int
wxEVT_STC_SAVEPOINTLEFT: int
wxEVT_STC_ROMODIFYATTEMPT: int
wxEVT_STC_KEY: int
wxEVT_STC_DOUBLECLICK: int
wxEVT_STC_UPDATEUI: int
wxEVT_STC_MODIFIED: int
wxEVT_STC_MACRORECORD: int
wxEVT_STC_MARGINCLICK: int
wxEVT_STC_NEEDSHOWN: int
wxEVT_STC_PAINTED: int
wxEVT_STC_USERLISTSELECTION: int
wxEVT_STC_URIDROPPED: int
wxEVT_STC_DWELLSTART: int
wxEVT_STC_DWELLEND: int
wxEVT_STC_START_DRAG: int
wxEVT_STC_DRAG_OVER: int
wxEVT_STC_DO_DROP: int
wxEVT_STC_ZOOM: int
wxEVT_STC_HOTSPOT_CLICK: int
wxEVT_STC_HOTSPOT_DCLICK: int
wxEVT_STC_CALLTIP_CLICK: int
wxEVT_STC_AUTOCOMP_SELECTION: int
wxEVT_STC_INDICATOR_CLICK: int
wxEVT_STC_INDICATOR_RELEASE: int
wxEVT_STC_AUTOCOMP_CANCELLED: int
wxEVT_STC_AUTOCOMP_CHAR_DELETED: int
wxEVT_STC_HOTSPOT_RELEASE_CLICK: int
wxEVT_STC_CLIPBOARD_COPY: int
wxEVT_STC_CLIPBOARD_PASTE: int
wxEVT_STC_AUTOCOMP_COMPLETED: int
wxEVT_STC_MARGIN_RIGHT_CLICK: int
wxEVT_STC_AUTOCOMP_SELECTION_CHANGE: int
STCNameStr: str

class StyledTextCtrl(wx.wx.Control, wx.wx.TextEntry):
    """
    StyledTextCtrl(parent, id=ID_ANY, pos=DefaultPosition, size=DefaultSize, style=0, name=STCNameStr) -> None
    StyledTextCtrl() -> None
    
    A wxWidgets implementation of the Scintilla source code editing
    component.
    """

    @overload
    def __init__(self) -> None:
        ...

    @overload
    def __init__(self, parent: Window, id: int=ID_ANY, pos: Point=DefaultPosition, size: Size=DefaultSize, style: int=0, name: str=STCNameStr) -> None:
        """
        StyledTextCtrl(parent, id=ID_ANY, pos=DefaultPosition, size=DefaultSize, style=0, name=STCNameStr) -> None
        StyledTextCtrl() -> None
        
        A wxWidgets implementation of the Scintilla source code editing
        component.
        """

    def Create(self, parent: Window, id: int=ID_ANY, pos: Point=DefaultPosition, size: Size=DefaultSize, style: int=0, name: str=STCNameStr) -> bool:
        """
        Create(parent, id=ID_ANY, pos=DefaultPosition, size=DefaultSize, style=0, name=STCNameStr) -> bool
        
        Create the UI elements for a STC that was created with the default ctor.
        """

    def AddText(self, text: str) -> None:
        """
        AddText(text) -> None
        
        Add text to the document at current position.
        """

    def AddStyledText(self, data: MemoryBuffer) -> None:
        """
        AddStyledText(data) -> None
        
        Add array of cells to document.
        """

    def InsertText(self, pos: int, text: str) -> None:
        """
        InsertText(pos, text) -> None
        
        Insert string at a position.
        """

    def ChangeInsertion(self, length: int, text: str) -> None:
        """
        ChangeInsertion(length, text) -> None
        
        Change the text that is being inserted in response to
        wxSTC_MOD_INSERTCHECK.
        """

    def ClearAll(self) -> None:
        """
        ClearAll() -> None
        
        Delete all text in the document.
        """

    def DeleteRange(self, start: int, lengthDelete: int) -> None:
        """
        DeleteRange(start, lengthDelete) -> None
        
        Delete a range of text in the document.
        """

    def ClearDocumentStyle(self) -> None:
        """
        ClearDocumentStyle() -> None
        
        Set all style bytes to 0, remove all folding information.
        """

    def GetCharAt(self, pos: int) -> int:
        """
        GetCharAt(pos) -> int
        
        Returns the character byte at the position.
        """

    def GetStyleAt(self, pos: int) -> int:
        """
        GetStyleAt(pos) -> int
        
        Returns the style byte at the position.
        """

    def SetSavePoint(self) -> None:
        """
        SetSavePoint() -> None
        
        Remember the current position in the undo history as the position at
        which the document was saved.
        """

    def GetStyledText(self, startPos: int, endPos: int) -> MemoryBuffer:
        """
        GetStyledText(startPos, endPos) -> MemoryBuffer
        
        Retrieve a buffer of cells.
        """

    def GetReadOnly(self) -> bool:
        """
        GetReadOnly() -> bool
        
        In read-only mode?
        """

    def GetLine(self, line: int) -> str:
        """
        GetLine(line) -> str
        
        Retrieve the contents of a line.
        """

    def GetTextRange(self, startPos: int, endPos: int) -> str:
        """
        GetTextRange(startPos, endPos) -> str
        
        Retrieve a range of text.
        """

    def ReplaceSelection(self, text: str) -> None:
        """
        ReplaceSelection(text) -> None
        
        Replace the selected text with the argument text.
        """

    def SetReadOnly(self, readOnly: bool) -> None:
        """
        SetReadOnly(readOnly) -> None
        
        Set to read only or read write.
        """

    def SetText(self, text: str) -> None:
        """
        SetText(text) -> None
        
        Replace the contents of the document with the argument text.
        """

    def GetText(self) -> str:
        """
        GetText() -> str
        
        Retrieve all the text in the document.
        """

    def AppendText(self, text: str) -> None:
        """
        AppendText(text) -> None
        
        Append a string to the end of the document without changing the
        selection.
        """

    def Allocate(self, bytes: int) -> None:
        """
        Allocate(bytes) -> None
        
        Enlarge the document to a particular size of text bytes.
        """

    def ReleaseAllExtendedStyles(self) -> None:
        """
        ReleaseAllExtendedStyles() -> None
        
        Release all extended (>255) style numbers.
        """

    def AllocateExtendedStyles(self, numberStyles: int) -> int:
        """
        AllocateExtendedStyles(numberStyles) -> int
        
        Allocate some extended (>255) style numbers and return the start of
        the range.
        """

    def FindText(self, minPos: int, maxPos: int, text: str, flags: int=0) -> Tuple[int, int]:
        """
        FindText(minPos, maxPos, text, flags=0) -> Tuple[int, int]
        
        Find some text in the document.
        """

    def SetTargetStart(self, start: int) -> None:
        """
        SetTargetStart(start) -> None
        
        Sets the position that starts the target which is used for updating
        the document without affecting the scroll position.
        """

    def GetTargetStart(self) -> int:
        """
        GetTargetStart() -> int
        
        Get the position that starts the target.
        """

    def SetTargetEnd(self, end: int) -> None:
        """
        SetTargetEnd(end) -> None
        
        Sets the position that ends the target which is used for updating the
        document without affecting the scroll position.
        """

    def GetTargetEnd(self) -> int:
        """
        GetTargetEnd() -> int
        
        Get the position that ends the target.
        """

    def SetTargetRange(self, start: int, end: int) -> None:
        """
        SetTargetRange(start, end) -> None
        
        Sets both the start and end of the target in one call.
        """

    def GetTargetText(self) -> str:
        """
        GetTargetText() -> str
        
        Retrieve the text in the target.
        """

    def TargetFromSelection(self) -> None:
        """
        TargetFromSelection() -> None
        
        Make the target range start and end be the same as the selection range
        start and end.
        """

    def TargetWholeDocument(self) -> None:
        """
        TargetWholeDocument() -> None
        
        Sets the target to the whole document.
        """

    def ReplaceTarget(self, text: str) -> int:
        """
        ReplaceTarget(text) -> int
        
        Replace the target text with the argument text.
        """

    def ReplaceTargetRE(self, text: str) -> int:
        """
        ReplaceTargetRE(text) -> int
        
        Replace the target text with the argument text after \d processing.
        """

    def SearchInTarget(self, text: str) -> int:
        """
        SearchInTarget(text) -> int
        
        Search for a counted string in the target and set the target to the
        found range.
        """

    def SetSearchFlags(self, searchFlags: int) -> None:
        """
        SetSearchFlags(searchFlags) -> None
        
        Set the search flags used by SearchInTarget.
        """

    def GetSearchFlags(self) -> int:
        """
        GetSearchFlags() -> int
        
        Get the search flags used by SearchInTarget.
        """

    def GetTag(self, tagNumber: int) -> str:
        """
        GetTag(tagNumber) -> str
        
        Retrieve the value of a tag from a regular expression search.
        """

    def SearchAnchor(self) -> None:
        """
        SearchAnchor() -> None
        
        Sets the current caret position to be the search anchor.
        """

    def SearchNext(self, searchFlags: int, text: str) -> int:
        """
        SearchNext(searchFlags, text) -> int
        
        Find some text starting at the search anchor.
        """

    def SearchPrev(self, searchFlags: int, text: str) -> int:
        """
        SearchPrev(searchFlags, text) -> int
        
        Find some text starting at the search anchor and moving backwards.
        """

    def SetOvertype(self, overType: bool) -> None:
        """
        SetOvertype(overType) -> None
        
        Set to overtype (true) or insert mode.
        """

    def GetOvertype(self) -> bool:
        """
        GetOvertype() -> bool
        
        Returns true if overtype mode is active otherwise false is returned.
        """

    def CanPaste(self) -> bool:
        """
        CanPaste() -> bool
        
        Will a paste succeed?
        """

    def Cut(self) -> None:
        """
        Cut() -> None
        
        Cut the selection to the clipboard.
        """

    def Copy(self) -> None:
        """
        Copy() -> None
        
        Copy the selection to the clipboard.
        """

    def Paste(self) -> None:
        """
        Paste() -> None
        
        Paste the contents of the clipboard into the document replacing the selection.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Clear the selection.
        """

    def CopyRange(self, start: int, end: int) -> None:
        """
        CopyRange(start, end) -> None
        
        Copy a range of text to the clipboard.
        """

    def CopyText(self, length: int, text: str) -> None:
        """
        CopyText(length, text) -> None
        
        Copy argument text to the clipboard.
        """

    def SetPasteConvertEndings(self, convert: bool) -> None:
        """
        SetPasteConvertEndings(convert) -> None
        
        Enable/Disable convert-on-paste for line endings.
        """

    def GetPasteConvertEndings(self) -> bool:
        """
        GetPasteConvertEndings() -> bool
        
        Get convert-on-paste setting.
        """

    def CopyAllowLine(self) -> None:
        """
        CopyAllowLine() -> None
        
        Copy the selection, if selection empty copy the line with the caret.
        """

    def SetStatus(self, status: int) -> None:
        """
        SetStatus(status) -> None
        
        Change error status - 0 = OK.
        """

    def GetStatus(self) -> int:
        """
        GetStatus() -> int
        
        Get error status.
        """

    def Redo(self) -> None:
        """
        Redo() -> None
        
        Redoes the next action on the undo history.
        """

    def SetUndoCollection(self, collectUndo: bool) -> None:
        """
        SetUndoCollection(collectUndo) -> None
        
        Choose between collecting actions into the undo history and discarding
        them.
        """

    def CanRedo(self) -> bool:
        """
        CanRedo() -> bool
        
        Are there any redoable actions in the undo history?
        """

    def GetUndoCollection(self) -> bool:
        """
        GetUndoCollection() -> bool
        
        Is undo history being collected?
        """

    def BeginUndoAction(self) -> None:
        """
        BeginUndoAction() -> None
        
        Start a sequence of actions that is undone and redone as a unit.
        """

    def EndUndoAction(self) -> None:
        """
        EndUndoAction() -> None
        
        End a sequence of actions that is undone and redone as a unit.
        """

    def CanUndo(self) -> bool:
        """
        CanUndo() -> bool
        
        Are there any undoable actions in the undo history?
        """

    def EmptyUndoBuffer(self) -> None:
        """
        EmptyUndoBuffer() -> None
        
        Delete the undo history.
        """

    def Undo(self) -> None:
        """
        Undo() -> None
        
        Undo one action in the undo history.
        """

    def AddUndoAction(self, token: int, flags: int) -> None:
        """
        AddUndoAction(token, flags) -> None
        
        Add a container action to the undo stack.
        """

    def GetLength(self) -> int:
        """
        GetLength() -> int
        
        Returns the number of bytes in the document.
        """

    def GetCurrentPos(self) -> int:
        """
        GetCurrentPos() -> int
        
        Returns the position of the caret.
        """

    def GetAnchor(self) -> int:
        """
        GetAnchor() -> int
        
        Returns the position of the opposite end of the selection to the
        caret.
        """

    def SelectAll(self) -> None:
        """
        SelectAll() -> None
        
        Select all the text in the document.
        """

    def PositionFromPoint(self, pt: Point) -> int:
        """
        PositionFromPoint(pt) -> int
        
        Find the position from a point within the window.
        """

    def PositionFromPointClose(self, x: int, y: int) -> int:
        """
        PositionFromPointClose(x, y) -> int
        
        Find the position from a point within the window but return
        wxSTC_INVALID_POSITION if not close to text.
        """

    def GotoLine(self, line: int) -> None:
        """
        GotoLine(line) -> None
        
        Set caret to start of a line and ensure it is visible.
        """

    def GotoPos(self, caret: int) -> None:
        """
        GotoPos(caret) -> None
        
        Set caret to a position and ensure it is visible.
        """

    def SetAnchor(self, anchor: int) -> None:
        """
        SetAnchor(anchor) -> None
        
        Set the selection anchor to a position.
        """

    def GetCurLine(self) -> Tuple[str, int]:
        """
        GetCurLine() -> Tuple[str, int]
        
        Retrieve the text of the line containing the caret.
        """

    def GetColumn(self, pos: int) -> int:
        """
        GetColumn(pos) -> int
        
        Retrieve the column number of a position, taking tab width into
        account.
        """

    def CountCharacters(self, start: int, end: int) -> int:
        """
        CountCharacters(start, end) -> int
        
        Count characters between two positions.
        """

    def GetLineEndPosition(self, line: int) -> int:
        """
        GetLineEndPosition(line) -> int
        
        Get the position after the last visible characters on a line.
        """

    def SetCurrentPos(self, caret: int) -> None:
        """
        SetCurrentPos(caret) -> None
        
        Sets the position of the caret.
        """

    def SetSelectionStart(self, anchor: int) -> None:
        """
        SetSelectionStart(anchor) -> None
        
        Sets the position that starts the selection - this becomes the anchor.
        """

    def GetSelectionStart(self) -> int:
        """
        GetSelectionStart() -> int
        
        Returns the position at the start of the selection.
        """

    def SetSelectionEnd(self, caret: int) -> None:
        """
        SetSelectionEnd(caret) -> None
        
        Sets the position that ends the selection - this becomes the caret.
        """

    def GetSelectionEnd(self) -> int:
        """
        GetSelectionEnd() -> int
        
        Returns the position at the end of the selection.
        """

    def SetEmptySelection(self, caret: int) -> None:
        """
        SetEmptySelection(caret) -> None
        
        Set caret to a position, while removing any existing selection.
        """

    def GetLineCount(self) -> int:
        """
        GetLineCount() -> int
        
        Returns the number of lines in the document.
        """

    def GetModify(self) -> bool:
        """
        GetModify() -> bool
        
        Is the document different from when it was last saved?
        """

    def GetSelectedText(self) -> str:
        """
        GetSelectedText() -> str
        
        Retrieve the selected text.
        """

    def HideSelection(self, hide: bool) -> None:
        """
        HideSelection(hide) -> None
        
        Draw the selection in normal style or with selection highlighted.
        """

    def PointFromPosition(self, pos: int) -> Point:
        """
        PointFromPosition(pos) -> Point
        
        Retrieve the point in the window where a position is displayed.
        """

    def LineFromPosition(self, pos: int) -> int:
        """
        LineFromPosition(pos) -> int
        
        Retrieve the line containing a position.
        """

    def PositionFromLine(self, line: int) -> int:
        """
        PositionFromLine(line) -> int
        
        Retrieve the position at the start of a line.
        """

    def GetTextLength(self) -> int:
        """
        GetTextLength() -> int
        
        Retrieve the number of characters in the document.
        """

    def TextWidth(self, style: int, text: str) -> int:
        """
        TextWidth(style, text) -> int
        
        Measure the pixel width of some text in a particular style.
        """

    def TextHeight(self, line: int) -> int:
        """
        TextHeight(line) -> int
        
        Retrieve the height of a particular line of text in pixels.
        """

    def MoveCaretInsideView(self) -> None:
        """
        MoveCaretInsideView() -> None
        
        Move the caret inside current view if it's not there already.
        """

    def LineLength(self, line: int) -> int:
        """
        LineLength(line) -> int
        
        How many characters are on a line, including end of line characters?
        """

    def LinesOnScreen(self) -> int:
        """
        LinesOnScreen() -> int
        
        Retrieves the number of lines completely visible.
        """

    def SelectionIsRectangle(self) -> bool:
        """
        SelectionIsRectangle() -> bool
        
        Is the selection rectangular? The alternative is the more common
        stream selection.
        """

    def ChooseCaretX(self) -> None:
        """
        ChooseCaretX() -> None
        
        Set the last x chosen value to be the caret x position.
        """

    def PositionBefore(self, pos: int) -> int:
        """
        PositionBefore(pos) -> int
        
        Given a valid document position, return the previous position taking
        code page into account.
        """

    def PositionAfter(self, pos: int) -> int:
        """
        PositionAfter(pos) -> int
        
        Given a valid document position, return the next position taking code
        page into account.
        """

    def PositionRelative(self, pos: int, relative: int) -> int:
        """
        PositionRelative(pos, relative) -> int
        
        Given a valid document position, return a position that differs in a
        number of characters.
        """

    def SetSelectionMode(self, selectionMode: int) -> None:
        """
        SetSelectionMode(selectionMode) -> None
        
        Set the selection mode to stream (wxSTC_SEL_STREAM) or rectangular
        (wxSTC_SEL_RECTANGLE/wxSTC_SEL_THIN) or by lines (wxSTC_SEL_LINES).
        """

    def GetSelectionMode(self) -> int:
        """
        GetSelectionMode() -> int
        
        Get the mode of the current selection.
        """

    def GetLineSelStartPosition(self, line: int) -> int:
        """
        GetLineSelStartPosition(line) -> int
        
        Retrieve the position of the start of the selection at the given line
        (wxSTC_INVALID_POSITION if no selection on this line).
        """

    def GetLineSelEndPosition(self, line: int) -> int:
        """
        GetLineSelEndPosition(line) -> int
        
        Retrieve the position of the end of the selection at the given line
        (wxSTC_INVALID_POSITION if no selection on this line).
        """

    def FindColumn(self, line: int, column: int) -> int:
        """
        FindColumn(line, column) -> int
        
        Find the position of a column on a line taking into account tabs and
        multi-byte characters.
        """

    def CharPositionFromPoint(self, x: int, y: int) -> int:
        """
        CharPositionFromPoint(x, y) -> int
        
        Find the position of a character from a point within the window.
        """

    def CharPositionFromPointClose(self, x: int, y: int) -> int:
        """
        CharPositionFromPointClose(x, y) -> int
        
        Find the position of a character from a point within the window.
        """

    def SetMouseSelectionRectangularSwitch(self, mouseSelectionRectangularSwitch: bool) -> None:
        """
        SetMouseSelectionRectangularSwitch(mouseSelectionRectangularSwitch) -> None
        
        Set whether switching to rectangular mode while selecting with the
        mouse is allowed.
        """

    def GetMouseSelectionRectangularSwitch(self) -> bool:
        """
        GetMouseSelectionRectangularSwitch() -> bool
        
        Whether switching to rectangular mode while selecting with the mouse
        is allowed.
        """

    def MoveSelectedLinesUp(self) -> None:
        """
        MoveSelectedLinesUp() -> None
        
        Move the selected lines up one line, shifting the line above after the
        selection.
        """

    def MoveSelectedLinesDown(self) -> None:
        """
        MoveSelectedLinesDown() -> None
        
        Move the selected lines down one line, shifting the line below before
        the selection.
        """

    def SetMultiPaste(self, multiPaste: int) -> None:
        """
        SetMultiPaste(multiPaste) -> None
        
        Change the effect of pasting when there are multiple selections.
        """

    def GetMultiPaste(self) -> int:
        """
        GetMultiPaste() -> int
        
        Retrieve the effect of pasting when there are multiple selections.
        """

    def SetMultipleSelection(self, multipleSelection: bool) -> None:
        """
        SetMultipleSelection(multipleSelection) -> None
        
        Set whether multiple selections can be made.
        """

    def GetMultipleSelection(self) -> bool:
        """
        GetMultipleSelection() -> bool
        
        Whether multiple selections can be made.
        """

    def SetAdditionalSelectionTyping(self, additionalSelectionTyping: bool) -> None:
        """
        SetAdditionalSelectionTyping(additionalSelectionTyping) -> None
        
        Set whether typing can be performed into multiple selections.
        """

    def GetAdditionalSelectionTyping(self) -> bool:
        """
        GetAdditionalSelectionTyping() -> bool
        
        Whether typing can be performed into multiple selections.
        """

    def SetAdditionalCaretsBlink(self, additionalCaretsBlink: bool) -> None:
        """
        SetAdditionalCaretsBlink(additionalCaretsBlink) -> None
        
        Set whether additional carets will blink.
        """

    def GetAdditionalCaretsBlink(self) -> bool:
        """
        GetAdditionalCaretsBlink() -> bool
        
        Whether additional carets will blink.
        """

    def SetAdditionalCaretsVisible(self, additionalCaretsVisible: bool) -> None:
        """
        SetAdditionalCaretsVisible(additionalCaretsVisible) -> None
        
        Set whether additional carets are visible.
        """

    def GetAdditionalCaretsVisible(self) -> bool:
        """
        GetAdditionalCaretsVisible() -> bool
        
        Whether additional carets are visible.
        """

    def GetSelections(self) -> int:
        """
        GetSelections() -> int
        
        How many selections are there?
        """

    def GetSelectionEmpty(self) -> bool:
        """
        GetSelectionEmpty() -> bool
        
        Is every selected range empty?
        """

    def ClearSelections(self) -> None:
        """
        ClearSelections() -> None
        
        Clear selections to a single empty stream selection.
        """

    def AddSelection(self, caret: int, anchor: int) -> int:
        """
        AddSelection(caret, anchor) -> int
        
        Add a selection.
        """

    def DropSelectionN(self, selection: int) -> None:
        """
        DropSelectionN(selection) -> None
        
        Drop one selection.
        """

    def SetMainSelection(self, selection: int) -> None:
        """
        SetMainSelection(selection) -> None
        
        Set the main selection.
        """

    def GetMainSelection(self) -> int:
        """
        GetMainSelection() -> int
        
        Which selection is the main selection.
        """

    def SetSelectionNCaret(self, selection: int, caret: int) -> None:
        """
        SetSelectionNCaret(selection, caret) -> None
        
        Set the caret position of the nth selection.
        """

    def GetSelectionNCaret(self, selection: int) -> int:
        """
        GetSelectionNCaret(selection) -> int
        
        Return the caret position of the nth selection.
        """

    def SetSelectionNAnchor(self, selection: int, anchor: int) -> None:
        """
        SetSelectionNAnchor(selection, anchor) -> None
        
        Set the anchor position of the nth selection.
        """

    def GetSelectionNAnchor(self, selection: int) -> int:
        """
        GetSelectionNAnchor(selection) -> int
        
        Return the anchor position of the nth selection.
        """

    def SetSelectionNCaretVirtualSpace(self, selection: int, space: int) -> None:
        """
        SetSelectionNCaretVirtualSpace(selection, space) -> None
        
        Set the virtual space of the caret of the nth selection.
        """

    def GetSelectionNCaretVirtualSpace(self, selection: int) -> int:
        """
        GetSelectionNCaretVirtualSpace(selection) -> int
        
        Return the virtual space of the caret of the nth selection.
        """

    def SetSelectionNAnchorVirtualSpace(self, selection: int, space: int) -> None:
        """
        SetSelectionNAnchorVirtualSpace(selection, space) -> None
        
        Set the virtual space of the anchor of the nth selection.
        """

    def GetSelectionNAnchorVirtualSpace(self, selection: int) -> int:
        """
        GetSelectionNAnchorVirtualSpace(selection) -> int
        
        Return the virtual space of the anchor of the nth selection.
        """

    def SetSelectionNStart(self, selection: int, anchor: int) -> None:
        """
        SetSelectionNStart(selection, anchor) -> None
        
        Sets the position that starts the selection - this becomes the anchor.
        """

    def GetSelectionNStart(self, selection: int) -> int:
        """
        GetSelectionNStart(selection) -> int
        
        Returns the position at the start of the selection.
        """

    def SetSelectionNEnd(self, selection: int, caret: int) -> None:
        """
        SetSelectionNEnd(selection, caret) -> None
        
        Sets the position that ends the selection - this becomes the
        currentPosition.
        """

    def GetSelectionNEnd(self, selection: int) -> int:
        """
        GetSelectionNEnd(selection) -> int
        
        Returns the position at the end of the selection.
        """

    def SetRectangularSelectionCaret(self, caret: int) -> None:
        """
        SetRectangularSelectionCaret(caret) -> None
        
        Set the caret position of the rectangular selection.
        """

    def GetRectangularSelectionCaret(self) -> int:
        """
        GetRectangularSelectionCaret() -> int
        
        Return the caret position of the rectangular selection.
        """

    def SetRectangularSelectionAnchor(self, anchor: int) -> None:
        """
        SetRectangularSelectionAnchor(anchor) -> None
        
        Set the anchor position of the rectangular selection.
        """

    def GetRectangularSelectionAnchor(self) -> int:
        """
        GetRectangularSelectionAnchor() -> int
        
        Return the anchor position of the rectangular selection.
        """

    def SetRectangularSelectionCaretVirtualSpace(self, space: int) -> None:
        """
        SetRectangularSelectionCaretVirtualSpace(space) -> None
        
        Set the virtual space of the caret of the rectangular selection.
        """

    def GetRectangularSelectionCaretVirtualSpace(self) -> int:
        """
        GetRectangularSelectionCaretVirtualSpace() -> int
        
        Return the virtual space of the caret of the rectangular selection.
        """

    def SetRectangularSelectionAnchorVirtualSpace(self, space: int) -> None:
        """
        SetRectangularSelectionAnchorVirtualSpace(space) -> None
        
        Set the virtual space of the anchor of the rectangular selection.
        """

    def GetRectangularSelectionAnchorVirtualSpace(self) -> int:
        """
        GetRectangularSelectionAnchorVirtualSpace() -> int
        
        Return the virtual space of the anchor of the rectangular selection.
        """

    def SetVirtualSpaceOptions(self, virtualSpaceOptions: int) -> None:
        """
        SetVirtualSpaceOptions(virtualSpaceOptions) -> None
        
        Set options for virtual space behaviour.
        """

    def GetVirtualSpaceOptions(self) -> int:
        """
        GetVirtualSpaceOptions() -> int
        
        Return options for virtual space behaviour.
        """

    def SetRectangularSelectionModifier(self, modifier: int) -> None:
        """
        SetRectangularSelectionModifier(modifier) -> None
        
        On GTK+, allow selecting the modifier key to use for mouse-based
        rectangular selection.
        """

    def GetRectangularSelectionModifier(self) -> int:
        """
        GetRectangularSelectionModifier() -> int
        
        Get the modifier key used for rectangular selection.
        """

    def SetAdditionalSelForeground(self, fore: Colour) -> None:
        """
        SetAdditionalSelForeground(fore) -> None
        
        Set the foreground colour of additional selections.
        """

    def SetAdditionalSelBackground(self, back: Colour) -> None:
        """
        SetAdditionalSelBackground(back) -> None
        
        Set the background colour of additional selections.
        """

    def SetAdditionalSelAlpha(self, alpha: int) -> None:
        """
        SetAdditionalSelAlpha(alpha) -> None
        
        Set the alpha of the selection.
        """

    def GetAdditionalSelAlpha(self) -> int:
        """
        GetAdditionalSelAlpha() -> int
        
        Get the alpha of the selection.
        """

    def SetAdditionalCaretForeground(self, fore: Colour) -> None:
        """
        SetAdditionalCaretForeground(fore) -> None
        
        Set the foreground colour of additional carets.
        """

    def GetAdditionalCaretForeground(self) -> Colour:
        """
        GetAdditionalCaretForeground() -> Colour
        
        Get the foreground colour of additional carets.
        """

    def RotateSelection(self) -> None:
        """
        RotateSelection() -> None
        
        Set the main selection to the next selection.
        """

    def SwapMainAnchorCaret(self) -> None:
        """
        SwapMainAnchorCaret() -> None
        
        Swap that caret and anchor of the main selection.
        """

    def MultipleSelectAddNext(self) -> None:
        """
        MultipleSelectAddNext() -> None
        
        Add the next occurrence of the main selection to the set of selections
        as main.
        """

    def MultipleSelectAddEach(self) -> None:
        """
        MultipleSelectAddEach() -> None
        
        Add each occurrence of the main selection in the target to the set of
        selections.
        """

    def SetUseHorizontalScrollBar(self, visible: bool) -> None:
        """
        SetUseHorizontalScrollBar(visible) -> None
        
        Show or hide the horizontal scroll bar.
        """

    def GetUseHorizontalScrollBar(self) -> bool:
        """
        GetUseHorizontalScrollBar() -> bool
        
        Is the horizontal scroll bar visible?
        """

    def GetFirstVisibleLine(self) -> int:
        """
        GetFirstVisibleLine() -> int
        
        Retrieve the display line at the top of the display.
        """

    def LineScroll(self, columns: int, lines: int) -> None:
        """
        LineScroll(columns, lines) -> None
        
        Scroll horizontally and vertically.
        """

    def EnsureCaretVisible(self) -> None:
        """
        EnsureCaretVisible() -> None
        
        Ensure the caret is visible.
        """

    def ScrollRange(self, secondary: int, primary: int) -> None:
        """
        ScrollRange(secondary, primary) -> None
        
        Scroll the argument positions and the range between them into view
        giving priority to the primary position then the secondary position.
        """

    def SetScrollWidth(self, pixelWidth: int) -> None:
        """
        SetScrollWidth(pixelWidth) -> None
        
        Sets the document width assumed for scrolling.
        """

    def GetScrollWidth(self) -> int:
        """
        GetScrollWidth() -> int
        
        Retrieve the document width assumed for scrolling.
        """

    def SetScrollWidthTracking(self, tracking: bool) -> None:
        """
        SetScrollWidthTracking(tracking) -> None
        
        Sets whether the maximum width line displayed is used to set scroll
        width.
        """

    def GetScrollWidthTracking(self) -> bool:
        """
        GetScrollWidthTracking() -> bool
        
        Retrieve whether the scroll width tracks wide lines.
        """

    def SetEndAtLastLine(self, endAtLastLine: bool) -> None:
        """
        SetEndAtLastLine(endAtLastLine) -> None
        
        Sets the scroll range so that maximum scroll position has the last
        line at the bottom of the view (default).
        """

    def GetEndAtLastLine(self) -> bool:
        """
        GetEndAtLastLine() -> bool
        
        Retrieve whether the maximum scroll position has the last line at the
        bottom of the view.
        """

    def SetUseVerticalScrollBar(self, visible: bool) -> None:
        """
        SetUseVerticalScrollBar(visible) -> None
        
        Show or hide the vertical scroll bar.
        """

    def GetUseVerticalScrollBar(self) -> bool:
        """
        GetUseVerticalScrollBar() -> bool
        
        Is the vertical scroll bar visible?
        """

    def SetFirstVisibleLine(self, displayLine: int) -> None:
        """
        SetFirstVisibleLine(displayLine) -> None
        
        Scroll so that a display line is at the top of the display.
        """

    def SetVisiblePolicy(self, visiblePolicy: int, visibleSlop: int) -> None:
        """
        SetVisiblePolicy(visiblePolicy, visibleSlop) -> None
        
        Set the way the display area is determined when a particular line is
        to be moved to by Find, FindNext, GotoLine, etc.
        """

    def SetXOffset(self, xOffset: int) -> None:
        """
        SetXOffset(xOffset) -> None
        
        Set the xOffset (ie, horizontal scroll position).
        """

    def GetXOffset(self) -> int:
        """
        GetXOffset() -> int
        
        Get the xOffset (ie, horizontal scroll position).
        """

    def SetXCaretPolicy(self, caretPolicy: int, caretSlop: int) -> None:
        """
        SetXCaretPolicy(caretPolicy, caretSlop) -> None
        
        Set the way the caret is kept visible when going sideways.
        """

    def SetYCaretPolicy(self, caretPolicy: int, caretSlop: int) -> None:
        """
        SetYCaretPolicy(caretPolicy, caretSlop) -> None
        
        Set the way the line the caret is on is kept visible.
        """

    def GetViewWhiteSpace(self) -> int:
        """
        GetViewWhiteSpace() -> int
        
        Are white space characters currently visible? Returns one of
        wxSTC_WS_* constants.
        """

    def SetViewWhiteSpace(self, viewWS: int) -> None:
        """
        SetViewWhiteSpace(viewWS) -> None
        
        Make white space characters invisible, always visible or visible
        outside indentation.
        """

    def GetTabDrawMode(self) -> int:
        """
        GetTabDrawMode() -> int
        
        Retrieve the current tab draw mode.
        """

    def SetTabDrawMode(self, tabDrawMode: int) -> None:
        """
        SetTabDrawMode(tabDrawMode) -> None
        
        Set how tabs are drawn when visible.
        """

    def SetWhitespaceForeground(self, useSetting: bool, fore: Colour) -> None:
        """
        SetWhitespaceForeground(useSetting, fore) -> None
        
        Set the foreground colour of all whitespace and whether to use this
        setting.
        """

    def SetWhitespaceBackground(self, useSetting: bool, back: Colour) -> None:
        """
        SetWhitespaceBackground(useSetting, back) -> None
        
        Set the background colour of all whitespace and whether to use this
        setting.
        """

    def SetWhitespaceSize(self, size: int) -> None:
        """
        SetWhitespaceSize(size) -> None
        
        Set the size of the dots used to mark space characters.
        """

    def GetWhitespaceSize(self) -> int:
        """
        GetWhitespaceSize() -> int
        
        Get the size of the dots used to mark space characters.
        """

    def SetExtraAscent(self, extraAscent: int) -> None:
        """
        SetExtraAscent(extraAscent) -> None
        
        Set extra ascent for each line.
        """

    def GetExtraAscent(self) -> int:
        """
        GetExtraAscent() -> int
        
        Get extra ascent for each line.
        """

    def SetExtraDescent(self, extraDescent: int) -> None:
        """
        SetExtraDescent(extraDescent) -> None
        
        Set extra descent for each line.
        """

    def GetExtraDescent(self) -> int:
        """
        GetExtraDescent() -> int
        
        Get extra descent for each line.
        """

    def SetSTCCursor(self, cursorType: int) -> None:
        """
        SetSTCCursor(cursorType) -> None
        
        Sets the cursor to one of the wxSTC_CURSOR* values.
        """

    def GetSTCCursor(self) -> int:
        """
        GetSTCCursor() -> int
        
        Get cursor type.
        """

    def SetMouseDownCaptures(self, captures: bool) -> None:
        """
        SetMouseDownCaptures(captures) -> None
        
        Set whether the mouse is captured when its button is pressed.
        """

    def GetMouseDownCaptures(self) -> bool:
        """
        GetMouseDownCaptures() -> bool
        
        Get whether mouse gets captured.
        """

    def SetMouseWheelCaptures(self, captures: bool) -> None:
        """
        SetMouseWheelCaptures(captures) -> None
        
        Set whether the mouse wheel can be active outside the window.
        """

    def GetMouseWheelCaptures(self) -> bool:
        """
        GetMouseWheelCaptures() -> bool
        
        Get whether mouse wheel can be active outside the window.
        """

    def ConvertEOLs(self, eolMode: int) -> None:
        """
        ConvertEOLs(eolMode) -> None
        
        Convert all line endings in the document to one mode.
        """

    def GetEOLMode(self) -> int:
        """
        GetEOLMode() -> int
        
        Retrieve the current end of line mode - one of wxSTC_EOL_CRLF,
        wxSTC_EOL_CR, or wxSTC_EOL_LF.
        """

    def SetEOLMode(self, eolMode: int) -> None:
        """
        SetEOLMode(eolMode) -> None
        
        Set the current end of line mode.
        """

    def GetViewEOL(self) -> bool:
        """
        GetViewEOL() -> bool
        
        Are the end of line characters visible?
        """

    def SetViewEOL(self, visible: bool) -> None:
        """
        SetViewEOL(visible) -> None
        
        Make the end of line characters visible or invisible.
        """

    def SetLineEndTypesAllowed(self, lineEndBitSet: int) -> None:
        """
        SetLineEndTypesAllowed(lineEndBitSet) -> None
        
        Set the line end types that the application wants to use.
        """

    def GetLineEndTypesAllowed(self) -> int:
        """
        GetLineEndTypesAllowed() -> int
        
        Get the line end types currently allowed.
        """

    def GetLineEndTypesActive(self) -> int:
        """
        GetLineEndTypesActive() -> int
        
        Get the line end types currently recognised.
        """

    def GetLineEndTypesSupported(self) -> int:
        """
        GetLineEndTypesSupported() -> int
        
        Bit set of LineEndType enumertion for which line ends beyond the
        standard LF, CR, and CRLF are supported by the lexer.
        """

    def SetWordChars(self, characters: str) -> None:
        """
        SetWordChars(characters) -> None
        
        Set the set of characters making up words for when moving or selecting
        by word.
        """

    def GetWordChars(self) -> str:
        """
        GetWordChars() -> str
        
        Get the set of characters making up words for when moving or selecting
        by word.
        """

    def WordStartPosition(self, pos: int, onlyWordCharacters: bool) -> int:
        """
        WordStartPosition(pos, onlyWordCharacters) -> int
        
        Get position of start of word.
        """

    def WordEndPosition(self, pos: int, onlyWordCharacters: bool) -> int:
        """
        WordEndPosition(pos, onlyWordCharacters) -> int
        
        Get position of end of word.
        """

    def IsRangeWord(self, start: int, end: int) -> bool:
        """
        IsRangeWord(start, end) -> bool
        
        Is the range start..end considered a word?
        """

    def SetWhitespaceChars(self, characters: str) -> None:
        """
        SetWhitespaceChars(characters) -> None
        
        Set the set of characters making up whitespace for when moving or
        selecting by word.
        """

    def GetWhitespaceChars(self) -> str:
        """
        GetWhitespaceChars() -> str
        
        Get the set of characters making up whitespace for when moving or
        selecting by word.
        """

    def SetPunctuationChars(self, characters: str) -> None:
        """
        SetPunctuationChars(characters) -> None
        
        Set the set of characters making up punctuation characters Should be
        called after SetWordChars.
        """

    def GetPunctuationChars(self) -> str:
        """
        GetPunctuationChars() -> str
        
        Get the set of characters making up punctuation characters.
        """

    def SetCharsDefault(self) -> None:
        """
        SetCharsDefault() -> None
        
        Reset the set of characters for whitespace and word characters to the
        defaults.
        """

    def GetEndStyled(self) -> int:
        """
        GetEndStyled() -> int
        
        Retrieve the position of the last correctly styled character.
        """

    def StartStyling(self, start: int) -> None:
        """
        StartStyling(start) -> None
        
        Set the current styling position to start.
        """

    def SetStyling(self, length: int, style: int) -> None:
        """
        SetStyling(length, style) -> None
        
        Change style from current styling position for length characters to a
        style and move the current styling position to after this newly styled
        segment.
        """

    def SetStyleBytes(self, length: int, styleBytes: str) -> None:
        """
        SetStyleBytes(length, styleBytes) -> None
        
        Set the styles for a segment of the document.
        """

    def SetLineState(self, line: int, state: int) -> None:
        """
        SetLineState(line, state) -> None
        
        Used to hold extra styling information for each line.
        """

    def GetLineState(self, line: int) -> int:
        """
        GetLineState(line) -> int
        
        Retrieve the extra styling information for a line.
        """

    def GetMaxLineState(self) -> int:
        """
        GetMaxLineState() -> int
        
        Retrieve the last line number that has line state.
        """

    def SetIdleStyling(self, idleStyling: int) -> None:
        """
        SetIdleStyling(idleStyling) -> None
        
        Sets limits to idle styling.
        """

    def GetIdleStyling(self) -> int:
        """
        GetIdleStyling() -> int
        
        Retrieve the limits to idle styling.
        """

    def StyleClearAll(self) -> None:
        """
        StyleClearAll() -> None
        
        Clear all the styles and make equivalent to the global default style.
        """

    def StyleSetForeground(self, style: int, fore: Colour) -> None:
        """
        StyleSetForeground(style, fore) -> None
        
        Set the foreground colour of a style.
        """

    def StyleSetBackground(self, style: int, back: Colour) -> None:
        """
        StyleSetBackground(style, back) -> None
        
        Set the background colour of a style.
        """

    def StyleSetBold(self, style: int, bold: bool) -> None:
        """
        StyleSetBold(style, bold) -> None
        
        Set a style to be bold or not.
        """

    def StyleSetItalic(self, style: int, italic: bool) -> None:
        """
        StyleSetItalic(style, italic) -> None
        
        Set a style to be italic or not.
        """

    def StyleSetSize(self, style: int, sizePoints: int) -> None:
        """
        StyleSetSize(style, sizePoints) -> None
        
        Set the size of characters of a style.
        """

    def StyleSetFaceName(self, style: int, fontName: str) -> None:
        """
        StyleSetFaceName(style, fontName) -> None
        
        Set the font of a style.
        """

    def StyleSetEOLFilled(self, style: int, eolFilled: bool) -> None:
        """
        StyleSetEOLFilled(style, eolFilled) -> None
        
        Set a style to have its end of line filled or not.
        """

    def StyleResetDefault(self) -> None:
        """
        StyleResetDefault() -> None
        
        Reset the default style to its state at startup.
        """

    def StyleSetUnderline(self, style: int, underline: bool) -> None:
        """
        StyleSetUnderline(style, underline) -> None
        
        Set a style to be underlined or not.
        """

    def StyleGetForeground(self, style: int) -> Colour:
        """
        StyleGetForeground(style) -> Colour
        
        Get the foreground colour of a style.
        """

    def StyleGetBackground(self, style: int) -> Colour:
        """
        StyleGetBackground(style) -> Colour
        
        Get the background colour of a style.
        """

    def StyleGetBold(self, style: int) -> bool:
        """
        StyleGetBold(style) -> bool
        
        Get is a style bold or not.
        """

    def StyleGetItalic(self, style: int) -> bool:
        """
        StyleGetItalic(style) -> bool
        
        Get is a style italic or not.
        """

    def StyleGetSize(self, style: int) -> int:
        """
        StyleGetSize(style) -> int
        
        Get the size of characters of a style.
        """

    def StyleGetFaceName(self, style: int) -> str:
        """
        StyleGetFaceName(style) -> str
        
        Get the font facename of a style.
        """

    def StyleGetEOLFilled(self, style: int) -> bool:
        """
        StyleGetEOLFilled(style) -> bool
        
        Get is a style to have its end of line filled or not.
        """

    def StyleGetUnderline(self, style: int) -> bool:
        """
        StyleGetUnderline(style) -> bool
        
        Get is a style underlined or not.
        """

    def StyleGetCase(self, style: int) -> int:
        """
        StyleGetCase(style) -> int
        
        Get is a style mixed case, or to force upper or lower case.
        """

    def StyleGetCharacterSet(self, style: int) -> int:
        """
        StyleGetCharacterSet(style) -> int
        
        Get the character get of the font in a style.
        """

    def StyleGetVisible(self, style: int) -> bool:
        """
        StyleGetVisible(style) -> bool
        
        Get is a style visible or not.
        """

    def StyleGetChangeable(self, style: int) -> bool:
        """
        StyleGetChangeable(style) -> bool
        
        Get is a style changeable or not (read only).
        """

    def StyleGetHotSpot(self, style: int) -> bool:
        """
        StyleGetHotSpot(style) -> bool
        
        Get is a style a hotspot or not.
        """

    def StyleSetCase(self, style: int, caseVisible: int) -> None:
        """
        StyleSetCase(style, caseVisible) -> None
        
        Set a style to be mixed case, or to force upper or lower case.
        """

    def StyleSetSizeFractional(self, style: int, sizeHundredthPoints: int) -> None:
        """
        StyleSetSizeFractional(style, sizeHundredthPoints) -> None
        
        Set the size of characters of a style.
        """

    def StyleGetSizeFractional(self, style: int) -> int:
        """
        StyleGetSizeFractional(style) -> int
        
        Get the size of characters of a style in points multiplied by 100.
        """

    def StyleSetWeight(self, style: int, weight: int) -> None:
        """
        StyleSetWeight(style, weight) -> None
        
        Set the weight of characters of a style.
        """

    def StyleGetWeight(self, style: int) -> int:
        """
        StyleGetWeight(style) -> int
        
        Get the weight of characters of a style.
        """

    def StyleSetCharacterSet(self, style: int, characterSet: int) -> None:
        """
        StyleSetCharacterSet(style, characterSet) -> None
        
        Set the character set of the font in a style.
        """

    def StyleSetHotSpot(self, style: int, hotspot: bool) -> None:
        """
        StyleSetHotSpot(style, hotspot) -> None
        
        Set a style to be a hotspot or not.
        """

    def StyleSetVisible(self, style: int, visible: bool) -> None:
        """
        StyleSetVisible(style, visible) -> None
        
        Set a style to be visible or not.
        """

    def StyleSetChangeable(self, style: int, changeable: bool) -> None:
        """
        StyleSetChangeable(style, changeable) -> None
        
        Set a style to be changeable or not (read only).
        """

    def SetSelForeground(self, useSetting: bool, fore: Colour) -> None:
        """
        SetSelForeground(useSetting, fore) -> None
        
        Set the foreground colour of the main and additional selections and
        whether to use this setting.
        """

    def SetSelBackground(self, useSetting: bool, back: Colour) -> None:
        """
        SetSelBackground(useSetting, back) -> None
        
        Set the background colour of the main and additional selections and
        whether to use this setting.
        """

    def GetSelAlpha(self) -> int:
        """
        GetSelAlpha() -> int
        
        Get the alpha of the selection.
        """

    def SetSelAlpha(self, alpha: int) -> None:
        """
        SetSelAlpha(alpha) -> None
        
        Set the alpha of the selection.
        """

    def GetSelEOLFilled(self) -> bool:
        """
        GetSelEOLFilled() -> bool
        
        Is the selection end of line filled?
        """

    def SetSelEOLFilled(self, filled: bool) -> None:
        """
        SetSelEOLFilled(filled) -> None
        
        Set the selection to have its end of line filled or not.
        """

    def SetCaretForeground(self, fore: Colour) -> None:
        """
        SetCaretForeground(fore) -> None
        
        Set the foreground colour of the caret.
        """

    def GetCaretPeriod(self) -> int:
        """
        GetCaretPeriod() -> int
        
        Get the time in milliseconds that the caret is on and off.
        """

    def SetCaretPeriod(self, periodMilliseconds: int) -> None:
        """
        SetCaretPeriod(periodMilliseconds) -> None
        
        Get the time in milliseconds that the caret is on and off.
        """

    def GetCaretLineVisible(self) -> bool:
        """
        GetCaretLineVisible() -> bool
        
        Is the background of the line containing the caret in a different
        colour?
        """

    def SetCaretLineVisible(self, show: bool) -> None:
        """
        SetCaretLineVisible(show) -> None
        
        Display the background of the line containing the caret in a different
        colour.
        """

    def GetCaretLineBackground(self) -> Colour:
        """
        GetCaretLineBackground() -> Colour
        
        Get the colour of the background of the line containing the caret.
        """

    def SetCaretLineBackground(self, back: Colour) -> None:
        """
        SetCaretLineBackground(back) -> None
        
        Set the colour of the background of the line containing the caret.
        """

    def GetCaretForeground(self) -> Colour:
        """
        GetCaretForeground() -> Colour
        
        Get the foreground colour of the caret.
        """

    def SetCaretWidth(self, pixelWidth: int) -> None:
        """
        SetCaretWidth(pixelWidth) -> None
        
        Set the width of the insert mode caret.
        """

    def GetCaretWidth(self) -> int:
        """
        GetCaretWidth() -> int
        
        Returns the width of the insert mode caret.
        """

    def SetHotspotActiveForeground(self, useSetting: bool, fore: Colour) -> None:
        """
        SetHotspotActiveForeground(useSetting, fore) -> None
        
        Set a fore colour for active hotspots.
        """

    def GetHotspotActiveForeground(self) -> Colour:
        """
        GetHotspotActiveForeground() -> Colour
        
        Get the fore colour for active hotspots.
        """

    def SetHotspotActiveBackground(self, useSetting: bool, back: Colour) -> None:
        """
        SetHotspotActiveBackground(useSetting, back) -> None
        
        Set a back colour for active hotspots.
        """

    def GetHotspotActiveBackground(self) -> Colour:
        """
        GetHotspotActiveBackground() -> Colour
        
        Get the back colour for active hotspots.
        """

    def SetHotspotActiveUnderline(self, underline: bool) -> None:
        """
        SetHotspotActiveUnderline(underline) -> None
        
        Enable / Disable underlining active hotspots.
        """

    def GetHotspotActiveUnderline(self) -> bool:
        """
        GetHotspotActiveUnderline() -> bool
        
        Get whether underlining for active hotspots.
        """

    def SetHotspotSingleLine(self, singleLine: bool) -> None:
        """
        SetHotspotSingleLine(singleLine) -> None
        
        Limit hotspots to single line so hotspots on two lines don't merge.
        """

    def GetHotspotSingleLine(self) -> bool:
        """
        GetHotspotSingleLine() -> bool
        
        Get the HotspotSingleLine property.
        """

    def GetCaretSticky(self) -> int:
        """
        GetCaretSticky() -> int
        
        Can the caret preferred x position only be changed by explicit
        movement commands?
        """

    def SetCaretSticky(self, useCaretStickyBehaviour: int) -> None:
        """
        SetCaretSticky(useCaretStickyBehaviour) -> None
        
        Stop the caret preferred x position changing when the user types.
        """

    def ToggleCaretSticky(self) -> None:
        """
        ToggleCaretSticky() -> None
        
        Switch between sticky and non-sticky: meant to be bound to a key.
        """

    def SetCaretLineBackAlpha(self, alpha: int) -> None:
        """
        SetCaretLineBackAlpha(alpha) -> None
        
        Set background alpha of the caret line.
        """

    def GetCaretLineBackAlpha(self) -> int:
        """
        GetCaretLineBackAlpha() -> int
        
        Get the background alpha of the caret line.
        """

    def SetCaretStyle(self, caretStyle: int) -> None:
        """
        SetCaretStyle(caretStyle) -> None
        
        Set the style of the caret to be drawn.
        """

    def GetCaretStyle(self) -> int:
        """
        GetCaretStyle() -> int
        
        Returns the current style of the caret.
        """

    def GetCaretLineVisibleAlways(self) -> bool:
        """
        GetCaretLineVisibleAlways() -> bool
        
        Is the caret line always visible?
        """

    def SetCaretLineVisibleAlways(self, alwaysVisible: bool) -> None:
        """
        SetCaretLineVisibleAlways(alwaysVisible) -> None
        
        Sets the caret line to always visible.
        """

    def SetControlCharSymbol(self, symbol: int) -> None:
        """
        SetControlCharSymbol(symbol) -> None
        
        Change the way control characters are displayed: If symbol is < 32,
        keep the drawn way, else, use the given character.
        """

    def GetControlCharSymbol(self) -> int:
        """
        GetControlCharSymbol() -> int
        
        Get the way control characters are displayed.
        """

    def SetRepresentation(self, encodedCharacter: str, representation: str) -> None:
        """
        SetRepresentation(encodedCharacter, representation) -> None
        
        Set the way a character is drawn.
        """

    def GetRepresentation(self, encodedCharacter: str) -> str:
        """
        GetRepresentation(encodedCharacter) -> str
        
        Set the way a character is drawn.
        """

    def ClearRepresentation(self, encodedCharacter: str) -> None:
        """
        ClearRepresentation(encodedCharacter) -> None
        
        Remove a character representation.
        """

    def SetMarginType(self, margin: int, marginType: int) -> None:
        """
        SetMarginType(margin, marginType) -> None
        
        Set a margin to be either numeric or symbolic.
        """

    def GetMarginType(self, margin: int) -> int:
        """
        GetMarginType(margin) -> int
        
        Retrieve the type of a margin.
        """

    def SetMarginWidth(self, margin: int, pixelWidth: int) -> None:
        """
        SetMarginWidth(margin, pixelWidth) -> None
        
        Set the width of a margin to a width expressed in pixels.
        """

    def GetMarginWidth(self, margin: int) -> int:
        """
        GetMarginWidth(margin) -> int
        
        Retrieve the width of a margin in pixels.
        """

    def SetMarginMask(self, margin: int, mask: int) -> None:
        """
        SetMarginMask(margin, mask) -> None
        
        Set a mask that determines which markers are displayed in a margin.
        """

    def GetMarginMask(self, margin: int) -> int:
        """
        GetMarginMask(margin) -> int
        
        Retrieve the marker mask of a margin.
        """

    def SetMarginSensitive(self, margin: int, sensitive: bool) -> None:
        """
        SetMarginSensitive(margin, sensitive) -> None
        
        Make a margin sensitive or insensitive to mouse clicks.
        """

    def GetMarginSensitive(self, margin: int) -> bool:
        """
        GetMarginSensitive(margin) -> bool
        
        Retrieve the mouse click sensitivity of a margin.
        """

    def SetMarginCursor(self, margin: int, cursor: int) -> None:
        """
        SetMarginCursor(margin, cursor) -> None
        
        Set the cursor shown when the mouse is inside a margin.
        """

    def GetMarginCursor(self, margin: int) -> int:
        """
        GetMarginCursor(margin) -> int
        
        Retrieve the cursor shown in a margin.
        """

    def SetMarginBackground(self, margin: int, back: Colour) -> None:
        """
        SetMarginBackground(margin, back) -> None
        
        Set the background colour of a margin.
        """

    def GetMarginBackground(self, margin: int) -> Colour:
        """
        GetMarginBackground(margin) -> Colour
        
        Retrieve the background colour of a margin.
        """

    def SetMarginCount(self, margins: int) -> None:
        """
        SetMarginCount(margins) -> None
        
        Allocate a non-standard number of margins.
        """

    def GetMarginCount(self) -> int:
        """
        GetMarginCount() -> int
        
        How many margins are there?.
        """

    def SetMarginLeft(self, pixelWidth: int) -> None:
        """
        SetMarginLeft(pixelWidth) -> None
        
        Sets the size in pixels of the left margin.
        """

    def GetMarginLeft(self) -> int:
        """
        GetMarginLeft() -> int
        
        Returns the size in pixels of the left margin.
        """

    def SetMarginRight(self, pixelWidth: int) -> None:
        """
        SetMarginRight(pixelWidth) -> None
        
        Sets the size in pixels of the right margin.
        """

    def GetMarginRight(self) -> int:
        """
        GetMarginRight() -> int
        
        Returns the size in pixels of the right margin.
        """

    def SetFoldMarginColour(self, useSetting: bool, back: Colour) -> None:
        """
        SetFoldMarginColour(useSetting, back) -> None
        
        Set one of the colours used as a chequerboard pattern in the fold
        margin.
        """

    def SetFoldMarginHiColour(self, useSetting: bool, fore: Colour) -> None:
        """
        SetFoldMarginHiColour(useSetting, fore) -> None
        
        Set the other colour used as a chequerboard pattern in the fold
        margin.
        """

    def MarginSetText(self, line: int, text: str) -> None:
        """
        MarginSetText(line, text) -> None
        
        Set the text in the text margin for a line.
        """

    def MarginGetText(self, line: int) -> str:
        """
        MarginGetText(line) -> str
        
        Get the text in the text margin for a line.
        """

    def MarginSetStyle(self, line: int, style: int) -> None:
        """
        MarginSetStyle(line, style) -> None
        
        Set the style number for the text margin for a line.
        """

    def MarginGetStyle(self, line: int) -> int:
        """
        MarginGetStyle(line) -> int
        
        Get the style number for the text margin for a line.
        """

    def MarginSetStyles(self, line: int, styles: str) -> None:
        """
        MarginSetStyles(line, styles) -> None
        
        Set the style in the text margin for a line.
        """

    def MarginGetStyles(self, line: int) -> str:
        """
        MarginGetStyles(line) -> str
        
        Get the styles in the text margin for a line.
        """

    def MarginTextClearAll(self) -> None:
        """
        MarginTextClearAll() -> None
        
        Clear the margin text on all lines.
        """

    def MarginSetStyleOffset(self, style: int) -> None:
        """
        MarginSetStyleOffset(style) -> None
        
        Get the start of the range of style numbers used for margin text.
        """

    def MarginGetStyleOffset(self) -> int:
        """
        MarginGetStyleOffset() -> int
        
        Get the start of the range of style numbers used for margin text.
        """

    def SetMarginOptions(self, marginOptions: int) -> None:
        """
        SetMarginOptions(marginOptions) -> None
        
        Set the margin options.
        """

    def GetMarginOptions(self) -> int:
        """
        GetMarginOptions() -> int
        
        Get the margin options.
        """

    def AnnotationSetText(self, line: int, text: str) -> None:
        """
        AnnotationSetText(line, text) -> None
        
        Set the annotation text for a line.
        """

    def AnnotationGetText(self, line: int) -> str:
        """
        AnnotationGetText(line) -> str
        
        Get the annotation text for a line.
        """

    def AnnotationSetStyle(self, line: int, style: int) -> None:
        """
        AnnotationSetStyle(line, style) -> None
        
        Set the style number for the annotations for a line.
        """

    def AnnotationGetStyle(self, line: int) -> int:
        """
        AnnotationGetStyle(line) -> int
        
        Get the style number for the annotations for a line.
        """

    def AnnotationSetStyles(self, line: int, styles: str) -> None:
        """
        AnnotationSetStyles(line, styles) -> None
        
        Set the annotation styles for a line.
        """

    def AnnotationGetStyles(self, line: int) -> str:
        """
        AnnotationGetStyles(line) -> str
        
        Get the annotation styles for a line.
        """

    def AnnotationGetLines(self, line: int) -> int:
        """
        AnnotationGetLines(line) -> int
        
        Get the number of annotation lines for a line.
        """

    def AnnotationClearAll(self) -> None:
        """
        AnnotationClearAll() -> None
        
        Clear the annotations from all lines.
        """

    def AnnotationSetVisible(self, visible: int) -> None:
        """
        AnnotationSetVisible(visible) -> None
        
        Set the visibility for the annotations for a view.
        """

    def AnnotationGetVisible(self) -> int:
        """
        AnnotationGetVisible() -> int
        
        Get the visibility for the annotations for a view.
        """

    def AnnotationSetStyleOffset(self, style: int) -> None:
        """
        AnnotationSetStyleOffset(style) -> None
        
        Get the start of the range of style numbers used for annotations.
        """

    def AnnotationGetStyleOffset(self) -> int:
        """
        AnnotationGetStyleOffset() -> int
        
        Get the start of the range of style numbers used for annotations.
        """

    def GetBufferedDraw(self) -> bool:
        """
        GetBufferedDraw() -> bool
        
        Is drawing done first into a buffer or direct to the screen?
        """

    def SetBufferedDraw(self, buffered: bool) -> None:
        """
        SetBufferedDraw(buffered) -> None
        
        If drawing is buffered then each line of text is drawn into a bitmap
        buffer before drawing it to the screen to avoid flicker.
        """

    def SetCodePage(self, codePage: int) -> None:
        """
        SetCodePage(codePage) -> None
        
        Set the code page used to interpret the bytes of the document as
        characters.
        """

    def GetIMEInteraction(self) -> int:
        """
        GetIMEInteraction() -> int
        
        Is the IME displayed in a window or inline?
        """

    def SetIMEInteraction(self, imeInteraction: int) -> None:
        """
        SetIMEInteraction(imeInteraction) -> None
        
        Choose to display the IME in a winow or inline.
        """

    def GetCodePage(self) -> int:
        """
        GetCodePage() -> int
        
        Get the code page used to interpret the bytes of the document as
        characters.
        """

    def SetLayoutCache(self, cacheMode: int) -> None:
        """
        SetLayoutCache(cacheMode) -> None
        
        Sets the degree of caching of layout information.
        """

    def GetTwoPhaseDraw(self) -> bool:
        """
        GetTwoPhaseDraw() -> bool
        
        Is drawing done in two phases with backgrounds drawn before
        foregrounds?
        """

    def SetTwoPhaseDraw(self, twoPhase: bool) -> None:
        """
        SetTwoPhaseDraw(twoPhase) -> None
        
        In twoPhaseDraw mode, drawing is performed in two phases, first the
        background and then the foreground.
        """

    def GetPhasesDraw(self) -> int:
        """
        GetPhasesDraw() -> int
        
        How many phases is drawing done in?
        """

    def SetPhasesDraw(self, phases: int) -> None:
        """
        SetPhasesDraw(phases) -> None
        
        In one phase draw, text is drawn in a series of rectangular blocks
        with no overlap.
        """

    def SetFontQuality(self, fontQuality: int) -> None:
        """
        SetFontQuality(fontQuality) -> None
        
        Choose the quality level for text.
        """

    def GetFontQuality(self) -> int:
        """
        GetFontQuality() -> int
        
        Retrieve the quality level for text.
        """

    def SetSTCFocus(self, focus: bool) -> None:
        """
        SetSTCFocus(focus) -> None
        
        Change internal focus flag.
        """

    def GetSTCFocus(self) -> bool:
        """
        GetSTCFocus() -> bool
        
        Get internal focus flag.
        """

    def SetTechnology(self, technology: int) -> None:
        """
        SetTechnology(technology) -> None
        
        Set the technology used.
        """

    def GetTechnology(self) -> int:
        """
        GetTechnology() -> int
        
        Get the tech.
        """

    def BraceHighlight(self, posA: int, posB: int) -> None:
        """
        BraceHighlight(posA, posB) -> None
        
        Highlight the characters at two positions.
        """

    def BraceHighlightIndicator(self, useSetting: bool, indicator: int) -> None:
        """
        BraceHighlightIndicator(useSetting, indicator) -> None
        
        Use specified indicator to highlight matching braces instead of
        changing their style.
        """

    def BraceBadLight(self, pos: int) -> None:
        """
        BraceBadLight(pos) -> None
        
        Highlight the character at a position indicating there is no matching
        brace.
        """

    def BraceBadLightIndicator(self, useSetting: bool, indicator: int) -> None:
        """
        BraceBadLightIndicator(useSetting, indicator) -> None
        
        Use specified indicator to highlight non matching brace instead of
        changing its style.
        """

    def BraceMatch(self, pos: int, maxReStyle: int=0) -> int:
        """
        BraceMatch(pos, maxReStyle=0) -> int
        
        Find the position of a matching brace or wxSTC_INVALID_POSITION if no
        match.
        """

    def SetTabWidth(self, tabWidth: int) -> None:
        """
        SetTabWidth(tabWidth) -> None
        
        Change the visible size of a tab to be a multiple of the width of a
        space character.
        """

    def GetTabWidth(self) -> int:
        """
        GetTabWidth() -> int
        
        Retrieve the visible size of a tab.
        """

    def ClearTabStops(self, line: int) -> None:
        """
        ClearTabStops(line) -> None
        
        Clear explicit tabstops on a line.
        """

    def AddTabStop(self, line: int, x: int) -> None:
        """
        AddTabStop(line, x) -> None
        
        Add an explicit tab stop for a line.
        """

    def GetNextTabStop(self, line: int, x: int) -> int:
        """
        GetNextTabStop(line, x) -> int
        
        Find the next explicit tab stop position on a line after a position.
        """

    def SetIndent(self, indentSize: int) -> None:
        """
        SetIndent(indentSize) -> None
        
        Set the number of spaces used for one level of indentation.
        """

    def GetIndent(self) -> int:
        """
        GetIndent() -> int
        
        Retrieve indentation size.
        """

    def SetUseTabs(self, useTabs: bool) -> None:
        """
        SetUseTabs(useTabs) -> None
        
        Indentation will only use space characters if useTabs is false,
        otherwise it will use a combination of tabs and spaces.
        """

    def GetUseTabs(self) -> bool:
        """
        GetUseTabs() -> bool
        
        Retrieve whether tabs will be used in indentation.
        """

    def SetLineIndentation(self, line: int, indentation: int) -> None:
        """
        SetLineIndentation(line, indentation) -> None
        
        Change the indentation of a line to a number of columns.
        """

    def GetLineIndentation(self, line: int) -> int:
        """
        GetLineIndentation(line) -> int
        
        Retrieve the number of columns that a line is indented.
        """

    def GetLineIndentPosition(self, line: int) -> int:
        """
        GetLineIndentPosition(line) -> int
        
        Retrieve the position before the first non indentation character on a
        line.
        """

    def SetIndentationGuides(self, indentView: int) -> None:
        """
        SetIndentationGuides(indentView) -> None
        
        Show or hide indentation guides.
        """

    def GetIndentationGuides(self) -> int:
        """
        GetIndentationGuides() -> int
        
        Are the indentation guides visible?
        """

    def SetHighlightGuide(self, column: int) -> None:
        """
        SetHighlightGuide(column) -> None
        
        Set the highlighted indentation guide column.
        """

    def GetHighlightGuide(self) -> int:
        """
        GetHighlightGuide() -> int
        
        Get the highlighted indentation guide column.
        """

    def SetTabIndents(self, tabIndents: bool) -> None:
        """
        SetTabIndents(tabIndents) -> None
        
        Sets whether a tab pressed when caret is within indentation indents.
        """

    def GetTabIndents(self) -> bool:
        """
        GetTabIndents() -> bool
        
        Does a tab pressed when caret is within indentation indent?
        """

    def SetBackSpaceUnIndents(self, bsUnIndents: bool) -> None:
        """
        SetBackSpaceUnIndents(bsUnIndents) -> None
        
        Sets whether a backspace pressed when caret is within indentation
        unindents.
        """

    def GetBackSpaceUnIndents(self) -> bool:
        """
        GetBackSpaceUnIndents() -> bool
        
        Does a backspace pressed when caret is within indentation unindent?
        """

    def MarkerLineFromHandle(self, markerHandle: int) -> int:
        """
        MarkerLineFromHandle(markerHandle) -> int
        
        Retrieve the line number at which a particular marker is located.
        """

    def MarkerDeleteHandle(self, markerHandle: int) -> None:
        """
        MarkerDeleteHandle(markerHandle) -> None
        
        Delete a marker.
        """

    def MarkerDefine(self, markerNumber: int, markerSymbol: int, foreground: Colour=NullColour, background: Colour=NullColour) -> None:
        """
        MarkerDefine(markerNumber, markerSymbol, foreground=NullColour, background=NullColour) -> None
        
        Set the symbol used for a particular marker number, and optionally the
        fore and background colours.
        """

    def MarkerSetForeground(self, markerNumber: int, fore: Colour) -> None:
        """
        MarkerSetForeground(markerNumber, fore) -> None
        
        Set the foreground colour used for a particular marker number.
        """

    def MarkerSetBackground(self, markerNumber: int, back: Colour) -> None:
        """
        MarkerSetBackground(markerNumber, back) -> None
        
        Set the background colour used for a particular marker number.
        """

    def MarkerSetBackgroundSelected(self, markerNumber: int, back: Colour) -> None:
        """
        MarkerSetBackgroundSelected(markerNumber, back) -> None
        
        Set the background colour used for a particular marker number when its
        folding block is selected.
        """

    def MarkerEnableHighlight(self, enabled: bool) -> None:
        """
        MarkerEnableHighlight(enabled) -> None
        
        Enable/disable highlight for current folding block (smallest one that
        contains the caret)
        """

    def MarkerAdd(self, line: int, markerNumber: int) -> int:
        """
        MarkerAdd(line, markerNumber) -> int
        
        Add a marker to a line, returning an ID which can be used to find or
        delete the marker.
        """

    def MarkerDelete(self, line: int, markerNumber: int) -> None:
        """
        MarkerDelete(line, markerNumber) -> None
        
        Delete a marker from a line.
        """

    def MarkerDeleteAll(self, markerNumber: int) -> None:
        """
        MarkerDeleteAll(markerNumber) -> None
        
        Delete all markers with a particular number from all lines.
        """

    def MarkerGet(self, line: int) -> int:
        """
        MarkerGet(line) -> int
        
        Get a bit mask of all the markers set on a line.
        """

    def MarkerNext(self, lineStart: int, markerMask: int) -> int:
        """
        MarkerNext(lineStart, markerMask) -> int
        
        Find the next line at or after lineStart that includes a marker in
        mask.
        """

    def MarkerPrevious(self, lineStart: int, markerMask: int) -> int:
        """
        MarkerPrevious(lineStart, markerMask) -> int
        
        Find the previous line before lineStart that includes a marker in
        mask.
        """

    def MarkerAddSet(self, line: int, markerSet: int) -> None:
        """
        MarkerAddSet(line, markerSet) -> None
        
        Add a set of markers to a line.
        """

    def MarkerSetAlpha(self, markerNumber: int, alpha: int) -> None:
        """
        MarkerSetAlpha(markerNumber, alpha) -> None
        
        Set the alpha used for a marker that is drawn in the text area, not
        the margin.
        """

    def GetMarkerSymbolDefined(self, markerNumber: int) -> int:
        """
        GetMarkerSymbolDefined(markerNumber) -> int
        
        Which symbol was defined for markerNumber with MarkerDefine.
        """

    def RGBAImageSetWidth(self, width: int) -> None:
        """
        RGBAImageSetWidth(width) -> None
        
        Set the width for future RGBA image data.
        """

    def RGBAImageSetHeight(self, height: int) -> None:
        """
        RGBAImageSetHeight(height) -> None
        
        Set the height for future RGBA image data.
        """

    def RGBAImageSetScale(self, scalePercent: int) -> None:
        """
        RGBAImageSetScale(scalePercent) -> None
        
        Set the scale factor in percent for future RGBA image data.
        """

    def MarkerDefineRGBAImage(self, markerNumber: int, pixels: PyBuffer) -> None:
        """
        MarkerDefineRGBAImage(markerNumber, pixels) -> None
        
        Define a marker from RGBA data.
        
        It has the width and height from RGBAImageSetWidth/Height. You must
        ensure that the buffer is at least width*height*4 bytes long.
        """

    def IndicatorSetStyle(self, indicator: int, indicatorStyle: int) -> None:
        """
        IndicatorSetStyle(indicator, indicatorStyle) -> None
        
        Set an indicator to plain, squiggle or TT.
        """

    def IndicatorGetStyle(self, indicator: int) -> int:
        """
        IndicatorGetStyle(indicator) -> int
        
        Retrieve the style of an indicator.
        """

    def IndicatorSetForeground(self, indicator: int, fore: Colour) -> None:
        """
        IndicatorSetForeground(indicator, fore) -> None
        
        Set the foreground colour of an indicator.
        """

    def IndicatorGetForeground(self, indicator: int) -> Colour:
        """
        IndicatorGetForeground(indicator) -> Colour
        
        Retrieve the foreground colour of an indicator.
        """

    def IndicatorSetUnder(self, indicator: int, under: bool) -> None:
        """
        IndicatorSetUnder(indicator, under) -> None
        
        Set an indicator to draw under text or over(default).
        """

    def IndicatorGetUnder(self, indicator: int) -> bool:
        """
        IndicatorGetUnder(indicator) -> bool
        
        Retrieve whether indicator drawn under or over text.
        """

    def IndicatorSetHoverStyle(self, indicator: int, indicatorStyle: int) -> None:
        """
        IndicatorSetHoverStyle(indicator, indicatorStyle) -> None
        
        Set a hover indicator to plain, squiggle or TT.
        """

    def IndicatorGetHoverStyle(self, indicator: int) -> int:
        """
        IndicatorGetHoverStyle(indicator) -> int
        
        Retrieve the hover style of an indicator.
        """

    def IndicatorSetHoverForeground(self, indicator: int, fore: Colour) -> None:
        """
        IndicatorSetHoverForeground(indicator, fore) -> None
        
        Set the foreground hover colour of an indicator.
        """

    def IndicatorGetHoverForeground(self, indicator: int) -> Colour:
        """
        IndicatorGetHoverForeground(indicator) -> Colour
        
        Retrieve the foreground hover colour of an indicator.
        """

    def IndicatorSetFlags(self, indicator: int, flags: int) -> None:
        """
        IndicatorSetFlags(indicator, flags) -> None
        
        Set the attributes of an indicator.
        """

    def IndicatorGetFlags(self, indicator: int) -> int:
        """
        IndicatorGetFlags(indicator) -> int
        
        Retrieve the attributes of an indicator.
        """

    def SetIndicatorCurrent(self, indicator: int) -> None:
        """
        SetIndicatorCurrent(indicator) -> None
        
        Set the indicator used for IndicatorFillRange and IndicatorClearRange.
        """

    def GetIndicatorCurrent(self) -> int:
        """
        GetIndicatorCurrent() -> int
        
        Get the current indicator.
        """

    def SetIndicatorValue(self, value: int) -> None:
        """
        SetIndicatorValue(value) -> None
        
        Set the value used for IndicatorFillRange.
        """

    def GetIndicatorValue(self) -> int:
        """
        GetIndicatorValue() -> int
        
        Get the current indicator value.
        """

    def IndicatorFillRange(self, start: int, lengthFill: int) -> None:
        """
        IndicatorFillRange(start, lengthFill) -> None
        
        Turn an indicator on over a range.
        """

    def IndicatorClearRange(self, start: int, lengthClear: int) -> None:
        """
        IndicatorClearRange(start, lengthClear) -> None
        
        Turn an indicator off over a range.
        """

    def IndicatorAllOnFor(self, pos: int) -> int:
        """
        IndicatorAllOnFor(pos) -> int
        
        Are any indicators present at pos?
        """

    def IndicatorValueAt(self, indicator: int, pos: int) -> int:
        """
        IndicatorValueAt(indicator, pos) -> int
        
        What value does a particular indicator have at a position?
        """

    def IndicatorStart(self, indicator: int, pos: int) -> int:
        """
        IndicatorStart(indicator, pos) -> int
        
        Where does a particular indicator start?
        """

    def IndicatorEnd(self, indicator: int, pos: int) -> int:
        """
        IndicatorEnd(indicator, pos) -> int
        
        Where does a particular indicator end?
        """

    def IndicatorSetAlpha(self, indicator: int, alpha: int) -> None:
        """
        IndicatorSetAlpha(indicator, alpha) -> None
        
        Set the alpha fill colour of the given indicator.
        """

    def IndicatorGetAlpha(self, indicator: int) -> int:
        """
        IndicatorGetAlpha(indicator) -> int
        
        Get the alpha fill colour of the given indicator.
        """

    def IndicatorSetOutlineAlpha(self, indicator: int, alpha: int) -> None:
        """
        IndicatorSetOutlineAlpha(indicator, alpha) -> None
        
        Set the alpha outline colour of the given indicator.
        """

    def IndicatorGetOutlineAlpha(self, indicator: int) -> int:
        """
        IndicatorGetOutlineAlpha(indicator) -> int
        
        Get the alpha outline colour of the given indicator.
        """

    def AutoCompShow(self, lengthEntered: int, itemList: str) -> None:
        """
        AutoCompShow(lengthEntered, itemList) -> None
        
        Display an auto-completion list.
        """

    def AutoCompCancel(self) -> None:
        """
        AutoCompCancel() -> None
        
        Remove the auto-completion list from the screen.
        """

    def AutoCompActive(self) -> bool:
        """
        AutoCompActive() -> bool
        
        Is there an auto-completion list visible?
        """

    def AutoCompPosStart(self) -> int:
        """
        AutoCompPosStart() -> int
        
        Retrieve the position of the caret when the auto-completion list was
        displayed.
        """

    def AutoCompComplete(self) -> None:
        """
        AutoCompComplete() -> None
        
        User has selected an item so remove the list and insert the selection.
        """

    def AutoCompStops(self, characterSet: str) -> None:
        """
        AutoCompStops(characterSet) -> None
        
        Define a set of character that when typed cancel the auto-completion
        list.
        """

    def AutoCompSetSeparator(self, separatorCharacter: int) -> None:
        """
        AutoCompSetSeparator(separatorCharacter) -> None
        
        Change the separator character in the string setting up an auto-
        completion list.
        """

    def AutoCompGetSeparator(self) -> int:
        """
        AutoCompGetSeparator() -> int
        
        Retrieve the auto-completion list separator character.
        """

    def AutoCompSelect(self, select: str) -> None:
        """
        AutoCompSelect(select) -> None
        
        Select the item in the auto-completion list that starts with a string.
        """

    def AutoCompSetCancelAtStart(self, cancel: bool) -> None:
        """
        AutoCompSetCancelAtStart(cancel) -> None
        
        Should the auto-completion list be cancelled if the user backspaces to
        a position before where the box was created.
        """

    def AutoCompGetCancelAtStart(self) -> bool:
        """
        AutoCompGetCancelAtStart() -> bool
        
        Retrieve whether auto-completion cancelled by backspacing before
        start.
        """

    def AutoCompSetFillUps(self, characterSet: str) -> None:
        """
        AutoCompSetFillUps(characterSet) -> None
        
        Define a set of characters that when typed will cause the
        autocompletion to choose the selected item.
        """

    def AutoCompSetChooseSingle(self, chooseSingle: bool) -> None:
        """
        AutoCompSetChooseSingle(chooseSingle) -> None
        
        Should a single item auto-completion list automatically choose the
        item.
        """

    def AutoCompGetChooseSingle(self) -> bool:
        """
        AutoCompGetChooseSingle() -> bool
        
        Retrieve whether a single item auto-completion list automatically
        choose the item.
        """

    def AutoCompSetIgnoreCase(self, ignoreCase: bool) -> None:
        """
        AutoCompSetIgnoreCase(ignoreCase) -> None
        
        Set whether case is significant when performing auto-completion
        searches.
        """

    def AutoCompGetIgnoreCase(self) -> bool:
        """
        AutoCompGetIgnoreCase() -> bool
        
        Retrieve state of ignore case flag.
        """

    def AutoCompSetAutoHide(self, autoHide: bool) -> None:
        """
        AutoCompSetAutoHide(autoHide) -> None
        
        Set whether or not autocompletion is hidden automatically when nothing
        matches.
        """

    def AutoCompGetAutoHide(self) -> bool:
        """
        AutoCompGetAutoHide() -> bool
        
        Retrieve whether or not autocompletion is hidden automatically when
        nothing matches.
        """

    def AutoCompSetDropRestOfWord(self, dropRestOfWord: bool) -> None:
        """
        AutoCompSetDropRestOfWord(dropRestOfWord) -> None
        
        Set whether or not autocompletion deletes any word characters after
        the inserted text upon completion.
        """

    def AutoCompGetDropRestOfWord(self) -> bool:
        """
        AutoCompGetDropRestOfWord() -> bool
        
        Retrieve whether or not autocompletion deletes any word characters
        after the inserted text upon completion.
        """

    def RegisterImage(self, type: int, bmp: Bitmap) -> None:
        """
        RegisterImage(type, bmp) -> None
        
        Register an image for use in autocompletion lists.
        """

    def ClearRegisteredImages(self) -> None:
        """
        ClearRegisteredImages() -> None
        
        Clear all the registered images.
        """

    def AutoCompGetTypeSeparator(self) -> int:
        """
        AutoCompGetTypeSeparator() -> int
        
        Retrieve the auto-completion list type-separator character.
        """

    def AutoCompSetTypeSeparator(self, separatorCharacter: int) -> None:
        """
        AutoCompSetTypeSeparator(separatorCharacter) -> None
        
        Change the type-separator character in the string setting up an auto-
        completion list.
        """

    def AutoCompSetMaxWidth(self, characterCount: int) -> None:
        """
        AutoCompSetMaxWidth(characterCount) -> None
        
        Set the maximum width, in characters, of auto-completion and user
        lists.
        """

    def AutoCompGetMaxWidth(self) -> int:
        """
        AutoCompGetMaxWidth() -> int
        
        Get the maximum width, in characters, of auto-completion and user
        lists.
        """

    def AutoCompSetMaxHeight(self, rowCount: int) -> None:
        """
        AutoCompSetMaxHeight(rowCount) -> None
        
        Set the maximum height, in rows, of auto-completion and user lists.
        """

    def AutoCompGetMaxHeight(self) -> int:
        """
        AutoCompGetMaxHeight() -> int
        
        Set the maximum height, in rows, of auto-completion and user lists.
        """

    def AutoCompGetCurrent(self) -> int:
        """
        AutoCompGetCurrent() -> int
        
        Get currently selected item position in the auto-completion list.
        """

    def AutoCompGetCurrentText(self) -> str:
        """
        AutoCompGetCurrentText() -> str
        
        Get currently selected item text in the auto-completion list.
        """

    def AutoCompSetCaseInsensitiveBehaviour(self, behaviour: int) -> None:
        """
        AutoCompSetCaseInsensitiveBehaviour(behaviour) -> None
        
        Set auto-completion case insensitive behaviour to either prefer case-
        sensitive matches or have no preference.
        """

    def AutoCompGetCaseInsensitiveBehaviour(self) -> int:
        """
        AutoCompGetCaseInsensitiveBehaviour() -> int
        
        Get auto-completion case insensitive behaviour.
        """

    def AutoCompSetMulti(self, multi: int) -> None:
        """
        AutoCompSetMulti(multi) -> None
        
        Change the effect of autocompleting when there are multiple
        selections.
        """

    def AutoCompGetMulti(self) -> int:
        """
        AutoCompGetMulti() -> int
        
        Retrieve the effect of autocompleting when there are multiple
        selections.
        """

    def AutoCompSetOrder(self, order: int) -> None:
        """
        AutoCompSetOrder(order) -> None
        
        Set the way autocompletion lists are ordered.
        """

    def AutoCompGetOrder(self) -> int:
        """
        AutoCompGetOrder() -> int
        
        Get the way autocompletion lists are ordered.
        """

    def RegisterRGBAImage(self, type: int, pixels: PyBuffer) -> None:
        """
        RegisterRGBAImage(type, pixels) -> None
        
        Register an RGBA image for use in autocompletion lists.
        
        It has the width and height from RGBAImageSetWidth/Height. You must
        ensure that the buffer is at least width*height*4 bytes long.
        """

    def UserListShow(self, listType: int, itemList: str) -> None:
        """
        UserListShow(listType, itemList) -> None
        
        Display a list of strings and send notification when user chooses one.
        """

    def CallTipShow(self, pos: int, definition: str) -> None:
        """
        CallTipShow(pos, definition) -> None
        
        Show a call tip containing a definition near position pos.
        """

    def CallTipCancel(self) -> None:
        """
        CallTipCancel() -> None
        
        Remove the call tip from the screen.
        """

    def CallTipActive(self) -> bool:
        """
        CallTipActive() -> bool
        
        Is there an active call tip?
        """

    def CallTipPosAtStart(self) -> int:
        """
        CallTipPosAtStart() -> int
        
        Retrieve the position where the caret was before displaying the call
        tip.
        """

    def CallTipSetPosAtStart(self, posStart: int) -> None:
        """
        CallTipSetPosAtStart(posStart) -> None
        
        Set the start position in order to change when backspacing removes the
        calltip.
        """

    def CallTipSetHighlight(self, highlightStart: int, highlightEnd: int) -> None:
        """
        CallTipSetHighlight(highlightStart, highlightEnd) -> None
        
        Highlight a segment of the definition.
        """

    def CallTipSetBackground(self, back: Colour) -> None:
        """
        CallTipSetBackground(back) -> None
        
        Set the background colour for the call tip.
        """

    def CallTipSetForeground(self, fore: Colour) -> None:
        """
        CallTipSetForeground(fore) -> None
        
        Set the foreground colour for the call tip.
        """

    def CallTipSetForegroundHighlight(self, fore: Colour) -> None:
        """
        CallTipSetForegroundHighlight(fore) -> None
        
        Set the foreground colour for the highlighted part of the call tip.
        """

    def CallTipUseStyle(self, tabSize: int) -> None:
        """
        CallTipUseStyle(tabSize) -> None
        
        Enable use of wxSTC_STYLE_CALLTIP and set call tip tab size in pixels.
        """

    def CallTipSetPosition(self, above: bool) -> None:
        """
        CallTipSetPosition(above) -> None
        
        Set position of calltip, above or below text.
        """

    def LineDown(self) -> None:
        """
        LineDown() -> None
        
        Move caret down one line.
        """

    def LineDownExtend(self) -> None:
        """
        LineDownExtend() -> None
        
        Move caret down one line extending selection to new caret position.
        """

    def LineUp(self) -> None:
        """
        LineUp() -> None
        
        Move caret up one line.
        """

    def LineUpExtend(self) -> None:
        """
        LineUpExtend() -> None
        
        Move caret up one line extending selection to new caret position.
        """

    def CharLeft(self) -> None:
        """
        CharLeft() -> None
        
        Move caret left one character.
        """

    def CharLeftExtend(self) -> None:
        """
        CharLeftExtend() -> None
        
        Move caret left one character extending selection to new caret
        position.
        """

    def CharRight(self) -> None:
        """
        CharRight() -> None
        
        Move caret right one character.
        """

    def CharRightExtend(self) -> None:
        """
        CharRightExtend() -> None
        
        Move caret right one character extending selection to new caret
        position.
        """

    def WordLeft(self) -> None:
        """
        WordLeft() -> None
        
        Move caret left one word.
        """

    def WordLeftExtend(self) -> None:
        """
        WordLeftExtend() -> None
        
        Move caret left one word extending selection to new caret position.
        """

    def WordRight(self) -> None:
        """
        WordRight() -> None
        
        Move caret right one word.
        """

    def WordRightExtend(self) -> None:
        """
        WordRightExtend() -> None
        
        Move caret right one word extending selection to new caret position.
        """

    def Home(self) -> None:
        """
        Home() -> None
        
        Move caret to first position on line.
        """

    def HomeExtend(self) -> None:
        """
        HomeExtend() -> None
        
        Move caret to first position on line extending selection to new caret
        position.
        """

    def LineEnd(self) -> None:
        """
        LineEnd() -> None
        
        Move caret to last position on line.
        """

    def LineEndExtend(self) -> None:
        """
        LineEndExtend() -> None
        
        Move caret to last position on line extending selection to new caret
        position.
        """

    def DocumentStart(self) -> None:
        """
        DocumentStart() -> None
        
        Move caret to first position in document.
        """

    def DocumentStartExtend(self) -> None:
        """
        DocumentStartExtend() -> None
        
        Move caret to first position in document extending selection to new
        caret position.
        """

    def DocumentEnd(self) -> None:
        """
        DocumentEnd() -> None
        
        Move caret to last position in document.
        """

    def DocumentEndExtend(self) -> None:
        """
        DocumentEndExtend() -> None
        
        Move caret to last position in document extending selection to new
        caret position.
        """

    def PageUp(self) -> None:
        """
        PageUp() -> None
        
        Move caret one page up.
        """

    def PageUpExtend(self) -> None:
        """
        PageUpExtend() -> None
        
        Move caret one page up extending selection to new caret position.
        """

    def PageDown(self) -> None:
        """
        PageDown() -> None
        
        Move caret one page down.
        """

    def PageDownExtend(self) -> None:
        """
        PageDownExtend() -> None
        
        Move caret one page down extending selection to new caret position.
        """

    def EditToggleOvertype(self) -> None:
        """
        EditToggleOvertype() -> None
        
        Switch from insert to overtype mode or the reverse.
        """

    def Cancel(self) -> None:
        """
        Cancel() -> None
        
        Cancel any modes such as call tip or auto-completion list display.
        """

    def DeleteBack(self) -> None:
        """
        DeleteBack() -> None
        
        Delete the selection or if no selection, the character before the
        caret.
        """

    def Tab(self) -> None:
        """
        Tab() -> None
        
        If selection is empty or all on one line replace the selection with a
        tab character.
        """

    def BackTab(self) -> None:
        """
        BackTab() -> None
        
        Dedent the selected lines.
        """

    def NewLine(self) -> None:
        """
        NewLine() -> None
        
        Insert a new line, may use a CRLF, CR or LF depending on EOL mode.
        """

    def FormFeed(self) -> None:
        """
        FormFeed() -> None
        
        Insert a Form Feed character.
        """

    def VCHome(self) -> None:
        """
        VCHome() -> None
        
        Move caret to before first visible character on line.
        """

    def VCHomeExtend(self) -> None:
        """
        VCHomeExtend() -> None
        
        Like VCHome but extending selection to new caret position.
        """

    def DelWordLeft(self) -> None:
        """
        DelWordLeft() -> None
        
        Delete the word to the left of the caret.
        """

    def DelWordRight(self) -> None:
        """
        DelWordRight() -> None
        
        Delete the word to the right of the caret.
        """

    def DelWordRightEnd(self) -> None:
        """
        DelWordRightEnd() -> None
        
        Delete the word to the right of the caret, but not the trailing non-
        word characters.
        """

    def LineCut(self) -> None:
        """
        LineCut() -> None
        
        Cut the line containing the caret.
        """

    def LineDelete(self) -> None:
        """
        LineDelete() -> None
        
        Delete the line containing the caret.
        """

    def LineTranspose(self) -> None:
        """
        LineTranspose() -> None
        
        Switch the current line with the previous.
        """

    def LineDuplicate(self) -> None:
        """
        LineDuplicate() -> None
        
        Duplicate the current line.
        """

    def LowerCase(self) -> None:
        """
        LowerCase() -> None
        
        Transform the selection to lower case.
        """

    def UpperCase(self) -> None:
        """
        UpperCase() -> None
        
        Transform the selection to upper case.
        """

    def LineScrollDown(self) -> None:
        """
        LineScrollDown() -> None
        
        Scroll the document down, keeping the caret visible.
        """

    def LineScrollUp(self) -> None:
        """
        LineScrollUp() -> None
        
        Scroll the document up, keeping the caret visible.
        """

    def DeleteBackNotLine(self) -> None:
        """
        DeleteBackNotLine() -> None
        
        Delete the selection or if no selection, the character before the
        caret.
        """

    def HomeDisplay(self) -> None:
        """
        HomeDisplay() -> None
        
        Move caret to first position on display line.
        """

    def HomeDisplayExtend(self) -> None:
        """
        HomeDisplayExtend() -> None
        
        Move caret to first position on display line extending selection to
        new caret position.
        """

    def LineEndDisplay(self) -> None:
        """
        LineEndDisplay() -> None
        
        Move caret to last position on display line.
        """

    def LineEndDisplayExtend(self) -> None:
        """
        LineEndDisplayExtend() -> None
        
        Move caret to last position on display line extending selection to new
        caret position.
        """

    def HomeWrap(self) -> None:
        """
        HomeWrap() -> None
        
        Like Home but when word-wrap is enabled goes first to start of display
        line HomeDisplay, then to start of document line Home.
        """

    def HomeWrapExtend(self) -> None:
        """
        HomeWrapExtend() -> None
        
        Like HomeExtend but when word-wrap is enabled extends first to start
        of display line HomeDisplayExtend, then to start of document line
        HomeExtend.
        """

    def LineEndWrap(self) -> None:
        """
        LineEndWrap() -> None
        
        Like LineEnd but when word-wrap is enabled goes first to end of
        display line LineEndDisplay, then to start of document line LineEnd.
        """

    def LineEndWrapExtend(self) -> None:
        """
        LineEndWrapExtend() -> None
        
        Like LineEndExtend but when word-wrap is enabled extends first to end
        of display line LineEndDisplayExtend, then to start of document line
        LineEndExtend.
        """

    def VCHomeWrap(self) -> None:
        """
        VCHomeWrap() -> None
        
        Like VCHome but when word-wrap is enabled goes first to start of
        display line VCHomeDisplay, then behaves like VCHome.
        """

    def VCHomeWrapExtend(self) -> None:
        """
        VCHomeWrapExtend() -> None
        
        Like VCHomeExtend but when word-wrap is enabled extends first to start
        of display line VCHomeDisplayExtend, then behaves like VCHomeExtend.
        """

    def LineCopy(self) -> None:
        """
        LineCopy() -> None
        
        Copy the line containing the caret.
        """

    def WordPartLeft(self) -> None:
        """
        WordPartLeft() -> None
        
        Move to the previous change in capitalisation.
        """

    def WordPartLeftExtend(self) -> None:
        """
        WordPartLeftExtend() -> None
        
        Move to the previous change in capitalisation extending selection to
        new caret position.
        """

    def WordPartRight(self) -> None:
        """
        WordPartRight() -> None
        
        Move to the change next in capitalisation.
        """

    def WordPartRightExtend(self) -> None:
        """
        WordPartRightExtend() -> None
        
        Move to the next change in capitalisation extending selection to new
        caret position.
        """

    def DelLineLeft(self) -> None:
        """
        DelLineLeft() -> None
        
        Delete back from the current position to the start of the line.
        """

    def DelLineRight(self) -> None:
        """
        DelLineRight() -> None
        
        Delete forwards from the current position to the end of the line.
        """

    def ParaDown(self) -> None:
        """
        ParaDown() -> None
        
        Move caret down one paragraph (delimited by empty lines).
        """

    def ParaDownExtend(self) -> None:
        """
        ParaDownExtend() -> None
        
        Extend selection down one paragraph (delimited by empty lines).
        """

    def ParaUp(self) -> None:
        """
        ParaUp() -> None
        
        Move caret up one paragraph (delimited by empty lines).
        """

    def ParaUpExtend(self) -> None:
        """
        ParaUpExtend() -> None
        
        Extend selection up one paragraph (delimited by empty lines).
        """

    def LineDownRectExtend(self) -> None:
        """
        LineDownRectExtend() -> None
        
        Move caret down one line, extending rectangular selection to new caret
        position.
        """

    def LineUpRectExtend(self) -> None:
        """
        LineUpRectExtend() -> None
        
        Move caret up one line, extending rectangular selection to new caret
        position.
        """

    def CharLeftRectExtend(self) -> None:
        """
        CharLeftRectExtend() -> None
        
        Move caret left one character, extending rectangular selection to new
        caret position.
        """

    def CharRightRectExtend(self) -> None:
        """
        CharRightRectExtend() -> None
        
        Move caret right one character, extending rectangular selection to new
        caret position.
        """

    def HomeRectExtend(self) -> None:
        """
        HomeRectExtend() -> None
        
        Move caret to first position on line, extending rectangular selection
        to new caret position.
        """

    def VCHomeRectExtend(self) -> None:
        """
        VCHomeRectExtend() -> None
        
        Move caret to before first visible character on line.
        """

    def LineEndRectExtend(self) -> None:
        """
        LineEndRectExtend() -> None
        
        Move caret to last position on line, extending rectangular selection
        to new caret position.
        """

    def PageUpRectExtend(self) -> None:
        """
        PageUpRectExtend() -> None
        
        Move caret one page up, extending rectangular selection to new caret
        position.
        """

    def PageDownRectExtend(self) -> None:
        """
        PageDownRectExtend() -> None
        
        Move caret one page down, extending rectangular selection to new caret
        position.
        """

    def StutteredPageUp(self) -> None:
        """
        StutteredPageUp() -> None
        
        Move caret to top of page, or one page up if already at top of page.
        """

    def StutteredPageUpExtend(self) -> None:
        """
        StutteredPageUpExtend() -> None
        
        Move caret to top of page, or one page up if already at top of page,
        extending selection to new caret position.
        """

    def StutteredPageDown(self) -> None:
        """
        StutteredPageDown() -> None
        
        Move caret to bottom of page, or one page down if already at bottom of
        page.
        """

    def StutteredPageDownExtend(self) -> None:
        """
        StutteredPageDownExtend() -> None
        
        Move caret to bottom of page, or one page down if already at bottom of
        page, extending selection to new caret position.
        """

    def WordLeftEnd(self) -> None:
        """
        WordLeftEnd() -> None
        
        Move caret left one word, position cursor at end of word.
        """

    def WordLeftEndExtend(self) -> None:
        """
        WordLeftEndExtend() -> None
        
        Move caret left one word, position cursor at end of word, extending
        selection to new caret position.
        """

    def WordRightEnd(self) -> None:
        """
        WordRightEnd() -> None
        
        Move caret right one word, position cursor at end of word.
        """

    def WordRightEndExtend(self) -> None:
        """
        WordRightEndExtend() -> None
        
        Move caret right one word, position cursor at end of word, extending
        selection to new caret position.
        """

    def SelectionDuplicate(self) -> None:
        """
        SelectionDuplicate() -> None
        
        Duplicate the selection.
        """

    def VerticalCentreCaret(self) -> None:
        """
        VerticalCentreCaret() -> None
        
        Centre current line in window.
        """

    def ScrollToStart(self) -> None:
        """
        ScrollToStart() -> None
        
        Scroll to start of document.
        """

    def ScrollToEnd(self) -> None:
        """
        ScrollToEnd() -> None
        
        Scroll to end of document.
        """

    def VCHomeDisplay(self) -> None:
        """
        VCHomeDisplay() -> None
        
        Move caret to before first visible character on display line.
        """

    def VCHomeDisplayExtend(self) -> None:
        """
        VCHomeDisplayExtend() -> None
        
        Like VCHomeDisplay but extending selection to new caret position.
        """

    def CmdKeyAssign(self, key: int, modifiers: int, cmd: int) -> None:
        """
        CmdKeyAssign(key, modifiers, cmd) -> None
        
        When key+modifier combination keyDefinition is pressed perform
        sciCommand.
        """

    def CmdKeyClear(self, key: int, modifiers: int) -> None:
        """
        CmdKeyClear(key, modifiers) -> None
        
        When key+modifier combination keyDefinition is pressed do nothing.
        """

    def CmdKeyClearAll(self) -> None:
        """
        CmdKeyClearAll() -> None
        
        Drop all key mappings.
        """

    def UsePopUp(self, popUpMode: int) -> None:
        """
        UsePopUp(popUpMode) -> None
        
        Set whether a pop up menu is displayed automatically when the user
        presses the wrong mouse button on certain areas.
        """

    def StartRecord(self) -> None:
        """
        StartRecord() -> None
        
        Start notifying the container of all key presses and commands.
        """

    def StopRecord(self) -> None:
        """
        StopRecord() -> None
        
        Stop notifying the container of all key presses and commands.
        """

    def SetPrintMagnification(self, magnification: int) -> None:
        """
        SetPrintMagnification(magnification) -> None
        
        Sets the print magnification added to the point size of each style for
        printing.
        """

    def GetPrintMagnification(self) -> int:
        """
        GetPrintMagnification() -> int
        
        Returns the print magnification.
        """

    def SetPrintColourMode(self, mode: int) -> None:
        """
        SetPrintColourMode(mode) -> None
        
        Modify colours when printing for clearer printed text.
        """

    def GetPrintColourMode(self) -> int:
        """
        GetPrintColourMode() -> int
        
        Returns the print colour mode.
        """

    def FormatRange(self, doDraw: bool, startPos: int, endPos: int, draw: DC, target: DC, renderRect: Rect, pageRect: Rect) -> int:
        """
        FormatRange(doDraw, startPos, endPos, draw, target, renderRect, pageRect) -> int
        
        On Windows, will draw the document into a display context such as a
        printer.
        """

    def SetPrintWrapMode(self, wrapMode: int) -> None:
        """
        SetPrintWrapMode(wrapMode) -> None
        
        Set printing to line wrapped (wxSTC_WRAP_WORD) or not line wrapped
        (wxSTC_WRAP_NONE).
        """

    def GetPrintWrapMode(self) -> int:
        """
        GetPrintWrapMode() -> int
        
        Is printing line wrapped?
        """

    def GetDirectFunction(self) -> Any:
        """
        GetDirectFunction() -> Any
        
        Retrieve a pointer to a function that processes messages for this
        Scintilla.
        """

    def GetDirectPointer(self) -> Any:
        """
        GetDirectPointer() -> Any
        
        Retrieve a pointer value to use as the first argument when calling the
        function returned by GetDirectFunction.
        """

    def GetCharacterPointer(self) -> Any:
        """
        GetCharacterPointer() -> Any
        
        Compact the document buffer and return a read-only memoryview
        object of the characters in the document.
        """

    def GetRangePointer(self, position: int, rangeLength: int) -> Any:
        """
        GetRangePointer(position, rangeLength) -> Any
        
        Return a read-only pointer to a range of characters in the
        document. May move the gap so that the range is contiguous,
        but will only move up to rangeLength bytes.
        """

    def GetGapPosition(self) -> int:
        """
        GetGapPosition() -> int
        
        Return a position which, to avoid performance costs, should not be
        within the range of a call to GetRangePointer.
        """

    def GetDocPointer(self) -> Any:
        """
        GetDocPointer() -> Any
        
        Retrieve a pointer to the document object.
        """

    def SetDocPointer(self, docPointer: Any) -> None:
        """
        SetDocPointer(docPointer) -> None
        
        Change the document object used.
        """

    def CreateDocument(self) -> Any:
        """
        CreateDocument() -> Any
        
        Create a new document object.
        """

    def AddRefDocument(self, docPointer: Any) -> None:
        """
        AddRefDocument(docPointer) -> None
        
        Extend life of document.
        """

    def ReleaseDocument(self, docPointer: Any) -> None:
        """
        ReleaseDocument(docPointer) -> None
        
        Release a reference to the document, deleting document if it fades to
        black.
        """

    def CreateLoader(self, bytes: int) -> Any:
        """
        CreateLoader(bytes) -> Any
        
        Create an ILoader*.
        """

    def VisibleFromDocLine(self, docLine: int) -> int:
        """
        VisibleFromDocLine(docLine) -> int
        
        Find the display line of a document line taking hidden lines into
        account.
        """

    def DocLineFromVisible(self, displayLine: int) -> int:
        """
        DocLineFromVisible(displayLine) -> int
        
        Find the document line of a display line taking hidden lines into
        account.
        """

    def SetFoldLevel(self, line: int, level: int) -> None:
        """
        SetFoldLevel(line, level) -> None
        
        Set the fold level of a line.
        """

    def GetFoldLevel(self, line: int) -> int:
        """
        GetFoldLevel(line) -> int
        
        Retrieve the fold level of a line.
        """

    def GetLastChild(self, line: int, level: int) -> int:
        """
        GetLastChild(line, level) -> int
        
        Find the last child line of a header line.
        """

    def GetFoldParent(self, line: int) -> int:
        """
        GetFoldParent(line) -> int
        
        Find the parent line of a child line.
        """

    def ShowLines(self, lineStart: int, lineEnd: int) -> None:
        """
        ShowLines(lineStart, lineEnd) -> None
        
        Make a range of lines visible.
        """

    def HideLines(self, lineStart: int, lineEnd: int) -> None:
        """
        HideLines(lineStart, lineEnd) -> None
        
        Make a range of lines invisible.
        """

    def GetLineVisible(self, line: int) -> bool:
        """
        GetLineVisible(line) -> bool
        
        Is a line visible?
        """

    def GetAllLinesVisible(self) -> bool:
        """
        GetAllLinesVisible() -> bool
        
        Are all lines visible?
        """

    def SetFoldExpanded(self, line: int, expanded: bool) -> None:
        """
        SetFoldExpanded(line, expanded) -> None
        
        Show the children of a header line.
        """

    def GetFoldExpanded(self, line: int) -> bool:
        """
        GetFoldExpanded(line) -> bool
        
        Is a header line expanded?
        """

    def ToggleFold(self, line: int) -> None:
        """
        ToggleFold(line) -> None
        
        Switch a header line between expanded and contracted.
        """

    def ToggleFoldShowText(self, line: int, text: str) -> None:
        """
        ToggleFoldShowText(line, text) -> None
        
        Switch a header line between expanded and contracted and show some
        text after the line.
        """

    def FoldDisplayTextSetStyle(self, style: int) -> None:
        """
        FoldDisplayTextSetStyle(style) -> None
        
        Set the style of fold display text.
        """

    def FoldLine(self, line: int, action: int) -> None:
        """
        FoldLine(line, action) -> None
        
        Expand or contract a fold header.
        """

    def FoldChildren(self, line: int, action: int) -> None:
        """
        FoldChildren(line, action) -> None
        
        Expand or contract a fold header and its children.
        """

    def ExpandChildren(self, line: int, level: int) -> None:
        """
        ExpandChildren(line, level) -> None
        
        Expand a fold header and all children.
        """

    def FoldAll(self, action: int) -> None:
        """
        FoldAll(action) -> None
        
        Expand or contract all fold headers.
        """

    def EnsureVisible(self, line: int) -> None:
        """
        EnsureVisible(line) -> None
        
        Ensure a particular line is visible by expanding any header line
        hiding it.
        """

    def SetAutomaticFold(self, automaticFold: int) -> None:
        """
        SetAutomaticFold(automaticFold) -> None
        
        Set automatic folding behaviours.
        """

    def GetAutomaticFold(self) -> int:
        """
        GetAutomaticFold() -> int
        
        Get automatic folding behaviours.
        """

    def SetFoldFlags(self, flags: int) -> None:
        """
        SetFoldFlags(flags) -> None
        
        Set some style options for folding.
        """

    def EnsureVisibleEnforcePolicy(self, line: int) -> None:
        """
        EnsureVisibleEnforcePolicy(line) -> None
        
        Ensure a particular line is visible by expanding any header line
        hiding it.
        """

    def ContractedFoldNext(self, lineStart: int) -> int:
        """
        ContractedFoldNext(lineStart) -> int
        
        Find the next line at or after lineStart that is a contracted fold
        header line.
        """

    def WrapCount(self, docLine: int) -> int:
        """
        WrapCount(docLine) -> int
        
        The number of display lines needed to wrap a document line.
        """

    def SetWrapMode(self, wrapMode: int) -> None:
        """
        SetWrapMode(wrapMode) -> None
        
        Sets whether text is word wrapped.
        """

    def GetWrapMode(self) -> int:
        """
        GetWrapMode() -> int
        
        Retrieve whether text is word wrapped.
        """

    def SetWrapVisualFlags(self, wrapVisualFlags: int) -> None:
        """
        SetWrapVisualFlags(wrapVisualFlags) -> None
        
        Set the display mode of visual flags for wrapped lines.
        """

    def GetWrapVisualFlags(self) -> int:
        """
        GetWrapVisualFlags() -> int
        
        Retrieve the display mode of visual flags for wrapped lines.
        """

    def SetWrapVisualFlagsLocation(self, wrapVisualFlagsLocation: int) -> None:
        """
        SetWrapVisualFlagsLocation(wrapVisualFlagsLocation) -> None
        
        Set the location of visual flags for wrapped lines.
        """

    def GetWrapVisualFlagsLocation(self) -> int:
        """
        GetWrapVisualFlagsLocation() -> int
        
        Retrieve the location of visual flags for wrapped lines.
        """

    def SetWrapStartIndent(self, indent: int) -> None:
        """
        SetWrapStartIndent(indent) -> None
        
        Set the start indent for wrapped lines.
        """

    def GetWrapStartIndent(self) -> int:
        """
        GetWrapStartIndent() -> int
        
        Retrieve the start indent for wrapped lines.
        """

    def SetWrapIndentMode(self, wrapIndentMode: int) -> None:
        """
        SetWrapIndentMode(wrapIndentMode) -> None
        
        Sets how wrapped sublines are placed.
        """

    def GetWrapIndentMode(self) -> int:
        """
        GetWrapIndentMode() -> int
        
        Retrieve how wrapped sublines are placed.
        """

    def GetLayoutCache(self) -> int:
        """
        GetLayoutCache() -> int
        
        Retrieve the degree of caching of layout information.
        """

    def LinesJoin(self) -> None:
        """
        LinesJoin() -> None
        
        Join the lines in the target.
        """

    def LinesSplit(self, pixelWidth: int) -> None:
        """
        LinesSplit(pixelWidth) -> None
        
        Split the lines in the target into lines that are less wide than
        pixelWidth where possible.
        """

    def SetPositionCacheSize(self, size: int) -> None:
        """
        SetPositionCacheSize(size) -> None
        
        Set number of entries in position cache.
        """

    def GetPositionCacheSize(self) -> int:
        """
        GetPositionCacheSize() -> int
        
        How many entries are allocated to the position cache?
        """

    def ZoomIn(self) -> None:
        """
        ZoomIn() -> None
        
        Magnify the displayed text by increasing the sizes by 1 point.
        """

    def ZoomOut(self) -> None:
        """
        ZoomOut() -> None
        
        Make the displayed text smaller by decreasing the sizes by 1 point.
        """

    def SetZoom(self, zoomInPoints: int) -> None:
        """
        SetZoom(zoomInPoints) -> None
        
        Set the zoom level.
        """

    def GetZoom(self) -> int:
        """
        GetZoom() -> int
        
        Retrieve the zoom level.
        """

    def GetEdgeColumn(self) -> int:
        """
        GetEdgeColumn() -> int
        
        Retrieve the column number which text should be kept within.
        """

    def SetEdgeColumn(self, column: int) -> None:
        """
        SetEdgeColumn(column) -> None
        
        Set the column number of the edge.
        """

    def GetEdgeMode(self) -> int:
        """
        GetEdgeMode() -> int
        
        Retrieve the edge highlight mode.
        """

    def SetEdgeMode(self, edgeMode: int) -> None:
        """
        SetEdgeMode(edgeMode) -> None
        
        The edge may be displayed by a line
        (wxSTC_EDGE_LINE/wxSTC_EDGE_MULTILINE) or by highlighting text that
        goes beyond it (wxSTC_EDGE_BACKGROUND) or not displayed at all
        (wxSTC_EDGE_NONE).
        """

    def GetEdgeColour(self) -> Colour:
        """
        GetEdgeColour() -> Colour
        
        Retrieve the colour used in edge indication.
        """

    def SetEdgeColour(self, edgeColour: Colour) -> None:
        """
        SetEdgeColour(edgeColour) -> None
        
        Change the colour used in edge indication.
        """

    def MultiEdgeAddLine(self, column: int, edgeColour: Colour) -> None:
        """
        MultiEdgeAddLine(column, edgeColour) -> None
        
        Add a new vertical edge to the view.
        """

    def MultiEdgeClearAll(self) -> None:
        """
        MultiEdgeClearAll() -> None
        
        Clear all vertical edges.
        """

    def ChangeLexerState(self, start: int, end: int) -> int:
        """
        ChangeLexerState(start, end) -> int
        
        Indicate that the internal state of a lexer has changed over a range
        and therefore there may be a need to redraw.
        """

    def SetLexer(self, lexer: int) -> None:
        """
        SetLexer(lexer) -> None
        
        Set the lexing language of the document.
        """

    def GetLexer(self) -> int:
        """
        GetLexer() -> int
        
        Retrieve the lexing language of the document.
        """

    def Colourise(self, start: int, end: int) -> None:
        """
        Colourise(start, end) -> None
        
        Colourise a segment of the document using the current lexing language.
        """

    def SetProperty(self, key: str, value: str) -> None:
        """
        SetProperty(key, value) -> None
        
        Set up a value that may be used by a lexer for some optional feature.
        """

    def SetKeyWords(self, keyWordSet: int, keyWords: str) -> None:
        """
        SetKeyWords(keyWordSet, keyWords) -> None
        
        Set up the key words used by the lexer.
        """

    def SetLexerLanguage(self, language: str) -> None:
        """
        SetLexerLanguage(language) -> None
        
        Set the lexing language of the document based on string name.
        """

    def LoadLexerLibrary(self, path: str) -> None:
        """
        LoadLexerLibrary(path) -> None
        
        Load a lexer library (dll / so).
        """

    def GetProperty(self, key: str) -> str:
        """
        GetProperty(key) -> str
        
        Retrieve a "property" value previously set with SetProperty.
        """

    def GetPropertyExpanded(self, key: str) -> str:
        """
        GetPropertyExpanded(key) -> str
        
        Retrieve a "property" value previously set with SetProperty, with
        "$()" variable replacement on returned buffer.
        """

    def GetPropertyInt(self, key: str, defaultValue: int=0) -> int:
        """
        GetPropertyInt(key, defaultValue=0) -> int
        
        Retrieve a "property" value previously set with SetProperty,
        interpreted as an int AFTER any "$()" variable replacement.
        """

    def GetLexerLanguage(self) -> str:
        """
        GetLexerLanguage() -> str
        
        Retrieve the lexing language of the document.
        """

    def PrivateLexerCall(self, operation: int, pointer: Any) -> Any:
        """
        PrivateLexerCall(operation, pointer) -> Any
        
        For private communication between an application and a known lexer.
        """

    def PropertyNames(self) -> str:
        """
        PropertyNames() -> str
        
        Retrieve a '\n' separated list of properties understood by the current
        lexer.
        """

    def PropertyType(self, name: str) -> int:
        """
        PropertyType(name) -> int
        
        Retrieve the type of a property.
        """

    def DescribeProperty(self, name: str) -> str:
        """
        DescribeProperty(name) -> str
        
        Describe a property.
        """

    def DescribeKeyWordSets(self) -> str:
        """
        DescribeKeyWordSets() -> str
        
        Retrieve a '\n' separated list of descriptions of the keyword sets
        understood by the current lexer.
        """

    def AllocateSubStyles(self, styleBase: int, numberStyles: int) -> int:
        """
        AllocateSubStyles(styleBase, numberStyles) -> int
        
        Allocate a set of sub styles for a particular base style, returning
        start of range.
        """

    def GetSubStylesStart(self, styleBase: int) -> int:
        """
        GetSubStylesStart(styleBase) -> int
        
        The starting style number for the sub styles associated with a base
        style.
        """

    def GetSubStylesLength(self, styleBase: int) -> int:
        """
        GetSubStylesLength(styleBase) -> int
        
        The number of sub styles associated with a base style.
        """

    def GetStyleFromSubStyle(self, subStyle: int) -> int:
        """
        GetStyleFromSubStyle(subStyle) -> int
        
        For a sub style, return the base style, else return the argument.
        """

    def GetPrimaryStyleFromStyle(self, style: int) -> int:
        """
        GetPrimaryStyleFromStyle(style) -> int
        
        For a secondary style, return the primary style, else return the
        argument.
        """

    def FreeSubStyles(self) -> None:
        """
        FreeSubStyles() -> None
        
        Free allocated sub styles.
        """

    def SetIdentifiers(self, style: int, identifiers: str) -> None:
        """
        SetIdentifiers(style, identifiers) -> None
        
        Set the identifiers that are shown in a particular style.
        """

    def DistanceToSecondaryStyles(self) -> int:
        """
        DistanceToSecondaryStyles() -> int
        
        Where styles are duplicated by a feature such as active/inactive code
        return the distance between the two types.
        """

    def GetSubStyleBases(self) -> str:
        """
        GetSubStyleBases() -> str
        
        Get the set of base styles that can be extended with sub styles.
        """

    def SetMouseDwellTime(self, periodMilliseconds: int) -> None:
        """
        SetMouseDwellTime(periodMilliseconds) -> None
        
        Sets the time the mouse must sit still to generate a mouse dwell
        event.
        """

    def GetMouseDwellTime(self) -> int:
        """
        GetMouseDwellTime() -> int
        
        Retrieve the time the mouse must sit still to generate a mouse dwell
        event.
        """

    def SetModEventMask(self, eventMask: int) -> None:
        """
        SetModEventMask(eventMask) -> None
        
        Set which document modification events are sent to the container.
        """

    def GetModEventMask(self) -> int:
        """
        GetModEventMask() -> int
        
        Get which document modification events are sent to the container.
        """

    def SetIdentifier(self, identifier: int) -> None:
        """
        SetIdentifier(identifier) -> None
        
        Set the identifier reported as idFrom in notification messages.
        """

    def GetIdentifier(self) -> int:
        """
        GetIdentifier() -> int
        
        Get the identifier.
        """

    def SetStyleBits(self, bits: int) -> None:
        """
        SetStyleBits(bits) -> None
        
        Divide each styling byte into lexical class bits (default: 5) and
        indicator bits (default: 3).
        """

    def GetStyleBits(self) -> int:
        """
        GetStyleBits() -> int
        
        Retrieve number of bits in style bytes used to hold the lexical state.
        """

    def GetStyleBitsNeeded(self) -> int:
        """
        GetStyleBitsNeeded() -> int
        
        Retrieve the number of bits the current lexer needs for styling.
        """

    def GetCurrentLine(self) -> int:
        """
        GetCurrentLine() -> int
        
        Returns the line number of the line with the caret.
        """

    def StyleSetSpec(self, styleNum: int, spec: str) -> None:
        """
        StyleSetSpec(styleNum, spec) -> None
        
        Extract style settings from a spec-string which is composed of one or
        more of the following comma separated elements:
        """

    def StyleGetFont(self, style: int) -> Font:
        """
        StyleGetFont(style) -> Font
        
        Get the font of a style.
        """

    def StyleSetFont(self, styleNum: int, font: Font) -> None:
        """
        StyleSetFont(styleNum, font) -> None
        
        Set style size, face, bold, italic, and underline attributes from a
        wxFont's attributes.
        """

    def StyleSetFontAttr(self, styleNum: int, size: int, faceName: str, bold: bool, italic: bool, underline: bool, encoding: FontEncoding=FONTENCODING_DEFAULT) -> None:
        """
        StyleSetFontAttr(styleNum, size, faceName, bold, italic, underline, encoding=FONTENCODING_DEFAULT) -> None
        
        Set all font style attributes at once.
        """

    def StyleSetFontEncoding(self, style: int, encoding: FontEncoding) -> None:
        """
        StyleSetFontEncoding(style, encoding) -> None
        
        Set the font encoding to be used by a style.
        """

    def CmdKeyExecute(self, cmd: int) -> None:
        """
        CmdKeyExecute(cmd) -> None
        
        Perform one of the operations defined by the wxSTC_CMD_* constants.
        """

    def SetMargins(self, left: int, right: int) -> None:
        """
        SetMargins(left, right) -> None
        
        Set the left and right margin in the edit area, measured in pixels.
        """

    def ScrollToLine(self, line: int) -> None:
        """
        ScrollToLine(line) -> None
        
        Scroll enough to make the given line visible.
        """

    def ScrollToColumn(self, column: int) -> None:
        """
        ScrollToColumn(column) -> None
        
        Scroll enough to make the given column visible.
        """

    def SendMsg(self, msg: int, wp: UIntPtr=0, lp: IntPtr=0) -> IntPtr:
        """
        SendMsg(msg, wp=0, lp=0) -> IntPtr
        
        Scintilla API call.
        """

    def SetVScrollBar(self, bar: ScrollBar) -> None:
        """
        SetVScrollBar(bar) -> None
        
        Set the vertical scrollbar to use instead of the one that's built-in.
        """

    def SetHScrollBar(self, bar: ScrollBar) -> None:
        """
        SetHScrollBar(bar) -> None
        
        Set the horizontal scrollbar to use instead of the one that's built-
        in.
        """

    def GetLastKeydownProcessed(self) -> bool:
        """
        GetLastKeydownProcessed() -> bool
        
        Can be used to prevent the EVT_CHAR handler from adding the char.
        """

    def SetLastKeydownProcessed(self, val: bool) -> None:
        """
        SetLastKeydownProcessed(val) -> None
        
        Returns the line number of the line with the caret.
        """

    def SaveFile(self, filename: str) -> bool:
        """
        SaveFile(filename) -> bool
        
        Write the contents of the editor to filename.
        """

    def LoadFile(self, filename: str) -> bool:
        """
        LoadFile(filename) -> bool
        
        Load the contents of filename into the editor.
        """

    def DoDragEnter(self, x: int, y: int, defaultRes: DragResult) -> DragResult:
        """
        DoDragEnter(x, y, defaultRes) -> DragResult
        
        Allow for simulating a DnD DragEnter.
        """

    def DoDragOver(self, x: int, y: int, defaultRes: DragResult) -> DragResult:
        """
        DoDragOver(x, y, defaultRes) -> DragResult
        
        Allow for simulating a DnD DragOver.
        """

    def DoDragLeave(self) -> None:
        """
        DoDragLeave() -> None
        
        Allow for simulating a DnD DragLeave.
        """

    def DoDropText(self, x: int, y: int, data: str) -> bool:
        """
        DoDropText(x, y, data) -> bool
        
        Allow for simulating a DnD DropText.
        """

    def SetUseAntiAliasing(self, useAA: bool) -> None:
        """
        SetUseAntiAliasing(useAA) -> None
        
        Specify whether anti-aliased fonts should be used.
        """

    def GetUseAntiAliasing(self) -> bool:
        """
        GetUseAntiAliasing() -> bool
        
        Returns the current UseAntiAliasing setting.
        """

    def AnnotationClearLine(self, line: int) -> None:
        """
        AnnotationClearLine(line) -> None
        
        Clear annotations from the given line.
        """

    def MarkerDefineBitmap(self, markerNumber: int, bmp: Bitmap) -> None:
        """
        MarkerDefineBitmap(markerNumber, bmp) -> None
        
        Define a marker with a wxBitmap.
        """

    def AddTextRaw(self, text: str, length: int=-1) -> None:
        """
        AddTextRaw(text, length=-1) -> None
        
        Add text to the document at current position.
        """

    def InsertTextRaw(self, pos: int, text: str) -> None:
        """
        InsertTextRaw(pos, text) -> None
        
        Insert string at a position.
        """

    def GetCurLineRaw(self) -> Tuple[CharBuffer, int]:
        """
        GetCurLineRaw() -> Tuple[CharBuffer, int]
        
        Retrieve the text of the line containing the caret.
        """

    def GetLineRaw(self, line: int) -> CharBuffer:
        """
        GetLineRaw(line) -> CharBuffer
        
        Retrieve the contents of a line.
        """

    def GetSelectedTextRaw(self) -> CharBuffer:
        """
        GetSelectedTextRaw() -> CharBuffer
        
        Retrieve the selected text.
        """

    def GetTargetTextRaw(self) -> CharBuffer:
        """
        GetTargetTextRaw() -> CharBuffer
        
        Retrieve the target text.
        """

    def GetTextRangeRaw(self, startPos: int, endPos: int) -> CharBuffer:
        """
        GetTextRangeRaw(startPos, endPos) -> CharBuffer
        
        Retrieve a range of text.
        """

    def SetTextRaw(self, text: str) -> None:
        """
        SetTextRaw(text) -> None
        
        Replace the contents of the document with the argument text.
        """

    def GetTextRaw(self) -> CharBuffer:
        """
        GetTextRaw() -> CharBuffer
        
        Retrieve all the text in the document.
        """

    def AppendTextRaw(self, text: str, length: int=-1) -> None:
        """
        AppendTextRaw(text, length=-1) -> None
        
        Append a string to the end of the document without changing the
        selection.
        """

    def ReplaceSelectionRaw(self, text: str) -> None:
        """
        ReplaceSelectionRaw(text) -> None
        
        Replace the current selection with text.
        """

    def ReplaceTargetRaw(self, text: str, length: int=-1) -> int:
        """
        ReplaceTargetRaw(text, length=-1) -> int
        
        Replace the current target with text.
        """

    def ReplaceTargetRERaw(self, text: str, length: int=-1) -> int:
        """
        ReplaceTargetRERaw(text, length=-1) -> int
        
        Replace the current target with text using regular expressions.
        """

    def WriteText(self, text: str) -> None:
        """
        WriteText(text) -> None
        
        Writes the text into the text control at the current insertion
        position.
        """

    def Remove(self, from_: int, to_: int) -> None:
        """
        Remove(from_, to_) -> None
        
        Removes the text starting at the first given position up to (but not including) the character at the last position.
        """

    def Replace(self, from_: int, to_: int, value: str) -> None:
        """
        Replace(from_, to_, value) -> None
        
        Replaces the text starting at the first position up to (but not including) the character at the last position with the given text.
        """

    def SetInsertionPoint(self, pos: int) -> None:
        """
        SetInsertionPoint(pos) -> None
        
        Sets the insertion point at the given position.
        """

    def GetInsertionPoint(self) -> int:
        """
        GetInsertionPoint() -> int
        
        Returns the insertion point, or cursor, position.
        """

    def GetLastPosition(self) -> int:
        """
        GetLastPosition() -> int
        
        Returns the zero based index of the last position in the text control,
        which is equal to the number of characters in the control.
        """

    def SetSelection(self, from_: int, to_: int) -> None:
        """
        SetSelection(from_, to_) -> None
        
        Selects the text starting at the first position up to (but not
        including) the character at the last position.
        """

    def SelectNone(self) -> None:
        """
        SelectNone() -> None
        
        Deselects selected text in the control.
        """

    def GetSelection(self) -> Tuple[int, int]:
        """
        GetSelection() -> Tuple[int, int]
        
        Gets the current selection span.
        """

    def IsEditable(self) -> bool:
        """
        IsEditable() -> bool
        
        Returns true if the controls contents may be edited by user (note that
        it always can be changed by the program).
        """

    def SetEditable(self, editable: bool) -> None:
        """
        SetEditable(editable) -> None
        
        Makes the text item editable or read-only, overriding the
        wxTE_READONLY flag.
        """

    def GetLineLength(self, lineNo: int) -> int:
        """
        GetLineLength(lineNo) -> int
        
        Gets the length of the specified line, not including any trailing
        newline character(s).
        """

    def GetLineText(self, lineNo: int) -> str:
        """
        GetLineText(lineNo) -> str
        
        Returns the contents of a given line in the text control, not
        including any trailing newline character(s).
        """

    def GetNumberOfLines(self) -> int:
        """
        GetNumberOfLines() -> int
        
        Returns the number of lines in the text control buffer.
        """

    def IsModified(self) -> bool:
        """
        IsModified() -> bool
        
        Returns true if the text has been modified by user.
        """

    def MarkDirty(self) -> None:
        """
        MarkDirty() -> None
        
        Mark text as modified (dirty).
        """

    def DiscardEdits(self) -> None:
        """
        DiscardEdits() -> None
        
        Resets the internal modified flag as if the current changes had been
        saved.
        """

    def SetStyle(self, start: int, end: int, style: TextAttr) -> bool:
        """
        SetStyle(start, end, style) -> bool
        
        This method is inherited from wxTextAreaBase but is not implemented in
        wxStyledTextCtrl.
        """

    def GetStyle(self, position: int, style: TextAttr) -> bool:
        """
        GetStyle(position, style) -> bool
        
        This method is inherited from wxTextAreaBase but is not implemented in
        wxStyledTextCtrl.
        """

    def SetDefaultStyle(self, style: TextAttr) -> bool:
        """
        SetDefaultStyle(style) -> bool
        
        This method is inherited from wxTextAreaBase but is not implemented in
        wxStyledTextCtrl.
        """

    def XYToPosition(self, x: int, y: int) -> int:
        """
        XYToPosition(x, y) -> int
        
        Converts the given zero based column and line number to a position.
        """

    def PositionToXY(self, pos: int) -> Tuple[bool, int, int]:
        """
        PositionToXY(pos) -> Tuple[bool, int, int]
        
        Converts given position to a zero-based column, line number pair.
        """

    def ShowPosition(self, pos: int) -> None:
        """
        ShowPosition(pos) -> None
        
        Makes the line containing the given position visible.
        """

    def HitTestPos(self, pt: Point) -> Tuple[TextCtrlHitTestResult, int]:
        """
        HitTestPos(pt) -> Tuple[TextCtrlHitTestResult, int]
        
        Finds the position of the character at the specified point.
        """

    def HitTest(self, pt: Point) -> Tuple[TextCtrlHitTestResult, TextCoord, TextCoord]:
        """
        HitTest(pt) -> Tuple[TextCtrlHitTestResult, TextCoord, TextCoord]
        
        Finds the row and column of the character at the specified point.
        """

    @staticmethod
    def GetLibraryVersionInfo() -> VersionInfo:
        """
        GetLibraryVersionInfo() -> VersionInfo
        
        Returns the version of the Scintilla library used by this control.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: WindowVariant=WINDOW_VARIANT_NORMAL) -> VisualAttributes:
        """
        GetClassDefaultAttributes(variant=WINDOW_VARIANT_NORMAL) -> VisualAttributes
        """

    @overload
    def AutoComplete(self, completer: TextCompleter) -> bool:
        ...

    @overload
    def AutoComplete(self, choices: List[str]) -> bool:
        """
        AutoComplete(choices) -> bool
        AutoComplete(completer) -> bool
        
        Call this function to enable auto-completion of the text typed in a
        single-line text control using the given choices.
        """

    def AutoCompleteFileNames(self) -> bool:
        """
        AutoCompleteFileNames() -> bool
        
        Call this function to enable auto-completion of the text typed in a
        single-line text control using all valid file system paths.
        """

    def AutoCompleteDirectories(self) -> bool:
        """
        AutoCompleteDirectories() -> bool
        
        Call this function to enable auto-completion of the text using the
        file system directories.
        """

    def CanCopy(self) -> bool:
        """
        CanCopy() -> bool
        
        Returns true if the selection can be copied to the clipboard.
        """

    def CanCut(self) -> bool:
        """
        CanCut() -> bool
        
        Returns true if the selection can be cut to the clipboard.
        """

    def ChangeValue(self, value: str) -> None:
        """
        ChangeValue(value) -> None
        
        Sets the new text control value.
        """

    def ForceUpper(self) -> None:
        """
        ForceUpper() -> None
        
        Convert all text entered into the control to upper case.
        """

    def GetRange(self, from_: int, to_: int) -> str:
        """
        GetRange(from_, to_) -> str
        
        Returns the string containing the text starting in the positions from
        and up to to in the control.
        """

    def GetStringSelection(self) -> str:
        """
        GetStringSelection() -> str
        
        Gets the text currently selected in the control.
        """

    def GetValue(self) -> str:
        """
        GetValue() -> str
        
        Gets the contents of the control.
        """

    def IsEmpty(self) -> bool:
        """
        IsEmpty() -> bool
        
        Returns true if the control is currently empty.
        """

    def SetInsertionPointEnd(self) -> None:
        """
        SetInsertionPointEnd() -> None
        
        Sets the insertion point at the end of the text control.
        """

    def SetMaxLength(self, len: int) -> None:
        """
        SetMaxLength(len) -> None
        
        This function sets the maximum number of characters the user can enter
        into the control.
        """

    def SetHint(self, hint: str) -> bool:
        """
        SetHint(hint) -> bool
        
        Sets a hint shown in an empty unfocused text control.
        """

    def GetHint(self) -> str:
        """
        GetHint() -> str
        
        Returns the current hint string.
        """

    def GetMargins(self) -> Point:
        """
        GetMargins() -> Point
        
        Returns the margins used by the control.
        """

    def SetValue(self, value: str) -> None:
        """
        SetValue(value) -> None
        
        Sets the new text control value.
        """

    def GetDefaultStyle(self) -> TextAttr:
        """
        GetDefaultStyle() -> TextAttr
        
        Returns the style currently used for the new text.
        """

    def PositionToCoords(self, pos: int) -> Point:
        """
        PositionToCoords(pos) -> Point
        
        Converts given text position to client coordinates in pixels.
        """

    def SetModified(self, modified: bool) -> None:
        """
        SetModified(modified) -> None
        
        Marks the control as being modified by the user or not.
        """

    def write(self, text: str) -> None:
        """
        write(text) -> None
        
        Append text to the textctrl, for file-like compatibility.
        """

    def flush(self) -> None:
        """
        flush() -> None
        
        NOP, for file-like compatibility.
        """
    @property
    def AdditionalCaretForeground(self) -> Colour: ...
    @AdditionalCaretForeground.setter
    def AdditionalCaretForeground(self, value: Colour, /) -> None: ...
    @property
    def AdditionalCaretsBlink(self) -> bool: ...
    @AdditionalCaretsBlink.setter
    def AdditionalCaretsBlink(self, value: bool, /) -> None: ...
    @property
    def AdditionalCaretsVisible(self) -> bool: ...
    @AdditionalCaretsVisible.setter
    def AdditionalCaretsVisible(self, value: bool, /) -> None: ...
    @property
    def AdditionalSelAlpha(self) -> int: ...
    @AdditionalSelAlpha.setter
    def AdditionalSelAlpha(self, value: int, /) -> None: ...
    @property
    def AdditionalSelectionTyping(self) -> bool: ...
    @AdditionalSelectionTyping.setter
    def AdditionalSelectionTyping(self, value: bool, /) -> None: ...
    @property
    def AllLinesVisible(self) -> bool: ...
    @property
    def Anchor(self) -> int: ...
    @Anchor.setter
    def Anchor(self, value: int, /) -> None: ...
    @property
    def AutomaticFold(self) -> int: ...
    @AutomaticFold.setter
    def AutomaticFold(self, value: int, /) -> None: ...
    @property
    def BackSpaceUnIndents(self) -> bool: ...
    @BackSpaceUnIndents.setter
    def BackSpaceUnIndents(self, value: bool, /) -> None: ...
    @property
    def BufferedDraw(self) -> bool: ...
    @BufferedDraw.setter
    def BufferedDraw(self, value: bool, /) -> None: ...
    @property
    def CaretForeground(self) -> Colour: ...
    @CaretForeground.setter
    def CaretForeground(self, value: Colour, /) -> None: ...
    @property
    def CaretLineBackAlpha(self) -> int: ...
    @CaretLineBackAlpha.setter
    def CaretLineBackAlpha(self, value: int, /) -> None: ...
    @property
    def CaretLineBackground(self) -> Colour: ...
    @CaretLineBackground.setter
    def CaretLineBackground(self, value: Colour, /) -> None: ...
    @property
    def CaretLineVisible(self) -> bool: ...
    @CaretLineVisible.setter
    def CaretLineVisible(self, value: bool, /) -> None: ...
    @property
    def CaretLineVisibleAlways(self) -> bool: ...
    @CaretLineVisibleAlways.setter
    def CaretLineVisibleAlways(self, value: bool, /) -> None: ...
    @property
    def CaretPeriod(self) -> int: ...
    @CaretPeriod.setter
    def CaretPeriod(self, value: int, /) -> None: ...
    @property
    def CaretSticky(self) -> int: ...
    @CaretSticky.setter
    def CaretSticky(self, value: int, /) -> None: ...
    @property
    def CaretStyle(self) -> int: ...
    @CaretStyle.setter
    def CaretStyle(self, value: int, /) -> None: ...
    @property
    def CaretWidth(self) -> int: ...
    @CaretWidth.setter
    def CaretWidth(self, value: int, /) -> None: ...
    @property
    def CharacterPointer(self) -> Any: ...
    @property
    def CodePage(self) -> int: ...
    @CodePage.setter
    def CodePage(self, value: int, /) -> None: ...
    @property
    def ControlCharSymbol(self) -> int: ...
    @ControlCharSymbol.setter
    def ControlCharSymbol(self, value: int, /) -> None: ...
    @property
    def CurLine(self) -> Tuple[str, int]: ...
    @property
    def CurLineRaw(self) -> Tuple[CharBuffer, int]: ...
    @property
    def CurrentLine(self) -> int: ...
    @property
    def CurrentPos(self) -> int: ...
    @CurrentPos.setter
    def CurrentPos(self, value: int, /) -> None: ...
    @property
    def DefaultStyle(self) -> TextAttr: ...
    @DefaultStyle.setter
    def DefaultStyle(self, value: TextAttr, /) -> None: ...
    @property
    def DirectFunction(self) -> Any: ...
    @property
    def DirectPointer(self) -> Any: ...
    @property
    def DocPointer(self) -> Any: ...
    @DocPointer.setter
    def DocPointer(self, value: Any, /) -> None: ...
    @property
    def EOLMode(self) -> int: ...
    @EOLMode.setter
    def EOLMode(self, value: int, /) -> None: ...
    @property
    def EdgeColour(self) -> Colour: ...
    @EdgeColour.setter
    def EdgeColour(self, value: Colour, /) -> None: ...
    @property
    def EdgeColumn(self) -> int: ...
    @EdgeColumn.setter
    def EdgeColumn(self, value: int, /) -> None: ...
    @property
    def EdgeMode(self) -> int: ...
    @EdgeMode.setter
    def EdgeMode(self, value: int, /) -> None: ...
    @property
    def EndAtLastLine(self) -> bool: ...
    @EndAtLastLine.setter
    def EndAtLastLine(self, value: bool, /) -> None: ...
    @property
    def EndStyled(self) -> int: ...
    @property
    def ExtraAscent(self) -> int: ...
    @ExtraAscent.setter
    def ExtraAscent(self, value: int, /) -> None: ...
    @property
    def ExtraDescent(self) -> int: ...
    @ExtraDescent.setter
    def ExtraDescent(self, value: int, /) -> None: ...
    @property
    def FirstVisibleLine(self) -> int: ...
    @FirstVisibleLine.setter
    def FirstVisibleLine(self, value: int, /) -> None: ...
    @property
    def FontQuality(self) -> int: ...
    @FontQuality.setter
    def FontQuality(self, value: int, /) -> None: ...
    @property
    def GapPosition(self) -> int: ...
    @property
    def HighlightGuide(self) -> int: ...
    @HighlightGuide.setter
    def HighlightGuide(self, value: int, /) -> None: ...
    @property
    def Hint(self) -> str: ...
    @Hint.setter
    def Hint(self, value: str, /) -> None: ...
    @property
    def HotspotActiveBackground(self) -> Colour: ...
    @property
    def HotspotActiveForeground(self) -> Colour: ...
    @property
    def HotspotActiveUnderline(self) -> bool: ...
    @HotspotActiveUnderline.setter
    def HotspotActiveUnderline(self, value: bool, /) -> None: ...
    @property
    def HotspotSingleLine(self) -> bool: ...
    @HotspotSingleLine.setter
    def HotspotSingleLine(self, value: bool, /) -> None: ...
    @property
    def IMEInteraction(self) -> int: ...
    @IMEInteraction.setter
    def IMEInteraction(self, value: int, /) -> None: ...
    @property
    def Identifier(self) -> int: ...
    @Identifier.setter
    def Identifier(self, value: int, /) -> None: ...
    @property
    def IdleStyling(self) -> int: ...
    @IdleStyling.setter
    def IdleStyling(self, value: int, /) -> None: ...
    @property
    def Indent(self) -> int: ...
    @Indent.setter
    def Indent(self, value: int, /) -> None: ...
    @property
    def IndentationGuides(self) -> int: ...
    @IndentationGuides.setter
    def IndentationGuides(self, value: int, /) -> None: ...
    @property
    def IndicatorCurrent(self) -> int: ...
    @IndicatorCurrent.setter
    def IndicatorCurrent(self, value: int, /) -> None: ...
    @property
    def IndicatorValue(self) -> int: ...
    @IndicatorValue.setter
    def IndicatorValue(self, value: int, /) -> None: ...
    @property
    def InsertionPoint(self) -> int: ...
    @InsertionPoint.setter
    def InsertionPoint(self, value: int, /) -> None: ...
    @property
    def LastKeydownProcessed(self) -> bool: ...
    @LastKeydownProcessed.setter
    def LastKeydownProcessed(self, value: bool, /) -> None: ...
    @property
    def LastPosition(self) -> int: ...
    @property
    def LayoutCache(self) -> int: ...
    @LayoutCache.setter
    def LayoutCache(self, value: int, /) -> None: ...
    @property
    def Length(self) -> int: ...
    @property
    def Lexer(self) -> int: ...
    @Lexer.setter
    def Lexer(self, value: int, /) -> None: ...
    @property
    def LexerLanguage(self) -> str: ...
    @LexerLanguage.setter
    def LexerLanguage(self, value: str, /) -> None: ...
    @property
    def LineCount(self) -> int: ...
    @property
    def LineEndTypesActive(self) -> int: ...
    @property
    def LineEndTypesAllowed(self) -> int: ...
    @LineEndTypesAllowed.setter
    def LineEndTypesAllowed(self, value: int, /) -> None: ...
    @property
    def LineEndTypesSupported(self) -> int: ...
    @property
    def MainSelection(self) -> int: ...
    @MainSelection.setter
    def MainSelection(self, value: int, /) -> None: ...
    @property
    def MarginCount(self) -> int: ...
    @MarginCount.setter
    def MarginCount(self, value: int, /) -> None: ...
    @property
    def MarginLeft(self) -> int: ...
    @MarginLeft.setter
    def MarginLeft(self, value: int, /) -> None: ...
    @property
    def MarginOptions(self) -> int: ...
    @MarginOptions.setter
    def MarginOptions(self, value: int, /) -> None: ...
    @property
    def MarginRight(self) -> int: ...
    @MarginRight.setter
    def MarginRight(self, value: int, /) -> None: ...
    @property
    def Margins(self) -> Point: ...
    @property
    def MaxLineState(self) -> int: ...
    @property
    def ModEventMask(self) -> int: ...
    @ModEventMask.setter
    def ModEventMask(self, value: int, /) -> None: ...
    @property
    def Modify(self) -> bool: ...
    @property
    def MouseDownCaptures(self) -> bool: ...
    @MouseDownCaptures.setter
    def MouseDownCaptures(self, value: bool, /) -> None: ...
    @property
    def MouseDwellTime(self) -> int: ...
    @MouseDwellTime.setter
    def MouseDwellTime(self, value: int, /) -> None: ...
    @property
    def MouseSelectionRectangularSwitch(self) -> bool: ...
    @MouseSelectionRectangularSwitch.setter
    def MouseSelectionRectangularSwitch(self, value: bool, /) -> None: ...
    @property
    def MouseWheelCaptures(self) -> bool: ...
    @MouseWheelCaptures.setter
    def MouseWheelCaptures(self, value: bool, /) -> None: ...
    @property
    def MultiPaste(self) -> int: ...
    @MultiPaste.setter
    def MultiPaste(self, value: int, /) -> None: ...
    @property
    def MultipleSelection(self) -> bool: ...
    @MultipleSelection.setter
    def MultipleSelection(self, value: bool, /) -> None: ...
    @property
    def NumberOfLines(self) -> int: ...
    @property
    def Overtype(self) -> bool: ...
    @Overtype.setter
    def Overtype(self, value: bool, /) -> None: ...
    @property
    def PasteConvertEndings(self) -> bool: ...
    @PasteConvertEndings.setter
    def PasteConvertEndings(self, value: bool, /) -> None: ...
    @property
    def PhasesDraw(self) -> int: ...
    @PhasesDraw.setter
    def PhasesDraw(self, value: int, /) -> None: ...
    @property
    def PositionCacheSize(self) -> int: ...
    @PositionCacheSize.setter
    def PositionCacheSize(self, value: int, /) -> None: ...
    @property
    def PrintColourMode(self) -> int: ...
    @PrintColourMode.setter
    def PrintColourMode(self, value: int, /) -> None: ...
    @property
    def PrintMagnification(self) -> int: ...
    @PrintMagnification.setter
    def PrintMagnification(self, value: int, /) -> None: ...
    @property
    def PrintWrapMode(self) -> int: ...
    @PrintWrapMode.setter
    def PrintWrapMode(self, value: int, /) -> None: ...
    @property
    def PunctuationChars(self) -> str: ...
    @PunctuationChars.setter
    def PunctuationChars(self, value: str, /) -> None: ...
    @property
    def RangePointer(self) -> Any: ...
    @property
    def ReadOnly(self) -> bool: ...
    @ReadOnly.setter
    def ReadOnly(self, value: bool, /) -> None: ...
    @property
    def RectangularSelectionAnchor(self) -> int: ...
    @RectangularSelectionAnchor.setter
    def RectangularSelectionAnchor(self, value: int, /) -> None: ...
    @property
    def RectangularSelectionAnchorVirtualSpace(self) -> int: ...
    @RectangularSelectionAnchorVirtualSpace.setter
    def RectangularSelectionAnchorVirtualSpace(self, value: int, /) -> None: ...
    @property
    def RectangularSelectionCaret(self) -> int: ...
    @RectangularSelectionCaret.setter
    def RectangularSelectionCaret(self, value: int, /) -> None: ...
    @property
    def RectangularSelectionCaretVirtualSpace(self) -> int: ...
    @RectangularSelectionCaretVirtualSpace.setter
    def RectangularSelectionCaretVirtualSpace(self, value: int, /) -> None: ...
    @property
    def RectangularSelectionModifier(self) -> int: ...
    @RectangularSelectionModifier.setter
    def RectangularSelectionModifier(self, value: int, /) -> None: ...
    @property
    def STCCursor(self) -> int: ...
    @STCCursor.setter
    def STCCursor(self, value: int, /) -> None: ...
    @property
    def STCFocus(self) -> bool: ...
    @STCFocus.setter
    def STCFocus(self, value: bool, /) -> None: ...
    @property
    def ScrollWidth(self) -> int: ...
    @ScrollWidth.setter
    def ScrollWidth(self, value: int, /) -> None: ...
    @property
    def ScrollWidthTracking(self) -> bool: ...
    @ScrollWidthTracking.setter
    def ScrollWidthTracking(self, value: bool, /) -> None: ...
    @property
    def SearchFlags(self) -> int: ...
    @SearchFlags.setter
    def SearchFlags(self, value: int, /) -> None: ...
    @property
    def SelAlpha(self) -> int: ...
    @SelAlpha.setter
    def SelAlpha(self, value: int, /) -> None: ...
    @property
    def SelEOLFilled(self) -> bool: ...
    @SelEOLFilled.setter
    def SelEOLFilled(self, value: bool, /) -> None: ...
    @property
    def SelectedText(self) -> str: ...
    @property
    def SelectedTextRaw(self) -> CharBuffer: ...
    @property
    def SelectionEmpty(self) -> bool: ...
    @property
    def SelectionEnd(self) -> int: ...
    @SelectionEnd.setter
    def SelectionEnd(self, value: int, /) -> None: ...
    @property
    def SelectionMode(self) -> int: ...
    @SelectionMode.setter
    def SelectionMode(self, value: int, /) -> None: ...
    @property
    def SelectionStart(self) -> int: ...
    @SelectionStart.setter
    def SelectionStart(self, value: int, /) -> None: ...
    @property
    def Selections(self) -> int: ...
    @property
    def Status(self) -> int: ...
    @Status.setter
    def Status(self, value: int, /) -> None: ...
    @property
    def StringSelection(self) -> str: ...
    @property
    def StyleBits(self) -> int: ...
    @StyleBits.setter
    def StyleBits(self, value: int, /) -> None: ...
    @property
    def StyleBitsNeeded(self) -> int: ...
    @property
    def SubStyleBases(self) -> str: ...
    @property
    def TabDrawMode(self) -> int: ...
    @TabDrawMode.setter
    def TabDrawMode(self, value: int, /) -> None: ...
    @property
    def TabIndents(self) -> bool: ...
    @TabIndents.setter
    def TabIndents(self, value: bool, /) -> None: ...
    @property
    def TabWidth(self) -> int: ...
    @TabWidth.setter
    def TabWidth(self, value: int, /) -> None: ...
    @property
    def TargetEnd(self) -> int: ...
    @TargetEnd.setter
    def TargetEnd(self, value: int, /) -> None: ...
    @property
    def TargetStart(self) -> int: ...
    @TargetStart.setter
    def TargetStart(self, value: int, /) -> None: ...
    @property
    def TargetText(self) -> str: ...
    @property
    def TargetTextRaw(self) -> CharBuffer: ...
    @property
    def Technology(self) -> int: ...
    @Technology.setter
    def Technology(self, value: int, /) -> None: ...
    @property
    def Text(self) -> str: ...
    @Text.setter
    def Text(self, value: str, /) -> None: ...
    @property
    def TextLength(self) -> int: ...
    @property
    def TextRaw(self) -> str: ...
    @TextRaw.setter
    def TextRaw(self, value: str, /) -> None: ...
    @property
    def TwoPhaseDraw(self) -> bool: ...
    @TwoPhaseDraw.setter
    def TwoPhaseDraw(self, value: bool, /) -> None: ...
    @property
    def UndoCollection(self) -> bool: ...
    @UndoCollection.setter
    def UndoCollection(self, value: bool, /) -> None: ...
    @property
    def UseAntiAliasing(self) -> bool: ...
    @UseAntiAliasing.setter
    def UseAntiAliasing(self, value: bool, /) -> None: ...
    @property
    def UseHorizontalScrollBar(self) -> bool: ...
    @UseHorizontalScrollBar.setter
    def UseHorizontalScrollBar(self, value: bool, /) -> None: ...
    @property
    def UseTabs(self) -> bool: ...
    @UseTabs.setter
    def UseTabs(self, value: bool, /) -> None: ...
    @property
    def UseVerticalScrollBar(self) -> bool: ...
    @UseVerticalScrollBar.setter
    def UseVerticalScrollBar(self, value: bool, /) -> None: ...
    @property
    def Value(self) -> str: ...
    @Value.setter
    def Value(self, value: str, /) -> None: ...
    @property
    def ViewEOL(self) -> bool: ...
    @ViewEOL.setter
    def ViewEOL(self, value: bool, /) -> None: ...
    @property
    def ViewWhiteSpace(self) -> int: ...
    @ViewWhiteSpace.setter
    def ViewWhiteSpace(self, value: int, /) -> None: ...
    @property
    def VirtualSpaceOptions(self) -> int: ...
    @VirtualSpaceOptions.setter
    def VirtualSpaceOptions(self, value: int, /) -> None: ...
    @property
    def WhitespaceChars(self) -> str: ...
    @WhitespaceChars.setter
    def WhitespaceChars(self, value: str, /) -> None: ...
    @property
    def WhitespaceSize(self) -> int: ...
    @WhitespaceSize.setter
    def WhitespaceSize(self, value: int, /) -> None: ...
    @property
    def WordChars(self) -> str: ...
    @WordChars.setter
    def WordChars(self, value: str, /) -> None: ...
    @property
    def WrapIndentMode(self) -> int: ...
    @WrapIndentMode.setter
    def WrapIndentMode(self, value: int, /) -> None: ...
    @property
    def WrapMode(self) -> int: ...
    @WrapMode.setter
    def WrapMode(self, value: int, /) -> None: ...
    @property
    def WrapStartIndent(self) -> int: ...
    @WrapStartIndent.setter
    def WrapStartIndent(self, value: int, /) -> None: ...
    @property
    def WrapVisualFlags(self) -> int: ...
    @WrapVisualFlags.setter
    def WrapVisualFlags(self, value: int, /) -> None: ...
    @property
    def WrapVisualFlagsLocation(self) -> int: ...
    @WrapVisualFlagsLocation.setter
    def WrapVisualFlagsLocation(self, value: int, /) -> None: ...
    @property
    def XOffset(self) -> int: ...
    @XOffset.setter
    def XOffset(self, value: int, /) -> None: ...
    @property
    def Zoom(self) -> int: ...
    @Zoom.setter
    def Zoom(self, value: int, /) -> None: ...
# end of class StyledTextCtrl


class StyledTextEvent(CommandEvent):
    """
    StyledTextEvent(commandType=0, id=0) -> None
    StyledTextEvent(event) -> None
    
    The type of events sent from wxStyledTextCtrl.
    """

    @overload
    def __init__(self, event: StyledTextEvent) -> None:
        ...

    @overload
    def __init__(self, commandType: EventType=0, id: int=0) -> None:
        """
        StyledTextEvent(commandType=0, id=0) -> None
        StyledTextEvent(event) -> None
        
        The type of events sent from wxStyledTextCtrl.
        """

    def GetAlt(self) -> bool:
        """
        GetAlt() -> bool
        
        Returns true if the Alt key is pressed.
        """

    def GetAnnotationsLinesAdded(self) -> int:
        """
        GetAnnotationsLinesAdded() -> int
        
        Returns the number of lines that have been added to or removed from an
        annotation.
        """

    def GetControl(self) -> bool:
        """
        GetControl() -> bool
        
        Returns true if the Control key is pressed.
        """

    def GetDragFlags(self) -> int:
        """
        GetDragFlags() -> int
        
        Returns flags for the drag operation associated with this event.
        """

    def GetDragResult(self) -> DragResult:
        """
        GetDragResult() -> DragResult
        
        Returns drag result for this event.
        """

    def GetDragText(self) -> str:
        """
        GetDragText() -> str
        """

    def GetFoldLevelNow(self) -> int:
        """
        GetFoldLevelNow() -> int
        
        Returns the current fold level for the line.
        """

    def GetFoldLevelPrev(self) -> int:
        """
        GetFoldLevelPrev() -> int
        
        Returns previous fold level for the line.
        """

    def GetKey(self) -> int:
        """
        GetKey() -> int
        
        Returns the key code of the key that generated this event.
        """

    def GetLength(self) -> int:
        """
        GetLength() -> int
        
        Returns the length (number of characters) of this event.
        """

    def GetLine(self) -> int:
        """
        GetLine() -> int
        
        Returns zero-based line number for this event.
        """

    def GetLinesAdded(self) -> int:
        """
        GetLinesAdded() -> int
        
        Returns the number of lines added or deleted with this event.
        """

    def GetListCompletionMethod(self) -> int:
        """
        GetListCompletionMethod() -> int
        
        Returns a value describing the action that closed the list.
        """

    def GetListType(self) -> int:
        """
        GetListType() -> int
        
        Returns the list type for this event.
        """

    def GetLParam(self) -> int:
        """
        GetLParam() -> int
        
        Returns the value of the LParam field for this event.
        """

    def GetMargin(self) -> int:
        """
        GetMargin() -> int
        
        Returns the zero-based index of the margin that generated this event.
        """

    def GetMessage(self) -> int:
        """
        GetMessage() -> int
        
        Returns a message number while a macro is being recorded.
        """

    def GetModificationType(self) -> int:
        """
        GetModificationType() -> int
        
        Returns the modification type for this event.
        """

    def GetModifiers(self) -> int:
        """
        GetModifiers() -> int
        
        Returns the modifiers of the key press or mouse click for this event.
        """

    def GetPosition(self) -> int:
        """
        GetPosition() -> int
        
        Returns the zero-based text position associated this event.
        """

    def GetShift(self) -> bool:
        """
        GetShift() -> bool
        
        Returns true if the Shift key is pressed.
        """

    def GetText(self) -> str:
        """
        GetText() -> str
        """

    def GetToken(self) -> int:
        """
        GetToken() -> int
        
        Returns the token value for this event.
        """

    def GetUpdated(self) -> int:
        """
        GetUpdated() -> int
        
        Returns the value of the updated field for this event.
        """

    def GetWParam(self) -> int:
        """
        GetWParam() -> int
        
        Returns value of the WParam field for this event.
        """

    def GetX(self) -> int:
        """
        GetX() -> int
        
        Returns the X coordinate of the mouse for this event.
        """

    def GetY(self) -> int:
        """
        GetY() -> int
        
        Returns the Y coordinate of the mouse for this event.
        """

    def SetAnnotationLinesAdded(self, val: int) -> None:
        """
        SetAnnotationLinesAdded(val) -> None
        
        Sets the annotation lines added value for this event.
        """

    def SetDragFlags(self, flags: int) -> None:
        """
        SetDragFlags(flags) -> None
        
        Sets the drag flags for this event.
        """

    def SetDragResult(self, val: DragResult) -> None:
        """
        SetDragResult(val) -> None
        
        Sets the drag result for this event.
        """

    def SetDragText(self, val: str) -> None:
        """
        SetDragText(val) -> None
        
        Sets the drag text for this event.
        """

    def SetFoldLevelNow(self, val: int) -> None:
        """
        SetFoldLevelNow(val) -> None
        
        Sets the current fold level for this event.
        """

    def SetFoldLevelPrev(self, val: int) -> None:
        """
        SetFoldLevelPrev(val) -> None
        
        Sets the previous fold level for this event.
        """

    def SetKey(self, k: int) -> None:
        """
        SetKey(k) -> None
        
        Sets the key code for this event.
        """

    def SetLength(self, len: int) -> None:
        """
        SetLength(len) -> None
        
        Sets the length value for this event.
        """

    def SetLine(self, val: int) -> None:
        """
        SetLine(val) -> None
        
        Sets line number for this event.
        """

    def SetLinesAdded(self, num: int) -> None:
        """
        SetLinesAdded(num) -> None
        
        Sets the number of lines added for this event.
        """

    def SetListCompletionMethod(self, val: int) -> None:
        """
        SetListCompletionMethod(val) -> None
        
        Sets the list completion method for this event.
        """

    def SetListType(self, val: int) -> None:
        """
        SetListType(val) -> None
        
        Sets the list type for this event.
        """

    def SetLParam(self, val: int) -> None:
        """
        SetLParam(val) -> None
        
        Sets value of the LParam field for this event.
        """

    def SetMargin(self, val: int) -> None:
        """
        SetMargin(val) -> None
        
        Sets margin number for this event.
        """

    def SetMessage(self, val: int) -> None:
        """
        SetMessage(val) -> None
        
        Sets message number for this event.
        """

    def SetModificationType(self, t: int) -> None:
        """
        SetModificationType(t) -> None
        
        Sets the modification type for this event.
        """

    def SetModifiers(self, m: int) -> None:
        """
        SetModifiers(m) -> None
        
        Sets the value of the modifiers field for this event.
        """

    def SetPosition(self, pos: int) -> None:
        """
        SetPosition(pos) -> None
        
        Sets file position for this event.
        """

    def SetText(self, t: str) -> None:
        """
        SetText(t) -> None
        
        Sets the text for this event.
        """

    def SetToken(self, val: int) -> None:
        """
        SetToken(val) -> None
        
        Sets the token for this event.
        """

    def SetUpdated(self, val: int) -> None:
        """
        SetUpdated(val) -> None
        
        Sets the value of the updated field for this event.
        """

    def SetWParam(self, val: int) -> None:
        """
        SetWParam(val) -> None
        
        Sets the value of the WParam field for this event.
        """

    def SetX(self, val: int) -> None:
        """
        SetX(val) -> None
        
        Sets the X value for this event.
        """

    def SetY(self, val: int) -> None:
        """
        SetY(val) -> None
        
        Sets the Y value for this event.
        """
    @property
    def Alt(self) -> bool: ...
    @property
    def AnnotationsLinesAdded(self) -> int: ...
    @property
    def Control(self) -> bool: ...
    @property
    def DragFlags(self) -> int: ...
    @DragFlags.setter
    def DragFlags(self, value: int, /) -> None: ...
    @property
    def DragResult(self) -> DragResult: ...
    @DragResult.setter
    def DragResult(self, value: DragResult, /) -> None: ...
    @property
    def DragText(self) -> str: ...
    @DragText.setter
    def DragText(self, value: str, /) -> None: ...
    @property
    def FoldLevelNow(self) -> int: ...
    @FoldLevelNow.setter
    def FoldLevelNow(self, value: int, /) -> None: ...
    @property
    def FoldLevelPrev(self) -> int: ...
    @FoldLevelPrev.setter
    def FoldLevelPrev(self, value: int, /) -> None: ...
    @property
    def Key(self) -> int: ...
    @Key.setter
    def Key(self, value: int, /) -> None: ...
    @property
    def LParam(self) -> int: ...
    @LParam.setter
    def LParam(self, value: int, /) -> None: ...
    @property
    def Length(self) -> int: ...
    @Length.setter
    def Length(self, value: int, /) -> None: ...
    @property
    def Line(self) -> int: ...
    @Line.setter
    def Line(self, value: int, /) -> None: ...
    @property
    def LinesAdded(self) -> int: ...
    @LinesAdded.setter
    def LinesAdded(self, value: int, /) -> None: ...
    @property
    def ListCompletionMethod(self) -> int: ...
    @ListCompletionMethod.setter
    def ListCompletionMethod(self, value: int, /) -> None: ...
    @property
    def ListType(self) -> int: ...
    @ListType.setter
    def ListType(self, value: int, /) -> None: ...
    @property
    def Margin(self) -> int: ...
    @Margin.setter
    def Margin(self, value: int, /) -> None: ...
    @property
    def Message(self) -> int: ...
    @Message.setter
    def Message(self, value: int, /) -> None: ...
    @property
    def ModificationType(self) -> int: ...
    @ModificationType.setter
    def ModificationType(self, value: int, /) -> None: ...
    @property
    def Modifiers(self) -> int: ...
    @Modifiers.setter
    def Modifiers(self, value: int, /) -> None: ...
    @property
    def Position(self) -> int: ...
    @Position.setter
    def Position(self, value: int, /) -> None: ...
    @property
    def Shift(self) -> bool: ...
    @property
    def Text(self) -> str: ...
    @Text.setter
    def Text(self, value: str, /) -> None: ...
    @property
    def Token(self) -> int: ...
    @Token.setter
    def Token(self, value: int, /) -> None: ...
    @property
    def Updated(self) -> int: ...
    @Updated.setter
    def Updated(self, value: int, /) -> None: ...
    @property
    def WParam(self) -> int: ...
    @WParam.setter
    def WParam(self, value: int, /) -> None: ...
    @property
    def X(self) -> int: ...
    @X.setter
    def X(self, value: int, /) -> None: ...
    @property
    def Y(self) -> int: ...
    @Y.setter
    def Y(self, value: int, /) -> None: ...
# end of class StyledTextEvent


EVT_STC_CHANGE = wx.PyEventBinder( wxEVT_STC_CHANGE, 1 )
EVT_STC_STYLENEEDED = wx.PyEventBinder( wxEVT_STC_STYLENEEDED, 1 )
EVT_STC_CHARADDED = wx.PyEventBinder( wxEVT_STC_CHARADDED, 1 )
EVT_STC_SAVEPOINTREACHED = wx.PyEventBinder( wxEVT_STC_SAVEPOINTREACHED, 1 )
EVT_STC_SAVEPOINTLEFT = wx.PyEventBinder( wxEVT_STC_SAVEPOINTLEFT, 1 )
EVT_STC_ROMODIFYATTEMPT = wx.PyEventBinder( wxEVT_STC_ROMODIFYATTEMPT, 1 )
EVT_STC_KEY = wx.PyEventBinder( wxEVT_STC_KEY, 1 )
EVT_STC_DOUBLECLICK = wx.PyEventBinder( wxEVT_STC_DOUBLECLICK, 1 )
EVT_STC_UPDATEUI = wx.PyEventBinder( wxEVT_STC_UPDATEUI, 1 )
EVT_STC_MODIFIED = wx.PyEventBinder( wxEVT_STC_MODIFIED, 1 )
EVT_STC_MACRORECORD = wx.PyEventBinder( wxEVT_STC_MACRORECORD, 1 )
EVT_STC_MARGINCLICK = wx.PyEventBinder( wxEVT_STC_MARGINCLICK, 1 )
EVT_STC_NEEDSHOWN = wx.PyEventBinder( wxEVT_STC_NEEDSHOWN, 1 )
EVT_STC_PAINTED = wx.PyEventBinder( wxEVT_STC_PAINTED, 1 )
EVT_STC_USERLISTSELECTION = wx.PyEventBinder( wxEVT_STC_USERLISTSELECTION, 1 )
EVT_STC_URIDROPPED = wx.PyEventBinder( wxEVT_STC_URIDROPPED, 1 )
EVT_STC_DWELLSTART = wx.PyEventBinder( wxEVT_STC_DWELLSTART, 1 )
EVT_STC_DWELLEND = wx.PyEventBinder( wxEVT_STC_DWELLEND, 1 )
EVT_STC_START_DRAG = wx.PyEventBinder( wxEVT_STC_START_DRAG, 1 )
EVT_STC_DRAG_OVER = wx.PyEventBinder( wxEVT_STC_DRAG_OVER, 1 )
EVT_STC_DO_DROP = wx.PyEventBinder( wxEVT_STC_DO_DROP, 1 )
EVT_STC_ZOOM = wx.PyEventBinder( wxEVT_STC_ZOOM, 1 )
EVT_STC_HOTSPOT_CLICK = wx.PyEventBinder( wxEVT_STC_HOTSPOT_CLICK, 1 )
EVT_STC_HOTSPOT_DCLICK = wx.PyEventBinder( wxEVT_STC_HOTSPOT_DCLICK, 1 )
EVT_STC_HOTSPOT_RELEASE_CLICK = wx.PyEventBinder( wxEVT_STC_HOTSPOT_RELEASE_CLICK, 1 )
EVT_STC_CALLTIP_CLICK = wx.PyEventBinder( wxEVT_STC_CALLTIP_CLICK, 1 )
EVT_STC_AUTOCOMP_SELECTION = wx.PyEventBinder( wxEVT_STC_AUTOCOMP_SELECTION, 1 )
EVT_STC_INDICATOR_CLICK = wx.PyEventBinder( wxEVT_STC_INDICATOR_CLICK, 1 )
EVT_STC_INDICATOR_RELEASE = wx.PyEventBinder( wxEVT_STC_INDICATOR_RELEASE, 1 )
EVT_STC_AUTOCOMP_CANCELLED = wx.PyEventBinder( wxEVT_STC_AUTOCOMP_CANCELLED, 1 )
EVT_STC_AUTOCOMP_CHAR_DELETED = wx.PyEventBinder( wxEVT_STC_AUTOCOMP_CHAR_DELETED, 1 )
EVT_STC_CLIPBOARD_COPY = wx.PyEventBinder( wxEVT_STC_CLIPBOARD_COPY, 1)
EVT_STC_CLIPBOARD_PASTE = wx.PyEventBinder( wxEVT_STC_CLIPBOARD_PASTE, 1)
EVT_STC_AUTOCOMP_COMPLETED = wx.PyEventBinder( wxEVT_STC_AUTOCOMP_COMPLETED, 1)
EVT_STC_MARGIN_RIGHT_CLICK = wx.PyEventBinder( wxEVT_STC_MARGIN_RIGHT_CLICK, 1)
EVT_STC_AUTOCOMP_SELECTION_CHANGE = wx.PyEventBinder( wxEVT_STC_AUTOCOMP_SELECTION_CHANGE, 1)

# compatibility aliases
STC_SCMOD_NORM = STC_KEYMOD_NORM
STC_SCMOD_SHIFT = STC_KEYMOD_SHIFT
STC_SCMOD_CTRL = STC_KEYMOD_CTRL
STC_SCMOD_ALT = STC_KEYMOD_ALT
STC_SCMOD_SUPER = STC_KEYMOD_SUPER
STC_SCMOD_META = STC_KEYMOD_META
#-- end-_stc --#
