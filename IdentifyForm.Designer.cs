﻿namespace MultiFingerDemo
{
    partial class IdentifyResult
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            this.songsDataGridView = new System.Windows.Forms.DataGridView();
            ((System.ComponentModel.ISupportInitialize)(this.songsDataGridView)).BeginInit();
            this.SuspendLayout();
            // 
            // songsDataGridView
            // 
            this.songsDataGridView.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)));
            this.songsDataGridView.ColumnHeadersHeight = 37;
            this.songsDataGridView.Location = new System.Drawing.Point(12, 12);
            this.songsDataGridView.Name = "songsDataGridView";
            this.songsDataGridView.ReadOnly = true;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.songsDataGridView.RowHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.songsDataGridView.RowHeadersWidth = 100;
            this.songsDataGridView.RowTemplate.Height = 37;
            this.songsDataGridView.RowTemplate.ReadOnly = true;
            this.songsDataGridView.Size = new System.Drawing.Size(748, 450);
            this.songsDataGridView.TabIndex = 0;
            // 
            // IdentifyResult
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(772, 474);
            this.Controls.Add(this.songsDataGridView);
            this.MaximizeBox = false;
            this.Name = "IdentifyResult";
            this.Text = "IdentifyResult";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.IdentifyResult_FormClosed);
            this.Load += new System.EventHandler(this.IdentifyResult_Load);
            ((System.ComponentModel.ISupportInitialize)(this.songsDataGridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridView songsDataGridView;


    }
}