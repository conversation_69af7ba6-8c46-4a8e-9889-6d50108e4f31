#!/usr/bin/env python3
"""
Test script for the new CAPTURE_AND_SAVE API endpoint
"""
import requests
import json
import time
import sys

# API Configuration
API_BASE_URL = "http://localhost:5001"

def test_api_health():
    """Test if API is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/api/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ API Health Check:")
            print(f"   API Status: {data['data']['api_status']}")
            print(f"   TCP Connection: {data['data']['tcp_connection']}")
            return True
        else:
            print(f"❌ API Health Check Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API Health Check Error: {e}")
        return False

def test_device_status():
    """Test device status"""
    try:
        response = requests.get(f"{API_BASE_URL}/api/device/info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Device Info:")
            print(f"   Response: {data['data']}")
            return True
        else:
            print(f"❌ Device Info Failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Device Info Error: {e}")
        return False

def test_capture_and_save(person_id, finger_index, finger_name):
    """Test the new CAPTURE_AND_SAVE endpoint"""
    print(f"\n🎯 Testing CAPTURE_AND_SAVE for {person_id} - {finger_name} (index {finger_index})")
    print("=" * 60)
    
    try:
        # Prepare request data
        request_data = {
            "person_id": person_id,
            "finger_index": finger_index
        }
        
        print(f"📤 Sending request: {json.dumps(request_data, indent=2)}")
        print("👆 Please place your finger on the scanner when prompted...")
        
        # Send request
        start_time = time.time()
        response = requests.post(
            f"{API_BASE_URL}/api/fingerprint/capture-and-save",
            json=request_data,
            timeout=60  # 60 second timeout for capture + save
        )
        duration = time.time() - start_time
        
        print(f"📥 Response received in {duration:.2f} seconds")
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ SUCCESS!")
            print(f"   Response: {json.dumps(data, indent=2)}")
            
            # Extract finger details if available
            if data.get('success') and 'fingers' in data.get('data', {}):
                fingers = data['data']['fingers']
                if fingers:
                    print("\n📊 Finger Details:")
                    for finger in fingers:
                        print(f"   - {finger.get('finger_name', 'Unknown')}: Quality {finger.get('nfiq_quality', 'N/A')}, Status: {finger.get('status', 'Unknown')}")
            
            return True
        else:
            print("❌ FAILED!")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ TIMEOUT! Capture took longer than 60 seconds")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_error_cases():
    """Test error handling"""
    print("\n❌ Testing Error Cases")
    print("=" * 40)
    
    # Test missing person_id
    print("\n1️⃣ Testing missing person_id:")
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/fingerprint/capture-and-save",
            json={"finger_index": 2},
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test invalid finger_index
    print("\n2️⃣ Testing invalid finger_index:")
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/fingerprint/capture-and-save",
            json={"person_id": "TEST999", "finger_index": 99},
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")

def main():
    """Main test function"""
    print("🚀 CAPTURE_AND_SAVE API Test")
    print("=" * 50)
    
    # Test API health
    if not test_api_health():
        print("\n❌ API is not running. Please start the API first:")
        print("   cd python_client")
        print("   python start_api.py")
        sys.exit(1)
    
    # Test device status
    if not test_device_status():
        print("\n⚠️ Device may not be connected. Please ensure:")
        print("   1. C# bridge application is running")
        print("   2. Device is connected and opened")
    
    print("\n" + "="*60)
    print("🧪 STARTING CAPTURE AND SAVE TESTS")
    print("="*60)
    
    # Test individual finger capture
    test_capture_and_save("TEST001", 2, "Left Index")
    
    # Wait between tests
    print("\n⏳ Waiting 3 seconds before next test...")
    time.sleep(3)
    
    # Test slaps capture (uncomment to test)
    # test_capture_and_save("TEST002", 12, "Left 4 Fingers")
    
    # Test error cases
    test_error_cases()
    
    print("\n✅ Testing completed!")
    print("\n💡 Next steps:")
    print("   1. Check C# console for detailed logs")
    print("   2. Verify database records were created")
    print("   3. Test with web interface")

if __name__ == "__main__":
    main()
