<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Capture Function</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 5px; padding: 8px 16px; }
        #results { margin-top: 20px; border: 1px solid #ccc; padding: 10px; min-height: 200px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Test Capture Function</h1>
    
    <div>
        <button id="testCaptureBtn">Test Capture Function</button>
    </div>
    
    <div id="results">
        <p>Results will appear here...</p>
    </div>

    <script>
        const apiBaseUrl = 'http://localhost:5001';
        const resultsDiv = document.getElementById('results');
        
        // Add log function
        function log(message, isError = false) {
            const p = document.createElement('p');
            p.textContent = message;
            p.className = isError ? 'error' : 'success';
            resultsDiv.appendChild(p);
            console.log(isError ? '❌' : '✅', message);
        }
        
        // Clear results
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        // API call function
        async function callApi(endpoint, body = null, method = 'GET') {
            clearResults();
            log(`Calling ${method} ${endpoint}...`);
            
            try {
                const response = await fetch(apiBaseUrl + endpoint, {
                    method: method,
                    headers: { 'Content-Type': 'application/json' },
                    body: body ? JSON.stringify(body) : null
                });
                
                const data = await response.json();
                log(`Response status: ${response.status}`);
                log(`Response data: ${JSON.stringify(data, null, 2)}`);
                
                return data;
            } catch (error) {
                log(`Error: ${error.message}`, true);
                throw error;
            }
        }
        
        // Test the capture function
        async function testCapture() {
            try {
                // First, make sure the device is open
                log('Opening device...');
                await callApi('/api/device/open', {}, 'POST');
                
                // Then try to capture
                log('Capturing fingerprint...');
                const captureData = {
                    finger_position: 1,  // Right thumb
                    user_id: 'test_user',
                    operation_type: 'flat',
                    timeout: 15,
                    save_image: true
                };
                
                await callApi('/api/fingerprint/capture', captureData, 'POST');
            } catch (error) {
                log(`Test failed: ${error.message}`, true);
            }
        }
        
        // Event listeners
        document.getElementById('testCaptureBtn').addEventListener('click', testCapture);
    </script>
</body>
</html>