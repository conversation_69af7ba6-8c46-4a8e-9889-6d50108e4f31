# MultipleFinger Bridge Web Demo Setup Guide

## Updated Files for Python REST API

The web demo has been updated to work with the new Python REST API (port 5000) instead of the old C# REST API (port 9000).

## Files Created/Updated:

1. **`fingerprint_updated.php`** - Complete PHP web interface
2. **`app_updated.js`** - JavaScript for API communication
3. **`test_api.html`** - Simple HTML test page (no PHP required)
4. **`SETUP_GUIDE.md`** - This setup guide

## Quick Setup for WAMP Testing

### Option 1: PHP Version (Recommended)

1. **Copy files to WAMP:**
   ```
   Copy these files to: d:\wamp64\www\multifingerprint\
   - fingerprint_updated.php
   - app_updated.js
   ```

2. **Rename for use:**
   ```
   Rename fingerprint_updated.php to fingerprint.php
   Rename app_updated.js to app.js
   ```

3. **Start services:**
   - Start WAMP server
   - Start MultipleFinger.exe (C# application with TCP server)
   - Start Python REST API: `python start_api.py` (runs on port 5001)

4. **Access web demo:**
   ```
   http://localhost/multifingerprint/fingerprint.php
   ```

### Option 2: Simple HTML Version

1. **Copy files to WAMP:**
   ```
   Copy these files to: d:\wamp64\www\multifingerprint\
   - test_api.html
   - app_updated.js
   ```

2. **Start services:**
   - Start WAMP server
   - Start MultipleFinger.exe (C# application with TCP server)
   - Start Python REST API: `python start_api.py`

3. **Access test page:**
   ```
   http://localhost/multifingerprint/test_api.html
   ```

## Key Changes Made

### 1. API Endpoint Updates
- **Old:** `http://localhost:9000/api/fingerprint/...`
- **New:** `http://localhost:5001/api/fingerprint/...`

### 2. Request Format Changes
- Updated to match new Python REST API schema
- Proper JSON request/response handling
- Better error handling and validation

### 3. Finger Position Mapping
- Updated to use 1-10 finger positions (instead of complex mapping)
- Cleaner finger position names
- Consistent with new API design

### 4. New Features Added
- Health check endpoint integration
- Better status monitoring
- Improved error messages
- Real-time API connection status

## Testing Workflow

### 1. System Status Check
- Click "Refresh Status" to check API and device connection
- Green indicators = working, Red = issues

### 2. Fingerprint Capture
- Select operation type (flat, rolled, slaps)
- Click on finger positions to capture
- Captured fingerprints will show green checkmarks

### 3. Enrollment
- Enter User ID
- Captured templates will be automatically enrolled
- Success message will confirm enrollment

### 4. Identification
- Use captured templates for identification
- Results will show matching users and scores

### 5. Get Captured Data
- Retrieves data from the C# bridge application
- Useful for testing integration

## Troubleshooting

### Common Issues:

1. **"API Error: Connection refused"**
   - Python REST API is not running
   - Solution: Run `python start_api.py` in python_client directory

2. **"TCP connection disconnected"**
   - C# MultipleFinger.exe is not running
   - Solution: Start the C# application

3. **"Device not connected"**
   - Fingerprint device is not connected
   - This is normal for testing without physical device

4. **CORS errors in browser**
   - Browser blocking cross-origin requests
   - Solution: Use WAMP (localhost) instead of file:// URLs

### Debug Steps:

1. **Check Python REST API:**
   ```bash
   curl http://localhost:5001/api/health
   ```

2. **Check C# TCP Server:**
   ```bash
   cd python_client
   python test_tcp_client.py
   ```

3. **Check browser console:**
   - Open browser developer tools (F12)
   - Look for JavaScript errors in console
   - Check network tab for failed API calls

## API Endpoints Available

- `GET /api/health` - Health check
- `GET /api/status` - System status
- `POST /api/fingerprint/capture` - Capture fingerprint
- `POST /api/fingerprint/identify` - Identify fingerprint
- `POST /api/fingerprint/enroll` - Enroll fingerprint
- `GET /api/fingerprint/captured-data` - Get captured data
- `GET /api/fingerprint/positions` - Get finger positions

## Next Steps

1. Test with the updated web demo
2. Verify all operations work correctly
3. When you get a physical device, test real fingerprint operations
4. Customize the UI as needed for your specific requirements

## Support

If you encounter issues:
1. Check the browser console for JavaScript errors
2. Check Python REST API logs
3. Check C# application TCP server status
4. Verify all services are running on correct ports
