# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
The Grid and related classes in this module provide functionality similar to a
spreadsheet, where the application can display rows and columns of data of
various types, which the user can edit and otherwise interact with.
"""
#-- begin-_grid --#

import wx
#-- end-_grid --#
#-- begin-grid --#
GridNameStr: str
GRID_AUTOSIZE: int

class _GridCellFloatFormat(IntEnum):
    GRID_FLOAT_FORMAT_FIXED = auto()
    GRID_FLOAT_FORMAT_SCIENTIFIC = auto()
    GRID_FLOAT_FORMAT_COMPACT = auto()
    GRID_FLOAT_FORMAT_UPPER = auto()
    GRID_FLOAT_FORMAT_DEFAULT = auto()
GridCellFloatFormat: TypeAlias = Union[_GridCellFloatFormat, int]
GRID_FLOAT_FORMAT_FIXED = _GridCellFloatFormat.GRID_FLOAT_FORMAT_FIXED
GRID_FLOAT_FORMAT_SCIENTIFIC = _GridCellFloatFormat.GRID_FLOAT_FORMAT_SCIENTIFIC
GRID_FLOAT_FORMAT_COMPACT = _GridCellFloatFormat.GRID_FLOAT_FORMAT_COMPACT
GRID_FLOAT_FORMAT_UPPER = _GridCellFloatFormat.GRID_FLOAT_FORMAT_UPPER
GRID_FLOAT_FORMAT_DEFAULT = _GridCellFloatFormat.GRID_FLOAT_FORMAT_DEFAULT

class _GridTableRequest(IntEnum):
    GRIDTABLE_NOTIFY_ROWS_INSERTED = auto()
    GRIDTABLE_NOTIFY_ROWS_APPENDED = auto()
    GRIDTABLE_NOTIFY_ROWS_DELETED = auto()
    GRIDTABLE_NOTIFY_COLS_INSERTED = auto()
    GRIDTABLE_NOTIFY_COLS_APPENDED = auto()
    GRIDTABLE_NOTIFY_COLS_DELETED = auto()
GridTableRequest: TypeAlias = Union[_GridTableRequest, int]
GRIDTABLE_NOTIFY_ROWS_INSERTED = _GridTableRequest.GRIDTABLE_NOTIFY_ROWS_INSERTED
GRIDTABLE_NOTIFY_ROWS_APPENDED = _GridTableRequest.GRIDTABLE_NOTIFY_ROWS_APPENDED
GRIDTABLE_NOTIFY_ROWS_DELETED = _GridTableRequest.GRIDTABLE_NOTIFY_ROWS_DELETED
GRIDTABLE_NOTIFY_COLS_INSERTED = _GridTableRequest.GRIDTABLE_NOTIFY_COLS_INSERTED
GRIDTABLE_NOTIFY_COLS_APPENDED = _GridTableRequest.GRIDTABLE_NOTIFY_COLS_APPENDED
GRIDTABLE_NOTIFY_COLS_DELETED = _GridTableRequest.GRIDTABLE_NOTIFY_COLS_DELETED

class _GridRenderStyle(IntEnum):
    GRID_DRAW_ROWS_HEADER = auto()
    GRID_DRAW_COLS_HEADER = auto()
    GRID_DRAW_CELL_LINES = auto()
    GRID_DRAW_BOX_RECT = auto()
    GRID_DRAW_SELECTION = auto()
    GRID_DRAW_DEFAULT = auto()
GridRenderStyle: TypeAlias = Union[_GridRenderStyle, int]
GRID_DRAW_ROWS_HEADER = _GridRenderStyle.GRID_DRAW_ROWS_HEADER
GRID_DRAW_COLS_HEADER = _GridRenderStyle.GRID_DRAW_COLS_HEADER
GRID_DRAW_CELL_LINES = _GridRenderStyle.GRID_DRAW_CELL_LINES
GRID_DRAW_BOX_RECT = _GridRenderStyle.GRID_DRAW_BOX_RECT
GRID_DRAW_SELECTION = _GridRenderStyle.GRID_DRAW_SELECTION
GRID_DRAW_DEFAULT = _GridRenderStyle.GRID_DRAW_DEFAULT
wxEVT_GRID_CELL_LEFT_CLICK: int
wxEVT_GRID_CELL_RIGHT_CLICK: int
wxEVT_GRID_CELL_LEFT_DCLICK: int
wxEVT_GRID_CELL_RIGHT_DCLICK: int
wxEVT_GRID_LABEL_LEFT_CLICK: int
wxEVT_GRID_LABEL_RIGHT_CLICK: int
wxEVT_GRID_LABEL_LEFT_DCLICK: int
wxEVT_GRID_LABEL_RIGHT_DCLICK: int
wxEVT_GRID_ROW_SIZE: int
wxEVT_GRID_ROW_AUTO_SIZE: int
wxEVT_GRID_COL_SIZE: int
wxEVT_GRID_COL_AUTO_SIZE: int
wxEVT_GRID_RANGE_SELECTING: int
wxEVT_GRID_RANGE_SELECTED: int
wxEVT_GRID_CELL_CHANGING: int
wxEVT_GRID_CELL_CHANGED: int
wxEVT_GRID_SELECT_CELL: int
wxEVT_GRID_EDITOR_SHOWN: int
wxEVT_GRID_EDITOR_HIDDEN: int
wxEVT_GRID_EDITOR_CREATED: int
wxEVT_GRID_CELL_BEGIN_DRAG: int
wxEVT_GRID_ROW_MOVE: int
wxEVT_GRID_COL_MOVE: int
wxEVT_GRID_COL_SORT: int
wxEVT_GRID_TABBING: int

class GridCellCoords:
    """
    GridCellCoords() -> None
    GridCellCoords(row, col) -> None
    
    Represents coordinates of a grid cell.
    """

    @overload
    def __init__(self, row: int, col: int) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        GridCellCoords() -> None
        GridCellCoords(row, col) -> None
        
        Represents coordinates of a grid cell.
        """

    def GetRow(self) -> int:
        """
        GetRow() -> int
        
        Return the row of the coordinate.
        """

    def SetRow(self, n: int) -> None:
        """
        SetRow(n) -> None
        
        Set the row of the coordinate.
        """

    def GetCol(self) -> int:
        """
        GetCol() -> int
        
        Return the column of the coordinate.
        """

    def SetCol(self, n: int) -> None:
        """
        SetCol(n) -> None
        
        Set the column of the coordinate.
        """

    def Set(self, row: int, col: int) -> None:
        """
        Set(row, col) -> None
        
        Set the row and column of the coordinate.
        """

    def __eq__(self, other: Union[GridCellCoords, wx._TwoInts]) -> bool:
        """
        """

    def __ne__(self, other: Union[GridCellCoords, wx._TwoInts]) -> bool:
        """
        """
    @property
    def Col(self) -> int: ...
    @Col.setter
    def Col(self, value: int, /) -> None: ...
    @property
    def Row(self) -> int: ...
    @Row.setter
    def Row(self, value: int, /) -> None: ...

    def Get(self) -> Any:
        """
        Get() -> (row,col)
        
        Return the row and col properties as a tuple.
        """

    def GetIM(self):
        """
        Returns an immutable representation of the ``wx.GridCellCoords`` object, based on ``namedtuple``.
        
        This new object is hashable and can be used as a dictionary key,
        be added to sets, etc.  It can be converted back into a real ``wx.GridCellCoords``
        with a simple statement like this: ``obj = wx.GridCellCoords(imObj)``.
        """

    def __str__(self):
        """
        
        """

    def __repr__(self):
        """
        
        """

    def __len__(self):
        """
        
        """

    def __nonzero__(self):
        """
        
        """

    def __bool__(self):
        """
        
        """

    def __reduce__(self):
        """
        
        """

    def __getitem__(self, idx):
        """
        
        """

    def __setitem__(self, idx, val):
        """
        
        """

    __safe_for_unpickling__ = True
# end of class GridCellCoords


class GridBlockCoords:
    """
    GridBlockCoords() -> None
    GridBlockCoords(topRow, leftCol, bottomRow, rightCol) -> None
    
    Represents coordinates of a block of cells in the grid.
    """

    @overload
    def __init__(self, topRow: int, leftCol: int, bottomRow: int, rightCol: int) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        GridBlockCoords() -> None
        GridBlockCoords(topRow, leftCol, bottomRow, rightCol) -> None
        
        Represents coordinates of a block of cells in the grid.
        """

    def GetTopRow(self) -> int:
        """
        GetTopRow() -> int
        
        Return the row of the left top corner.
        """

    def SetTopRow(self, row: int) -> None:
        """
        SetTopRow(row) -> None
        
        Set the row of the left top corner.
        """

    def GetLeftCol(self) -> int:
        """
        GetLeftCol() -> int
        
        Return the column of the left top corner.
        """

    def SetLeftCol(self, col: int) -> None:
        """
        SetLeftCol(col) -> None
        
        Set the column of the left top corner.
        """

    def GetBottomRow(self) -> int:
        """
        GetBottomRow() -> int
        
        Return the row of the bottom right corner.
        """

    def SetBottomRow(self, row: int) -> None:
        """
        SetBottomRow(row) -> None
        
        Set the row of the bottom right corner.
        """

    def GetRightCol(self) -> int:
        """
        GetRightCol() -> int
        
        Return the column of the bottom right corner.
        """

    def SetRightCol(self, col: int) -> None:
        """
        SetRightCol(col) -> None
        
        Set the column of the bottom right corner.
        """

    def GetTopLeft(self) -> GridCellCoords:
        """
        GetTopLeft() -> GridCellCoords
        
        Return the coordinates of the top left corner.
        """

    def GetBottomRight(self) -> GridCellCoords:
        """
        GetBottomRight() -> GridCellCoords
        
        Return the coordinates of the bottom right corner.
        """

    def Canonicalize(self) -> GridBlockCoords:
        """
        Canonicalize() -> GridBlockCoords
        
        Return the canonicalized block where top left coordinates is less then
        bottom right coordinates.
        """

    def Intersects(self, other: GridBlockCoords) -> bool:
        """
        Intersects(other) -> bool
        
        Whether the blocks intersect.
        """

    @overload
    def Contains(self, other: GridBlockCoords) -> bool:
        ...

    @overload
    def Contains(self, cell: Union[GridCellCoords, wx._TwoInts]) -> bool:
        """
        Contains(cell) -> bool
        Contains(other) -> bool
        
        Check whether this block contains the given cell.
        """

    def Difference(self, other: GridBlockCoords, splitOrientation: int) -> GridBlockDiffResult:
        """
        Difference(other, splitOrientation) -> GridBlockDiffResult
        
        Calculates the result blocks by subtracting the other block from this
        block.
        """

    def SymDifference(self, other: GridBlockCoords) -> GridBlockDiffResult:
        """
        SymDifference(other) -> GridBlockDiffResult
        
        Calculates the symmetric difference of the blocks.
        """

    def __eq__(self, other: GridBlockCoords) -> bool:
        """
        """

    def __ne__(self, other: GridBlockCoords) -> bool:
        """
        """
    @property
    def BottomRight(self) -> GridCellCoords: ...
    @property
    def BottomRow(self) -> int: ...
    @BottomRow.setter
    def BottomRow(self, value: int, /) -> None: ...
    @property
    def LeftCol(self) -> int: ...
    @LeftCol.setter
    def LeftCol(self, value: int, /) -> None: ...
    @property
    def RightCol(self) -> int: ...
    @RightCol.setter
    def RightCol(self, value: int, /) -> None: ...
    @property
    def TopLeft(self) -> GridCellCoords: ...
    @property
    def TopRow(self) -> int: ...
    @TopRow.setter
    def TopRow(self, value: int, /) -> None: ...

    def __bool__(self) -> bool:
        """
        __bool__() -> bool
        """

    def Get(self) -> Any:
        """
        Get() -> (topRow, leftCol, bottomRow, rightCol)
        
        Return the block coordinants as a tuple.
        """

    def __str__(self):
        """
        
        """

    def __repr__(self):
        """
        
        """
# end of class GridBlockCoords


class GridBlockDiffResult:
    """
    The helper struct uses as a result type for difference functions of
    wxGridBlockCoords class.
    """

    def _getParts(self) -> Any:
        """
        _getParts() -> Any
        """
    @property
    def m_parts(self) -> Any: ...
# end of class GridBlockDiffResult


class GridBlocks:
    """
    Represents a collection of grid blocks that can be iterated over.
    """

    class iterator:
        """
        Read-only forward iterator type.
        """
    # end of class iterator


    def begin(self) -> iterator:
        """
        begin() -> iterator
        
        Return iterator corresponding to the beginning of the range.
        """

    def end(self) -> iterator:
        """
        end() -> iterator
        
        Return iterator corresponding to the end of the range.
        """

    def __iter__(self):
        """
        Returns a Python iterator for accessing the collection of grid blocks.
        """

    class PyGridBlocksIterator(object):
        "A Python iterator for GridBlocks objects"
        def __init__(self, blocks):
            self._blocks = blocks
            self._iterator = self._blocks.begin()
    
        def __next__(self):
            if self._iterator == self._blocks.end():
                raise StopIteration
            obj = self._iterator._get()
            self._iterator = self._iterator._next()
            return obj
# end of class GridBlocks


class GridCellRenderer(wx.SharedClientDataContainer, wx.RefCounter):
    """
    GridCellRenderer() -> None
    
    This class is responsible for actually drawing the cell in the grid.
    """

    def __init__(self) -> None:
        """
        GridCellRenderer() -> None
        
        This class is responsible for actually drawing the cell in the grid.
        """

    def Clone(self) -> GridCellRenderer:
        """
        Clone() -> GridCellRenderer
        
        This function must be implemented in derived classes to return a copy
        of itself.
        """

    def Draw(self, grid: Grid, attr: GridCellAttr, dc: wx.DC, rect: wx.Rect, row: int, col: int, isSelected: bool) -> None:
        """
        Draw(grid, attr, dc, rect, row, col, isSelected) -> None
        
        Draw the given cell on the provided DC inside the given rectangle using the style specified by the attribute and the default or selected state corresponding to the isSelected value.
        """

    def GetBestSize(self, grid: Grid, attr: GridCellAttr, dc: wx.DC, row: int, col: int) -> wx.Size:
        """
        GetBestSize(grid, attr, dc, row, col) -> wx.Size
        
        Get the preferred size of the cell for its contents.
        """

    def GetBestHeight(self, grid: Grid, attr: GridCellAttr, dc: wx.DC, row: int, col: int, width: int) -> int:
        """
        GetBestHeight(grid, attr, dc, row, col, width) -> int
        
        Get the preferred height of the cell at the given width.
        """

    def GetBestWidth(self, grid: Grid, attr: GridCellAttr, dc: wx.DC, row: int, col: int, height: int) -> int:
        """
        GetBestWidth(grid, attr, dc, row, col, height) -> int
        
        Get the preferred width of the cell at the given height.
        """

    def GetMaxBestSize(self, grid: Grid, attr: GridCellAttr, dc: wx.DC) -> wx.Size:
        """
        GetMaxBestSize(grid, attr, dc) -> wx.Size
        
        Get the maximum possible size for a cell using this renderer, if
        possible.
        """
# end of class GridCellRenderer


class GridCellStringRenderer(GridCellRenderer):
    """
    GridCellStringRenderer() -> None
    
    This class may be used to format string data in a cell; it is the
    default for string cells.
    """

    def __init__(self) -> None:
        """
        GridCellStringRenderer() -> None
        
        This class may be used to format string data in a cell; it is the
        default for string cells.
        """
# end of class GridCellStringRenderer


class GridCellAutoWrapStringRenderer(GridCellStringRenderer):
    """
    GridCellAutoWrapStringRenderer() -> None
    
    This class may be used to format string data in a cell.
    """

    def __init__(self) -> None:
        """
        GridCellAutoWrapStringRenderer() -> None
        
        This class may be used to format string data in a cell.
        """
# end of class GridCellAutoWrapStringRenderer


class GridCellBoolRenderer(GridCellRenderer):
    """
    GridCellBoolRenderer() -> None
    
    This class may be used to format boolean data in a cell.
    """

    def __init__(self) -> None:
        """
        GridCellBoolRenderer() -> None
        
        This class may be used to format boolean data in a cell.
        """
# end of class GridCellBoolRenderer


class GridCellDateRenderer(GridCellStringRenderer):
    """
    GridCellDateRenderer(outformat="") -> None
    
    This class may be used to show a date, without time, in a cell.
    """

    def __init__(self, outformat: str="") -> None:
        """
        GridCellDateRenderer(outformat="") -> None
        
        This class may be used to show a date, without time, in a cell.
        """

    def SetParameters(self, params: str) -> None:
        """
        SetParameters(params) -> None
        
        Sets the strftime()-like format string which will be used to render
        the date.
        """
# end of class GridCellDateRenderer


class GridCellDateTimeRenderer(GridCellDateRenderer):
    """
    GridCellDateTimeRenderer(outformat=wx.DefaultDateTimeFormat, informat=wx.DefaultDateTimeFormat) -> None
    
    This class may be used to format a date/time data in a cell.
    """

    def __init__(self, outformat: str=wx.DefaultDateTimeFormat, informat: str=wx.DefaultDateTimeFormat) -> None:
        """
        GridCellDateTimeRenderer(outformat=wx.DefaultDateTimeFormat, informat=wx.DefaultDateTimeFormat) -> None
        
        This class may be used to format a date/time data in a cell.
        """
# end of class GridCellDateTimeRenderer


class GridCellEnumRenderer(GridCellStringRenderer):
    """
    GridCellEnumRenderer(choices='') -> None
    
    This class may be used to render in a cell a number as a textual
    equivalent.
    """

    def __init__(self, choices: str='') -> None:
        """
        GridCellEnumRenderer(choices='') -> None
        
        This class may be used to render in a cell a number as a textual
        equivalent.
        """

    def SetParameters(self, params: str) -> None:
        """
        SetParameters(params) -> None
        
        Sets the comma separated string content of the enum.
        """
# end of class GridCellEnumRenderer


class GridCellFloatRenderer(GridCellStringRenderer):
    """
    GridCellFloatRenderer(width=-1, precision=-1, format=GRID_FLOAT_FORMAT_DEFAULT) -> None
    
    This class may be used to format floating point data in a cell.
    """

    def __init__(self, width: int=-1, precision: int=-1, format: int=GRID_FLOAT_FORMAT_DEFAULT) -> None:
        """
        GridCellFloatRenderer(width=-1, precision=-1, format=GRID_FLOAT_FORMAT_DEFAULT) -> None
        
        This class may be used to format floating point data in a cell.
        """

    def GetFormat(self) -> int:
        """
        GetFormat() -> int
        
        Returns the specifier used to format the data to string.
        """

    def GetPrecision(self) -> int:
        """
        GetPrecision() -> int
        
        Returns the precision.
        """

    def GetWidth(self) -> int:
        """
        GetWidth() -> int
        
        Returns the width.
        """

    def SetFormat(self, format: int) -> None:
        """
        SetFormat(format) -> None
        
        Set the format to use for display the number.
        """

    def SetParameters(self, params: str) -> None:
        """
        SetParameters(params) -> None
        
        The parameters string format is "width[,precision[,format]]" where
        format should be chosen between f|e|g|E|G (f is used by default)
        """

    def SetPrecision(self, precision: int) -> None:
        """
        SetPrecision(precision) -> None
        
        Sets the precision.
        """

    def SetWidth(self, width: int) -> None:
        """
        SetWidth(width) -> None
        
        Sets the width.
        """
    @property
    def Format(self) -> int: ...
    @Format.setter
    def Format(self, value: int, /) -> None: ...
    @property
    def Precision(self) -> int: ...
    @Precision.setter
    def Precision(self, value: int, /) -> None: ...
    @property
    def Width(self) -> int: ...
    @Width.setter
    def Width(self, value: int, /) -> None: ...
# end of class GridCellFloatRenderer


class GridCellNumberRenderer(GridCellStringRenderer):
    """
    GridCellNumberRenderer() -> None
    
    This class may be used to format integer data in a cell.
    """

    def __init__(self) -> None:
        """
        GridCellNumberRenderer() -> None
        
        This class may be used to format integer data in a cell.
        """
# end of class GridCellNumberRenderer


class GridActivationResult:
    """
    Represents the result of wxGridCellEditor::TryActivate().
    """

    @staticmethod
    def DoNothing() -> GridActivationResult:
        """
        DoNothing() -> GridActivationResult
        
        Indicate that nothing should be done and the cell shouldn't be edited
        at all.
        """

    @staticmethod
    def DoChange(newval: str) -> GridActivationResult:
        """
        DoChange(newval) -> GridActivationResult
        
        Indicate that activating the cell is possible and would change its
        value to the given one.
        """

    @staticmethod
    def DoEdit() -> GridActivationResult:
        """
        DoEdit() -> GridActivationResult
        
        Indicate that the editor control should be shown and the cell should
        be edited normally.
        """
# end of class GridActivationResult


class GridActivationSource:
    """
    Represents a source of cell activation, which may be either a user
    event (mouse or keyboard) or the program itself.
    """

    class _Origin(IntEnum):
        Program = auto()
        Key = auto()
        Mouse = auto()
    Origin: TypeAlias = Union[_Origin, int]
    Program = _Origin.Program
    Key = _Origin.Key
    Mouse = _Origin.Mouse

    def GetOrigin(self) -> Origin:
        """
        GetOrigin() -> Origin
        
        Get the origin of the activation.
        """

    def GetKeyEvent(self) -> wx.KeyEvent:
        """
        GetKeyEvent() -> wx.KeyEvent
        
        Get the key event corresponding to the key press activating the cell.
        """

    def GetMouseEvent(self) -> wx.MouseEvent:
        """
        GetMouseEvent() -> wx.MouseEvent
        
        Get the mouse event corresponding to the click activating the cell.
        """
    @property
    def KeyEvent(self) -> wx.KeyEvent: ...
    @property
    def MouseEvent(self) -> wx.MouseEvent: ...
# end of class GridActivationSource


class GridCellEditor(wx.SharedClientDataContainer, wx.RefCounter):
    """
    GridCellEditor() -> None
    
    This class is responsible for providing and manipulating the in-place
    edit controls for the grid.
    """

    def __init__(self) -> None:
        """
        GridCellEditor() -> None
        
        This class is responsible for providing and manipulating the in-place
        edit controls for the grid.
        """

    def BeginEdit(self, row: int, col: int, grid: Grid) -> None:
        """
        BeginEdit(row, col, grid) -> None
        
        Fetch the value from the table and prepare the edit control to begin
        editing.
        """

    def Clone(self) -> GridCellEditor:
        """
        Clone() -> GridCellEditor
        
        Create a new object which is the copy of this one.
        """

    def Create(self, parent: wx.Window, id: int, evtHandler: wx.EvtHandler) -> None:
        """
        Create(parent, id, evtHandler) -> None
        
        Creates the actual edit control.
        """

    def Destroy(self) -> None:
        """
        Destroy() -> None
        
        Final cleanup.
        """

    def EndEdit(self, row: int, col: int, grid: Grid, oldval: str) -> Any:
        """
        EndEdit(row, col, grid, oldval)
        
        End editing the cell.
        
        This function must check if the current value of the editing cell
        is valid and different from the original value in its string
        form. If not then simply return None.  If it has changed then
        this method should save the new value so that ApplyEdit can
        apply it later and the string representation of the new value
        should be returned.
        
        Notice that this method shoiuld not modify the grid as the
        change could still be vetoed.
        """

    def ApplyEdit(self, row: int, col: int, grid: Grid) -> None:
        """
        ApplyEdit(row, col, grid) -> None
        
        Effectively save the changes in the grid.
        """

    def HandleReturn(self, event: wx.KeyEvent) -> None:
        """
        HandleReturn(event) -> None
        
        Some types of controls on some platforms may need some help with the
        Return key.
        """

    def IsCreated(self) -> bool:
        """
        IsCreated() -> bool
        
        Returns true if the edit control has been created.
        """

    def PaintBackground(self, dc: wx.DC, rectCell: wx.Rect, attr: GridCellAttr) -> None:
        """
        PaintBackground(dc, rectCell, attr) -> None
        
        Draws the part of the cell not occupied by the control: the base class
        version just fills it with background colour from the attribute.
        """

    def Reset(self) -> None:
        """
        Reset() -> None
        
        Reset the value in the control back to its starting value.
        """

    def SetSize(self, rect: wx.Rect) -> None:
        """
        SetSize(rect) -> None
        
        Size and position the edit control.
        """

    def Show(self, show: bool, attr: Optional[GridCellAttr]=None) -> None:
        """
        Show(show, attr=None) -> None
        
        Show or hide the edit control, use the specified attributes to set colours/fonts for it.
        """

    def StartingClick(self) -> None:
        """
        StartingClick() -> None
        
        If the editor is enabled by clicking on the cell, this method will be
        called.
        """

    def StartingKey(self, event: wx.KeyEvent) -> None:
        """
        StartingKey(event) -> None
        
        If the editor is enabled by pressing keys on the grid, this will be
        called to let the editor do something about that first key if desired.
        """

    def IsAcceptedKey(self, event: wx.KeyEvent) -> bool:
        """
        IsAcceptedKey(event) -> bool
        
        Return true to allow the given key to start editing: the base class
        version only checks that the event has no modifiers.
        """

    def GetValue(self) -> str:
        """
        GetValue() -> str
        
        Returns the value currently in the editor control.
        """

    def GetWindow(self) -> wx.Window:
        """
        GetWindow() -> wx.Window
        
        Get the edit window used by this editor.
        """

    def SetWindow(self, window: wx.Window) -> None:
        """
        SetWindow(window) -> None
        
        Set the wxWindow that will be used by this cell editor for editing the
        value.
        """

    def GetControl(self) -> wx.Control:
        """
        GetControl() -> wx.Control
        
        Get the wxControl used by this editor.
        """

    def SetControl(self, control: wx.Control) -> None:
        """
        SetControl(control) -> None
        
        Set the wxControl that will be used by this cell editor for editing
        the value.
        """

    def TryActivate(self, row: int, col: int, grid: Grid, actSource: GridActivationSource) -> GridActivationResult:
        """
        TryActivate(row, col, grid, actSource) -> GridActivationResult
        
        Function allowing to create an "activatable" editor.
        """

    def DoActivate(self, row: int, col: int, grid: Grid) -> None:
        """
        DoActivate(row, col, grid) -> None
        
        Function which must be overridden for "activatable" editors.
        """
    @property
    def Control(self) -> wx.Control: ...
    @Control.setter
    def Control(self, value: wx.Control, /) -> None: ...
    @property
    def Value(self) -> str: ...
    @property
    def Window(self) -> wx.Window: ...
    @Window.setter
    def Window(self, value: wx.Window, /) -> None: ...
# end of class GridCellEditor


class GridCellActivatableEditor(GridCellEditor):
    """
    Base class for activatable editors.
    """

    def TryActivate(self, row: int, col: int, grid: Grid, actSource: GridActivationSource) -> GridActivationResult:
        """
        TryActivate(row, col, grid, actSource) -> GridActivationResult
        
        Same method as in wxGridCellEditor, but pure virtual.
        """

    def DoActivate(self, row: int, col: int, grid: Grid) -> None:
        """
        DoActivate(row, col, grid) -> None
        
        Same method as in wxGridCellEditor, but pure virtual.
        """
# end of class GridCellActivatableEditor


class GridCellTextEditor(GridCellEditor):
    """
    GridCellTextEditor(maxChars=0) -> None
    
    Grid cell editor for string/text data.
    """

    def __init__(self, maxChars: int=0) -> None:
        """
        GridCellTextEditor(maxChars=0) -> None
        
        Grid cell editor for string/text data.
        """

    def SetParameters(self, params: str) -> None:
        """
        SetParameters(params) -> None
        
        The parameters string format is "n" where n is a number representing
        the maximum width.
        """

    def SetValidator(self, validator: wx.Validator) -> None:
        """
        SetValidator(validator) -> None
        
        Set validator to validate user input.
        """

    def EndEdit(self, row: int, col: int, grid: Grid, oldval: str) -> Any:
        """
        EndEdit(row, col, grid, oldval)
        
        End editing the cell.
        
        This function must check if the current value of the editing cell
        is valid and different from the original value in its string
        form. If not then simply return None.  If it has changed then
        this method should save the new value so that ApplyEdit can
        apply it later and the string representation of the new value
        should be returned.
        
        Notice that this method shoiuld not modify the grid as the
        change could still be vetoed.
        """
# end of class GridCellTextEditor


class GridCellDateEditor(GridCellEditor):
    """
    GridCellDateEditor(format="") -> None
    
    Grid cell editor for dates.
    """

    def __init__(self, format: str="") -> None:
        """
        GridCellDateEditor(format="") -> None
        
        Grid cell editor for dates.
        """

    def EndEdit(self, row: int, col: int, grid: Grid, oldval: str) -> Any:
        """
        EndEdit(row, col, grid, oldval)
        
        End editing the cell.
        
        This function must check if the current value of the editing cell
        is valid and different from the original value in its string
        form. If not then simply return None.  If it has changed then
        this method should save the new value so that ApplyEdit can
        apply it later and the string representation of the new value
        should be returned.
        
        Notice that this method shoiuld not modify the grid as the
        change could still be vetoed.
        """
# end of class GridCellDateEditor


class GridCellAutoWrapStringEditor(GridCellTextEditor):
    """
    GridCellAutoWrapStringEditor() -> None
    
    Grid cell editor for wrappable string/text data.
    """

    def __init__(self) -> None:
        """
        GridCellAutoWrapStringEditor() -> None
        
        Grid cell editor for wrappable string/text data.
        """

    def EndEdit(self, row: int, col: int, grid: Grid, oldval: str) -> Any:
        """
        EndEdit(row, col, grid, oldval)
        
        End editing the cell.
        
        This function must check if the current value of the editing cell
        is valid and different from the original value in its string
        form. If not then simply return None.  If it has changed then
        this method should save the new value so that ApplyEdit can
        apply it later and the string representation of the new value
        should be returned.
        
        Notice that this method shoiuld not modify the grid as the
        change could still be vetoed.
        """
# end of class GridCellAutoWrapStringEditor


class GridCellBoolEditor(GridCellEditor):
    """
    GridCellBoolEditor() -> None
    
    Grid cell editor for boolean data.
    """

    def __init__(self) -> None:
        """
        GridCellBoolEditor() -> None
        
        Grid cell editor for boolean data.
        """

    @staticmethod
    def IsTrueValue(value: str) -> bool:
        """
        IsTrueValue(value) -> bool
        
        Returns true if the given value is equal to the string representation
        of the truth value we currently use (see UseStringValues()).
        """

    @staticmethod
    def UseStringValues(valueTrue: str="1", valueFalse: str='') -> None:
        """
        UseStringValues(valueTrue="1", valueFalse='') -> None
        
        This method allows you to customize the values returned by GetValue()
        for the cell using this editor.
        """

    def EndEdit(self, row: int, col: int, grid: Grid, oldval: str) -> Any:
        """
        EndEdit(row, col, grid, oldval)
        
        End editing the cell.
        
        This function must check if the current value of the editing cell
        is valid and different from the original value in its string
        form. If not then simply return None.  If it has changed then
        this method should save the new value so that ApplyEdit can
        apply it later and the string representation of the new value
        should be returned.
        
        Notice that this method shoiuld not modify the grid as the
        change could still be vetoed.
        """
# end of class GridCellBoolEditor


class GridCellChoiceEditor(GridCellEditor):
    """
    GridCellChoiceEditor(choices, allowOthers=False) -> None
    
    Grid cell editor for string data providing the user a choice from a
    list of strings.
    """

    def __init__(self, choices: List[str], allowOthers: bool=False) -> None:
        """
        GridCellChoiceEditor(choices, allowOthers=False) -> None
        
        Grid cell editor for string data providing the user a choice from a
        list of strings.
        """

    def SetParameters(self, params: str) -> None:
        """
        SetParameters(params) -> None
        
        Parameters string format is "item1[,item2[...,itemN]]".
        """

    def EndEdit(self, row: int, col: int, grid: Grid, oldval: str) -> Any:
        """
        EndEdit(row, col, grid, oldval)
        
        End editing the cell.
        
        This function must check if the current value of the editing cell
        is valid and different from the original value in its string
        form. If not then simply return None.  If it has changed then
        this method should save the new value so that ApplyEdit can
        apply it later and the string representation of the new value
        should be returned.
        
        Notice that this method shoiuld not modify the grid as the
        change could still be vetoed.
        """
# end of class GridCellChoiceEditor


class GridCellEnumEditor(GridCellChoiceEditor):
    """
    GridCellEnumEditor(choices='') -> None
    
    Grid cell editor which displays an enum number as a textual equivalent
    (e.g.
    """

    def __init__(self, choices: str='') -> None:
        """
        GridCellEnumEditor(choices='') -> None
        
        Grid cell editor which displays an enum number as a textual equivalent
        (e.g.
        """

    def EndEdit(self, row: int, col: int, grid: Grid, oldval: str) -> Any:
        """
        EndEdit(row, col, grid, oldval)
        
        End editing the cell.
        
        This function must check if the current value of the editing cell
        is valid and different from the original value in its string
        form. If not then simply return None.  If it has changed then
        this method should save the new value so that ApplyEdit can
        apply it later and the string representation of the new value
        should be returned.
        
        Notice that this method shoiuld not modify the grid as the
        change could still be vetoed.
        """
# end of class GridCellEnumEditor


class GridCellFloatEditor(GridCellTextEditor):
    """
    GridCellFloatEditor(width=-1, precision=-1, format=GRID_FLOAT_FORMAT_DEFAULT) -> None
    
    The editor for floating point numbers data.
    """

    def __init__(self, width: int=-1, precision: int=-1, format: int=GRID_FLOAT_FORMAT_DEFAULT) -> None:
        """
        GridCellFloatEditor(width=-1, precision=-1, format=GRID_FLOAT_FORMAT_DEFAULT) -> None
        
        The editor for floating point numbers data.
        """

    def SetParameters(self, params: str) -> None:
        """
        SetParameters(params) -> None
        
        The parameters string format is "width[,precision[,format]]" where
        format should be chosen between f|e|g|E|G (f is used by default)
        """

    def EndEdit(self, row: int, col: int, grid: Grid, oldval: str) -> Any:
        """
        EndEdit(row, col, grid, oldval)
        
        End editing the cell.
        
        This function must check if the current value of the editing cell
        is valid and different from the original value in its string
        form. If not then simply return None.  If it has changed then
        this method should save the new value so that ApplyEdit can
        apply it later and the string representation of the new value
        should be returned.
        
        Notice that this method shoiuld not modify the grid as the
        change could still be vetoed.
        """
# end of class GridCellFloatEditor


class GridCellNumberEditor(GridCellTextEditor):
    """
    GridCellNumberEditor(min=-1, max=-1) -> None
    
    Grid cell editor for numeric integer data.
    """

    def __init__(self, min: int=-1, max: int=-1) -> None:
        """
        GridCellNumberEditor(min=-1, max=-1) -> None
        
        Grid cell editor for numeric integer data.
        """

    def SetParameters(self, params: str) -> None:
        """
        SetParameters(params) -> None
        
        Parameters string format is "min,max".
        """

    def EndEdit(self, row: int, col: int, grid: Grid, oldval: str) -> Any:
        """
        EndEdit(row, col, grid, oldval)
        
        End editing the cell.
        
        This function must check if the current value of the editing cell
        is valid and different from the original value in its string
        form. If not then simply return None.  If it has changed then
        this method should save the new value so that ApplyEdit can
        apply it later and the string representation of the new value
        should be returned.
        
        Notice that this method shoiuld not modify the grid as the
        change could still be vetoed.
        """
# end of class GridCellNumberEditor


class GridFitMode:
    """
    GridFitMode() -> None
    
    Allows to specify the behaviour when the cell contents doesn't fit
    into its allotted space.
    """

    def __init__(self) -> None:
        """
        GridFitMode() -> None
        
        Allows to specify the behaviour when the cell contents doesn't fit
        into its allotted space.
        """

    def IsSpecified(self) -> bool:
        """
        IsSpecified() -> bool
        
        Return true if the object specifies some particular behaviour.
        """

    def IsClip(self) -> bool:
        """
        IsClip() -> bool
        
        Return true if the object specifies clipping behaviour.
        """

    def IsOverflow(self) -> bool:
        """
        IsOverflow() -> bool
        
        Return true if the object specifies overflow behaviour.
        """

    def GetEllipsizeMode(self) -> wx.EllipsizeMode:
        """
        GetEllipsizeMode() -> wx.EllipsizeMode
        
        Return ellipsize mode, possibly wxELLIPSIZE_NONE.
        """

    @staticmethod
    def Clip() -> GridFitMode:
        """
        Clip() -> GridFitMode
        
        Pseudo-constructor for object specifying clipping behaviour.
        """

    @staticmethod
    def Overflow() -> GridFitMode:
        """
        Overflow() -> GridFitMode
        
        Pseudo-constructor for object specifying overflow behaviour.
        """

    @staticmethod
    def Ellipsize(ellipsize: wx.EllipsizeMode=wx.ELLIPSIZE_END) -> GridFitMode:
        """
        Ellipsize(ellipsize=wx.ELLIPSIZE_END) -> GridFitMode
        
        Pseudo-constructor for object specifying ellipsize behaviour.
        """
    @property
    def EllipsizeMode(self) -> wx.EllipsizeMode: ...
# end of class GridFitMode


class GridCellAttr(wx.SharedClientDataContainer, wx.RefCounter):
    """
    GridCellAttr(attrDefault=None) -> None
    GridCellAttr(colText, colBack, font, hAlign, vAlign) -> None
    
    This class can be used to alter the cells' appearance in the grid by
    changing their attributes from the defaults.
    """

    class _AttrKind(IntEnum):
        Any = auto()
        Cell = auto()
        Row = auto()
        Col = auto()
        Default = auto()
        Merged = auto()
    AttrKind: TypeAlias = Union[_AttrKind, int]
    Any = _AttrKind.Any
    Cell = _AttrKind.Cell
    Row = _AttrKind.Row
    Col = _AttrKind.Col
    Default = _AttrKind.Default
    Merged = _AttrKind.Merged

    @overload
    def __init__(self, colText: wx.Colour, colBack: wx.Colour, font: wx.Font, hAlign: int, vAlign: int) -> None:
        ...

    @overload
    def __init__(self, attrDefault: Optional[GridCellAttr]=None) -> None:
        """
        GridCellAttr(attrDefault=None) -> None
        GridCellAttr(colText, colBack, font, hAlign, vAlign) -> None
        
        This class can be used to alter the cells' appearance in the grid by
        changing their attributes from the defaults.
        """

    def Clone(self) -> GridCellAttr:
        """
        Clone() -> GridCellAttr
        
        Creates a new copy of this object.
        """

    def DecRef(self) -> None:
        """
        DecRef() -> None
        
        This class is reference counted: it is created with ref count of 1, so
        calling DecRef() once will delete it.
        """

    def GetAlignment(self) -> Tuple[int, int]:
        """
        GetAlignment() -> Tuple[int, int]
        
        Get the alignment to use for the cell with the given attribute.
        """

    def GetBackgroundColour(self) -> wx.Colour:
        """
        GetBackgroundColour() -> wx.Colour
        
        Returns the background colour.
        """

    def GetEditor(self, grid: Grid, row: int, col: int) -> GridCellEditor:
        """
        GetEditor(grid, row, col) -> GridCellEditor
        
        Returns the cell editor.
        """

    def GetEditorPtr(self, grid: Grid, row: int, col: int) -> GridCellEditorPtr:
        """
        GetEditorPtr(grid, row, col) -> GridCellEditorPtr
        
        Returns the cell editor.
        """

    def GetFont(self) -> wx.Font:
        """
        GetFont() -> wx.Font
        
        Returns the font.
        """

    def GetNonDefaultAlignment(self) -> Tuple[int, int]:
        """
        GetNonDefaultAlignment() -> Tuple[int, int]
        
        Get the alignment defined by this attribute.
        """

    def GetRenderer(self, grid: Grid, row: int, col: int) -> GridCellRenderer:
        """
        GetRenderer(grid, row, col) -> GridCellRenderer
        
        Returns the cell renderer.
        """

    def GetTextColour(self) -> wx.Colour:
        """
        GetTextColour() -> wx.Colour
        
        Returns the text colour.
        """

    def HasAlignment(self) -> bool:
        """
        HasAlignment() -> bool
        
        Returns true if this attribute has a valid alignment set.
        """

    def HasBackgroundColour(self) -> bool:
        """
        HasBackgroundColour() -> bool
        
        Returns true if this attribute has a valid background colour set.
        """

    def HasEditor(self) -> bool:
        """
        HasEditor() -> bool
        
        Returns true if this attribute has a valid cell editor set.
        """

    def HasFont(self) -> bool:
        """
        HasFont() -> bool
        
        Returns true if this attribute has a valid font set.
        """

    def HasRenderer(self) -> bool:
        """
        HasRenderer() -> bool
        
        Returns true if this attribute has a valid cell renderer set.
        """

    def HasTextColour(self) -> bool:
        """
        HasTextColour() -> bool
        
        Returns true if this attribute has a valid text colour set.
        """

    def IncRef(self) -> None:
        """
        IncRef() -> None
        
        This class is reference counted: it is created with ref count of 1, so
        calling DecRef() once will delete it.
        """

    def IsReadOnly(self) -> bool:
        """
        IsReadOnly() -> bool
        
        Returns true if this cell is set as read-only.
        """

    def SetAlignment(self, hAlign: int, vAlign: int) -> None:
        """
        SetAlignment(hAlign, vAlign) -> None
        
        Sets the alignment.
        """

    def SetBackgroundColour(self, colBack: wx.Colour) -> None:
        """
        SetBackgroundColour(colBack) -> None
        
        Sets the background colour.
        """

    def SetDefAttr(self, defAttr: GridCellAttr) -> None:
        """
        SetDefAttr(defAttr) -> None
        """

    def SetEditor(self, editor: GridCellEditor) -> None:
        """
        SetEditor(editor) -> None
        
        Sets the editor to be used with the cells with this attribute.
        """

    def SetFont(self, font: wx.Font) -> None:
        """
        SetFont(font) -> None
        
        Sets the font.
        """

    def SetReadOnly(self, isReadOnly: bool=True) -> None:
        """
        SetReadOnly(isReadOnly=True) -> None
        
        Sets the cell as read-only.
        """

    def SetRenderer(self, renderer: GridCellRenderer) -> None:
        """
        SetRenderer(renderer) -> None
        
        Sets the renderer to be used for cells with this attribute.
        """

    def SetTextColour(self, colText: wx.Colour) -> None:
        """
        SetTextColour(colText) -> None
        
        Sets the text colour.
        """

    def MergeWith(self, mergefrom: GridCellAttr) -> None:
        """
        MergeWith(mergefrom) -> None
        """

    def SetSize(self, num_rows: int, num_cols: int) -> None:
        """
        SetSize(num_rows, num_cols) -> None
        """

    def SetFitMode(self, fitMode: GridFitMode) -> None:
        """
        SetFitMode(fitMode) -> None
        
        Specifies the behaviour of the cell contents if it doesn't fit into
        the available space.
        """

    def SetOverflow(self, allow: bool=True) -> None:
        """
        SetOverflow(allow=True) -> None
        
        Specifies if cells using this attribute should overflow or clip their
        contents.
        """

    def SetKind(self, kind: AttrKind) -> None:
        """
        SetKind(kind) -> None
        """

    def HasReadWriteMode(self) -> bool:
        """
        HasReadWriteMode() -> bool
        """

    def HasOverflowMode(self) -> bool:
        """
        HasOverflowMode() -> bool
        """

    def HasSize(self) -> bool:
        """
        HasSize() -> bool
        """

    def GetSize(self) -> Tuple[int, int]:
        """
        GetSize() -> Tuple[int, int]
        """

    def GetFitMode(self) -> GridFitMode:
        """
        GetFitMode() -> GridFitMode
        
        Returns the fitting mode for the cells using this attribute.
        """

    def GetOverflow(self) -> bool:
        """
        GetOverflow() -> bool
        
        Returns true if the cells using this attribute overflow into the
        neighbouring cells.
        """

    def CanOverflow(self) -> bool:
        """
        CanOverflow() -> bool
        
        Returns true if the cell will draw an overflowed text into the
        neighbouring cells.
        """

    def GetKind(self) -> AttrKind:
        """
        GetKind() -> AttrKind
        """
    @property
    def BackgroundColour(self) -> wx.Colour: ...
    @BackgroundColour.setter
    def BackgroundColour(self, value: wx.Colour, /) -> None: ...
    @property
    def FitMode(self) -> GridFitMode: ...
    @FitMode.setter
    def FitMode(self, value: GridFitMode, /) -> None: ...
    @property
    def Font(self) -> wx.Font: ...
    @Font.setter
    def Font(self, value: wx.Font, /) -> None: ...
    @property
    def Kind(self) -> AttrKind: ...
    @Kind.setter
    def Kind(self, value: AttrKind, /) -> None: ...
    @property
    def Overflow(self) -> bool: ...
    @Overflow.setter
    def Overflow(self, value: bool, /) -> None: ...
    @property
    def TextColour(self) -> wx.Colour: ...
    @TextColour.setter
    def TextColour(self, value: wx.Colour, /) -> None: ...
# end of class GridCellAttr


class GridCornerHeaderRenderer(GridHeaderLabelsRenderer):
    """
    Base class for corner header renderer.
    """
# end of class GridCornerHeaderRenderer


class GridHeaderLabelsRenderer:
    """
    Base class for header cells renderers.
    """

    def DrawBorder(self, grid: Grid, dc: wx.DC, rect: wx.Rect) -> None:
        """
        DrawBorder(grid, dc, rect) -> None
        
        Called by the grid to draw the border around the cell header.
        """

    def DrawLabel(self, grid: Grid, dc: wx.DC, value: str, rect: wx.Rect, horizAlign: int, vertAlign: int, textOrientation: int) -> None:
        """
        DrawLabel(grid, dc, value, rect, horizAlign, vertAlign, textOrientation) -> None
        
        Called by the grid to draw the specified label.
        """
# end of class GridHeaderLabelsRenderer


class GridRowHeaderRenderer(GridHeaderLabelsRenderer):
    """
    Base class for row headers renderer.
    """
# end of class GridRowHeaderRenderer


class GridColumnHeaderRenderer(GridHeaderLabelsRenderer):
    """
    Base class for column headers renderer.
    """
# end of class GridColumnHeaderRenderer


class GridRowHeaderRendererDefault(GridRowHeaderRenderer):
    """
    Default row header renderer.
    """

    def DrawBorder(self, grid: Grid, dc: wx.DC, rect: wx.Rect) -> None:
        """
        DrawBorder(grid, dc, rect) -> None
        
        Implement border drawing for the row labels.
        """
# end of class GridRowHeaderRendererDefault


class GridColumnHeaderRendererDefault(GridColumnHeaderRenderer):
    """
    Default column header renderer.
    """

    def DrawBorder(self, grid: Grid, dc: wx.DC, rect: wx.Rect) -> None:
        """
        DrawBorder(grid, dc, rect) -> None
        
        Implement border drawing for the column labels.
        """
# end of class GridColumnHeaderRendererDefault


class GridCornerHeaderRendererDefault(GridCornerHeaderRenderer):
    """
    Default corner window renderer.
    """

    def DrawBorder(self, grid: Grid, dc: wx.DC, rect: wx.Rect) -> None:
        """
        DrawBorder(grid, dc, rect) -> None
        
        Implement border drawing for the corner window.
        """
# end of class GridCornerHeaderRendererDefault


class GridCellAttrProvider(wx.ClientDataContainer):
    """
    GridCellAttrProvider() -> None
    
    Class providing attributes to be used for the grid cells.
    """

    def __init__(self) -> None:
        """
        GridCellAttrProvider() -> None
        
        Class providing attributes to be used for the grid cells.
        """

    def SetAttr(self, attr: GridCellAttr, row: int, col: int) -> None:
        """
        SetAttr(attr, row, col) -> None
        
        Set attribute for the specified cell.
        """

    def SetRowAttr(self, attr: GridCellAttr, row: int) -> None:
        """
        SetRowAttr(attr, row) -> None
        
        Set attribute for the specified row.
        """

    def SetColAttr(self, attr: GridCellAttr, col: int) -> None:
        """
        SetColAttr(attr, col) -> None
        
        Set attribute for the specified column.
        """

    def GetColumnHeaderRenderer(self, col: int) -> GridColumnHeaderRenderer:
        """
        GetColumnHeaderRenderer(col) -> GridColumnHeaderRenderer
        
        Return the renderer used for drawing column headers.
        """

    def GetRowHeaderRenderer(self, row: int) -> GridRowHeaderRenderer:
        """
        GetRowHeaderRenderer(row) -> GridRowHeaderRenderer
        
        Return the renderer used for drawing row headers.
        """

    def GetCornerRenderer(self) -> GridCornerHeaderRenderer:
        """
        GetCornerRenderer() -> GridCornerHeaderRenderer
        
        Return the renderer used for drawing the corner window.
        """

    def GetAttr(self, row: int, col: int, kind: GridCellAttr.wxAttrKind) -> GridCellAttr:
        """
        GetAttr(row, col, kind) -> GridCellAttr
        
        Get the attribute to use for the specified cell.
        """

    def GetAttrPtr(self, row: int, col: int, kind: GridCellAttr.wxAttrKind) -> GridCellAttrPtr:
        """
        GetAttrPtr(row, col, kind) -> GridCellAttrPtr
        
        Get the attribute to use for the specified cell.
        """
    @property
    def CornerRenderer(self) -> GridCornerHeaderRenderer: ...
# end of class GridCellAttrProvider


class GridTableBase(wx.Object):
    """
    GridTableBase() -> None
    
    The almost abstract base class for grid tables.
    """

    def __init__(self) -> None:
        """
        GridTableBase() -> None
        
        The almost abstract base class for grid tables.
        """

    def IsEmptyCell(self, row: int, col: int) -> bool:
        """
        IsEmptyCell(row, col) -> bool
        
        May be overridden to implement testing for empty cells.
        """

    def IsEmpty(self, coords: Union[GridCellCoords, wx._TwoInts]) -> bool:
        """
        IsEmpty(coords) -> bool
        
        Same as IsEmptyCell() but taking wxGridCellCoords.
        """

    def GetValue(self, row: int, col: int) -> Any:
        """
        GetValue(row, col) -> Any
        
        Must be overridden to implement accessing the table values as text.
        """

    def SetValue(self, row: int, col: int, value: str) -> None:
        """
        SetValue(row, col, value) -> None
        
        Must be overridden to implement setting the table values as text.
        """

    def GetTypeName(self, row: int, col: int) -> str:
        """
        GetTypeName(row, col) -> str
        
        Returns the type of the value in the given cell.
        """

    def CanGetValueAs(self, row: int, col: int, typeName: str) -> bool:
        """
        CanGetValueAs(row, col, typeName) -> bool
        
        Returns true if the value of the given cell can be accessed as if it
        were of the specified type.
        """

    def CanSetValueAs(self, row: int, col: int, typeName: str) -> bool:
        """
        CanSetValueAs(row, col, typeName) -> bool
        
        Returns true if the value of the given cell can be set as if it were
        of the specified type.
        """

    def GetValueAsLong(self, row: int, col: int) -> int:
        """
        GetValueAsLong(row, col) -> int
        
        Returns the value of the given cell as a long.
        """

    def GetValueAsDouble(self, row: int, col: int) -> float:
        """
        GetValueAsDouble(row, col) -> float
        
        Returns the value of the given cell as a double.
        """

    def GetValueAsBool(self, row: int, col: int) -> bool:
        """
        GetValueAsBool(row, col) -> bool
        
        Returns the value of the given cell as a boolean.
        """

    def SetValueAsLong(self, row: int, col: int, value: int) -> None:
        """
        SetValueAsLong(row, col, value) -> None
        
        Sets the value of the given cell as a long.
        """

    def SetValueAsDouble(self, row: int, col: int, value: float) -> None:
        """
        SetValueAsDouble(row, col, value) -> None
        
        Sets the value of the given cell as a double.
        """

    def SetValueAsBool(self, row: int, col: int, value: bool) -> None:
        """
        SetValueAsBool(row, col, value) -> None
        
        Sets the value of the given cell as a boolean.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Clear the table contents.
        """

    def InsertRows(self, pos: int=0, numRows: int=1) -> bool:
        """
        InsertRows(pos=0, numRows=1) -> bool
        
        Insert additional rows into the table.
        """

    def AppendRows(self, numRows: int=1) -> bool:
        """
        AppendRows(numRows=1) -> bool
        
        Append additional rows at the end of the table.
        """

    def DeleteRows(self, pos: int=0, numRows: int=1) -> bool:
        """
        DeleteRows(pos=0, numRows=1) -> bool
        
        Delete rows from the table.
        """

    def InsertCols(self, pos: int=0, numCols: int=1) -> bool:
        """
        InsertCols(pos=0, numCols=1) -> bool
        
        Exactly the same as InsertRows() but for columns.
        """

    def AppendCols(self, numCols: int=1) -> bool:
        """
        AppendCols(numCols=1) -> bool
        
        Exactly the same as AppendRows() but for columns.
        """

    def DeleteCols(self, pos: int=0, numCols: int=1) -> bool:
        """
        DeleteCols(pos=0, numCols=1) -> bool
        
        Exactly the same as DeleteRows() but for columns.
        """

    def GetRowLabelValue(self, row: int) -> str:
        """
        GetRowLabelValue(row) -> str
        
        Return the label of the specified row.
        """

    def GetColLabelValue(self, col: int) -> str:
        """
        GetColLabelValue(col) -> str
        
        Return the label of the specified column.
        """

    def GetCornerLabelValue(self) -> str:
        """
        GetCornerLabelValue() -> str
        
        Return the label of the grid's corner.
        """

    def SetRowLabelValue(self, row: int, label: str) -> None:
        """
        SetRowLabelValue(row, label) -> None
        
        Set the given label for the specified row.
        """

    def SetColLabelValue(self, col: int, label: str) -> None:
        """
        SetColLabelValue(col, label) -> None
        
        Exactly the same as SetRowLabelValue() but for columns.
        """

    def SetCornerLabelValue(self, : str) -> None:
        """
        SetCornerLabelValue() -> None
        
        Set the given label for the grid's corner.
        """

    def SetAttrProvider(self, attrProvider: GridCellAttrProvider) -> None:
        """
        SetAttrProvider(attrProvider) -> None
        
        Associate this attributes provider with the table.
        """

    def GetAttrProvider(self) -> GridCellAttrProvider:
        """
        GetAttrProvider() -> GridCellAttrProvider
        
        Returns the attribute provider currently being used.
        """

    def GetAttr(self, row: int, col: int, kind: GridCellAttr.wxAttrKind) -> GridCellAttr:
        """
        GetAttr(row, col, kind) -> GridCellAttr
        
        Return the attribute for the given cell.
        """

    def GetAttrPtr(self, row: int, col: int, kind: GridCellAttr.wxAttrKind) -> GridCellAttrPtr:
        """
        GetAttrPtr(row, col, kind) -> GridCellAttrPtr
        
        Return the attribute for the given cell.
        """

    def SetAttr(self, attr: GridCellAttr, row: int, col: int) -> None:
        """
        SetAttr(attr, row, col) -> None
        
        Set attribute of the specified cell.
        """

    def SetRowAttr(self, attr: GridCellAttr, row: int) -> None:
        """
        SetRowAttr(attr, row) -> None
        
        Set attribute of the specified row.
        """

    def SetColAttr(self, attr: GridCellAttr, col: int) -> None:
        """
        SetColAttr(attr, col) -> None
        
        Set attribute of the specified column.
        """

    def GetNumberRows(self) -> int:
        """
        GetNumberRows() -> int
        
        Must be overridden to return the number of rows in the table.
        """

    def GetNumberCols(self) -> int:
        """
        GetNumberCols() -> int
        
        Must be overridden to return the number of columns in the table.
        """

    def GetRowsCount(self) -> int:
        """
        GetRowsCount() -> int
        
        Return the number of rows in the table.
        """

    def GetColsCount(self) -> int:
        """
        GetColsCount() -> int
        
        Return the number of columns in the table.
        """

    def SetView(self, grid: Grid) -> None:
        """
        SetView(grid) -> None
        
        Called by the grid when the table is associated with it.
        """

    def GetView(self) -> Grid:
        """
        GetView() -> Grid
        
        Returns the last grid passed to SetView().
        """

    def CanHaveAttributes(self) -> bool:
        """
        CanHaveAttributes() -> bool
        
        Returns true if this table supports attributes or false otherwise.
        """

    def CanMeasureColUsingSameAttr(self, col: int) -> bool:
        """
        CanMeasureColUsingSameAttr(col) -> bool
        
        Override to return true if the same attribute can be used for
        measuring all cells in the given column.
        """

    def GetValueAsLong(self, row, col):
        """
        
        """

    def GetValueAsDouble(self, row, col):
        """
        
        """

    def GetValueAsBool(self, row, col):
        """
        
        """

    def SetValueAsLong(self, row, col, value):
        """
        
        """

    def SetValueAsDouble(self, row, col, value):
        """
        
        """

    def SetValueAsBool(self, row, col, value):
        """
        
        """
    @property
    def AttrProvider(self) -> GridCellAttrProvider: ...
    @AttrProvider.setter
    def AttrProvider(self, value: GridCellAttrProvider, /) -> None: ...
    @property
    def ColsCount(self) -> int: ...
    @property
    def CornerLabelValue(self) -> str: ...
    @CornerLabelValue.setter
    def CornerLabelValue(self, value: str, /) -> None: ...
    @property
    def NumberCols(self) -> int: ...
    @property
    def NumberRows(self) -> int: ...
    @property
    def RowsCount(self) -> int: ...
    @property
    def View(self) -> Grid: ...
    @View.setter
    def View(self, value: Grid, /) -> None: ...
# end of class GridTableBase


class GridTableMessage:
    """
    GridTableMessage() -> None
    GridTableMessage(table, id, comInt1=-1, comInt2=-1) -> None
    
    Message class used by the grid table to send requests and
    notifications to the grid view.
    """

    @overload
    def __init__(self, table: GridTableBase, id: int, comInt1: int=-1, comInt2: int=-1) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        GridTableMessage() -> None
        GridTableMessage(table, id, comInt1=-1, comInt2=-1) -> None
        
        Message class used by the grid table to send requests and
        notifications to the grid view.
        """

    def SetTableObject(self, table: GridTableBase) -> None:
        """
        SetTableObject(table) -> None
        
        Sets the table object.
        """

    def GetTableObject(self) -> GridTableBase:
        """
        GetTableObject() -> GridTableBase
        
        Gets the table object.
        """

    def SetId(self, id: int) -> None:
        """
        SetId(id) -> None
        
        Sets an id.
        """

    def GetId(self) -> int:
        """
        GetId() -> int
        
        Gets an id.
        """

    def SetCommandInt(self, comInt1: int) -> None:
        """
        SetCommandInt(comInt1) -> None
        
        Set the position after which the insertion/deletion occur.
        """

    def GetCommandInt(self) -> int:
        """
        GetCommandInt() -> int
        
        Get the position after which the insertion/deletion occur.
        """

    def SetCommandInt2(self, comInt2: int) -> None:
        """
        SetCommandInt2(comInt2) -> None
        
        Set the number of rows to be inserted/deleted.
        """

    def GetCommandInt2(self) -> int:
        """
        GetCommandInt2() -> int
        
        Get the number of rows to be inserted/deleted.
        """
    @property
    def CommandInt(self) -> int: ...
    @CommandInt.setter
    def CommandInt(self, value: int, /) -> None: ...
    @property
    def CommandInt2(self) -> int: ...
    @CommandInt2.setter
    def CommandInt2(self, value: int, /) -> None: ...
    @property
    def Id(self) -> int: ...
    @Id.setter
    def Id(self, value: int, /) -> None: ...
    @property
    def TableObject(self) -> GridTableBase: ...
    @TableObject.setter
    def TableObject(self, value: GridTableBase, /) -> None: ...
# end of class GridTableMessage


class GridStringTable(GridTableBase):
    """
    GridStringTable() -> None
    GridStringTable(numRows, numCols) -> None
    
    Simplest type of data table for a grid for small tables of strings
    that are stored in memory.
    """

    @overload
    def __init__(self, numRows: int, numCols: int) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        GridStringTable() -> None
        GridStringTable(numRows, numCols) -> None
        
        Simplest type of data table for a grid for small tables of strings
        that are stored in memory.
        """

    def GetNumberRows(self) -> int:
        """
        GetNumberRows() -> int
        
        Must be overridden to return the number of rows in the table.
        """

    def GetNumberCols(self) -> int:
        """
        GetNumberCols() -> int
        
        Must be overridden to return the number of columns in the table.
        """

    def GetValue(self, row: int, col: int) -> str:
        """
        GetValue(row, col) -> str
        
        Must be overridden to implement accessing the table values as text.
        """

    def SetValue(self, row: int, col: int, value: str) -> None:
        """
        SetValue(row, col, value) -> None
        
        Must be overridden to implement setting the table values as text.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Clear the table contents.
        """

    def InsertRows(self, pos: int=0, numRows: int=1) -> bool:
        """
        InsertRows(pos=0, numRows=1) -> bool
        
        Insert additional rows into the table.
        """

    def AppendRows(self, numRows: int=1) -> bool:
        """
        AppendRows(numRows=1) -> bool
        
        Append additional rows at the end of the table.
        """

    def DeleteRows(self, pos: int=0, numRows: int=1) -> bool:
        """
        DeleteRows(pos=0, numRows=1) -> bool
        
        Delete rows from the table.
        """

    def InsertCols(self, pos: int=0, numCols: int=1) -> bool:
        """
        InsertCols(pos=0, numCols=1) -> bool
        
        Exactly the same as InsertRows() but for columns.
        """

    def AppendCols(self, numCols: int=1) -> bool:
        """
        AppendCols(numCols=1) -> bool
        
        Exactly the same as AppendRows() but for columns.
        """

    def DeleteCols(self, pos: int=0, numCols: int=1) -> bool:
        """
        DeleteCols(pos=0, numCols=1) -> bool
        
        Exactly the same as DeleteRows() but for columns.
        """

    def SetRowLabelValue(self, row: int, label: str) -> None:
        """
        SetRowLabelValue(row, label) -> None
        
        Set the given label for the specified row.
        """

    def SetColLabelValue(self, col: int, label: str) -> None:
        """
        SetColLabelValue(col, label) -> None
        
        Exactly the same as SetRowLabelValue() but for columns.
        """

    def SetCornerLabelValue(self, : str) -> None:
        """
        SetCornerLabelValue() -> None
        
        Set the given label for the grid's corner.
        """

    def GetRowLabelValue(self, row: int) -> str:
        """
        GetRowLabelValue(row) -> str
        
        Return the label of the specified row.
        """

    def GetColLabelValue(self, col: int) -> str:
        """
        GetColLabelValue(col) -> str
        
        Return the label of the specified column.
        """

    def GetCornerLabelValue(self) -> str:
        """
        GetCornerLabelValue() -> str
        
        Return the label of the grid's corner.
        """
    @property
    def CornerLabelValue(self) -> str: ...
    @CornerLabelValue.setter
    def CornerLabelValue(self, value: str, /) -> None: ...
    @property
    def NumberCols(self) -> int: ...
    @property
    def NumberRows(self) -> int: ...
# end of class GridStringTable


class GridSizesInfo:
    """
    GridSizesInfo() -> None
    GridSizesInfo(defSize, allSizes) -> None
    
    wxGridSizesInfo stores information about sizes of all wxGrid rows or
    columns.
    """

    @overload
    def __init__(self, defSize: int, allSizes: List[int]) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        GridSizesInfo() -> None
        GridSizesInfo(defSize, allSizes) -> None
        
        wxGridSizesInfo stores information about sizes of all wxGrid rows or
        columns.
        """
    m_sizeDefault: int

    def GetSize(self, pos: int) -> int:
        """
        GetSize(pos) -> int
        
        Get the element size.
        """
# end of class GridSizesInfo


class Grid(wx.ScrolledCanvas):
    """
    Grid() -> None
    Grid(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.WANTS_CHARS, name=GridNameStr) -> None
    
    wxGrid and its related classes are used for displaying and editing
    tabular data.
    """

    class _GridSelectionModes(IntEnum):
        GridSelectCells = auto()
        GridSelectRows = auto()
        GridSelectColumns = auto()
        GridSelectRowsOrColumns = auto()
        GridSelectNone = auto()
    GridSelectionModes: TypeAlias = Union[_GridSelectionModes, int]
    GridSelectCells = _GridSelectionModes.GridSelectCells
    GridSelectRows = _GridSelectionModes.GridSelectRows
    GridSelectColumns = _GridSelectionModes.GridSelectColumns
    GridSelectRowsOrColumns = _GridSelectionModes.GridSelectRowsOrColumns
    GridSelectNone = _GridSelectionModes.GridSelectNone

    class _CellSpan(IntEnum):
        CellSpan_Inside = auto()
        CellSpan_None = auto()
        CellSpan_Main = auto()
    CellSpan: TypeAlias = Union[_CellSpan, int]
    CellSpan_Inside = _CellSpan.CellSpan_Inside
    CellSpan_None = _CellSpan.CellSpan_None
    CellSpan_Main = _CellSpan.CellSpan_Main

    class _TabBehaviour(IntEnum):
        Tab_Stop = auto()
        Tab_Wrap = auto()
        Tab_Leave = auto()
    TabBehaviour: TypeAlias = Union[_TabBehaviour, int]
    Tab_Stop = _TabBehaviour.Tab_Stop
    Tab_Wrap = _TabBehaviour.Tab_Wrap
    Tab_Leave = _TabBehaviour.Tab_Leave

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.WANTS_CHARS, name: str=GridNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        Grid() -> None
        Grid(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.WANTS_CHARS, name=GridNameStr) -> None
        
        wxGrid and its related classes are used for displaying and editing
        tabular data.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=wx.WANTS_CHARS, name: str=GridNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=wx.WANTS_CHARS, name=GridNameStr) -> bool
        
        Creates the grid window for an object initialized using the default constructor.
        """

    def CreateGrid(self, numRows: int, numCols: int, selmode: GridSelectionModes=GridSelectCells) -> bool:
        """
        CreateGrid(numRows, numCols, selmode=GridSelectCells) -> bool
        
        Creates a grid with the specified initial number of rows and columns.
        """

    def _SetTable(self, table: GridTableBase, takeOwnership: bool=False, selmode: GridSelectionModes=GridSelectCells) -> bool:
        """
        _SetTable(table, takeOwnership=False, selmode=GridSelectCells) -> bool
        
        Passes a pointer to a custom grid table to be used by the grid.
        """

    def AssignTable(self, table: GridTableBase, selmode: GridSelectionModes=GridSelectCells) -> None:
        """
        AssignTable(table, selmode=GridSelectCells) -> None
        
        Assigns a pointer to a custom grid table to be used by the grid.
        """

    def ProcessTableMessage(self, msg: GridTableMessage) -> bool:
        """
        ProcessTableMessage(msg) -> bool
        
        Receive and handle a message from the table.
        """

    def EnableGridLines(self, enable: bool=True) -> None:
        """
        EnableGridLines(enable=True) -> None
        
        Turns the drawing of grid lines on or off.
        """

    def GetColGridLinePen(self, col: int) -> wx.Pen:
        """
        GetColGridLinePen(col) -> wx.Pen
        
        Returns the pen used for vertical grid lines.
        """

    def GetDefaultGridLinePen(self) -> wx.Pen:
        """
        GetDefaultGridLinePen() -> wx.Pen
        
        Returns the pen used for grid lines.
        """

    def GetGridLineColour(self) -> wx.Colour:
        """
        GetGridLineColour() -> wx.Colour
        
        Returns the colour used for grid lines.
        """

    def GetRowGridLinePen(self, row: int) -> wx.Pen:
        """
        GetRowGridLinePen(row) -> wx.Pen
        
        Returns the pen used for horizontal grid lines.
        """

    def GridLinesEnabled(self) -> bool:
        """
        GridLinesEnabled() -> bool
        
        Returns true if drawing of grid lines is turned on, false otherwise.
        """

    def SetGridLineColour(self, colour: wx.Colour) -> None:
        """
        SetGridLineColour(colour) -> None
        
        Sets the colour used to draw grid lines.
        """

    def GetColLabelAlignment(self) -> Tuple[int, int]:
        """
        GetColLabelAlignment() -> Tuple[int, int]
        
        Sets the arguments to the current column label alignment values.
        """

    def GetColLabelTextOrientation(self) -> int:
        """
        GetColLabelTextOrientation() -> int
        
        Returns the orientation of the column labels (either wxHORIZONTAL or
        wxVERTICAL).
        """

    def GetColLabelValue(self, col: int) -> str:
        """
        GetColLabelValue(col) -> str
        
        Returns the specified column label.
        """

    def GetCornerLabelAlignment(self, horiz: int, vert: int) -> None:
        """
        GetCornerLabelAlignment(horiz, vert) -> None
        
        Sets the arguments to the current corner label alignment values.
        """

    def GetCornerLabelTextOrientation(self) -> int:
        """
        GetCornerLabelTextOrientation() -> int
        
        Returns the orientation of the corner label (either wxHORIZONTAL or
        wxVERTICAL).
        """

    def GetCornerLabelValue(self) -> str:
        """
        GetCornerLabelValue() -> str
        
        Returns the (top-left) corner label.
        """

    def GetLabelBackgroundColour(self) -> wx.Colour:
        """
        GetLabelBackgroundColour() -> wx.Colour
        
        Returns the colour used for the background of row and column labels.
        """

    def GetLabelFont(self) -> wx.Font:
        """
        GetLabelFont() -> wx.Font
        
        Returns the font used for row and column labels.
        """

    def GetLabelTextColour(self) -> wx.Colour:
        """
        GetLabelTextColour() -> wx.Colour
        
        Returns the colour used for row and column label text.
        """

    def GetRowLabelAlignment(self) -> Tuple[int, int]:
        """
        GetRowLabelAlignment() -> Tuple[int, int]
        
        Returns the alignment used for row labels.
        """

    def GetRowLabelValue(self, row: int) -> str:
        """
        GetRowLabelValue(row) -> str
        
        Returns the specified row label.
        """

    def HideColLabels(self) -> None:
        """
        HideColLabels() -> None
        
        Hides the column labels by calling SetColLabelSize() with a size of 0.
        """

    def HideRowLabels(self) -> None:
        """
        HideRowLabels() -> None
        
        Hides the row labels by calling SetRowLabelSize() with a size of 0.
        """

    def SetColLabelAlignment(self, horiz: int, vert: int) -> None:
        """
        SetColLabelAlignment(horiz, vert) -> None
        
        Sets the horizontal and vertical alignment of column label text.
        """

    def SetColLabelTextOrientation(self, textOrientation: int) -> None:
        """
        SetColLabelTextOrientation(textOrientation) -> None
        
        Sets the orientation of the column labels (either wxHORIZONTAL or
        wxVERTICAL).
        """

    def SetColLabelValue(self, col: int, value: str) -> None:
        """
        SetColLabelValue(col, value) -> None
        
        Set the value for the given column label.
        """

    def SetCornerLabelAlignment(self, horiz: int, vert: int) -> None:
        """
        SetCornerLabelAlignment(horiz, vert) -> None
        
        Sets the horizontal and vertical alignment of the (top-left) corner
        label text.
        """

    def SetCornerLabelTextOrientation(self, textOrientation: int) -> None:
        """
        SetCornerLabelTextOrientation(textOrientation) -> None
        
        Sets the orientation of the (top-left) corner label (either
        wxHORIZONTAL or wxVERTICAL).
        """

    def SetCornerLabelValue(self, : str) -> None:
        """
        SetCornerLabelValue() -> None
        
        Set the value for the (top-left) corner label.
        """

    def SetLabelBackgroundColour(self, colour: wx.Colour) -> None:
        """
        SetLabelBackgroundColour(colour) -> None
        
        Sets the background colour for row and column labels.
        """

    def SetLabelFont(self, font: wx.Font) -> None:
        """
        SetLabelFont(font) -> None
        
        Sets the font for row and column labels.
        """

    def SetLabelTextColour(self, colour: wx.Colour) -> None:
        """
        SetLabelTextColour(colour) -> None
        
        Sets the colour for row and column label text.
        """

    def SetRowLabelAlignment(self, horiz: int, vert: int) -> None:
        """
        SetRowLabelAlignment(horiz, vert) -> None
        
        Sets the horizontal and vertical alignment of row label text.
        """

    def SetRowLabelValue(self, row: int, value: str) -> None:
        """
        SetRowLabelValue(row, value) -> None
        
        Sets the value for the given row label.
        """

    def SetUseNativeColLabels(self, native: bool=True) -> None:
        """
        SetUseNativeColLabels(native=True) -> None
        
        Call this in order to make the column labels use a native look by
        using wxRendererNative::DrawHeaderButton() internally.
        """

    def UseNativeColHeader(self, native: bool=True) -> bool:
        """
        UseNativeColHeader(native=True) -> bool
        
        Enable the use of native header window for column labels.
        """

    def GetCellAlignment(self, row: int, col: int) -> Tuple[int, int]:
        """
        GetCellAlignment(row, col) -> Tuple[int, int]
        
        Sets the arguments to the horizontal and vertical text alignment
        values for the grid cell at the specified location.
        """

    def GetCellBackgroundColour(self, row: int, col: int) -> wx.Colour:
        """
        GetCellBackgroundColour(row, col) -> wx.Colour
        
        Returns the background colour of the cell at the specified location.
        """

    def GetCellFont(self, row: int, col: int) -> wx.Font:
        """
        GetCellFont(row, col) -> wx.Font
        
        Returns the font for text in the grid cell at the specified location.
        """

    def GetCellTextColour(self, row: int, col: int) -> wx.Colour:
        """
        GetCellTextColour(row, col) -> wx.Colour
        
        Returns the text colour for the grid cell at the specified location.
        """

    def GetDefaultCellAlignment(self) -> Tuple[int, int]:
        """
        GetDefaultCellAlignment() -> Tuple[int, int]
        
        Returns the default cell alignment.
        """

    def GetDefaultCellBackgroundColour(self) -> wx.Colour:
        """
        GetDefaultCellBackgroundColour() -> wx.Colour
        
        Returns the current default background colour for grid cells.
        """

    def GetDefaultCellFont(self) -> wx.Font:
        """
        GetDefaultCellFont() -> wx.Font
        
        Returns the current default font for grid cell text.
        """

    def GetDefaultCellTextColour(self) -> wx.Colour:
        """
        GetDefaultCellTextColour() -> wx.Colour
        
        Returns the current default colour for grid cell text.
        """

    def SetCellAlignment(self, row: int, col: int, horiz: int, vert: int) -> None:
        """
        SetCellAlignment(row, col, horiz, vert) -> None
        
        Sets the horizontal and vertical alignment for grid cell text at the
        specified location.
        """

    def SetCellBackgroundColour(self, row: int, col: int, colour: wx.Colour) -> None:
        """
        SetCellBackgroundColour(row, col, colour) -> None
        
        Set the background colour for the given cell or all cells by default.
        """

    def SetCellFont(self, row: int, col: int, font: wx.Font) -> None:
        """
        SetCellFont(row, col, font) -> None
        
        Sets the font for text in the grid cell at the specified location.
        """

    def SetCellTextColour(self, row: int, col: int, colour: wx.Colour) -> None:
        """
        SetCellTextColour(row, col, colour) -> None
        
        Sets the text colour for the given cell.
        """

    def SetDefaultCellAlignment(self, horiz: int, vert: int) -> None:
        """
        SetDefaultCellAlignment(horiz, vert) -> None
        
        Sets the default horizontal and vertical alignment for grid cell text.
        """

    def SetDefaultCellBackgroundColour(self, colour: wx.Colour) -> None:
        """
        SetDefaultCellBackgroundColour(colour) -> None
        
        Sets the default background colour for grid cells.
        """

    def SetDefaultCellFont(self, font: wx.Font) -> None:
        """
        SetDefaultCellFont(font) -> None
        
        Sets the default font to be used for grid cell text.
        """

    def SetDefaultCellTextColour(self, colour: wx.Colour) -> None:
        """
        SetDefaultCellTextColour(colour) -> None
        
        Sets the current default colour for grid cell text.
        """

    def CanEnableCellControl(self) -> bool:
        """
        CanEnableCellControl() -> bool
        
        Returns true if the in-place edit control for the current grid cell
        can be used and false otherwise.
        """

    def DisableCellEditControl(self) -> None:
        """
        DisableCellEditControl() -> None
        
        Disables in-place editing of grid cells.
        """

    def EnableCellEditControl(self, enable: bool=True) -> None:
        """
        EnableCellEditControl(enable=True) -> None
        
        Enables or disables in-place editing of grid cell data.
        """

    def EnableEditing(self, edit: bool) -> None:
        """
        EnableEditing(edit) -> None
        
        Makes the grid globally editable or read-only.
        """

    def GetCellEditor(self, row: int, col: int) -> GridCellEditor:
        """
        GetCellEditor(row, col) -> GridCellEditor
        
        Returns a pointer to the editor for the cell at the specified
        location.
        """

    def GetCellRenderer(self, row: int, col: int) -> GridCellRenderer:
        """
        GetCellRenderer(row, col) -> GridCellRenderer
        
        Returns a pointer to the renderer for the grid cell at the specified
        location.
        """

    @overload
    def GetCellValue(self, coords: Union[GridCellCoords, wx._TwoInts]) -> str:
        ...

    @overload
    def GetCellValue(self, row: int, col: int) -> str:
        """
        GetCellValue(row, col) -> str
        GetCellValue(coords) -> str
        
        Returns the string contained in the cell at the specified location.
        """

    def GetDefaultEditor(self) -> GridCellEditor:
        """
        GetDefaultEditor() -> GridCellEditor
        
        Returns a pointer to the current default grid cell editor.
        """

    @overload
    def GetDefaultEditorForCell(self, c: Union[GridCellCoords, wx._TwoInts]) -> GridCellEditor:
        ...

    @overload
    def GetDefaultEditorForCell(self, row: int, col: int) -> GridCellEditor:
        """
        GetDefaultEditorForCell(row, col) -> GridCellEditor
        GetDefaultEditorForCell(c) -> GridCellEditor
        
        Returns the default editor for the specified cell.
        """

    def GetDefaultEditorForType(self, typeName: str) -> GridCellEditor:
        """
        GetDefaultEditorForType(typeName) -> GridCellEditor
        
        Returns the default editor for the cells containing values of the
        given type.
        """

    def GetDefaultRenderer(self) -> GridCellRenderer:
        """
        GetDefaultRenderer() -> GridCellRenderer
        
        Returns a pointer to the current default grid cell renderer.
        """

    def GetDefaultRendererForCell(self, row: int, col: int) -> GridCellRenderer:
        """
        GetDefaultRendererForCell(row, col) -> GridCellRenderer
        
        Returns the default renderer for the given cell.
        """

    def GetDefaultRendererForType(self, typeName: str) -> GridCellRenderer:
        """
        GetDefaultRendererForType(typeName) -> GridCellRenderer
        
        Returns the default renderer for the cell containing values of the
        given type.
        """

    def HideCellEditControl(self) -> None:
        """
        HideCellEditControl() -> None
        
        Hides the in-place cell edit control.
        """

    def IsCellEditControlEnabled(self) -> bool:
        """
        IsCellEditControlEnabled() -> bool
        
        Returns true if the in-place edit control is currently enabled.
        """

    def IsCellEditControlShown(self) -> bool:
        """
        IsCellEditControlShown() -> bool
        
        Returns true if the in-place edit control is currently shown.
        """

    def IsCurrentCellReadOnly(self) -> bool:
        """
        IsCurrentCellReadOnly() -> bool
        
        Returns true if the current cell is read-only.
        """

    def IsEditable(self) -> bool:
        """
        IsEditable() -> bool
        
        Returns false if the whole grid has been set as read-only or true
        otherwise.
        """

    def IsReadOnly(self, row: int, col: int) -> bool:
        """
        IsReadOnly(row, col) -> bool
        
        Returns true if the cell at the specified location can't be edited.
        """

    def RegisterDataType(self, typeName: str, renderer: GridCellRenderer, editor: GridCellEditor) -> None:
        """
        RegisterDataType(typeName, renderer, editor) -> None
        
        Register a new data type.
        """

    def SaveEditControlValue(self) -> None:
        """
        SaveEditControlValue() -> None
        
        Sets the value of the current grid cell to the current in-place edit
        control value.
        """

    def SetCellEditor(self, row: int, col: int, editor: GridCellEditor) -> None:
        """
        SetCellEditor(row, col, editor) -> None
        
        Sets the editor for the grid cell at the specified location.
        """

    def SetCellRenderer(self, row: int, col: int, renderer: GridCellRenderer) -> None:
        """
        SetCellRenderer(row, col, renderer) -> None
        
        Sets the renderer for the grid cell at the specified location.
        """

    @overload
    def SetCellValue(self, coords: Union[GridCellCoords, wx._TwoInts], s: str) -> None:
        ...

    @overload
    def SetCellValue(self, row: int, col: int, s: str) -> None:
        """
        SetCellValue(row, col, s) -> None
        SetCellValue(coords, s) -> None
        
        Sets the string value for the cell at the specified location.
        """

    def SetColFormatBool(self, col: int) -> None:
        """
        SetColFormatBool(col) -> None
        
        Sets the specified column to display boolean values.
        """

    def SetColFormatCustom(self, col: int, typeName: str) -> None:
        """
        SetColFormatCustom(col, typeName) -> None
        
        Sets the specified column to display data in a custom format.
        """

    def SetColFormatFloat(self, col: int, width: int=-1, precision: int=-1) -> None:
        """
        SetColFormatFloat(col, width=-1, precision=-1) -> None
        
        Sets the specified column to display floating point values with the
        given width and precision.
        """

    def SetColFormatNumber(self, col: int) -> None:
        """
        SetColFormatNumber(col) -> None
        
        Sets the specified column to display integer values.
        """

    def SetColFormatDate(self, col: int, format: str="") -> None:
        """
        SetColFormatDate(col, format="") -> None
        
        Sets the specified column to display date values.
        """

    def SetDefaultEditor(self, editor: GridCellEditor) -> None:
        """
        SetDefaultEditor(editor) -> None
        
        Sets the default editor for grid cells.
        """

    def SetDefaultRenderer(self, renderer: GridCellRenderer) -> None:
        """
        SetDefaultRenderer(renderer) -> None
        
        Sets the default renderer for grid cells.
        """

    def SetReadOnly(self, row: int, col: int, isReadOnly: bool=True) -> None:
        """
        SetReadOnly(row, col, isReadOnly=True) -> None
        
        Makes the cell at the specified location read-only or editable.
        """

    def ShowCellEditControl(self) -> None:
        """
        ShowCellEditControl() -> None
        
        Displays the active in-place cell edit control for the current cell
        after it was hidden.
        """

    def AutoSize(self) -> None:
        """
        AutoSize() -> None
        
        Automatically sets the height and width of all rows and columns to fit
        their contents.
        """

    def AutoSizeColLabelSize(self, col: int) -> None:
        """
        AutoSizeColLabelSize(col) -> None
        
        Automatically adjusts width of the column to fit its label.
        """

    def AutoSizeColumn(self, col: int, setAsMin: bool=True) -> None:
        """
        AutoSizeColumn(col, setAsMin=True) -> None
        
        Automatically sizes the column to fit its contents.
        """

    def AutoSizeColumns(self, setAsMin: bool=True) -> None:
        """
        AutoSizeColumns(setAsMin=True) -> None
        
        Automatically sizes all columns to fit their contents.
        """

    def AutoSizeRow(self, row: int, setAsMin: bool=True) -> None:
        """
        AutoSizeRow(row, setAsMin=True) -> None
        
        Automatically sizes the row to fit its contents.
        """

    def AutoSizeRowLabelSize(self, col: int) -> None:
        """
        AutoSizeRowLabelSize(col) -> None
        
        Automatically adjusts height of the row to fit its label.
        """

    def AutoSizeRows(self, setAsMin: bool=True) -> None:
        """
        AutoSizeRows(setAsMin=True) -> None
        
        Automatically sizes all rows to fit their contents.
        """

    def GetCellFitMode(self, row: int, col: int) -> GridFitMode:
        """
        GetCellFitMode(row, col) -> GridFitMode
        
        Returns the cell fitting mode.
        """

    def GetCellOverflow(self, row: int, col: int) -> bool:
        """
        GetCellOverflow(row, col) -> bool
        
        Returns true if the cell value can overflow.
        """

    def GetColLabelSize(self) -> int:
        """
        GetColLabelSize() -> int
        
        Returns the current height of the column labels.
        """

    def GetColMinimalAcceptableWidth(self) -> int:
        """
        GetColMinimalAcceptableWidth() -> int
        
        Returns the minimal width to which a column may be resized.
        """

    def GetColSize(self, col: int) -> int:
        """
        GetColSize(col) -> int
        
        Returns the width of the specified column.
        """

    def IsColShown(self, col: int) -> bool:
        """
        IsColShown(col) -> bool
        
        Returns true if the specified column is not currently hidden.
        """

    def GetDefaultCellFitMode(self) -> GridFitMode:
        """
        GetDefaultCellFitMode() -> GridFitMode
        
        Returns the default cell fitting mode.
        """

    def GetDefaultCellOverflow(self) -> bool:
        """
        GetDefaultCellOverflow() -> bool
        
        Returns true if the cells can overflow by default.
        """

    def GetDefaultColLabelSize(self) -> int:
        """
        GetDefaultColLabelSize() -> int
        
        Returns the default height for column labels.
        """

    def GetDefaultColSize(self) -> int:
        """
        GetDefaultColSize() -> int
        
        Returns the current default width for grid columns.
        """

    def GetDefaultRowLabelSize(self) -> int:
        """
        GetDefaultRowLabelSize() -> int
        
        Returns the default width for the row labels.
        """

    def GetDefaultRowSize(self) -> int:
        """
        GetDefaultRowSize() -> int
        
        Returns the current default height for grid rows.
        """

    def GetRowMinimalAcceptableHeight(self) -> int:
        """
        GetRowMinimalAcceptableHeight() -> int
        
        Returns the minimal size to which rows can be resized.
        """

    def GetRowLabelSize(self) -> int:
        """
        GetRowLabelSize() -> int
        
        Returns the current width of the row labels.
        """

    def GetRowSize(self, row: int) -> int:
        """
        GetRowSize(row) -> int
        
        Returns the height of the specified row.
        """

    def IsRowShown(self, row: int) -> bool:
        """
        IsRowShown(row) -> bool
        
        Returns true if the specified row is not currently hidden.
        """

    def SetCellFitMode(self, row: int, col: int, fitMode: GridFitMode) -> None:
        """
        SetCellFitMode(row, col, fitMode) -> None
        
        Specifies the behaviour of the cell contents if it doesn't fit into
        the available space.
        """

    def SetCellOverflow(self, row: int, col: int, allow: bool) -> None:
        """
        SetCellOverflow(row, col, allow) -> None
        
        Sets the overflow permission of the cell.
        """

    def SetColLabelSize(self, height: int) -> None:
        """
        SetColLabelSize(height) -> None
        
        Sets the height of the column labels.
        """

    def SetColMinimalAcceptableWidth(self, width: int) -> None:
        """
        SetColMinimalAcceptableWidth(width) -> None
        
        Sets the minimal width to which the user can resize columns.
        """

    def SetColMinimalWidth(self, col: int, width: int) -> None:
        """
        SetColMinimalWidth(col, width) -> None
        
        Sets the minimal width for the specified column col.
        """

    def SetColSize(self, col: int, width: int) -> None:
        """
        SetColSize(col, width) -> None
        
        Sets the width of the specified column.
        """

    def HideCol(self, col: int) -> None:
        """
        HideCol(col) -> None
        
        Hides the specified column.
        """

    def ShowCol(self, col: int) -> None:
        """
        ShowCol(col) -> None
        
        Shows the previously hidden column by resizing it to non-0 size.
        """

    def SetDefaultCellFitMode(self, fitMode: GridFitMode) -> None:
        """
        SetDefaultCellFitMode(fitMode) -> None
        
        Specifies the default behaviour of the cell contents if it doesn't fit
        into the available space.
        """

    def SetDefaultCellOverflow(self, allow: bool) -> None:
        """
        SetDefaultCellOverflow(allow) -> None
        
        Sets the default overflow permission of the cells.
        """

    def SetDefaultColSize(self, width: int, resizeExistingCols: bool=False) -> None:
        """
        SetDefaultColSize(width, resizeExistingCols=False) -> None
        
        Sets the default width for columns in the grid.
        """

    def SetDefaultRowSize(self, height: int, resizeExistingRows: bool=False) -> None:
        """
        SetDefaultRowSize(height, resizeExistingRows=False) -> None
        
        Sets the default height for rows in the grid.
        """

    def SetRowLabelSize(self, width: int) -> None:
        """
        SetRowLabelSize(width) -> None
        
        Sets the width of the row labels.
        """

    def SetRowMinimalAcceptableHeight(self, height: int) -> None:
        """
        SetRowMinimalAcceptableHeight(height) -> None
        
        Sets the minimal row height used by default.
        """

    def SetRowMinimalHeight(self, row: int, height: int) -> None:
        """
        SetRowMinimalHeight(row, height) -> None
        
        Sets the minimal height for the specified row.
        """

    def SetRowSize(self, row: int, height: int) -> None:
        """
        SetRowSize(row, height) -> None
        
        Sets the height of the specified row.
        """

    def HideRow(self, col: int) -> None:
        """
        HideRow(col) -> None
        
        Hides the specified row.
        """

    def ShowRow(self, col: int) -> None:
        """
        ShowRow(col) -> None
        
        Shows the previously hidden row.
        """

    def GetColSizes(self) -> GridSizesInfo:
        """
        GetColSizes() -> GridSizesInfo
        
        Get size information for all columns at once.
        """

    def GetRowSizes(self) -> GridSizesInfo:
        """
        GetRowSizes() -> GridSizesInfo
        
        Get size information for all row at once.
        """

    def SetColSizes(self, sizeInfo: GridSizesInfo) -> None:
        """
        SetColSizes(sizeInfo) -> None
        
        Restore all columns sizes.
        """

    def SetRowSizes(self, sizeInfo: GridSizesInfo) -> None:
        """
        SetRowSizes(sizeInfo) -> None
        
        Restore all rows sizes.
        """

    def SetCellSize(self, row: int, col: int, num_rows: int, num_cols: int) -> None:
        """
        SetCellSize(row, col, num_rows, num_cols) -> None
        
        Set the size of the cell.
        """

    @overload
    def GetCellSize(self, coords: Union[GridCellCoords, wx._TwoInts]) -> wx.Size:
        ...

    @overload
    def GetCellSize(self, row: int, col: int, num_rows: int, num_cols: int) -> CellSpan:
        """
        GetCellSize(row, col, num_rows, num_cols) -> CellSpan
        GetCellSize(coords) -> wx.Size
        
        Get the size of the cell in number of cells covered by it.
        """

    def CanDragCell(self) -> bool:
        """
        CanDragCell() -> bool
        
        Return true if the dragging of cells is enabled or false otherwise.
        """

    def CanDragColMove(self) -> bool:
        """
        CanDragColMove() -> bool
        
        Returns true if columns can be moved by dragging with the mouse.
        """

    def CanDragColSize(self, col: int) -> bool:
        """
        CanDragColSize(col) -> bool
        
        Returns true if the given column can be resized by dragging with the
        mouse.
        """

    def CanDragGridColEdges(self) -> bool:
        """
        CanDragGridColEdges() -> bool
        
        Return true if column edges inside the grid can be dragged to resize
        the rows.
        """

    def CanDragGridRowEdges(self) -> bool:
        """
        CanDragGridRowEdges() -> bool
        
        Return true if row edges inside the grid can be dragged to resize the
        rows.
        """

    def CanDragGridSize(self) -> bool:
        """
        CanDragGridSize() -> bool
        
        Return true if the dragging of grid lines to resize rows and columns
        is enabled or false otherwise.
        """

    def CanDragRowMove(self) -> bool:
        """
        CanDragRowMove() -> bool
        
        Returns true if rows can be moved by dragging with the mouse.
        """

    def CanDragRowSize(self, row: int) -> bool:
        """
        CanDragRowSize(row) -> bool
        
        Returns true if the given row can be resized by dragging with the
        mouse.
        """

    def CanHideColumns(self) -> bool:
        """
        CanHideColumns() -> bool
        
        Returns true if columns can be hidden from the popup menu of the
        native header.
        """

    def DisableColResize(self, col: int) -> None:
        """
        DisableColResize(col) -> None
        
        Disable interactive resizing of the specified column.
        """

    def DisableRowResize(self, row: int) -> None:
        """
        DisableRowResize(row) -> None
        
        Disable interactive resizing of the specified row.
        """

    def DisableDragColMove(self) -> None:
        """
        DisableDragColMove() -> None
        
        Disables column moving by dragging with the mouse.
        """

    def DisableDragRowMove(self) -> None:
        """
        DisableDragRowMove() -> None
        
        Disables row moving by dragging with the mouse.
        """

    def DisableDragColSize(self) -> None:
        """
        DisableDragColSize() -> None
        
        Disables column sizing by dragging with the mouse.
        """

    def DisableDragGridSize(self) -> None:
        """
        DisableDragGridSize() -> None
        
        Disable mouse dragging of grid lines to resize rows and columns.
        """

    def DisableDragRowSize(self) -> None:
        """
        DisableDragRowSize() -> None
        
        Disables row sizing by dragging with the mouse.
        """

    def DisableHidingColumns(self) -> None:
        """
        DisableHidingColumns() -> None
        
        Disables column hiding from the header popup menu.
        """

    def EnableDragCell(self, enable: bool=True) -> None:
        """
        EnableDragCell(enable=True) -> None
        
        Enables or disables cell dragging with the mouse.
        """

    def EnableDragColMove(self, enable: bool=True) -> bool:
        """
        EnableDragColMove(enable=True) -> bool
        
        Enables or disables column moving by dragging with the mouse.
        """

    def EnableDragRowMove(self, enable: bool=True) -> bool:
        """
        EnableDragRowMove(enable=True) -> bool
        
        Enables or disables row moving by dragging with the mouse.
        """

    def EnableDragColSize(self, enable: bool=True) -> None:
        """
        EnableDragColSize(enable=True) -> None
        
        Enables or disables column sizing by dragging with the mouse.
        """

    def EnableDragGridSize(self, enable: bool=True) -> None:
        """
        EnableDragGridSize(enable=True) -> None
        
        Enables or disables row and column resizing by dragging gridlines with
        the mouse.
        """

    def EnableDragRowSize(self, enable: bool=True) -> None:
        """
        EnableDragRowSize(enable=True) -> None
        
        Enables or disables row sizing by dragging with the mouse.
        """

    def EnableHidingColumns(self, enable: bool=True) -> bool:
        """
        EnableHidingColumns(enable=True) -> bool
        
        Enables or disables column hiding from the header popup menu.
        """

    def GetColAt(self, colPos: int) -> int:
        """
        GetColAt(colPos) -> int
        
        Returns the column ID of the specified column position.
        """

    def GetColPos(self, colID: int) -> int:
        """
        GetColPos(colID) -> int
        
        Returns the position of the specified column.
        """

    def SetColPos(self, colID: int, newPos: int) -> None:
        """
        SetColPos(colID, newPos) -> None
        
        Sets the position of the specified column.
        """

    def SetColumnsOrder(self, order: List[int]) -> None:
        """
        SetColumnsOrder(order) -> None
        
        Sets the positions of all columns at once.
        """

    def ResetColPos(self) -> None:
        """
        ResetColPos() -> None
        
        Resets the position of the columns to the default.
        """

    def GetRowAt(self, rowPos: int) -> int:
        """
        GetRowAt(rowPos) -> int
        
        Returns the row ID of the specified row position.
        """

    def GetRowPos(self, rowID: int) -> int:
        """
        GetRowPos(rowID) -> int
        
        Returns the position of the specified row.
        """

    def SetRowPos(self, rowID: int, newPos: int) -> None:
        """
        SetRowPos(rowID, newPos) -> None
        
        Sets the position of the specified row.
        """

    def SetRowsOrder(self, order: List[int]) -> None:
        """
        SetRowsOrder(order) -> None
        
        Sets the positions of all rows at once.
        """

    def ResetRowPos(self) -> None:
        """
        ResetRowPos() -> None
        
        Resets the position of the rows to the default.
        """

    def GetGridCursorCoords(self) -> GridCellCoords:
        """
        GetGridCursorCoords() -> GridCellCoords
        
        Returns the current grid cursor position.
        """

    def GetGridCursorCol(self) -> int:
        """
        GetGridCursorCol() -> int
        
        Returns the current grid cell column position.
        """

    def GetGridCursorRow(self) -> int:
        """
        GetGridCursorRow() -> int
        
        Returns the current grid cell row position.
        """

    @overload
    def GoToCell(self, coords: Union[GridCellCoords, wx._TwoInts]) -> None:
        ...

    @overload
    def GoToCell(self, row: int, col: int) -> None:
        """
        GoToCell(row, col) -> None
        GoToCell(coords) -> None
        
        Make the given cell current and ensure it is visible.
        """

    def MoveCursorDown(self, expandSelection: bool) -> bool:
        """
        MoveCursorDown(expandSelection) -> bool
        
        Moves the grid cursor down by one row.
        """

    def MoveCursorDownBlock(self, expandSelection: bool) -> bool:
        """
        MoveCursorDownBlock(expandSelection) -> bool
        
        Moves the grid cursor down in the current column such that it skips to
        the beginning or end of a block of non-empty cells.
        """

    def MoveCursorLeft(self, expandSelection: bool) -> bool:
        """
        MoveCursorLeft(expandSelection) -> bool
        
        Moves the grid cursor left by one column.
        """

    def MoveCursorLeftBlock(self, expandSelection: bool) -> bool:
        """
        MoveCursorLeftBlock(expandSelection) -> bool
        
        Moves the grid cursor left in the current row such that it skips to
        the beginning or end of a block of non-empty cells.
        """

    def MoveCursorRight(self, expandSelection: bool) -> bool:
        """
        MoveCursorRight(expandSelection) -> bool
        
        Moves the grid cursor right by one column.
        """

    def MoveCursorRightBlock(self, expandSelection: bool) -> bool:
        """
        MoveCursorRightBlock(expandSelection) -> bool
        
        Moves the grid cursor right in the current row such that it skips to
        the beginning or end of a block of non-empty cells.
        """

    def MoveCursorUp(self, expandSelection: bool) -> bool:
        """
        MoveCursorUp(expandSelection) -> bool
        
        Moves the grid cursor up by one row.
        """

    def MoveCursorUpBlock(self, expandSelection: bool) -> bool:
        """
        MoveCursorUpBlock(expandSelection) -> bool
        
        Moves the grid cursor up in the current column such that it skips to
        the beginning or end of a block of non-empty cells.
        """

    def MovePageDown(self) -> bool:
        """
        MovePageDown() -> bool
        
        Moves the grid cursor down by some number of rows so that the previous
        bottom visible row becomes the top visible row.
        """

    def MovePageUp(self) -> bool:
        """
        MovePageUp() -> bool
        
        Moves the grid cursor up by some number of rows so that the previous
        top visible row becomes the bottom visible row.
        """

    @overload
    def SetGridCursor(self, coords: Union[GridCellCoords, wx._TwoInts]) -> None:
        ...

    @overload
    def SetGridCursor(self, row: int, col: int) -> None:
        """
        SetGridCursor(row, col) -> None
        SetGridCursor(coords) -> None
        
        Set the grid cursor to the specified cell.
        """

    def SetTabBehaviour(self, behaviour: TabBehaviour) -> None:
        """
        SetTabBehaviour(behaviour) -> None
        
        Set the grid's behaviour when the user presses the TAB key.
        """

    def ClearSelection(self) -> None:
        """
        ClearSelection() -> None
        
        Deselects all cells that are currently selected.
        """

    def DeselectRow(self, row: int) -> None:
        """
        DeselectRow(row) -> None
        
        Deselects a row of cells.
        """

    def DeselectCol(self, col: int) -> None:
        """
        DeselectCol(col) -> None
        
        Deselects a column of cells.
        """

    def DeselectCell(self, row: int, col: int) -> None:
        """
        DeselectCell(row, col) -> None
        
        Deselects a cell.
        """

    def GetSelectedBlocks(self) -> GridBlocks:
        """
        GetSelectedBlocks() -> GridBlocks
        
        Returns a range of grid selection blocks.
        """

    def GetSelectedRowBlocks(self) -> Any:
        """
        GetSelectedRowBlocks() -> Any
        
        Returns an ordered range of non-overlapping selected rows.
        """

    def GetSelectedColBlocks(self) -> Any:
        """
        GetSelectedColBlocks() -> Any
        
        Returns an ordered range of non-overlapping selected columns.
        """

    def GetSelectedCells(self) -> GridCellCoordsArray:
        """
        GetSelectedCells() -> GridCellCoordsArray
        
        Returns an array of individually selected cells.
        """

    def GetSelectedCols(self) -> List[int]:
        """
        GetSelectedCols() -> List[int]
        
        Returns an array of selected columns.
        """

    def GetSelectedRows(self) -> List[int]:
        """
        GetSelectedRows() -> List[int]
        
        Returns an array of selected rows.
        """

    def GetSelectionBackground(self) -> wx.Colour:
        """
        GetSelectionBackground() -> wx.Colour
        
        Returns the colour used for drawing the selection background.
        """

    def GetSelectionBlockBottomRight(self) -> GridCellCoordsArray:
        """
        GetSelectionBlockBottomRight() -> GridCellCoordsArray
        
        Returns an array of the bottom right corners of blocks of selected
        cells.
        """

    def GetSelectionBlockTopLeft(self) -> GridCellCoordsArray:
        """
        GetSelectionBlockTopLeft() -> GridCellCoordsArray
        
        Returns an array of the top left corners of blocks of selected cells.
        """

    def GetSelectionForeground(self) -> wx.Colour:
        """
        GetSelectionForeground() -> wx.Colour
        
        Returns the colour used for drawing the selection foreground.
        """

    def GetSelectionMode(self) -> GridSelectionModes:
        """
        GetSelectionMode() -> GridSelectionModes
        
        Returns the current selection mode.
        """

    @overload
    def IsInSelection(self, coords: Union[GridCellCoords, wx._TwoInts]) -> bool:
        ...

    @overload
    def IsInSelection(self, row: int, col: int) -> bool:
        """
        IsInSelection(row, col) -> bool
        IsInSelection(coords) -> bool
        
        Returns true if the given cell is selected.
        """

    def IsSelection(self) -> bool:
        """
        IsSelection() -> bool
        
        Returns true if there are currently any selected cells, rows, columns
        or blocks.
        """

    def SelectAll(self) -> None:
        """
        SelectAll() -> None
        
        Selects all cells in the grid.
        """

    @overload
    def SelectBlock(self, topLeft: Union[GridCellCoords, wx._TwoInts], bottomRight: Union[GridCellCoords, wx._TwoInts], addToSelected: bool=False) -> None:
        ...

    @overload
    def SelectBlock(self, topRow: int, leftCol: int, bottomRow: int, rightCol: int, addToSelected: bool=False) -> None:
        """
        SelectBlock(topRow, leftCol, bottomRow, rightCol, addToSelected=False) -> None
        SelectBlock(topLeft, bottomRight, addToSelected=False) -> None
        
        Selects a rectangular block of cells.
        """

    def SelectCol(self, col: int, addToSelected: bool=False) -> None:
        """
        SelectCol(col, addToSelected=False) -> None
        
        Selects the specified column.
        """

    def SelectRow(self, row: int, addToSelected: bool=False) -> None:
        """
        SelectRow(row, addToSelected=False) -> None
        
        Selects the specified row.
        """

    def SetSelectionBackground(self, c: wx.Colour) -> None:
        """
        SetSelectionBackground(c) -> None
        
        Set the colour to be used for drawing the selection background.
        """

    def SetSelectionForeground(self, c: wx.Colour) -> None:
        """
        SetSelectionForeground(c) -> None
        
        Set the colour to be used for drawing the selection foreground.
        """

    def SetSelectionMode(self, selmode: GridSelectionModes) -> None:
        """
        SetSelectionMode(selmode) -> None
        
        Set the selection behaviour of the grid.
        """

    def GetScrollLineX(self) -> int:
        """
        GetScrollLineX() -> int
        
        Returns the number of pixels per horizontal scroll increment.
        """

    def GetScrollLineY(self) -> int:
        """
        GetScrollLineY() -> int
        
        Returns the number of pixels per vertical scroll increment.
        """

    @overload
    def IsVisible(self, coords: Union[GridCellCoords, wx._TwoInts], wholeCellVisible: bool=True) -> bool:
        ...

    @overload
    def IsVisible(self, row: int, col: int, wholeCellVisible: bool=True) -> bool:
        """
        IsVisible(row, col, wholeCellVisible=True) -> bool
        IsVisible(coords, wholeCellVisible=True) -> bool
        
        Returns true if a cell is either entirely or at least partially
        visible in the grid window.
        """

    @overload
    def MakeCellVisible(self, coords: Union[GridCellCoords, wx._TwoInts]) -> None:
        ...

    @overload
    def MakeCellVisible(self, row: int, col: int) -> None:
        """
        MakeCellVisible(row, col) -> None
        MakeCellVisible(coords) -> None
        
        Brings the specified cell into the visible grid cell area with minimal
        scrolling.
        """

    def GetFirstFullyVisibleRow(self) -> int:
        """
        GetFirstFullyVisibleRow() -> int
        
        Returns the topmost row of the current visible area.
        """

    def GetFirstFullyVisibleColumn(self) -> int:
        """
        GetFirstFullyVisibleColumn() -> int
        
        Returns the leftmost column of the current visible area.
        """

    def SetScrollLineX(self, x: int) -> None:
        """
        SetScrollLineX(x) -> None
        
        Sets the number of pixels per horizontal scroll increment.
        """

    def SetScrollLineY(self, y: int) -> None:
        """
        SetScrollLineY(y) -> None
        
        Sets the number of pixels per vertical scroll increment.
        """

    def BlockToDeviceRect(self, topLeft: Union[GridCellCoords, wx._TwoInts], bottomRight: Union[GridCellCoords, wx._TwoInts], gridWindow: Optional[GridWindow]=None) -> wx.Rect:
        """
        BlockToDeviceRect(topLeft, bottomRight, gridWindow=None) -> wx.Rect
        
        Convert grid cell coordinates to grid window pixel coordinates.
        """

    @overload
    def CellToRect(self, coords: Union[GridCellCoords, wx._TwoInts]) -> wx.Rect:
        ...

    @overload
    def CellToRect(self, row: int, col: int) -> wx.Rect:
        """
        CellToRect(row, col) -> wx.Rect
        CellToRect(coords) -> wx.Rect
        
        Return the rectangle corresponding to the grid cell's size and
        position in logical coordinates.
        """

    @overload
    def CellToGridWindow(self, coords: Union[GridCellCoords, wx._TwoInts]) -> GridWindow:
        ...

    @overload
    def CellToGridWindow(self, row: int, col: int) -> GridWindow:
        """
        CellToGridWindow(row, col) -> GridWindow
        CellToGridWindow(coords) -> GridWindow
        
        Returns the grid window that contains the cell.
        """

    @overload
    def DevicePosToGridWindow(self, x: int, y: int) -> GridWindow:
        ...

    @overload
    def DevicePosToGridWindow(self, pos: wx.Point) -> GridWindow:
        """
        DevicePosToGridWindow(pos) -> GridWindow
        DevicePosToGridWindow(x, y) -> GridWindow
        
        Returns the grid window that includes the input coordinates.
        """

    def GetGridWindowOffset(self, gridWindow: GridWindow) -> wx.Point:
        """
        GetGridWindowOffset(gridWindow) -> wx.Point
        
        This is an overloaded member function, provided for convenience. It
        differs from the above function only in what argument(s) it accepts.
        """

    @overload
    def CalcGridWindowUnscrolledPosition(self, pt: wx.Point, gridWindow: GridWindow) -> wx.Point:
        ...

    @overload
    def CalcGridWindowUnscrolledPosition(self, x: int, y: int, xx: int, yy: int, gridWindow: GridWindow) -> None:
        """
        CalcGridWindowUnscrolledPosition(x, y, xx, yy, gridWindow) -> None
        CalcGridWindowUnscrolledPosition(pt, gridWindow) -> wx.Point
        
        Translates the device coordinates to the logical ones, taking into
        account the grid window type.
        """

    @overload
    def CalcGridWindowScrolledPosition(self, pt: wx.Point, gridWindow: GridWindow) -> wx.Point:
        ...

    @overload
    def CalcGridWindowScrolledPosition(self, x: int, y: int, xx: int, yy: int, gridWindow: GridWindow) -> None:
        """
        CalcGridWindowScrolledPosition(x, y, xx, yy, gridWindow) -> None
        CalcGridWindowScrolledPosition(pt, gridWindow) -> wx.Point
        
        Translates the logical coordinates to the device ones, taking into
        account the grid window type.
        """

    def XToCol(self, x: int, clipToMinMax: bool=False, gridWindow: Optional[GridWindow]=None) -> int:
        """
        XToCol(x, clipToMinMax=False, gridWindow=None) -> int
        
        Returns the column at the given pixel position depending on the
        window.
        """

    def XToEdgeOfCol(self, x: int) -> int:
        """
        XToEdgeOfCol(x) -> int
        
        Returns the column whose right hand edge is close to the given logical
        x position.
        """

    @overload
    def XYToCell(self, pos: wx.Point, gridWindow: Optional[GridWindow]=None) -> GridCellCoords:
        ...

    @overload
    def XYToCell(self, x: int, y: int, gridWindow: Optional[GridWindow]=None) -> GridCellCoords:
        """
        XYToCell(x, y, gridWindow=None) -> GridCellCoords
        XYToCell(pos, gridWindow=None) -> GridCellCoords
        
        Translates logical pixel coordinates to the grid cell coordinates.
        """

    def YToEdgeOfRow(self, y: int) -> int:
        """
        YToEdgeOfRow(y) -> int
        
        Returns the row whose bottom edge is close to the given logical y
        position.
        """

    def YToRow(self, y: int, clipToMinMax: bool=False, gridWindow: Optional[GridWindow]=None) -> int:
        """
        YToRow(y, clipToMinMax=False, gridWindow=None) -> int
        
        Returns the grid row that corresponds to the logical y coordinate.
        """

    def AppendCols(self, numCols: int=1, updateLabels: bool=True) -> bool:
        """
        AppendCols(numCols=1, updateLabels=True) -> bool
        
        Appends one or more new columns to the right of the grid.
        """

    def AppendRows(self, numRows: int=1, updateLabels: bool=True) -> bool:
        """
        AppendRows(numRows=1, updateLabels=True) -> bool
        
        Appends one or more new rows to the bottom of the grid.
        """

    def AreHorzGridLinesClipped(self) -> bool:
        """
        AreHorzGridLinesClipped() -> bool
        
        Return true if the horizontal grid lines stop at the last column
        boundary or false if they continue to the end of the window.
        """

    def AreVertGridLinesClipped(self) -> bool:
        """
        AreVertGridLinesClipped() -> bool
        
        Return true if the vertical grid lines stop at the last row boundary
        or false if they continue to the end of the window.
        """

    def BeginBatch(self) -> None:
        """
        BeginBatch() -> None
        
        Increments the grid's batch count.
        """

    def ClearGrid(self) -> None:
        """
        ClearGrid() -> None
        
        Clears all data in the underlying grid table and repaints the grid.
        """

    def ClipHorzGridLines(self, clip: bool) -> None:
        """
        ClipHorzGridLines(clip) -> None
        
        Change whether the horizontal grid lines are clipped by the end of the
        last column.
        """

    def ClipVertGridLines(self, clip: bool) -> None:
        """
        ClipVertGridLines(clip) -> None
        
        Change whether the vertical grid lines are clipped by the end of the
        last row.
        """

    def DeleteCols(self, pos: int=0, numCols: int=1, updateLabels: bool=True) -> bool:
        """
        DeleteCols(pos=0, numCols=1, updateLabels=True) -> bool
        
        Deletes one or more columns from a grid starting at the specified
        position.
        """

    def DeleteRows(self, pos: int=0, numRows: int=1, updateLabels: bool=True) -> bool:
        """
        DeleteRows(pos=0, numRows=1, updateLabels=True) -> bool
        
        Deletes one or more rows from a grid starting at the specified
        position.
        """

    @overload
    def FreezeTo(self, coords: Union[GridCellCoords, wx._TwoInts]) -> bool:
        ...

    @overload
    def FreezeTo(self, row: int, col: int) -> bool:
        """
        FreezeTo(row, col) -> bool
        FreezeTo(coords) -> bool
        
        Sets or resets the frozen columns and rows.
        """

    def EndBatch(self) -> None:
        """
        EndBatch() -> None
        
        Decrements the grid's batch count.
        """

    def Fit(self) -> None:
        """
        Fit() -> None
        
        Overridden wxWindow method.
        """

    def ForceRefresh(self) -> None:
        """
        ForceRefresh() -> None
        
        Causes immediate repainting of the grid.
        """

    def GetBatchCount(self) -> int:
        """
        GetBatchCount() -> int
        
        Returns the number of times that BeginBatch() has been called without
        (yet) matching calls to EndBatch().
        """

    def GetNumberCols(self) -> int:
        """
        GetNumberCols() -> int
        
        Returns the total number of grid columns.
        """

    def GetNumberRows(self) -> int:
        """
        GetNumberRows() -> int
        
        Returns the total number of grid rows.
        """

    def GetNumberFrozenCols(self) -> int:
        """
        GetNumberFrozenCols() -> int
        
        Returns the number of frozen grid columns.
        """

    def GetNumberFrozenRows(self) -> int:
        """
        GetNumberFrozenRows() -> int
        
        Returns the number of frozen grid rows.
        """

    def GetOrCreateCellAttr(self, row: int, col: int) -> GridCellAttr:
        """
        GetOrCreateCellAttr(row, col) -> GridCellAttr
        
        Returns the attribute for the given cell creating one if necessary.
        """

    def GetOrCreateCellAttrPtr(self, row: int, col: int) -> GridCellAttrPtr:
        """
        GetOrCreateCellAttrPtr(row, col) -> GridCellAttrPtr
        
        Returns the attribute for the given cell creating one if necessary.
        """

    def GetTable(self) -> GridTableBase:
        """
        GetTable() -> GridTableBase
        
        Returns a base pointer to the current table object.
        """

    def InsertCols(self, pos: int=0, numCols: int=1, updateLabels: bool=True) -> bool:
        """
        InsertCols(pos=0, numCols=1, updateLabels=True) -> bool
        
        Inserts one or more new columns into a grid with the first new column
        at the specified position.
        """

    def InsertRows(self, pos: int=0, numRows: int=1, updateLabels: bool=True) -> bool:
        """
        InsertRows(pos=0, numRows=1, updateLabels=True) -> bool
        
        Inserts one or more new rows into a grid with the first new row at the
        specified position.
        """

    def RefreshAttr(self, row: int, col: int) -> None:
        """
        RefreshAttr(row, col) -> None
        
        Invalidates the cached attribute for the given cell.
        """

    @overload
    def RefreshBlock(self, topRow: int, leftCol: int, bottomRow: int, rightCol: int) -> None:
        ...

    @overload
    def RefreshBlock(self, topLeft: Union[GridCellCoords, wx._TwoInts], bottomRight: Union[GridCellCoords, wx._TwoInts]) -> None:
        """
        RefreshBlock(topLeft, bottomRight) -> None
        RefreshBlock(topRow, leftCol, bottomRow, rightCol) -> None
        
        Redraw all the cells in the given block.
        """

    def Render(self, dc: wx.DC, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, topLeft: Union[GridCellCoords, wx._TwoInts]=GridCellCoords(-1,-1), bottomRight: Union[GridCellCoords, wx._TwoInts]=GridCellCoords(-1,-1), style: int=GRID_DRAW_DEFAULT) -> None:
        """
        Render(dc, pos=wx.DefaultPosition, size=wx.DefaultSize, topLeft=GridCellCoords(-1,-1), bottomRight=GridCellCoords(-1,-1), style=GRID_DRAW_DEFAULT) -> None
        
        Draws part or all of a wxGrid on a wxDC for printing or display.
        """

    def SetAttr(self, row: int, col: int, attr: GridCellAttr) -> None:
        """
        SetAttr(row, col, attr) -> None
        
        Sets the cell attributes for the specified cell.
        """

    def SetColAttr(self, col: int, attr: GridCellAttr) -> None:
        """
        SetColAttr(col, attr) -> None
        
        Sets the cell attributes for all cells in the specified column.
        """

    def SetMargins(self, extraWidth: int, extraHeight: int) -> None:
        """
        SetMargins(extraWidth, extraHeight) -> None
        
        Sets the extra margins used around the grid area.
        """

    def SetRowAttr(self, row: int, attr: GridCellAttr) -> None:
        """
        SetRowAttr(row, attr) -> None
        
        Sets the cell attributes for all cells in the specified row.
        """

    def CalcRowLabelsExposed(self, reg: wx.Region, gridWindow: Optional[GridWindow]=None) -> List[int]:
        """
        CalcRowLabelsExposed(reg, gridWindow=None) -> List[int]
        
        Returns an array of row labels within the given region.
        """

    def CalcColLabelsExposed(self, reg: wx.Region, gridWindow: Optional[GridWindow]=None) -> List[int]:
        """
        CalcColLabelsExposed(reg, gridWindow=None) -> List[int]
        
        Returns an array of column labels within the given region.
        """

    def CalcCellsExposed(self, reg: wx.Region, gridWindow: Optional[GridWindow]=None) -> GridCellCoordsArray:
        """
        CalcCellsExposed(reg, gridWindow=None) -> GridCellCoordsArray
        
        Returns an array of (visible) cells within the given region.
        """

    def GetSortingColumn(self) -> int:
        """
        GetSortingColumn() -> int
        
        Return the column in which the sorting indicator is currently
        displayed.
        """

    def IsSortingBy(self, col: int) -> bool:
        """
        IsSortingBy(col) -> bool
        
        Return true if this column is currently used for sorting.
        """

    def IsSortOrderAscending(self) -> bool:
        """
        IsSortOrderAscending() -> bool
        
        Return true if the current sorting order is ascending or false if it
        is descending.
        """

    def SetSortingColumn(self, col: int, ascending: bool=True) -> None:
        """
        SetSortingColumn(col, ascending=True) -> None
        
        Set the column to display the sorting indicator in and its direction.
        """

    def UnsetSortingColumn(self) -> None:
        """
        UnsetSortingColumn() -> None
        
        Remove any currently shown sorting indicator.
        """

    def GetGridWindow(self) -> wx.Window:
        """
        GetGridWindow() -> wx.Window
        
        Return the main grid window containing the grid cells.
        """

    def GetFrozenCornerGridWindow(self) -> wx.Window:
        """
        GetFrozenCornerGridWindow() -> wx.Window
        
        Return the corner grid window containing frozen cells.
        """

    def GetFrozenRowGridWindow(self) -> wx.Window:
        """
        GetFrozenRowGridWindow() -> wx.Window
        
        Return the rows grid window containing row frozen cells.
        """

    def GetFrozenColGridWindow(self) -> wx.Window:
        """
        GetFrozenColGridWindow() -> wx.Window
        
        Return the columns grid window containing column frozen cells.
        """

    def GetGridRowLabelWindow(self) -> wx.Window:
        """
        GetGridRowLabelWindow() -> wx.Window
        
        Return the row labels window.
        """

    def GetGridColLabelWindow(self) -> wx.Window:
        """
        GetGridColLabelWindow() -> wx.Window
        
        Return the column labels window.
        """

    def GetGridCornerLabelWindow(self) -> wx.Window:
        """
        GetGridCornerLabelWindow() -> wx.Window
        
        Return the window in the top left grid corner.
        """

    def GetGridColHeader(self) -> wx.HeaderCtrl:
        """
        GetGridColHeader() -> wx.HeaderCtrl
        
        Return the header control used for column labels display.
        """

    def IsUsingNativeHeader(self) -> bool:
        """
        IsUsingNativeHeader() -> bool
        
        Return true if native header control is currently being used.
        """

    def DrawCellHighlight(self, dc: wx.DC, attr: GridCellAttr) -> None:
        """
        DrawCellHighlight(dc, attr) -> None
        """

    def DrawRowLabels(self, dc: wx.DC, rows: List[int]) -> None:
        """
        DrawRowLabels(dc, rows) -> None
        """

    def DrawRowLabel(self, dc: wx.DC, row: int) -> None:
        """
        DrawRowLabel(dc, row) -> None
        """

    def DrawColLabels(self, dc: wx.DC, cols: List[int]) -> None:
        """
        DrawColLabels(dc, cols) -> None
        """

    def DrawColLabel(self, dc: wx.DC, col: int) -> None:
        """
        DrawColLabel(dc, col) -> None
        """

    def DrawCornerLabel(self, dc: wx.DC) -> None:
        """
        DrawCornerLabel(dc) -> None
        """

    @overload
    def DrawTextRectangle(self, dc: wx.DC, lines: List[str], rect: wx.Rect, horizontalAlignment: int=wx.ALIGN_LEFT, verticalAlignment: int=wx.ALIGN_TOP, textOrientation: int=wx.HORIZONTAL) -> None:
        ...

    @overload
    def DrawTextRectangle(self, dc: wx.DC, text: str, rect: wx.Rect, horizontalAlignment: int=wx.ALIGN_LEFT, verticalAlignment: int=wx.ALIGN_TOP, textOrientation: int=wx.HORIZONTAL) -> None:
        """
        DrawTextRectangle(dc, text, rect, horizontalAlignment=wx.ALIGN_LEFT, verticalAlignment=wx.ALIGN_TOP, textOrientation=wx.HORIZONTAL) -> None
        DrawTextRectangle(dc, lines, rect, horizontalAlignment=wx.ALIGN_LEFT, verticalAlignment=wx.ALIGN_TOP, textOrientation=wx.HORIZONTAL) -> None
        """

    def GetCellHighlightColour(self) -> wx.Colour:
        """
        GetCellHighlightColour() -> wx.Colour
        """

    def GetCellHighlightPenWidth(self) -> int:
        """
        GetCellHighlightPenWidth() -> int
        """

    def GetCellHighlightROPenWidth(self) -> int:
        """
        GetCellHighlightROPenWidth() -> int
        """

    def SetCellHighlightColour(self, : wx.Colour) -> None:
        """
        SetCellHighlightColour() -> None
        """

    def SetCellHighlightPenWidth(self, width: int) -> None:
        """
        SetCellHighlightPenWidth(width) -> None
        """

    def SetCellHighlightROPenWidth(self, width: int) -> None:
        """
        SetCellHighlightROPenWidth(width) -> None
        """

    def SetGridFrozenBorderColour(self, : wx.Colour) -> None:
        """
        SetGridFrozenBorderColour() -> None
        """

    def SetGridFrozenBorderPenWidth(self, width: int) -> None:
        """
        SetGridFrozenBorderPenWidth(width) -> None
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    def SetTable(self, table, takeOwnership=False, selmode=GridSelectCells):
        """
        Set the Grid Table to be used by this grid.
        """

    SelectCells = GridSelectCells
    SelectRows = GridSelectRows
    SelectColumns = GridSelectColumns
    SelectRowsOrColumns = GridSelectRowsOrColumns
    @property
    def BatchCount(self) -> int: ...
    @property
    def CellHighlightColour(self) -> wx.Colour: ...
    @CellHighlightColour.setter
    def CellHighlightColour(self, value: wx.Colour, /) -> None: ...
    @property
    def CellHighlightPenWidth(self) -> int: ...
    @CellHighlightPenWidth.setter
    def CellHighlightPenWidth(self, value: int, /) -> None: ...
    @property
    def CellHighlightROPenWidth(self) -> int: ...
    @CellHighlightROPenWidth.setter
    def CellHighlightROPenWidth(self, value: int, /) -> None: ...
    @property
    def ColLabelSize(self) -> int: ...
    @ColLabelSize.setter
    def ColLabelSize(self, value: int, /) -> None: ...
    @property
    def ColLabelTextOrientation(self) -> int: ...
    @ColLabelTextOrientation.setter
    def ColLabelTextOrientation(self, value: int, /) -> None: ...
    @property
    def ColMinimalAcceptableWidth(self) -> int: ...
    @ColMinimalAcceptableWidth.setter
    def ColMinimalAcceptableWidth(self, value: int, /) -> None: ...
    @property
    def ColSizes(self) -> GridSizesInfo: ...
    @ColSizes.setter
    def ColSizes(self, value: GridSizesInfo, /) -> None: ...
    @property
    def CornerLabelTextOrientation(self) -> int: ...
    @CornerLabelTextOrientation.setter
    def CornerLabelTextOrientation(self, value: int, /) -> None: ...
    @property
    def CornerLabelValue(self) -> str: ...
    @CornerLabelValue.setter
    def CornerLabelValue(self, value: str, /) -> None: ...
    @property
    def DefaultCellBackgroundColour(self) -> wx.Colour: ...
    @DefaultCellBackgroundColour.setter
    def DefaultCellBackgroundColour(self, value: wx.Colour, /) -> None: ...
    @property
    def DefaultCellFitMode(self) -> GridFitMode: ...
    @DefaultCellFitMode.setter
    def DefaultCellFitMode(self, value: GridFitMode, /) -> None: ...
    @property
    def DefaultCellFont(self) -> wx.Font: ...
    @DefaultCellFont.setter
    def DefaultCellFont(self, value: wx.Font, /) -> None: ...
    @property
    def DefaultCellOverflow(self) -> bool: ...
    @DefaultCellOverflow.setter
    def DefaultCellOverflow(self, value: bool, /) -> None: ...
    @property
    def DefaultCellTextColour(self) -> wx.Colour: ...
    @DefaultCellTextColour.setter
    def DefaultCellTextColour(self, value: wx.Colour, /) -> None: ...
    @property
    def DefaultColLabelSize(self) -> int: ...
    @property
    def DefaultColSize(self) -> int: ...
    @DefaultColSize.setter
    def DefaultColSize(self, value: int, /) -> None: ...
    @property
    def DefaultEditor(self) -> GridCellEditor: ...
    @DefaultEditor.setter
    def DefaultEditor(self, value: GridCellEditor, /) -> None: ...
    @property
    def DefaultGridLinePen(self) -> wx.Pen: ...
    @property
    def DefaultRenderer(self) -> GridCellRenderer: ...
    @DefaultRenderer.setter
    def DefaultRenderer(self, value: GridCellRenderer, /) -> None: ...
    @property
    def DefaultRowLabelSize(self) -> int: ...
    @property
    def DefaultRowSize(self) -> int: ...
    @DefaultRowSize.setter
    def DefaultRowSize(self, value: int, /) -> None: ...
    @property
    def FirstFullyVisibleColumn(self) -> int: ...
    @property
    def FirstFullyVisibleRow(self) -> int: ...
    @property
    def FrozenColGridWindow(self) -> wx.Window: ...
    @property
    def FrozenCornerGridWindow(self) -> wx.Window: ...
    @property
    def FrozenRowGridWindow(self) -> wx.Window: ...
    @property
    def GridColHeader(self) -> wx.HeaderCtrl: ...
    @property
    def GridColLabelWindow(self) -> wx.Window: ...
    @property
    def GridCornerLabelWindow(self) -> wx.Window: ...
    @property
    def GridCursorCol(self) -> int: ...
    @property
    def GridCursorCoords(self) -> GridCellCoords: ...
    @property
    def GridCursorRow(self) -> int: ...
    @property
    def GridLineColour(self) -> wx.Colour: ...
    @GridLineColour.setter
    def GridLineColour(self, value: wx.Colour, /) -> None: ...
    @property
    def GridRowLabelWindow(self) -> wx.Window: ...
    @property
    def GridWindow(self) -> wx.Window: ...
    @property
    def LabelBackgroundColour(self) -> wx.Colour: ...
    @LabelBackgroundColour.setter
    def LabelBackgroundColour(self, value: wx.Colour, /) -> None: ...
    @property
    def LabelFont(self) -> wx.Font: ...
    @LabelFont.setter
    def LabelFont(self, value: wx.Font, /) -> None: ...
    @property
    def LabelTextColour(self) -> wx.Colour: ...
    @LabelTextColour.setter
    def LabelTextColour(self, value: wx.Colour, /) -> None: ...
    @property
    def NumberCols(self) -> int: ...
    @property
    def NumberFrozenCols(self) -> int: ...
    @property
    def NumberFrozenRows(self) -> int: ...
    @property
    def NumberRows(self) -> int: ...
    @property
    def RowLabelSize(self) -> int: ...
    @RowLabelSize.setter
    def RowLabelSize(self, value: int, /) -> None: ...
    @property
    def RowMinimalAcceptableHeight(self) -> int: ...
    @RowMinimalAcceptableHeight.setter
    def RowMinimalAcceptableHeight(self, value: int, /) -> None: ...
    @property
    def RowSizes(self) -> GridSizesInfo: ...
    @RowSizes.setter
    def RowSizes(self, value: GridSizesInfo, /) -> None: ...
    @property
    def ScrollLineX(self) -> int: ...
    @ScrollLineX.setter
    def ScrollLineX(self, value: int, /) -> None: ...
    @property
    def ScrollLineY(self) -> int: ...
    @ScrollLineY.setter
    def ScrollLineY(self, value: int, /) -> None: ...
    @property
    def SelectedBlocks(self) -> GridBlocks: ...
    @property
    def SelectedCells(self) -> GridCellCoordsArray: ...
    @property
    def SelectedColBlocks(self) -> Any: ...
    @property
    def SelectedCols(self) -> List[int]: ...
    @property
    def SelectedRowBlocks(self) -> Any: ...
    @property
    def SelectedRows(self) -> List[int]: ...
    @property
    def SelectionBackground(self) -> wx.Colour: ...
    @SelectionBackground.setter
    def SelectionBackground(self, value: wx.Colour, /) -> None: ...
    @property
    def SelectionBlockBottomRight(self) -> GridCellCoordsArray: ...
    @property
    def SelectionBlockTopLeft(self) -> GridCellCoordsArray: ...
    @property
    def SelectionForeground(self) -> wx.Colour: ...
    @SelectionForeground.setter
    def SelectionForeground(self, value: wx.Colour, /) -> None: ...
    @property
    def SelectionMode(self) -> GridSelectionModes: ...
    @SelectionMode.setter
    def SelectionMode(self, value: GridSelectionModes, /) -> None: ...
    @property
    def SortingColumn(self) -> int: ...
    @SortingColumn.setter
    def SortingColumn(self, value: int, /) -> None: ...
    @property
    def Table(self) -> GridTableBase: ...
    @Table.setter
    def Table(self, value: GridTableBase, /) -> None: ...

    def CanHaveAttributes(self) -> bool:
        """
        CanHaveAttributes() -> bool
        
        Returns true if this grid has support for cell attributes.
        """

    def GetColMinimalWidth(self, col: int) -> int:
        """
        GetColMinimalWidth(col) -> int
        
        Get the minimal width of the given column/row.
        """

    def GetColRight(self, col: int) -> int:
        """
        GetColRight(col) -> int
        
        Returns the coordinate of the right border specified column.
        """

    def GetColLeft(self, col: int) -> int:
        """
        GetColLeft(col) -> int
        
        Returns the coordinate of the left border specified column.
        """

    def GetRowMinimalHeight(self, col: int) -> int:
        """
        GetRowMinimalHeight(col) -> int
        
        Returns the minimal size for the given column.
        """
# end of class Grid


class GridUpdateLocker:
    """
    GridUpdateLocker(grid=None) -> None
    
    This small class can be used to prevent wxGrid from redrawing during
    its lifetime by calling wxGrid::BeginBatch() in its constructor and
    wxGrid::EndBatch() in its destructor.
    """

    def __init__(self, grid: Optional[Grid]=None) -> None:
        """
        GridUpdateLocker(grid=None) -> None
        
        This small class can be used to prevent wxGrid from redrawing during
        its lifetime by calling wxGrid::BeginBatch() in its constructor and
        wxGrid::EndBatch() in its destructor.
        """

    def Create(self, grid: Grid) -> None:
        """
        Create(grid) -> None
        
        This method can be called if the object had been constructed using the
        default constructor.
        """

    def __enter__(self):
        """
        
        """

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        
        """
# end of class GridUpdateLocker


class GridEvent(wx.NotifyEvent):
    """
    GridEvent() -> None
    GridEvent(id, type, obj, row=-1, col=-1, x=-1, y=-1, sel=True, kbd=wx.KeyboardState()) -> None
    
    This event class contains information about various grid events.
    """

    @overload
    def __init__(self, id: int, type: EventType, obj: wx.Object, row: int=-1, col: int=-1, x: int=-1, y: int=-1, sel: bool=True, kbd: wx.KeyboardState=wx.KeyboardState()) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        GridEvent() -> None
        GridEvent(id, type, obj, row=-1, col=-1, x=-1, y=-1, sel=True, kbd=wx.KeyboardState()) -> None
        
        This event class contains information about various grid events.
        """

    def AltDown(self) -> bool:
        """
        AltDown() -> bool
        
        Returns true if the Alt key was down at the time of the event.
        """

    def ControlDown(self) -> bool:
        """
        ControlDown() -> bool
        
        Returns true if the Control key was down at the time of the event.
        """

    def GetCol(self) -> int:
        """
        GetCol() -> int
        
        Column at which the event occurred.
        """

    def GetPosition(self) -> wx.Point:
        """
        GetPosition() -> wx.Point
        
        Position in pixels at which the event occurred.
        """

    def GetRow(self) -> int:
        """
        GetRow() -> int
        
        Row at which the event occurred.
        """

    def MetaDown(self) -> bool:
        """
        MetaDown() -> bool
        
        Returns true if the Meta key was down at the time of the event.
        """

    def Selecting(self) -> bool:
        """
        Selecting() -> bool
        
        Returns true if the user is selecting grid cells, or false if
        deselecting.
        """

    def ShiftDown(self) -> bool:
        """
        ShiftDown() -> bool
        
        Returns true if the Shift key was down at the time of the event.
        """
    @property
    def Col(self) -> int: ...
    @property
    def Position(self) -> wx.Point: ...
    @property
    def Row(self) -> int: ...
# end of class GridEvent


class GridSizeEvent(wx.NotifyEvent):
    """
    GridSizeEvent() -> None
    GridSizeEvent(id, type, obj, rowOrCol=-1, x=-1, y=-1, kbd=wx.KeyboardState()) -> None
    
    This event class contains information about a row/column resize event.
    """

    @overload
    def __init__(self, id: int, type: EventType, obj: wx.Object, rowOrCol: int=-1, x: int=-1, y: int=-1, kbd: wx.KeyboardState=wx.KeyboardState()) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        GridSizeEvent() -> None
        GridSizeEvent(id, type, obj, rowOrCol=-1, x=-1, y=-1, kbd=wx.KeyboardState()) -> None
        
        This event class contains information about a row/column resize event.
        """

    def AltDown(self) -> bool:
        """
        AltDown() -> bool
        
        Returns true if the Alt key was down at the time of the event.
        """

    def ControlDown(self) -> bool:
        """
        ControlDown() -> bool
        
        Returns true if the Control key was down at the time of the event.
        """

    def GetPosition(self) -> wx.Point:
        """
        GetPosition() -> wx.Point
        
        Position in pixels at which the event occurred.
        """

    def GetRowOrCol(self) -> int:
        """
        GetRowOrCol() -> int
        
        Row or column at that was resized.
        """

    def MetaDown(self) -> bool:
        """
        MetaDown() -> bool
        
        Returns true if the Meta key was down at the time of the event.
        """

    def ShiftDown(self) -> bool:
        """
        ShiftDown() -> bool
        
        Returns true if the Shift key was down at the time of the event.
        """
    @property
    def Position(self) -> wx.Point: ...
    @property
    def RowOrCol(self) -> int: ...
# end of class GridSizeEvent


class GridRangeSelectEvent(wx.NotifyEvent):
    """
    GridRangeSelectEvent() -> None
    GridRangeSelectEvent(id, type, obj, topLeft, bottomRight, sel=True, kbd=wx.KeyboardState()) -> None
    
    Events of this class notify about a range of cells being selected.
    """

    @overload
    def __init__(self, id: int, type: EventType, obj: wx.Object, topLeft: Union[GridCellCoords, wx._TwoInts], bottomRight: Union[GridCellCoords, wx._TwoInts], sel: bool=True, kbd: wx.KeyboardState=wx.KeyboardState()) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        GridRangeSelectEvent() -> None
        GridRangeSelectEvent(id, type, obj, topLeft, bottomRight, sel=True, kbd=wx.KeyboardState()) -> None
        
        Events of this class notify about a range of cells being selected.
        """

    def AltDown(self) -> bool:
        """
        AltDown() -> bool
        
        Returns true if the Alt key was down at the time of the event.
        """

    def ControlDown(self) -> bool:
        """
        ControlDown() -> bool
        
        Returns true if the Control key was down at the time of the event.
        """

    def GetBottomRightCoords(self) -> GridCellCoords:
        """
        GetBottomRightCoords() -> GridCellCoords
        
        Top left corner of the rectangular area that was (de)selected.
        """

    def GetBottomRow(self) -> int:
        """
        GetBottomRow() -> int
        
        Bottom row of the rectangular area that was (de)selected.
        """

    def GetLeftCol(self) -> int:
        """
        GetLeftCol() -> int
        
        Left column of the rectangular area that was (de)selected.
        """

    def GetRightCol(self) -> int:
        """
        GetRightCol() -> int
        
        Right column of the rectangular area that was (de)selected.
        """

    def GetTopLeftCoords(self) -> GridCellCoords:
        """
        GetTopLeftCoords() -> GridCellCoords
        
        Top left corner of the rectangular area that was (de)selected.
        """

    def GetTopRow(self) -> int:
        """
        GetTopRow() -> int
        
        Top row of the rectangular area that was (de)selected.
        """

    def MetaDown(self) -> bool:
        """
        MetaDown() -> bool
        
        Returns true if the Meta key was down at the time of the event.
        """

    def Selecting(self) -> bool:
        """
        Selecting() -> bool
        
        Returns true if the area was selected, false otherwise.
        """

    def ShiftDown(self) -> bool:
        """
        ShiftDown() -> bool
        
        Returns true if the Shift key was down at the time of the event.
        """
    @property
    def BottomRightCoords(self) -> GridCellCoords: ...
    @property
    def BottomRow(self) -> int: ...
    @property
    def LeftCol(self) -> int: ...
    @property
    def RightCol(self) -> int: ...
    @property
    def TopLeftCoords(self) -> GridCellCoords: ...
    @property
    def TopRow(self) -> int: ...
# end of class GridRangeSelectEvent


class GridEditorCreatedEvent(wx.CommandEvent):
    """
    GridEditorCreatedEvent() -> None
    GridEditorCreatedEvent(id, type, obj, row, col, ctrl) -> None
    """

    @overload
    def __init__(self, id: int, type: EventType, obj: wx.Object, row: int, col: int, ctrl: wx.Control) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        GridEditorCreatedEvent() -> None
        GridEditorCreatedEvent(id, type, obj, row, col, ctrl) -> None
        """

    def GetCol(self) -> int:
        """
        GetCol() -> int
        
        Returns the column at which the event occurred.
        """

    def GetControl(self) -> wx.Control:
        """
        GetControl() -> wx.Control
        
        Returns the edit control.
        """

    def GetRow(self) -> int:
        """
        GetRow() -> int
        
        Returns the row at which the event occurred.
        """

    def GetWindow(self) -> wx.Window:
        """
        GetWindow() -> wx.Window
        
        Returns the edit window.
        """

    def SetCol(self, col: int) -> None:
        """
        SetCol(col) -> None
        
        Sets the column at which the event occurred.
        """

    def SetControl(self, ctrl: wx.Control) -> None:
        """
        SetControl(ctrl) -> None
        
        Sets the edit control.
        """

    def SetRow(self, row: int) -> None:
        """
        SetRow(row) -> None
        
        Sets the row at which the event occurred.
        """

    def SetWindow(self, window: wx.Window) -> None:
        """
        SetWindow(window) -> None
        
        Sets the edit window.
        """
    @property
    def Col(self) -> int: ...
    @Col.setter
    def Col(self, value: int, /) -> None: ...
    @property
    def Control(self) -> wx.Control: ...
    @Control.setter
    def Control(self, value: wx.Control, /) -> None: ...
    @property
    def Row(self) -> int: ...
    @Row.setter
    def Row(self, value: int, /) -> None: ...
    @property
    def Window(self) -> wx.Window: ...
    @Window.setter
    def Window(self, value: wx.Window, /) -> None: ...
# end of class GridEditorCreatedEvent


GRID_VALUE_STRING =    "string"
GRID_VALUE_BOOL =      "bool"
GRID_VALUE_NUMBER =    "long"
GRID_VALUE_FLOAT =     "double"
GRID_VALUE_CHOICE =    "choice"
GRID_VALUE_DATE =      "date"
GRID_VALUE_TEXT =      "string"
GRID_VALUE_LONG =      "long"
GRID_VALUE_CHOICEINT = "choiceint"
GRID_VALUE_DATETIME =  "datetime"

GRIDTABLE_REQUEST_VIEW_GET_VALUES = 2000
GRIDTABLE_REQUEST_VIEW_SEND_VALUES = 2001

from collections import namedtuple
_im_GridCellCoords = namedtuple('_im_GridCellCoords', ['Row', 'Col'])
del namedtuple

PyGridCellRenderer = wx.deprecated(GridCellRenderer, 'Use GridCellRenderer instead.')

PyGridCellEditor = wx.deprecated(GridCellEditor, 'Use GridCellEditor instead.')

PyGridCellAttrProvider = wx.deprecated(GridCellAttrProvider, 'Use GridCellAttrProvider instead.')

PyGridTableBase = wx.deprecated(GridTableBase, 'Use GridTableBase instead.')

EVT_GRID_CELL_LEFT_CLICK = wx.PyEventBinder( wxEVT_GRID_CELL_LEFT_CLICK )
EVT_GRID_CELL_RIGHT_CLICK = wx.PyEventBinder( wxEVT_GRID_CELL_RIGHT_CLICK )
EVT_GRID_CELL_LEFT_DCLICK = wx.PyEventBinder( wxEVT_GRID_CELL_LEFT_DCLICK )
EVT_GRID_CELL_RIGHT_DCLICK = wx.PyEventBinder( wxEVT_GRID_CELL_RIGHT_DCLICK )
EVT_GRID_LABEL_LEFT_CLICK = wx.PyEventBinder( wxEVT_GRID_LABEL_LEFT_CLICK )
EVT_GRID_LABEL_RIGHT_CLICK = wx.PyEventBinder( wxEVT_GRID_LABEL_RIGHT_CLICK )
EVT_GRID_LABEL_LEFT_DCLICK = wx.PyEventBinder( wxEVT_GRID_LABEL_LEFT_DCLICK )
EVT_GRID_LABEL_RIGHT_DCLICK = wx.PyEventBinder( wxEVT_GRID_LABEL_RIGHT_DCLICK )
EVT_GRID_ROW_SIZE = wx.PyEventBinder( wxEVT_GRID_ROW_SIZE )
EVT_GRID_COL_SIZE = wx.PyEventBinder( wxEVT_GRID_COL_SIZE )
EVT_GRID_COL_AUTO_SIZE = wx.PyEventBinder( wxEVT_GRID_COL_AUTO_SIZE )
EVT_GRID_RANGE_SELECTING = wx.PyEventBinder( wxEVT_GRID_RANGE_SELECTING )
EVT_GRID_RANGE_SELECTED = wx.PyEventBinder( wxEVT_GRID_RANGE_SELECTED )
EVT_GRID_CELL_CHANGING = wx.PyEventBinder( wxEVT_GRID_CELL_CHANGING )
EVT_GRID_CELL_CHANGED = wx.PyEventBinder( wxEVT_GRID_CELL_CHANGED )
EVT_GRID_SELECT_CELL = wx.PyEventBinder( wxEVT_GRID_SELECT_CELL )
EVT_GRID_EDITOR_SHOWN = wx.PyEventBinder( wxEVT_GRID_EDITOR_SHOWN )
EVT_GRID_EDITOR_HIDDEN = wx.PyEventBinder( wxEVT_GRID_EDITOR_HIDDEN )
EVT_GRID_EDITOR_CREATED = wx.PyEventBinder( wxEVT_GRID_EDITOR_CREATED )
EVT_GRID_CELL_BEGIN_DRAG = wx.PyEventBinder( wxEVT_GRID_CELL_BEGIN_DRAG )
EVT_GRID_ROW_MOVE = wx.PyEventBinder( wxEVT_GRID_ROW_MOVE )
EVT_GRID_COL_MOVE = wx.PyEventBinder( wxEVT_GRID_COL_MOVE )
EVT_GRID_COL_SORT = wx.PyEventBinder( wxEVT_GRID_COL_SORT )
EVT_GRID_TABBING = wx.PyEventBinder( wxEVT_GRID_TABBING )

# The same as above but with the ability to specify an identifier
EVT_GRID_CMD_CELL_LEFT_CLICK =     wx.PyEventBinder( wxEVT_GRID_CELL_LEFT_CLICK,    1 )
EVT_GRID_CMD_CELL_RIGHT_CLICK =    wx.PyEventBinder( wxEVT_GRID_CELL_RIGHT_CLICK,   1 )
EVT_GRID_CMD_CELL_LEFT_DCLICK =    wx.PyEventBinder( wxEVT_GRID_CELL_LEFT_DCLICK,   1 )
EVT_GRID_CMD_CELL_RIGHT_DCLICK =   wx.PyEventBinder( wxEVT_GRID_CELL_RIGHT_DCLICK,  1 )
EVT_GRID_CMD_LABEL_LEFT_CLICK =    wx.PyEventBinder( wxEVT_GRID_LABEL_LEFT_CLICK,   1 )
EVT_GRID_CMD_LABEL_RIGHT_CLICK =   wx.PyEventBinder( wxEVT_GRID_LABEL_RIGHT_CLICK,  1 )
EVT_GRID_CMD_LABEL_LEFT_DCLICK =   wx.PyEventBinder( wxEVT_GRID_LABEL_LEFT_DCLICK,  1 )
EVT_GRID_CMD_LABEL_RIGHT_DCLICK =  wx.PyEventBinder( wxEVT_GRID_LABEL_RIGHT_DCLICK, 1 )
EVT_GRID_CMD_ROW_SIZE =            wx.PyEventBinder( wxEVT_GRID_ROW_SIZE,           1 )
EVT_GRID_CMD_COL_SIZE =            wx.PyEventBinder( wxEVT_GRID_COL_SIZE,           1 )
EVT_GRID_CMD_COL_AUTO_SIZE =       wx.PyEventBinder( wxEVT_GRID_COL_AUTO_SIZE,      1 )
EVT_GRID_CMD_RANGE_SELECTING =     wx.PyEventBinder( wxEVT_GRID_RANGE_SELECTING,    1 )
EVT_GRID_CMD_RANGE_SELECTED =      wx.PyEventBinder( wxEVT_GRID_RANGE_SELECTED,     1 )
EVT_GRID_CMD_CELL_CHANGING =       wx.PyEventBinder( wxEVT_GRID_CELL_CHANGING,      1 )
EVT_GRID_CMD_CELL_CHANGED =        wx.PyEventBinder( wxEVT_GRID_CELL_CHANGED,       1 )
EVT_GRID_CMD_SELECT_CELL =         wx.PyEventBinder( wxEVT_GRID_SELECT_CELL,        1 )
EVT_GRID_CMD_EDITOR_SHOWN =        wx.PyEventBinder( wxEVT_GRID_EDITOR_SHOWN,       1 )
EVT_GRID_CMD_EDITOR_HIDDEN =       wx.PyEventBinder( wxEVT_GRID_EDITOR_HIDDEN,      1 )
EVT_GRID_CMD_EDITOR_CREATED =      wx.PyEventBinder( wxEVT_GRID_EDITOR_CREATED,     1 )
EVT_GRID_CMD_CELL_BEGIN_DRAG =     wx.PyEventBinder( wxEVT_GRID_CELL_BEGIN_DRAG,    1 )
EVT_GRID_CMD_ROW_MOVE =            wx.PyEventBinder( wxEVT_GRID_ROW_MOVE,           1 )
EVT_GRID_CMD_COL_MOVE =            wx.PyEventBinder( wxEVT_GRID_COL_MOVE,           1 )
EVT_GRID_CMD_COL_SORT =            wx.PyEventBinder( wxEVT_GRID_COL_SORT,           1 )
EVT_GRID_CMD_TABBING =             wx.PyEventBinder( wxEVT_GRID_TABBING,            1 )

# Just for compatibility, remove them in a future release
EVT_GRID_RANGE_SELECT =            EVT_GRID_RANGE_SELECTED
EVT_GRID_CMD_RANGE_SELECT =        EVT_GRID_CMD_RANGE_SELECTED
wxEVT_GRID_RANGE_SELECT =          wxEVT_GRID_RANGE_SELECTED
#-- end-grid --#
