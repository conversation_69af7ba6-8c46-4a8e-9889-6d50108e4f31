// propertysheet.sip generated by MetaSIP
//
// This file is part of the QtDesigner Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDesignerPropertySheetExtension
{
%TypeHeaderCode
#include <propertysheet.h>
%End

public:
    virtual ~QDesignerPropertySheetExtension();
    virtual int count() const = 0 /__len__/;
    virtual int indexOf(const QString &name) const = 0;
    virtual QString propertyName(int index) const = 0;
    virtual QString propertyGroup(int index) const = 0;
    virtual void setPropertyGroup(int index, const QString &group) = 0;
    virtual bool hasReset(int index) const = 0;
    virtual bool reset(int index) = 0;
    virtual bool isVisible(int index) const = 0;
    virtual void setVisible(int index, bool b) = 0;
    virtual bool isAttribute(int index) const = 0;
    virtual void setAttribute(int index, bool b) = 0;
    virtual QVariant property(int index) const = 0;
    virtual void setProperty(int index, const QVariant &value) = 0;
    virtual bool isChanged(int index) const = 0;
    virtual void setChanged(int index, bool changed) = 0;
    virtual bool isEnabled(int index) const;
};
