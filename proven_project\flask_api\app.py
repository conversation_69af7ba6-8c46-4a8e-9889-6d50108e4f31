"""Flask API for Fingerprint Management System

This module provides a RESTful API interface for fingerprint operations including:
- Capturing new fingerprints
- Verifying fingerprints against stored templates
- Retrieving stored fingerprint images
- Matching fingerprints

The API communicates with a fingerprint bridge service via TCP socket and stores
fingerprint templates in a MySQL database.

Author: AratekTrustFinger Team
"""

import logging
import os
import base64
import pymysql
import socket
import time
import sys

from flask import Flask, request, jsonify
from capture import capture_fingerprint_bmp
from verify import verify_fingerprint
from match import match_fingerprint
from flask_cors import CORS

# Setup logging
if getattr(sys, 'frozen', False):
    bundle_dir = sys._MEIPASS
    log_file = os.path.join(bundle_dir, 'app.log')
else:
    log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    # handlers=[logging.FileHandler(log_file), logging.StreamHandler()]
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),  # File logging with emoji-safe encoding
        logging.StreamHandler()  # Console logging
    ]

)

app = Flask(__name__)
CORS(app)

# -----------------------------
# ✅ GET SAVED IMAGE (BMP) FROM DB
# -----------------------------
@app.route('/get-image', methods=['GET'])
def get_image():
    """Retrieve a stored fingerprint image from the database.
    
    Query Parameters:
        person_id (str): The unique identifier of the person
        finger_index (str): The index of the finger (1-10)
    
    Returns:
        JSON: Contains base64 encoded BMP image or error message
    """
    person_id = request.args.get('person_id')
    finger_index = request.args.get('finger_index')

    if not person_id or not finger_index:
        return jsonify({"error": "Missing person_id or finger_index"}), 400

    try:
        conn = pymysql.connect(host='localhost', user='root', password='sa', database='finger')
        cursor = conn.cursor()
        cursor.execute("SELECT image_bmp FROM fingerprint_templates WHERE person_id = %s AND finger_index = %s",
                       (person_id, finger_index))
        result = cursor.fetchone()
        conn.close()

        if not result:
            return jsonify({"error": "No image found"}), 404

        image_bmp = result[0]
        encoded_image = base64.b64encode(image_bmp).decode('utf-8')
        return jsonify({"image_base64": encoded_image})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


# -----------------------------
# ✅ CAPTURE
# -----------------------------
@app.route('/capture', methods=['POST'])
def capture():
    """Capture a new fingerprint and store it in the database.
    
    Request Body:
        person_id (str): The unique identifier of the person
        finger_index (int): The index of the finger (1-10)
    
    Returns:
        JSON: Contains status, message, and base64 encoded BMP image
              - status: 'success' or 'error'
              - message: Description of the operation result
              - bmp_base64: Base64 encoded fingerprint image (if successful)
    """
    data = request.get_json()
    person_id = data.get('person_id')
    finger_index = int(data.get('finger_index', 1))

    if not person_id:
        logging.error("Missing person_id in /capture request")
        return jsonify({"status": "ມີຄວາມຜິດພາດ", "message": "ບໍ່ມີ person_id"}), 400

    logging.info(f"Calling capture_fingerprint_bmp({person_id}, {finger_index})")
    result = capture_fingerprint_bmp(person_id, finger_index)
    logging.info(f"Bridge response: {result}")
    return jsonify(result)


# -----------------------------
# ✅ VERIFY
# -----------------------------
@app.route('/verify', methods=['POST'])
def verify():
    """Verify a captured fingerprint against stored template.
    
    Request Body:
        person_id (str): The unique identifier of the person
        finger_index (int): The index of the finger (1-10)
    
    Returns:
        JSON: Contains verification result
              - status: 'match', 'no_match', or 'error'
              - message: Description of the verification result
              - bmp_base64: Base64 encoded fingerprint image (if available)
    """
    data = request.get_json()
    person_id = data.get('person_id')
    finger_index = int(data.get('finger_index', 1))

    if not person_id:
        logging.error("Missing person_id in /verify request")
        return jsonify({"status": "error", "message": "Missing person_id"}), 400

    logging.info(f"Calling verify_fingerprint({person_id}, {finger_index})")
    result = verify_fingerprint(person_id, finger_index)
    logging.info(f"Bridge response: {result}")
    return jsonify(result)

# def send_to_bridge(command, payload):
#     import socket
#     with socket.create_connection(("localhost", 5050), timeout=10) as sock:
#         sock.sendall((command + "\n" + payload + "\n").encode())
#         sock.shutdown(socket.SHUT_WR)
#         response = sock.recv(65536).decode()
#     return response


# @app.route('/verify', methods=['POST'])
# def verify_fingerprint():
#     try:
#         data = request.get_json()
#         person_id = data.get('person_id')
#         finger_index = data.get('finger_index')

#         if not person_id or finger_index is None:
#             return jsonify({'status': 'error', 'message': 'Missing person_id or finger_index'}), 400

#         logging.info(f"Calling verify_fingerprint({person_id}, {finger_index})")

#         response = send_to_bridge("VERIFY", f"{person_id},{finger_index}")

#         if response.startswith("✅ Match") or response.startswith("❌ No Match"):
#             bmp_line = None
#             for line in response.splitlines():
#                 if line.startswith("BMP:"):
#                     bmp_line = line[4:]
#                     break

#             return jsonify({
#                 'status': 'match' if "✅" in response else 'no_match',
#                 'message': response.splitlines()[0],
#                 'bmp_base64': bmp_line or ''
#             })
#         else:
#             return jsonify({
#                 'status': 'no_match',
#                 'message': response,
#                 'bmp_base64': ''
#             })
#     except Exception as e:
#         logging.exception("Error in /verify")
#         return jsonify({'status': 'error', 'message': str(e), 'bmp_base64': ''}), 500

# -----------------------------
# ✅ MATCH
# -----------------------------
@app.route('/match', methods=['POST'])
def match():
    logging.info("Calling match_fingerprint()")
    result = match_fingerprint()
    logging.info(f"Bridge response: {result}")
    return jsonify(result)


# -----------------------------
# ✅ RUN SERVER
# -----------------------------
if __name__ == '__main__':
    try:
        logging.info("Starting Flask API service on http://0.0.0.0:5001")
        app.run(host='0.0.0.0', port=5001, debug=False)
    except Exception as e:
        logging.exception("Exception occurred while running the Flask API service: %s", e)
