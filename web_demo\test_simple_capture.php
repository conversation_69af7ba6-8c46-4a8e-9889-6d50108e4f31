<?php
// Simple test to debug the capture API parameter binding issue

echo "<h2>Testing Simple Capture API Call</h2>\n";

// Test with minimal data first
$testData = [
    'FingerPosition' => 1,
    'OperationType' => 'flat'
];

echo "Testing with minimal data:<br>\n";
echo "Request JSON: " . json_encode($testData, JSON_PRETTY_PRINT) . "<br>\n";

// Make API call
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:9000/api/fingerprint/capture');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 35);
curl_setopt($ch, CURLOPT_VERBOSE, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode<br>\n";

if ($curlError) {
    echo "CURL Error: $curlError<br>\n";
} elseif ($response === false) {
    echo "No response received<br>\n";
} else {
    echo "Raw Response: <pre>$response</pre><br>\n";
    
    $result = json_decode($response, true);
    if ($result) {
        echo "Parsed Response: " . json_encode($result, JSON_PRETTY_PRINT) . "<br>\n";
    } else {
        echo "Failed to parse JSON response<br>\n";
    }
}

echo "<hr>\n";

// Test with empty body to see if that works
echo "<h3>Testing with empty POST body</h3>\n";

$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, 'http://localhost:9000/api/fingerprint/capture');
curl_setopt($ch2, CURLOPT_POST, true);
curl_setopt($ch2, CURLOPT_POSTFIELDS, '');
curl_setopt($ch2, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_TIMEOUT, 35);

$response2 = curl_exec($ch2);
$httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
curl_close($ch2);

echo "HTTP Code: $httpCode2<br>\n";
echo "Response: <pre>$response2</pre><br>\n";

echo "<hr>\n";

// Test with null JSON
echo "<h3>Testing with null JSON</h3>\n";

$ch3 = curl_init();
curl_setopt($ch3, CURLOPT_URL, 'http://localhost:9000/api/fingerprint/capture');
curl_setopt($ch3, CURLOPT_POST, true);
curl_setopt($ch3, CURLOPT_POSTFIELDS, 'null');
curl_setopt($ch3, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch3, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch3, CURLOPT_TIMEOUT, 35);

$response3 = curl_exec($ch3);
$httpCode3 = curl_getinfo($ch3, CURLINFO_HTTP_CODE);
curl_close($ch3);

echo "HTTP Code: $httpCode3<br>\n";
echo "Response: <pre>$response3</pre><br>\n";
?>
