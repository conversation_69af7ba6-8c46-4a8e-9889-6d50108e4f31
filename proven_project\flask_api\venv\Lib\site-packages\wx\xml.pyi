# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
Some simple XML classes for use with XRC.

For more advanced XML needs it would be better to use one of the XML packages
provided by Python.
"""
#-- begin-_xml --#

import wx
XML_NO_INDENTATION: int

class _XmlNodeType(IntEnum):
    XML_ELEMENT_NODE = auto()
    XML_ATTRIBUTE_NODE = auto()
    XML_TEXT_NODE = auto()
    XML_CDATA_SECTION_NODE = auto()
    XML_ENTITY_REF_NODE = auto()
    XML_ENTITY_NODE = auto()
    XML_PI_NODE = auto()
    XML_COMMENT_NODE = auto()
    XML_DOCUMENT_NODE = auto()
    XML_DOCUMENT_TYPE_NODE = auto()
    XML_DOCUMENT_FRAG_NODE = auto()
    XML_NOTATION_NODE = auto()
    XML_HTML_DOCUMENT_NODE = auto()
XmlNodeType: TypeAlias = Union[_XmlNodeType, int]
XML_ELEMENT_NODE = _XmlNodeType.XML_ELEMENT_NODE
XML_ATTRIBUTE_NODE = _XmlNodeType.XML_ATTRIBUTE_NODE
XML_TEXT_NODE = _XmlNodeType.XML_TEXT_NODE
XML_CDATA_SECTION_NODE = _XmlNodeType.XML_CDATA_SECTION_NODE
XML_ENTITY_REF_NODE = _XmlNodeType.XML_ENTITY_REF_NODE
XML_ENTITY_NODE = _XmlNodeType.XML_ENTITY_NODE
XML_PI_NODE = _XmlNodeType.XML_PI_NODE
XML_COMMENT_NODE = _XmlNodeType.XML_COMMENT_NODE
XML_DOCUMENT_NODE = _XmlNodeType.XML_DOCUMENT_NODE
XML_DOCUMENT_TYPE_NODE = _XmlNodeType.XML_DOCUMENT_TYPE_NODE
XML_DOCUMENT_FRAG_NODE = _XmlNodeType.XML_DOCUMENT_FRAG_NODE
XML_NOTATION_NODE = _XmlNodeType.XML_NOTATION_NODE
XML_HTML_DOCUMENT_NODE = _XmlNodeType.XML_HTML_DOCUMENT_NODE

class _XmlDocumentLoadFlag(IntEnum):
    XMLDOC_NONE = auto()
    XMLDOC_KEEP_WHITESPACE_NODES = auto()
XmlDocumentLoadFlag: TypeAlias = Union[_XmlDocumentLoadFlag, int]
XMLDOC_NONE = _XmlDocumentLoadFlag.XMLDOC_NONE
XMLDOC_KEEP_WHITESPACE_NODES = _XmlDocumentLoadFlag.XMLDOC_KEEP_WHITESPACE_NODES

class XmlNode:
    """
    XmlNode(parent, type, name, content='', attrs=None, next=None, lineNo=-1) -> None
    XmlNode(type, name, content='', lineNo=-1) -> None
    XmlNode(node) -> None
    
    Represents a node in an XML document.
    """

    @overload
    def __init__(self, type: XmlNodeType, name: str, content: str='', lineNo: int=-1) -> None:
        ...

    @overload
    def __init__(self, node: XmlNode) -> None:
        ...

    @overload
    def __init__(self, parent: XmlNode, type: XmlNodeType, name: str, content: str='', attrs: Optional[XmlAttribute]=None, next: Optional[XmlNode]=None, lineNo: int=-1) -> None:
        """
        XmlNode(parent, type, name, content='', attrs=None, next=None, lineNo=-1) -> None
        XmlNode(type, name, content='', lineNo=-1) -> None
        XmlNode(node) -> None
        
        Represents a node in an XML document.
        """

    @overload
    def AddAttribute(self, attr: XmlAttribute) -> None:
        ...

    @overload
    def AddAttribute(self, name: str, value: str) -> None:
        """
        AddAttribute(name, value) -> None
        AddAttribute(attr) -> None
        
        Appends an attribute with given name and value to the list of
        attributes for this node.
        """

    def AddChild(self, child: XmlNode) -> None:
        """
        AddChild(child) -> None
        
        Adds node child as the last child of this node.
        """

    def DeleteAttribute(self, name: str) -> bool:
        """
        DeleteAttribute(name) -> bool
        
        Removes the first attributes which has the given name from the list of
        attributes for this node.
        """

    def GetAttribute(self, attrName: str, defaultVal: str='') -> str:
        """
        GetAttribute(attrName, defaultVal='') -> str
        
        Returns the value of the attribute named attrName if it does exist.
        """

    def GetAttributes(self) -> XmlAttribute:
        """
        GetAttributes() -> XmlAttribute
        
        Return a pointer to the first attribute of this node.
        """

    def GetChildren(self) -> XmlNode:
        """
        GetChildren() -> XmlNode
        
        Returns the first child of this node.
        """

    def GetContent(self) -> str:
        """
        GetContent() -> str
        
        Returns the content of this node.
        """

    def GetDepth(self, grandparent: Optional[XmlNode]=None) -> int:
        """
        GetDepth(grandparent=None) -> int
        
        Returns the number of nodes which separate this node from grandparent.
        """

    def GetNoConversion(self) -> bool:
        """
        GetNoConversion() -> bool
        
        Returns a flag indicating whether encoding conversion is necessary
        when saving.
        """

    def GetLineNumber(self) -> int:
        """
        GetLineNumber() -> int
        
        Returns line number of the node in the input XML file or -1 if it is
        unknown.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns the name of this node.
        """

    def GetNext(self) -> XmlNode:
        """
        GetNext() -> XmlNode
        
        Returns a pointer to the sibling of this node or NULL if there are no
        siblings.
        """

    def GetNodeContent(self) -> str:
        """
        GetNodeContent() -> str
        
        Returns the content of the first child node of type wxXML_TEXT_NODE or
        wxXML_CDATA_SECTION_NODE.
        """

    def GetParent(self) -> XmlNode:
        """
        GetParent() -> XmlNode
        
        Returns a pointer to the parent of this node or NULL if this node has
        no parent.
        """

    def GetType(self) -> XmlNodeType:
        """
        GetType() -> XmlNodeType
        
        Returns the type of this node.
        """

    def HasAttribute(self, attrName: str) -> bool:
        """
        HasAttribute(attrName) -> bool
        
        Returns true if this node has a attribute named attrName.
        """

    def InsertChild(self, child: XmlNode, followingNode: XmlNode) -> bool:
        """
        InsertChild(child, followingNode) -> bool
        
        Inserts the child node immediately before followingNode in the
        children list.
        """

    def InsertChildAfter(self, child: XmlNode, precedingNode: XmlNode) -> bool:
        """
        InsertChildAfter(child, precedingNode) -> bool
        
        Inserts the child node immediately after precedingNode in the children
        list.
        """

    def IsWhitespaceOnly(self) -> bool:
        """
        IsWhitespaceOnly() -> bool
        
        Returns true if the content of this node is a string containing only
        whitespaces (spaces, tabs, new lines, etc).
        """

    def RemoveChild(self, child: XmlNode) -> bool:
        """
        RemoveChild(child) -> bool
        
        Removes the given node from the children list.
        """

    def SetContent(self, con: str) -> None:
        """
        SetContent(con) -> None
        
        Sets the content of this node.
        """

    def SetName(self, name: str) -> None:
        """
        SetName(name) -> None
        
        Sets the name of this node.
        """

    def SetNext(self, next: XmlNode) -> None:
        """
        SetNext(next) -> None
        
        Sets as sibling the given node.
        """

    def SetNoConversion(self, noconversion: bool) -> None:
        """
        SetNoConversion(noconversion) -> None
        
        Sets a flag to indicate whether encoding conversion is necessary when
        saving.
        """

    def SetParent(self, parent: XmlNode) -> None:
        """
        SetParent(parent) -> None
        
        Sets as parent the given node.
        """

    def SetType(self, type: XmlNodeType) -> None:
        """
        SetType(type) -> None
        
        Sets the type of this node.
        """
    @property
    def Attributes(self) -> XmlAttribute: ...
    @property
    def Children(self) -> XmlNode: ...
    @property
    def Content(self) -> str: ...
    @Content.setter
    def Content(self, value: str, /) -> None: ...
    @property
    def Depth(self) -> int: ...
    @property
    def LineNumber(self) -> int: ...
    @property
    def Name(self) -> str: ...
    @Name.setter
    def Name(self, value: str, /) -> None: ...
    @property
    def Next(self) -> XmlNode: ...
    @Next.setter
    def Next(self, value: XmlNode, /) -> None: ...
    @property
    def NoConversion(self) -> bool: ...
    @NoConversion.setter
    def NoConversion(self, value: bool, /) -> None: ...
    @property
    def NodeContent(self) -> str: ...
    @property
    def Parent(self) -> XmlNode: ...
    @Parent.setter
    def Parent(self, value: XmlNode, /) -> None: ...
    @property
    def Type(self) -> XmlNodeType: ...
    @Type.setter
    def Type(self, value: XmlNodeType, /) -> None: ...
# end of class XmlNode


class XmlAttribute:
    """
    XmlAttribute() -> None
    XmlAttribute(name, value, next=None) -> None
    
    Represents a node attribute.
    """

    @overload
    def __init__(self, name: str, value: str, next: Optional[XmlAttribute]=None) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        XmlAttribute() -> None
        XmlAttribute(name, value, next=None) -> None
        
        Represents a node attribute.
        """

    def GetName(self) -> str:
        """
        GetName() -> str
        
        Returns the name of this attribute.
        """

    def GetNext(self) -> XmlAttribute:
        """
        GetNext() -> XmlAttribute
        
        Returns the sibling of this attribute or NULL if there are no
        siblings.
        """

    def GetValue(self) -> str:
        """
        GetValue() -> str
        
        Returns the value of this attribute.
        """

    def SetName(self, name: str) -> None:
        """
        SetName(name) -> None
        
        Sets the name of this attribute.
        """

    def SetNext(self, next: XmlAttribute) -> None:
        """
        SetNext(next) -> None
        
        Sets the sibling of this attribute.
        """

    def SetValue(self, value: str) -> None:
        """
        SetValue(value) -> None
        
        Sets the value of this attribute.
        """
    @property
    def Name(self) -> str: ...
    @Name.setter
    def Name(self, value: str, /) -> None: ...
    @property
    def Next(self) -> XmlAttribute: ...
    @Next.setter
    def Next(self, value: XmlAttribute, /) -> None: ...
    @property
    def Value(self) -> str: ...
    @Value.setter
    def Value(self, value: str, /) -> None: ...
# end of class XmlAttribute


class XmlDocument(wx.wx.Object):
    """
    XmlDocument() -> None
    XmlDocument(doc) -> None
    XmlDocument(filename, encoding="UTF-8") -> None
    XmlDocument(stream, encoding="UTF-8") -> None
    
    This class holds XML data/document as parsed by XML parser in the root
    node.
    """

    @overload
    def __init__(self, doc: XmlDocument) -> None:
        ...

    @overload
    def __init__(self, filename: str, encoding: str="UTF-8") -> None:
        ...

    @overload
    def __init__(self, stream: InputStream, encoding: str="UTF-8") -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        XmlDocument() -> None
        XmlDocument(doc) -> None
        XmlDocument(filename, encoding="UTF-8") -> None
        XmlDocument(stream, encoding="UTF-8") -> None
        
        This class holds XML data/document as parsed by XML parser in the root
        node.
        """

    def AppendToProlog(self, node: XmlNode) -> None:
        """
        AppendToProlog(node) -> None
        
        Appends a Process Instruction or Comment node to the document
        prologue.
        """

    def DetachDocumentNode(self) -> XmlNode:
        """
        DetachDocumentNode() -> XmlNode
        
        Detaches the document node and returns it.
        """

    def DetachRoot(self) -> XmlNode:
        """
        DetachRoot() -> XmlNode
        
        Detaches the root entity node and returns it.
        """

    def GetFileEncoding(self) -> str:
        """
        GetFileEncoding() -> str
        
        Returns encoding of document (may be empty).
        """

    def GetDoctype(self) -> XmlDoctype:
        """
        GetDoctype() -> XmlDoctype
        
        Returns the DOCTYPE declaration data for the document.
        """

    def GetFileType(self) -> TextFileType:
        """
        GetFileType() -> TextFileType
        
        Returns the output line ending format used for documents.
        """

    def GetEOL(self) -> str:
        """
        GetEOL() -> str
        
        Returns the output line ending string used for documents.
        """

    def GetDocumentNode(self) -> XmlNode:
        """
        GetDocumentNode() -> XmlNode
        
        Returns the document node of the document.
        """

    def GetRoot(self) -> XmlNode:
        """
        GetRoot() -> XmlNode
        
        Returns the root element node of the document.
        """

    def GetVersion(self) -> str:
        """
        GetVersion() -> str
        
        Returns the version of document.
        """

    def IsOk(self) -> bool:
        """
        IsOk() -> bool
        
        Returns true if the document has been loaded successfully.
        """

    @overload
    def Load(self, stream: InputStream, encoding: str="UTF-8", flags: int=XMLDOC_NONE) -> bool:
        ...

    @overload
    def Load(self, filename: str, encoding: str="UTF-8", flags: int=XMLDOC_NONE) -> bool:
        """
        Load(filename, encoding="UTF-8", flags=XMLDOC_NONE) -> bool
        Load(stream, encoding="UTF-8", flags=XMLDOC_NONE) -> bool
        
        Parses filename as an xml document and loads its data.
        """

    @overload
    def Save(self, stream: OutputStream, indentstep: int=2) -> bool:
        ...

    @overload
    def Save(self, filename: str, indentstep: int=2) -> bool:
        """
        Save(filename, indentstep=2) -> bool
        Save(stream, indentstep=2) -> bool
        
        Saves XML tree creating a file named with given string.
        """

    def SetDocumentNode(self, node: XmlNode) -> None:
        """
        SetDocumentNode(node) -> None
        
        Sets the document node of this document.
        """

    def SetFileEncoding(self, encoding: str) -> None:
        """
        SetFileEncoding(encoding) -> None
        
        Sets the encoding of the file which will be used to save the document.
        """

    def SetDoctype(self, doctype: XmlDoctype) -> None:
        """
        SetDoctype(doctype) -> None
        
        Sets the data which will appear in the DOCTYPE declaration when the
        document is saved.
        """

    def SetFileType(self, fileType: TextFileType) -> None:
        """
        SetFileType(fileType) -> None
        
        Sets the output line ending formats when the document is saved.
        """

    def SetRoot(self, node: XmlNode) -> None:
        """
        SetRoot(node) -> None
        
        Sets the root element node of this document.
        """

    def SetVersion(self, version: str) -> None:
        """
        SetVersion(version) -> None
        
        Sets the version of the XML file which will be used to save the
        document.
        """

    @staticmethod
    def GetLibraryVersionInfo() -> VersionInfo:
        """
        GetLibraryVersionInfo() -> VersionInfo
        
        Get expat library version information.
        """
    @property
    def Doctype(self) -> XmlDoctype: ...
    @Doctype.setter
    def Doctype(self, value: XmlDoctype, /) -> None: ...
    @property
    def DocumentNode(self) -> XmlNode: ...
    @DocumentNode.setter
    def DocumentNode(self, value: XmlNode, /) -> None: ...
    @property
    def EOL(self) -> str: ...
    @property
    def FileEncoding(self) -> str: ...
    @FileEncoding.setter
    def FileEncoding(self, value: str, /) -> None: ...
    @property
    def FileType(self) -> TextFileType: ...
    @FileType.setter
    def FileType(self, value: TextFileType, /) -> None: ...
    @property
    def Root(self) -> XmlNode: ...
    @Root.setter
    def Root(self, value: XmlNode, /) -> None: ...
    @property
    def Version(self) -> str: ...
    @Version.setter
    def Version(self, value: str, /) -> None: ...
# end of class XmlDocument


class XmlDoctype:
    """
    XmlDoctype(rootName="", systemId="", publicId="") -> None
    
    Represents a DOCTYPE Declaration.
    """

    def __init__(self, rootName: str="", systemId: str="", publicId: str="") -> None:
        """
        XmlDoctype(rootName="", systemId="", publicId="") -> None
        
        Represents a DOCTYPE Declaration.
        """

    def Clear(self) -> None:
        """
        Clear() -> None
        
        Removes all the DOCTYPE values.
        """

    def GetRootName(self) -> str:
        """
        GetRootName() -> str
        
        Returns the root name of the document.
        """

    def GetSystemId(self) -> str:
        """
        GetSystemId() -> str
        
        Returns the system id of the document.
        """

    def GetPublicId(self) -> str:
        """
        GetPublicId() -> str
        
        Returns the public id of the document.
        """

    def GetFullString(self) -> str:
        """
        GetFullString() -> str
        
        Returns the formatted DOCTYPE contents.
        """

    def IsValid(self) -> bool:
        """
        IsValid() -> bool
        
        Returns true if the contents can produce a valid DOCTYPE string.
        """
    @property
    def FullString(self) -> str: ...
    @property
    def PublicId(self) -> str: ...
    @property
    def RootName(self) -> str: ...
    @property
    def SystemId(self) -> str: ...
# end of class XmlDoctype


XmlProperty = wx.deprecated(XmlAttribute, 'Use XmlProperty instead.')
#-- end-_xml --#
