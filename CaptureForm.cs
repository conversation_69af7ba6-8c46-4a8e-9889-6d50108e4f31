﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;
using Aratek.TrustFinger;
using System.Threading;
using System.IO;

namespace MultiFingerDemo
{
    public partial class CaptureForm : Form
    {
        public struct stFourFingersOptionTypeTable
        {
            public stFourFingersOptionTypeTable(int index, string aszOptionTypeDesc, EnumOperationType nSDKOperationType, CapFingerPosition nFingerPosition)
            {
                m_nIndex = index;
                m_aszOptionTypeDesc = aszOptionTypeDesc;
                m_nSDKOperationType = nSDKOperationType;
                m_nFingerPosition = nFingerPosition;
            }
            public int m_nIndex;
            public string m_aszOptionTypeDesc;
            public EnumOperationType m_nSDKOperationType;
            public CapFingerPosition m_nFingerPosition;
        }
        public stFourFingersOptionTypeTable[] sg_stFourFingersOptionTypeTable = new stFourFingersOptionTypeTable[]
        {
            new stFourFingersOptionTypeTable(0, "Left four fingers", EnumOperationType.SLAP_4_LEFT_FINGERS, CapFingerPosition.LeftFour ),
            new stFourFingersOptionTypeTable(1, "Right four fingers", EnumOperationType.SLAP_4_RIGHT_FINGERS, CapFingerPosition.RightFour),
            new stFourFingersOptionTypeTable(2, "Two thumbs", EnumOperationType.SLAP_2_THUMBS_FINGERS, CapFingerPosition.TwoThumb),
            new stFourFingersOptionTypeTable(3, "Flat left thumb", EnumOperationType.FLAT_LEFT_THUMB_FINGER, CapFingerPosition.LeftThumb),
            new stFourFingersOptionTypeTable(4, "Flat left index", EnumOperationType.FLAT_LEFT_INDEX_FINGER, CapFingerPosition.LeftIndex),
            new stFourFingersOptionTypeTable(5, "Flat left middle", EnumOperationType.FLAT_LEFT_MIDDLE_FINGER, CapFingerPosition.LeftMiddle),
            new stFourFingersOptionTypeTable(6, "Flat left ring", EnumOperationType.FLAT_LEFT_RING_FINGER, CapFingerPosition.LeftRing),
            new stFourFingersOptionTypeTable(7, "Flat left little", EnumOperationType.FLAT_LEFT_LITTLE_FINGER, CapFingerPosition.LeftLittle),
            new stFourFingersOptionTypeTable(8, "Flat right thumb", EnumOperationType.FLAT_RIGHT_THUMB_FINGER, CapFingerPosition.RightThumb),
            new stFourFingersOptionTypeTable(9, "Flat right index", EnumOperationType.FLAT_RIGHT_INDEX_FINGER, CapFingerPosition.RightIndex),
            new stFourFingersOptionTypeTable(10, "Flat right middle", EnumOperationType.FLAT_RIGHT_MIDDLE_FINGER, CapFingerPosition.RightMiddle),
            new stFourFingersOptionTypeTable(11, "Flat right ring", EnumOperationType.FLAT_RIGHT_RING_FINGER, CapFingerPosition.RightRing ),
            new stFourFingersOptionTypeTable(12, "Flat right little", EnumOperationType.FLAT_RIGHT_LITTLE_FINGER, CapFingerPosition.RightLittle),
            new stFourFingersOptionTypeTable(13, "Rolled left thumb", EnumOperationType.ROLL_LEFT_THUMB_FINGER, CapFingerPosition.LeftThumb ),
            new stFourFingersOptionTypeTable(14, "Rolled left index", EnumOperationType.ROLL_LEFT_INDEX_FINGER, CapFingerPosition.LeftIndex  ),
            new stFourFingersOptionTypeTable(15, "Rolled left middle", EnumOperationType.ROLL_LEFT_MIDDLE_FINGER, CapFingerPosition.LeftMiddle),
            new stFourFingersOptionTypeTable(16, "Rolled left ring", EnumOperationType.ROLL_LEFT_RING_FINGER, CapFingerPosition.LeftRing),
            new stFourFingersOptionTypeTable(17, "Rolled left little", EnumOperationType.ROLL_LEFT_LITTLE_FINGER, CapFingerPosition.LeftLittle ),
            new stFourFingersOptionTypeTable(18, "Rolled right thumb", EnumOperationType.ROLL_RIGHT_THUMB_FINGER, CapFingerPosition.RightThumb ),
            new stFourFingersOptionTypeTable(19, "Rolled right index", EnumOperationType.ROLL_RIGHT_INDEX_FINGER, CapFingerPosition.RightIndex),
            new stFourFingersOptionTypeTable(20, "Rolled right middle", EnumOperationType.ROLL_RIGHT_MIDDLE_FINGER, CapFingerPosition.RightMiddle ),
            new stFourFingersOptionTypeTable(21, "Rolled right ring", EnumOperationType.ROLL_RIGHT_RING_FINGER, CapFingerPosition.RightRing ),
            new stFourFingersOptionTypeTable(22, "Rolled right little", EnumOperationType.ROLL_RIGHT_LITTLE_FINGER, CapFingerPosition.RightLittle),
        };

        public struct stTwoFingersOptionTypeTable
        {
            public stTwoFingersOptionTypeTable(int index, string aszOptionTypeDesc, EnumOperationType nSDKOperationType, CapFingerPosition nFingerPosition)
            {
                m_nIndex = index;
                m_aszOptionTypeDesc = aszOptionTypeDesc;
                m_nSDKOperationType = nSDKOperationType;
                m_nFingerPosition = nFingerPosition;
            }
            public int m_nIndex;
            public string m_aszOptionTypeDesc;
            public EnumOperationType m_nSDKOperationType;
            public CapFingerPosition m_nFingerPosition;
        }
        public stTwoFingersOptionTypeTable[] sg_stTwoFingersOptionTypeTable = new stTwoFingersOptionTypeTable[]
        {
            new stTwoFingersOptionTypeTable(0, "Two fingers", EnumOperationType.SLAP_ANY_TWO_FINGERS, CapFingerPosition.TwoFinger ),

            new stTwoFingersOptionTypeTable(1, "Flat right thumb", EnumOperationType.FLAT_RIGHT_THUMB_FINGER, CapFingerPosition.RightThumb),
            new stTwoFingersOptionTypeTable(2, "Flat right index", EnumOperationType.FLAT_RIGHT_INDEX_FINGER, CapFingerPosition.RightIndex),
            new stTwoFingersOptionTypeTable(3, "Flat right middle", EnumOperationType.FLAT_RIGHT_MIDDLE_FINGER, CapFingerPosition.RightMiddle),
            new stTwoFingersOptionTypeTable(4, "Flat right ring", EnumOperationType.FLAT_RIGHT_RING_FINGER, CapFingerPosition.RightRing ),
            new stTwoFingersOptionTypeTable(5, "Flat right little", EnumOperationType.FLAT_RIGHT_LITTLE_FINGER, CapFingerPosition.RightLittle),

            new stTwoFingersOptionTypeTable(6, "Flat left thumb", EnumOperationType.FLAT_LEFT_THUMB_FINGER, CapFingerPosition.LeftThumb),
            new stTwoFingersOptionTypeTable(7, "Flat left index", EnumOperationType.FLAT_LEFT_INDEX_FINGER, CapFingerPosition.LeftIndex),
            new stTwoFingersOptionTypeTable(8, "Flat left middle", EnumOperationType.FLAT_LEFT_MIDDLE_FINGER, CapFingerPosition.LeftMiddle),
            new stTwoFingersOptionTypeTable(9, "Flat left ring", EnumOperationType.FLAT_LEFT_RING_FINGER, CapFingerPosition.LeftRing),
            new stTwoFingersOptionTypeTable(10, "Flat left little", EnumOperationType.FLAT_LEFT_LITTLE_FINGER, CapFingerPosition.LeftLittle),

            new stTwoFingersOptionTypeTable(11, "Rolled right thumb", EnumOperationType.ROLL_RIGHT_THUMB_FINGER, CapFingerPosition.RightThumb ),
            new stTwoFingersOptionTypeTable(12, "Rolled right index", EnumOperationType.ROLL_RIGHT_INDEX_FINGER, CapFingerPosition.RightIndex),
            new stTwoFingersOptionTypeTable(13, "Rolled right middle", EnumOperationType.ROLL_RIGHT_MIDDLE_FINGER, CapFingerPosition.RightMiddle ),
            new stTwoFingersOptionTypeTable(14, "Rolled right ring", EnumOperationType.ROLL_RIGHT_RING_FINGER, CapFingerPosition.RightRing ),
            new stTwoFingersOptionTypeTable(15, "Rolled right little", EnumOperationType.ROLL_RIGHT_LITTLE_FINGER, CapFingerPosition.RightLittle),

            new stTwoFingersOptionTypeTable(16, "Rolled left thumb", EnumOperationType.ROLL_LEFT_THUMB_FINGER, CapFingerPosition.LeftThumb ),
            new stTwoFingersOptionTypeTable(17, "Rolled left index", EnumOperationType.ROLL_LEFT_INDEX_FINGER, CapFingerPosition.LeftIndex  ),
            new stTwoFingersOptionTypeTable(18, "Rolled left middle", EnumOperationType.ROLL_LEFT_MIDDLE_FINGER, CapFingerPosition.LeftMiddle),
            new stTwoFingersOptionTypeTable(19, "Rolled left ring", EnumOperationType.ROLL_LEFT_RING_FINGER, CapFingerPosition.LeftRing),
            new stTwoFingersOptionTypeTable(20, "Rolled left little", EnumOperationType.ROLL_LEFT_LITTLE_FINGER, CapFingerPosition.LeftLittle ),
        };

        private TrustFingerDevice devCapture = null;
        private int nMissingFinger1 = 0;
        private int nMissingFinger2 = 0;
        private int nDevType = 0;
        private int nImgFormat = 0;
        private int nMainCurrentIndex = 0;
        private EnumOperationType currentOperationType = 0;
        private bool capStatus = false;
        private bool bLive = false;
        private bool bRollFinger = false;

        public uint fingerNum = 0;
        public int srcImgW = 0;
        public int srcImgH = 0;
        Bitmap gbmp;

        public struct DrawFingerFarme
        {
            public int TopLeftX;
            public int TopLeftY;
            public int ImgW;
            public int ImgH;
            public int ARAIQ;
            public int ImgNFIQ;
            public int RollX;
            public int fingerPos;
            public int fingerContrast;
            public int ulY;
            public int ulX;
            public int urY;
            public int urX;
            public int llY;
            public int llX;
            public int lrY;
            public int lrX;
            public int centerD;
        }

        DrawFingerFarme[] fingerFarmelist = new DrawFingerFarme[4];

        public class AcquisitionEvents
        {
            public const int SEGMENT_SUCCESS_END = 0;
            public const int ACQUISITION_LIVE_CAPTURE = 1;
            public const int DURATION_END = 2;
            public const int EXTRACT_FEATURE_FAIL = 3;
            public const int ROLL_SUCCESS_END = 4;
            public const int ROLL_AREA_SMALL_END = 5;
            public const int LFDCHECK_FAIL_END = 6;
            public const int LEFT_RIGHT_HAND_CHECK_FAIL_END = 7;
            public const int ROLLED_IAMGE_DISCONTINUITY = 8;
            public const int ROLLED_IMAGE_BACK = 9;
            public const int ROLLED_MULTI_FINGER = 10;
            public const int CAPTURE_STOP = 11;
            public const int CAPTURE_FAIL = 12;
            public const int CONTRAST_LOW_RETRY = 13;
            public const int IMAGE_QUALITY_LOW_RETRY = 14;
        }

        public CaptureForm(TrustFingerDevice devCap, int missingFinge1, int missingFinge2, int devType/*int missingCount*/, int imgFormat, int currentIndex)
        {
            InitializeComponent();
            devCapture = devCap;
            nMissingFinger1 = missingFinge1;
            nMissingFinger2 = missingFinge2;
            //nMissingCount = missingCount;
            nDevType = devType;
            nImgFormat = imgFormat;
            nMainCurrentIndex = currentIndex;

            lfdlevel_comboBox.Items.Clear();
            lfdlevel_comboBox.Items.Add("ExtraLow");
            lfdlevel_comboBox.Items.Add("Low");
            lfdlevel_comboBox.Items.Add("Medium");
            lfdlevel_comboBox.Items.Add("High");
            lfdlevel_comboBox.Items.Add("UltraHigh");

            // Load ORIGINAL VENDOR SETTINGS from config
            int lfdLevel = ConfigManager.GetIntValue("lfd_level", 3); // Original: Medium (index 2, but 0-based = 2)
            lfdlevel_comboBox.SelectedIndex = Math.Max(0, Math.Min(4, lfdLevel - 1)); // Convert to 0-based index

            bool lfdEnabled = ConfigManager.GetBoolValue("lfd_enabled", true); // Original: ENABLED
            lfd_checkBox.Checked = lfdEnabled;

            int captureTimeout = ConfigManager.GetIntValue("capture_timeout", 10); // Original: 10 seconds
            timeout_textBox.Text = captureTimeout.ToString();

            // Use original vendor settings - no special ring finger handling
            bool vendorSettings = ConfigManager.GetBoolValue("vendor_original_settings", true);
            if (vendorSettings)
            {
                // Add visual indicator for vendor original settings
                this.Text = "Capture - VENDOR ORIGINAL SETTINGS";
            }

//             lfd_checkBox.Visible = false;
//             label3.Visible = false;
//             lfdlevel_comboBox.Visible = false;
            open();
           
        }

        public static void CopyRawImageIntoBitmap(Byte[] RawImage, ref Bitmap bmpImage)
        {
            try
            {
                BitmapData bmData;
                int iw = bmpImage.Width, ih = bmpImage.Height;
                Rectangle bmpRect = new Rectangle(0, 0, iw, ih);

                bmpImage = new Bitmap(iw, ih, PixelFormat.Format8bppIndexed);

                bmData = bmpImage.LockBits(bmpRect,
                        ImageLockMode.WriteOnly,
                        PixelFormat.Format8bppIndexed);

                int diff = bmData.Stride - bmpImage.Width;
                if (diff == 0)
                {
                    Marshal.Copy(RawImage, 0, bmData.Scan0, bmpImage.Width * bmpImage.Height);
                }
                else
                {
                    int RawIndex = 0;
                    long Ptr = (long)bmData.Scan0;
                    for (int i = 0; i < bmpImage.Height; i++, RawIndex += bmpImage.Width)
                    {
                        Marshal.Copy(RawImage, RawIndex, (IntPtr)Ptr, bmpImage.Width);
                        Ptr += bmData.Stride;
                    }
                }

                // Unlock the bits
                bmpImage.UnlockBits(bmData);

                ColorPalette pal = bmpImage.Palette;
                for (int i = 0; i < pal.Entries.Length; i++)
                {
                    pal.Entries[i] = Color.FromArgb(255, i, i, i);
                }
                bmpImage.Palette = pal;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + " in CopyRawImageIntoBitmap", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
        }

        public string FingerPosChangeString(int nPos)
        {
            string strPositon;
            switch (nPos)
            {
                case 1:
                    strPositon = "Right_thumb";
                    break;
                case 2:
                    strPositon = "Right_index";
                    break;
                case 3:
                    strPositon = "Right_middle";
                    break;
                case 4:
                    strPositon = "Right_ring";
                    break;
                case 5:
                    strPositon = "Right_little";
                    break;
                case 6:
                    strPositon = "Left_thumb";
                    break;
                case 7:
                    strPositon = "Left_index";
                    break;
                case 8:
                    strPositon = "Left_middle";
                    break;
                case 9:
                    strPositon = "Left_ring";
                    break;
                case 10:
                    strPositon = "Left_little";
                    break;
                default:
                    strPositon = "Default";
                    break;
            }

            return strPositon;
        }

        public string FingerPosChangeStringUIshow(int nPos)
        {
            string strPositon;
            switch (nPos)
            {
                case 1:
                    strPositon = "RThumb";
                    break;
                case 6:
                    strPositon = "LThumb";
                    break;
                case 2:
                case 7:
                    strPositon = "Index";
                    break;
                case 3:
                case 8:
                    strPositon = "Middle";
                    break;
                case 4:
                case 9:
                    strPositon = "Ring";
                    break;
                case 5:
                case 10:
                    strPositon = "Little";
                    break;
                default:
                    strPositon = "";
                    break;
            }

            return strPositon;
        }

        public delegate void TransferDataDelegate(EnumOperationType oType, int nPos, Bitmap bmpImage, IntPtr feature, int nIQ, int nNFIQ, int A800LedFlag);
        public event TransferDataDelegate TransferPreDataEvent;

//         public delegate void TransferSegDataDelegate(int nFingerPos, Bitmap bmpImage, IntPtr feature);
//         public event TransferSegDataDelegate TransferSegDataEvent;

        public delegate void TransferCloseDelegate();
        public event TransferCloseDelegate TransferClosePre;

        public Bitmap Convert8BppTo24Bpp(Bitmap src8bpp)
        {
            // 加载8bpp的Bitmap

            // 创建一个24bpp的Bitmap
            Bitmap dest24Bpp = new Bitmap(src8bpp.Width, src8bpp.Height, PixelFormat.Format24bppRgb);

            // 锁定源和目标位图的像素区域
            using (Graphics g = Graphics.FromImage(dest24Bpp))
            {
                // 使用画图Graphics对象将8bpp的Bitmap转换为24bpp
                g.DrawImageUnscaled(src8bpp, 0, 0);
            }

            // 返回转换后的24bpp Bitmap
            return dest24Bpp;
        }

        public int captureCallback(
                int OccurredEventCode,
                byte[] FramePtr,
                int FrameSizeX,
                int FrameSizeY,
                NativeSegmentImagDesc[] segmentImageList,
                uint segmentedNum
            )
        {
            int bExit = 0;

            bLive = false;
            bRollFinger = false;
            //uint seg_iq_pass_num = 0;
            fingerNum = 0;
            Bitmap previewBitmap = null;
            string dir = AppDomain.CurrentDomain.BaseDirectory;
            string capturePath = Path.Combine(dir, "FingerData");
            if (!Directory.Exists(capturePath))
                Directory.CreateDirectory(capturePath);

            srcImgW = FrameSizeX;
            srcImgH = FrameSizeY;

            previewBitmap = new Bitmap(FrameSizeX, FrameSizeY, PixelFormat.Format8bppIndexed);

            switch (OccurredEventCode)
            {
                case AcquisitionEvents.SEGMENT_SUCCESS_END:
                case AcquisitionEvents.DURATION_END:
                case AcquisitionEvents.EXTRACT_FEATURE_FAIL:
                case AcquisitionEvents.CAPTURE_FAIL:
                case AcquisitionEvents.CONTRAST_LOW_RETRY:
                case AcquisitionEvents.IMAGE_QUALITY_LOW_RETRY:
                    {
                        fingerNum = segmentedNum;
                       // seg_iq_pass_num = segmentedPassNum;

                        CopyRawImageIntoBitmap(FramePtr, ref previewBitmap);
                        Bitmap bmp = Convert8BppTo24Bpp(previewBitmap);
                        gbmp = bmp;

                        if (segmentedNum > 1)
                            TransferPreDataEvent(currentOperationType, 0, previewBitmap, IntPtr.Zero, 0, 0, 0);//大图
                        if(segmentedNum == 1)
                            TransferPreDataEvent(currentOperationType, 100, previewBitmap, IntPtr.Zero, 0, 0, 0);//大图

                        int nFingerPos = 0, nFingerPosUI = 0;
                        for (int i = 0; i < segmentedNum; i++)
                        {
                            Bitmap smallBitmap = null;
                            int size = segmentImageList[i].SubFingerWidth * segmentImageList[i].SubFingerHeight;
                            smallBitmap = new Bitmap(segmentImageList[i].SubFingerWidth, segmentImageList[i].SubFingerHeight, PixelFormat.Format8bppIndexed);

                            byte[] smallImg = new byte[size];
                            Marshal.Copy(segmentImageList[i].SegmentImagePtr, smallImg, 0, size);
                            CopyRawImageIntoBitmap(smallImg, ref smallBitmap);

                            int NFIQ = devCapture.ImageNFIQ(smallImg, segmentImageList[i].SubFingerWidth, segmentImageList[i].SubFingerHeight);

                            if (nDevType == 800)
                            {
                                nFingerPos = i + 1;
                                if (currentOperationType == EnumOperationType.SLAP_ANY_TWO_FINGERS)
                                {
                                    int min = 0; int max = 0;
                                    if (nMissingFinger1 < nMissingFinger2)
                                    {
                                        min = nMissingFinger1;
                                        max = nMissingFinger2;
                                    }
                                    else
                                    {
                                        min = nMissingFinger2;
                                        max = nMissingFinger1;
                                    }

                                    if (nFingerPos == 1)
                                        nFingerPosUI = ((max < 6) ? min : max);
                                    else if (nFingerPos == 2)
                                        nFingerPosUI = ((max < 6) ? max : min);
                                }
                                else
                                    nFingerPosUI = segmentImageList[i].FingerPos;
                            }
                            else
                            {
                                nFingerPosUI = segmentImageList[i].FingerPos;
                                nFingerPos = segmentImageList[i].FingerPos;

                                // Use original vendor position detection - no overrides
                            }

                            TransferPreDataEvent(currentOperationType, nFingerPos, smallBitmap, segmentImageList[i].FeatureData, segmentImageList[i].Quality, NFIQ, 2);
                            
                            fingerFarmelist[i].TopLeftX = segmentImageList[i].FingerTopLeftX;
                            fingerFarmelist[i].TopLeftY = segmentImageList[i].FingerTopLeftY;
                            fingerFarmelist[i].ImgW = segmentImageList[i].SubFingerWidth;
                            fingerFarmelist[i].ImgH = segmentImageList[i].SubFingerHeight;
                            fingerFarmelist[i].ARAIQ = segmentImageList[i].Quality;
                            fingerFarmelist[i].ImgNFIQ = NFIQ;
                            fingerFarmelist[i].fingerPos = nFingerPosUI;
                            fingerFarmelist[i].ulY = segmentImageList[i].segFPinfo.ulY;
                            fingerFarmelist[i].ulX = segmentImageList[i].segFPinfo.ulX;
                            fingerFarmelist[i].urY = segmentImageList[i].segFPinfo.urY;
                            fingerFarmelist[i].urX = segmentImageList[i].segFPinfo.urX;
                            fingerFarmelist[i].llY = segmentImageList[i].segFPinfo.llY;
                            fingerFarmelist[i].llX = segmentImageList[i].segFPinfo.llX;
                            fingerFarmelist[i].lrY = segmentImageList[i].segFPinfo.lrY;
                            fingerFarmelist[i].lrX = segmentImageList[i].segFPinfo.lrX;
                            fingerFarmelist[i].centerD = segmentImageList[i].segFPinfo.centerD;



                            if (0 == nImgFormat)//BMP
                            {
                                string strPos = FingerPosChangeString(segmentImageList[i].FingerPos);
                                byte[] feature = new byte[segmentImageList[i].FeatureLength];
                                Marshal.Copy(segmentImageList[i].FeatureData, feature, 0, segmentImageList[i].FeatureLength);
                                this.BeginInvoke(new Action(() =>
                                {
                                    if (savepic_checkBox.Checked)
                                    {
                                        string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                        string namestr = "\\Finger_" + time + "_" + strPos + "_" + i + ".bmp";
                                        smallBitmap.Save(capturePath + namestr, System.Drawing.Imaging.ImageFormat.Bmp);

                                        string featurenamestr = "\\feature_" + time + "_" + strPos + "_" + i + ".bin";
                                        File.WriteAllBytes(capturePath + featurenamestr, feature);
                                    }
                                }));
                            }
                            else if (1 == nImgFormat)//FMR-ISO2005
                            {
                                byte[] wsq = new byte[segmentImageList[i].FeatureLength];
                                Marshal.Copy(segmentImageList[i].FeatureData, wsq, 0, segmentImageList[i].FeatureLength);
                                string strPos = FingerPosChangeString(segmentImageList[i].FingerPos);

                                if (savepic_checkBox.Checked)
                                {
                                    string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                    string namestr = "\\FMRISO2005_" + time + "_" + strPos + "_" + i + ".bin";
                                    File.WriteAllBytes(capturePath + namestr, wsq);
                                }
                            }
                            else if (2 == nImgFormat)//FMR-ISO2011
                            {
                                byte[] wsq = new byte[segmentImageList[i].FeatureLength];
                                Marshal.Copy(segmentImageList[i].FeatureData, wsq, 0, segmentImageList[i].FeatureLength);
                                string strPos = FingerPosChangeString(segmentImageList[i].FingerPos);

                                if (savepic_checkBox.Checked)
                                {
                                    string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                    string namestr = "\\FMRISO2011_" + time + "_" + strPos + "_" + i + ".bin";
                                    File.WriteAllBytes(capturePath + namestr, wsq);
                                }
                            }
                            else if (3 == nImgFormat)//FIR-2005WSQ10
                            {
                                byte[] wsq = new byte[segmentImageList[i].FirLength];
                                Marshal.Copy(segmentImageList[i].FirData, wsq, 0, segmentImageList[i].FirLength);
                                string strPos = FingerPosChangeString(segmentImageList[i].FingerPos);

                                if (savepic_checkBox.Checked)
                                {
                                    string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                    string namestr = "\\FIR2005WSQ10_" + time + "_" + strPos + "_" + i + ".wsq";
                                    File.WriteAllBytes(capturePath + namestr, wsq);
                                }
                            }
                            else if (4 == nImgFormat)//FIR-2005WSQ15
                            {
                                byte[] wsq = new byte[segmentImageList[i].FirLength];
                                Marshal.Copy(segmentImageList[i].FirData, wsq, 0, segmentImageList[i].FirLength);
                                string strPos = FingerPosChangeString(segmentImageList[i].FingerPos);

                                if (savepic_checkBox.Checked)
                                {
                                    string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                    string namestr = "\\FIR2005WSQ15_" + time + "_" + strPos + "_" + i + ".wsq";
                                    File.WriteAllBytes(capturePath + namestr, wsq);
                                }
                            }
                            else if (5 == nImgFormat)//FIR-2011WSQ10
                            {
                                byte[] wsq = new byte[segmentImageList[i].FirLength];
                                Marshal.Copy(segmentImageList[i].FirData, wsq, 0, segmentImageList[i].FirLength);
                                string strPos = FingerPosChangeString(segmentImageList[i].FingerPos);

                                if (savepic_checkBox.Checked)
                                {
                                    string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                    string namestr = "\\FIR2011WSQ10_" + time + "_" + strPos + "_" + i + ".wsq";
                                    File.WriteAllBytes(capturePath + namestr, wsq);
                                }
                            }
                            else if (6 == nImgFormat)//FIR-2011WSQ15
                            {
                                byte[] wsq = new byte[segmentImageList[i].FirLength];
                                Marshal.Copy(segmentImageList[i].FirData, wsq, 0, segmentImageList[i].FirLength);
                                string strPos = FingerPosChangeString(segmentImageList[i].FingerPos);

                                if (savepic_checkBox.Checked)
                                {
                                    string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                    string namestr = "\\FIR2011WSQ15_" + time + "_" + strPos + "_" + i + ".wsq";
                                    File.WriteAllBytes(capturePath + namestr, wsq);
                                }
                            }
                            else if (7 == nImgFormat)//WSQ15
                            {
                                byte[] wsq = new byte[segmentImageList[i].FirLength];
                                Marshal.Copy(segmentImageList[i].FirData, wsq, 0, segmentImageList[i].FirLength);
                                string strPos = FingerPosChangeString(segmentImageList[i].FingerPos);

                                if (savepic_checkBox.Checked)
                                {
                                    string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                    string namestr = "\\WSQ15_" + time + "_" + strPos + "_" + i + ".wsq";
                                    File.WriteAllBytes(capturePath + namestr, wsq);
                                }
                            }
                        }
                        
                       

                        this.BeginInvoke(new Action(() =>
                        {
                            UpdateUI();
                            //preview_pictureBox.Refresh();

                            if (OccurredEventCode == AcquisitionEvents.SEGMENT_SUCCESS_END)
                                message_textBox.AppendText("\r\nCapture success");
                            else
                            {
                                if (OccurredEventCode == AcquisitionEvents.DURATION_END)
                                    message_textBox.AppendText("\r\nCapture timeout,retry");
                                else if(OccurredEventCode == AcquisitionEvents.EXTRACT_FEATURE_FAIL)
                                    message_textBox.AppendText("\r\nExtrct feature fail");
                                else if (OccurredEventCode == AcquisitionEvents.CAPTURE_FAIL)
                                    message_textBox.AppendText("\r\nCapture image fail");
                                else if (OccurredEventCode == AcquisitionEvents.CONTRAST_LOW_RETRY)
                                    message_textBox.AppendText("\r\nFinger contrast is low,suggest retry");
                                else if (OccurredEventCode == AcquisitionEvents.IMAGE_QUALITY_LOW_RETRY)
                                    message_textBox.AppendText("\r\nImage quality is low,suggest retry");
                                else
                                    message_textBox.AppendText("\r\nImage quality low, or finger number incorrect");
                            }


                            if (savepic_checkBox.Checked)
                            {
                                string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                string namestr = "\\MultipleFinger_" + time + ".bmp";
                                previewBitmap.Save(capturePath + namestr, System.Drawing.Imaging.ImageFormat.Bmp);
                            }
                        }));


                        break;
                    }
                    
                case AcquisitionEvents.ACQUISITION_LIVE_CAPTURE:
                    {
                        bLive = true;
                        fingerFarmelist[0].ImgW = segmentImageList[0].SubFingerWidth;
                        fingerFarmelist[0].RollX = segmentImageList[0].RollPostionX;
                        fingerFarmelist[0].ARAIQ = segmentImageList[0].Quality;
                        CopyRawImageIntoBitmap(FramePtr, ref previewBitmap);
                        Thread.Sleep(50);
                        preview_pictureBox.Image = previewBitmap;

                        bExit = 0;
                        break;
                    }

                //case AcquisitionEvents.DURATION_END:
                //    {
                //        this.BeginInvoke(new Action(() =>
                //        {
                //            message_textBox.AppendText("\r\nTime out");
                //        }));

                //        bExit=1;
                //        if(nDevType == 800)
                //            TransferPreDataEvent(currentOperationType, 100, null, IntPtr.Zero, 0, 0, 1);
                        
                //        break;
                //    }

//                 case AcquisitionEvents.EXTRACT_FEATURE_FAIL:
//                     {
//                         string strPosInfo = "";
//                         for (int i = 0; i < segmentedNum; i++)
//                         {
//                             if(segmentImageList[i].FeatureLength < 512)
//                             {
//                                 strPosInfo = FingerPosChangeString(segmentImageList[i].FingerPos);
// 
//                                 strPosInfo = "\r\n" + strPosInfo + $" Extract failed,return {segmentImageList[i].FeatureLength}";
//                             }
//                         }
// 
//                         this.BeginInvoke(new Action(() =>
//                         {
//                             message_textBox.AppendText(strPosInfo);
//                             if (savepic_checkBox.Checked)
//                             {
//                                 CopyRawImageIntoBitmap(FramePtr, ref previewBitmap);
//                                 string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
//                                 string namestr = "\\ExtractFail_" + time + ".bmp";
//                                 previewBitmap.Save(capturePath + namestr, System.Drawing.Imaging.ImageFormat.Bmp);
//                             }
//                         }));
// 
//                         bExit=1;
//                         if (nDevType == 800)
//                             TransferPreDataEvent(currentOperationType, 100, null, (IntPtr)0, 0, 0, 1);
// 
//                         break;
//                     }

                case AcquisitionEvents.ROLL_SUCCESS_END:
                    {
                        bLive = false;
                        bRollFinger = true;
                        fingerNum = segmentedNum;
                       
                        for (int i = 0; i < segmentedNum; i++)
                        {
                            Bitmap smallBitmap = null;
                            int size = segmentImageList[i].SubFingerWidth * segmentImageList[i].SubFingerHeight;
                            smallBitmap = new Bitmap(segmentImageList[i].SubFingerWidth, segmentImageList[i].SubFingerHeight, PixelFormat.Format8bppIndexed);

                            byte[] smallImg = new byte[size];
                            Marshal.Copy(segmentImageList[i].SegmentImagePtr, smallImg, 0, size);

                            int NFIQ = devCapture.ImageNFIQ(smallImg, segmentImageList[i].SubFingerWidth, segmentImageList[i].SubFingerHeight);

                            fingerFarmelist[i].TopLeftX = segmentImageList[i].FingerTopLeftX;
                            fingerFarmelist[i].TopLeftY = segmentImageList[i].FingerTopLeftY;
                            fingerFarmelist[i].ImgW = segmentImageList[i].SubFingerWidth;
                            fingerFarmelist[i].ImgH = segmentImageList[i].SubFingerHeight;
                            fingerFarmelist[i].ARAIQ = segmentImageList[i].Quality;
                            fingerFarmelist[i].ImgNFIQ = NFIQ;


                            CopyRawImageIntoBitmap(smallImg, ref smallBitmap);

                            TransferPreDataEvent(currentOperationType, 0, smallBitmap, segmentImageList[i].FeatureData, 0, NFIQ, 0);//滚采无分数判断

                            CopyRawImageIntoBitmap(FramePtr, ref previewBitmap);
                            //preview_pictureBox.Image = previewBitmap;
                            //bNeedDraw = true;
                            Bitmap bmp = Convert8BppTo24Bpp(previewBitmap);
                            gbmp = bmp;

                            this.BeginInvoke(new Action(() =>
                            {
                                UpdateUI();
                                message_textBox.AppendText("\r\nRolled success");
                                if (savepic_checkBox.Checked)
                                {
                                    string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                    string namestr = "\\RolledFinger_" + time + ".bmp";
                                    smallBitmap.Save(capturePath + namestr, System.Drawing.Imaging.ImageFormat.Bmp);
                                }
                            }));
                        }
                        
                        break;
                    }

                case AcquisitionEvents.ROLL_AREA_SMALL_END:
                    {
                        bLive = false;
                        this.BeginInvoke(new Action(() =>
                        {
                            message_textBox.AppendText("\r\nRolled area too small");
                            for (int i = 0; i < segmentedNum; i++)
                            {
                                if (savepic_checkBox.Checked)
                                {
                                    Bitmap smallBitmap = null;
                                    int size = segmentImageList[i].SubFingerWidth * segmentImageList[i].SubFingerHeight;
                                    smallBitmap = new Bitmap(segmentImageList[i].SubFingerWidth, segmentImageList[i].SubFingerHeight, PixelFormat.Format8bppIndexed);

                                    byte[] smallImg = new byte[size];
                                    Marshal.Copy(segmentImageList[i].SegmentImagePtr, smallImg, 0, size);

                                    CopyRawImageIntoBitmap(smallImg, ref smallBitmap);
                                    string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                    string namestr = "\\RollTooSmall_" + time + ".bmp";
                                    smallBitmap.Save(capturePath + namestr, System.Drawing.Imaging.ImageFormat.Bmp);
                                }
                            }
                        }));

                        bExit = 1;
                        if (nDevType == 800)
                            TransferPreDataEvent(currentOperationType, 100, null, IntPtr.Zero, 0, 0, 1);

                        break;
                    }

                case AcquisitionEvents.LFDCHECK_FAIL_END:
                    {
                        CopyRawImageIntoBitmap(FramePtr, ref previewBitmap);
                        preview_pictureBox.Image = previewBitmap;
                        
                        this.BeginInvoke(new Action(() =>
                        {
                            message_textBox.AppendText("\r\nFake finger detected");
                            if (savepic_checkBox.Checked)
                            {
                                string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                string namestr = "\\CheckLFDfail_" + time + ".bmp";
                                previewBitmap.Save(capturePath + namestr, System.Drawing.Imaging.ImageFormat.Bmp);
                            }
                        }));

                        bExit = 1;
                        if (nDevType == 800)
                            TransferPreDataEvent(currentOperationType, 100, null, (IntPtr)0, 0, 0, 1);

                        break;
                    }

                case AcquisitionEvents.LEFT_RIGHT_HAND_CHECK_FAIL_END:
                    {
                        this.BeginInvoke(new Action(() =>
                        {
                            message_textBox.AppendText("\r\nLeft right hand check fail");
                            if (savepic_checkBox.Checked)
                            {
                                CopyRawImageIntoBitmap(FramePtr, ref previewBitmap);
                                preview_pictureBox.Image = previewBitmap;
                                string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                string namestr = "\\CheckHandFail_" + time + ".bmp";
                                previewBitmap.Save(capturePath + namestr, System.Drawing.Imaging.ImageFormat.Bmp);
                            }
                        }));

                        bExit = 1;
                        if (nDevType == 800)
                            TransferPreDataEvent(currentOperationType, 100, null, IntPtr.Zero, 0, 0, 1);

                        break;
                    }
                case AcquisitionEvents.ROLLED_IAMGE_DISCONTINUITY:
                    {
                        bLive = false;
                        this.BeginInvoke(new Action(() =>
                        {
                            message_textBox.AppendText("\r\nRolled image discontinuity,retry");
                            if (savepic_checkBox.Checked)
                            {
                                CopyRawImageIntoBitmap(FramePtr, ref previewBitmap);
                                preview_pictureBox.Image = previewBitmap;

                                string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                string namestr = "\\RolledDiscontinuity_" + time + ".bmp";
                                previewBitmap.Save(capturePath + namestr, System.Drawing.Imaging.ImageFormat.Bmp);
                            }
                        }));

                        bExit = 1;
                        if (nDevType == 800)
                            TransferPreDataEvent(currentOperationType, 100, null, IntPtr.Zero, 0, 0, 1);

                        break;
                    }
                case AcquisitionEvents.ROLLED_IMAGE_BACK:
                    {
                        bLive = false;
                        this.BeginInvoke(new Action(() =>
                        {
                            message_textBox.AppendText("\r\nRolled back");
                            if (savepic_checkBox.Checked)
                            {
                                CopyRawImageIntoBitmap(FramePtr, ref previewBitmap);
                                preview_pictureBox.Image = previewBitmap;

                                string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                string namestr = "\\RolledBack_" + time + ".bmp";
                                previewBitmap.Save(capturePath + namestr, System.Drawing.Imaging.ImageFormat.Bmp);
                            }
                        }));

                        bExit = 1;
                        if (nDevType == 800)
                            TransferPreDataEvent(currentOperationType, 100, null, IntPtr.Zero, 0, 0, 1);

                        break;
                    }
                case AcquisitionEvents.ROLLED_MULTI_FINGER:
                    {
                        bLive = false;
                        this.BeginInvoke(new Action(() =>
                        {
                            message_textBox.AppendText("\r\nPlease press one finger roll");
                            if (savepic_checkBox.Checked)
                            {
                                CopyRawImageIntoBitmap(FramePtr, ref previewBitmap);
                                preview_pictureBox.Image = previewBitmap;

                                string time = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                                string namestr = "\\RolledMultiFinger_" + time + ".bmp";
                                previewBitmap.Save(capturePath + namestr, System.Drawing.Imaging.ImageFormat.Bmp);
                            }
                        }));

                        bExit = 1;
                        if (nDevType == 800)
                            TransferPreDataEvent(currentOperationType, 100, null, IntPtr.Zero, 0, 0, 1);

                        break;
                    }
                case AcquisitionEvents.CAPTURE_STOP:
                    {
                        this.BeginInvoke(new Action(() =>
                        {
                            message_textBox.AppendText("\r\nCapture stop,repeat");
                        }));

                        bExit = 1;
                        if (nDevType == 800)
                            TransferPreDataEvent(currentOperationType, 100, null, IntPtr.Zero, 0, 0, 1);

                        break;
                    }
            }

            return bExit;
        }

        public void open()
        {
            int index = 0;
            FPoperation_comboBox.Items.Clear();

            if(nDevType == 800)
            {
                for (int i = 0; i < sg_stTwoFingersOptionTypeTable.Length; i++)
                {
                    FPoperation_comboBox.Items.Add(sg_stTwoFingersOptionTypeTable[i].m_aszOptionTypeDesc);
                }
                if (nMissingFinger1 == 0)
                {
                    index = nMissingFinger2;
                }
                if (nMissingFinger2 == 0)
                {
                    index = nMissingFinger1;
                }
                if (nMissingFinger2 == 0 && nMainCurrentIndex == 1)
                {
                    index = nMissingFinger1 + 10;//roll
                }
            }
            else
            {
                for (int i = 0; i < sg_stFourFingersOptionTypeTable.Length; i++)
                {
                    FPoperation_comboBox.Items.Add(sg_stFourFingersOptionTypeTable[i].m_aszOptionTypeDesc);
                }
                index = 0;
            }
            
            FPoperation_comboBox.SelectedIndex = index;
        }

        private void FPoperation_comboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            int nComboxIDX = FPoperation_comboBox.SelectedIndex;
            if(nDevType == 800)
            {
                if (nComboxIDX >= 11 && nComboxIDX <= 20)
                {
                    lfd_checkBox.Visible = false;
                    label3.Visible = false;
                    lfdlevel_comboBox.Visible = false;
                }
                else
                {
                    lfd_checkBox.Visible = true;
                    label3.Visible = true;
                    lfdlevel_comboBox.Visible = true;
                }
                for (int i = 0; i < sg_stTwoFingersOptionTypeTable.Length; i++)
                {
                    if (nComboxIDX == sg_stTwoFingersOptionTypeTable[i].m_nIndex)
                    {
                        currentOperationType = sg_stTwoFingersOptionTypeTable[i].m_nSDKOperationType;
                        setFingerPictureStatus(sg_stTwoFingersOptionTypeTable[i].m_nFingerPosition, true, nMissingFinger1, nMissingFinger2);
                        break;
                    }
                }
            }
            else
            {
                if (nComboxIDX >= 13 && nComboxIDX <= 22)
                {
                    lfd_checkBox.Visible = false;
                    label3.Visible = false;
                    lfdlevel_comboBox.Visible = false;
                }
                else
                {
                    lfd_checkBox.Visible = true;
                    label3.Visible = true;
                    lfdlevel_comboBox.Visible = true;
                }
                for (int i = 0; i < sg_stFourFingersOptionTypeTable.Length; i++)
                {
                    if (nComboxIDX == sg_stFourFingersOptionTypeTable[i].m_nIndex)
                    {
                        currentOperationType = sg_stFourFingersOptionTypeTable[i].m_nSDKOperationType;
                        setFingerPictureStatus(sg_stFourFingersOptionTypeTable[i].m_nFingerPosition, true, nMissingFinger1, nMissingFinger2);
                        break;
                    }
                }
            }
            

            startCapture(currentOperationType);
        }

        public void setFingerPictureStatus(CapFingerPosition capPosition, bool bClearstatus, int nSelectFinger1, int nSelectFinger2)
        {
            if (bClearstatus)
            {
                LL_pictureBox.Image = Properties.Resources.LL;
                LR_pictureBox.Image = Properties.Resources.LR;
                LM_pictureBox.Image = Properties.Resources.LM;
                LI_pictureBox.Image = Properties.Resources.LI;
                LT_pictureBox.Image = Properties.Resources.LT;

                RL_pictureBox.Image = Properties.Resources.RL;
                RR_pictureBox.Image = Properties.Resources.RR;
                RM_pictureBox.Image = Properties.Resources.RM;
                RI_pictureBox.Image = Properties.Resources.RI;
                RT_pictureBox.Image = Properties.Resources.RT;
            }

            switch(capPosition)
            {
                case CapFingerPosition.LeftThumb:
                    LT_pictureBox.Image = Properties.Resources.LT_P;
                    break;
                case CapFingerPosition.LeftIndex:
                    LI_pictureBox.Image = Properties.Resources.LI_P;
                    break;
                case CapFingerPosition.LeftMiddle:
                    LM_pictureBox.Image = Properties.Resources.LM_P;
                    break;
                case CapFingerPosition.LeftRing:
                    LR_pictureBox.Image = Properties.Resources.LR_P;
                    break;
                case CapFingerPosition.LeftLittle:
                    LL_pictureBox.Image = Properties.Resources.LL_P;
                    break;

                case CapFingerPosition.RightThumb:
                    RT_pictureBox.Image = Properties.Resources.RT_P;
                    break;
                case CapFingerPosition.RightIndex:
                    RI_pictureBox.Image = Properties.Resources.RI_P;
                    break;
                case CapFingerPosition.RightMiddle:
                    RM_pictureBox.Image = Properties.Resources.RM_P;
                    break;
                case CapFingerPosition.RightRing:
                    RR_pictureBox.Image = Properties.Resources.RR_P;
                    break;
                case CapFingerPosition.RightLittle:
                    RL_pictureBox.Image = Properties.Resources.RL_P;
                    break;

                case CapFingerPosition.LeftFour:
                    LI_pictureBox.Image = Properties.Resources.LI_P;
                    LM_pictureBox.Image = Properties.Resources.LM_P;
                    LR_pictureBox.Image = Properties.Resources.LR_P;
                    LL_pictureBox.Image = Properties.Resources.LL_P;

                    if (nSelectFinger1 == (int)CapFingerPosition.LeftIndex || nSelectFinger2 == (int)CapFingerPosition.LeftIndex)
                        LI_pictureBox.Image = Properties.Resources.LI;
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftMiddle || nSelectFinger2 == (int)CapFingerPosition.LeftMiddle)
                        LM_pictureBox.Image = Properties.Resources.LM;
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftRing || nSelectFinger2 == (int)CapFingerPosition.LeftRing)
                        LR_pictureBox.Image = Properties.Resources.LR;
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftLittle || nSelectFinger2 == (int)CapFingerPosition.LeftLittle)
                        LL_pictureBox.Image = Properties.Resources.LL;
                    break;
                case CapFingerPosition.RightFour:
                    RI_pictureBox.Image = Properties.Resources.RI_P;
                    RM_pictureBox.Image = Properties.Resources.RM_P;
                    RR_pictureBox.Image = Properties.Resources.RR_P;
                    RL_pictureBox.Image = Properties.Resources.RL_P;

                    if (nSelectFinger1 == (int)CapFingerPosition.RightIndex || nSelectFinger2 == (int)CapFingerPosition.RightIndex)
                        RI_pictureBox.Image = Properties.Resources.RI;
                    if (nSelectFinger1 == (int)CapFingerPosition.RightMiddle || nSelectFinger2 == (int)CapFingerPosition.RightMiddle)
                        RM_pictureBox.Image = Properties.Resources.RM;
                    if (nSelectFinger1 == (int)CapFingerPosition.RightRing || nSelectFinger2 == (int)CapFingerPosition.RightRing)
                        RR_pictureBox.Image = Properties.Resources.RR;
                    if (nSelectFinger1 == (int)CapFingerPosition.RightLittle || nSelectFinger2 == (int)CapFingerPosition.RightLittle)
                        RL_pictureBox.Image = Properties.Resources.RL;
                    break;
                case CapFingerPosition.TwoThumb:
                    LT_pictureBox.Image = Properties.Resources.LT_P;
                    RT_pictureBox.Image = Properties.Resources.RT_P;
                    break;
                case CapFingerPosition.TwoFinger:
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftIndex || nSelectFinger2 == (int)CapFingerPosition.LeftIndex)
                        LI_pictureBox.Image = Properties.Resources.LI_P;
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftMiddle || nSelectFinger2 == (int)CapFingerPosition.LeftMiddle)
                        LM_pictureBox.Image = Properties.Resources.LM_P;
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftRing || nSelectFinger2 == (int)CapFingerPosition.LeftRing)
                        LR_pictureBox.Image = Properties.Resources.LR_P;
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftLittle || nSelectFinger2 == (int)CapFingerPosition.LeftLittle)
                        LL_pictureBox.Image = Properties.Resources.LL_P;
                    if (nSelectFinger1 == (int)CapFingerPosition.RightIndex || nSelectFinger2 == (int)CapFingerPosition.RightIndex)
                        RI_pictureBox.Image = Properties.Resources.RI_P;
                    if (nSelectFinger1 == (int)CapFingerPosition.RightMiddle || nSelectFinger2 == (int)CapFingerPosition.RightMiddle)
                        RM_pictureBox.Image = Properties.Resources.RM_P;
                    if (nSelectFinger1 == (int)CapFingerPosition.RightRing || nSelectFinger2 == (int)CapFingerPosition.RightRing)
                        RR_pictureBox.Image = Properties.Resources.RR_P;
                    if (nSelectFinger1 == (int)CapFingerPosition.RightLittle || nSelectFinger2 == (int)CapFingerPosition.RightLittle)
                        RL_pictureBox.Image = Properties.Resources.RL_P;
                    if (nSelectFinger1 == (int)CapFingerPosition.RightThumb || nSelectFinger2 == (int)CapFingerPosition.RightThumb)
                        RT_pictureBox.Image = Properties.Resources.RT_P;
                    if (nSelectFinger1 == (int)CapFingerPosition.LeftThumb || nSelectFinger2 == (int)CapFingerPosition.LeftThumb)
                        LT_pictureBox.Image = Properties.Resources.LT_P;
                    break;
            }
        }

        public int TransitionFirfmrType()
        {
            int nFeaturetype = 0;
            if (0 == nImgFormat)//BMP_BIONE
            {
                nFeaturetype = (int)EnumFeatureFormatType.ARAFP_TYPE_FMR_BIONE;
            }
            else if (1 == nImgFormat)//FMR-ISO2005_BIONE
            {
                nFeaturetype = /*(int)EnumFeatureFormatType.ARAFP_TYPE_FMR_BIONE | */(int)EnumFeatureFormatType.ARAFP_TYPE_FMR_ISO_2005;
            }
            else if (2 == nImgFormat)//FMR-ISO2011_BIONE
            {
                nFeaturetype = /*(int)EnumFeatureFormatType.ARAFP_TYPE_FMR_BIONE |*/ (int)EnumFeatureFormatType.ARAFP_TYPE_FMR_ISO_2011;
            }
            else if (3 == nImgFormat)//FIR-2005WSQ10_BIONE
            {
                nFeaturetype = (int)EnumFeatureFormatType.ARAFP_TYPE_FMR_BIONE | (int)EnumFeatureFormatType.ARAFP_TYPE_FIR_2005_WSQ_10_1;
            }
            else if (4 == nImgFormat)//FIR-2005WSQ15_BIONE
            {
                nFeaturetype = (int)EnumFeatureFormatType.ARAFP_TYPE_FMR_BIONE | (int)EnumFeatureFormatType.ARAFP_TYPE_FIR_2005_WSQ_15_1;
            }
            else if (5 == nImgFormat)//FIR-2011WSQ10_BIONE
            {
                nFeaturetype = (int)EnumFeatureFormatType.ARAFP_TYPE_FMR_BIONE | (int)EnumFeatureFormatType.ARAFP_TYPE_FIR_2011_WSQ_10_1;
            }
            else if (6 == nImgFormat)//FIR-2011WSQ15_BIONE
            {
                nFeaturetype = (int)EnumFeatureFormatType.ARAFP_TYPE_FMR_BIONE | (int)EnumFeatureFormatType.ARAFP_TYPE_FIR_2011_WSQ_15_1;
            }
            else if (7 == nImgFormat)//WSQ15_BIONE
            {
                nFeaturetype = (int)EnumFeatureFormatType.ARAFP_TYPE_FMR_BIONE | (int)EnumFeatureFormatType.ARAFP_TYPE_WSQ_15_1;
            }

            return nFeaturetype;
        }

        public void startCapture(EnumOperationType operationType)
        {
            int ret = 0;
            int LFDLevel = 1;
            EnMissingFingerType missingFinger = 0;
            int featuretype = 0;
            featuretype = TransitionFirfmrType();

            // Use original vendor approach - no operation type conversion

            if (timeout_textBox.Text == "")  timeout_textBox.Text = "0";
            int second = int.Parse(timeout_textBox.Text); //0;//int.Parse(timeout_textBox.Text);

            // CRITICAL: Set A900 dry/wet finger level BEFORE capture (like original vendor)
            if (nDevType == 900)
            {
                int dryLevel = ConfigManager.GetIntValue("dry_level", 5); // Original vendor default: Medium_Dry (index 5)
                devCapture.MultiFingerSetDryWetFingerLevel((EnDryWetFingerLevelType)dryLevel);
            }

            if(capStatus)
            {
                ret = devCapture.MultiFingerStopAcquisition();
                if (ret != 0) return;
                capStatus = false;
            }

            if(nDevType == 800)
            {
                devCapture.SetLedStatus(0, LedStatus.Off);
                devCapture.SetLedStatus(1, LedStatus.Off);
                devCapture.SetLedStatus(1, LedStatus.On);
            }

            if(EnumOperationType.SLAP_4_LEFT_FINGERS == operationType)
            {
                if ((nMissingFinger1 == (int)CapFingerPosition.LeftIndex && nMissingFinger2 == 0) || (nMissingFinger2 == (int)CapFingerPosition.LeftIndex && nMissingFinger1 == 0))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_INDEX;
                else if ((nMissingFinger1 == (int)CapFingerPosition.LeftMiddle && nMissingFinger2 == 0) || (nMissingFinger2 == (int)CapFingerPosition.LeftMiddle && nMissingFinger1 == 0))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_MIDDLE;
                else if ((nMissingFinger1 == (int)CapFingerPosition.LeftRing && nMissingFinger2 == 0) || (nMissingFinger2 == (int)CapFingerPosition.LeftRing && nMissingFinger1 == 0))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_RING;
                else if ((nMissingFinger1 == (int)CapFingerPosition.LeftLittle && nMissingFinger2 == 0) || (nMissingFinger2 == (int)CapFingerPosition.LeftLittle && nMissingFinger1 == 0))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_LITTLE;
                //two
                else if ((nMissingFinger1 == (int)CapFingerPosition.LeftIndex && nMissingFinger2 == (int)CapFingerPosition.LeftMiddle) ||
                (nMissingFinger2 == (int)CapFingerPosition.LeftIndex && nMissingFinger1 == (int)CapFingerPosition.LeftMiddle))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_INDEX | EnMissingFingerType.MISSING_FINGER_SLAP4_MIDDLE;
                else if ((nMissingFinger1 == (int)CapFingerPosition.LeftIndex && nMissingFinger2 == (int)CapFingerPosition.LeftRing) ||
                (nMissingFinger2 == (int)CapFingerPosition.LeftIndex && nMissingFinger1 == (int)CapFingerPosition.LeftRing))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_INDEX | EnMissingFingerType.MISSING_FINGER_SLAP4_RING;
                else if ((nMissingFinger1 == (int)CapFingerPosition.LeftMiddle && nMissingFinger2 == (int)CapFingerPosition.LeftRing) ||
                (nMissingFinger2 == (int)CapFingerPosition.LeftMiddle && nMissingFinger1 == (int)CapFingerPosition.LeftRing))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_MIDDLE | EnMissingFingerType.MISSING_FINGER_SLAP4_RING;
                else if ((nMissingFinger1 == (int)CapFingerPosition.LeftIndex && nMissingFinger2 == (int)CapFingerPosition.LeftLittle) ||
                (nMissingFinger2 == (int)CapFingerPosition.LeftIndex && nMissingFinger1 == (int)CapFingerPosition.LeftLittle))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_INDEX | EnMissingFingerType.MISSING_FINGER_SLAP4_LITTLE;
                else if ((nMissingFinger1 == (int)CapFingerPosition.LeftMiddle && nMissingFinger2 == (int)CapFingerPosition.LeftLittle) ||
                (nMissingFinger2 == (int)CapFingerPosition.LeftMiddle && nMissingFinger1 == (int)CapFingerPosition.LeftLittle))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_MIDDLE | EnMissingFingerType.MISSING_FINGER_SLAP4_LITTLE;
                else if ((nMissingFinger1 == (int)CapFingerPosition.LeftRing && nMissingFinger2 == (int)CapFingerPosition.LeftLittle) ||
                (nMissingFinger2 == (int)CapFingerPosition.LeftRing && nMissingFinger1 == (int)CapFingerPosition.LeftLittle))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_RING | EnMissingFingerType.MISSING_FINGER_SLAP4_LITTLE;
            }

            if (EnumOperationType.SLAP_4_RIGHT_FINGERS == operationType)
            {
                if ((nMissingFinger1 == (int)CapFingerPosition.RightIndex && nMissingFinger2 == 0) || (nMissingFinger2 == (int)CapFingerPosition.RightIndex && nMissingFinger1 == 0))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_INDEX;
                else if ((nMissingFinger1 == (int)CapFingerPosition.RightMiddle && nMissingFinger2 == 0) || (nMissingFinger2 == (int)CapFingerPosition.RightMiddle && nMissingFinger1 == 0))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_MIDDLE;
                else if ((nMissingFinger1 == (int)CapFingerPosition.RightRing && nMissingFinger2 == 0) || (nMissingFinger2 == (int)CapFingerPosition.RightRing && nMissingFinger1 == 0))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_RING;
                else if ((nMissingFinger1 == (int)CapFingerPosition.RightLittle && nMissingFinger2 == 0) || (nMissingFinger2 == (int)CapFingerPosition.RightLittle && nMissingFinger1 == 0))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_LITTLE;
                //two
                else if ((nMissingFinger1 == (int)CapFingerPosition.RightIndex && nMissingFinger2 == (int)CapFingerPosition.RightMiddle) ||
                (nMissingFinger2 == (int)CapFingerPosition.RightIndex && nMissingFinger1 == (int)CapFingerPosition.RightMiddle))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_INDEX | EnMissingFingerType.MISSING_FINGER_SLAP4_MIDDLE;
                else if ((nMissingFinger1 == (int)CapFingerPosition.RightIndex && nMissingFinger2 == (int)CapFingerPosition.RightRing) ||
                (nMissingFinger2 == (int)CapFingerPosition.RightIndex && nMissingFinger1 == (int)CapFingerPosition.RightRing))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_INDEX | EnMissingFingerType.MISSING_FINGER_SLAP4_RING;
                else if ((nMissingFinger1 == (int)CapFingerPosition.RightMiddle && nMissingFinger2 == (int)CapFingerPosition.RightRing) ||
                (nMissingFinger2 == (int)CapFingerPosition.RightMiddle && nMissingFinger1 == (int)CapFingerPosition.RightRing))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_MIDDLE | EnMissingFingerType.MISSING_FINGER_SLAP4_RING;
                else if ((nMissingFinger1 == (int)CapFingerPosition.RightIndex && nMissingFinger2 == (int)CapFingerPosition.RightLittle) ||
                (nMissingFinger2 == (int)CapFingerPosition.RightIndex && nMissingFinger1 == (int)CapFingerPosition.RightLittle))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_INDEX | EnMissingFingerType.MISSING_FINGER_SLAP4_LITTLE;
                else if ((nMissingFinger1 == (int)CapFingerPosition.RightMiddle && nMissingFinger2 == (int)CapFingerPosition.RightLittle) ||
                (nMissingFinger2 == (int)CapFingerPosition.RightMiddle && nMissingFinger1 == (int)CapFingerPosition.RightLittle))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_MIDDLE | EnMissingFingerType.MISSING_FINGER_SLAP4_LITTLE;
                else if ((nMissingFinger1 == (int)CapFingerPosition.RightRing && nMissingFinger2 == (int)CapFingerPosition.RightLittle) ||
                (nMissingFinger2 == (int)CapFingerPosition.RightRing && nMissingFinger1 == (int)CapFingerPosition.RightLittle))
                    missingFinger = EnMissingFingerType.MISSING_FINGER_SLAP4_RING | EnMissingFingerType.MISSING_FINGER_SLAP4_LITTLE;
            }

            ret = devCapture.MultiFingerSetMissingFingers(missingFinger);

            if (lfd_checkBox.Checked)
            {
                LFDLevel = lfdlevel_comboBox.SelectedIndex + 1;
                devCapture.EnableLFD(true, LFDLevel);
            }
            else
            {
                LFDLevel = lfdlevel_comboBox.SelectedIndex + 1;
                devCapture.EnableLFD(false, LFDLevel);
            }

            stMultiFingerParam stParam = new stMultiFingerParam();
            stParam.operationType = (int)operationType;
            stParam.featureFormat = featuretype;
            stParam.duration = second * 1000;
            ret = devCapture.MultiFingerStartAcquisition(stParam, this.captureCallback);
            if (ret != 0) return;

            capStatus = true;

            message_textBox.AppendText("\r\n\r\nPlease press your finger");
        }

        private void timeout_textBox_KeyPress(object sender, KeyPressEventArgs e)
        {
             if (((int)e.KeyChar < 48 && (int)e.KeyChar != 8) || ((int)e.KeyChar > 57))
             {
                 e.Handled = true;
             }
        }


        private void timeout_textBox_TextChanged(object sender, EventArgs e)
        {
            if (timeout_textBox.Text == "") return;
            int nTimeout = int.Parse(timeout_textBox.Text);
            if (nTimeout < 0 || nTimeout > 60)
            {
                timeout_textBox.Text = timeout_textBox.Text.Remove(1);
                timeout_textBox.SelectionStart = timeout_textBox.Text.Length;
                return;
            }
        }

        private void repeat_button_Click(object sender, EventArgs e)
        {
            startCapture(currentOperationType);
        }

        private void CaptureForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            int ret = 0;
            if (capStatus)
            {
                ret = devCapture.MultiFingerStopAcquisition();
                if(900 == nDevType)
                    devCapture.MultiFingerSetDryWetFingerLevel(EnDryWetFingerLevelType.WET_DRY_FINGER_NORMAL);
                if (ret != 0) return;
                devCapture = null;
                capStatus = false;
            }
            TransferClosePre();
        }


        public void UpdateUI()
        {

            Bitmap bmp2 = (Bitmap)gbmp.Clone();
            Graphics g = Graphics.FromImage(bmp2);
            Pen testpen = new Pen(Color.FromArgb(61, 145, 64), 4);
           
            if (bRollFinger != true)
            {
                for (int n = 0; n < fingerNum; n++)
                {
                    float x1 = (float)(fingerFarmelist[n].ulX);
                    float y1 = (float)(fingerFarmelist[n].ulY);

                    float x2 = (float)(fingerFarmelist[n].urX);
                    float y2 = (float)(fingerFarmelist[n].urY);

                    float x3 = (float)(fingerFarmelist[n].llX);
                    float y3 = (float)(fingerFarmelist[n].llY);

                    float x4 = (float)(fingerFarmelist[n].lrX);
                    float y4 = (float)(fingerFarmelist[n].lrY);

                    g.DrawLine(testpen, x1, y1, x2, y2);
                    g.DrawLine(testpen, x2, y2, x4, y4);
                    g.DrawLine(testpen, x4, y4, x3, y3);
                    g.DrawLine(testpen, x3, y3, x1, y1);

                    string strPos;
                    strPos = FingerPosChangeStringUIshow(fingerFarmelist[n].fingerPos);

                    Font fontPos = new Font("Arial", 25, FontStyle.Bold);
                    Brush brushP = new SolidBrush(Color.FromArgb(0, 191, 255));

                    g.DrawString(strPos, fontPos, brushP, x1, y1);
                    
                    //string score1 = $"{fingerFarmelist[n].ImgNFIQ}"+"/"+ $"{fingerFarmelist[n].fingerContrast}";
                    string score1 = fingerFarmelist[n].ImgNFIQ.ToString();
                    Font font1 = new Font("Arial", 30, FontStyle.Bold);
                    Brush brush1 = new SolidBrush(Color.Black);

                    if (fingerFarmelist[n].ImgNFIQ > 3 || fingerFarmelist[n].ImgNFIQ <= 0)
                        g.FillRectangle(new SolidBrush(Color.FromArgb(255, 0, 0)), x3, y3 - 37, 35, 35);
                    else
                        g.FillRectangle(new SolidBrush(Color.FromArgb(0, 255, 0)), x3, y3 - 37, 35, 35);

                    g.DrawString(score1, font1, brush1, x3, y3 - 40);
                }
            }
            else
            {
                for (int n = 0; n < fingerNum; n++)
                {
                    int nclientW = preview_pictureBox.Width;
                    int nclientH = preview_pictureBox.Height;
                    double Wsize = 1;// srcImgW / nclientW;
                    double Hsize = 1;//srcImgH / nclientH;

                    Pen testpenRoll = new Pen(Color.FromArgb(61, 145, 64), 2);
                    double fingerL = fingerFarmelist[n].TopLeftX / Wsize;
                    double fingerR = fingerFarmelist[n].TopLeftY / Hsize;
                    double fingerT = fingerFarmelist[n].ImgW / Wsize;
                    double fingerB = fingerFarmelist[n].ImgH / Hsize;
                    var rect = new Rectangle((int)fingerL, (int)fingerR, (int)fingerT, (int)fingerB);
                    g.DrawRectangle(testpen, rect);

                    string score = "NFIQ:" + fingerFarmelist[n].ImgNFIQ.ToString();
                    Font font = new Font("Arial", 20, FontStyle.Regular);
                    Brush brush;
                    if (fingerFarmelist[n].ImgNFIQ > 3)
                        brush = new SolidBrush(Color.Red);
                    else
                        brush = new SolidBrush(Color.Blue);

                    var rectText = new RectangleF((int)fingerL, ((int)fingerR + (int)fingerB), (int)fingerT + 15, (int)fingerB);
                    g.DrawString(score, font, brush, rectText);
                }
            }
            
            preview_pictureBox.Image = bmp2;
        }

        private void preview_pictureBox_Paint(object sender, PaintEventArgs e)
        {
            int nclientW = preview_pictureBox.Width;
            int nclientH = preview_pictureBox.Height;
            double Wsize = srcImgW / nclientW;
            double Hsize = srcImgH / nclientH;
            int MAX_WIDTH_ROLL = 350;

            if (bLive == true && fingerFarmelist[0].RollX > 0)
            {
                //画直线
                base.OnPaint(e);
                Graphics sss = e.Graphics;
                Pen testpen;
                if (fingerFarmelist[0].ImgW > MAX_WIDTH_ROLL)
                    testpen = new Pen(Color.FromArgb(0, 176, 171), 5);
                else
                    testpen = new Pen(Color.FromArgb(255, 0, 0), 5);

                int x1 = (int)(fingerFarmelist[0].RollX / Wsize);
                int y1 = 0;

                int x2 = (int)(fingerFarmelist[0].RollX / Wsize);
                int y2 = nclientH;

                sss.DrawLine(testpen, x1, y1, x2, y2);
            }
        }
    }
}
