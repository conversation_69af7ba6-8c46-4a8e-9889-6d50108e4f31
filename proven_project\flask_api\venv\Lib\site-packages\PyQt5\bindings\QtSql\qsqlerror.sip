// qsqlerror.sip generated by MetaSIP
//
// This file is part of the QtSql Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSqlError
{
%TypeHeaderCode
#include <qsqlerror.h>
%End

public:
    enum ErrorType
    {
        NoError,
        ConnectionError,
        StatementError,
        TransactionError,
        UnknownError,
    };

%If (Qt_5_3_0 -)
    QSqlError(const QString &driverText = QString(), const QString &databaseText = QString(), QSqlError::ErrorType type = QSqlError::NoError, const QString &errorCode = QString());
%End
%If (Qt_5_3_0 -)
    QSqlError(const QString &driverText, const QString &databaseText, QSqlError::ErrorType type, int number);
%End
%If (- Qt_5_3_0)
    QSqlError(const QString &driverText = QString(), const QString &databaseText = QString(), QSqlError::ErrorType type = QSqlError::NoError, int number = -1);
%End
    QSqlError(const QSqlError &other);
    ~QSqlError();
    QString driverText() const;
    void setDriverText(const QString &driverText);
    QString databaseText() const;
    void setDatabaseText(const QString &databaseText);
    QSqlError::ErrorType type() const;
    void setType(QSqlError::ErrorType type);
    int number() const;
    void setNumber(int number);
    QString text() const;
    bool isValid() const;
    bool operator==(const QSqlError &other) const;
    bool operator!=(const QSqlError &other) const;
%If (Qt_5_3_0 -)
    QString nativeErrorCode() const;
%End
%If (Qt_5_10_0 -)
    void swap(QSqlError &other /Constrained/);
%End
};
