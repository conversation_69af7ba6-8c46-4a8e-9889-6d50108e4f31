#!/usr/bin/env python3
"""
Test script for the TCP Bridge Server

This script tests the TCP bridge server communication by sending
capture commands and verifying responses.
"""

import socket
import time

def test_tcp_bridge():
    """Test the TCP bridge server connection and commands."""
    
    print("🔌 Testing TCP Bridge Server Connection...")
    
    capture_test_cases = [
        ("USER001", 1, "Right Thumb"),
        ("USER001", 6, "Left Thumb"),
        ("USER001", 11, "Two Thumbs"),
        ("USER001", 12, "Left Four Fingers"),
        ("USER001", 13, "Right Four Fingers"),
    ]

    verify_test_cases = [
        ("USER001", 1, "Right Thumb"),
        ("USER002", 2, "Right Index"),
    ]
    
    # Test CAPTURE commands
    print("\n🖐️ Testing CAPTURE commands:")
    for person_id, finger_index, description in capture_test_cases:
        test_command("CAPTURE", f"{person_id} {finger_index}", f"Capture {description}")

    # Test VERIFY commands
    print("\n🔍 Testing VERIFY commands:")
    for person_id, finger_index, description in verify_test_cases:
        test_command("VERIFY", f"{person_id} {finger_index}", f"Verify {description} for {person_id}")

    # Test IDENTIFY command
    print("\n🔎 Testing IDENTIFY command:")
    test_command("IDENTIFY", "", "Identify person (1:N matching)")

    # Test ENROLL command
    print("\n📝 Testing ENROLL command:")
    test_command("ENROLL", "USER001", "Enroll USER001")

def test_command(command, params, description):
    """Test a single TCP command."""
    print(f"\n📡 Testing: {description}")

    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(10)
            s.connect(('127.0.0.1', 8123))

            full_command = f"{command} {params}\n".strip() + "\n"
            print(f"➡️ Sending: {full_command.strip()}")
            s.sendall(full_command.encode())

            # Receive response
            data = b""
            start_time = time.time()
            while time.time() - start_time < 5:  # 5 second timeout
                try:
                    chunk = s.recv(4096)
                    if not chunk:
                        break
                    data += chunk
                except socket.timeout:
                    break

            response = data.decode("utf-8", errors="ignore")
            lines = response.splitlines()

            print("⬅️ Response:")
            for line in lines:
                clean_line = line.strip()
                if clean_line:
                    print(f"   {clean_line}")

            # Check for success indicators
            has_success = any("✅" in line or "OK" in line.upper() for line in lines)
            has_bmp = any(line.startswith("BMP:") for line in lines)
            has_result = any("RESULT:" in line for line in lines)

            if has_success and (has_bmp or has_result):
                print("   ✅ SUCCESS: Got success message and data")
            elif has_success:
                print("   ⚠️ PARTIAL: Got success message but no data")
            else:
                print("   ❌ FAILED: No success message received")

    except ConnectionRefusedError:
        print("   ❌ CONNECTION REFUSED: TCP bridge server not running")
        print("   💡 Please start the C# application first")
        return False
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False

    return True

def test_health_check():
    """Test basic TCP connection."""
    print("\n🏥 Testing basic TCP connection...")
    
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(5)
            s.connect(('127.0.0.1', 8123))
            print("   ✅ TCP connection successful")
            return True
    except ConnectionRefusedError:
        print("   ❌ TCP connection refused - server not running")
        return False
    except Exception as e:
        print(f"   ❌ TCP connection error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 TCP Bridge Server Test")
    print("=" * 50)
    
    if test_health_check():
        test_tcp_bridge()
    else:
        print("\n💡 To fix this:")
        print("1. Build and run the C# application: dotnet run")
        print("2. Make sure the TCP bridge server starts on port 8123")
        print("3. Run this test script again")
    
    print("\n🏁 Test completed")
