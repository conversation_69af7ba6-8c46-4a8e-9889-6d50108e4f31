# The PEP 484 type hints stub file for the QtSerialPort module.
#
# Generated by SIP 6.8.6
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt5.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import typing

import PyQt5.sip

from PyQt5 import QtCore

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., Any], QtCore.pyqtBoundSignal]


class QSerialPort(QtCore.QIODevice):

    class SerialPortError(int):
        NoError = ... # type: QSerialPort.SerialPortError
        DeviceNotFoundError = ... # type: QSerialPort.SerialPortError
        PermissionError = ... # type: QSerialPort.SerialPortError
        OpenError = ... # type: QSerialPort.SerialPortError
        ParityError = ... # type: QSerialPort.SerialPortError
        FramingError = ... # type: QSerialPort.SerialPortError
        BreakConditionError = ... # type: QSerialPort.SerialPortError
        WriteError = ... # type: QSerialPort.SerialPortError
        ReadError = ... # type: QSerialPort.SerialPortError
        ResourceError = ... # type: QSerialPort.SerialPortError
        UnsupportedOperationError = ... # type: QSerialPort.SerialPortError
        TimeoutError = ... # type: QSerialPort.SerialPortError
        NotOpenError = ... # type: QSerialPort.SerialPortError
        UnknownError = ... # type: QSerialPort.SerialPortError

    class DataErrorPolicy(int):
        SkipPolicy = ... # type: QSerialPort.DataErrorPolicy
        PassZeroPolicy = ... # type: QSerialPort.DataErrorPolicy
        IgnorePolicy = ... # type: QSerialPort.DataErrorPolicy
        StopReceivingPolicy = ... # type: QSerialPort.DataErrorPolicy
        UnknownPolicy = ... # type: QSerialPort.DataErrorPolicy

    class PinoutSignal(int):
        NoSignal = ... # type: QSerialPort.PinoutSignal
        TransmittedDataSignal = ... # type: QSerialPort.PinoutSignal
        ReceivedDataSignal = ... # type: QSerialPort.PinoutSignal
        DataTerminalReadySignal = ... # type: QSerialPort.PinoutSignal
        DataCarrierDetectSignal = ... # type: QSerialPort.PinoutSignal
        DataSetReadySignal = ... # type: QSerialPort.PinoutSignal
        RingIndicatorSignal = ... # type: QSerialPort.PinoutSignal
        RequestToSendSignal = ... # type: QSerialPort.PinoutSignal
        ClearToSendSignal = ... # type: QSerialPort.PinoutSignal
        SecondaryTransmittedDataSignal = ... # type: QSerialPort.PinoutSignal
        SecondaryReceivedDataSignal = ... # type: QSerialPort.PinoutSignal

    class FlowControl(int):
        NoFlowControl = ... # type: QSerialPort.FlowControl
        HardwareControl = ... # type: QSerialPort.FlowControl
        SoftwareControl = ... # type: QSerialPort.FlowControl
        UnknownFlowControl = ... # type: QSerialPort.FlowControl

    class StopBits(int):
        OneStop = ... # type: QSerialPort.StopBits
        OneAndHalfStop = ... # type: QSerialPort.StopBits
        TwoStop = ... # type: QSerialPort.StopBits
        UnknownStopBits = ... # type: QSerialPort.StopBits

    class Parity(int):
        NoParity = ... # type: QSerialPort.Parity
        EvenParity = ... # type: QSerialPort.Parity
        OddParity = ... # type: QSerialPort.Parity
        SpaceParity = ... # type: QSerialPort.Parity
        MarkParity = ... # type: QSerialPort.Parity
        UnknownParity = ... # type: QSerialPort.Parity

    class DataBits(int):
        Data5 = ... # type: QSerialPort.DataBits
        Data6 = ... # type: QSerialPort.DataBits
        Data7 = ... # type: QSerialPort.DataBits
        Data8 = ... # type: QSerialPort.DataBits
        UnknownDataBits = ... # type: QSerialPort.DataBits

    class BaudRate(int):
        Baud1200 = ... # type: QSerialPort.BaudRate
        Baud2400 = ... # type: QSerialPort.BaudRate
        Baud4800 = ... # type: QSerialPort.BaudRate
        Baud9600 = ... # type: QSerialPort.BaudRate
        Baud19200 = ... # type: QSerialPort.BaudRate
        Baud38400 = ... # type: QSerialPort.BaudRate
        Baud57600 = ... # type: QSerialPort.BaudRate
        Baud115200 = ... # type: QSerialPort.BaudRate
        UnknownBaud = ... # type: QSerialPort.BaudRate

    class Direction(int):
        Input = ... # type: QSerialPort.Direction
        Output = ... # type: QSerialPort.Direction
        AllDirections = ... # type: QSerialPort.Direction

    class Directions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSerialPort.Directions', 'QSerialPort.Direction']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSerialPort.Directions', 'QSerialPort.Direction']) -> 'QSerialPort.Directions': ...
        def __xor__(self, f: typing.Union['QSerialPort.Directions', 'QSerialPort.Direction']) -> 'QSerialPort.Directions': ...
        def __ior__(self, f: typing.Union['QSerialPort.Directions', 'QSerialPort.Direction']) -> 'QSerialPort.Directions': ...
        def __or__(self, f: typing.Union['QSerialPort.Directions', 'QSerialPort.Direction']) -> 'QSerialPort.Directions': ...
        def __iand__(self, f: typing.Union['QSerialPort.Directions', 'QSerialPort.Direction']) -> 'QSerialPort.Directions': ...
        def __and__(self, f: typing.Union['QSerialPort.Directions', 'QSerialPort.Direction']) -> 'QSerialPort.Directions': ...
        def __invert__(self) -> 'QSerialPort.Directions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class PinoutSignals(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSerialPort.PinoutSignals', 'QSerialPort.PinoutSignal']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSerialPort.PinoutSignals', 'QSerialPort.PinoutSignal']) -> 'QSerialPort.PinoutSignals': ...
        def __xor__(self, f: typing.Union['QSerialPort.PinoutSignals', 'QSerialPort.PinoutSignal']) -> 'QSerialPort.PinoutSignals': ...
        def __ior__(self, f: typing.Union['QSerialPort.PinoutSignals', 'QSerialPort.PinoutSignal']) -> 'QSerialPort.PinoutSignals': ...
        def __or__(self, f: typing.Union['QSerialPort.PinoutSignals', 'QSerialPort.PinoutSignal']) -> 'QSerialPort.PinoutSignals': ...
        def __iand__(self, f: typing.Union['QSerialPort.PinoutSignals', 'QSerialPort.PinoutSignal']) -> 'QSerialPort.PinoutSignals': ...
        def __and__(self, f: typing.Union['QSerialPort.PinoutSignals', 'QSerialPort.PinoutSignal']) -> 'QSerialPort.PinoutSignals': ...
        def __invert__(self) -> 'QSerialPort.PinoutSignals': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, info: 'QSerialPortInfo', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    breakEnabledChanged: typing.ClassVar[QtCore.pyqtSignal]
    def isBreakEnabled(self) -> bool: ...
    def handle(self) -> typing.Optional[PyQt5.sip.voidptr]: ...
    def writeData(self, data: typing.Optional[PyQt5.sip.array[bytes]]) -> int: ...
    def readLineData(self, maxlen: int) -> bytes: ...
    def readData(self, maxlen: int) -> bytes: ...
    settingsRestoredOnCloseChanged: typing.ClassVar[QtCore.pyqtSignal]
    requestToSendChanged: typing.ClassVar[QtCore.pyqtSignal]
    dataTerminalReadyChanged: typing.ClassVar[QtCore.pyqtSignal]
    dataErrorPolicyChanged: typing.ClassVar[QtCore.pyqtSignal]
    flowControlChanged: typing.ClassVar[QtCore.pyqtSignal]
    stopBitsChanged: typing.ClassVar[QtCore.pyqtSignal]
    parityChanged: typing.ClassVar[QtCore.pyqtSignal]
    dataBitsChanged: typing.ClassVar[QtCore.pyqtSignal]
    baudRateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setBreakEnabled(self, enabled: bool = ...) -> bool: ...
    def sendBreak(self, duration: int = ...) -> bool: ...
    def waitForBytesWritten(self, msecs: int = ...) -> bool: ...
    def waitForReadyRead(self, msecs: int = ...) -> bool: ...
    def canReadLine(self) -> bool: ...
    def bytesToWrite(self) -> int: ...
    def bytesAvailable(self) -> int: ...
    def isSequential(self) -> bool: ...
    def setReadBufferSize(self, size: int) -> None: ...
    def readBufferSize(self) -> int: ...
    def clearError(self) -> None: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def dataErrorPolicy(self) -> 'QSerialPort.DataErrorPolicy': ...
    def setDataErrorPolicy(self, policy: 'QSerialPort.DataErrorPolicy' = ...) -> bool: ...
    def atEnd(self) -> bool: ...
    def clear(self, dir: typing.Union['QSerialPort.Directions', 'QSerialPort.Direction'] = ...) -> bool: ...
    def flush(self) -> bool: ...
    def pinoutSignals(self) -> 'QSerialPort.PinoutSignals': ...
    def isRequestToSend(self) -> bool: ...
    def setRequestToSend(self, set: bool) -> bool: ...
    def isDataTerminalReady(self) -> bool: ...
    def setDataTerminalReady(self, set: bool) -> bool: ...
    def flowControl(self) -> 'QSerialPort.FlowControl': ...
    def setFlowControl(self, flow: 'QSerialPort.FlowControl') -> bool: ...
    def stopBits(self) -> 'QSerialPort.StopBits': ...
    def setStopBits(self, stopBits: 'QSerialPort.StopBits') -> bool: ...
    def parity(self) -> 'QSerialPort.Parity': ...
    def setParity(self, parity: 'QSerialPort.Parity') -> bool: ...
    def dataBits(self) -> 'QSerialPort.DataBits': ...
    def setDataBits(self, dataBits: 'QSerialPort.DataBits') -> bool: ...
    def baudRate(self, dir: typing.Union['QSerialPort.Directions', 'QSerialPort.Direction'] = ...) -> int: ...
    def setBaudRate(self, baudRate: int, dir: typing.Union['QSerialPort.Directions', 'QSerialPort.Direction'] = ...) -> bool: ...
    def settingsRestoredOnClose(self) -> bool: ...
    def setSettingsRestoredOnClose(self, restore: bool) -> None: ...
    def close(self) -> None: ...
    def open(self, mode: typing.Union[QtCore.QIODevice.OpenMode, QtCore.QIODevice.OpenModeFlag]) -> bool: ...
    def setPort(self, info: 'QSerialPortInfo') -> None: ...
    def portName(self) -> str: ...
    def setPortName(self, name: typing.Optional[str]) -> None: ...


class QSerialPortInfo(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, port: QSerialPort) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSerialPortInfo') -> None: ...

    def serialNumber(self) -> str: ...
    def isNull(self) -> bool: ...
    @staticmethod
    def availablePorts() -> typing.List['QSerialPortInfo']: ...
    @staticmethod
    def standardBaudRates() -> typing.List[int]: ...
    def isValid(self) -> bool: ...
    def isBusy(self) -> bool: ...
    def hasProductIdentifier(self) -> bool: ...
    def hasVendorIdentifier(self) -> bool: ...
    def productIdentifier(self) -> int: ...
    def vendorIdentifier(self) -> int: ...
    def manufacturer(self) -> str: ...
    def description(self) -> str: ...
    def systemLocation(self) -> str: ...
    def portName(self) -> str: ...
    def swap(self, other: 'QSerialPortInfo') -> None: ...
