﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Http.Cors</name>
  </assembly>
  <members>
    <member name="T:System.Web.Http.CorsHttpConfigurationExtensions">
      <summary>CORS-related extension methods for <see cref="T:System.Web.Http.HttpConfiguration" />.</summary>
    </member>
    <member name="M:System.Web.Http.CorsHttpConfigurationExtensions.EnableCors(System.Web.Http.HttpConfiguration)">
      <summary>Enables the support for CORS.</summary>
      <param name="httpConfiguration">The <see cref="T:System.Web.Http.HttpConfiguration" />.</param>
    </member>
    <member name="M:System.Web.Http.CorsHttpConfigurationExtensions.EnableCors(System.Web.Http.HttpConfiguration,System.Web.Http.Cors.ICorsPolicyProvider)">
      <summary>Enables the support for CORS.</summary>
      <param name="httpConfiguration">The <see cref="T:System.Web.Http.HttpConfiguration" />.</param>
      <param name="defaultPolicyProvider">The default <see cref="T:System.Web.Http.Cors.ICorsPolicyProvider" />.</param>
      <exception cref="T:System.ArgumentNullException">httpConfiguration</exception>
    </member>
    <member name="M:System.Web.Http.CorsHttpConfigurationExtensions.GetCorsEngine(System.Web.Http.HttpConfiguration)">
      <summary>Gets the <see cref="T:System.Web.Cors.ICorsEngine" /> from the <see cref="T:System.Web.Http.HttpConfiguration" />.</summary>
      <returns>The <see cref="T:System.Web.Cors.ICorsEngine" />.</returns>
      <param name="httpConfiguration">The <see cref="T:System.Web.Http.HttpConfiguration" />.</param>
      <exception cref="T:System.ArgumentNullException">httpConfiguration</exception>
    </member>
    <member name="M:System.Web.Http.CorsHttpConfigurationExtensions.GetCorsPolicyProviderFactory(System.Web.Http.HttpConfiguration)">
      <summary>Gets the <see cref="T:System.Web.Http.Cors.ICorsPolicyProviderFactory" /> from the <see cref="T:System.Web.Http.HttpConfiguration" />.</summary>
      <returns>The <see cref="T:System.Web.Http.Cors.ICorsPolicyProviderFactory" />.</returns>
      <param name="httpConfiguration">The <see cref="T:System.Web.Http.HttpConfiguration" />.</param>
      <exception cref="T:System.ArgumentNullException">httpConfiguration</exception>
    </member>
    <member name="M:System.Web.Http.CorsHttpConfigurationExtensions.SetCorsEngine(System.Web.Http.HttpConfiguration,System.Web.Cors.ICorsEngine)">
      <summary>Sets the <see cref="T:System.Web.Cors.ICorsEngine" /> on the <see cref="T:System.Web.Http.HttpConfiguration" />.</summary>
      <param name="httpConfiguration">The <see cref="T:System.Web.Http.HttpConfiguration" />.</param>
      <param name="corsEngine">The <see cref="T:System.Web.Cors.ICorsEngine" />.</param>
      <exception cref="T:System.ArgumentNullException">httpConfiguration or corsEngine</exception>
    </member>
    <member name="M:System.Web.Http.CorsHttpConfigurationExtensions.SetCorsPolicyProviderFactory(System.Web.Http.HttpConfiguration,System.Web.Http.Cors.ICorsPolicyProviderFactory)">
      <summary>Sets the <see cref="T:System.Web.Http.Cors.ICorsPolicyProviderFactory" /> on the <see cref="T:System.Web.Http.HttpConfiguration" />.</summary>
      <param name="httpConfiguration">The <see cref="T:System.Web.Http.HttpConfiguration" />.</param>
      <param name="corsPolicyProviderFactory">The <see cref="T:System.Web.Http.Cors.ICorsPolicyProviderFactory" />.</param>
      <exception cref="T:System.ArgumentNullException">httpConfiguration or corsPolicyProviderFactory</exception>
    </member>
    <member name="T:System.Web.Http.Cors.AttributeBasedPolicyProviderFactory">
      <summary>An implementation of <see cref="T:System.Web.Http.Cors.ICorsPolicyProviderFactory" /> that returns the <see cref="T:System.Web.Http.Cors.ICorsPolicyProvider" /> from the controller or action attribute.</summary>
    </member>
    <member name="M:System.Web.Http.Cors.AttributeBasedPolicyProviderFactory.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Cors.AttributeBasedPolicyProviderFactory" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.Cors.AttributeBasedPolicyProviderFactory.DefaultPolicyProvider">
      <summary>Gets or sets the default <see cref="T:System.Web.Http.Cors.ICorsPolicyProvider" />.</summary>
      <returns>The default <see cref="T:System.Web.Http.Cors.ICorsPolicyProvider" />.</returns>
    </member>
    <member name="M:System.Web.Http.Cors.AttributeBasedPolicyProviderFactory.GetCorsPolicyProvider(System.Net.Http.HttpRequestMessage)">
      <summary>Gets the <see cref="T:System.Web.Http.Cors.ICorsPolicyProvider" /> for the request.</summary>
      <returns>The <see cref="T:System.Web.Http.Cors.ICorsPolicyProvider" />.</returns>
      <param name="request">The request.</param>
      <exception cref="T:System.ArgumentNullException">request</exception>
    </member>
    <member name="T:System.Web.Http.Cors.CorsHttpRequestMessageExtensions">
      <summary>CORS-related extension methods for <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
    </member>
    <member name="M:System.Web.Http.Cors.CorsHttpRequestMessageExtensions.GetCorsRequestContext(System.Net.Http.HttpRequestMessage)">
      <summary>Gets the <see cref="T:System.Web.Cors.CorsRequestContext" /> for a given request.</summary>
      <returns>The <see cref="T:System.Web.Cors.CorsRequestContext" />.</returns>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" />.</param>
      <exception cref="T:System.ArgumentNullException">request</exception>
    </member>
    <member name="T:System.Web.Http.Cors.CorsHttpResponseMessageExtensions">
      <summary>CORS-related extension methods for <see cref="T:System.Net.Http.HttpResponseMessage" />.</summary>
    </member>
    <member name="M:System.Web.Http.Cors.CorsHttpResponseMessageExtensions.WriteCorsHeaders(System.Net.Http.HttpResponseMessage,System.Web.Cors.CorsResult)">
      <summary>Writes the CORS headers on the response.</summary>
      <param name="response">The <see cref="T:System.Net.Http.HttpResponseMessage" />.</param>
      <param name="corsResult">The <see cref="T:System.Web.Cors.CorsResult" />.</param>
      <exception cref="T:System.ArgumentNullException">response or corsResult</exception>
    </member>
    <member name="T:System.Web.Http.Cors.CorsMessageHandler">
      <summary>Custom <see cref="T:System.Net.Http.DelegatingHandler" /> for handling CORS requests.</summary>
    </member>
    <member name="M:System.Web.Http.Cors.CorsMessageHandler.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Cors.CorsMessageHandler" /> class.</summary>
      <param name="httpConfiguration">The <see cref="T:System.Web.Http.HttpConfiguration" />.</param>
      <exception cref="T:System.ArgumentNullException">httpConfiguration</exception>
    </member>
    <member name="M:System.Web.Http.Cors.CorsMessageHandler.HandleCorsPreflightRequestAsync(System.Net.Http.HttpRequestMessage,System.Web.Cors.CorsRequestContext,System.Threading.CancellationToken)">
      <summary>Handles the preflight request specified by CORS.</summary>
      <returns>The <see cref="T:System.Threading.Tasks.Task`1" /></returns>
      <param name="request">The request.</param>
      <param name="corsRequestContext">The cors request context.</param>
      <param name="cancellationToken">The cancellation token.</param>
      <exception cref="T:System.ArgumentNullException">request or corsRequestContext</exception>
    </member>
    <member name="M:System.Web.Http.Cors.CorsMessageHandler.HandleCorsRequestAsync(System.Net.Http.HttpRequestMessage,System.Web.Cors.CorsRequestContext,System.Threading.CancellationToken)">
      <summary>Handles the actual CORS request.</summary>
      <returns>The <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" />.</param>
      <param name="corsRequestContext">The <see cref="T:System.Web.Cors.CorsRequestContext" />.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.ArgumentNullException">request or corsRequestContext</exception>
    </member>
    <member name="M:System.Web.Http.Cors.CorsMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Sends an HTTP request to the inner handler to send to the server as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />. The task object representing the asynchronous operation.</returns>
      <param name="request">The HTTP request message to send to the server.</param>
      <param name="cancellationToken">A cancellation token to cancel operation.</param>
    </member>
    <member name="T:System.Web.Http.Cors.DisableCorsAttribute">
      <summary>This class defines an attribute that can be applied to an action or a controller to disable CORS.</summary>
    </member>
    <member name="M:System.Web.Http.Cors.DisableCorsAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Cors.DisableCorsAttribute" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Cors.DisableCorsAttribute.GetCorsPolicyAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Gets the CORS policy asynchronously.</summary>
      <returns>The newly created task for this operation.</returns>
      <param name="request">The request.</param>
      <param name="cancellationToken">The cancellation token assigned for this task.</param>
    </member>
    <member name="T:System.Web.Http.Cors.EnableCorsAttribute">
      <summary>This class defines an attribute that can be applied to an action or a controller to enable CORS. By default, it allows all origins, methods and headers.</summary>
    </member>
    <member name="M:System.Web.Http.Cors.EnableCorsAttribute.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Cors.EnableCorsAttribute" /> class.</summary>
      <param name="origins">Comma-separated list of origins that are allowed to access the resource. Use "*" to allow all.</param>
      <param name="headers">Comma-separated list of headers that are supported by the resource. Use "*" to allow all. Use null or empty string to allow none.</param>
      <param name="methods">Comma-separated list of methods that are supported by the resource. Use "*" to allow all. Use null or empty string to allow none.</param>
    </member>
    <member name="M:System.Web.Http.Cors.EnableCorsAttribute.#ctor(System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Cors.EnableCorsAttribute" /> class.</summary>
      <param name="origins">Comma-separated list of origins that are allowed to access the resource. Use "*" to allow all.</param>
      <param name="headers">Comma-separated list of headers that are supported by the resource. Use "*" to allow all. Use null or empty string to allow none.</param>
      <param name="methods">Comma-separated list of methods that are supported by the resource. Use "*" to allow all. Use null or empty string to allow none.</param>
      <param name="exposedHeaders">Comma-separated list of headers that the resource might use and can be exposed. Use null or empty string to expose none.</param>
    </member>
    <member name="P:System.Web.Http.Cors.EnableCorsAttribute.ExposedHeaders">
      <summary>Gets the headers that the resource might use and can be exposed.</summary>
    </member>
    <member name="M:System.Web.Http.Cors.EnableCorsAttribute.GetCorsPolicyAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.Cors.EnableCorsAttribute.Headers">
      <summary>Gets the headers that are supported by the resource.</summary>
    </member>
    <member name="P:System.Web.Http.Cors.EnableCorsAttribute.Methods">
      <summary>Gets the methods that are supported by the resource.</summary>
    </member>
    <member name="P:System.Web.Http.Cors.EnableCorsAttribute.Origins">
      <summary>Gets the origins that are allowed to access the resource.</summary>
    </member>
    <member name="P:System.Web.Http.Cors.EnableCorsAttribute.PreflightMaxAge">
      <summary>Gets or sets the number of seconds the results of a preflight request can be cached.</summary>
    </member>
    <member name="P:System.Web.Http.Cors.EnableCorsAttribute.SupportsCredentials">
      <summary>Gets or sets a value indicating whether the resource supports user credentials in the request.</summary>
    </member>
    <member name="T:System.Web.Http.Cors.ICorsPolicyProvider">
      <summary>Provides an abstraction for getting the <see cref="T:System.Web.Cors.CorsPolicy" />.</summary>
    </member>
    <member name="M:System.Web.Http.Cors.ICorsPolicyProvider.GetCorsPolicyAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Gets the <see cref="T:System.Web.Cors.CorsPolicy" />.</summary>
      <returns>The <see cref="T:System.Web.Cors.CorsPolicy" />.</returns>
      <param name="request">The request.</param>
      <param name="cancellationToken">The cancellation token.</param>
    </member>
    <member name="T:System.Web.Http.Cors.ICorsPolicyProviderFactory">
      <summary>Provides an abstraction for getting the <see cref="T:System.Web.Http.Cors.ICorsPolicyProvider" />.</summary>
    </member>
    <member name="M:System.Web.Http.Cors.ICorsPolicyProviderFactory.GetCorsPolicyProvider(System.Net.Http.HttpRequestMessage)">
      <summary>Gets the <see cref="T:System.Web.Http.Cors.ICorsPolicyProvider" /> for the request.</summary>
      <returns>The <see cref="T:System.Web.Http.Cors.ICorsPolicyProvider" />.</returns>
      <param name="request">The request.</param>
    </member>
    <member name="T:System.Web.Http.Cors.Tracing.TraceCategories">
      <summary>Category names traced by the default CORS tracing implementation.</summary>
    </member>
    <member name="F:System.Web.Http.Cors.Tracing.TraceCategories.CorsCategory">
      <summary>The trace category for CORS-related events.</summary>
    </member>
  </members>
</doc>